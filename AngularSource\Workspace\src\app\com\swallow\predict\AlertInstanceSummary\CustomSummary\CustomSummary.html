<SwtModule (creationComplete)='onLoad()' width="100%"  height="100%">
<VBox #mainHGroup id="mainHGroup"  width="100%" height="100%">
  <HDividedBox id="divBox" #divBox  width="100%" height="100%">
      <VBox id="treeContainer" #treeContainer width="320" height="100%" class="left">
          <SwtLabel  id="treeTitle" #treeTitle
                     fontWeight="normal"
                     height="25"
                     paddingLeft="5">
          </SwtLabel>
          <CustomTree id="tree" #tree
                      width="100%"
                      height="92%"
                      doubleClickEnabled="true"
                      (keyDown)="treeKeyDownHandler($event)"
                      (itemDoubleClick)="treeItemDoubleClickHandler($event)">
          </CustomTree>
      </VBox>
      <VBox height="100%"  width="60%" id="gridContainer" #gridContainer  class="right">
          <SwtLabel id="gridTitle"
                    #gridTitle
                    height="25"
                    fontWeight="normal"
                   >
          </SwtLabel>
          <SwtCanvas id="customGrid" #customGrid width="100%" height="92%" borderStyle="solid" cornerRadius="4" dropShadowEnabled="true" borderColor="#f9f9f9" >
              <SwtCommonGrid 
                      (onFilterChanged)="saveGridVisibleState($event)"
                      id="summaryGrid" 
                      #summaryGrid 
                      width="100%" 
                      height="100%">
              </SwtCommonGrid>
          </SwtCanvas>
      </VBox>
  </HDividedBox>
  <HBox width="100%" height="30"
        horizontalAlign="right"
        paddingTop="4"
        paddingRight="10">
      <SwtLabel id="lastRanLbl"  #lastRanLbl  fontWeight="normal" visible="false">
      </SwtLabel>
  </HBox>
</VBox>
</SwtModule>