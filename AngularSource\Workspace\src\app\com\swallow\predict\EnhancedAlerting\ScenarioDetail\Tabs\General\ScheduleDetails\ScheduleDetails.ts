import { Component, OnInit, ModuleWithProviders, NgModule, ViewChild, ElementRef } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { DefineParams } from '../DefineParams/DefineParams';
import { SwtToolBoxModule, SwtModule, SwtLabel, SwtTextInput, SwtCanvas, SwtButton, SwtCommonGrid, SwtAlert, CommonService, SwtUtil, ExternalInterface } from 'swt-tool-box';
declare var require: any;
let convert = require('xml-js');
var prettyData = require('pretty-data');
declare  function validateFormatTime(strField): any;
@Component({
  selector: 'app-schedule-details',
  templateUrl: './ScheduleDetails.html',
  styleUrls: ['./ScheduleDetails.css']
})
export class ScheduleDetails extends SwtModule implements OnInit {


  @ViewChild('scenarioIdLbl') scenarioIdLbl: SwtLabel;
  @ViewChild('runAt') runAt: SwtLabel;
  @ViewChild('runAtDesc') runAtDesc: SwtLabel;
  @ViewChild('scenarioIdtxt') scenarioIdtxt: SwtTextInput;
  @ViewChild('runAtTxt') runAtTxt: SwtTextInput;
  @ViewChild('scheduleCanvas') scheduleCanvas: SwtCanvas;
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;

  
  private paramsEventsFromParent: any;
  private scheduleGrid: SwtCommonGrid;
  private gridParams;
  private scheduleData=[];
  private swtalert: SwtAlert;
  private methodName;
  public scheduleXml="";
  private changedRow;
  private generalSchedGridData;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.scheduleGrid = <SwtCommonGrid>this.scheduleCanvas.addChild(SwtCommonGrid);
    this.scenarioIdLbl.text = SwtUtil.getPredictMessage('scenario.scenarioId', null);
    this.runAt.text = SwtUtil.getPredictMessage('scenario.runAt', null);
    this.runAtDesc.text = SwtUtil.getPredictMessage('scenario.runAtDesc', null);
    this.cancelButton.label = SwtUtil.getPredictMessage("button.cancel", null);
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    //tooltips part
    this.scenarioIdtxt.toolTip = SwtUtil.getPredictMessage('scenario.schedule.tooltip.scenarioId', null);
    this.runAtTxt.toolTip = SwtUtil.getPredictMessage('scenario.schedule.runAt', null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage("tooltip.cancelbutton", null);
    this.okButton.toolTip = SwtUtil.getPredictMessage('tooltip.ok', null);
    this.scheduleGrid.editable=true;
    this.runAtTxt.required = true;
  }

  onLoad(){
    this.scheduleData=[];
    if (window.opener && window.opener.instanceElement) {
      this.paramsEventsFromParent = window.opener.instanceElement.sendGeneralDataToSchedule();
      this.scenarioIdtxt.text = this.paramsEventsFromParent.scenarioId;
      this.scheduleGrid.CustomGrid(this.paramsEventsFromParent.gridData.metadata);
      this.gridParams=this.paramsEventsFromParent.xmlData;
      this.scheduleData=this.paramsEventsFromParent.scheduleGridData;
      this.methodName= this.paramsEventsFromParent.methodName;
      this.changedRow=this.paramsEventsFromParent.selectedItem;
      this.generalSchedGridData= this.paramsEventsFromParent.genaralGridData;
  }
      this.populateData();

}

  populateData() {
    if (this.methodName == "add") {
      this.convertXml();
    } else {
      let data = [];
      let time="";
      this.scheduleData = [];
      data = this.changedRow.parameters.content ?this.changedRow.parameters.content.split(";"):"";
      time= this.runAtTxt.text=this.changedRow.time.content;
      if(data){
      for (let i = 0; i < data.length; i++) {
        let paramVal = data[i].split("(");
        let param = paramVal[0];
        let val = paramVal[1].replace(")", "");
        this.scheduleData.push({ parameter: { clickable: false, content: param, negative: false }, value: { clickable: false, content: val, negative: false } });

      }
      this.scheduleGrid.gridData = { size: this.scheduleData.length, row: this.scheduleData };
      this.scheduleGrid.refresh();
      } else {
        this.scheduleGrid.gridData = { size: 0, row: [] };
      }

    }
  }

   convertXml(){
    this.scheduleData=[];
    if(this.gridParams) {
      let options = {
        object: false,
        reversible: false,
        coerce: false,
        sanitize: true,
        trim: true,
        arrayNotation: false,
        alternateTextNode: false,
        compact: true
      };
      let jsonData = convert.xml2js(this.gridParams, options);
      let data = jsonData.requiredParameters.parameter;
      if (!data.length)
        data = [data];
        for (let i = 0; i < data.length; i++) {
          this.scheduleData.push({parameter: { clickable: false, content: data[i].name._cdata, negative: false },value: { clickable: false, content: "", negative: false }});

        }
    }
    this.scheduleGrid.gridData = { size: this.scheduleData.length, row: this.scheduleData };
    this.scheduleGrid.refresh();

}

prepareData(){
  if (this.runAtTxt.text == '') {
  this.swtalert.error(SwtUtil.getPredictMessage('scenario.missingRunAtValue', null));
  } else if (this.methodName == "add" && this.checkIfTimeExists(this.runAtTxt.text)) {
    this.swtalert.error(SwtUtil.getPredictMessage('scenario.timeAlreadyExists', null));
  } else {
  if (window.opener && window.opener.instanceElement) {

    let parameters=[];
    let paramAsString="";
    let generalGridRows=[];
    let time=this.runAtTxt.text;
    for (let i = 0; i < this.scheduleGrid.gridData.length; i++) {
    parameters.push(this.scheduleGrid.gridData[i].parameter+"("+ this.scheduleGrid.gridData[i].value +")" +";");
  }
    paramAsString=parameters.toString().slice(0, -1).replace(/,/g,"");
    generalGridRows.push({time: { clickable: false, content: time, negative: false },parameters: { clickable: false, content: paramAsString, negative: false }});
    window.opener.instanceElement.generalGridData=generalGridRows;
    window.opener.instanceElement.refreshGeneralGrid();
  } 
   window.close();
}
}

checkIfTimeExists(time){
  let timeFlags=[];
  for (let i = 0; i < this.generalSchedGridData.length; i++) {
    if (this.generalSchedGridData[i].time == time) {
      timeFlags.push("true");

    } else {
      timeFlags.push("false");
    }
  }
  if (!timeFlags.includes("true")) {
    return false;
  } else {
    return true;
  }
  
  
}

cancelHandler() {
  ExternalInterface.call('close');
}


validateTime(textInput): any {
  let validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
  if(textInput.text.endsWith(":")) {
    textInput.text = textInput.text + "00";
  }
  if (textInput.text  && validateFormatTime(textInput) == false) {
    this.swtalert.warning(validTimeMessage, null );
    textInput.text = "";
    return false;
  } else  {
    textInput.text = textInput.text.substring(0,5);
    return true;

  }
}

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ScheduleDetails }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ScheduleDetails],
  entryComponents: []
})
export class ScheduleDetailsModule {}
