import { Component, OnInit, ViewChild } from '@angular/core';
import { SwtComboBox, SwtLabel, SwtTextInput, SwtUtil, SwtButton } from 'swt-tool-box';

@Component({
  selector: 'app-add-cols-pop-up',
  templateUrl: './AddColsPopUp.html',
  styleUrls: ['./AddColsPopUp.css']
})
export class AddColsPopUp implements OnInit {

  /***********SwtComboBox***********/
  @ViewChild('tableCombo') tableCombo: SwtComboBox;
  @ViewChild('columnsCombo') columnsCombo: SwtComboBox;

  /***********SwtLabel***********/
  @ViewChild('tableLbl') tableLbl: SwtLabel;
  @ViewChild('columnsLbl') columnsLbl: SwtLabel;
  @ViewChild('colLbl') colLbl: SwtLabel;

  /***********SwtTextInput***********/
  @ViewChild('colLblTxt') colLblTxt: SwtTextInput;

    /***********SwtButton***********/
    @ViewChild('saveButton') saveButton: SwtButton;
    @ViewChild('closeButton') closeButton: SwtButton; 
  public selectCursor;
  constructor() { }

  ngOnInit() {
    this.tableLbl.text= SwtUtil.getPredictMessage("additionalColumns.label.table", null);
    this.columnsLbl.text= SwtUtil.getPredictMessage("additionalColumns.label.column", null);
    this.colLbl.text= SwtUtil.getPredictMessage("additionalColumns.label.colLabel", null);
    this.saveButton.label= SwtUtil.getPredictMessage('button.save', null);
    this.closeButton.label=  SwtUtil.getPredictMessage('button.close', null);
  }

  onLoad(){
    let listValues = this.selectCursor.select.option;
   // [this.jsonReader.getSelects()['select'].find(x => x.id == "selectedHost").option];
    this.tableCombo.setComboData(listValues);
    this.tableCombo.dataProvider = listValues;
    this.columnsCombo.setComboData(listValues);
    this.columnsCombo.dataProvider = listValues;
  }

  save(){

  }


  close(){

  }
}
