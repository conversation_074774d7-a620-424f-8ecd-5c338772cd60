<SwtModule (creationComplete)="onLoad()"  width="100%" height="100%">
  <VBox width="100%" height="100%">
    <SwtFieldSet style="height: 280px; width: 100%; padding-left:5px; padding-right: 5px; padding-top: 10px; color: blue " legendText="Events to launch for an active scenario instance">
      <VBox width="100%" height="100%">
        <HBox width="100%" paddingTop="5">
        <SwtCanvas #eventCanvas width="100%" height="155"></SwtCanvas>
          <VBox verticalAlign="middle" height="100%" paddingTop="60">
            <SwtButton id="imgUpButton"
                       #imgUpButton
                       styleName="upButtonEnable"
                       (click)="moveRecord('imgUpButton')"
                       buttonMode="false"
                       enabled="false">
            </SwtButton>
            <SwtButton id="imgDownButton"
                       #imgDownButton
                       styleName="downButtonEnable"
                       (click)="moveRecord('imgDownButton')"
                       buttonMode="false"
                       enabled="false">
            </SwtButton>
          </VBox>
        </HBox>
        <HBox horizontalGap="10"  height="20" paddingLeft="10" width="100%">
        <HBox width="65%"></HBox>
        <HBox  horizontalAlign="right" width="35%">

          <SwtImage width="19"  height="19" id="BtnLockImg" #BtnLockImg 
          (click)="changeBtnStatus(BtnLockImg)" 
          visible="false" includeInLayout="false">
          </SwtImage> 

          <SwtButton #addButton (click)="addEvHandler()"></SwtButton>
          <SwtButton #changeButton (click)="changeEvHandler()" enabled="false"></SwtButton>
          <SwtButton #viewButton (click)="viewEvHandler()" enabled="false"></SwtButton>
          <SwtButton #deleteButton (click)="deleteEvHandler()" enabled="false"></SwtButton>
        </HBox>
        </HBox>
        <HBox height="60">
          <SwtLabel #afterLaunchLbl width="18%"></SwtLabel>
          <VBox>
            <SwtRadioButtonGroup #triggeEventsRadioGroup   id="triggeEventsRadioGroup"
              enabled="false"   align="vertical" (change)="enableDisableFields()">
              <SwtRadioItem #radioActive id="radioActive" value="A" selected="true" groupName="triggeEventsRadioGroup"></SwtRadioItem>
              <SwtRadioItem #radioIResolved id="radioIResolved" value="R" groupName="triggeEventsRadioGroup"></SwtRadioItem>
              <SwtRadioItem #radioPending id="radioPending" value="P" groupName="triggeEventsRadioGroup" ></SwtRadioItem>
            </SwtRadioButtonGroup>
          </VBox>
        </HBox>
      </VBox>
    </SwtFieldSet>
    <SwtFieldSet style="height: 100%; width:100%; padding-left:5px; padding-right: 5px; color: blue " legendText="For Instances Pending Resolution">
      <Grid width="100%" height="100%">
        <GridRow height="30">
          <GridItem>
            <SwtLabel #resolutionQueryLbl></SwtLabel>
          </GridItem>
        </GridRow>
        <GridRow height="70%" minHeight="75" width="100%">
          <SwtTextArea #resolutionQueryText width="100%" height="100%"></SwtTextArea>
        </GridRow>
        <GridRow paddingTop="5" height="50" width="100%">
          <GridItem width="170">
            <SwtLabel #refColumnLbl></SwtLabel>
          </GridItem>
          <Hbox width="82%">
            <SwtMultiselectCombobox #refColumnCombo id='refColumnCombo' width="650"  height="100" showAbove="true"
            visible ="true"></SwtMultiselectCombobox>
          </Hbox>
        </GridRow>
        <GridRow paddingTop="9" height="50">
          <GridItem width="182">
            <SwtLabel #resolutionLbl></SwtLabel>
          </GridItem>
          <GridItem>
            <VBox width="100%" height="100%">
              <SwtRadioButtonGroup #resolOverdueRadioGroup   id="resolOverdueRadioGroup"
                enabled="false" align="vertical" (change)="enableDisableTxtInput()">
                <SwtRadioItem #radioNever id="radioNever" value="N"    selected="true" groupName="resolOverdueRadioGroup"></SwtRadioItem>
                 <SwtRadioItem #radioAfter id="radioAfter" value="A" groupName="resolOverdueRadioGroup" ></SwtRadioItem>
              </SwtRadioButtonGroup>

            </VBox>
            <HBox marginTop="15" marginLeft="5">
            <SwtNumericInput #minsText textAlign="right" width="50"></SwtNumericInput>
            <SwtLabel #minsLbl fontWeight="normal" paddingLeft="5"></SwtLabel>
            </HBox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtFieldSet>
  </VBox>
</SwtModule>
