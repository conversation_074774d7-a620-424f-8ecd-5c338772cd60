<SwtModule #swtModule (creationComplete)='onLoad()' height='100%'  width='100%' (close)="popupClosed()">
  <VBox id="vbox1"
        width='100%' height='95%' paddingBottom="5" paddingLeft="5" paddingRight="5" paddingTop="5">
    <SwtCanvas width="100%" height="92%">
      <VBox width="100%" height="100%">
      <VBox width="100%" height="12%">
        <HBox width="100%" paddingLeft="10">
          <HBox width="40%">
            <SwtLabel width="150" #accountGpIdLabel>
            </SwtLabel>
            <SwtTextInput id="accountGpId"
                          #accountGpId
                          (focusOut)="validateAccountIdHandler()"
                          restrict="a-zA-Z0-9\-_"
                          maxChars="20"
                          width="200">
            </SwtTextInput>

          </HBox>
          <HBox width="60%">
            <SwtLabel #accountGrpNameLabel width="150"></SwtLabel>
            <SwtTextInput id="accountGpName"
                          #accountGpName
                          (focusOut)="validateAccountNameHandler()"
                          restrict="A-Za-z0-9\d_ !\&quot;#$%&'()*+,\-.\/:;&lt;=&gt;?@[\\\]^`{|}~"
                          maxChars="50"
                          width="350">
            </SwtTextInput>
          </HBox>
        </HBox>
        <HBox width="100%" paddingLeft="10">
          <SwtLabel #currencyLabel width="150"></SwtLabel>
          <SwtComboBox id="currencyComboBox"
                       #currencyComboBox
                       width="200"
                       dataLabel="currencyList"
                       enabled="true"
                       (change)="changeComboCurrency()">
          </SwtComboBox>
          <SwtLabel #ccyLabel fontWeight="normal"  text=" "></SwtLabel>
        </HBox>
      </VBox>
  <SwtTabNavigator #aggAccountNavigator height="90%" width="100%">

  </SwtTabNavigator>
      </VBox>
    </SwtCanvas>
    <SwtCanvas id="canvasContainer"
               width="100%">
      <HBox width="100%" height="100%">
        <SwtButton #saveButton
                   (click)="save()"
                   (keyDown)="keyDownEventHandler($event)"
                   id="saveButton"></SwtButton>
        <SwtButton #amendButton
                     width="70" 
                    label="Amend"
                    visible="false" includeInLayout="false"
                   (click)="amendEventHandler()"
                   (keyDown)="keyDownEventHandler($event)"
                   id="amendButton"></SwtButton>
      <SwtButton buttonMode="true"
                   id="cancelAmendButton"
                   visible="false" includeInLayout="false"
                   #cancelAmendButton
                   (click)="cancelAmendEventHandler();"
                   (keyDown)="keyDownEventHandler($event)"></SwtButton>
      <SwtButton buttonMode="true"
                   id="closeButton"
                   marginLeft="5"
                   visible="false" includeInLayout="false"
                   #closeButton
                   (click)="popupClosed();"
                   (keyDown)="keyDownEventHandler($event)"></SwtButton>
        <SwtButton buttonMode="true"
                   id="cancelButton"
                   marginLeft="5"
                   #cancelButton
                   (click)="popupClosed();"

                   (keyDown)="keyDownEventHandler($event)"></SwtButton>
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
      </HBox>
      <HBox horizontalAlign="right" paddingRight="10">
        <SwtButton #acceptButton (click)="acceptEventEventHandler()" visible="false" label="Accept" (keyDown)="keyDownEventHandler($event)" id="acceptButton"
          width="70"></SwtButton>
        <SwtButton buttonMode="true" id="rejectButton" width="70" visible="false" label="Reject" #rejectButton (click)="rejectEventEventHandler();"
          (keyDown)="keyDownEventHandler($event)"></SwtButton>
      </HBox>

    </SwtCanvas>
  </VBox>
</SwtModule>

