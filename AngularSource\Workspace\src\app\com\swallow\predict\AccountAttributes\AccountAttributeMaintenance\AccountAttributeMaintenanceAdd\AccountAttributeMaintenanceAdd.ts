import {Component, ElementRef, ModuleWithProviders, NgModule, OnDestroy, OnInit, ViewChild} from '@angular/core';
import 'rxjs/add/observable/interval';
import {
  Alert,
  CommonService, CommonUtil,
  ExternalInterface, HBox,
  HTTPComms,
  JSONReader, StringUtils,
  SwtAlert,
  SwtButton,
  SwtComboBox,
  SwtDateField,
  SwtLabel, SwtLoadingImage,
  SwtModule, SwtStepper,
  SwtTextInput,
  SwtToolBoxModule,
  SwtUtil
} from 'swt-tool-box';
import {RouterModule, Routes} from '@angular/router';
import moment from "moment";


declare var instanceElement: any;
@Component({
  selector: 'app-account-attribute-add',
  templateUrl: './AccountAttributeMaintenanceAdd.html',
  styleUrls: ['./AccountAttributeMaintenanceAdd.css']
})
export class AccountAttributeMaintenanceAdd extends  SwtModule implements OnInit  , OnDestroy{


  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  /*********Combobox*********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('accountCombo') accountCombo: SwtComboBox;
  @ViewChild('attributeCombo') attributeCombo: SwtComboBox;
  @ViewChild('typesCombo') typesCombo: SwtComboBox;
  /*********SwtLabel*********/
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('accountLabel') accountLabel: SwtLabel;
  @ViewChild('selectedAccount') selectedAccount: SwtLabel;
  @ViewChild('attributeLabel') attributeLabel: SwtLabel;
  @ViewChild('selectedAttribute') selectedAttribute: SwtLabel;
  @ViewChild('startDateLabel') startDateLabel: SwtLabel;
  @ViewChild('valueDateLabel') valueDateLabel: SwtLabel;
  @ViewChild('typeLabel') typeLabel: SwtLabel;
  @ViewChild('valueNumLabel') valueNumLabel: SwtLabel;
  @ViewChild('valueLabel') valueLabel: SwtLabel;
  @ViewChild('timeLabel') timeLabel: SwtLabel;
  @ViewChild('valueTimeLabel') valueTimeLabel: SwtLabel;
  /*********SwtTextInput*********/
  @ViewChild('valueField') valueField: SwtTextInput;
  @ViewChild('valueNumField') valueNumField: SwtTextInput;
  /*********SwtButton*********/
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  /*********SwtDateField*********/
  @ViewChild('effectiveDate') effectiveDate: SwtDateField;
  @ViewChild('valueDate') valueDate: SwtDateField;
  /*********SwtNumericStepper*********/
  @ViewChild('timeHours') timeHours: SwtStepper;
  @ViewChild('timeMinutes') timeMinutes: SwtStepper;
  @ViewChild('timeSeconds') timeSeconds: SwtStepper;
  @ViewChild('valueTimeHours') valueTimeHours: SwtStepper;
  @ViewChild('valueTimeMinutes') valueTimeMinutes: SwtStepper;
  @ViewChild('valueTimeSeconds') valueTimeSeconds: SwtStepper;
  /*********SwtLoadingImage*********/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********HBox*************/
  @ViewChild('hboxTypeNumeric') hboxTypeNumeric: HBox;
  @ViewChild('hboxTypeDate') hboxTypeDate: HBox;
  @ViewChild('hboxTypeText') hboxTypeText: HBox;

  /**
   * Communication Objects
   **/
  private inputData  = new HTTPComms(this.commonService);
  private baseURL  = SwtUtil.getBaseURL();
  private actionMethod;
  private actionPath;
  private requestParams = [];
  private invalidComms = "";


  /**
   * Data Objects
   **/
  private jsonReader: JSONReader = new JSONReader();
  private lastReceivedJSON;
  private prevRecievedJSON;

  private swtAlert: SwtAlert;
  private errorLocation = 0;

  private dateFrom: string;
  private dateFormat: string = null;
  private testDate: string;

  private  entityId = null;
  private  entityName = null;
  private  seqKey = null;
  private currencyCode  = null;
  private currencyName  = null;
  private  accountId = null;
  private  accountName = null;
  private  attributeId = null;
  private  attributeName = null;
  private effectiveDateTime = null;
  private  value = null;
  private valResult: string = null;

  private screenNameForPopup = "Account Attributes Maintenance Screen";
  private  versionNumber = "1.0";

  private  methodName =  null;
  private	 validateTextRegexMsg: string = null;
  private  validateTextRegex: string  = null;
  private  validateTextMinLen = null;
  private  validateTextMaxLen = null;
  private  validateNumMin = null;
  private  validateNumMax = null;
  private minLength = null;
  private maxLength = null;
  private  tooltipText = null;
  public   MIN_VALUE = -1000000000000000000000;
  public   MAX_VALUE = 1000000000000000000000;
  /**
   * This method is called to validate the selected date and enable or disable the time part according
   * to attribute allow time
   */
  protected function; 
  ngOnInit(): void {
    instanceElement = this;
    /**Text and tooltip*********/
    this.currencyLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.currency', null)+ "*";
    this.ccyCombo.toolTip  = SwtUtil.getPredictMessage('label.accountattribute.currency', null);
    this.entityLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.entity', null)+ "*";
    this.entityCombo.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.entity', null);
    this.accountLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.accountId', null)+ "*";
    this.accountCombo.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.account', null);
    this.attributeLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.attribute', null)+ "*";
    this.attributeCombo.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.attribute', null);
    this.effectiveDate.toolTip= SwtUtil.getPredictMessage("tip.accountattributeadd.effectivedate");
    this.startDateLabel.text = SwtUtil.getPredictMessage('label.accountattributeadd.effectivedate', null);
    this.valueLabel.text  = SwtUtil.getPredictMessage("label.accountattributeadd.value", null) + "*";
    this.valueNumLabel.text  = SwtUtil.getPredictMessage("label.accountattributeadd.value", null) + "*";
    this.valueDateLabel.text  = SwtUtil.getPredictMessage("label.accountattributeadd.value", null) + "*";
    this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    this.saveButton.toolTip = SwtUtil.getPredictMessage('tooltip.save', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.timeLabel.text = SwtUtil.getPredictMessage("label.accountattributeadd.time", null);
    this.valueTimeLabel.text = SwtUtil.getPredictMessage("label.accountattributeadd.time", null);
    this.timeLabel.toolTip = SwtUtil.getPredictMessage("tip.accountattributeadd.time", null);
    this.typeLabel.text  = SwtUtil.getPredictMessage("label.accountattributeadd.type", null);
    this.typesCombo.toolTip  = SwtUtil.getPredictMessage("label.accountattributeadd.type", null);
    this.hboxTypeNumeric.visible = true;
    this.hboxTypeNumeric.includeInLayout = true;
    this.hboxTypeDate.visible = false;
    this.hboxTypeDate.includeInLayout = false;
    this.hboxTypeText.visible = false;
    this.hboxTypeText.includeInLayout = false;

  }
  ngOnDestroy(): any {
    instanceElement = null;
  }

  /**
   * Upon completion of loading into the flash player this method is called
   **/
  onLoad() {
    this.dateFormat = ExternalInterface.call('eval', 'dateFormat');
    this.testDate = ExternalInterface.call('eval', 'dbDate');
    this.entityId = ExternalInterface.call('eval', 'entityId');
    this.entityName = ExternalInterface.call('eval', 'entityName');
    this.currencyCode = ExternalInterface.call('eval', 'currencyCode');
    this.currencyName = ExternalInterface.call('eval', 'currencyName');
    this.accountId = ExternalInterface.call('eval', 'accountId');
    this.accountName = ExternalInterface.call('eval', 'accountName');
    this.attributeId = ExternalInterface.call('eval', 'attributeId');
    this.attributeName = ExternalInterface.call('eval', 'attributeName');
    this.value = ExternalInterface.call('eval', 'valueStr');
    this.effectiveDateTime = ExternalInterface.call('eval', 'effectivedateTime');
    this.methodName = ExternalInterface.call('eval', 'methodName');
    this.seqKey = ExternalInterface.call('eval', 'seqKey');
    this.effectiveDate.formatString =   this.dateFormat;
    this.inputData.cbStart  = this.startOfComms.bind(this);
    this.inputData.cbStop  = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault  = this.inputDataFault.bind(this);
    this.inputData.encodeURL  = false;
    this.requestParams = [];
    this.actionMethod = 'method=accountAttributesValuesAdd';
    this.actionPath = 'accountAttribute.do?';
    this.requestParams['entityId']  = this.entityId;
    this.requestParams['currencyCode']  = this.currencyCode;
    this.requestParams['accountId']  = this.accountId;
    this.requestParams['attributeId']  = this.attributeId;
    this.requestParams['effectivedateTime']  = this.effectiveDateTime;
    this.requestParams['methodName']  = this.methodName;

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

  }

  /**
   * This method will change text color of the tooltip to black
   */
  textInput_toolTipShown(evt): void {
    let tt = evt.toolTip;
    tt.setStyle("color", "#000000");
  }


  /**
   * saveDataResult
   * @param event:ResultEvent
   *
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   */
  saveDataResult(event): void {
    this.lastReceivedJSON = event;
    let jsonResponse: JSONReader = new JSONReader();
    jsonResponse.setInputJSON(this.lastReceivedJSON);
    if (jsonResponse.getRequestReplyMessage() == "Data fetch OK") {
      if(window.opener.instanceElement) {
        // call the closeWindow method in jsp
        ExternalInterface.call("closeWindow");
      }
    } else {// display an alert warning since the record is already exist
      if (jsonResponse.getRequestReplyMessage() == "errors.DataIntegrityViolationExceptioninAdd"
        || jsonResponse.getRequestReplyMessage() == "errors.DataIntegrityViolationExceptioninChange") {
        this.swtAlert.warning(SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninAdd', null), SwtUtil.getPredictMessage('screen.warning', null));
      }
    }
  }

  /**
   * disableEnableEffectiveDateTimeFields
   * @param enable:Boolean (enable or disable fields)
   * This function disable or enable the time fields if no date was seleced
   * or if Time part is not allowed in the attribute definition
   */
  disableEnableEffectiveDateTimeFields(enable: boolean): void {
    this.timeHours.text = 0;
    this.timeHours.enabled = enable;
    this.timeMinutes.text = 0;
    this.timeMinutes.enabled = enable;
    this.timeSeconds.text = 0;
    this.timeSeconds.enabled = enable;
  }

  inputDataResult(event) {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastReceivedJSON = event;
      this.jsonReader.setInputJSON(this.lastReceivedJSON);
      if ((JSON.stringify(this.lastReceivedJSON) !== JSON.stringify(this.prevRecievedJSON))) {
        if (this.jsonReader.getRequestReplyStatus()) {

          this.entityCombo.setComboData( this.jsonReader.getSelects(), false);
          this.selectedEntity.text  = this.entityCombo.selectedItem.value;

          this.ccyCombo.setComboData( this.jsonReader.getSelects(), false);
          this.selectedCcy.text  = this.ccyCombo.selectedItem.value;

          this.accountCombo.setComboData(this.jsonReader.getSelects(), false);
          this.selectedAccount.text  = this.accountCombo.selectedItem.value;

          this.attributeCombo.setComboData(this.jsonReader.getSelects(), false);
          this.selectedAttribute.text  =  this.attributeCombo.selectedItem.value;

          this.typesCombo.setComboData(this.jsonReader.getSelects(), false);

          this.dateFrom =  ExternalInterface.call('eval', 'dateFormat');

          if (!this.jsonReader.isDataBuilding()) {
            let timeValue;
            let dateValue: string;
            let dateTimeArr ;
            if("Y" != (this.jsonReader.getSingletons().attrEffDateRequired)) {
              this.effectiveDate.enabled = false;
              this.effectiveDate.selectedDate = null;
              this.timeHours.text = 0;
              this.timeMinutes.text = 0;
              this.timeSeconds.text = 0;
              this.timeHours.enabled=false;
              this.timeMinutes.enabled=false;
              this.timeSeconds.enabled=false;
            } else {
              this.effectiveDate.enabled = true;
              if (this.effectiveDateTime == "") {
                this.effectiveDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate, this.dateFormat.toUpperCase()));
                timeValue = "00:00:00";
              } else {
                // set time fields values depending of effective date time by splitting on " "
                dateTimeArr = this.effectiveDateTime.split(" ");
                dateValue = dateTimeArr[0];
                timeValue = dateTimeArr[1];
                if(timeValue == null) {
                  timeValue = "00:00:00";
                }
                // set the date part
                this.effectiveDate.selectedDate = new Date(CommonUtil.parseDate(dateValue, this.dateFormat.toUpperCase()));
              }

              // If effective date  not allows to use time
              if ("N" == this.jsonReader.getSingletons().attrAllowTime) {
                // disable time component and set default value as 00:00:00
                this.timeHours.enabled = false;
                this.timeMinutes.enabled = false;
                this.timeSeconds.enabled = false;
                // if the method name is add then set time component to default "00:00:00"
                if ( this.methodName == "add") {
                  this.timeHours.text = 0;
                  this.timeMinutes.text = 0;
                  this.timeSeconds.text = 0;

                } else {
                  // Otherwise split the time part on ":" to set hours, minutes and seconds
                  this.timeHours.text =  timeValue.split(":")[0];
                  this.timeMinutes.text =  timeValue.split(":")[1];
                  this.timeSeconds.text =  timeValue.split(":")[2];
                }
              } else {
                // enable time component
                this.timeHours.enabled = true;
                this.timeMinutes.enabled = true;
                this.timeSeconds.enabled = true;
                // set the time component by the required values
                this.timeHours.text = timeValue.split(":")[0];
                this.timeMinutes.text = timeValue.split(":")[1];
                this.timeSeconds.text = timeValue.split(":")[2];
              }
            }

            if( this.typesCombo.selectedLabel == "Text") {
              this.hboxTypeNumeric.visible = false;
              this.hboxTypeNumeric.includeInLayout = false;
              this.hboxTypeDate.visible = false;
              this.hboxTypeDate.includeInLayout = false;
              this.hboxTypeText.visible = true;
              this.hboxTypeText.includeInLayout = true;
            } else if(this.typesCombo.selectedLabel == "Numeric") {
              this.hboxTypeNumeric.visible = true;
              this.hboxTypeNumeric.includeInLayout = true;
              this.hboxTypeDate.visible = false;
              this.hboxTypeDate.includeInLayout = false;
              this.hboxTypeText.visible = false;
              this.hboxTypeText.includeInLayout = false;
            } else {
              this.hboxTypeNumeric.visible = false;
              this.hboxTypeNumeric.includeInLayout = false;
              this.hboxTypeDate.visible = true;
              this.hboxTypeDate.includeInLayout = true;
              this.hboxTypeText.visible = false;
              this.hboxTypeText.includeInLayout = false;

            }

            this.validateTextRegexMsg = this.jsonReader.getSingletons().validatetextregexmsg;
            this.validateTextRegex = this.jsonReader.getSingletons().validatetextregex;
            this.validateTextMinLen = this.jsonReader.getSingletons().validatetextminlen;
            this.validateTextMaxLen = this.jsonReader.getSingletons().validatetextmaxlen;
            this.validateNumMin = this.jsonReader.getSingletons().validatenummin;
            this.validateNumMax = this.jsonReader.getSingletons().validatenummax;
            this.tooltipText	= this.jsonReader.getSingletons().tooltipText;

            this.prevRecievedJSON = this.lastReceivedJSON;

            if (!(this.methodName == "add")) {
              this.accountCombo.enabled = false;
              this.entityCombo.enabled = false;
              this.ccyCombo.enabled = false;
              this.attributeCombo.enabled = false;
              if(this.methodName == "view") {
                this.effectiveDate.enabled=false;
                this.timeHours.enabled=false;
                this.timeMinutes.enabled=false;
                this.timeSeconds.enabled=false;
                this.saveButton.enabled = false;
              }
            }
            // Set value depending of attribute type

            let validateDateAllowTime =  this.jsonReader.getSingletons().validateDateAllowTime;
            this.setValueData(validateDateAllowTime);
          }
        } else {
          this.swtAlert.error(SwtUtil.getPredictMessage('label.errorContactSystemAdmin', null),SwtUtil.getPredictMessage('screen.error', null));
        }
      }
    }
  }

  /**
   * Method used to set the value depending of the attribute type
   * param : validateDateAllowTime : if date type then enable  time part depending of this param
   */
   setValueData(validateDateAllowTime: string): void {
    if(this.typesCombo.selectedLabel == "Text" ) {
      this.valueField.text = this.value;
      this.valueField.toolTip = this.tooltipText;
    } else if(this.typesCombo.selectedLabel == "Numeric" ) {
      this.valueNumField.text = (this.value) ? this.value : "";
      this.valueNumField.toolTip = this.tooltipText;
    } else {
      if(this.value != null && this.value !== "") {
        // if attribute type is date then select date and time part
        this.valueDate.formatString=this.dateFormat;
        let arr2 = this.value.split(" ");
        this.valueDate.selectedDate = new Date(CommonUtil.parseDate(arr2[0], this.dateFormat.toUpperCase()));
        let str = arr2[1];
        if(str == null) {
          str = "00:00:00";
        }
        this.valueTimeHours.text = str.split(":")[0];
        this.valueTimeMinutes.text = str.split(":")[1];
        this.valueTimeSeconds.text = str.split(":")[2];
      } else {
        this.valueDate.formatString = this.dateFormat;
        this.valueDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate, this.dateFormat.toUpperCase()));
        this.valueTimeHours.text = 0;
        this.valueTimeMinutes.text = 0;
        this.valueTimeSeconds.text = 0;
      }
      // If time is not allowed then disable time fields
      if("N" == validateDateAllowTime) {
        this.valueTimeHours.enabled = false;
        this.valueTimeMinutes.enabled = false;
        this.valueTimeSeconds.enabled = false;
      }
    }
   // if the screen is opened by clickong on view button then disable all fields
    if(this.methodName == "view") {
      if(this.typesCombo.selectedLabel == "Text" ) {
        this.valueField.enabled = false;
      } else if(this.typesCombo.selectedLabel == "Numeric" ) {
        this.valueNumField.enabled = false;
      } else {
        this.valueDate.enabled = false;
        this.valueTimeHours.enabled = false;
        this.valueTimeMinutes.enabled = false;
        this.valueTimeSeconds.enabled = false;
      }
    }
  }

  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   * @param event:FaultEvent
   **/
  public inputDataFault( event ): void {
    this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  /**
   * This method format time before sending it to server by adding ":" between hours minutes and seconds
   */
  formatTimeUsingNumberStpper(hours: SwtStepper, mins: SwtStepper, secds: SwtStepper): string {
    let result = " ";
    if(hours.enabled && mins.enabled && secds.enabled) {
      result += this.formatNumber(hours) + ":" ;
      result += this.formatNumber(mins) + ":" ;
      result += this.formatNumber(secds);
    } else {
      result = " 00:00:00";
    }
    return result;
  }


  /**
   * This method is used to validate then save/update an account attribute value
   */
  saveClickHandler(): void {
    let validationResult = true;
    if (this.entityCombo.selectedIndex == -1 || this.ccyCombo.selectedIndex == -1 || this.accountCombo.selectedIndex == -1 || this.attributeCombo.selectedIndex == -1) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.pleaseFillAllMandatoryFields', null),SwtUtil.getPredictMessage('screen.warning', null));
      return ;
    }
    // if effective date is required then validate the fields
    if ("Y" == (this.jsonReader.getSingletons().attrEffDateRequired) && this.effectiveDate.text.length == 0) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('errors.effectiveDateRequired', null),SwtUtil.getPredictMessage('screen.warning', null));
    } else {
      // If the type is text then validate field according to attribute definition
      if (this.typesCombo.selectedLabel == "Text") {
        if(StringUtils.trim(this.valueField.text) == "") {
          this.valueField.required = true;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.pleaseFillAllMandatoryFields', null),SwtUtil.getPredictMessage('screen.warning', null));
          return ;
        }
        // Check text lenght and validate if it's shorter or longer
        if((this.validateTextMinLen)) {
          this.minLength = parseInt( this.validateTextMinLen,10);
        }
        if((this.validateTextMaxLen )) {
          this.maxLength = parseInt(this.validateTextMaxLen,10);
        }

        // If a regex is defined then validate regex and show the appropriate alert set on the attribute definition if the value is null show a default alert
       // valResult = stringVal.validate();
        if (this.validate(this.valueField.text)) {
          if (StringUtils.trim(this.validateTextRegex).length > 0) {
            validationResult = new RegExp(this.validateTextRegex).test(StringUtils.trimRight(this.valueField.text));
          }
          if (!validationResult) {
            if (StringUtils.trim(this.validateTextRegexMsg).length > 0) {
              this.swtAlert.invalid(SwtUtil.getPredictMessage('screen.invalid', null));
            } else {
              this.swtAlert.invalid(SwtUtil.getPredictMessage('errors.valuenotmatchpatteren', null),SwtUtil.getPredictMessage('screen.invalid', null));
            }
            return;
          }
        } else {
          if (this.valResult.indexOf("minimum") != -1) {
            this.swtAlert.invalid(SwtUtil.getPredictMessage('errors.stringistoosmall', null).replace("!!!", this.validateTextMinLen),SwtUtil.getPredictMessage('screen.invalid', null));
          } else if (this.valResult.indexOf("maximum") != -1) {
            this.swtAlert.invalid(SwtUtil.getPredictMessage('errors.stringistoolarge', null).replace("!!!", this.validateTextMaxLen),SwtUtil.getPredictMessage('screen.invalid', null));
          }
          return;
        }
      } else if(this.typesCombo.selectedLabel == "Numeric"  ) {
        let value: number;
        if(this.valueNumField.text == "") {
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.pleaseFillAllMandatoryFields', null),SwtUtil.getPredictMessage('screen.invalid', null));
          return;
        }


        let minV = (this.validateNumMin) ? Number((this.validateNumMin).replace(/,/g, '.')) : this.MIN_VALUE;
        let maxV = (this.validateNumMax) ? Number((this.validateNumMax).replace(/,/g, '.')) : this.MAX_VALUE;
        this.valueNumField.text=(this.valueNumField.text).replace(/,/g, '.');
        if(Number(this.valueNumField.text) || Number(this.valueNumField.text) == 0) {
          value = Number(this.valueNumField.text);
        } else {
          this.swtAlert.invalid(SwtUtil.getPredictMessage('errors.invalidNumber', null),SwtUtil.getPredictMessage('screen.invalid', null));
          this.valueNumField.text ="";
          return;
        }

        if(value > maxV) {
          this.swtAlert.invalid(SwtUtil.getPredictMessage('errors.valuetoolarge', null)+ maxV,SwtUtil.getPredictMessage('screen.invalid', null));
          this.valueNumField.text ="";
          return;
        } else if (value < minV) {
          this.swtAlert.invalid(SwtUtil.getPredictMessage('errors.valuetoosmall', null)+ minV,SwtUtil.getPredictMessage('screen.invalid', null));
          this.valueNumField.text ="";
          return;
        }

      } else if(this.typesCombo.selectedLabel == "Date" ) {
        if ((this.valueDate.text).length == 0) {
          this.swtAlert.invalid(SwtUtil.getPredictMessage('errors.valueDateRequired', null),SwtUtil.getPredictMessage('screen.invalid', null));
          return;
        }
      }
      this.entityId = this.entityCombo.selectedItem.content;
      this.currencyCode = this.ccyCombo.selectedItem.content;
      this.accountId = this.accountCombo.selectedItem.content;
      this.attributeId = this.attributeCombo.selectedItem.content;

      this.actionMethod  = 'method=saveAccountAttributeValue';
      this.requestParams  = [];
      this.requestParams['entityId']  = this.entityId;
      this.requestParams['currencyCode']  = this.currencyCode;
      this.requestParams['accountId']  = this.accountId;
      this.requestParams['attributeId']  = this.attributeId;
      this.requestParams['selectedType'] = this.typesCombo.selectedLabel;
      this.requestParams['methodName']  = this.methodName;
      this.requestParams['effectiveDate']  = ((this.effectiveDate.text).length > 0) ? this.effectiveDate.text + this.formatTimeUsingNumberStpper(this.timeHours,this.timeMinutes,this.timeSeconds) : "";

      // Set value depending of the attribute type
      if( this.typesCombo.selectedLabel == "Numeric") {
        this.requestParams['valueData']  =  StringUtils.trimRight(this.valueNumField.text);
     } else if(this.typesCombo.selectedLabel == "Text") {
        this.requestParams['valueData']  =  this.valueField.text;
     } else {
        this.requestParams['valueData']  = ((this.valueDate.text).length > 0 ? this.valueDate.text + this.formatTimeUsingNumberStpper(this.valueTimeHours, this.valueTimeMinutes, this.valueTimeSeconds):"");
     }
      if (this.methodName !== "add") {
        this.requestParams['seqKey'] = this.seqKey;
      }

      this.inputData.cbResult = (event) => {
        this.saveDataResult(event);
      };
      this.inputData.cbFault  = this.inputDataFault.bind(this);
      this.inputData.url  = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
   }
  }


  validate(text: string): boolean {
    let result = false;
    if((text) && (text!= "") ) {
      result = true;
    }

    if( text.length > this.maxLength ) {
      this.valResult = "maximum";
      result = false;
    } else if(text.length < this.minLength) {
      this.valResult = "minimum";
      result = false;
    }
    return result;
  }

  numberValidator(textInput: SwtTextInput): void {
   let str = textInput.text;
   if(textInput.id =='valueNumField'){
    str= str.replace(/,/g, '.');
   }
    
    let message = "'"+str+"'"+ SwtUtil.getPredictMessage('alert.accountattributehdr.checknumbervalue', null);
    let title = SwtUtil.getPredictMessage('screen.warning', null);
    // Alert.okLabel = "OK";
    if (isNaN(Number(str))&& str.length>1) {
      this.swtAlert.warning(message, title, Alert.OK , null, (data) => {this.clearInput(data, textInput);}, null);
    }
  }



  clearInput(event, textInput: SwtTextInput): void {
    if(event.detail == Alert.OK ) {
      textInput.text= "";
      textInput.setFocus();
    }
  }
  /**
   * changeCombo
   * This function is called when there is a change in one of the combo's
   **/
  changeCombo(e): void {
    let from = null;

    if(e.target.id == "entityCombo") {
      from = "entity";
    } else if (e.target.id == "ccyCombo") {
      from = "ccy";
    } else if (e.target.id == "accountCombo") {
      from = "account";
    } else if (e.target.id == "attributeCombo") {
      from = "attribute";
    }
    this.updateData(from);
  }


  /**
   * Update the data, this is called whenever a fresh of the data is required.
   * This could be called from either a change in a combobox selection of from the timer
   **/
  updateData(from: string = null): void {
    this.entityId = this.entityCombo.selectedItem.content;
    this.currencyCode = this.ccyCombo.selectedItem.content;
    this.accountId = this.accountCombo.selectedItem.content;
    this.attributeId = this.attributeCombo.selectedItem.content;
    if(this.effectiveDate.enabled == true && this.effectiveDate.selectedDate != null) {
      this.effectiveDateTime = this.effectiveDate.text;
    } else {
      this.effectiveDateTime = "";
    }
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.actionMethod = 'method=accountAttributesValuesAdd';
    this.requestParams = [];
    this.requestParams['entityId']  = this.entityId;
    this.requestParams['currencyCode']  = this.currencyCode;
    this.requestParams['accountId'] =  this.accountId;
    this.requestParams['attributeId'] = this.attributeId;
    this.requestParams['effectivedateTime'] =  this.effectiveDateTime;
    this.requestParams['methodName'] = this.methodName;
    this.requestParams['from']  = from;

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }


  /**
   * Event handled when changing the Process category combobox value
   *
   * @ param {*} event
   */
  typeChangeCombo(e) {
    if( this.typesCombo.selectedLabel == "Numeric") {
      this.hboxTypeNumeric.visible = true;
      this.hboxTypeNumeric.includeInLayout = true;
      this.hboxTypeDate.visible = false;
      this.hboxTypeDate.includeInLayout = false;
      this.hboxTypeText.visible = false;
      this.hboxTypeText.includeInLayout = false;
    } else if(this.typesCombo.selectedLabel == "Date") {
      this.hboxTypeNumeric.visible = false;
      this.hboxTypeNumeric.includeInLayout = false;
      this.hboxTypeDate.visible = true;
      this.hboxTypeDate.includeInLayout = true;
      this.hboxTypeText.visible = false;
      this.hboxTypeText.includeInLayout = false;
    } else {
      this.hboxTypeNumeric.visible = false;
      this.hboxTypeNumeric.includeInLayout = false;
      this.hboxTypeDate.visible = false;
      this.hboxTypeDate.includeInLayout = false;
      this.hboxTypeText.visible = true;
      this.hboxTypeText.includeInLayout = true;

    }

  }

  /**
   * Method is called when a numeric stepper value has changed
   */
  valueCommitHandler(event): void {
    this.formatStepper(event.target as SwtStepper);

  }
  /**
   * This method format the numeric stepper by adding a "0" if the value is lower then "10"
   */
  formatStepper(stepper: SwtStepper): void {
    if(stepper.value < 10) {
      // stepper.text = "0" + stepper.value;
    }
  }

  /**
   * This method format the numeric stepper by adding a "0" if the value is lower then "10"
   */
  formatNumber(value: SwtStepper): string {
    let valueAux = value.text;
    if(!valueAux || valueAux == null) {
      return '00' ;
    } else {
      if (valueAux < 10) {
        return '0' + valueAux;
      }
    }
    return valueAux.toString();
  }

  selectedDate_changeHandler(effectiveDate: SwtDateField): void {
    let showTime = false;
    if(this.validateDateField(effectiveDate)) {
      showTime = ! ("N" == this.jsonReader.getSingletons().attrAllowTime);
    } else {
      showTime = false;
    }

    if(!showTime) {
      this.disableEnableEffectiveDateTimeFields(false);
    } else {
      this.timeHours.enabled = true;
      this.timeMinutes.enabled = true;
      this.timeSeconds.enabled = true;
    }
  }

  /**
   * This method is called to validate the selected date and enable or disable the time part according
   * to attribute allow time
   */
  valueDateChangeHandler(): void {
    if ((this.valueDate.text).length > 0 && !this.validateDateField(this.valueDate)) {
      this.timeHours.enabled = true;
      this.timeMinutes.enabled = true;
      this.timeSeconds.enabled = true;
    }
  }


  closeHandler() {
    ExternalInterface.call("closeChild");
  }


  getParamsFromParent(): any {
   let  params = [{timeFrame: ""}];
   return params;
  }


  /**
   * doHelp
   * Function is called when 'Help' button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call('help');
    } catch (e) {

    }
  }

  validateDateField(dateField) {
    try {
      let date;
      const alert = SwtUtil.getPredictMessage('alert.validDate', null);
      if(dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase() , true);

        if(!date.isValid()) {
          this.swtAlert.warning(alert+ "("+ this.dateFormat.toUpperCase()+ ")");
          return false;
        }
      }
      dateField.selectedDate = date.toDate();
    } catch(error) {
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 'AccountAttributeMaintenance', ' validateDateField', this.errorLocation);
    }

    return true;
  }

}


// Define lazy loading routes
const routes: Routes = [
  { path: '', component: AccountAttributeMaintenanceAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AccountAttributeMaintenanceAdd],
  entryComponents: []
})
export class AccountAttributeMaintenanceAddModule {}
