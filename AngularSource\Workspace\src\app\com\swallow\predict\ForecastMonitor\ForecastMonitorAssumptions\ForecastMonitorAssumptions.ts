
import {Component, ElementRef, NgModule, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService, ExternalInterface,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas, SwtCommonGrid,
  SwtLoadingImage, SwtModule, SwtUtil, SwtToolBoxModule, SwtDataExport, ExportEvent, SwtLabel, ContextMenuItem, SwtPopUpManager, JSONViewer, ScreenVersion, SwtText, SwtTextInput,
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";
declare var instanceElement: any;
@Component({
  selector: 'app-forcast-monitor-assumptions',
  templateUrl: './ForecastMonitorAssumptions.html',
  styleUrls: ['./ForecastMonitorAssumptions.css']
})

export class ForecastMonitorAssumptions extends  SwtModule  implements  OnInit, OnDestroy {

  @ViewChild('forecastMonitorAssumptionsCanvas') forecastMonitorAssumptionsCanvas: SwtCanvas;

  /********SwtButton*********************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') btnChange: SwtButton;
  @ViewChild('deleteButton') btnDelete: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  /*********LodingImage*************/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('dataExport') exportContainer: SwtDataExport;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  /*********SwtLabel*********/
  @ViewChild('templateIdLabel') templateIdLabel: SwtLabel;
  @ViewChild('lblTemplateId') lblTemplateId: SwtLabel;

  /*********SwtText*********/
  @ViewChild('lostConnectionText') lostConnectionText: SwtTextInput;

   /* Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public sendData = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;
  private swtAlert: SwtAlert;
  private invalidComms = '';

  public menuAccessId: string = null;
  private  templateId: string=null;
  private userId: string=null;
  public fourEyesRequired = true;
  private  screenName="Forecast Assumption Screen";
  private versionNumber="1.1.0001";
  private releaseDate = '20 August 2020';
  public screenVersion  = new ScreenVersion(this.commonService) ;
  public showJSONPopup: any;
  private  menuAccessIdParent=0;
  private  currencyFormat="";
  private assumptionGrid: SwtCommonGrid;
  
  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    Window['Main'] = this;
    this.swtAlert = new SwtAlert(commonService);
  }

  public static ngOnDestroy(): any {
    instanceElement = null;
  }
  ngOnInit(): void {
    instanceElement = this;
    this.assumptionGrid = this.forecastMonitorAssumptionsCanvas.addChild(SwtCommonGrid) as SwtCommonGrid;
    this.addButton.label = SwtUtil.getPredictMessage('button.forecastMonitor.add', null);
    this.btnChange.label = SwtUtil.getPredictMessage('button.forecastMonitor.change', null);
    this.btnDelete.label = SwtUtil.getPredictMessage('button.forecastMonitor.delete', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.forecastMonitor.close', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.add', null);
    this.btnChange.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.change', null);
    this.btnDelete.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.delete', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.close', null);
    this.lostConnectionText.text = SwtUtil.getPredictMessage('screen.connectionError', null);
    this.templateIdLabel.text = SwtUtil.getPredictMessage('label.forecastMonitor.templateId', null);
    this.assumptionGrid.onFilterChanged = this.disableButtons.bind(this);
    this.assumptionGrid.onSortChanged = this.disableButtons.bind(this);
    // label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
    // label["text"]["label-validPageNumber"] = "<fmt:message key="genericDisplayMonitor.validPageNumber"/>";
  }

  onLoad() {
    try {
      this.requestParams = [];

      this.initializeMenus();
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.btnChange.enabled = false;
      this.btnDelete.enabled = false;
      this.requestParams["forecastAssumption.currencyCode"] = ExternalInterface.call('eval','currencyCode');
      this.requestParams["forecastAssumption.entityId"] = ExternalInterface.call('eval','entityId');
      this.requestParams["forecastAssumption.valueDateAsString"] = ExternalInterface.call('eval','date');
      this.requestParams["forecastAssumption.templateId"] = ExternalInterface.call('eval','templateId');
      this.lblTemplateId.text = ExternalInterface.call('eval','templateId');
      this.sendData.cbStart = this.startOfComms.bind(this);
      this.sendData.cbStop = this.endOfComms.bind(this);
      this.sendData.cbResult = (data) => {
        this.sendDataResult(data);
      };
      this.sendData.cbFault = this.sendDataFault.bind(this);
      this.sendData.encodeURL = false;
      this.menuAccessIdParent=ExternalInterface.call('eval', 'menuAccessIdParent');
      if (this.menuAccessIdParent == 1) {
        // disable the button
        this.btnDelete.enabled = false;
      }
      this.actionMethod = 'method=displayAssumption';
      this.actionPath = 'forecastMonitor.do?';

      this.inputData.url =  this.baseURL + this.actionPath + this.actionMethod;
      this.sendData.url =  this.baseURL + this.actionPath + "method=deleteForecastAssumption";
      this.inputData.send(this.requestParams);
      this.assumptionGrid.onRowClick = (event) => {
        this.onGridCellClick(event);
      };

      ExportEvent.subscribe((type) => {
        this.report(type);
      });

    } catch (e) {
      console.log('errr', e);
    }
  }



  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   * @param event:FaultEvent
   **/
  sendDataFault(event): void {
    this.lostConnectionText.visible=true;
    // this.lostConnectionText.buttonMode=true;
    // this.lostConnectionText.useHandCursor=true;
    // this.lostConnectionText.mouseChildren=false;
    this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }

  /**
   * inputDataResult
   * @param data: ResultEvent
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(data): void {
    try {
      if (this.inputData && this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = data;
        this.exportContainer.enabled = true;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        this.lostConnectionText.visible=false;
        if (this.jsonReader.getRequestReplyStatus()) {
          this.currencyFormat = this.jsonReader.getScreenAttributes()["currencyFormat"];
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            if (!this.jsonReader.isDataBuilding()) {
              const obj = {columns: this.jsonReader.getColumnData()};
              this.assumptionGrid.CustomGrid(obj);
              if (this.jsonReader.getGridData().size > 0) {
                this.assumptionGrid.gridData = this.jsonReader.getGridData();
                this.assumptionGrid.setRowSize = this.jsonReader.getRowSize();
                this.assumptionGrid.rowHeight = 22;
              } else {
                this.assumptionGrid.dataProvider = [];
                this.assumptionGrid.selectedIndex = -1;
              }
            }

            if (this.jsonReader.getRowSize() < 1) {
              this.exportContainer.enabled=false;
              // this.exportContainer.skin.currentState="disabled";
            } else {
              this.exportContainer.enabled=true;
              // this.exportContainer.addEventListener(ExportEvent.EXPORT_CLICK, export);
            }
          }

          this.prevRecievedJSON = this.lastRecievedJSON;

        }
      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  /**
   *  This function updates the data changes made and refresh the grid.
   */
  public updateData(): void {
      this.requestParams = [];
      this.requestParams["forecastAssumption.currencyCode"] = ExternalInterface.call('eval','currencyCode');
      this.requestParams["forecastAssumption.entityId"] = ExternalInterface.call('eval','entityId');
      this.requestParams["forecastAssumption.valueDateAsString"] = ExternalInterface.call('eval','date');
      this.requestParams["forecastAssumption.templateId"] = ExternalInterface.call('eval','templateId');
      this.inputData.send(this.requestParams);
    }
  inputDataFault(event): void {
    this.lostConnectionText.visible=true;
    // lostConnectionText.buttonMode=true;
    // lostConnectionText.useHandCursor=true;
    // lostConnectionText.mouseChildren=false;
    this.invalidComms=event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;

  }



  private  initializeMenus(): void {
    // Creating a new instance for AboutScreen
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
    let contextMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    contextMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(contextMenuItem);
    this.contextMenu = this.screenVersion.svContextMenu;
  }

  /** This function is used to display the JSON
   *    for data showed in grid
   */
  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });

    // showXML.baseURL="forecastMonitor.do?method=displayForecastMonitorDetails";
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }



  /**
   * This method is called by the HTTPComms when result event occurs and used to refresh the grid<br>
   * @param event:ResultEvent
   **/
  sendDataResult(event): void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON=event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyStatus()) {
        this.updateData();
        ExternalInterface.call('refreshParent');
      }
    }
  }

  /**
   * This method is used to display the connection error
   **/
  connError(event): void {
    this.swtAlert.error("" + this.invalidComms, SwtUtil.getPredictMessage('screen.error', null));
  }

   closeHandler(): void {
    // call for close window
    ExternalInterface.call("close");
  }


  doHelp(): void {
    ExternalInterface.call('help');
  }
  /**
   * cellLogic
   * @param event: Event
   * This method is used to maintain the button status when a row is clicked
   */
  onGridCellClick(event: Event): void {
    try {
      if (this.assumptionGrid.selectedIndices.length > 0) {
        this.btnChange.enabled = true;
        this.btnDelete.enabled = true;
      } else {
        this.btnChange.enabled = false;
        this.btnDelete.enabled = false;
      }
      if (this.menuAccessIdParent == 1) {
        this.btnDelete.enabled = false;
      }


    } catch (e) {
      console.log('error event onGridCellClick', e);

    }
  }

  /**
   * close the window from the close button
   **/
  public deleteAssumption(): void {
    this.swtAlert.confirm(
      SwtUtil.getPredictMessage('alert.assumption.deleteConfirm', null),
      'Delete', Alert.OK | Alert.CANCEL,
      this,
      this.alertListener.bind(this), null);
  }

  /**
   * If the ok button is clicked in alert, this method is called
   * @param eventObj CloseEvent
   **/
  alertListener(eventObj): void {
    if (eventObj.detail == Alert.OK) {
      this.btnDelete.enabled=false;
      this.btnChange.enabled=false;
      this.requestParams=[];
      this.actionMethod="method=deleteForecastAssumption";
      let entity="";
      let currency="";
      this.requestParams["forecastAssumption.currencyCode"]=String(this.assumptionGrid.selectedItem.currency.content);
      this.requestParams["forecastAssumption.entityId"]=String(this.assumptionGrid.selectedItem.entity.content);
      this.requestParams["forecastAssumption.assumptionId"]= String(this.assumptionGrid.selectedItem.id.content);

      if (this.assumptionGrid.selectedItem && this.assumptionGrid.selectedItem.id && this.assumptionGrid.selectedItem.id.content) {
        this.requestParams["forecastAssumption.assumptionId"] = String(this.assumptionGrid.selectedItem.id.content);
      } else {
        this.requestParams["forecastAssumption.assumptionId"] = "";
      }


      this.sendData.url=this.baseURL +this.actionPath + this.actionMethod;
      // send the request
      this.sendData.send(this.requestParams);
    }
  }

  /**
   * This method is called when click the assumption button
   * @param method:String
   */
  assumptionClick(method: string): void {
    let param = "";
    let assumptions = new Object();
    assumptions["entity"] = ExternalInterface.call('eval','entityId');
    assumptions["currency"] = ExternalInterface.call('eval','currencyCode');
    assumptions["date"] = ExternalInterface.call('eval','date');
    assumptions["currencyName"] = ExternalInterface.call('eval','currencyName');
    assumptions["templateId"] = this.lblTemplateId.text;

    if (method == "add") {
      // param = ExternalInterface.call(wrapperFunction, usertemplate);
    } else if (method == "change") {
      assumptions["amount"] = String(this.assumptionGrid.selectedItem.amount.content);
      assumptions["assumption"] = String(this.assumptionGrid.selectedItem.assumption.content);
      assumptions["date"] = String(this.assumptionGrid.selectedItem.date.content);
      assumptions["currency"] = String(this.assumptionGrid.selectedItem.currency.content);
      assumptions["entity"] = String(this.assumptionGrid.selectedItem.entity.content);
      if (this.assumptionGrid.selectedItem && this.assumptionGrid.selectedItem.id && this.assumptionGrid.selectedItem.id.content) {
        assumptions["assumptionId"] = String(this.assumptionGrid.selectedItem.id.content);
      } else {
          assumptions["assumptionId"] = "";
      }
    }
    let wrapperFunction = "openModifyAssumptionWindow";
    ExternalInterface.call(wrapperFunction,assumptions);
  }

  /**
   * Method to disable or enable buttons
   * @param status:Boolean
   */
   disableOrEnableButtons(status: boolean): void {
    if (status) {
      this.btnChange.enabled=true;
      this.btnDelete.enabled= !((this.templateId == "*DEFAULT*" && this.userId == "*DEFAULT*"));
    } else {
      this.btnChange.enabled=false;
      this.btnDelete.enabled=false;
    }
  }
  disableButtons() {
     if(this.assumptionGrid.selectedIndex == -1) {
       this.btnChange.enabled=false;
       this.btnDelete.enabled=false;
     } else {
       this.btnChange.enabled = true;
       this.btnDelete.enabled = true;
     }
  }


  report(str): void {
    let  selects =[];
    selects.push("Template ID=" + this.lblTemplateId.text);
    this.exportContainer.convertData(this.lastRecievedJSON.assumption.grid.metadata.columns , this.assumptionGrid, null , selects, str, false);
  }

}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: ForecastMonitorAssumptions }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ForecastMonitorAssumptions],
  entryComponents: []
})
export class ForecastMonitorAssumptionsModule {}
