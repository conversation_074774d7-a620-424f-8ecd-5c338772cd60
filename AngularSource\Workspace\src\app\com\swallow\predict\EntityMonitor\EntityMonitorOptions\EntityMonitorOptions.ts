import {Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild} from '@angular/core';
import {
  CommonService, ContextMenuItem, ExternalInterface, HTTPComms, JSONReader, JSONViewer, SwtAlert,
  SwtButton,
  SwtCheckBox,
  SwtComboBox,
  SwtLabel, SwtModule, SwtPopUpManager,
  SwtRadioButtonGroup,
  SwtRadioItem,
  SwtTextInput, SwtToolBoxModule, SwtUtil, StringUtils, Alert, SwtNumericInput
} from "swt-tool-box";
import {RouterModule, Routes} from "@angular/router";
import {EntityMonitor} from "../EntityMonitor";

@Component({
  selector: 'app-entitymonitoroptions',
  templateUrl: './EntityMonitorOptions.html',
  styleUrls: ['./EntityMonitorOptions.css']
})
export class EntityMonitorOptions extends SwtModule implements OnInit {

  @ViewChild('cbUsePeList') cbUsePeList: SwtCheckBox;
  @ViewChild('btnEntity') btnEntity: SwtButton;
  @ViewChild('cbHideWeekends') cbHideWeekends: SwtCheckBox;
  @ViewChild('reportingCcy') reportingCcy: SwtComboBox;
  @ViewChild('lblcurrencyname') lblcurrencyname: SwtLabel;
  @ViewChild('lblccymultiplier') lblccymultiplier: SwtLabel;
  @ViewChild('ccyMultiplier') ccyMultiplier: SwtComboBox;
  @ViewChild('cbPersonalCcyList') cbPersonalCcyList: SwtCheckBox;
  @ViewChild('btnCurrency') btnCurrency: SwtButton;
  @ViewChild('textRefreshRate') textRefreshRate: SwtNumericInput;
  @ViewChild('fontSize') fontSize: SwtRadioButtonGroup;
  @ViewChild('fontNormal') fontNormal: SwtRadioItem;
  @ViewChild('fontSmall') fontSmall: SwtRadioItem;
  @ViewChild('btnSave') btnSave: SwtButton;
  @ViewChild('btnCancel') btnCancel: SwtButton;
  
  private swtAlert: SwtAlert;
  private  baseURL:string= SwtUtil.getBaseURL();
  private  actionPath:string="";
  private  actionMethod:string="";
  private  lastRecievedJSON;
  private  prevRecievedJSON;
  private  inputData:HTTPComms=new HTTPComms(this.commonService);
  private  saveData:HTTPComms=new HTTPComms(this.commonService);
  public   jsonReader: JSONReader = new JSONReader();
  private  comboOpen:boolean=false;
  private  comboChange:boolean=false;
  private  requestParams = [];
  private  menuAccessIdParent:number=0;
  private  ccyList:any;
  private  multiplier:any;
  private  showXML:any;

  // Initializes fontValue
  private  fontValue:string="";
  private  fontLabel:string="";
  private  fontRequest:string="";
  private  selectedFont:number;
  private  tempFontSize:string="";
  private  refreshRate:number=10;
  //private  autoRefresh:Timer;
  private  itemId:string="";
  // Instantiates HTTPComms for Font Setting
  private  updateFontSize:HTTPComms=new HTTPComms(this.commonService);
  public showJSONPopup: any;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.cbUsePeList.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.usePersonalEntityList", null);
    this.btnEntity.label = SwtUtil.getPredictMessage("label.entityMonitorOptions.entity", null);
    this.btnEntity.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.entity", null);
    this.cbHideWeekends.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.hideWeekends", null);
    this.reportingCcy.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.reportingCcy", null);
    this.ccyMultiplier.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.useCurrencyMultiplier", null);
    this.cbPersonalCcyList.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.usePersonalCcyList", null);
    this.textRefreshRate.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.rate", null);
    this.fontNormal.label = SwtUtil.getPredictMessage("label.entityMonitorOptions.fontnormal", null);
    this.fontNormal.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.fontnormal", null);
    this.fontSmall.label = SwtUtil.getPredictMessage("label.entityMonitorOptions.fontsmall", null);
    this.fontSmall.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.fontsmall", null);
    this.btnSave.label = SwtUtil.getPredictMessage("label.entityMonitorOptions.save", null);
    this.btnSave.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.save", null);
    this.btnCancel.label = SwtUtil.getPredictMessage("label.entityMonitorOptions.cancel", null);
    this.btnCancel.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.cancel", null);
    this.btnCurrency.label = SwtUtil.getPredictMessage("label.entityMonitorOptions.currency", null);
    this.btnCurrency.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitorOptions.currency", null);
  }
  onLoad() {
    this.requestParams= [];
    this.menuAccessIdParent=ExternalInterface.call('eval', 'menuAccessIdParent');
   /* if (this.menuAccessIdParent == 1)
    {
      this.btnSave.enabled=false;
    }*/
    //Initialize the context menu
    this.initializeMenus();
    //result event
    this.inputData.cbResult= this.inputDataResult.bind(this);
    //fault event
    this.inputData.cbFault=this.inputDataFault.bind(this);
    // set the encode url
    this.inputData.encodeURL=false;
    //action url
    this.actionPath="entityMonitor.do?";
    //Then declare the action method:
    this.actionMethod="method=displayEntityMonitorOptions";
    this.actionMethod= this.actionMethod + "&selectedEntityId=" + ExternalInterface.call('eval', 'selectedEntityId');
    this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
    this.saveData.cbResult= (event) => {
      this.saveDataResult(event);
    }
    this.saveData.cbFault=this.inputDataFault.bind(this);
    this.saveData.encodeURL=false;
    this.inputData.send(this.requestParams);
  }
  /**
   * This method closes the Entity Monitor Options window upon saving any changes made.<br>
   */
  saveDataResult(event):void {
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    }
    else
    {
      this.lastRecievedJSON=event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus())
      {
        ExternalInterface.call("unloadCloseWindow");
        ExternalInterface.call("closeWindow");
      }
    }
  }
  initializeMenus(): void {
    //this.screenVersion.loadScreenVersion(this, thi, this.versionNumber, this.releaseDate);
    let addMenuItem: ContextMenuItem = new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    /*this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;*/
  }

  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    //this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "400";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.display();
  }

  /**
   * This method is called by the HTTPComms when result event occurs.<br>
   * @param event:ResultEvent
   */
  inputDataResult(event):void
  {
    // checks whether the inputData is busy
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    }
    else
    {
      this.lastRecievedJSON=event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus())
      {
        if ((this.lastRecievedJSON != this.prevRecievedJSON))
        {
          if (this.lastRecievedJSON.monitoroptions.options.usepersonalentitylist == "N")
          {
            this.cbUsePeList.selected=false;
          }
          else
          {
            this.cbUsePeList.selected=true;
          }
          // Hide Weekends checkbox will be Checked/Unchecked based on the condition
          if (this.lastRecievedJSON.monitoroptions.options.hideweekend == "N")
          {
            this.cbHideWeekends.selected=false;
          }
          else
          {
            this.cbHideWeekends.selected=true;
          }
          // Use Personal Currency List checkbox will be Checked/Unchecked based on the condition
          if (this.lastRecievedJSON.monitoroptions.options.usepersonalcurrency == "N")
          {
            this.cbPersonalCcyList.selected=false;
          }
          else
          {
            this.cbPersonalCcyList.selected=true;
          }
          // set the reporting currency data
          this.reportingCcy.setComboData(this.jsonReader.getSelects());
          this.ccyMultiplier.setComboData(this.jsonReader.getSelects());
          // Displaying the Reporting Currency label next to the combo box
          if (StringUtils.trim(this.reportingCcy.text) != "")
          {
            this.lblcurrencyname.text=this.reportingCcy.selectedValue;
          }
          if (StringUtils.trim(this.ccyMultiplier.text) != "")
          {
            this.lblccymultiplier.text=this.ccyMultiplier.selectedValue;
          }

          this.refreshRate=parseInt(this.lastRecievedJSON.monitoroptions.refresh);
          this.textRefreshRate.text=this.refreshRate.toString();
          if (this.jsonReader.getScreenAttributes()["currfontsize"] == "Normal")
          {
            this.selectedFont=0;
            this.fontNormal.selected = true;
          }
          else if (this.jsonReader.getScreenAttributes()["currfontsize"] == "Small")
          {
            this.selectedFont=1;
            this.fontSmall.selected = true;
          }
          //TODO

          /*// getting the reporting currency list from the xml data
          ccyList=(this.lastRecievedJSON["selects"].children().(@id.search("ccylist") != -1)).option;
          // getting the currency multiplier list from the xml data
          multiplier=(this.lastRecievedJSON["selects"].children().(@id.search("ccymultiplier") != -1)).option;*/
        }
      }
    }
  }
  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   * @param event :FaultEvent
   */
  inputDataFault(event):void
  {
    //invalidComms=event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.error('')

  }

  /**
   * Combobox event handling when there is a change in the in one of the combo's
   * @param e :Event
   */
  changeCombo(e):void
  {

    this.lblcurrencyname.text =  (this.reportingCcy.selectedValue) ? this.reportingCcy.selectedValue: "";
    this.lblccymultiplier.text = (this.ccyMultiplier.selectedValue) ? this.ccyMultiplier.selectedValue :"";
    this.comboChange=true;
  }
  /**
   * This method is used to save the date
   */
 updateData():void
  {
    this.btnSave.enabled=false;
    this.requestParams= [];
    this.actionMethod="method=saveEntityMonitorOptions";
    let usePersonalEntityListVal: string="";
    let hideWeekendsVal:string="";
    let usePersonalCurrencyListVal:string="";
    let reportingCcyVal:string="";
    let ccyMultiplierVal:string="";
    if ( this.cbUsePeList.selected) {
      usePersonalEntityListVal="Y"
    }
    else {
      usePersonalEntityListVal="N";
    }
    if ( this.cbHideWeekends.selected) {
      hideWeekendsVal="Y";
    }
    else {
      hideWeekendsVal="N";
    }
    if ( this.cbPersonalCcyList.selected) {
      usePersonalCurrencyListVal="Y";
    }
    else {
      usePersonalCurrencyListVal="N";
    }
    if( this.reportingCcy.selectedValue ) {
      reportingCcyVal= this.reportingCcy.selectedItem.content;
    }
    if( this.ccyMultiplier.selectedValue ) {
      ccyMultiplierVal= this.ccyMultiplier.selectedItem.content;
    }
    // If the refresh rate entered is not a number then show an alert
    if (isNaN(parseInt( this.textRefreshRate.text)))
    {
      this.swtAlert.error(SwtUtil.getPredictMessage("alert.entityMonitorOptions.notANumber", null), 'Error',
        Alert.OK, null,
        (event) => {
          this.textRefreshRate.setFocus();
          this.btnSave.enabled=true;
        });
    }
    else {
      let selectedRateBelowMinimum:Boolean=false;
      // get the refresh rate
      this.refreshRate=parseInt(this.textRefreshRate.text);
      if (this.refreshRate < 5) {
        this.refreshRate=5;
        selectedRateBelowMinimum=true;
      }
      //auto refresh
     /* if (autoRefresh) {
        autoRefresh.delay=(refreshRate * 1000);
      }*/
      this.itemId=ExternalInterface.call('eval', 'itemId');
      this.textRefreshRate.text= this.refreshRate.toString();
      this.fontValue= this.fontSize.selectedValue.toString();
      if (this.fontValue == "N")
      {
        this.selectedFont=0;
        this.fontLabel= this.fontNormal.label; //fontSize.getRadioButtonAt(0).label;
        this.fontRequest=ExternalInterface.call("getUpdateFontSize", this.fontLabel, this.itemId);
      }
      else if (this.fontValue == "S")
      {
        this.selectedFont=1;
        this.fontLabel=this.fontSmall.label; //fontSize.getRadioButtonAt(1).label;
        this.fontRequest=ExternalInterface.call("getUpdateFontSize", this.fontLabel , this.itemId);
      }
      if (this.fontRequest != null && this.fontRequest != "")
      {

          let originalURL ;
          // Sets the original URL
          originalURL=this.updateFontSize.url;
          // Sets the request in the URL
          this.updateFontSize.url = this.baseURL + this.fontRequest;
          // Calls the send function
          this.updateFontSize.send();
          // Sets the URL
          this.updateFontSize.url=originalURL;


      }
      // set all values to request
      this.requestParams["entityMonitorOptions.personalEntityList"]=usePersonalEntityListVal;
      this.requestParams["entityMonitorOptions.hideWeekends"]=hideWeekendsVal;
      this.requestParams["entityMonitorOptions.personalCurrencyList"]=usePersonalCurrencyListVal;
      this.requestParams["entityMonitorOptions.reportCurrency"]=reportingCcyVal;
      this.requestParams["entityMonitorOptions.useCurrencyMultiplier"]=ccyMultiplierVal;
      this.requestParams["entityMonitorOptions.propertyValue"]= this.refreshRate.toString();
      this.saveData.url=this.baseURL + this.actionPath + this.actionMethod;
      // alert will be thrown if selected rate below 5
      if (selectedRateBelowMinimum) {
        //ExternalInterface.call('getBundle', 'text', 'label-ratealert', 'Refresh rate selected was below minimum.\nSet to 5 seconds.') ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning')
        this.swtAlert.warning('Refresh rate selected was below minimum.\nSet to 5 seconds.','Warning' , Alert.OK, null, (event)=> {
          this.saveData.send(this.requestParams);

        } );
      }
      else {
        this.saveData.send(this.requestParams);
      }

    }


  }
  send(data) : void {
    this.saveData.send(this.requestParams);
  }
  closeHandler():void {
    ExternalInterface.call("close");
  }
 openEntity():void {
    ExternalInterface.call("openPersonalEntityWindow");
  }
 openCurrency():void {
    ExternalInterface.call("openPersonalCurrencyWindow");
  }
  doHelp() {
    ExternalInterface.call('help')
  }

}
const routes: Routes = [
  { path: '', component: EntityMonitorOptions }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [EntityMonitorOptions],
  entryComponents: []
})
export class EntityMonitorOptionsModule { }
