<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%" paddingTop="10">
    <VBox paddingLeft="10" width="100%" verticalGap="0" height="20%">
      <HBox >
          <SwtLabel #processingOrderLabel width="180"></SwtLabel>
          <SwtNumericInput id="ordinalNumericInput"
                           #ordinalNumericInput textAlign="right"
                           width="60" (focusOut)="ordinalChangeHandler()" >
          </SwtNumericInput>
        </HBox>
      <HBox >
          <SwtLabel #quickCategoryLabel  width="180">
          </SwtLabel>
          <SwtComboBox id="quickCategoryCombo"
                       #quickCategoryCombo  width="250" dataLabel="categoryList"> </SwtComboBox>
        </HBox>
      <HBox>
          <SwtLabel #defaultCategoryLabel  width="180">
          </SwtLabel>
          <SwtComboBox id="defaultCategoryCombo"
                       #defaultCategoryCombo  width="250" dataLabel="categoryList"> </SwtComboBox>
        </HBox>
      </VBox>
    <HBox width="100%" height="80%" paddingLeft="10">
      <HBox width="45%" height="100%">
      <VBox width="100%" height="100%">
          <HBox width="100%" height="7%">
            <SwtLabel #accountsNotInGroup ></SwtLabel>
            <SwtLabel #countIDNotInGroup></SwtLabel>
          </HBox>
          <HBox  width="100%" height="80%">
            <SwtCanvas id="leftCanvas" #leftCanvas height="100%" width="100%"></SwtCanvas>
          </HBox>
          <HBox width="100%" height="10%" paddingTop="10">
            <SwtLabel #quickSearchLabel width="20%">
            </SwtLabel>
            <SwtTextInput width="80%" #filterTextLeft (keyUp)="filtringLeftGrid(filterTextLeft.text)"></SwtTextInput>
          </HBox>
        </VBox>
      </HBox>
      <HBox width="10%" height="100%" >
      <VBox #buttonsContainer  width="100%" height="100%" horizontalAlign="center" verticalAlign="middle" verticalGap="5">
          <SwtButton id="buttonMoveRight"
                     #buttonMoveRight
                     (click)="moveToRight($event, false)"
                     enabled="false"
                     width="60"
                     label=">" marginBottom="6"></SwtButton>
          <SwtButton id="buttonMoveAllRight"
                     #buttonMoveAllRight
                     (click)="moveToRight($event, true)"
                     enabled="false"
                     width="60"
                     label=">>" marginBottom="6"></SwtButton>
          <SwtButton id="buttonMoveLeft"
                     #buttonMoveLeft
                     (click)="moveToLeft($event, false)"
                     enabled="false"
                     width="60"
                     label="<" marginBottom="6"></SwtButton>

          <SwtButton id="buttonMoveAllLeft"
                     #buttonMoveAllLeft
                     (click)="moveToLeft($event, true)"
                     enabled="false"
                     width="60"
                     label="<<" marginBottom="6"></SwtButton>
        </VBox>
      </HBox>
      <HBox width="45%" height="100%">
      <VBox width="100%" height="100%" >
          <HBox width="100%" height="7%">
            <SwtLabel #accountsInGRoupLabel>
            </SwtLabel>
            <SwtLabel #countIDInGroup ></SwtLabel>
          </HBox>
          <HBox width="100%" height="80%">
            <SwtCanvas id="rightCanvas" #rightCanvas height="100%" width="100%"></SwtCanvas>
          </HBox>
          <HBox width="100%" height="10%" paddingTop="10">
            <SwtLabel #quickSearchLabel2 width="20%">
            </SwtLabel>
            <SwtTextInput width="80%"   #filterTextRight (keyUp)="filtringRightGrid(filterTextRight.text)"></SwtTextInput>
          </HBox>
        </VBox>
      </HBox>
      </HBox>
  </VBox>
</SwtModule>
