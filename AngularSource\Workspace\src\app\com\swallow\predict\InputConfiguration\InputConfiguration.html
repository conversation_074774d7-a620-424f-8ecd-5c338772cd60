<SwtModule #swtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <VBox height="94%" width="100%">
  <VDividedBox  height="100%" width="100%">
    <SwtCanvas class="top" width="100%" height="60%" style="border: 1px solid gray" border="false" #dataGridContainer>
    </SwtCanvas>
    <SwtCanvas class="bottom" id="bottomGridContainer"   #bottomGridContainer style="border: 1px solid gray" border="false" width="100%" height="40%"></SwtCanvas>
  </VDividedBox>
    </VBox>
  <SwtCanvas  id="canvasButtons"  width="100%" marginTop="5" height="5%">
    <HBox width="100%">
      <HBox paddingLeft="5" width="100%">
        <SwtButton #changeButton
                   width="70"
                   label="Change"
                   id="changeButton"
                   (click)="changeHandle()"
                   enabled="false"
                   toolTip="Change Input Configuration"
                   [buttonMode]="true"></SwtButton>
        <SwtButton #saveButton width="70"
                   label="Save"
                   id="saveButton"
                   (click)="confirmSave()"
                   enabled="false"
                   toolTip="Save changes"></SwtButton>
        <SwtButton  #cancelButton
                    id="cancelButton"
                    enabled="false"
                    width="70"
                    label="Cancel"
                    (click)="cancelHandle()"
                    toolTip="Cancel changes"></SwtButton>
        <SwtButton  #closeButton
                    id="closeButton"
                    enabled="true"
                    width="70"
                    label="Close"
                    (click)="closeHandle()"
                    toolTip="Close window"></SwtButton>
      </HBox>
      <HBox paddingTop="8">
        <SwtLabel visible="false" color="red" text="DATA BUILD IN PORGRESS" #dataBuildingText></SwtLabel>
        <SwtLabel visible="false" color="red" text="CONNECTION ERROR" #lostConnectionText></SwtLabel>
      </HBox>
      <HBox horizontalAlign="right" paddingRight="5">

        <SwtButton id="diskSaveImage"  #diskSaveImage
                   visible="false"
                   styleName="fileSaveIcon"></SwtButton>
        <SwtButton id="diskSaveImageError"  #diskSaveImageError
                   visible="false"
                   styleName="fileDeleteIcon"></SwtButton>
        <DataExport  #exportContainer id="exportContainer"></DataExport>
        <SwtHelpButton id="helpIcon"
                       #helpIcon
                       (click)="doHelp()">
        </SwtHelpButton>
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
      </HBox>
    </HBox>
  </SwtCanvas>
  </VBox>
</SwtModule>
