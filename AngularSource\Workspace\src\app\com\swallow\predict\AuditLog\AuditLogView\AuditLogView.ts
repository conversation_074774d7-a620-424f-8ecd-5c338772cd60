import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, SwtAlert, CommonService, ExternalInterface, SwtUtil, SwtCommonGrid, SwtButton, JSONReader, HTTPComms, Logger, SwtCanvas, SwtLoadingImage } from 'swt-tool-box';

@Component({
  selector: 'app-audit-log-view',
  templateUrl: './AuditLogView.html',
  styleUrls: ['./AuditLogView.css']
})
export class AuditLogView extends SwtModule implements OnInit {
    
    /***********SwtButton***********/
    @ViewChild("closeButton") closeButton: SwtButton;
    @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
    @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

    private swtAlert: SwtAlert;
    private actionPath;
    private actionMethod: string = "";
    private requestParams;
    private jsonReader: JSONReader = new JSONReader();
    private lastRecievedJSON;
    private prevRecievedJSON;
    private inputData = new HTTPComms(this.commonService);
    private baseURL: string = SwtUtil.getBaseURL();
    public moduleId = "Predict";
    private logger: Logger = null;
    public errorLocation = 0;
    private _invalidComms: string;
    private viewGrid: SwtCommonGrid;
  
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.viewGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.closeButton.label = SwtUtil.getPredictMessage('sweep.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

  }

  onLoad() {
    this.requestParams = [];
    let selectedReferenceId= ExternalInterface.call('eval', 'selectedReferenceId');
    let selectedUserId= ExternalInterface.call('eval', 'selectedUserId');
    let selectedDate= ExternalInterface.call('eval', 'selectedDate');
    let selectedTime= ExternalInterface.call('eval', 'selectedTime');
    let selectedReference= ExternalInterface.call('eval', 'selectedReference');
    let selectedAction= ExternalInterface.call('eval', 'selectedAction');
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };

    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "auditlog.do?";
    this.actionMethod = 'method=viewDetails';
    this.requestParams['selectedReferenceId'] = selectedReferenceId;
    this.requestParams['selectedUserId'] = selectedUserId;
    this.requestParams['selectedDate'] = selectedDate;
    this.requestParams['selectedTime'] = selectedTime;
    this.requestParams['selectedReference'] = selectedReference;
    this.requestParams['selectedAction'] = selectedAction;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

  }


  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);

        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            this.viewGrid.selectable = false;
            errorLocation = 10;
            if (!this.jsonReader.isDataBuilding()) {
              const obj = { columns: this.lastRecievedJSON.userLogView.userLogViewGrid.metadata.columns };
              //UserLog grid
              this.viewGrid.CustomGrid(obj);
              var gridRows = this.lastRecievedJSON.userLogView.userLogViewGrid.rows;
              if (gridRows.size > 0) {
                this.viewGrid.gridData = gridRows;
                this.viewGrid.setRowSize = this.jsonReader.getSingletons().totalCount;
              }
              else {
                this.viewGrid.gridData = { size: 0, row: [] };
              }
              this.prevRecievedJSON = this.lastRecievedJSON;
            }
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }
        }
      }

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'ErrorLog.ts', "inputDataResult", errorLocation);
    }
  }
  
  closeHandler() {
    ExternalInterface.call("close");
  }

  doHelp(): void {
    ExternalInterface.call("help");
  }

    /**
  * printPage
  *
  * param event
  *
  * Method to get call the action to get reports
  */
 printPage(): void {
  let errorLocation = 0;
  try {
    ExternalInterface.call('printPage');

  } catch (error) {
    // log the error in ERROR LOG
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, "className", "printPage", errorLocation);
  }
}

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: AuditLogView }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AuditLogView],
  entryComponents: []
})
export class AuditLogViewModule { }