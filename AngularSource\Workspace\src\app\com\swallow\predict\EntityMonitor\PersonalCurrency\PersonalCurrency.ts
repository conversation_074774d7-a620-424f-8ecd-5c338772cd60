import {Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild} from '@angular/core';
import {
  CommonService,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCommonGrid,
  SwtLabel,
  SwtLoadingImage,
  SwtModule,
  HTTPComms,
  SwtUtil,
  ExternalInterface,
  ContextMenuItem,
  SwtPopUpManager,
  JSONViewer,
  SwtToolBoxModule,
  SwtGroupedCommonGrid, SwtGroupedTotalCommonGrid, ScreenVersion
} from "swt-tool-box";
import {RouterModule, Routes} from "@angular/router";
import {EntityMonitor} from "../EntityMonitor";

@Component({
  selector: 'app-personal-currency',
  templateUrl: './PersonalCurrency.html',
  styleUrls: ['./PersonalCurrency.css']
})
export class PersonalCurrency extends SwtModule implements OnInit {
  
  private swtAlert: Swt<PERSON>lert;
  private  personalCurrencyGrid:SwtGroupedCommonGrid;
  private  totalsGrid:SwtGroupedTotalCommonGrid;
  private  jsonReader: JSONReader=new JSONReader();
  private  lastRecievedJSON;
  private  prevRecievedJSON;
  private  inputData:HTTPComms=new HTTPComms(this.commonService);
  private  sendData:HTTPComms=new HTTPComms(this.commonService);
  private  baseURL:string= SwtUtil.getBaseURL();
  private  actionMethod:string="";
  private  actionPath:string="";
  private  requestParams = [];
  private  updateRefreshRate:HTTPComms=new HTTPComms(this.commonService);
  private  invalidComms= [];
  private  menuAccessId:number;
  private  showJSONPopup:any;
  // Holds the name of the screen
  //private  screenName:string=ExternalInterface.call('getBundle', 'text', 'label-personalCurrency', 'Personal Currency List Screen');
  private gridUpdates = [];
  private  versionNumber:string="1.1.0003";
  public  dataChanged: any;
  public  update:any;
  public  editdataChanged:any;
  public  editupdate:Object=new Object();
  private  menuAccessIdParent:number=0;
  private  saveFlag:boolean=true;
  private  alertPreventFlag:boolean=false;
  private screenVersion = new ScreenVersion(this.commonService);
  @ViewChild('cvGridContainer') cvGridContainer: SwtCanvas;
  @ViewChild('cvTotalsContainer') cvTotalsContainer: SwtCanvas;
  @ViewChild('btnOk') btnOk: SwtButton;
  @ViewChild('btnCancel') btnCancel: SwtButton;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.personalCurrencyGrid = <SwtGroupedCommonGrid>this.cvGridContainer.addChild(SwtGroupedCommonGrid);
    this.totalsGrid = <SwtGroupedTotalCommonGrid>this.cvTotalsContainer.addChild(SwtGroupedTotalCommonGrid);
    this.totalsGrid.initialColumnsToSkip = 3;
    this.personalCurrencyGrid.lockedColumnCount = 3;
    this.personalCurrencyGrid.uniqueColumn = "ccy";
    this.totalsGrid.lockedColumnCount = 1;
    this.personalCurrencyGrid.hideHorizontalScrollBar = true;
    this.personalCurrencyGrid.listenHorizontalScrollEvent = true;
    this.totalsGrid.fireHorizontalScrollEvent = true;
    this.totalsGrid.enableRowSelection = false;
    this.totalsGrid.editable = true;
    this.personalCurrencyGrid.editable = true;
    this.personalCurrencyGrid.enableRowSelection = false;
  }
  onLoad() {
    this.initializeMenus();
    this.menuAccessIdParent=ExternalInterface.call('eval', 'menuAccessIdParent');
   /* if (this.menuAccessIdParent == 1)
    {
      this.btnOk.enabled=false;
    }*/
    this.btnCancel.enabled=false;
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop=this.endOfComms.bind(this);
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.sendData.cbStart=this.startOfComms.bind(this);
    this.sendData.cbStop=this.endOfComms.bind(this);

    this.sendData.cbResult=(event) => {
      this.sendDataResult(event);
    }
    this.sendData.cbFault=this.inputDataFault.bind(this);
    this.sendData.encodeURL=false;

    this.actionPath="entityMonitor.do?";
    this.actionMethod="method=displayPersonalCurrencyList";
    this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
    this.sendData.url=this.baseURL + this.actionPath + "method=savePersonalCurrencyList";

    this.inputData.send(this.requestParams);
    this.totalsGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.onTotalCheckBoxChecked(selectedRowData);
    });
    this.personalCurrencyGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.onMainGridCheckBoxChecked(selectedRowData);
    });
    this.personalCurrencyGrid.columnWidthChanged.subscribe((event) => {
      this.resizeGrids(event);
    });

  }
  resizeGrids(event) {
    try {
      this.totalsGrid.setRefreshColumnWidths(this.personalCurrencyGrid.gridObj.getColumns());

    } catch(e) {
      console.log("resizeGrids", e)
    }

  }
  initializeMenus(): void {
    /*//this.screenVersion.loadScreenVersion(this, thi, this.versionNumber, this.releaseDate);
    let addMenuItem: ContextMenuItem = new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
*/
  }

  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    //this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "400";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.display();
  }
  inputDataFault(event): void {
    //invalidComms=event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.lostConnectionText.visible = true;
    this.swtAlert.error('Generic exception error');
  }
  sendDataResult(event):void {
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    }
    else {
     
      this.lastRecievedJSON=event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        this.saveFlag=false;
        this.updateData();
      }
    }
  }
  
  updateData():void {
    this.inputData.send(this.requestParams);
  }

   startOfComms():void {
     this.loadingImage.setVisible(true);

  }
  endOfComms():void {
    this.loadingImage.setVisible(false);
  }
  onTotalCheckBoxChecked(selectedRowData) {
    this.btnCancel.enabled=true;
    let field = selectedRowData.target.field;
    if(field != "order") {
      let isChecked: boolean = selectedRowData.target.data[field]
      let entity: boolean = selectedRowData.target.name
      for (let i = 0; i < this.personalCurrencyGrid.dataProvider.length; i++) {
        this.personalCurrencyGrid.dataProvider[i][field] = isChecked
      }
      this.personalCurrencyGrid.refresh();
    }

  }
  


  /**
   * This function is used to handle the checkbox selection in the Main Grid
   *
   * @param event:SwtCheckboxEvent
   */
    onMainGridCheckBoxChecked(selectedRowData):void
  {
    this.btnCancel.enabled=true;
    let field = selectedRowData.target.field
    let isAllChecked: boolean = false;
    if(field != "order") {
      let isChecked: boolean = selectedRowData.target.data[field]
      for (let i = 0; i < this.personalCurrencyGrid.dataProvider.length; i++) {
        if (this.personalCurrencyGrid.dataProvider[i][field].toString() == "true") {
          isAllChecked = true;
        } else {
          isAllChecked = false;
          this.totalsGrid.dataProvider[0][field] = false;
          this.totalsGrid.refresh();
          return;
        }

      }
      if (isAllChecked) {
        this.totalsGrid.dataProvider[0][field] = isChecked
      }
      this.totalsGrid.refresh();
    }
  }

  saveHandle():void {
      this.btnOk.enabled = false;
      let requestParams = [];
    let changes = this.personalCurrencyGrid.changes.getValues();
    let ccy: string;
    let entityName: string;
    let entityId: string = '';
    let isChecked: boolean = false;
    let list = [];
    let listEdit = [];

    for(let i=0; i< changes.length; i++ ) {
      ccy = changes[i].crud_data.ccy;
      let crudOpList = changes[i].crud_operation.split('>');
      for (let k = 0; k < crudOpList.length; k++) {
        if(!crudOpList[k].includes("order")) {
          entityId = crudOpList[k].substring(2, crudOpList[k].length - 1);
          entityName = changes[i].crud_data.slickgrid_rowcontent[entityId].id;
          isChecked = changes[i].crud_data[entityId];
          requestParams[ccy + "_#" + entityName] = isChecked;
          list.push(ccy + "_#" + entityName)

        } else if(crudOpList[k].includes("order")) {
          requestParams[ccy] = changes[i].crud_data.order;
          listEdit.push(ccy)
        }

      }

    }
    requestParams["update"] = list.toString();
    requestParams["editupdate"] = listEdit.toString();
    if(requestParams["update"].length > 0 || requestParams["editupdate"].length > 0)// (this.configGrid.originalDataprovider.length != this.configGrid.dataProvider.length)
    {
      this.sendData.send(requestParams);
    }
    else
    {
      // call the close window method in jsp
      ExternalInterface.call("closeWindow");
    }
    /*this.btnOk.enabled=false;
    this.btnOk.buttonMode=false;
    let updatestring:string="";
    let editstr:string="";
    let requestParams = [];
    let changes = this.personalCurrencyGrid.changes.getValues();
    // collect all checkbox keys
    for (let k = 0; k <changes.length; k++)
    {
      updatestring+=(k + ",");
    }
    // collect all editable keys
    for (let n:Object in editupdate)
    {
      editstr+=(n + ",");
    }
    // get the all string values
    updatestring=updatestring.substr(0, updatestring.length - 1);
    // Set the checkbox values to request
    requestParams["update"]=updatestring;
    // set the checkbox datachanged values
    for (let l:Object in dataChanged)
    {
      requestParams[l]=dataChanged[l];
    }
    // get the all string values
    editstr=editstr.substr(0, editstr.length - 1);
    // Set the editable values to request 
    requestParams["editupdate"]=editstr;
    // set the editable datachanged values
    for (let m:Object in editdataChanged)
    {
      requestParams[m]=editdataChanged[m];
    }

    // Checks if there is anything to be updated, else an alert message will be shown
    if ((requestParams["update"] as string).length > 0 || (requestParams["editupdate"] as string).length > 0)
    {
      sendData.send(requestParams);
    }
    else
    {
      // call the close window method in jsp
      ExternalInterface.call("closeWindow");
    }
    // reinitialize the objects
    update=new Object();
    dataChanged=new Object();
    editdataChanged=new Object();
    editupdate=new Object();*/
  }

  inputDataResult(event):void
  {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    }
    else {
      
      this.lastRecievedJSON=event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus())
      {
        /* Condition to check the last received and previous xml to build grid data.*/
        if ((this.lastRecievedJSON != this.prevRecievedJSON))
        {
          let responseJSON=this.lastRecievedJSON;
          const obj = {columns: this.jsonReader.getColumnData()};
          this.personalCurrencyGrid.CustomGrid(obj);
          /*if (personalCurrencyGrid == null)
          {
            // Creating new instance of PersonalCurrencyGrid with data, base URL and intialColumnsToSkip value
            personalCurrencyGrid=new PersonalCurrencyGrid(this.jsonReader.getColumnData(), baseURL, 3);
            cvGridContainer.addElement(personalCurrencyGrid);
            personalCurrencyGrid.addEventListener(SwtCheckboxEvent.STATUS_CHANGE, onMainGridCheckBoxChecked);
            // calling validate function in Item Edit End
            personalCurrencyGrid.addEventListener(AdvancedDataGridEvent.ITEM_EDIT_END, validate);
            //add the listener for item edit begin
            personalCurrencyGrid.addEventListener(AdvancedDataGridEvent.ITEM_EDIT_BEGIN, itemBegin);
            personalCurrencyGrid.setListeners();
          }*/
          /* Code added by Karthik on 10-Aug-2011 for Mantis 1524:Exclamation mark in flex screens*/
          //set the flag for indicating column change to refresh the column header.
          //personalCurrencyGrid.refreshHeaderFlag = false;
          // set the data provider
          this.personalCurrencyGrid.gridData=this.jsonReader.getGridData();
          // set the row size
          this.personalCurrencyGrid.setRowSize=this.jsonReader.getRowSize();
          let obj2 = JSON.parse(JSON.stringify(obj));
              for (var i = 0; i < obj2.columns.column.length; i++) {

                  if (obj2.columns.column[i].dataelement === 'order')
                      obj2.columns.column[i].editable = "false";
              }
          this.totalsGrid.CustomGrid(obj2);
          this.totalsGrid.gridData= this.jsonReader.getTotalsData();

         /* if (totalsGrid == null)
          {
            // Creating an instance of SwtAdvTotalsDataGrid with : data and intialColumnsToSkip value
            totalsGrid=new SwtAdvTotalsDataGrid(this.jsonReader.getColumnData(), 3);

            cvTotalsContainer.addElement(totalsGrid);
            // calling onTotalCheckBoxChecked while status changes
            totalsGrid.addEventListener(SwtCheckboxEvent.STATUS_CHANGE, onTotalCheckBoxChecked);
            totalsGrid.setListeners();
          }
          // set the horizontal Scroll Position to totalsGridHBar
          let totalsGridHBar:int=totalsGrid.horizontalScrollPosition;
          // update totals grid

          totalsGrid.updateGrid(responseXML.grid.totals.total, responseXML.grid.metadata.columns);
          appContainer.setStyle("horizontalScrollPolicy",ScrollPolicy.OFF);
          appContainer.setStyle("verticalScrollPolicy",ScrollPolicy.OFF);
          /!* cvTotalsContainer.setStyle("horizontalScrollPolicy",ScrollPolicy.OFF);
                        cvTotalsContainer.setStyle("verticalScrollPolicy",ScrollPolicy.OFF); *!/
          vbGridContainer.setStyle("horizontalScrollPolicy",ScrollPolicy.OFF);
          vbGridContainer.setStyle("verticalScrollPolicy",ScrollPolicy.OFF);
          // set the horizontalScrollPosition
          totalsGrid.horizontalScrollPosition=totalsGridHBar;*/


        }
        this.prevRecievedJSON = this.lastRecievedJSON;
        if (!this.saveFlag)
        {
          // call the close window method in jsp
          ExternalInterface.call("closeWindow");
        }
      }
    }
  }

  /**
   * This is used to validate the editable cell value
   *
   * @param event:AdvancedDataGridEvent
   */
  /*private function validate(event:AdvancedDataGridEvent):void
  {
    // enable the cancel button
    btnCancel.enabled=true;
    // enable the cancel button mode
    btnCancel.buttonMode=true;
    // variable used to hold the successful keys
    let successful:string=null;
    if (event.reason == AdvancedDataGridEventReason.CANCELLED)
    {
      return;
    }
    if (alertPreventFlag)
    {
      if (event.reason == AdvancedDataGridEventReason.OTHER)
      {
        event.preventDefault();
      }
    }
    // Holds the last received xml
    let responseXML:XML=this.lastRecievedJSON;
    let gridList:XMLList=responseXML.grid.rows.row;
    let isAllSelected:Boolean=true;
    // Get the cell editor and cast it to TextInput.
    let myEditor:TextInput=TextInput(event.currentTarget.itemEditorInstance);
    // Get the new value from the editor.
    let newVal:string=myEditor.text;
    // Get the old value.
    let oldVal:string=event.currentTarget.editedItemRenderer.data[event.dataField];
    // Initialize the AdvancedDataGrid 
    let datagrid:AdvancedDataGrid=event.target as AdvancedDataGrid;
    if (oldVal != newVal)
    {
      if (!alertPreventFlag)
      {
        alertPreventFlag=true;
        if ((event.reason == AdvancedDataGridEventReason.NEW_ROW) || (event.reason == AdvancedDataGridEventReason.NEW_COLUMN) || (event.reason == AdvancedDataGridEventReason.OTHER))
        {
          // Checks whether the newVal is of numeric value or not 
          if (isNaN(parseInt(newVal)))
          {
            event.preventDefault();
            SwtAlert.getInstance().show(ExternalInterface.call('getBundle', 'text', 'label-number', 'Number'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
          }
          else
          {
            // Checks whether newVal is a positive number
            if (parseInt(newVal) < 0)
            {
              event.preventDefault();
              SwtAlert.getInstance().show(ExternalInterface.call('getBundle', 'text', 'label-positiveNumber', 'Please enter a positive number'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
            }
            // checks whether newVal is between 1 and 99
            else if (newVal.length > 2 || parseInt(newVal) < 1)
            {
              event.preventDefault();
              SwtAlert.getInstance().show(ExternalInterface.call('getBundle', 'text', 'label-NumberBetween', 'Please enter a number between 1 - 999'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
            }
            else
            {
              // set the editable textbox value
              TextInput(event.currentTarget.itemEditorInstance).text=parseInt(newVal).tostring();
              // add the value to edit data Changed object
              editdataChanged[event.currentTarget.editedItemRenderer.data[datagrid.columns[0].dataField]]=newVal;
              // add the value to edit update object
              if (!editupdate.hasOwnProperty(event.currentTarget.editedItemRenderer.data[datagrid.columns[0].dataField]))
              {
                editupdate[event.currentTarget.editedItemRenderer.data[datagrid.columns[0].dataField]]=true;
              }
            }
          }
        }
      }
    }
  }

  /**
   * This function shows an alert if any failure occurs.
   * @param event:FaultEvent
   *
   */
  /*private function onFailure(event:FaultEvent):void
  {
    SwtAlert.getInstance().show(event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail);
  }*/

  /**
   * close the window when click the close button
   **/
 cancelHandle():void
  {
    ExternalInterface.call("close");
  }

  doHelp() {
   ExternalInterface.call('help')
  }

}
const routes: Routes = [
  { path: '', component: PersonalCurrency }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PersonalCurrency],
  entryComponents: []
})
export class PersonalCurrencyModule { }
