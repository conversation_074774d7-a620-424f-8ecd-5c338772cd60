/*
 * @(#)SweepCancelQueueAction.java 1.0 11-06-2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.control.model.AccountAccess;
import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.control.model.RoleTO;
import org.swallow.control.service.AccountAccessManager;
import org.swallow.control.web.AccountAccessAction;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.model.User;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.PageInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PageDetails;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepQueueDetailVO;
import org.swallow.work.service.SweepQueueManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.util.struts.ActionMessages;

import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;

/**
 * <AUTHOR> Balaji .A
 *
 * Class to handle the sweep cancel queue related operations
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/sweepcancelqueue", "/sweepcancelqueue.do"})
public class SweepCancelQueueAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("cancel", "jsp/work/sweepqueuecancel");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "displayList":
				return displayList();
			case "displayAngular":
				return displayAngular();
			case "submit":
				return submit();
			case "next":
				return next();
			case "nextAngular":
				return nextAngular();
			case "acctAccessConfirm":
				return acctAccessConfirm();
		}


		return unspecified();
	}


	private Sweep sweepQueue;
	public Sweep getSweepQueue() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		sweepQueue = RequestObjectMapper.getObjectFromRequest(Sweep.class, request);
		return sweepQueue;
	}

	public void setSweepQueue(Sweep sweepQueue) {
		this.sweepQueue = sweepQueue;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("sweepQueue", sweepQueue);
	}


	// Misc Param to added the entityId field in table and check the entityId
	@Autowired
	private SweepQueueManager sweepQueueManager = null;

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SweepCancelQueueAction.class);

	/**
	 * Method invoked when no method name has been specified for invoking a
	 * request
	 *
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws SwtException {

		return display();
	}

	/**
	 * display method is used to display the sweeps that are ready for
	 * cancelling. This method fetches sweeps for the default currency group of
	 * the logged in user.
	 *
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Variable Declaration for forward */
		String forward = "cancel";
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		/* Variable Declaration for maxPage */
		int maxPage = 0;
		/* Variable Declaration for pSummary */
		PageDetails pSummary = null;
		ArrayList<PageDetails> pageSummaryList = null;
		/* Variable Declaration for initialCurrencyCode */
		String initialCurrencyCode = null;
		/* Variable Declaration for totalCount */
		int totalCount = 0;
		/* Variable Declaration for sweepDetailListAll */
		ArrayList sweepDetailListAll = null;
		/* Variable Declaration for otherDetailListAll */
		ArrayList otherDetailListAll = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for session */
		HttpSession session = null;
		/* Variable Declaration for queueName */
		String queueName = null;
		/* Variable Declaration for currGrpId */
		String currGrpId = null;
		/* Variable Declaration for roleId */
		String roleId = null;
		/* Variable Declaration for entityId */
		String entityId = null;
		// Currency Group set to 0 by default
		// Variable Declaration for currencyGrpAccess
		int currencyGrpAccess = 0;
		// Variable Declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable Declaration for currGrpAccess
		int currGrpAccess = 0;
		// Variable Declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable Declaration for systemFormats
		SystemFormats systemFormats = null;
		// Variable Declaration for sweepForm
		Sweep sweepForm = null;
		// Variable Declaration for sweep
		Sweep sweep = null;
		// Variable Declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable Declaration for groupList
		Collection groupList = null;
		// Variable Declaration for itGroupList
		Iterator itGroupList = null;
		// Variable Declaration for temp
		ArrayList temp = null;
		// Variable Declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		// Variable Declaration for tempItr
		Iterator tempItr = null;
		try {
			log
					.debug(this.getClass().getName() + " - [display()] - "
							+ "Entry");
			// ArrayLists added for Currency Group 'All' case
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();
			session = request.getSession();
			queueName = (String) request.getParameter("queueName");
			// This condition is cheks thant the opened Screen either Cancel
			// queue or other Screen
			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(queueName)) {

				forward = "cancel";
				// Read the selected filter from the request and store in String
				// variable
				currentFilter = request.getParameter("selectedFilter");
				// Read the selectedSort from the request and store in String
				// variable
				currentSort = request.getParameter("selectedSort");
				/* Condition to check currentFilter is null */
				if (currentFilter == null
						|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
					// If current filter is null the default value 'All' is set
					// for currentFilter
					currentFilter = "all";
				}

				/* Condition to check currentSort is null */
				if (currentSort == null
						|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
					/* If currentSort is null then set current sort as none. */
					currentSort = "none";
				}

				// Use # as separator for the combined current filter and
				// current sort
				filterSortStatus = currentFilter + "#" + currentSort;
				currentPage = 1;
				initialPageCount = 0;
				isNext = false;
			}
			systemFormats = new SystemFormats();
			// Setting Currency Formats
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			// Setiing Date Formats
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			sweepForm = (Sweep) getSweepQueue();
			sweepForm.setEntityId(
					SwtUtil.getUserCurrentEntity(request.getSession()));

			// Get the user's default currency group
			// Value set to default Currency Grp

			currGrpId = ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentCcyGrpId();
			roleId = getRoleId(request);
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			if (request.getParameter("entityId") != null) {
				entityId = request.getParameter("entityId");
				currGrpId = request.getParameter("currCode");
				sweepForm.setEntityId(entityId);
			}
			// Getting Currency Group Access
			currGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);

			if (currGrpAccess == SwtConstants.CURRENCYGRP_NO_ACCESS) {
				currGrpId = "All";
			}

			// Identifies the screen displayed - Submit or Authorise.
			sweepForm.setQueueName(queueName);
			sweepForm.setCurrencyCode(currGrpId);
			setSweepQueue(sweepForm);

			sweep = new Sweep();
			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));
			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());
			sweep.setEntityId(entityId);
			sweep.setCurrencyCode(currGrpId);
			sweep.setAccountType("All");
			sweep.setQueueName(queueName);
			initialCurrencyCode = currGrpId;
			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			// Code added for Currency Group 'All' case
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				groupList = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				itGroupList = groupList.iterator();
				temp = new ArrayList();

				currGrpId = "";
				/*
				 * Start:Code Modified by Balaji for Mantis 1483 on
				 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error when
				 * the last currency group in Currency group Access screen is
				 * set to View Access
				 */
				// Appending commas on currency group Id's
				while (itGroupList.hasNext()) {
					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					if (entityCurrencyGroupAccess.getAccess() == 0) {
						if (currGrpCounter > 0) {
							currGrpId += ",'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
						} else {
							currGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
						}
						currGrpCounter++;
					}
				}

				/*
				 * End:Code Modified by Balaji on 8-JUN-2011 for Mantis 1483:For
				 * Sweep Cancel Queue Screen displays error when the last
				 * currency group in Currency group Access screen is set to View
				 * Access
				 */
				if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
					currGrpId = "''";
				}
				sweep.setCurrencyCode(currGrpId);

				itGroupList = groupList.iterator();
				// Intializing cancelViewCurrGrpId as empty
				cancelViewCurrGrpId = "";
				// This following condition ids used to get currency group which
				// are having access.
				while (itGroupList.hasNext()) {

					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					if (itGroupList.hasNext())
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
								.getCurrencyGroupId() + "',";
					else
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
								.getCurrencyGroupId() + "'";
				}
				if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
					cancelViewCurrGrpId = "''";
				}

				sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);

				sweepQueueDetailVO.setEntityAccessList(SwtUtil
						.getUserEntityAccessList(request.getSession()));
				sweepQueueDetailVO.setSweepDetailList(new ArrayList());
				sweepQueueDetailVO.setOtherDetailList(new ArrayList());

				currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				// getiing no of counts of records having and setting it to
				// total count variable
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setCurrencyCode(initialCurrencyCode);
				temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

				tempItr = temp.iterator();

				while (tempItr.hasNext())
					sweepDetailListAll.add(tempItr.next());

				temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
				tempItr = temp.iterator();

				while (tempItr.hasNext())
					otherDetailListAll.add(tempItr.next());

				sweep.setCurrencyCode("All");
			}
			// This else condition is for Submit queue and authhorized queue
			else {
				sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
				sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode + "'");
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweepDetailListAll = (ArrayList) sweepQueueDetailVO
						.getSweepDetailList();

				otherDetailListAll = (ArrayList) sweepQueueDetailVO
						.getOtherDetailList();
			}

			sweep.setCurrencyCode(initialCurrencyCode);
			putEntityListInReq(request);
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());
			putAccountListInReq(request, entityId);
			request.setAttribute("AccountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);

			pageSummaryList = new ArrayList<PageDetails>();
			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked to enable or disable the next link */
			if (maxPage > 1) {
				// If true the isNext flag is set to true which is set to
				// nextEnabled request attribute which is used in UI to
				// enable the next link.
				isNext = true;
			}

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			// Code added for Currency Group 'All' case - attributes set in
			// request
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);
			log.debug(this.getClass().getName() + " - [display()] - " + "Exit");
			return getView(forward);
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepCancelQueueAction.'display' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepCancelQueueAction.'display' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", SweepCancelQueueAction.class), request, "");

			return getView("fail");
		} finally {
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
		}
	}


	@SuppressWarnings("unchecked")
	public String displayAngular()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Variable Declaration for forward */
		String forward = "cancel";
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		/* Variable Declaration for maxPage */
		int maxPage = 0;
		/* Variable Declaration for pSummary */
		PageDetails pSummary = null;
		ArrayList<PageDetails> pageSummaryList = null;
		/* Variable Declaration for initialCurrencyCode */
		String initialCurrencyCode = null;
		/* Variable Declaration for totalCount */
		int totalCount = 0;
		/* Variable Declaration for sweepDetailListAll */
		ArrayList sweepDetailListAll = null;
		/* Variable Declaration for otherDetailListAll */
		ArrayList otherDetailListAll = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for session */
		HttpSession session = null;
		/* Variable Declaration for queueName */
		String queueName = null;
		/* Variable Declaration for currGrpId */
		String currGrpId = null;
		/* Variable Declaration for roleId */
		String roleId = null;
		/* Variable Declaration for entityId */
		String entityId = null;
		// Currency Group set to 0 by default
		// Variable Declaration for currencyGrpAccess
		int currencyGrpAccess = 0;
		// Variable Declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable Declaration for currGrpAccess
		int currGrpAccess = 0;
		// Variable Declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable Declaration for systemFormats
		SystemFormats systemFormats = null;
		// Variable Declaration for sweepForm
		Sweep sweepForm = null;
		// Variable Declaration for sweep
		Sweep sweep = null;
		// Variable Declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable Declaration for groupList
		Collection groupList = null;
		// Variable Declaration for itGroupList
		Iterator itGroupList = null;
		// Variable Declaration for temp
		ArrayList temp = null;
		// Variable Declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		// Variable Declaration for tempItr
		Iterator tempItr = null;
		String calledFrom = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		// To hold date format
		String dateFormat = null;
		// To hold amount format
		String amountFormat = null;
		// To hold system format
		SystemFormats sysFormat = null;
		try {
			log
					.debug(this.getClass().getName() + " - [displayAngular()] - "
							+ "Entry");
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			// ArrayLists added for Currency Group 'All' case
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();
			session = request.getSession();
			queueName = (String) request.getParameter("queueName");

			// This condition is cheks thant the opened Screen either Cancel
			// queue or other Screen
			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(queueName)) {

				forward = "cancel";
				// Read the selected filter from the request and store in String
				// variable
				currentFilter = request.getParameter("selectedFilter");
				// Read the selectedSort from the request and store in String
				// variable
				currentSort = request.getParameter("selectedSort");
				/* Condition to check currentFilter is null */
				if (currentFilter == null
						|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
					// If current filter is null the default value 'All' is set
					// for currentFilter
					currentFilter = "all";
				}

				/* Condition to check currentSort is null */
				if (currentSort == null
						|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
					/* If currentSort is null then set current sort as none. */
					currentSort = "none";
				}

				// Use # as separator for the combined current filter and
				// current sort
				filterSortStatus = currentFilter + "#" + currentSort;
				if(!SwtUtil.isEmptyOrNull(request.getParameter("currentPage"))) {
					currentPage = Integer.parseInt(request.getParameter("currentPage"));
				}
				initialPageCount = 0;
				isNext = false;
			}
			systemFormats = new SystemFormats();
			// Setting Currency Formats
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			// Setiing Date Formats
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			sweepForm = (Sweep) getSweepQueue();
			sweepForm.setEntityId(
					SwtUtil.getUserCurrentEntity(request.getSession()));
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE);

			// Get the user's default currency group
			// Value set to default Currency Grp

			currGrpId = ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentCcyGrpId();
			roleId = getRoleId(request);
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			if (request.getParameter("entityId") != null) {
				entityId = request.getParameter("entityId");
				currGrpId = request.getParameter("currCode");
				sweepForm.setEntityId(entityId);
			}
			// Getting Currency Group Access
			currGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);

			if (currGrpAccess == SwtConstants.CURRENCYGRP_NO_ACCESS) {
				currGrpId = "All";
			}

			// Identifies the screen displayed - Submit or Authorise.
			sweepForm.setQueueName(queueName);
			sweepForm.setCurrencyCode(currGrpId);
			setSweepQueue(sweepForm);

			sweep = new Sweep();
			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));
			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());
			sweep.setEntityId(entityId);
			sweep.setCurrencyCode(currGrpId);
			sweep.setAccountType("All");
			sweep.setQueueName(queueName);
			initialCurrencyCode = currGrpId;
			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			// Code added for Currency Group 'All' case
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				groupList = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				itGroupList = groupList.iterator();
				temp = new ArrayList();

				currGrpId = "";
				/*
				 * Start:Code Modified by Balaji for Mantis 1483 on
				 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error when
				 * the last currency group in Currency group Access screen is
				 * set to View Access
				 */
				// Appending commas on currency group Id's
				while (itGroupList.hasNext()) {
					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					if (entityCurrencyGroupAccess.getAccess() == 0) {
						if (currGrpCounter > 0) {
							currGrpId += ",'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
						} else {
							currGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
						}
						currGrpCounter++;
					}
				}

				/*
				 * End:Code Modified by Balaji on 8-JUN-2011 for Mantis 1483:For
				 * Sweep Cancel Queue Screen displays error when the last
				 * currency group in Currency group Access screen is set to View
				 * Access
				 */
				if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
					currGrpId = "''";
				}
				sweep.setCurrencyCode(currGrpId);

				itGroupList = groupList.iterator();
				// Intializing cancelViewCurrGrpId as empty
				cancelViewCurrGrpId = "";
				// This following condition ids used to get currency group which
				// are having access.
				while (itGroupList.hasNext()) {

					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					if (itGroupList.hasNext())
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
								.getCurrencyGroupId() + "',";
					else
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
								.getCurrencyGroupId() + "'";
				}
				if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
					cancelViewCurrGrpId = "''";
				}

				sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);

				sweepQueueDetailVO.setEntityAccessList(SwtUtil
						.getUserEntityAccessList(request.getSession()));
				sweepQueueDetailVO.setSweepDetailList(new ArrayList());
				sweepQueueDetailVO.setOtherDetailList(new ArrayList());

				currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				// getiing no of counts of records having and setting it to
				// total count variable
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setCurrencyCode(initialCurrencyCode);
				temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

				tempItr = temp.iterator();

				while (tempItr.hasNext())
					sweepDetailListAll.add(tempItr.next());

				temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
				tempItr = temp.iterator();

				while (tempItr.hasNext())
					otherDetailListAll.add(tempItr.next());

				sweep.setCurrencyCode("All");
			}
			// This else condition is for Submit queue and authhorized queue
			else {
				sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
				sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode + "'");
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweepDetailListAll = (ArrayList) sweepQueueDetailVO
						.getSweepDetailList();

				otherDetailListAll = (ArrayList) sweepQueueDetailVO
						.getOtherDetailList();
			}

			sweep.setCurrencyCode(initialCurrencyCode);
			putEntityListInReq(request);
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());
			putAccountListInReq(request, entityId);
			request.setAttribute("AccountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);

			pageSummaryList = new ArrayList<PageDetails>();
			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked to enable or disable the next link */
			if (maxPage > 1) {
				// If true the isNext flag is set to true which is set to
				// nextEnabled request attribute which is used in UI to
				// enable the next link.
				isNext = true;
			}

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			// Code added for Currency Group 'All' case - attributes set in
			// request
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);
			//Angular part
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SWEEP_QUEUE);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntity", entityId);
			responseConstructor.createElement("defaultCcyGrp", currGrpId);
			responseConstructor.createElement("defaultAcctType", "All");
			responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			responseConstructor.createElement("accountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);
			responseConstructor.createElement("currentPage", Integer.toString(currentPage));
			responseConstructor.createElement("prevEnabled", "false");
			responseConstructor.createElement("nextEnabled", "" + isNext);
			responseConstructor.createElement("maxPage", Integer.toString(maxPage));
			responseConstructor.createElement("selectedFilter", currentFilter);
			responseConstructor.createElement("selectedSort", currentSort);
			responseConstructor.createElement("totalCount", totalCount);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entityList = putEntityListInReq(request);
			Iterator j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));
			/***** Entity Combo End ***********/

			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection currencyGrpList = putCurrencyGroupListInReqNew(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			j = currencyGrpList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyGrpList", lstOptions));
			/***** Currency Combo End ***********/


			/***** Account Type list Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection acctTypeList = putAccountListInReq(request, sweep.getEntityId());

			j = acctTypeList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getLabel(), row.getValue(), false));
			}
			lstSelect.add(new SelectInfo("acctTypeList", lstOptions));
			/***** Account list Combo End ***********/

			responseConstructor.formSelect(lstSelect);

			/******* AuthoriseQueueGrid ******/
			responseConstructor.formGridStart("authoriseQueueGrid");
			responseConstructor.formColumn(getGridColumns("auth", width, columnOrder, hiddenColumns));
			/*****  Form Paging Start ***********/
			if(currentPage > maxPage) {
				currentPage = maxPage;
			}
			responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalCount, pageSize),currentPage));
			/*****  Form Paging End ***********/
			// form rows (records)
			responseConstructor.formRowsStart(totalCount);
			for (Iterator<Sweep> it = sweepDetailListAll.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser());
				}
				responseConstructor.formRowEnd();
			};

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* ViewQueueGrid ******/
			responseConstructor.formGridStart("viewQueueGrid");
			responseConstructor.formColumn(getGridColumns("view", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(otherDetailListAll.size());
			for (Iterator<Sweep> it = otherDetailListAll.iterator(); it.hasNext();) {
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser());
				}
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.SWEEP_QUEUE);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [displayAngular()] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepCancelQueueAction.'displayAngular' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepCancelQueueAction.'displayAngular' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", SweepCancelQueueAction.class), request, "");

			return getView("fail");
		} finally {
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
		}
	}
	/**
	 * displayList method fetches the list of sweeps based on the user chosen
	 * criteria.
	 *
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String displayList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Variable Declaration for sweep */
		Sweep sweep = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for forward */
		String forward = "cancel";
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		/* Variable Declaration for maxPage */
		int maxPage = 0;
		/* Variable Declaration for pSummary */

		ArrayList<PageDetails> pageSummaryList = null;
		/* Variable Declaration for initialCurrencyCode */
		String initialCurrencyCode = null;
		// Variable Declaration for sweepDetailListAll
		ArrayList sweepDetailListAll = null;
		// Variable Declaration for otherDetailListAll
		ArrayList otherDetailListAll = null;

		// Variable Declaration for session
		HttpSession session = null;
		// Variable Declaration for currencyGrpAccess
		int currencyGrpAccess = 0;
		// Variable Declaration for systemFormats
		SystemFormats systemFormats = new SystemFormats();
		// Variable Declaration for totalCount
		int totalCount = 0;
		// Variable Declaration for currGrpId
		String currGrpId = null;
		// Variable Declaration for roleId
		String roleId = null;
		// Variable Declaration for entityId
		String entityId = null;
		// Variable Declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable Declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable Declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable Declaration for roleObject
		RoleTO roleObject = null;
		// Variable Declaration for entityList
		Collection entityList = null;
		// Variable Declaration for itr
		Iterator itr = null;
		// Variable Declaration for itGroupList
		Iterator itGroupList = null;
		// Variable Declaration for temp
		ArrayList temp = null;
		// Variable Declaration for tempItr
		Iterator tempItr = null;
		// Variable Declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		// DynaValidatorForm dyForm = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		// To hold date format
		String dateFormat = null;
		// To hold amount format
		String amountFormat = null;
		// To hold system format
		SystemFormats sysFormat = null;
		String queueName= null;
		String defaultCcyGrp = null;
		String defaultAcctType = null;
		try {
			log.debug(this.getClass().getName() + " - [displayList()] - "
					+ "Entry");
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			queueName = (String) request.getParameter("queueName");
			defaultCcyGrp= (String) request.getParameter("currencyCode");
			defaultAcctType= (String) request.getParameter("accountType");
			// Read the selected filter from the request and store in String
			// variable
			currentFilter = request.getParameter("selectedFilter");
			// Read the selectedSort from the request and store in String
			// variable
			currentSort = request.getParameter("selectedSort");
			/* Condition to check currentFilter is null */
			if (currentFilter == null
					|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
				// If current filter is null the default value 'All' is set
				// for currentFilter
				currentFilter = "all";
			}

			/* Condition to check currentSort is null */
			if (currentSort == null
					|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
				/* If currentSort is null then set current sort as none. */
				currentSort = "none";
			}

			// Use # as separator for the combined current filter and
			// current sort
			filterSortStatus = currentFilter + "#" + currentSort;
			currentPage = 1;
			initialPageCount = 0;
			isNext = false;

			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));
			sweep = (Sweep) getSweepQueue();

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));

			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());


			//added by nadia
			sweep.setEntityId(request.getParameter("entityId"));

			sweep.setAccountType(request.getParameter("accountType"));

			sweep.setCurrencyCode(request.getParameter("currencyCode"));

			sweep.setQueueName(request.getParameter("queueName"));

			if (sweep.getAccountType().equalsIgnoreCase("C")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CASH_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("U")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CUSTODIAN_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("All")) {
				request.setAttribute("AccountDesp",
						SwtConstants.ALL_ACCOUNTS_TYPE);
			}

			putEntityListInReq(request);

			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());
			putAccountListInReq(request, sweep.getEntityId());

			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();

			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			roleId = getRoleId(request);
			entityId = sweep.getEntityId();

			session = request.getSession();
			if ((!entityId.equalsIgnoreCase(SwtUtil
					.getUserCurrentEntity(request.getSession())))
					&& sweep.getCurrencyCode().equalsIgnoreCase(
					((CommonDataManager) session
							.getAttribute(SwtConstants.CDM_BEAN))
							.getUser().getCurrentCcyGrpId())) {
				currGrpId = "All";

			} else {
				currGrpId = sweep.getCurrencyCode();
			}
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			initialCurrencyCode = currGrpId;
			// code added to handle Entity as All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				maxPage = 0;
				roleObject = new RoleTO(roleId);
				entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				itr = entityList.iterator();
				// Getting all the details of the entity
				while (itr.hasNext()) {

					currGrpId = sweep.getCurrencyCode();

					EntityUserAccess entityUserAccess = (EntityUserAccess) itr
							.next();
					entityId = entityUserAccess.getEntityId();

					sweep.setEntityId(entityId);

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						Collection groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);
						itGroupList = groupList.iterator();
						temp = new ArrayList();
						currGrpId = "";
						/*
						 * Start:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */
						// This while loop is for gennerating currecy group
						// access for cancel panel
						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access
							if (entityCurrencyGroupAccess.getAccess() == 0) {
								if (currGrpCounter > 0) {
									currGrpId += ",'"
											+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
								} else {
									currGrpId += "'"
											+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
								}
							}
							currGrpCounter++;
						}
						/*
						 * End:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */
						if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
							currGrpId = "''";
						}
						sweep.setCurrencyCode(currGrpId);
						// Intialzing cancelViewCurrGrpId as empty
						cancelViewCurrGrpId = "";
						itGroupList = groupList.iterator();
						// This while loop is for gennerating currecy group
						// access for view panel
						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access
							if (itGroupList.hasNext())
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "',";
							else
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
						}
						if (cancelViewCurrGrpId
								.equals(SwtConstants.EMPTY_STRING)) {
							cancelViewCurrGrpId = "''";
						}
						// Setting value in bean
						sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());
						sweepQueueDetailVO.setOtherDetailList(new ArrayList());

						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
						// Setting count records will available in the grid
						maxPage += sweepQueueManager.getSweepQueueDetail(sweep,
								roleId, currencyGrpAccess, sweepQueueDetailVO,
								currentPage, initialPageCount,
								filterSortStatus, systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
						sweep.setCurrencyCode("All");
					} else {
						temp = new ArrayList();
						sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
						sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
								+ "'");
						maxPage += sweepQueueManager.getSweepQueueDetail(sweep,
								roleId, currencyGrpAccess, sweepQueueDetailVO,
								currentPage, initialPageCount,
								filterSortStatus, systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}
				request.setAttribute("totalCount", maxPage);
				maxPage = setMaxPageAttribute(
						maxPage,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setEntityId("All");
			} else {
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					currencyGrpAccess = 0;
				} else {
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				}
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					Collection groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					itGroupList = groupList.iterator();
					temp = new ArrayList();

					currGrpId = "";
					/*
					 * Start:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					currGrpCounter = 0;
					// This while loop is for gennerating currecy group
					// access for cancel panel

					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access
						if (entityCurrencyGroupAccess.getAccess() == 0) {
							if (currGrpCounter > 0) {
								currGrpId += ",'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
							} else {
								currGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
							}
						}
						currGrpCounter++;
					}
					/*
					 * End:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
						currGrpId = "''";
					}
					sweep.setCurrencyCode(currGrpId);
					// Intialzing cancelViewCurrGrpId as empty
					cancelViewCurrGrpId = "";
					itGroupList = groupList.iterator();
					// This while loop is for gennerating currecy group
					// access for view panel

					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access
						if (itGroupList.hasNext())
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "',";
						else
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
					}
					if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
						cancelViewCurrGrpId = "''";
					}
					sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());

					sweepQueueDetailVO.setOtherDetailList(new ArrayList());
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
					// getting count of records from manager class
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();
					tempItr = temp.iterator();
					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());
					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();
					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());
					sweep.setCurrencyCode("All");
				} else {
					sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
					sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
							+ "'");
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}
			sweep.setCurrencyCode(initialCurrencyCode);
			setSweepQueue(sweep);
			pageSummaryList = new ArrayList<PageDetails>();
			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked to enable or disable the next link */
			if (maxPage > 1) {
				// If true the isNext flag is set to true which is set to
				// nextEnabled request attribute which is used in UI to
				// enable the next link.
				isNext = true;
			}
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE);

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}
			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				forward = "cancel";
			}

			//Angular part
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SWEEP_QUEUE);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntity",
					entityId);
			responseConstructor.createElement("defaultCcyGrp",
					defaultCcyGrp);
			responseConstructor.createElement("defaultAcctType",
					defaultAcctType);
			responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			responseConstructor.createElement("accountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);
			responseConstructor.createElement("currentPage", Integer.toString(currentPage));
			responseConstructor.createElement("prevEnabled", "false");
			responseConstructor.createElement("nextEnabled", "" + isNext);
			responseConstructor.createElement("maxPage", Integer.toString(maxPage));
			responseConstructor.createElement("selectedFilter", currentFilter);
			responseConstructor.createElement("selectedSort", currentSort);
			responseConstructor.createElement("totalCount", totalCount);

			xmlWriter.endElement(SwtConstants.SINGLETONS);
			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entities = putEntityListInReq(request);
			Iterator j = entities.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));
			/***** Entity Combo End ***********/

			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection currencyGrpList = putCurrencyGroupListInReqNew(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			j = currencyGrpList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyGrpList", lstOptions));
			/***** Currency Combo End ***********/


			/***** Account Type list Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection acctTypeList = putAccountListInReq(request, sweep.getEntityId());

			j = acctTypeList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getLabel(), row.getValue(), false));
			}
			lstSelect.add(new SelectInfo("acctTypeList", lstOptions));
			/***** Account list Combo End ***********/

			responseConstructor.formSelect(lstSelect);

			/******* AuthoriseQueueGrid ******/
			responseConstructor.formGridStart("authoriseQueueGrid");
			responseConstructor.formColumn(getGridColumns("auth", width, columnOrder, hiddenColumns));
			responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalCount, pageSize),currentPage));
			// form rows (records)
			responseConstructor.formRowsStart(sweepDetailListAll.size());
			for (Iterator<Sweep> it = sweepDetailListAll.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser());
				}
				responseConstructor.formRowEnd();
			};

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* ViewQueueGrid ******/
			responseConstructor.formGridStart("viewQueueGrid");
			responseConstructor.formColumn(getGridColumns("view", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(otherDetailListAll.size());
			for (Iterator<Sweep> it = otherDetailListAll.iterator(); it.hasNext();) {
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if ("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE, SwtUtil.formatDate(swp.getValueDate(), dateFormat), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT, swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType()) ? SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request) : SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser(), true, false);
				} else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE, SwtUtil.formatDate(swp.getValueDate(), dateFormat));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT, swp.getNewCalulatedAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType()) ? SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request) : SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser());
				}
				responseConstructor.formRowEnd();
			}
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.SWEEP_QUEUE);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [displayList()] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepCancelQueueAction.'displayList' method : "
							+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			setSweepQueue(sweep);
			return getView("cancel");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepCancelQueueAction.'displayList' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayList", SweepCancelQueueAction.class), request,
					"");

			return getView("fail");
		} finally {
			/* null the objects created already. */
			sweep = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			initialCurrencyCode = null;
		}
	}

	/**
	 * Submit method is used to cancel a single sweep or the list of sweeps
	 * selected by the user.
	 *
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String submit()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		Sweep sweep = null;
		// DynaValidatorForm dyForm = null;
		SystemFormats systemFormats = null;
		ArrayList sweepDetailListAll = null;
		ArrayList otherDetailListAll = null;
		int currencyGrpAccess = -1;
		String sweeps = "";
		String currentFilter = "";
		String currentSort = "";
		String filterSortStatus = "";
		int currentPage = 0;
		int initialPageCount = 0;
		boolean isNext = false;
		int maxPage = 0;
		PageDetails pSummary;
		ArrayList<PageDetails> pageSummaryList = null;
		String initialCurrencyCode = null;
		// Variable declaration for totalcount
		int totalCount = 0;
		// Variable declaration for forward
		String forward = null;
		// Variable declaration for selectedlist
		String selectedlist = null;
		// Variable declaration for bypassChangedSweep
		String bypassChangedSweep = null;
		// Variable declaration for bypassCutOff
		String bypassCutOff = null;
		// Variable declaration for bypassAccountBreach
		String bypassAccountBreach = null;
		// Variable declaration for list
		// submit/authorize/cancel sweep
		ActionMessages list = null;
		// Variable declaration for sweepMsg
		String sweepMsg = null;
		// Variable declaration for start
		int start = 0;
		// Variable declaration for end
		int end = 0;
		// Variable declaration for currGrpId
		String currGrpId = null;
		// Variable declaration for roleId
		String roleId = null;
		// Variable declaration for entityId
		String entityId = null;
		// Variable declaration for entityList
		Collection entityList = null;
		// Variable declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable declaration for temp
		ArrayList temp = null;
		// Variable declaration for groupList
		Collection groupList = null;
		// Variable declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable declaration for errorsType1
		Iterator errorsType1 = null;
		// Variable declaration for errorsType2
		Iterator errorsType2 = null;
		// Variable declaration for errorsType3
		Iterator errorsType3 = null;
		// Variable declaration for roleObject
		RoleTO roleObject = null;
		// Variable declaration for itGroupList
		Iterator itGroupList = null;
		// Variable declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		// Variable declaration for tempItr
		Iterator tempItr = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		// To hold date format
		String dateFormat = null;
		// To hold amount format
		String amountFormat = null;
		// To hold system format
		SystemFormats sysFormat = null;
		try {
			log.debug(" Entering submit method");
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			forward = "cancel";
			selectedlist = (String) request.getParameter("selectedList");
			// Read the selected filter from the request and store in String
			// variable
			currentFilter = request.getParameter("selectedFilter");
			// Read the selectedSort from the request and store in String
			// variable
			currentSort = request.getParameter("selectedSort");

			systemFormats = new SystemFormats();
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();

			/* Condition to check currentFilter is null */
			if (currentFilter == null
					|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
				// If current filter is null the default value 'All' is set
				// for currentFilter
				currentFilter = "all";
			}
			/* Condition to check currentSort is null */
			if (currentSort == null
					|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
				/* If currentSort is null then set current sort as none. */
				currentSort = "none";
			}
			// Use # as separator for the combined current filter and
			// current sort
			filterSortStatus = currentFilter + "#" + currentSort;
			currentPage = 1;
			initialPageCount = 0;
			isNext = false;
			// Added to identify if the sweep is to be submitted
			// even if new sweep amount is changed.
			bypassChangedSweep = (String) request
					.getParameter("bypassChangedSweep");
			bypassCutOff = (String) request.getParameter("bypassCutOff");
			bypassAccountBreach = (String) request
					.getParameter("bypassAccountBreach");
			sweep = (Sweep) getSweepQueue();
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));
			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());

			//to be checked by Atef

			sweep.setEntityId(request.getParameter("entityId"));

			sweep.setAccountType(request.getParameter("accountType"));

			sweep.setCurrencyCode(request.getParameter("currencyCode"));

			sweep.setQueueName(request.getParameter("queueName"));

			// If condition for canceling or submiting the queue
			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				list = sweepQueueManager.cancel(sweep, selectedlist,
						systemFormats);
				forward = "cancel";
			} else {
				list = sweepQueueManager.submit(sweep, selectedlist,
						systemFormats, bypassChangedSweep, bypassCutOff,
						bypassAccountBreach);
			}
			// Checking if error is of type - SwtConstants.SWEEP_AMOUNT_CHANGED
			errorsType1 = list.get(SwtConstants.SWEEP_CUTOFF_EXCEEDED);

			errorsType2 = list.get(SwtConstants.SWEEP_AMOUNT_CHANGED);

			errorsType3 = list.get(SwtConstants.SWEEP_ACCOUNT_BREACHED);

			if (errorsType1.hasNext()) {
				// Setting this status will enable to call a pop-up with Yes and
				// No
				// as buttons along with error message.
				sweepMsg = errorsType1.next().toString();
				start = sweepMsg.indexOf("[");
				end = sweepMsg.indexOf("]");
				sweeps = sweepMsg.substring(start + 1, end);

				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_CUT_OFF", "Y");
				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			if (errorsType2.hasNext()) {
				// Setting this status will enable to call a pop-up with Yes and
				// No as buttons along with error message.
				sweepMsg = errorsType2.next().toString();
				start = sweepMsg.indexOf("[");
				end = sweepMsg.indexOf("]");
				if (sweeps.equals(""))
					sweeps = sweepMsg.substring(start + 1, end);
				else
					sweeps = sweeps + "," + sweepMsg.substring(start + 1, end);
				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_SWEEP_AMOUNT", "Y");

				request.setAttribute("bypassCutOff", bypassCutOff);
				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			if (errorsType3.hasNext()) {
				// Setting this status will enable to call a pop-up with Yes and
				// No as buttons along with error message.
				sweepMsg = errorsType3.next().toString();
				start = sweepMsg.indexOf("[");
				end = sweepMsg.indexOf("]");
				if (sweeps.equals(""))
					sweeps = sweepMsg.substring(start + 1, end);
				else
					sweeps = sweeps + "," + sweepMsg.substring(start + 1, end);
				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_ACCOUNT_BREACH", "Y");
				// sets the sweep ids whose calculated amount <> queue amount
				// request.setAttribute("ERROR_SWEEPS", sweeps);
				request.setAttribute("bypassChangedSweep", bypassChangedSweep);
				request.setAttribute("bypassCutOff", bypassCutOff);
				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			saveErrors(request, list);
			if (!sweeps.equals(""))
				request.setAttribute("ERROR_SWEEPS", sweeps);
			// after processing refresh the screen
			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));
			putEntityListInReq(request);

			currGrpId = sweep.getCurrencyCode();
			initialCurrencyCode = currGrpId;
			roleId = getRoleId(request);
			entityId = sweep.getEntityId();
			if (!entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)
					&& !currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupAccess(roleId, entityId, currGrpId);
			}
			// code handling for Entity:All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				totalCount = 0;
				roleObject = new RoleTO(roleId);
				entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				Iterator itr = entityList.iterator();

				while (itr.hasNext()) {
					currGrpId = sweep.getCurrencyCode();

					EntityUserAccess entityUserAccess = (EntityUserAccess) itr
							.next();
					entityId = entityUserAccess.getEntityId();

					sweep.setEntityId(entityId);

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);
						itGroupList = groupList.iterator();
						temp = new ArrayList();
						currGrpId = "";
						/*
						 * Start:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */
						// This while loop is for gennerating currecy group
						// access for cancel panel
						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access
							if (entityCurrencyGroupAccess.getAccess() == 0) {
								if (currGrpCounter > 0) {
									currGrpId += ",'"
											+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
								} else {
									currGrpId += "'"
											+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
								}
							}
							currGrpCounter++;
						}
						/*
						 * End:Code Modified by Balaji for Mantis 1483 on for
						 * Mantis 1483 8-JUN-2011:For Sweep Cancel Queue Screen
						 * displays error when the last currency group in
						 * Currency group Access screen is set to View Access
						 */
						if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
							currGrpId = "''";
						}
						sweep.setCurrencyCode(currGrpId);
						// Intializing cancelViewCurrGrpId as empty
						cancelViewCurrGrpId = "";
						itGroupList = groupList.iterator();
						// This while loop is for gennerating currecy group
						// access for view panel

						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access
							if (itGroupList.hasNext())
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "',";
							else
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
						}
						if (cancelViewCurrGrpId
								.equals(SwtConstants.EMPTY_STRING)) {
							cancelViewCurrGrpId = "''";
						}
						sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());
						sweepQueueDetailVO.setOtherDetailList(new ArrayList());

						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);

						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext()) {
							sweepDetailListAll.add(tempItr.next());
						}

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext()) {
							otherDetailListAll.add(tempItr.next());
						}
						sweep.setCurrencyCode("All");
					} else {
						temp = new ArrayList();
						sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
						sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
								+ "'");
						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setEntityId("All");

			} else {
				// code handling for Entity: All ends
				// code for handling of All currency group added starts
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					itGroupList = groupList.iterator();
					temp = new ArrayList();

					currGrpId = "";
					/*
					 * Start:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					currGrpCounter = 0;
					// This while loop is for gennerating currecy group
					// access for cancel panel

					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						if (entityCurrencyGroupAccess.getAccess() == 0) {
							// If condition is for appending qouation and commas
							// for currency group access
							if (currGrpCounter > 0) {
								currGrpId += ",'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
							} else {
								currGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
							}
						}
						currGrpCounter++;
					}
					/*
					 * End:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
						currGrpId = "''";
					}
					sweep.setCurrencyCode(currGrpId);
					// Intializing cancelViewCurrGrpId as empty
					cancelViewCurrGrpId = "";
					itGroupList = groupList.iterator();
					// This while loop is for gennerating currecy group
					// access for view panel

					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access
						if (itGroupList.hasNext())
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "',";
						else
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
					}
					if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
						cancelViewCurrGrpId = "''";
					}
					sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());

					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);

					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

					tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				} else {
					sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
					sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
							+ "'");
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}
			sweep.setCurrencyCode(initialCurrencyCode);
			// code for handling of All currency group ends
			setSweepQueue(sweep);

			pageSummaryList = new ArrayList<PageDetails>();
			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked to enable or disable the next link */
			if (maxPage > 1) {
				// If true the isNext flag is set to true which is set to
				// nextEnabled request attribute which is used in UI to
				// enable the next link.
				isNext = true;
			}

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}
			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE);

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);

			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, entityId);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			if (!sweeps.equals("")) {

				String msg = getMsgDesc(list.toString(), request);
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message", msg);


				return getView("statechange");
			}else {
				//Angular part
				responseHandler = new ResponseHandler();
				responseConstructor = new SwtResponseConstructor();
				xmlWriter = responseConstructor.getXMLWriter();

				xmlWriter.startElement(SwtConstants.SWEEP_QUEUE);

				responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
						SwtConstants.DATA_FETCH_OK);
				// forms singleton node
				xmlWriter.startElement(SwtConstants.SINGLETONS);

				String msg = getMsgDesc(list.toString(), request);
				responseConstructor.createElement("listOfMsgs",
						msg);
				responseConstructor.createElement("defaultEntity",
						entityId);
				responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTime(request,
						entityId));
				responseConstructor.createElement("accountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);
				responseConstructor.createElement("currentPage", Integer.toString(currentPage));
				responseConstructor.createElement("prevEnabled", "false");
				responseConstructor.createElement("nextEnabled", "" + isNext);
				responseConstructor.createElement("maxPage", Integer.toString(maxPage));
				responseConstructor.createElement("selectedFilter", currentFilter);
				responseConstructor.createElement("selectedSort", currentSort);
				responseConstructor.createElement("totalCount", totalCount);
				xmlWriter.endElement(SwtConstants.SINGLETONS);
				/***** Entity Combo Start ***********/
				ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
				// options drop down list
				ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

				lstOptions = new ArrayList<OptionInfo>();
				LabelValueBean row = null;
				entityList = putEntityListInReq(request);
				Iterator j = entityList.iterator();
				row = null;
				while (j.hasNext()) {
					row = (LabelValueBean) j.next();
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
				}
				lstSelect.add(new SelectInfo("entityList", lstOptions));
				/***** Entity Combo End ***********/

				/***** Currency Combo Start ***********/
				lstOptions = new ArrayList<OptionInfo>();
				row = null;
				Collection currencyGrpList = putCurrencyGroupListInReqNew(request, sweep.getId().getHostId(), sweep
						.getEntityId());

				j = currencyGrpList.iterator();
				row = null;
				while (j.hasNext()) {
					row = (LabelValueBean) j.next();
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
				}
				lstSelect.add(new SelectInfo("currencyGrpList", lstOptions));
				/***** Currency Combo End ***********/


				/***** Account Type list Combo Start ***********/
				// options drop down list
				lstOptions = new ArrayList<OptionInfo>();
				row = null;
				Collection acctTypeList = putAccountListInReq(request, sweep.getEntityId());

				j = acctTypeList.iterator();
				row = null;
				while (j.hasNext()) {
					row = (LabelValueBean) j.next();
					lstOptions.add(new OptionInfo(row.getLabel(), row.getValue(), false));
				}
				lstSelect.add(new SelectInfo("acctTypeList", lstOptions));
				/***** Account list Combo End ***********/

				responseConstructor.formSelect(lstSelect);

				/******* AuthoriseQueueGrid ******/
				responseConstructor.formGridStart("authoriseQueueGrid");
				responseConstructor.formColumn(getGridColumns("auth", width, columnOrder, hiddenColumns));
				responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalCount, pageSize),currentPage));

				// form rows (records)
				responseConstructor.formRowsStart(sweepDetailListAll.size());
				for (Iterator<Sweep> it = sweepDetailListAll.iterator(); it.hasNext();) {
					// Obtain rules definition tag from iterator
					Sweep swp = (Sweep) it.next();
					responseConstructor.formRowStart();
					if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
						responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
						responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
						responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser(), true, false);
					}else {
						responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
						responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
						responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
						responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
						responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
						responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
						responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
						responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
						responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
						responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
						responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
						responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
						responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus());
						responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser());
					}
					responseConstructor.formRowEnd();
				};

				responseConstructor.formRowsEnd();
				responseConstructor.formGridEnd();

				/******* ViewQueueGrid ******/
				responseConstructor.formGridStart("viewQueueGrid");
				responseConstructor.formColumn(getGridColumns("view", width, columnOrder, hiddenColumns));
				// form rows (records)
				responseConstructor.formRowsStart(otherDetailListAll.size());
				for (Iterator<Sweep> it = otherDetailListAll.iterator(); it.hasNext();) {
					Sweep swp = (Sweep) it.next();
					responseConstructor.formRowStart();
					if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
						responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
						responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
						responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser(), true, false);
					}else {
						responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
						responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
						responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount());
						responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
						responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
						responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
						responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
						responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
						responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
						responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
						responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
						responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
						responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus());
						responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser());
					}
					responseConstructor.formRowEnd();
				}

				responseConstructor.formRowsEnd();
				responseConstructor.formGridEnd();

				xmlWriter.endElement(SwtConstants.SWEEP_QUEUE);
				request.setAttribute("data", xmlWriter.getData());
				log.debug(this.getClass().getName() + " - [displayAngular()] - " + "Exit");
				return getView("data");
			}
		} catch (SwtException sexp) {
			log
					.error("SwtException Catch in SweepCancelQueueAction.'submit' method : "
							+ sexp.getMessage());

			saveErrors(request, SwtUtil.logException(sexp, request, ""));

			currGrpId = sweep.getCurrencyCode();
			initialCurrencyCode = currGrpId;
			roleId = getRoleId(request);
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			// open the screen in same status again
			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			// code for handling of All currency group added starts
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				// log.debug("processing for ALL starts");
				groupList = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				itGroupList = groupList.iterator();
				temp = new ArrayList();

				currGrpId = "";
				/*
				 * Start:Code Modified by Balaji for Mantis 1483 on
				 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error when
				 * the last currency group in Currency group Access screen is
				 * set to View Access
				 */
				currGrpCounter = 0;
				// This while loop is for gennerating currecy group
				// access for cancel panel

				while (itGroupList.hasNext()) {
					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					// If condition is for appending qouation and commas
					// for currency group access
					if (entityCurrencyGroupAccess.getAccess() == 0) {
						if (currGrpCounter > 0) {
							currGrpId += ",'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
						} else {
							currGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
						}
					}
					currGrpCounter++;
				}
				/*
				 * End:Code Modified by Balaji on 8-JUN-2011:For Sweep Cancel
				 * Queue Screen displays error when the last currency group in
				 * Currency group Access screen is set to View Access
				 */
				if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
					currGrpId = "''";
				}
				sweep.setCurrencyCode(currGrpId);
				// Intializing cancelViewCurrGrpId as empty
				cancelViewCurrGrpId = "";
				itGroupList = groupList.iterator();
				// This while loop is for gennerating currecy group
				// access for view panel

				while (itGroupList.hasNext()) {
					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					// If condition is for appending qouation and commas
					// for currency group access
					if (itGroupList.hasNext())
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
								.getCurrencyGroupId() + "',";
					else
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
								.getCurrencyGroupId() + "'";
				}
				if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
					cancelViewCurrGrpId = "''";
				}
				// Setting currency group to bean
				sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
				sweepQueueDetailVO.setEntityAccessList(SwtUtil
						.getUserEntityAccessList(request.getSession()));
				sweepQueueDetailVO.setSweepDetailList(new ArrayList());
				sweepQueueDetailVO.setOtherDetailList(new ArrayList());
				currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

				tempItr = temp.iterator();

				while (tempItr.hasNext())
					sweepDetailListAll.add(tempItr.next());

				temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
				tempItr = temp.iterator();

				while (tempItr.hasNext())
					otherDetailListAll.add(tempItr.next());

				sweep.setCurrencyCode("All");

			} else {
				sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
				sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode + "'");
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
			}
			sweep.setCurrencyCode(initialCurrencyCode);
			pageSummaryList = new ArrayList<PageDetails>();

			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked to enable or disable the next link */
			if (maxPage > 1) {
				// If true the isNext flag is set to true which is set to
				// nextEnabled request attribute which is used in UI to
				// enable the next link.
				isNext = true;
			}

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			setSweepQueue(sweep);
			request.setAttribute("sweepDetailList", sweepQueueDetailVO
					.getSweepDetailList());
			request.setAttribute("othersDetailList", sweepQueueDetailVO
					.getOtherDetailList());

			putEntityListInReq(request);

			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, entityId);

			forward = "cancel";

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				forward = "cancel";
			}
			return getView(forward);
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepCancelQueueAction.'submit' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "submit", SweepCancelQueueAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * Method to put the entity list in the request
	 *
	 * @param request
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	private Collection putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, request
				.getSession());

		request.setAttribute("entityList", coll);
		return coll;
	}

	/**
	 * Method to put the account type in the request
	 *
	 * @param request
	 * @param entityId
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	private Collection putAccountListInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		CacheManager cacheManagerInst = CacheManager.getInstance();
		ArrayList coll = (ArrayList) cacheManagerInst.getMiscParamsLVL(
				"ACCOUNTTYPE", entityId);
		coll.remove(0);
		coll.add(0, new LabelValueBean("All", "All"));
		request.setAttribute("accountList", coll);
		return coll;
	}

	/**
	 * @return Returns the sweepQueueManager.
	 */
	public SweepQueueManager getSweepQueueManager() {
		return sweepQueueManager;
	}

	/**
	 * @param sweepQueueManager
	 *            The sweepQueueManager to set.
	 */
	public void setSweepQueueManager(SweepQueueManager sweepQueueManager) {
		this.sweepQueueManager = sweepQueueManager;
	}

	/**
	 * This method is used to put associated CurrencyGroup list into the request
	 * that is further displayed on the UI.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 */
	@SuppressWarnings("unchecked")
	private String putCurrencyGroupListInReq(HttpServletRequest request,
											 String hostId, String entityId) throws SwtException {
		log
				.debug("Inside SweepCancelQueueAction.putCurrencyGroupListInReq method");

		Collection currencyGroupList = new ArrayList();

		String defaultCurrencyGroup = new String();
		String roleId = getRoleId(request);
		// code added for handling of the Entity:All STARTS
		ArrayList al = new ArrayList();

		if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
			RoleTO roleObject = new RoleTO(roleId);
			Collection entityList = SwtUtil.getSwtMaintenanceCache()
					.getEntityAccessCollection(roleObject);
			Iterator itr = entityList.iterator();

			while (itr.hasNext()) {
				EntityUserAccess entityUserAccess = (EntityUserAccess) itr
						.next();
				String entityIdAll = entityUserAccess.getEntityId();

				Collection groupListAll = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityIdAll);
				Iterator groupListAllitr = groupListAll.iterator();

				while (groupListAllitr.hasNext())
					al.add(groupListAllitr.next());
			}
		} else {
			// code added for handling of the Entity:All ENDS
			al = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupViewORFullAcess(roleId, entityId);

		}

		Iterator itGroupList = al.iterator();
		currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();

			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}

		request.setAttribute("currencyGroupList", currencyGroupList);

		if ((currencyGroupList != null) && (currencyGroupList.size() > 1)) {
			ArrayList tempArrayList = (ArrayList) currencyGroupList;
			LabelValueBean labelValueBean = (LabelValueBean) tempArrayList
					.get(1);

			defaultCurrencyGroup = labelValueBean.getValue();
		}

		return defaultCurrencyGroup;
	}

	/**
	 * This method is used to put associated CurrencyGroup list into the request
	 * that is further displayed on the UI.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 */
	@SuppressWarnings("unchecked")
	private Collection putCurrencyGroupListInReqNew(HttpServletRequest request,
													String hostId, String entityId) throws SwtException {
		log
				.debug("Inside SweepCancelQueueAction.putCurrencyGroupListInReqNew method");

		Collection currencyGroupList = new ArrayList();

		String defaultCurrencyGroup = new String();
		String roleId = getRoleId(request);
		// code added for handling of the Entity:All STARTS
		ArrayList al = new ArrayList();

		if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
			RoleTO roleObject = new RoleTO(roleId);
			Collection entityList = SwtUtil.getSwtMaintenanceCache()
					.getEntityAccessCollection(roleObject);
			Iterator itr = entityList.iterator();

			while (itr.hasNext()) {
				EntityUserAccess entityUserAccess = (EntityUserAccess) itr
						.next();
				String entityIdAll = entityUserAccess.getEntityId();

				Collection groupListAll = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityIdAll);
				Iterator groupListAllitr = groupListAll.iterator();

				while (groupListAllitr.hasNext())
					al.add(groupListAllitr.next());
			}
		} else {
			// code added for handling of the Entity:All ENDS
			al = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupViewORFullAcess(roleId, entityId);

		}

		Iterator itGroupList = al.iterator();
		currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();

			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}

		request.setAttribute("currencyGroupList", currencyGroupList);

		if ((currencyGroupList != null) && (currencyGroupList.size() > 1)) {
			ArrayList tempArrayList = (ArrayList) currencyGroupList;
			LabelValueBean labelValueBean = (LabelValueBean) tempArrayList
					.get(1);

			defaultCurrencyGroup = labelValueBean.getValue();
		}

		return currencyGroupList;
	}
	/**
	 * Fetched the role id of the current logged in user
	 *
	 * @param request
	 * @return String
	 */
	public String getRoleId(HttpServletRequest request) {
		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute("CDM");
		User currUser = (User) CDM.getUser();
		String roleId = currUser.getRoleId();

		return roleId;
	}

	/**
	 * This method is called when PREVIOUS LINK, NEXT LINK or any Page no is
	 * clicked. It fetches the sweeps that can be cancelled for that page.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String next()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String currentFilter = "";
		String currentSort = "";
		String filterSortStatus = "";
		int currentPage = 0;
		int initialPageCount = 0;
		boolean isNext = false;
		int maxPage = 0;

		ArrayList<PageDetails> pageSummaryList = null;
		int clickedPage;
		String nextLinkStatus = null;
		String prevLinkStatus = null;
		Sweep sweep;
		String initialCurrencyCode = "";
		// Variable declaration for sweepDetailListAll
		ArrayList sweepDetailListAll = null;
		// Variable declaration for otherDetailListAll
		ArrayList otherDetailListAll = null;
		// Variable declaration for dyForm
		// DynaValidatorForm dyForm = null;
		// Variable declaration for session
		HttpSession session = null;
		// Variable declaration for currencyGrpAccess
		int currencyGrpAccess = 0;
		// Variable declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable declaration for entityList
		Collection entityList = null;
		// Variable declaration for entityUserAccess
		EntityUserAccess entityUserAccess = null;
		// Variable declaration for groupList
		Collection groupList = null;
		// Variable declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		// Variable declaration for acctType
		String acctType = null;
		// Variable declaration for currGrpId
		String currGrpId = null;
		// Variable declaration for roleId
		String roleId = null;
		// Variable declaration for entityId
		String entityId = null;
		// Variable declaration for roleObject
		RoleTO roleObject = null;
		// Variable declaration for itr
		Iterator itr = null;
		// Variable declaration for itGroupList
		Iterator itGroupList = null;
		// Variable declaration for systemFormats
		SystemFormats systemFormats = null;
		// Variable declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable declaration for temp
		ArrayList temp = null;
		// Variable declaration for tempItr
		Iterator tempItr = null;
		try {
			log.debug(this.getClass().getName() + " - [next] - " + "Entry");
			// ArrayLists added for Currency Group 'All' case
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();
			session = request.getSession();
			int totalCount = 0;
			/* Get all the filter values specified for all columns */
			currentFilter = (request.getParameter("selectedFilter") != null) ? request
					.getParameter("selectedFilter").trim().toString()
					: "all";

			/* Gets the sort column specified */
			currentSort = (request.getParameter("selectedSort") != null) ? request
					.getParameter("selectedSort").trim().toString()
					: "1|false";

			filterSortStatus = currentFilter + "#" + currentSort;

			/*
			 * Read the current page value and store it as integer in a integer
			 * variable
			 */
			currentPage = Integer.parseInt(((request
					.getParameter("currentPage") != null) && !request
					.getParameter("currentPage").equals("")) ? request
					.getParameter("currentPage").trim().toString() : "0");

			/*
			 * Read the clicked page value and store it as integer in a integer
			 * variable
			 */
			clickedPage = Integer
					.parseInt(((request.getParameter("goToPageNo") != null) && !request
							.getParameter("goToPageNo").equals("")) ? request
							.getParameter("goToPageNo").trim().toString() : "0");

			/*
			 * Read the maxpage value and store it as integer in a integer
			 * variable
			 */
			maxPage = Integer
					.parseInt(((request.getParameter("maxPages") != null) && !request
							.getParameter("maxPages").equals("")) ? request
							.getParameter("maxPages").trim().toString() : "0");

			/* Previous Link Clicked */
			if (clickedPage == -2) {
				currentPage--;
				clickedPage = currentPage;
			} else {
				/* Next Link Clicked */
				if (clickedPage == -1) {
					currentPage++;
					clickedPage = currentPage;
				} else {
					currentPage = clickedPage;
				}
			}
			/*
			 * Condition to check the clicked page.If Clicked page greater than
			 * 1 then previous link enable. If Clicked page less than 1 then
			 * previous link disable
			 */
			if (clickedPage > 1) {
				prevLinkStatus = "true";
			} else {
				prevLinkStatus = "false";
			}
			/*
			 * Condition to check the clicked page.If Clicked page less than
			 * maximum page then next link enable. If Clicked page greater than
			 * maximum page then next link disable
			 */

			if (clickedPage < maxPage) {
				nextLinkStatus = "true";
			} else {
				nextLinkStatus = "false";
			}

			systemFormats = new SystemFormats();

			// Read the selected filter from the request and store in String
			// variable
			currentFilter = request.getParameter("selectedFilter");
			// Read the selectedSort from the request and store in String
			// variable
			currentSort = request.getParameter("selectedSort");
			/* Condition to check currentFilter is null */
			if (currentFilter == null
					|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
				// If current filter is null the default value 'All' is set
				// for currentFilter
				currentFilter = "all";
			}

			/* Condition to check currentSort is null */
			if (currentSort == null
					|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
				/* If currentSort is null then set current sort as none. */
				currentSort = "none";
			}

			// Use # as separator for the combined current filter and
			// current sort
			filterSortStatus = currentFilter + "#" + currentSort;

			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));
			sweep = (Sweep) getSweepQueue();

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));

			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());
			sweep.setQueueName(SwtConstants.SWEEP_STATUS_CANCEL);
			acctType = request.getParameter("accountType");
			sweep.setEntityId(request.getParameter("entityId"));
			sweep.setCurrencyCode(request.getParameter("currGrpId"));
			sweep.setAccountType(acctType);
			if (sweep.getAccountType().equalsIgnoreCase("C")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CASH_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("U")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CUSTODIAN_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("All")) {
				request.setAttribute("AccountDesp",
						SwtConstants.ALL_ACCOUNTS_TYPE);
			}

			putEntityListInReq(request);

			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, sweep.getEntityId());

			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			currGrpId = "";
			roleId = getRoleId(request);
			entityId = sweep.getEntityId();

			session = request.getSession();
			if ((!entityId.equalsIgnoreCase(SwtUtil
					.getUserCurrentEntity(request.getSession())))
					&& sweep.getCurrencyCode().equalsIgnoreCase(
					((CommonDataManager) session
							.getAttribute(SwtConstants.CDM_BEAN))
							.getUser().getCurrentCcyGrpId())) {
				currGrpId = "All";

			} else {
				currGrpId = sweep.getCurrencyCode();
			}
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			initialCurrencyCode = currGrpId;
			// code added to handle Entity as All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				maxPage = 0;
				roleObject = new RoleTO(roleId);
				entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				itr = entityList.iterator();

				while (itr.hasNext()) {

					currGrpId = sweep.getCurrencyCode();
					entityUserAccess = (EntityUserAccess) itr.next();
					entityId = entityUserAccess.getEntityId();
					sweep.setEntityId(entityId);
					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}
					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);
						itGroupList = groupList.iterator();
						temp = new ArrayList();
						currGrpId = "";
						/*
						 * Start:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */

						// This while loop is for gennerating currecy group
						// access for cancel panel
						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access

							if (entityCurrencyGroupAccess.getAccess() == 0) {
								if (currGrpCounter > 0) {
									currGrpId += ",'"
											+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
								} else {
									currGrpId += "'"
											+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
								}
							}
							currGrpCounter++;
						}
						/*
						 * End:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */
						if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
							currGrpId = "''";
						}
						sweep.setCurrencyCode(currGrpId);
						// Intializing cancelViewCurrGrpId as empty
						cancelViewCurrGrpId = "";
						itGroupList = groupList.iterator();
						// This while loop is for gennerating currecy group
						// access for view panel
						while (itGroupList.hasNext()) {
							// If condition is for appending qouation and commas
							// for currency group access
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();

							if (itGroupList.hasNext())
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "',";
							else
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
						}
						if (cancelViewCurrGrpId
								.equals(SwtConstants.EMPTY_STRING)) {
							cancelViewCurrGrpId = "''";
						}
						sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());
						sweepQueueDetailVO.setOtherDetailList(new ArrayList());
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
						// Getting no of counts of recors from the mannager
						// class
						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
						sweep.setCurrencyCode("All");
					} else {
						temp = new ArrayList();
						sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
						sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
								+ "'");
						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setEntityId("All");
			} else {
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					currencyGrpAccess = 0;
				} else {
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				}
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					itGroupList = groupList.iterator();
					temp = new ArrayList();
					currGrpId = "";
					/*
					 * Start:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					currGrpCounter = 0;
					// This while loop is for gennerating currecy group
					// access for cancel panel
					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access

						if (entityCurrencyGroupAccess.getAccess() == 0) {
							if (currGrpCounter > 0) {
								currGrpId += ",'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
							} else {
								currGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
							}
							currGrpCounter++;
						}
					}
					/*
					 * End:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
						currGrpId = "''";
					}
					sweep.setCurrencyCode(currGrpId);
					// Intializing cancelViewCurrGrpId as empty
					cancelViewCurrGrpId = "";
					itGroupList = groupList.iterator();
					// This while loop is for gennerating currecy group
					// access for view panel
					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access

						if (itGroupList.hasNext())
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "',";
						else
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
					}
					if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
						cancelViewCurrGrpId = "''";
					}
					sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				} else {
					sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
					sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
							+ "'");
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}
			sweep.setCurrencyCode(initialCurrencyCode);
			setSweepQueue(sweep);
			pageSummaryList = new ArrayList<PageDetails>();

			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}
			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);

			log.debug(this.getClass().getName() + " - [next] - " + "Exit");
			return getView("cancel");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("cancel");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "next", SweepCancelQueueAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This function is used calculate the maximum pages based on the total
	 * number of records and the records to be displayed per page
	 *
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Entering");
		/* Class instance declaration */
		int maxPage = 0;
		int remainder = 0;
		if (totalCount > 0 && pageSize > 0) {
			maxPage = (totalCount) / (pageSize);
			remainder = totalCount % pageSize;
			if (remainder > 0) {
				maxPage++;
			}
		}
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Exiting");
		return maxPage;
	}

	/**
	 * Method added for setting the page numbers.
	 *
	 * @return none
	 */
	private void setPageSummaryList(int currentPage, int maxPage,
									int totalCount, ArrayList<PageDetails> pageSummaryList) {
		log.debug(this.getClass().getName() + " - [setPageSummaryList] - "
				+ "Entry");
		PageDetails pSummary = new PageDetails();
		pSummary.setCurrentPageNo(currentPage);
		pSummary.setMaxPages(maxPage);
		pSummary.setTotalCount(totalCount);
		pageSummaryList.add(pSummary);
		log.debug(this.getClass().getName() + " - [setPageSummaryList] - "
				+ "Exit");
	}


	private List<ColumnInfo> getGridColumns(String source, String width, String columnOrder, String hiddenColumns)
			throws SwtException {
		/* Array list to hold column order */
		ArrayList<String> orders = null;
		/* String array variable to hold column order property */
		String[] columnOrderProp = null;
		/* Iterator variable to hold column order */
		Iterator<String> columnOrderItr = null;
		/* Hash map to hold column width */
		HashMap<String, String> widths = null;
		/* String array variable to hold width property */
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;

		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ getGridColumns ] - Entry");
			/* Condition to check width is null */

			if (SwtUtil.isEmptyOrNull(width)) {

				width =  SwtConstants.SWEEP_VALUE_DATE + "=80,"
						+ SwtConstants.SWEEP_CCY_CODE + "=67,"
						+ SwtConstants.SWEEP_CURRENT_AMT + "=150,"
						+ SwtConstants.SWEEP_NEW_AMOUNT + "=150,"
						+ SwtConstants.SWEEP_SEARCH_ENTITY_CR+ "=100,"
						+ SwtConstants.SWEEP_ACCOUNT_ID_CR + "=220,"
						+ SwtConstants.ACCOUNT_CR_NAME + "=180,"
						+ SwtConstants.SWEEP_SEARCH_ENTITY_DR + "=100,"
						+ SwtConstants.SWEEP_ACCOUNT_ID_DR + "=220,"
						+ SwtConstants.ACCOUNT_DR_NAME + "=180,"
						+ SwtConstants.SWEEP_SWEEP_ID + "=120,"
						+ SwtConstants.SWEEP_CRD_INT_MSG + "=140,"
						+ SwtConstants.SWEEP_CRD_EXT_MSG + "=140,"
						+ SwtConstants.SWEEP_DR_INT_MSG + "=140,"
						+ SwtConstants.SWEEP_DR_EXT_MSG + "=140,"
						+ SwtConstants.SWEEP_TYPE + "=76,"
						+ SwtConstants.SWEEP_USER + "=144,"
						+ SwtConstants.SWEEP_STATUS + "=95,"
						+ SwtConstants.SWEEP_DATE_TIME_USER + "=230";

			}

			/* Obtain width for each column */
			columnWidthProperty = width.split(",");

			/* Loop to insert each column in hash map */
			widths = new HashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				/* Condition to check index of = is -1 */

				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");

					widths.put(propval[0], propval[1]);
				}
			}

			/* Condition to check column order is null or empty */
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				/* Default values for column order */
				columnOrder = SwtConstants.SWEEP_VALUE_DATE + ","
						+ SwtConstants.SWEEP_CCY_CODE + ","
						+ SwtConstants.SWEEP_CURRENT_AMT + ","
						+ SwtConstants.SWEEP_NEW_AMOUNT + ","
						+ SwtConstants.SWEEP_SEARCH_ENTITY_CR+ ","
						+ SwtConstants.SWEEP_ACCOUNT_ID_CR + ","
						+ SwtConstants.ACCOUNT_CR_NAME + ","
						+ SwtConstants.SWEEP_SEARCH_ENTITY_DR + ","
						+ SwtConstants.SWEEP_ACCOUNT_ID_DR + ","
						+ SwtConstants.ACCOUNT_DR_NAME + ","
						+ SwtConstants.SWEEP_SWEEP_ID + ","
						+ SwtConstants.SWEEP_CRD_INT_MSG + ","
						+ SwtConstants.SWEEP_CRD_EXT_MSG + ","
						+ SwtConstants.SWEEP_DR_INT_MSG + ","
						+ SwtConstants.SWEEP_DR_EXT_MSG + ","
						+ SwtConstants.SWEEP_TYPE + ","
						+ SwtConstants.SWEEP_USER + ","
						+ SwtConstants.SWEEP_STATUS + ","
						+ SwtConstants.SWEEP_DATE_TIME_USER ;

			}
			orders = new ArrayList<String>();
			/* Split the columns using , and save in string array */
			columnOrderProp = columnOrder.split(",");
			/* Loop to enter column order in array list */

			for (int i = 0; i < columnOrderProp.length; i++) {
				/* Adding the Column values to ArrayList */
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet()
						.iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();
			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				if (order.equals(SwtConstants.SWEEP_VALUE_DATE)) {
					// column Alert Date

					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_VALUE_DATE_HEADER, request),
							SwtConstants.SWEEP_VALUE_DATE,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false, 0,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_VALUE_DATE)),
							false, "auth".equalsIgnoreCase(source)?true:false, hiddenColumnsMap
							.get(SwtConstants.SWEEP_VALUE_DATE )));

					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);

				} else if (order.equals(SwtConstants.SWEEP_CCY_CODE)) {
					// column Alert Date
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_CCY_CODE_HEADER, request),
							SwtConstants.SWEEP_CCY_CODE,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false, 1,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_CCY_CODE)),
							false, "auth".equalsIgnoreCase(source)?true:false, hiddenColumnsMap
							.get(SwtConstants.SWEEP_CCY_CODE )));

					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.SWEEP_CURRENT_AMT )) {
					// column Alert Date
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_CURRENT_AMT_HEADER, request),
							SwtConstants.SWEEP_CURRENT_AMT ,
							SwtConstants.COLUMN_TYPE_NUMBER, false, 2,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_CURRENT_AMT )),
							false, false, hiddenColumnsMap
							.get(SwtConstants.SWEEP_CURRENT_AMT )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.SWEEP_NEW_AMOUNT )) {
					// column Alert Date
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_NEW_AMOUNT_HEADER, request),
							SwtConstants.SWEEP_NEW_AMOUNT ,
							SwtConstants.COLUMN_TYPE_NUMBER, false, 3,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_NEW_AMOUNT )),
							false, "auth".equalsIgnoreCase(source)?true:false, hiddenColumnsMap
							.get(SwtConstants.SWEEP_NEW_AMOUNT )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.SWEEP_SEARCH_ENTITY_CR)) {
					// column Alert Date
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_ENTITY_CR_HEADER, request),
							SwtConstants.SWEEP_SEARCH_ENTITY_CR,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false, 4,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_SEARCH_ENTITY_CR)),
							false, "auth".equalsIgnoreCase(source)?true:false, hiddenColumnsMap
							.get(SwtConstants.SWEEP_SEARCH_ENTITY_CR )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				}else if (order.equals(SwtConstants.SWEEP_ACCOUNT_ID_CR)) {
					// column Alert Time
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_ACCOUNT_ID_CR_HEADER, request),
							SwtConstants.SWEEP_ACCOUNT_ID_CR,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false, 5,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_ACCOUNT_ID_CR)),
							false, "auth".equalsIgnoreCase(source)?true:false, hiddenColumnsMap
							.get(SwtConstants.SWEEP_ACCOUNT_ID_CR)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.ACCOUNT_CR_NAME )) {
					// column Alert Type
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ACCOUNT_CR_NAME_HEADER, request),
							SwtConstants.ACCOUNT_CR_NAME ,
							SwtConstants.COLUMN_TYPE_STRING, false, 7,
							Integer.parseInt(widths
									.get(SwtConstants.ACCOUNT_CR_NAME )),
							false, false, hiddenColumnsMap
							.get(SwtConstants.ACCOUNT_CR_NAME )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_SEARCH_ENTITY_DR)) {
					// column Risk Weight
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_ENTITY_DR_HEADER, request),
							SwtConstants.SWEEP_SEARCH_ENTITY_DR,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false,
							8,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_SEARCH_ENTITY_DR)),
							false,
							"auth".equalsIgnoreCase(source)?true:false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_SEARCH_ENTITY_DR)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_ACCOUNT_ID_DR)) {
					// column Account ID
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_ACCOUNT_ID_DR_HEADER, request),
							SwtConstants.SWEEP_ACCOUNT_ID_DR,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false,
							9,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_ACCOUNT_ID_DR)),
							false,
							"auth".equalsIgnoreCase(source)?true:false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_ACCOUNT_ID_DR)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.ACCOUNT_DR_NAME )) {
					// column Alert Type
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ACCOUNT_DR_NAME_HEADER, request),
							SwtConstants.ACCOUNT_DR_NAME ,
							SwtConstants.COLUMN_TYPE_STRING, false, 10,
							Integer.parseInt(widths
									.get(SwtConstants.ACCOUNT_DR_NAME )),
							false, false, hiddenColumnsMap
							.get(SwtConstants.ACCOUNT_DR_NAME )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_SWEEP_ID)) {
					// column Client ID
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_SWEEP_ID_HEADER, request),
							SwtConstants.SWEEP_SWEEP_ID,
							SwtConstants.COLUMN_TYPE_NUMBER, false,
							11,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_SWEEP_ID)),
							false,
							false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_SWEEP_ID)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_CRD_INT_MSG)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_CRD_INT_MSG_HEADER, request),
							SwtConstants.SWEEP_CRD_INT_MSG,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false,
							12,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_CRD_INT_MSG)),
							false,
							"auth".equalsIgnoreCase(source)?true:false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_CRD_INT_MSG)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_CRD_EXT_MSG)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_CRD_EXT_MSG_HEADER, request),
							SwtConstants.SWEEP_CRD_EXT_MSG,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false,
							13,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_CRD_EXT_MSG))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_CRD_EXT_MSG))
									: 125,
							false,
							"auth".equalsIgnoreCase(source)?true:false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_CRD_EXT_MSG)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				}else if (order
						.equals(SwtConstants.SWEEP_DR_INT_MSG)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_DR_INT_MSG_HEADER, request),
							SwtConstants.SWEEP_DR_INT_MSG,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false,
							14,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_DR_INT_MSG))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_DR_INT_MSG))
									: 125,
							false,
							"auth".equalsIgnoreCase(source)?true:false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_DR_INT_MSG)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				}
				else if (order
						.equals(SwtConstants.SWEEP_DR_EXT_MSG)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_DR_EXT_MSG_HEADER, request),
							SwtConstants.SWEEP_DR_EXT_MSG,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false,
							15,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_DR_EXT_MSG))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_DR_EXT_MSG))
									: 125,
							false,
							"auth".equalsIgnoreCase(source)?true:false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_DR_EXT_MSG)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				}	else if (order
						.equals(SwtConstants.SWEEP_TYPE)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_TYPE_HEADER, request),
							SwtConstants.SWEEP_TYPE,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false,
							16,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_TYPE))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_TYPE))
									: 125,
							false,
							"auth".equalsIgnoreCase(source)?true:false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_TYPE)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				}	else if (order
						.equals(SwtConstants.SWEEP_USER)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_USER_HEADER, request),
							SwtConstants.SWEEP_USER,
							SwtConstants.COLUMN_TYPE_STRING, false,
							17,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_USER))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_USER))
									: 125,
							false,
							false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_USER)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				}	else if (order
						.equals(SwtConstants.SWEEP_STATUS)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_STATUS_HEADER, request),
							SwtConstants.SWEEP_STATUS,
							SwtConstants.COLUMN_TYPE_STRING, false,
							18,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_STATUS))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_STATUS))
									: 95,
							false,
							false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_STATUS)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_STATUS1_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				}	else if (order
						.equals(SwtConstants.SWEEP_DATE_TIME_USER)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_DATE_TIME_USER_HEADER, request),
							SwtConstants.SWEEP_DATE_TIME_USER,
							SwtConstants.COLUMN_TYPE_STRING, false,
							19,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_DATE_TIME_USER))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_DATE_TIME_USER))
									: 230,
							false,
							false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_DATE_TIME_USER)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_CANCEL_PANEL, request):SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request));
					lstColumns.add(tmpColumnInfo);
				}


			}
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getGridColumns] method : - "
					+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getGridColumns", this.getClass());

		} finally {
			// Nullify objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;

			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ getGridColumns ] - Exit");
		}
		// return xml column
		return lstColumns;
	}


	private String getMsgDesc(String msg, HttpServletRequest request) {

		String finalMsg = "";
		// Regex pattern to extract the key and values
		Pattern pattern = Pattern.compile("\\{(\\w+\\.\\w+)=\\[\\w+\\.\\w+\\[(.*)\\]\\]\\}");
		Matcher matcher = pattern.matcher(msg);

		String key = null;
		ArrayList<String> values = new ArrayList<>();

		if (matcher.find()) {
			key = matcher.group(1);
			String valueString = matcher.group(2);
			// Split the values by comma and trim any leading/trailing spaces
			String[] valueArray = valueString.split(", ");
			for (String value : valueArray) {
				values.add(value.trim());
			}
		}

		finalMsg = SwtUtil.getMessage(key, request, values.get(0), values.get(1));
		return finalMsg;
	}


	public String nextAngular()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String currentFilter = "";
		String currentSort = "";
		String filterSortStatus = "";
		int currentPage = 0;
		int initialPageCount = 0;
		boolean isNext = false;
		int maxPage = 0;

		ArrayList<PageDetails> pageSummaryList = null;
		int clickedPage;
		String nextLinkStatus = null;
		String prevLinkStatus = null;
		Sweep sweep;
		String initialCurrencyCode = "";
		// Variable declaration for sweepDetailListAll
		ArrayList sweepDetailListAll = null;
		// Variable declaration for otherDetailListAll
		ArrayList otherDetailListAll = null;
		// Variable declaration for dyForm
		// DynaValidatorForm dyForm = null;
		// Variable declaration for session
		HttpSession session = null;
		// Variable declaration for currencyGrpAccess
		int currencyGrpAccess = 0;
		// Variable declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable declaration for entityAccessList
		Collection entityAccessList = null;
		// Variable declaration for entityUserAccess
		EntityUserAccess entityUserAccess = null;
		// Variable declaration for groupList
		Collection groupList = null;
		// Variable declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		// Variable declaration for acctType
		String acctType = null;
		// Variable declaration for currGrpId
		String currGrpId = null;
		// Variable declaration for roleId
		String roleId = null;
		// Variable declaration for entityId
		String entityId = null;
		// Variable declaration for roleObject
		RoleTO roleObject = null;
		// Variable declaration for itr
		Iterator itr = null;
		// Variable declaration for itGroupList
		Iterator itGroupList = null;
		// Variable declaration for systemFormats
		SystemFormats systemFormats = null;
		// Variable declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable declaration for temp
		ArrayList temp = null;
		// Variable declaration for tempItr
		Iterator tempItr = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		// To hold date format
		String dateFormat = null;
		// To hold system format
		SystemFormats sysFormat = null;

		try {
			log.debug(this.getClass().getName() + " - [next] - " + "Entry");
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			// ArrayLists added for Currency Group 'All' case
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();
			session = request.getSession();
			int totalCount = 0;
			/* Get all the filter values specified for all columns */
			currentFilter = (request.getParameter("selectedFilter") != null) ? request
					.getParameter("selectedFilter").trim().toString()
					: "all";

			/* Gets the sort column specified */
			currentSort = (request.getParameter("selectedSort") != null) ? request
					.getParameter("selectedSort").trim().toString()
					: "1|false";

			filterSortStatus = currentFilter + "#" + currentSort;

			/*
			 * Read the current page value and store it as integer in a integer
			 * variable
			 */
			currentPage = Integer.parseInt(((request
					.getParameter("currentPage") != null) && !request
					.getParameter("currentPage").equals("")) ? request
					.getParameter("currentPage").trim().toString() : "0");

			/*
			 * Read the clicked page value and store it as integer in a integer
			 * variable
			 */
			clickedPage = Integer
					.parseInt(((request.getParameter("goToPageNo") != null) && !request
							.getParameter("goToPageNo").equals("")) ? request
							.getParameter("goToPageNo").trim().toString() : "0");

			/*
			 * Read the maxpage value and store it as integer in a integer
			 * variable
			 */
			maxPage = Integer
					.parseInt(((request.getParameter("maxPages") != null) && !request
							.getParameter("maxPages").equals("")) ? request
							.getParameter("maxPages").trim().toString() : "0");


			/*
			 * Condition to check the clicked page.If Clicked page greater than
			 * 1 then previous link enable. If Clicked page less than 1 then
			 * previous link disable
			 */
			if (clickedPage > 1) {
				prevLinkStatus = "true";
			} else {
				prevLinkStatus = "false";
			}
			/*
			 * Condition to check the clicked page.If Clicked page less than
			 * maximum page then next link enable. If Clicked page greater than
			 * maximum page then next link disable
			 */

			if (clickedPage < maxPage) {
				nextLinkStatus = "true";
			} else {
				nextLinkStatus = "false";
			}

			systemFormats = new SystemFormats();

			// Read the selected filter from the request and store in String
			// variable
			currentFilter = request.getParameter("selectedFilter");
			// Read the selectedSort from the request and store in String
			// variable
			currentSort = request.getParameter("selectedSort");
			/* Condition to check currentFilter is null */
			if (currentFilter == null
					|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
				// If current filter is null the default value 'All' is set
				// for currentFilter
				currentFilter = "all";
			}

			/* Condition to check currentSort is null */
			if (currentSort == null
					|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
				/* If currentSort is null then set current sort as none. */
				currentSort = "none";
			}

			// Use # as separator for the combined current filter and
			// current sort
			filterSortStatus = currentFilter + "#" + currentSort;

			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));
			sweep = (Sweep) getSweepQueue();

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));

			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());
			sweep.setQueueName(SwtConstants.SWEEP_STATUS_CANCEL);
			acctType = request.getParameter("accountType");
			sweep.setEntityId(request.getParameter("entityId"));
			sweep.setCurrencyCode(request.getParameter("currGrpId"));
			sweep.setAccountType(acctType);
			if (sweep.getAccountType().equalsIgnoreCase("C")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CASH_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("U")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CUSTODIAN_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("All")) {
				request.setAttribute("AccountDesp",
						SwtConstants.ALL_ACCOUNTS_TYPE);
			}

			putEntityListInReq(request);

			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, sweep.getEntityId());

			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			currGrpId = "";
			roleId = getRoleId(request);
			entityId = sweep.getEntityId();

			session = request.getSession();
			if ((!entityId.equalsIgnoreCase(SwtUtil
					.getUserCurrentEntity(request.getSession())))
					&& sweep.getCurrencyCode().equalsIgnoreCase(
					((CommonDataManager) session
							.getAttribute(SwtConstants.CDM_BEAN))
							.getUser().getCurrentCcyGrpId())) {
				currGrpId = "All";

			} else {
				currGrpId = sweep.getCurrencyCode();
			}
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			initialCurrencyCode = currGrpId;
			// code added to handle Entity as All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				maxPage = 0;
				roleObject = new RoleTO(roleId);
				entityAccessList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				itr = entityAccessList.iterator();

				while (itr.hasNext()) {

					currGrpId = sweep.getCurrencyCode();
					entityUserAccess = (EntityUserAccess) itr.next();
					entityId = entityUserAccess.getEntityId();
					sweep.setEntityId(entityId);
					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}
					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);
						itGroupList = groupList.iterator();
						temp = new ArrayList();
						currGrpId = "";
						/*
						 * Start:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */

						// This while loop is for gennerating currecy group
						// access for cancel panel
						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access

							if (entityCurrencyGroupAccess.getAccess() == 0) {
								if (currGrpCounter > 0) {
									currGrpId += ",'"
											+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
								} else {
									currGrpId += "'"
											+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
								}
							}
							currGrpCounter++;
						}
						/*
						 * End:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */
						if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
							currGrpId = "''";
						}
						sweep.setCurrencyCode(currGrpId);
						// Intializing cancelViewCurrGrpId as empty
						cancelViewCurrGrpId = "";
						itGroupList = groupList.iterator();
						// This while loop is for gennerating currecy group
						// access for view panel
						while (itGroupList.hasNext()) {
							// If condition is for appending qouation and commas
							// for currency group access
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();

							if (itGroupList.hasNext())
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "',";
							else
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
						}
						if (cancelViewCurrGrpId
								.equals(SwtConstants.EMPTY_STRING)) {
							cancelViewCurrGrpId = "''";
						}
						sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());
						sweepQueueDetailVO.setOtherDetailList(new ArrayList());
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
						// Getting no of counts of recors from the mannager
						// class
						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
						sweep.setCurrencyCode("All");
					} else {
						temp = new ArrayList();
						sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
						sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
								+ "'");
						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setEntityId("All");
			} else {
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					currencyGrpAccess = 0;
				} else {
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				}
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					itGroupList = groupList.iterator();
					temp = new ArrayList();
					currGrpId = "";
					/*
					 * Start:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					currGrpCounter = 0;
					// This while loop is for gennerating currecy group
					// access for cancel panel
					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access

						if (entityCurrencyGroupAccess.getAccess() == 0) {
							if (currGrpCounter > 0) {
								currGrpId += ",'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
							} else {
								currGrpId += "'"
										+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
							}
							currGrpCounter++;
						}
					}
					/*
					 * End:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
						currGrpId = "''";
					}
					sweep.setCurrencyCode(currGrpId);
					// Intializing cancelViewCurrGrpId as empty
					cancelViewCurrGrpId = "";
					itGroupList = groupList.iterator();
					// This while loop is for gennerating currecy group
					// access for view panel
					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access

						if (itGroupList.hasNext())
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "',";
						else
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
									.getCurrencyGroupId() + "'";
					}
					if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
						cancelViewCurrGrpId = "''";
					}
					sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				} else {
					sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
					sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
							+ "'");
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE);
					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}
			sweep.setCurrencyCode(initialCurrencyCode);
			setSweepQueue(sweep);
			pageSummaryList = new ArrayList<PageDetails>();

			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}
			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE);
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);
//Angular part
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SWEEP_QUEUE);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntity", entityId);
			responseConstructor.createElement("defaultCcyGrp", currGrpId);
			responseConstructor.createElement("defaultAcctType", "All");
			responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			responseConstructor.createElement("accountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);

			responseConstructor.createElement("currentPage", Integer.toString(currentPage));
			responseConstructor.createElement("prevEnabled", "false");
			responseConstructor.createElement("nextEnabled", "" + isNext);
			responseConstructor.createElement("maxPage", Integer.toString(maxPage));
			responseConstructor.createElement("selectedFilter", currentFilter);
			responseConstructor.createElement("selectedSort", currentSort);
			responseConstructor.createElement("totalCount", totalCount);

			xmlWriter.endElement(SwtConstants.SINGLETONS);
			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entityList = putEntityListInReq(request);
			Iterator j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));
			/***** Entity Combo End ***********/

			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection currencyGrpList = putCurrencyGroupListInReqNew(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			j = currencyGrpList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyGrpList", lstOptions));
			/***** Currency Combo End ***********/


			/***** Account Type list Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection acctTypeList = putAccountListInReq(request, sweep.getEntityId());

			j = acctTypeList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getLabel(), row.getValue(), false));
			}
			lstSelect.add(new SelectInfo("acctTypeList", lstOptions));
			/***** Account list Combo End ***********/

			responseConstructor.formSelect(lstSelect);

			/******* AuthoriseQueueGrid ******/
			responseConstructor.formGridStart("authoriseQueueGrid");
			responseConstructor.formColumn(getGridColumns("auth", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalCount, pageSize),currentPage));

			responseConstructor.formRowsStart(totalCount);
			for (Iterator<Sweep> it = sweepDetailListAll.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser());
				}
				responseConstructor.formRowEnd();
			};

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* ViewQueueGrid ******/
			responseConstructor.formGridStart("viewQueueGrid");
			responseConstructor.formColumn(getGridColumns("view", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(otherDetailListAll.size());
			for (Iterator<Sweep> it = otherDetailListAll.iterator(); it.hasNext();) {
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
					responseConstructor.createRowElement(SwtConstants.SWEEP_STATUS, swp.getDisplayStatus());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DATE_TIME_USER, swp.getDisplayDateTimeUser());
				}
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.SWEEP_QUEUE);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [nextAngular()] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("cancel");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "next", SweepCancelQueueAction.class), request, "");

			return getView("fail");
		}
	}

	public String acctAccessConfirm() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Local variable declaration and initialization */
		String roleId=null;
		String hostId=null;
		String accountIdCr=null;
		String accountIdDr=null;
		String entityIdCr=null;
		String entityIdDr=null;
		String status =null;
		boolean acctCrFlag = false;
		boolean acctDrFlag = false;
		AccountAccess acctCrAccess = null;
		AccountAccess acctDrAccess = null;

		CacheManager cacheManagerInst=null;
		User userFromCDM =null;
		boolean acctCrAccessFlag = false;
		boolean acctDrAccessFlag = false;
		boolean flag = false;
		try {
			log.debug(this.getClass().getName() + " - [acctAccessConfirm] - " + "Entry");
			acctCrAccess = new AccountAccess();
			acctDrAccess = new AccountAccess();
			cacheManagerInst = CacheManager.getInstance();
			/*Read the current user from SwtUtil file*/
			userFromCDM = SwtUtil.getCurrentUser(request.getSession());
			/*Getting host id from cache manager file*/
			hostId = cacheManagerInst.getHostId();
			/*Getting role id using bean class*/
			roleId = userFromCDM.getRoleId();
			/*Getting account id,entity id,status from request*/
			accountIdCr = request.getParameter("accountIdCr");
			accountIdDr = request.getParameter("accountIdDr");
			status = request.getParameter("status");
			entityIdCr = request.getParameter("entityIdCr");
			entityIdDr = request.getParameter("entityIdDr");

			//Account CR
			/*Setting role id using bean class*/
			acctCrAccess.getId().setRoleId(roleId);
			/*Setting entity id using bean class*/
			acctCrAccess.getId().setEntityId(entityIdCr);
			/*Setting account id using bean class*/
			acctCrAccess.getId().setAccountId(accountIdCr);
			/*Setting host id using bean class*/
			acctCrAccess.getId().setHostId(hostId);
			AccountAccessManager accountAccessManager = (AccountAccessManager) SwtUtil
					.getBean("accountAccessManager");
			/*Checking role account access flag by calling manager file*/
			acctCrAccessFlag = accountAccessManager.getRoleAccessDetails(acctCrAccess);
			/*Checking account access by calling manager file*/
			if(acctCrAccessFlag){
				acctCrFlag = accountAccessManager.checkAcctAccess(acctCrAccess,status);
			}
			else{
				acctCrFlag = true;
			}


			//Account DR
			acctDrAccess.getId().setRoleId(roleId);
			/*Setting entity id using bean class*/
			acctDrAccess.getId().setEntityId(entityIdDr);
			/*Setting account id using bean class*/
			acctDrAccess.getId().setAccountId(accountIdDr);
			/*Setting host id using bean class*/
			acctDrAccess.getId().setHostId(hostId);
			/*Checking role account access flag by calling manager file*/
			acctDrAccessFlag = accountAccessManager.getRoleAccessDetails(acctDrAccess);
			/*Checking account access by calling manager file*/
			if(acctDrAccessFlag){
				acctDrFlag = accountAccessManager.checkAcctAccess(acctDrAccess,status);
			}
			else{
				acctDrFlag = true;
			}

			if(acctCrFlag && acctDrFlag){
				flag=true;
			}else{
				flag=false;
			}
			response.getWriter().print(flag);
			log.debug(this.getClass().getName() + " - [acctAccessConfirm] - " + "Exit");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [acctAccessConfirm] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp,request,"");
			return getView("fail");
		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "acctAccessConfirm", SweepCancelQueueAction.class), request, "");
		} finally {
		}

		return null;

	}
}
