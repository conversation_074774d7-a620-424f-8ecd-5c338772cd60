import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CommonService, SwtAlert, SwtButton, SwtCanvas, SwtCommonGrid, SwtLabel, SwtModule, SwtTextInput, SwtToolBoxModule, SwtUtil, SwtComboBox, SwtFieldSet, SwtCheckBox, ExternalInterface } from 'swt-tool-box';

@Component({
  selector: 'app-configure-params',
  templateUrl: './ConfigureParams.html',
  styleUrls: ['./ConfigureParams.css']
})
export class ConfigureParams extends SwtModule implements OnInit {


  @ViewChild('scenarioIdLbl') scenarioIdLbl: SwtLabel;
  @ViewChild('apiTypeLbl') apiTypeLbl: SwtLabel; 
  @ViewChild('scenarioIdtxt') scenarioIdtxt: SwtTextInput;
  @ViewChild('apiTypeCombo') apiTypeCombo: SwtComboBox;
  @ViewChild('scheduleCanvas') scheduleCanvas: SwtCanvas;
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('fieldSet') fieldSet: SwtFieldSet;
  @ViewChild('hostCheck') hostCheck: SwtCheckBox;
  @ViewChild('entityCheck') entityCheck: SwtCheckBox;
  @ViewChild('ccyCheck') ccyCheck: SwtCheckBox;
  @ViewChild('accountCheck') accountCheck: SwtCheckBox;
  @ViewChild('valDateCheck') valDateCheck: SwtCheckBox;
  @ViewChild('amountCheck') amountCheck: SwtCheckBox;
  @ViewChild('signCheck') signCheck: SwtCheckBox;
  @ViewChild('mvtCheck') mvtCheck: SwtCheckBox;
  @ViewChild('matchCheck') matchCheck: SwtCheckBox;
  @ViewChild('sweepCheck') sweepCheck: SwtCheckBox;
  @ViewChild('payCheck') payCheck: SwtCheckBox;
  @ViewChild('allCheck') allCheck: SwtCheckBox;
  @ViewChild('hostLbl') hostLbl: SwtLabel; 
  @ViewChild('entityLbl') entityLbl: SwtLabel; 
  @ViewChild('ccyLbl') ccyLbl: SwtLabel; 
  @ViewChild('accountLbl') accountLbl: SwtLabel; 
  @ViewChild('valDateLbl') valDateLbl: SwtLabel; 
  @ViewChild('amountLbl') amountLbl: SwtLabel; 
  @ViewChild('signLbl') signLbl: SwtLabel; 
  @ViewChild('mvtLbl') mvtLbl: SwtLabel; 
  @ViewChild('matchLbl') matchLbl: SwtLabel; 
  @ViewChild('sweepLbl') sweepLbl: SwtLabel; 
  @ViewChild('payLbl') payLbl: SwtLabel; 
  @ViewChild('allLbl') allLbl: SwtLabel; 

  
  private swtalert: SwtAlert;
  private listParams=[];
  private savedAttributes;
  private methodName;
  private columns=[];
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.scenarioIdLbl.text = SwtUtil.getPredictMessage('scenario.scenarioId', null);
    this.apiTypeLbl.text = SwtUtil.getPredictMessage('scenario.apiTypeLbl', null);
    this.hostLbl.text = SwtUtil.getPredictMessage('scenario.hostLbl', null);
    this.entityLbl.text = SwtUtil.getPredictMessage('scenario.entityLbl', null);
    this.ccyLbl.text = SwtUtil.getPredictMessage('scenario.ccyLbl', null);
    this.accountLbl.text = SwtUtil.getPredictMessage('scenario.accountLbl', null);
    this.valDateLbl.text = SwtUtil.getPredictMessage('scenario.valDateLbl', null);
    this.amountLbl.text = SwtUtil.getPredictMessage('scenario.amountLbl', null);
    this.signLbl.text = SwtUtil.getPredictMessage('scenario.signLbl', null);
    this.mvtLbl.text = SwtUtil.getPredictMessage('scenario.mvtLbl', null);
    this.matchLbl.text = SwtUtil.getPredictMessage('scenario.matchLbl', null);
    this.sweepLbl.text = SwtUtil.getPredictMessage('scenario.sweepLbl', null);
    this.payLbl.text = SwtUtil.getPredictMessage('scenario.payLbl', null);
    this.allLbl.text = SwtUtil.getPredictMessage('scenario.allLbl', null);
    this.cancelButton.label = SwtUtil.getPredictMessage("button.cancel", null);
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    this.fieldSet.legendText = SwtUtil.getPredictMessage('scenario.configFieldSet.legendText', null);
    //tooltips part
    this.scenarioIdtxt.toolTip = SwtUtil.getPredictMessage('scenario.scenarioId', null);
    this.apiTypeCombo.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.apiType', null);
    this.hostCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.hostId', null);
    this.entityCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.entityId', null);
    this.ccyCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.ccy', null);
    this.accountCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.accountId', null);
    this.valDateCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.valDate', null);
    this.amountCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.amount', null);
    this.signCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.sign', null);
    this.mvtCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.mvt', null);
    this.matchCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.match', null);
    this.sweepCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.sweep', null);
    this.payCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.payment', null);
    this.allCheck.toolTip = SwtUtil.getPredictMessage('scenario.tooltip.checkedAll', null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage("tooltip.cancelbutton", null);
    this.okButton.toolTip = SwtUtil.getPredictMessage('tooltip.ok', null);
  }

  onLoad(){
    let parentList:any;
    let apiListFromParent: any;
    if (window.opener && window.opener.instanceElement) {
      parentList = window.opener.instanceElement.sendGeneralDataToConfigScreen();
      apiListFromParent=parentList.apiTypeList.option;
      apiListFromParent=[apiListFromParent];
      this.apiTypeCombo.setComboData(apiListFromParent);
      this.apiTypeCombo.dataProvider=apiListFromParent;
      this.methodName=parentList.methodName;
      let savedApiAttributes=window.opener.instanceElement.savedApiParams;
      let checkedAttributes=window.opener.instanceElement.apiRequiredCols;
      this.scenarioIdtxt.text=parentList.scenarioId;
      this.savedAttributes=checkedAttributes.length>0?checkedAttributes.toString():savedApiAttributes;
      if(this.savedAttributes){
      this.populateCheckBoxes();
      }
  }

  }


  populateCheckBoxes() {
    this.columns = ((this.savedAttributes.replace("[","")).replace("]","")).split(",");
    if (this.columns.length == 11) {    
    this.checkAll();
    this.allCheck.selected=true;
    } else {
      for (let i = 0; i < this.columns.length; i++) {
        this.selectMapping(this.columns[i].replace(/"/g, ""));
      }
    }
  }

  selectMapping(col) {  
    switch (col) {
      case SwtUtil.getPredictMessage('scenario.hostLbl', null):
        this.hostCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.entityLbl', null):
        this.entityCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.ccyLbl', null):
        this.ccyCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.accountLbl', null):
        this.accountCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.valDateLbl', null):
        this.valDateCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.amountLbl', null):
        this.amountCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.signLbl', null):
        this.signCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.mvtLbl', null):
        this.mvtCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.matchLbl', null):
        this.matchCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.sweepLbl', null):
        this.sweepCheck.selected = true;
        break;
      case SwtUtil.getPredictMessage('scenario.payLbl', null):
        this.payCheck.selected = true;
        break;
      default:
        break;
    }

  }




  checkAll() {
    if (this.allCheck.selected == false) {
      this.hostCheck.selected = true;
      this.entityCheck.selected = true;
      this.ccyCheck.selected = true;
      this.accountCheck.selected = true;
      this.valDateCheck.selected = true;
      this.amountCheck.selected = true;
      this.signCheck.selected = true;
      this.mvtCheck.selected = true;
      this.matchCheck.selected = true;
      this.sweepCheck.selected = true;
      this.payCheck.selected = true;
    }else{
      this.hostCheck.selected = false;
      this.entityCheck.selected = false;
      this.ccyCheck.selected = false;
      this.accountCheck.selected = false;
      this.valDateCheck.selected = false;
      this.amountCheck.selected = false;
      this.signCheck.selected = false;
      this.mvtCheck.selected = false;
      this.matchCheck.selected = false;
      this.sweepCheck.selected = false;
      this.payCheck.selected = false; 
    }
  }
  
  cancelHandler() {
    ExternalInterface.call('close')
  }

  saveMandatoryAttributes(){
    this.listParams=[];
    if (this.hostCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.hostLbl', null));
    if (this.entityCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.entityLbl', null));
    if (this.ccyCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.ccyLbl', null));
    if (this.accountCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.accountLbl', null));
    if (this.valDateCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.valDateLbl', null));
    if (this.amountCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.amountLbl', null));
    if (this.signCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.signLbl', null));
    if (this.mvtCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.mvtLbl', null));
    if (this.matchCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.matchLbl', null));
    if (this.sweepCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.sweepLbl', null));
    if (this.payCheck.selected == true)
    this.listParams.push(SwtUtil.getPredictMessage('scenario.payLbl', null));
    if (window.opener && window.opener.instanceElement) {
      window.opener.instanceElement.apiRequiredCols= this.listParams;
      window.opener.instanceElement.CreateInsDesc.text= this.listParams.length>0? SwtUtil.getPredictMessage('scenario.CreateInsDescFull', null)+ "<"+this.listParams.toString()+">":SwtUtil.getPredictMessage('scenario.CreateInsDesc', null);
    }

    window.close();
  }

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ConfigureParams }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ConfigureParams],
  entryComponents: []
})
export class ConfigureParamsModule {}
