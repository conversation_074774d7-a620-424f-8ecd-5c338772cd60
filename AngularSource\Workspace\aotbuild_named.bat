REM please change PredictWebRoot and PredictAngularWorkspace before launching this batch file
REM PredictWebRoot : is the Predict WebRoot path
REM PredictAngularWorkspace : is the Angular Predict workspace path

call npm run build_aot_named
call createjsinclude.bat
Xcopy /E /I /d /Y C:\GitWorkspace\Angular1072.3\workspace\AngularSource\Workspace\dist\*.* C:\GitWorkspace\Predict1073Jacarta\java-migration\src\main\webapp\angularSources /EXCLUDE:exclude.txt
Xcopy /E /I /d /Y C:\GitWorkspace\Angular1072.3\workspace\AngularSource\Workspace\dist\*.* C:\GitWorkspace\Predict1073Jacarta\java-migration-ForWindsurf\src\main\webapp\angularSources /EXCLUDE:exclude.txt
Xcopy /E /I /d /Y C:\GitWorkspace\Angular1072.3\workspace\AngularSource\Workspace\dist\*.* C:\GitWorkspace\Predict1073Jacarta\java-migration+Angular\src\main\webapp\angularSources /EXCLUDE:exclude.txt

xcopy C:\GitWorkspace\Angular1072.3\workspace\AngularSource\Workspace\dist\*.*" "C:\GitWorkspace\Predict1073Jacarta\java-migration\src\main\webapp\angularSources\" /K /D /H /Y
xcopy C:\GitWorkspace\Angular1072.3\workspace\AngularSource\Workspace\dist\*.*" "C:\GitWorkspace\Predict1073Jacarta\java-migration-ForWindsurf\src\main\webapp\angularSources\" /K /D /H /Y
xcopy C:\GitWorkspace\Angular1072.3\workspace\AngularSource\Workspace\dist\*.*" "C:\GitWorkspace\Predict1073Jacarta\java-migration+Angular\src\main\webapp\angularSources\" /K /D /H /Y

REM Xcopy /E /I /d /Y "C:\GitWorkspace\Angular1072.3\workspace\AngularSource\Workspace\AngularSource\Workspace\dist\assets" "D:\1066\JAVA\WebRoot\assets"