import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, SwtAlert, CommonService, JSONReader, HTTPComms, SwtUtil, ExternalInterface, SwtLabel, SwtComboBox, SwtCanvas, SwtButton, SwtCommonGrid, SwtLoadingImage, Alert, ScreenVersion, SwtPopUpManager, ContextMenuItem, JSONViewer } from 'swt-tool-box';
declare var instanceElement: any;

@Component({
  selector: 'app-message-formats',
  templateUrl: './MessageFormats.html',
  styleUrls: ['./MessageFormats.css']
})
export class MessageFormats extends SwtModule implements OnInit {
  
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
   
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  
  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;

  @ViewChild("addButton") addButton: SwtButton;
  @ViewChild("changeButton") changeButton: SwtButton;
  @ViewChild("deleteButton") deleteButton: SwtButton;
  @ViewChild("viewButton") viewButton: SwtButton;

  @ViewChild("closeButton") closeButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  

  private actionPath;
  private actionMethod: string = "";
  private requestParams;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private mainGrid: SwtCommonGrid;
  private defaultEntity;
  private defaultAcctType;
  private menuAccessId;
  private queueName;
  private _invalidComms: string;
  public moduleId = "Predict";

  private errorLocation = 0;
  private menuAccess = "";
  private deleteData = new HTTPComms(this.commonService);
  public screenVersion = new ScreenVersion(this.commonService);
  private screenName = "Message Formats Maintenance";
  private versionDate = "08/08/2023";
  private versionNumber = "1";
  public lastReceivedJSON;
  private showJsonPopup = null;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.mainGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.mainGrid.allowMultipleSelection = true;
    this.entityLabel.text= SwtUtil.getPredictMessage('sweep.entity', null); 
    this.entityCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    
    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('tooltip.add', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('tooltip.change', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('tooltip.view', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('tooltip.delete', null);
    this.closeButton.label = SwtUtil.getPredictMessage('sweep.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');

  }

  onLoad(){
    this.requestParams = [];
    // this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    // this.queueName= ExternalInterface.call('eval', 'queueName');
    // if (this.menuAccessId) {
    //   if (this.menuAccessId !== "") {
    //     this.menuAccessId = Number(this.menuAccessId);
    //   }
    // }
    this.loadingImage.setVisible(false);

    if (this.menuAccess) {
      if (this.menuAccess !== "") {
        this.menuAccessId = Number(this.menuAccess);
      }
    }
    // set version number
    this.initializeMenus();
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "messageformats.do?";
    this.actionMethod = 'method=displayAngular';
    // this.requestParams['menuAccessId'] = this.menuAccessId;
    // this.requestParams['queueName'] = this.queueName;

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);


    this.mainGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    };


  }
  
  cellClickEventHandler(event): void {
    if(this.mainGrid.selectedIndex >=0){
    this.changeButton.enabled = true;
    this.changeButton.buttonMode = true;
    this.viewButton.enabled = true;
    this.viewButton.buttonMode = true;
    this.deleteButton.enabled = true;
    this.deleteButton.buttonMode = true;
    }else{
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false;
      this.viewButton.enabled = false;
      this.viewButton.buttonMode = false;
      this.deleteButton.enabled = false;
      this.deleteButton.buttonMode = false;
    }
  }



  inputDataResult(event): void {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) { 
          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.defaultEntity = this.jsonReader.getSingletons().defaultEntity; 
          this.entityCombo.selectedLabel = this.defaultEntity;

          this.defaultAcctType = this.jsonReader.getSingletons().accounttype; 
            if (this.defaultEntity != undefined)
            this.entityCombo.selectedLabel = this.defaultEntity;
            
          this.selectedEntity.text = this.entityCombo.selectedValue;

          if (!this.jsonReader.isDataBuilding()) {
            const obj = { columns: this.lastRecievedJSON.messageformatsList.grid.metadata.columns };
            //Message Format grid
            this.mainGrid.CustomGrid(obj);
            var gridRows = this.lastRecievedJSON.messageformatsList.grid.rows;
            if (gridRows.size > 0) {
              this.mainGrid.gridData = gridRows;
              this.mainGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              this.mainGrid.gridData = { size: 0, row: [] };
            }

            this.prevRecievedJSON = this.lastRecievedJSON;

          }
        }
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }
      }
    }
  }




  /*change data grid*/
  updateData(event): void {
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId'); 
    this.queueName= ExternalInterface.call('eval', 'queueName');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "messageformats.do?";
    this.actionMethod = 'method=displayAngular';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['queueName'] = this.queueName;
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['parentScreen'] = "";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);


    this.mainGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    };
  }


  getSelectedList(){  
    let selectedRows = this.mainGrid.selectedItems;
    var selectedList = "";
    for (let i=0; i < selectedRows.length; i++) 
    {
     if( selectedRows[i]){
     
          selectedList = selectedList + selectedRows[i].sweepId.content + ",";
       }
    }  		
    return selectedList;
   }

  closeHandler() {
    ExternalInterface.call("close");
  }
  
  doOpenChildWindow(methodeName) {
    if (methodeName == "add") {
      ExternalInterface.call("openChildWindow", methodeName,
        "", // Empty string for formatId
        this.jsonReader.getSingletons().defaultEntity,
        "", // Empty string for formatName
        "", // Empty string for formatType1
        "", // Empty string for fieldDelimeter
        "", // Empty string for hexaFldDelimeter
        "", // Empty string for msgSeparator
        "", // Empty string for hexaMsgSeparator
        this.entityCombo.selectedValue
      );
    } else {
      ExternalInterface.call("openChildWindow", methodeName,
        this.mainGrid.selectedItem.formatId.content,
        this.jsonReader.getSingletons().defaultEntity,
        this.mainGrid.selectedItem.formatName.content,
        this.mainGrid.selectedItem.formatType1.content,
        this.mainGrid.selectedItem.fieldDelimeter.content,
        this.mainGrid.selectedItem.hexaFldDelimeter.content,
        this.mainGrid.selectedItem.msgSeparator.content,
        this.mainGrid.selectedItem.hexaMsgSeparator.content,
        this.entityCombo.selectedValue
      );
    }
  }

  /**
     * doDeleteRule
     *
     * @param event: Event
     *
     * Method to pop up delete confirmation
     *
     */
  doDeleteAccount(): void {
    try {
      Alert.yesLabel = "Yes";
      Alert.noLabel = "No";
      const message = SwtUtil.getPredictMessage('confirm.delete');
      this.swtAlert.confirm(message, 'Alert', Alert.OK | Alert.CANCEL, null, this.deleteAccount.bind(this));
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'MessageFormat', 'doDeleteAccount', this.errorLocation);
    }
  }

 /**
   * deleteRule
   *
   * @param event:
   *
   * Method to remove selected Row
   */
  deleteAccount(event): void {
    try {
      // Condition to check Ok Button is selected
      if (event.detail === Alert.OK) {
         // result event
         this.deleteData.cbResult = (event) => {
          this.deleteDataResult(event);
        };
        this.deleteCategory();
      }
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'MessageFormat', 'deleteAccount', this.errorLocation);
    }
  }

/**
   * deleteCategory
   *
   * @param event:
   *
   * Method to remove selected Category
   */
deleteCategory(): void {
  try {
      this.requestParams =[];
      this.actionPath = "messageformats.do?";
      this.actionMethod = 'method=deleteAngular';
        this.requestParams["selectedEntityId"] = this.jsonReader.getSingletons().defaultEntity;
        this.requestParams["selectedFormatId"] = this.mainGrid.selectedItem.formatId.content;
        this.deleteData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.deleteData.send(this.requestParams);

  } catch (e) {
    SwtUtil.logError(e, this.moduleId, 'MessageFormat', 'deleteCategory', this.errorLocation);
  }
}

deleteDataResult(event): void {
  if (this.inputData.isBusy()) {
    this.inputData.cbStop();
  } else {

    // Parse result json
    this.jsonReader.setInputJSON(event);
    if (!this.jsonReader.getRequestReplyStatus()) {
      if ("DataIntegrityViolationExceptioninDelete" == this.jsonReader.getRequestReplyMessage()) {
        this.swtAlert.error(SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninDelete') + SwtUtil.getPredictMessage('alert.ContactSysAdm'), 'Error');
      } else {

        this.swtAlert.error('Error occurred, Please contact your System Administrator: \n'
          + this.jsonReader.getRequestReplyMessage(), 'Error');
      }
    } else {
      this.updateData(event);
    }
  }
}

  doHelp(): void {
    ExternalInterface.call("help");
  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

    /**
   * enablePrintButton
   *
   */
  enablePrintButton(value): void {
    this.printButton.enabled = value;
    this.printButton.buttonMode = value;
  }

   /**
   * printPage
   *
   * param event
   *
   * Method to get call the action to get reports
   */
  printPage(): void {
    let errorLocation = 0;
    try {
      ExternalInterface.call('printPage');

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", errorLocation);
    }
  }

  keyDownEventHandler(event){

  }
/**
     * The function initializes the menus in the right click event on the Entity Monitor screen.
     * The links are redirected to their respective pages.
     */
private initializeMenus(): void {
  this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.versionDate);
  let addMenuItem = new ContextMenuItem("Show JSON");
  // add the listener to addMenuItem
  addMenuItem.MenuItemSelect = this.showJSONSelect.bind(this);
  this.screenVersion.svContextMenu.customItems.push(addMenuItem);
  this.contextMenu = this.screenVersion.svContextMenu;
}
 /** This function is used to display the XML for the monitor selected in the monitor combo
     */
 showJSONSelect(event): void {

  this.showJsonPopup = SwtPopUpManager.createPopUp(this,
    JSONViewer,
    {
      jsonData: this.lastReceivedJSON,
    });
  this.showJsonPopup.width = "700";
  this.showJsonPopup.title = "Last Received JSON";
  this.showJsonPopup.height = "500";
  this.showJsonPopup.enableResize = false;
  this.showJsonPopup.showControls = true;
  this.showJsonPopup.display();
}
}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: MessageFormats }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [MessageFormats],
  entryComponents: []
})
export class MessageFormatsModule { }