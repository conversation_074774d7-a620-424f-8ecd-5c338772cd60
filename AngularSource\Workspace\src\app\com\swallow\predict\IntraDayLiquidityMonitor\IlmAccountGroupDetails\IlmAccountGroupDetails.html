<SwtModule (creationComplete)="onLoad()" width="100%" height="100%" >
  <VBox  height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas width="100%" height="95%">
      <VBox width="100%" height="100%" verticalGap="0">
        <VBox width="100%"  height="40%" verticalGap="0">
        <Grid width="100%"  height="100%">
          <GridRow height="10%" >
            <GridItem width="50%">
              <GridItem width="30%">
                <SwtLabel #lblEntity text="Entity"></SwtLabel>
              </GridItem>
              <GridItem width="70%">
                <SwtComboBox  #entityCombo
                              (change)="entityChangeCombo()"
                              dataLabel="entity" ></SwtComboBox>
                <SwtLabel #selectedEntity fontWeight="normal"></SwtLabel>
              </GridItem>
            </GridItem>
            <GridItem width="50%">
              <HBox>
              <SwtLabel #lblCreatedBy text="Created by" width="400" textAlign="right"></SwtLabel>
              <SwtLabel #createdByLabelText fontWeight="normal"></SwtLabel>
              </HBox>
            </GridItem>
          </GridRow>
          <GridRow height="10%" >
            <GridItem width="50%">
              <GridItem width="30%">
                <SwtLabel #lblCurrency text="Currency"></SwtLabel>
              </GridItem>
              <GridItem width="70%">
                <SwtComboBox #ccyCombo
                             (change)="entityChangeCombo()"
                             dataLabel="currency"></SwtComboBox>
                <SwtLabel #selectedCcy  fontWeight="normal"></SwtLabel>
              </GridItem>
            </GridItem>
            <GridItem width="50%">
              <HBox>
              <SwtLabel #lblOn text="On" width="400" textAlign="right"></SwtLabel>
              <SwtLabel #createdOnLabelText fontWeight="normal"></SwtLabel>
              </HBox></GridItem>

          </GridRow>
          <GridRow height="10%">
            <GridItem width="50%">
              <GridItem width="30%">
                <SwtLabel #lblGrpId text="Group ID"></SwtLabel>

              </GridItem>
              <GridItem width="70%">
                <SwtTextInput #groupIdTextInput
                              width="275"
                              maxChars="20">
                </SwtTextInput>
              </GridItem>
            </GridItem>
            <GridItem width="50%">
              <HBox>
              <SwtLabel #lblRep text="Allow Reporting" width="400" textAlign="right"></SwtLabel>
              <SwtCheckBox #allowReportingCheckBox (change)="enableDisableRNCPC_CorrBank($event)"></SwtCheckBox>
              </HBox>
            </GridItem>
          </GridRow>
          <GridRow height="10%">
            <GridItem width="50%">
              <GridItem width="30%">
                <SwtLabel #lblPub text="Public/Private" ></SwtLabel>
              </GridItem>
              <GridItem width="70%">
                <SwtRadioButtonGroup #publicprivate id="publicprivate" align="horizontal" height="28">
                  <SwtRadioItem #privateRadio id="privateRadio" value="PRIVATE" label="Private" selected="true"
                                groupName="publicprivate" width="80" >
                  </SwtRadioItem>
                  <SwtRadioItem #publicRadio id="publicRadio" width="80"  value="PUBLIC" label="Public" groupName="publicprivate" >
                  </SwtRadioItem>
                </SwtRadioButtonGroup>
              </GridItem>
            </GridItem>
            <GridItem width="50%">
                <HBox>
                  <SwtLabel #lblRepNet text="Create Net Cumulative Position Data" width="400" textAlign="right"></SwtLabel>
                  <SwtCheckBox #collectNetCumPosCheckBox ></SwtCheckBox> </HBox>
            </GridItem>
          </GridRow>


          <GridRow height="10%">
            <GridItem width="50%">
              <GridItem width="30%">
                <SwtLabel #lblType text="Type"></SwtLabel>
              </GridItem>
              <GridItem width="70%">
                <SwtRadioButtonGroup #type id="type" align="horizontal" height="28" (change)="radioTypeChangeHandler()">
                  <SwtRadioItem #typeFixed id="typeFixed" value="F" width="80"  label="Fixed" selected="true"
                                groupName="type" >
                  </SwtRadioItem>
                  <SwtRadioItem #typeDynamic id="typeDynamic"  value="D" width="80"  label="Dynamic" groupName="type">
                  </SwtRadioItem>
                </SwtRadioButtonGroup>
              </GridItem>
            </GridItem>
            <GridItem width="50%">
              <HBox>
              <SwtLabel #lblCorr text="Correspondent Bank" width="400" textAlign="right"></SwtLabel>
              <SwtCheckBox #correspondentBankCheckBox ></SwtCheckBox>
              </HBox>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="50%"></GridItem>
            <GridItem>
              <HBox>
                <SwtLabel textDictionaryId="ilmAccountGroupDetails.createThroughput"  width="400" textAlign="right"></SwtLabel>
                <SwtCheckBox #throughputCheckBox ></SwtCheckBox> </HBox>
            </GridItem>
          </GridRow>
          <GridRow height="10%">
            <GridItem width="15%">
              <SwtLabel #lblName text="Name" ></SwtLabel>
            </GridItem>
            <GridItem width="85%">
              <SwtTextInput #groupName maxChars="50" width="100%"></SwtTextInput>
            </GridItem>
          </GridRow>
          <GridRow height="20%">
            <GridItem width="15%">
              <SwtLabel #lblDesc text="Description"  marginRight="8" ></SwtLabel>
            </GridItem>
            <GridItem width="85%">
              <SwtTextArea #groupDescription  maxChars="100" height="40" width="100%"></SwtTextArea>

            </GridItem>
          </GridRow>
          <GridRow height="20%">
            <GridItem width="50%">
              <VBox width="100%">
               <GridItem width="100%">
                 <GridItem width="43%">
                   <SwtLabel #lblDef text="Default Legend Text"></SwtLabel>
                 </GridItem>
                 <GridItem width="57%">
                   <SwtRadioButtonGroup #idName id="idName" align="horizontal" height="28">
                     <SwtRadioItem #idLegend id="idLegend" value="I" label="ID" selected="true" width="80"
                                   groupName="idName" >
                     </SwtRadioItem>
                     <SwtRadioItem #nameLegend id="nameLegend"  value="N" label="Name" groupName="idName"  width="80" >
                     </SwtRadioItem>
                   </SwtRadioButtonGroup>
                 </GridItem>
               </GridItem>
                <GridItem width="100%">
                  <GridItem width="43%">
                    <SwtLabel #lblAgent text="Main Agent" ></SwtLabel>
                  </GridItem>
                  <GridItem width="57%">
                    <SwtTextInput #mainAgentText width="275"  maxChars="30" ></SwtTextInput>
                  </GridItem>
                </GridItem>
              </VBox>
            </GridItem>
            <GridItem width="50%"></GridItem>
           <SwtFieldSet style="height: 88%; width: 100%" legendText="Throughput Ratio">
             <VBox width="100%" height="100%" verticalGap="0">
               <GridItem height="60%">
                 <SwtLabel textDictionaryId="ilmAccountGroupDetails.first"  width="30%"></SwtLabel>
                 <SwtLabel textDictionaryId="ilmAccountGroupDetails.time" width="10%" textAlign="right" paddingRight="10"></SwtLabel>
                 <SwtTextInput #startTime height="20" width="15%" maxChars="5" (focusOut)="validateTime(startTime)"></SwtTextInput>
                 <SwtLabel width="30%" textDictionaryId="ilmAccountGroupDetails.target" textAlign="right" paddingRight="10"></SwtLabel>
                 <SwtNumericInput #startTarget height="20" textAlign="right" width="10%" minimum="0" maximum="100" maxChars="3"></SwtNumericInput>
               </GridItem>
               <GridItem >
                 <SwtLabel textDictionaryId="ilmAccountGroupDetails.second" width="40%"></SwtLabel>
                 <SwtTextInput #endTime height="20" width="15%" maxChars="5" (focusOut)="validateTime(endTime)"></SwtTextInput>
                 <GridItem width="30%"></GridItem>
                 <SwtNumericInput #endTarget textAlign="right" height="20"  width="10%"minimum="0" maximum="100" maxChars="3"></SwtNumericInput>
               </GridItem>
             </VBox>
           </SwtFieldSet>
          </GridRow>
        </Grid>
        </VBox>
          <SwtFieldSet style="height:11%" legendText="Absolute Thresholds">
            <Grid width="100%">
              <GridRow>
                <GridItem width="50%">
                  <GridItem width="29%">
                    <SwtLabel #lblFistMin text="First Minimum" ></SwtLabel>
                  </GridItem>
                <GridItem width="70%">
                  <SwtTextInput #firstMinimum width="180" id="firstMinimum" restrict="0-9.,-bBtTmM" (focusOut)="formatAmount(firstMinimum)" ></SwtTextInput>
                </GridItem>
                </GridItem>
                <GridItem width="50%">
                  <GridItem width="29%">
                    <SwtLabel #lblFirstMax text="First Maximum"></SwtLabel>

                  </GridItem>
                  <GridItem width="70%">
                    <SwtTextInput #firstMaximum id="firstMaximum" width="180" restrict="0-9.,-bBtTmM" (focusOut)="formatAmount(firstMaximum)" ></SwtTextInput>

                  </GridItem>
                </GridItem>
              </GridRow>
              <GridRow>
                <GridItem width="50%">
                  <GridItem width="29%">
                    <SwtLabel #lblSecMin text="Second Minimum" ></SwtLabel>
                  </GridItem>
                  <GridItem width="70%">
                    <SwtTextInput #secondMinimum width="180" id="secondMinimum" restrict="0-9.,-bBtTmM" (focusOut)="formatAmount(secondMinimum)" ></SwtTextInput>
                  </GridItem>
                </GridItem>
                <GridItem width="50%">
                  <GridItem width="29%">
                    <SwtLabel #lblSecMax text="Second Maximum" ></SwtLabel>
                  </GridItem>
                  <GridItem width="70%">
                    <SwtTextInput #secondMaximum id="secondMaximum" width="180" restrict="0-9.,-bBtTmM" (focusOut)="formatAmount(secondMaximum)" ></SwtTextInput>
                  </GridItem>
                </GridItem>
              </GridRow>
            </Grid>
          </SwtFieldSet>

          <SwtFieldSet style="height:7%" legendText="Net Cumulative Position Thresholds">
            <Grid width="100%">
              <GridRow>
                <GridItem width="50%">
                  <GridItem width="29%">
                    <SwtLabel #lblMinNet text="Minimum"></SwtLabel>
                  </GridItem>
                  <GridItem width="70%">
                    <SwtTextInput #netMinimum id="netMinimum" width="180" restrict="0-9.,-bBtTmM" (focusOut)="formatAmount(netMinimum)" ></SwtTextInput>
                  </GridItem>
                </GridItem>
                <GridItem width="50%">
                  <GridItem width="29%">
                    <SwtLabel #lblMaxNet text="Maximum" ></SwtLabel>
                  </GridItem>
                  <GridItem width="70%">
                    <SwtTextInput #netMaximum id="netMaximum" width="180" restrict="0-9.,-bBtTmM" (focusOut)="formatAmount(netMaximum)" ></SwtTextInput>
                  </GridItem>
                </GridItem>
              </GridRow>
            </Grid>
          </SwtFieldSet>
        <Grid height="10%" paddingTop="5">
          <GridRow>
            <GridItem width="15%">
              <SwtLabel #lblCond text="Filter Condition:"></SwtLabel>
            </GridItem>
            <GridItem width="85%">
              <SwtTextArea #filterCondition height="70" enabled="false" width="100%" ></SwtTextArea>
            </GridItem>
          </GridRow>
        </Grid>

        <SwtFieldSet style="height:37%" legendText="Group member accounts">

        <HBox width="100%" height="100%" paddingLeft="10">
          <HBox width="45%" height="100%">
          <VBox width="100%" height="100%" verticalGap="0">
            <HBox width="100%" height="8%">
              <SwtLabel #accountNotInThisGroupLabel ></SwtLabel>
              <SwtLabel #accountNotInThisGroupTextLabel></SwtLabel>
            </HBox>
            <HBox width="100%" height="62%">
              <SwtCanvas #otherGridCanvas height="100%" width="100%"></SwtCanvas>
            </HBox>
            <VBox width="100%" height="20%" paddingTop="10" verticalGap="0">
              <HBox width="100%">
                <SwtLabel text="Quick Search" #quickSearchLabel width="29%">
                </SwtLabel>
                <SwtTextInput width="70%" #leftGridQuickSearch (change)="filtringLeftGrid(leftGridQuickSearch.text)" (focusOut)="refreshLeftHeader()"></SwtTextInput>
              </HBox>
              <HBox width="100%">
                <SwtLabel #lblListFromGrp text="List from group"  width="29%">
                </SwtLabel>
                <SwtComboBox #secondGridGroupsCombo (change)="leftGridGroupComboChange()" dataLabel="secondgridgroups" width="277" shiftUp="100"></SwtComboBox>
              </HBox>
            </VBox>

          </VBox>
          </HBox>
          <HBox width="10%" height="100%" >
          <VBox #buttonsContainer width="100%" height="76%" horizontalAlign="center" verticalAlign="middle">
            <SwtButton id="buttonMoveRight"
                       #buttonMoveRight
                       (click)="moveToRight($event, false)"
                       enabled="false"
                       width="40"
                       label=">"></SwtButton>
            <SwtButton id="buttonMoveAllRight"
                       #buttonMoveAllRight
                       (click)="moveToRight($event, true)"
                       enabled="false"
                       width="40"
                       label=">>"></SwtButton>
            <SwtButton id="buttonMoveLeft"
                       #buttonMoveLeft
                       (click)="moveToLeft($event, false)"
                       enabled="false"
                       width="40"
                       label="<"></SwtButton>

            <SwtButton id="buttonMoveAllLeft"
                       #buttonMoveAllLeft
                       (click)="moveToLeft($event, true)"
                       enabled="false"
                       width="40"
                       label="<<"></SwtButton>
          </VBox>
          </HBox>
          <HBox width="45%" height="100%">
          <VBox width="100%" height="100%" verticalGap="0">
            <HBox width="100%" height="8%">
              <SwtLabel #accountInThisGroupLabel></SwtLabel>
              <SwtLabel #accountInThisGroupTextLabel ></SwtLabel>
            </HBox>
            <HBox width="100%" height="62%">
              <SwtCanvas  #ownGridCanvas height="100%" width="100%"></SwtCanvas>
            </HBox>
            <HBox width="100%" height="20%" paddingTop="10">
              <SwtLabel text="Quick Search" #quickSearchLabel2 width="30%">
              </SwtLabel>
              <SwtTextInput width="70%" #rightGridQuickSearch (change)="filtringRightGrid(rightGridQuickSearch.text)" (focusOut)="refreshRightHeader()"></SwtTextInput>
            </HBox>
          </VBox>
          </HBox>
        </HBox>
       </SwtFieldSet>
      </VBox>
    </SwtCanvas>
    <SwtCanvas width="100%" height="5%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="80%">
          <SwtButton [buttonMode]="true"
                     #saveButton
                     label="Save"
                     (click)="saveButton_clickHandler($event)"></SwtButton>
          <SwtButton #cancelButton
                     label="Cancel"
                     (click)="closeHandler()"></SwtButton>
          <SwtButton #testButton
                     label="Test"
                     enabled="false"
                     (click)="filterConditionValidation()"></SwtButton>

        </HBox>
        <HBox horizontalAlign="right" top="4" width="20%">
          <DataExport #dataExport id="dataExport"></DataExport>
          <SwtHelpButton id="helpIcon"
                         #helpIcon
                         (click)="doHelp()"></SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
