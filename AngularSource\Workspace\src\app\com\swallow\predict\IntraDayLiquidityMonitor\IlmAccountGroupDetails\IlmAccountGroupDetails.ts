
import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService,
  ExternalInterface,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCommonGrid,
  SwtLoadingImage,
  SwtModule,
  SwtUtil,
  SwtToolBoxModule,
  SwtLabel,
  SwtTextInput,
  SwtComboBox,
  SwtCheckBox,
  SwtRadioButtonGroup,
  SwtRadioItem,
  SwtTextArea,
  Encryptor,
  StringUtils,
  VBox,
  SwtDataExport,
  ExportEvent,
  SwtNumericInput,
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";
import moment from "moment";
declare  function validateFormatTime(strField): any;
@Component({
  selector: 'app-ilm-account-group-details',
  templateUrl: './IlmAccountGroupDetails.html',
  styleUrls: ['./IlmAccountGroupDetails.css']
})
export class IlmAccountGroupDetails extends SwtModule {

  @ViewChild('buttonsContainer') buttonsContainer: VBox;
  @ViewChild('lblEntity') lblEntity: SwtLabel;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('lblCurrency') lblCurrency: SwtLabel;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('lblGrpId') lblGrpId: SwtLabel;
  @ViewChild('groupIdTextInput') groupIdTextInput: SwtTextInput;
  @ViewChild('startTime') startTime: SwtTextInput;
  @ViewChild('endTime') endTime: SwtTextInput;
  @ViewChild('startTarget') startTarget: SwtNumericInput;
  @ViewChild('endTarget') endTarget: SwtNumericInput;
  @ViewChild('lblPub') lblPub: SwtLabel;
  @ViewChild('publicprivate') publicprivate: SwtRadioButtonGroup;
  @ViewChild('privateRadio') privateRadio: SwtRadioItem;
  @ViewChild('publicRadio') publicRadio: SwtRadioItem;
  @ViewChild('lblType') lblType: SwtLabel;
  @ViewChild('type') type: SwtRadioButtonGroup;
  @ViewChild('typeFixed') typeFixed: SwtRadioItem;
  @ViewChild('typeDynamic') typeDynamic: SwtRadioItem;
  @ViewChild('lblCreatedBy') lblCreatedBy: SwtLabel;
  @ViewChild('createdByLabelText') createdByLabelText: SwtLabel;
  @ViewChild('lblOn') lblOn: SwtLabel;
  @ViewChild('createdOnLabelText') createdOnLabelText: SwtLabel;
  @ViewChild('lblRep') lblRep: SwtLabel;
  @ViewChild('allowReportingCheckBox') allowReportingCheckBox: SwtCheckBox;
  @ViewChild('lblRepNet') lblRepNet: SwtLabel;
  @ViewChild('collectNetCumPosCheckBox') collectNetCumPosCheckBox: SwtCheckBox;
  @ViewChild('lblCorr') lblCorr: SwtLabel;
  @ViewChild('correspondentBankCheckBox') correspondentBankCheckBox: SwtCheckBox;
  @ViewChild('throughputCheckBox') throughputCheckBox: SwtCheckBox;
  @ViewChild('lblName') lblName: SwtLabel;
  @ViewChild('groupName') groupName: SwtTextInput;
  @ViewChild('lblDesc') lblDesc: SwtLabel;
  @ViewChild('groupDescription') groupDescription: SwtTextArea;
  @ViewChild('lblDef') lblDef: SwtLabel;
  @ViewChild('idName') idName: SwtRadioButtonGroup;
  @ViewChild('nameLegend') nameLegend: SwtRadioItem;
  @ViewChild('idLegend') idLegend: SwtRadioItem;
  @ViewChild('lblAgent') lblAgent: SwtLabel;
  @ViewChild('mainAgentText') mainAgentText: SwtTextInput;
  @ViewChild('lblFistMin') lblFistMin: SwtLabel;
  @ViewChild('firstMinimum') firstMinimum: SwtTextInput;
  @ViewChild('lblSecMin') lblSecMin: SwtLabel;
  @ViewChild('secondMinimum') secondMinimum: SwtTextInput;
  @ViewChild('lblFirstMax') lblFirstMax: SwtLabel;
  @ViewChild('firstMaximum') firstMaximum: SwtTextInput;
  @ViewChild('lblSecMax') lblSecMax: SwtLabel;
  @ViewChild('secondMaximum') secondMaximum: SwtTextInput;
  @ViewChild('lblMinNet') lblMinNet: SwtLabel;
  @ViewChild('netMinimum') netMinimum: SwtTextInput;
  @ViewChild('lblMaxNet') lblMaxNet: SwtLabel;
  @ViewChild('netMaximum') netMaximum: SwtTextInput;
  @ViewChild('lblCond') lblCond: SwtLabel;
  @ViewChild('filterCondition') filterCondition: SwtTextArea;
  @ViewChild('accountNotInThisGroupLabel') accountNotInThisGroupLabel: SwtLabel;
  @ViewChild('accountNotInThisGroupTextLabel') accountNotInThisGroupTextLabel: SwtLabel;
  @ViewChild('otherGridCanvas') otherGridCanvas: SwtCanvas;
  @ViewChild('quickSearchLabel') quickSearchLabel: SwtLabel;
  @ViewChild('leftGridQuickSearch') leftGridQuickSearch: SwtTextInput;
  @ViewChild('lblListFromGrp') lblListFromGrp: SwtLabel;
  @ViewChild('secondGridGroupsCombo') secondGridGroupsCombo: SwtComboBox;
  @ViewChild('buttonMoveRight') buttonMoveRight: SwtButton;
  @ViewChild('buttonMoveAllRight') buttonMoveAllRight: SwtButton;
  @ViewChild('buttonMoveLeft') buttonMoveLeft: SwtButton;
  @ViewChild('buttonMoveAllLeft') buttonMoveAllLeft: SwtButton;
  @ViewChild('accountInThisGroupLabel') accountInThisGroupLabel: SwtLabel;
  @ViewChild('accountInThisGroupTextLabel') accountInThisGroupTextLabel: SwtLabel;
  @ViewChild('ownGridCanvas') ownGridCanvas: SwtCanvas;
  @ViewChild('quickSearchLabel2') quickSearchLabel2: SwtLabel;
  @ViewChild('rightGridQuickSearch') rightGridQuickSearch: SwtTextInput;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('testButton') testButton: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('dataExport') dataExport: SwtDataExport;


  /**
   * Constant Objects
   * */
  private  ALL_STRING: string="All";
  private  ASCII_RESTRICT_PATTERN: string="A-Za-z0-9\d_ !\"#$%&'()*+,\-.\/:;<=>?@[\\\]^`{|}~";
  private  ACCOUNT_NAME_RESTRICT_PATTERN: string="a-zA-Z0-9\d .,:;#\(\)\*\?\[\]%>_+=^\|\\+/";
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public jsonReader2: JSONReader = new JSONReader();
  public lastRecievedJSON2;
  public prevRecievedJSON2;
  private rightGrid:SwtCommonGrid;
  private leftGrid:SwtCommonGrid;
  private waitingRefreshLeft = false;
  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private saveData = new HTTPComms(this.commonService);
  private baseURL:string=  SwtUtil.getBaseURL();
  private actionMethod:string="";
  private actionPath:string="";
  private requestParams = [];
  private selectedAccountGroup: string;
  private accountInGroupToDelete = [];
  private accountInGroupToAdd = [];
  private accountAlreadyInGroup = [];
  private methodName:string=null;
  private entityId:string=null;
  private currencyCode:string=null;
  private alreadyOpened:Boolean=false;
  private description :string=null;
  private groupDefaultName:string=null;
  private filterConditionText:string=null;
  public leftTextOfInput: string = "";
  public rightTextOfInput:string = "";
  private allowReportingOnLoad: boolean = false;
  private isChangedQuickSearchLeft  = false;
  private isChangedQuickSearchRight  = false;

  private swtAlert: SwtAlert;

  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.leftGrid = <SwtCommonGrid> this.otherGridCanvas.addChild(SwtCommonGrid);
    this.rightGrid = <SwtCommonGrid> this.ownGridCanvas.addChild(SwtCommonGrid);
    this.accountInThisGroupLabel.text = ExternalInterface.call('getBundle', 'text', 'label-accntInGrp', 'Accounts in this group');
    this.accountNotInThisGroupLabel.text = ExternalInterface.call('getBundle', 'text', 'label-accntNotInGrp', 'Accounts not in this group');
    this.saveButton.toolTip = ExternalInterface.call('getBundle', 'text', 'button-save', 'Save');
    this.cancelButton.toolTip = ExternalInterface.call('getBundle', 'text', 'button-cancel', 'Save');
    this.testButton.toolTip = ExternalInterface.call('getBundle', 'text', 'button-test', 'Save');
    this.secondGridGroupsCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'label-listFromGrp', 'Select an account group');
    this.leftGridQuickSearch.toolTip = ExternalInterface.call('getBundle','tip','label-quickSearch','Quick Search an account id');
    this.filterCondition.toolTip = ExternalInterface.call('getBundle','tip','label-filterCondition','Specify a filter condition to identify the accounts which are to be members of a dynamic account');
    this.netMaximum.toolTip = ExternalInterface.call('getBundle','tip','label-Max','Maximum net cumulative positions threshold (must be positive)');
    this.netMinimum.toolTip = ExternalInterface.call('getBundle','tip','label-Min','Minimum net cumulative positions threshold (must be negative)');
    this.secondMaximum.toolTip = ExternalInterface.call('getBundle','tip','label-secondMax','');
    this.secondMinimum.toolTip = ExternalInterface.call('getBundle','tip','label-secondMin','Enter Second Minimum');
    this.firstMaximum.toolTip = ExternalInterface.call('getBundle','tip','label-firstMax','Enter First Maximum');
    this.firstMinimum.toolTip = ExternalInterface.call('getBundle','tip','label-firstMin','Enter First Minimum');
    this.mainAgentText.toolTip = ExternalInterface.call('getBundle','tip','label-mainAgent','Enter main agent');
    this.nameLegend.toolTip = ExternalInterface.call('getBundle','tip','label-legendText','Define default legend text for the liquidity monitor screen');
    this.idLegend.toolTip = ExternalInterface.call('getBundle','tip','label-legendText','Define default legend text for the liquidity monitor screen');
    this.groupDescription.toolTip = ExternalInterface.call('getBundle','tip','label-description','Enter Description');
    this.groupName.toolTip = ExternalInterface.call('getBundle','tip','label-name','Enter group name');
    this.correspondentBankCheckBox.toolTip =ExternalInterface.call('getBundle','tip','label-correspBank','When checked, this group will be selectable as a correspondent' +
      ' bank for the purpose of Basel reporting');
    this.collectNetCumPosCheckBox.toolTip = ExternalInterface.call('getBundle','tip','label-netCum','When checked, the ILM group report will include a chart ' +
      'of net cumulative position..  This data could grow to be large so only specify it on accounts where the feature is necessary.' +
      '  Data will be stored with 15 minute timeslot intervals');
    this.allowReportingCheckBox.toolTip = ExternalInterface.call('getBundle','tip','label-allowReporting','When checked this group will have ILM report ' +
      'data built on a daily basis and will be selectable for ILM reporting');
    this.typeDynamic.toolTip = ExternalInterface.call('getBundle','tip','label-type','Specify if the group will contain a fixed list of accounts, or has dynamic content determined by selection filters applied to the account table');
    this.typeFixed.toolTip = ExternalInterface.call('getBundle','tip','label-type','Specify if the group will contain a fixed list of accounts, or has dynamic content determined by selection filters applied to the account table');
    this.publicRadio.toolTip= this.privateRadio.toolTip =  ExternalInterface.call('getBundle','tip','label-pubPriv','Specify if group is public and available to all users or private and only available to the creator of the group');
    this.groupIdTextInput.toolTip = ExternalInterface.call('getBundle','tip','label-groupId','Enter a group Id');
    this.ccyCombo.toolTip = ExternalInterface.call('getBundle','tip','label-currency','Select currency code');
    this.entityCombo.toolTip = ExternalInterface.call('getBundle','tip','label-entity','Select an entity ID');
    
  }
  onLoad() {

    this.leftGrid.onFilterChanged = this.updateFilter.bind(this);
    this.rightGrid.onFilterChanged = this.updateFilterRight.bind(this);
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    }
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    this.selectedAccountGroup=ExternalInterface.call('eval', 'selectedAccountGroup');
    this.entityId=ExternalInterface.call('eval', 'entityId');
    this.currencyCode=ExternalInterface.call('eval', 'currencyCode');
    this.description=Encryptor.decode64(ExternalInterface.call('eval','description'));
    this.groupDefaultName=ExternalInterface.call('eval','groupDefaultName');
    this.filterConditionText= Encryptor.decode64(ExternalInterface.call('eval','filter'));
    this.methodName=ExternalInterface.call('eval', 'methodName');
    this.typeDynamic.enabled=false;
    this.typeFixed.enabled=false;
    this.publicRadio.enabled=false;
    this.privateRadio.enabled=false;
    this.idLegend.enabled = false;
    this.nameLegend.enabled = false;
    this.disableMoveButtons();
    switch(this.methodName)
    {
      case "view":
      {
        this.dataExport.enabled=true;
        break;
      }
      case "add":
      {
        this.createdByLabelText.text=ExternalInterface.call('eval', 'createdBy');
        this.createdOnLabelText.text=ExternalInterface.call('eval', 'createdOn');
        this.dataExport.enabled =false;
        break;
      }
      case "change":
      {
        this.dataExport.enabled =false;
        break;
      }
      case "addfromILM":
      {
        this.createdByLabelText.text=ExternalInterface.call('eval', 'createdBy');
        this.createdOnLabelText.text=ExternalInterface.call('eval', 'createdOn');
        this.dataExport.enabled =false;
      }
    }
    /*Define the action to send the request*/
    this.actionPath="intraDayLiquidity.do?";

    this.actionMethod="";
    this.rightGrid.onRowClick = (event)=> {
      this.cellLogic(event);
    };
    this.leftGrid.onRowClick = (event)=> {
      this.cellLogic(event);
    };
    this.actionMethod="method=getAccountGroupDetails";
    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;

    //set menuAccessId in params
    this.requestParams["selectedAccountGroup"] = this.selectedAccountGroup;
    this.requestParams["entityId"] = this.entityId;
    this.requestParams["currencyCode"] = this.currencyCode;
    this.requestParams["description"]= this.description ;
    this.requestParams["groupDefaultName"]= this.groupDefaultName;
    this.groupName.restrict = this.ASCII_RESTRICT_PATTERN;
    //TODO
   // this.groupDescription.restrict = this.ASCII_RESTRICT_PATTERN;
    //Make initial request
    this.inputData.send(this.requestParams);
    //groupIdTextInput.addEventListener(ToolTipEvent.TOOL_TIP_SHOWN, textInput_toolTipShown);
    ExportEvent.subscribe((type) => {
      this.report(type)
    });
  }
  inputDataFault(): void {
    this.swtAlert.error('alert.generic_exception');

  }
  cellLogic(event:Event):void
  {	 if (this.methodName != "view")
    this.enableMoveButtons();

  }

  /** This function calls while changing the amount
   *
   */
   formatAmount(textAmount:SwtTextInput):void {

    let beforeFormat:string = null;
    let afterFormat: string = null;
    let currencyFormat : string=  ExternalInterface.call('eval','ccyPattern');
    try
    {

      beforeFormat = textAmount.text.toString();
      if (beforeFormat != null && beforeFormat != "")
      {
        beforeFormat = StringUtils.unformatAmount(beforeFormat,Number(currencyFormat));
        afterFormat = StringUtils.formatAmount(beforeFormat, Number(2),Number(currencyFormat));

       if(afterFormat != 'NaN') {
         textAmount.text=afterFormat;
        if ((beforeFormat != null || beforeFormat != "") && afterFormat == "")
        {
         this.swtAlert.warning(ExternalInterface.call('getBundle','text','label-invalidAmountAlert','Please enter a valid amount'),
           ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'),
           Alert.OK ,null, (data) =>{
             this.setFocusField(data,textAmount)
           })


        }
        else if (textAmount.id == this.netMinimum.id && afterFormat.toString().indexOf("-") == -1)
        {
          this.swtAlert.warning(ExternalInterface.call('getBundle','text','alert-netMinimum','The minimum net cumulative positions threshold must be negative'),ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'), Alert.OK,null, (data) =>{
          this.setFocusField(data,textAmount)
        });
        }
        else if (textAmount.id == this.netMaximum.id && afterFormat.toString().indexOf("-") != -1)
        {
          this.swtAlert.warning(ExternalInterface.call('getBundle','text','alert-netMaximum','The maximum net cumulative positions threshold must be positive'),ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'), Alert.OK, null,(data) =>{
          this.setFocusField(data,textAmount)
        });
        }
        }
        else {
          textAmount.text= '';
          this.swtAlert.warning(ExternalInterface.call('getBundle','text','label-invalidAmountAlert','Please enter a valid amount'),ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'),  Alert.OK,null, (data) =>{
           this.setFocusField(data,textAmount)
         });

        }

     }
    }

    catch (error) {
      this.swtAlert.warning(ExternalInterface.call('getBundle','text','label-invalidAmountAlert','Please enter a valid amount'),ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
    }
  }
  setFocusField(event, textAmount: SwtTextInput) {
    if(event.detail == Alert.OK) {
      textAmount.setFocusAndSelect();
    }

  }
   startOfComms():void
  {
    this.loadingImage.setVisible(true);
    this.disableInterface();
  }

  endOfComms():void
  {
    this.loadingImage.setVisible(false);
    this.enableInterface();
  }
  saveDataResult(event):void
  {

    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    }
    else
    {

      this.lastRecievedJSON= event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      // Check the requst reply status in XML
      if (this.jsonReader.getRequestReplyMessage() == "Data fetch OK")
      {
        if(this.groupDefaultName.indexOf("Central")!= -1){
          ExternalInterface.call("window.opener.setStoredParam","CentralGroupId",this.groupIdTextInput.text);
        }
        else {
          //ExternalInterface.call("window.opener.setStoredParam","GlobalGroupId",this.groupIdTextInput.text);
        }
       // ExternalInterface.call("window.opener.setStoredParam","groupId",this.groupIdTextInput.text);
        ExternalInterface.call("refreshParent");
        ExternalInterface.call("close");
      }
      else {
        if(this.jsonReader.getRequestReplyMessage()== "errors.DataIntegrityViolationExceptioninAdd")
          this.swtAlert.warning(ExternalInterface.call('getBundle','text','label-recordExistsAlert','Record already exists'),ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
        else
          this.swtAlert.warning(ExternalInterface.call('getBundle','text','label-syntaxAlert','Query syntax is not correct, Please verfiy your query :') +
            this.jsonReader.getRequestReplyMessage(),ExternalInterface.call('getBundle','text','label-syntaxAlertTitle','Filter condition error'));
      }

    }

  }
 inputDataResult(event):void
  {
    // Condition to check the inputData HTTPcomms is busy ie to ensure the data load
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    }
    else
    {
      this.lastRecievedJSON= event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus())
      {
        if (this.lastRecievedJSON != this.prevRecievedJSON)
        {
          if (!this.jsonReader.isDataBuilding())
          {
            this.entityCombo.setComboData(this.jsonReader.getSelects());
            this.selectedEntity.text= this.entityCombo.selectedValue;
            this.ccyCombo.setComboData(this.jsonReader.getSelects());
            this.selectedCcy.text= this.ccyCombo.selectedValue;
            this.groupIdTextInput.text= this.selectedAccountGroup;
            let rowData = this.lastRecievedJSON.ilmaccountgroupdetails.grid.rows.row;
            if(rowData) {
            for ( let index = 0; index < rowData.length; index++) {
              this.accountAlreadyInGroup.push(rowData[index].account_id.content);
            }
            }
            const obj = {columns: this.jsonReader.getColumnData()};
            this.rightGrid.CustomGrid(obj);
            this.rightGrid.gridData=this.jsonReader.getGridData();
            this.rightGrid.setRowSize= this.jsonReader.getRowSize();
            if (!this.alreadyOpened)
            {
              this.groupName.text = this.jsonReader.getSingletons().groupname;
              this.groupDescription.text = this.jsonReader.getSingletons().groupdescription;
              this.firstMinimum.text = this.jsonReader.getSingletons().firstminimum;
              this.formatAmount(this.firstMinimum);
              this.firstMaximum.text = this.jsonReader.getSingletons().firstmaximum;
              this.formatAmount(this.firstMaximum);
              this.secondMinimum.text = this.jsonReader.getSingletons().secondminimum;
              this.formatAmount(this.secondMinimum);
              this.secondMaximum.text = this.jsonReader.getSingletons().secondmaximum;
              this.mainAgentText.text =this.jsonReader.getSingletons().mainAgent;
              this.formatAmount(this.secondMaximum);
              this.filterCondition.text = this.jsonReader.getSingletons().filtercondition;
              this.netMinimum.text = this.jsonReader.getSingletons().minNcpThreshold;
              this.formatAmount(this.netMinimum);
              this.netMaximum.text = this.jsonReader.getSingletons().maxNcpThreshold;
              this.formatAmount(this.netMaximum);
              this.privateRadio.enabled = ExternalInterface.call('eval', 'maintainAnyGroup');
              this.publicRadio.enabled = ExternalInterface.call('eval', 'maintainAnyGroup');
              this.typeDynamic.enabled = true;
              this.typeFixed.enabled = true;
              this.idLegend.enabled = true;
              this.nameLegend.enabled = true;
              this.allowReportingCheckBox.enabled = true;
              this.collectNetCumPosCheckBox.enabled = false;
              this.groupIdTextInput.required = true;
              this.startTime.text = this.jsonReader.getSingletons()['thresh1Time'];
              this.endTime.text = this.jsonReader.getSingletons()['thresh2Time'];
              this.startTarget.text = this.jsonReader.getSingletons()['thresh1Percent'];
              this.endTarget.text = this.jsonReader.getSingletons()['thresh2Percent'];
              if (this.methodName != "add" && this.methodName !=  "addfromILM")
              {
                this.createdByLabelText.text=this.jsonReader.getSingletons().createdByUser;
                this.createdOnLabelText.text=this.jsonReader.getSingletons().createDate;
              }
              if(this.jsonReader.getSingletons().grouptype) {
                this.type.selectedValue = this.jsonReader.getSingletons().grouptype;
              }
              if(this.jsonReader.getSingletons().publicprivate) {
                this.publicprivate.selectedValue = this.jsonReader.getSingletons().publicprivate;
              }
              if(this.jsonReader.getSingletons().idname) {
                this.idName.selectedValue = this.jsonReader.getSingletons().idname;
              }
              this.allowReportingCheckBox.selected = this.jsonReader.getSingletons().allowReporting == "Y";
              this.collectNetCumPosCheckBox.selected = this.jsonReader.getSingletons().collectNetCumPos == "Y";
              this.correspondentBankCheckBox.selected = this.jsonReader.getSingletons().correspondentBank == "Y";
              this.throughputCheckBox.selected = this.jsonReader.getSingletons()['createThroughputRatio'] =='Y';
              this.accountInThisGroupTextLabel.text = " (" + this.rightGrid.dataProvider.length + ")";
              if (this.methodName=="addfromILM")
              {
                this.groupDescription.text= this.description;
                this.type.selectedValue='D';
                this.groupName.text= this.groupDefaultName;
                this.filterCondition.enabled=true;
                this.filterCondition.text= this.filterConditionText;
                this.publicprivate.selectedValue="Public";
                this.publicRadio.enabled=false;
                this. privateRadio.enabled=false;
                this.allowReportingCheckBox.enabled=false;
                this.allowReportingCheckBox.selected=true;
                this.testButton.enabled = true ;
                this.nameLegend.selected = true;
                this.idLegend.selected = false;
              }
              else if (this.methodName == "change")
              {
                this.entityCombo.enabled = false;
                this.ccyCombo.enabled = false;
                this.groupIdTextInput.enabled = false;
                this.type.enabled = false;
                if(this.jsonReader.getSingletons().maintainAnyGroup == true && this.jsonReader.getSingletons().global != "Y"  && this.jsonReader.getSingletons().central != true)
                  this.publicprivate.enabled = true;
                else
                  this.publicprivate.enabled = false;
                if (this.type.selectedValue == "D")
                {
                  this.filterCondition.enabled = true;
                  this.ownGridCanvas.enabled = false;
                  this.otherGridCanvas.enabled = true;
                  this.secondGridGroupsCombo.enabled = false;
                  this.disableMoveButtons();
                  this.rightGridQuickSearch.enabled = false;
                  this.leftGridQuickSearch.enabled = true;
                  this.testButton.enabled = true ;
                  if (this.allowReportingCheckBox.selected)
                  {
                    this.correspondentBankCheckBox.enabled = true;
                    this.collectNetCumPosCheckBox.enabled=true;
                  }
                  else
                  {
                    this.correspondentBankCheckBox.enabled = false;
                    this.collectNetCumPosCheckBox.enabled=false;
                    this.collectNetCumPosCheckBox.selected=false;
                  }
                }
                else
                {
                  this.ownGridCanvas.enabled = true;
                  this.otherGridCanvas.enabled = true;
                  this.secondGridGroupsCombo.enabled = true;
                  this.rightGridQuickSearch.enabled = true;
                  this.leftGridQuickSearch.enabled = true;
                  this.filterCondition.enabled = false;
                  if (this.allowReportingCheckBox.selected)
                  {
                    this.correspondentBankCheckBox.enabled = true;
                    this.collectNetCumPosCheckBox.enabled=true;
                  }
                  else
                  {
                    this.correspondentBankCheckBox.enabled = false;
                    this.collectNetCumPosCheckBox.enabled=false;
                    this.collectNetCumPosCheckBox.selected=false;
                  }

                }


              }
              else if (this.methodName == "view")
              {
                this.entityCombo.enabled = false;
                this.ccyCombo.enabled = false;
                this.groupIdTextInput.enabled = false;
                this.type.enabled = false;
                this.publicprivate.enabled = false;
                this.groupDescription.enabled = false;
                this.idName.enabled = false;
                this.groupName.enabled = false;
                this.firstMaximum.enabled = false;
                this.firstMinimum.enabled = false;
                this.secondMaximum.enabled = false;
                this.secondMinimum.enabled = false;
                this.netMaximum.enabled = false;
                this.netMinimum.enabled = false;
                this.filterCondition.enabled = false;
                this.mainAgentText.enabled= false;
                this.disableMoveButtons();
                this.throughputCheckBox.enabled = false;
                this.startTarget.enabled=false;
                this.endTarget.enabled=false;
                this.startTime.enabled=false;
                this.endTime.enabled=false;


              }
              else
              {
                
                this.publicprivate.enabled= this.jsonReader.getSingletons().maintainAnyGroup;
                
                this.type.enabled=true;


                /*ownGridContainer.enabled=true;
                otherGridContainer.enabled=true;*/
                this.secondGridGroupsCombo.enabled=true;
                this.rightGridQuickSearch.enabled=true;
                this.leftGridQuickSearch.enabled=true;
                this.filterCondition.enabled=false;
                this.groupIdTextInput.setFocus();
              }

            }
          }
        }
      }
    }
    this.allowReportingOnLoad = this.allowReportingCheckBox.selected;
    this.showSecondGroupGrid();

  }
  entityChangeCombo():void
  {
    this.updateData();
  }

  leftGridGroupComboChange():void {
    
      this.inputData.cbResult= (event) => {
        this.inputDataSecondGridResult(event);
      };

      this.actionMethod="method=getAccountListDetails";
      this.leftGridQuickSearch.text="";
      this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;

      //set menuAccessId in params
      this.requestParams["selectedAccountGroup"]= this.selectedAccountGroup;
      this.requestParams["secondGridAccountGroup"]= this.secondGridGroupsCombo.selectedValue;
      //Make initial request
      this.inputData.send(this.requestParams);
    
    if(this.methodName == "view")
      this.disableMoveButtons();
    else
      this.enableMoveButtons();

  }
 enableInterface():void
  {
    this.typeDynamic.enabled = true;
    this.typeFixed.enabled = true;
    this.idLegend.enabled = true;
    this.nameLegend.enabled = true;
    if (this.methodName != 'view')
    {
      this.allowReportingCheckBox.enabled = true;
      this.collectNetCumPosCheckBox.enabled = true;
      if(this.allowReportingCheckBox.selected)
      {
        this.correspondentBankCheckBox.enabled = true;
        this.collectNetCumPosCheckBox.enabled=true;
      }
      else
      {
        this.correspondentBankCheckBox.enabled = false;
        this.collectNetCumPosCheckBox.enabled=false;
        this.collectNetCumPosCheckBox.selected=false;
      }
      this.idLegend.enabled = true;
      this.nameLegend.enabled = true;
    }
    this.rightGridQuickSearch.enabled = true;
    this.leftGridQuickSearch.enabled = true;
    this.saveButton.enabled=true;
    if (this.methodName == 'add')
    {
      this.ccyCombo.enabled=true;
      this.entityCombo.enabled=true;
    }
    this.secondGridGroupsCombo.enabled=true;
  }
  /**
   * Disable interface, turn off certain UI elements when a request is made to the server
   **/
  disableInterface():void
  {
    this.saveButton.enabled=false;
    this.ccyCombo.enabled=false;
    this.entityCombo.enabled=false;
    this.secondGridGroupsCombo.enabled=false;
    this.typeDynamic.enabled = false;
    this.typeFixed.enabled = false;
    this.idLegend.enabled = false;
    this.nameLegend.enabled = false;
    this.allowReportingCheckBox.enabled = false;
    this.collectNetCumPosCheckBox.enabled = false;
    this.correspondentBankCheckBox.enabled = false;
    this.rightGridQuickSearch.enabled = false;
    this.leftGridQuickSearch.enabled = false;

  }
  /**
   * This function implement the maintenance of the group member accounts : specify
   * (accounts that will be added as well that will be deleted from group)
   *
   * */
  /*manageMoveAccounts():void{

    for each (let xmlElement:XML in (rightGrid.dataProvider as XMLListCollection).source){
      if(xmlElement.@right_hidden=='false' && xmlElement.@original== undefined)
      // it is a new account
        this.accountInGroupToAdd.addItem(xmlElement.account_id);
      if( xmlElement.@original=='true'&& xmlElement.@right_hidden=='true')
      //accounts to delete
        this.accountInGroupToDelete.addItem(xmlElement.account_id);
    }

  }*/

  /**
   * This function allow user to move account from left grid to right grid
   * */
  moveToRight(event, moveAll: boolean): void {

    this.leftGrid.dataviewObj.beginUpdate();
    this.rightGrid.dataviewObj.beginUpdate();
    let rightList = [...this.rightGrid.dataset];
    let dpList = [...this.leftGrid.getFilteredItems()];
    let countMovedLeft: number;

    try {
      if (moveAll) {
        countMovedLeft = dpList.length;
        let items =[];
        for (let i= 0; i< dpList.length; i++)
        {
          rightList.push(dpList[i]);
          items.push(this.leftGrid.angularGridInstance.gridService.getDataItemByRowNumber( i ));

        }
          this.leftGrid.angularGridInstance.gridService.deleteItems( items);
        for (let i =0 ; i < rightList.length; i++) {
          rightList[i].id = i;
        }
        this.rightGrid.gridData = {row: rightList, size: rightList.length};

      }
      else{
        let items =[];
        countMovedLeft =  this.leftGrid.selectedIndices.length;
        items.push(this.leftGrid.angularGridInstance.gridService.getDataItemByRowNumber( this.leftGrid.selectedIndex ));
        this.leftGrid.angularGridInstance.gridService.deleteItems( items );
        rightList.push(this.leftGrid.selectedItem);
        for (let i =0 ; i < rightList.length; i++) {
          rightList[i].id = i;
        }
        this.rightGrid.gridData = {row: rightList, size: rightList.length};
      }
      let countRight = Number(this.accountInThisGroupTextLabel.text.replace('(', '').replace(')', ''));
      this.accountInThisGroupTextLabel.text = '('+ (countRight +countMovedLeft) +')';
      let countLeft = Number(this.accountNotInThisGroupTextLabel.text.replace('(', '').replace(')', ''));
      this.accountNotInThisGroupTextLabel.text = '('+ ( countLeft - countMovedLeft)  +')';

      this.leftGrid.selectedIndex=-1;
    } catch (error)
    {
      console.log('erorrrr--------', error)
    }
    this.leftGrid.dataviewObj.endUpdate();
    this.rightGrid.dataviewObj.endUpdate();
    this.enableMoveButtons();
    if(!this.waitingRefreshLeft) {
      this.waitingRefreshLeft = true;
      setTimeout(() => {
        this.refreshLeftHeader();
      }, 2000)
    }

  }
  /**
   * This function allow user to move account from left grid to right grid
   * */
  moveToLeft(event, moveAll: boolean): void {
    this.leftGrid.dataviewObj.beginUpdate();
    this.rightGrid.dataviewObj.beginUpdate();
    let rightList : any = [...this.rightGrid.getFilteredItems()];
    let leftList  : any = [...this.leftGrid.dataset];
    let countMovedRight: number;
    try {
      if (moveAll)
      {
        let items =[];
        countMovedRight = rightList.length;
        for (let i= 0; i< rightList.length; i++)
        {
          leftList.push(rightList[i]);
          items.push(this.rightGrid.angularGridInstance.gridService.getDataItemByRowNumber( i ));
        }
        this.rightGrid.angularGridInstance.gridService.deleteItems( items);
        for (let i =0 ; i < leftList.length; i++) {
          leftList[i].id = i
        }
        this.leftGrid.gridData = {row: leftList, size: leftList.length};
      }
      else{
        let items =[];
        countMovedRight =  this.rightGrid.selectedIndices.length;
        items.push(this.rightGrid.angularGridInstance.gridService.getDataItemByRowNumber( this.rightGrid.selectedIndex));
        this.rightGrid.angularGridInstance.gridService.deleteItems( items );
        leftList.push(this.rightGrid.selectedItem);
        for (let i =0 ; i < leftList.length; i++) {
          leftList[i].id = i;
        }
        this.leftGrid.gridData = {row: leftList, size: leftList.length};

      }

      let countRight = Number(this.accountInThisGroupTextLabel.text.replace('(', '').replace(')', ''));
      this.accountInThisGroupTextLabel.text = '('+ (countRight -countMovedRight) +')';
      let countLeft = Number(this.accountNotInThisGroupTextLabel.text.replace('(', '').replace(')', ''));
      this.accountNotInThisGroupTextLabel.text = '('+ ( countLeft + countMovedRight)  +')';
      this.rightGrid.selectedIndex=-1;


    } catch (error)
    {
      console.log('error in Move left', error);
    }
    this.leftGrid.dataviewObj.endUpdate();
    this.rightGrid.dataviewObj.endUpdate();
    this.enableMoveButtons();
    if(!this.waitingRefresh) {
      this.waitingRefresh = true;
      setTimeout(() => {
        this.refreshRightHeader();
      }, 2000)
    }
  }
  private waitingRefresh = false;
  updateCountGroupMember():void {
    if (this.rightGrid.dataProvider !=null){
      this.accountInThisGroupTextLabel.text = " (" + this.rightGrid.getFilteredData().length + ")";
    }
    if (this.leftGrid.dataProvider !=null){
      this.accountNotInThisGroupTextLabel.text =  " (" + this.leftGrid.getFilteredData().length + ")";
    }

  }

  accountsAddDelete() : void {
    let arrayIdOriginal = [];
    let arrayIdCurrent = [];
    if(this.rightGrid.dataProvider && this.rightGrid.originalDataprovider) {
      for (let i = 0; i < this.rightGrid.dataProvider.length; i++) {
        arrayIdCurrent.push(this.rightGrid.dataProvider[i].account_id);
      }
      for (let i = 0; i < this.rightGrid.originalDataprovider.length; i++) {
        arrayIdOriginal.push(this.rightGrid.originalDataprovider[i].account_id);
      }
      if (this.methodName == "add" || this.methodName ==  "addfromILM") {
        for (let i = 0; i < this.rightGrid.dataProvider.length; i++) {
          this.accountInGroupToAdd.push(this.rightGrid.dataProvider[i].account_id)

        }
      } else {
        for (let i = 0; i < this.rightGrid.dataProvider.length; i++) {
          if (arrayIdOriginal.indexOf(this.rightGrid.dataProvider[i].account_id) == -1) {
            this.accountInGroupToAdd.push(this.rightGrid.dataProvider[i].account_id)
          }
        }
        for (let i = 0; i < this.rightGrid.originalDataprovider.length; i++) {
          if (arrayIdCurrent.indexOf(this.rightGrid.originalDataprovider[i].account_id) == -1) {
            this.accountInGroupToDelete.push(this.rightGrid.originalDataprovider[i].account_id);

          }
        }
      }
    }
  }

  /**
   * Upon completion of loading into the flash player this method is called
   **/
  showSecondGroupGrid():void
  {


    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.cbResult= (event) => {
      this.inputDataSecondGridResult(event);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;

    /*Define the action to send the request*/
    this.actionPath="intraDayLiquidity.do?";
    /*Define method the request to access*/
    this.actionMethod="";
    this.rightGrid.onRowClick = (event) => {
      this.cellLogic(event);
    };
    this.leftGrid.onRowClick = (event) => {
      this.cellLogic(event);
    };
    if (this.filterCondition.text.length > 0)
    {
      this.entityId= this.entityCombo.selectedLabel;
      this.currencyCode= this.ccyCombo.selectedLabel;
      this.actionMethod="method=getFilterConditionTestResult";
      this.requestParams["query"]= this.filterCondition.text;
      this.requestParams["entityId"]= this.entityId;
      this.requestParams["currencyCode"]= this.currencyCode;

    }
    else
    {
      this.actionMethod="method=getAccountListDetails";
      this.requestParams["selectedAccountGroup"]= this.selectedAccountGroup;
      if (this.methodName == "add" || this.methodName=="addfromILM")
      {
        this.requestParams["entiyId"]= this.entityCombo.selectedLabel;
        this.requestParams["currencyCode"]= this.ccyCombo.selectedLabel;
      }
    }

    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
    //Make initial request
    this.inputData.send(this.requestParams);
  }
  inputDataSecondGridResult(event):void
  {
    // Condition to check the inputData HTTPcomms is busy ie to ensure the data load
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    }
    else
    {

      this.lastRecievedJSON2= event;
      this.jsonReader2.setInputJSON(this.lastRecievedJSON2);
      // Check the requst reply status in XML
      if (this.jsonReader2.getRequestReplyStatus()) {
        // Condition to check the last received and previous xml to build grid data
        if (this.lastRecievedJSON2 != this.prevRecievedJSON2)
        {
          // Condition to check xmlreader databuilding
          if (!this.jsonReader2.isDataBuilding())
          {
            if (String(this.jsonReader2.getSingletons().exceptioninquery).length > 0)
            {
              this.swtAlert.warning(ExternalInterface.call('getBundle','text','label-syntaxAlert','Query syntax is not correct, Please verfiy your query :') +
                this.jsonReader2.getSingletons().exceptioninquery,ExternalInterface.call('getBundle','text','label-syntaxAlertTitle','Filter condition error'));
            }
            else
            {
              if (this.type.selectedValue == "D" && this.filterCondition.text!="")
              {
                //ownGridContainer.removeAllElements();

                const obj = {columns: this.jsonReader2.getColumnData()};
                this.leftGrid.CustomGrid(obj);
                this.rightGrid.CustomGrid(obj);
                // Sets the data provider from xmlReader2: When combo box changes

                this.rightGrid.gridData=this.jsonReader2.getGridData();
                this.rightGrid.setRowSize=this.jsonReader2.getRowSize();
                this.accountInThisGroupTextLabel.text="(" + this.rightGrid.dataProvider.length + ")";
                this.otherGridCanvas.enabled = false;
                this.ownGridCanvas.enabled=true;
                this.secondGridGroupsCombo.enabled=false;
                this.rightGridQuickSearch.enabled=true;
                //otherGridContainer.addElement(leftGrid);
                this.leftGridQuickSearch.enabled = false;
              } else {
                this.secondGridGroupsCombo.setComboData(this.jsonReader2.getSelects(), true);

               const obj = {columns: this.jsonReader2.getColumnData()};
                this.leftGrid.CustomGrid(obj);
                this.leftGrid.gridData=this.jsonReader2.getGridData();
                //this.leftGrid.lockedColumnCount = 1;
                /*filterLeftGridElements((leftGrid.dataProvider as XMLListCollection));

                (leftGrid.dataProvider as XMLListCollection).filterFunction = leftGridFilterFunction;*/
                this.enableMoveButtons();
                this.leftGrid.setRowSize=this.jsonReader2.getRowSize();
                if(this.methodName != "add") {
                  this.leftGrid.dataviewObj.beginUpdate();
                  setTimeout(() => {
                    let rightList : any = [...this.rightGrid.getFilteredItems()];
                    for (let i= 0; i< rightList.length; i++) {
                      let item = this.leftGrid.getFilteredItems().find(x=>x.account_id ==rightList[i].account_id);
                      if(item)
                        this.leftGrid.angularGridInstance.gridService.deleteItem(item);
                    }
                 }, 0);
                    this.leftGrid.dataviewObj.endUpdate();
                    this.refreshLeftHeader();
                }
                this.accountNotInThisGroupTextLabel.text=" (" + this.leftGrid.dataProvider.length + ")";
                //dpMap.put(StringUtils.trim(selectedCombo),leftGrid.dataProvider);
              }
            }
          }
        }
      }
      else
      {
        this.swtAlert.warning(ExternalInterface.call('getBundle','text','label-syntaxAlert','Query syntax is not correct, Please verfiy your query :'),ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
      }
    }
    if (this.methodName == "view")
    {
      this.disableMoveButtons();
      this.saveButton.enabled = false ;
    }

  }

 updateData():void
  {
    //Obtain the url of the context
    //Initialize the context menu
    this.alreadyOpened=true;
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.selectedAccountGroup=this.groupIdTextInput.text;
    this.entityId=this.entityCombo.selectedLabel;
    this.currencyCode=this.ccyCombo.selectedLabel;
    this.methodName=ExternalInterface.call('eval', 'methodName');
    /*Define the action to send the request*/
    this.actionPath="intraDayLiquidity.do?";
    this.actionMethod="method=getAccountGroupDetails";
    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    //set menuAccessId in params
    this.requestParams["selectedAccountGroup"]= this.selectedAccountGroup;
    this.requestParams["entityId"]=this.entityId;
    this.requestParams["currencyCode"]=this.currencyCode;
    //Make initial request
    this.inputData.send(this.requestParams);

  }

  saveButton_clickHandler(event:MouseEvent):void
  {
    //Obtain the url of the context
    //Initialize the context menu
    try
    {
      if (this.groupIdTextInput.text.length > 0)
      {
        let isGroupIdValid :boolean ;
        isGroupIdValid = this.validateGroupId();
        if(!isGroupIdValid)
        {
          return
        }
        if(!this.isTimeValid())
          return;
        this.saveData.cbStart= this.startOfComms.bind(this);
        this.saveData.cbStop= this.endOfComms.bind(this);
        this.saveData.cbResult= (event) => {
          this.saveDataResult(event);
        };
        this.saveData.cbFault= this.inputDataFault.bind(this);
        this.saveData.encodeURL=false;
        this.selectedAccountGroup=ExternalInterface.call('eval', 'selectedAccountGroup');

        this.actionPath="intraDayLiquidity.do?";
        if (this.methodName == "add" )
          this.actionMethod="method=saveAccountGroupdetails";
        else if(this.methodName == "change" || this.methodName=="addfromILM")
          this.actionMethod="method=updateAccountGroupdetails";
        this.saveData.url= this.baseURL + this.actionPath + this.actionMethod;
        if (this.methodName == "add" || this.methodName=="addfromILM")
        {
          this.selectedAccountGroup= this.groupIdTextInput.text;
        }
        this.requestParams["fromFlex"] = "true";
        this.requestParams["selectedAccountGroup"] = this.selectedAccountGroup;
        this.requestParams["entityId"] = this.entityCombo.selectedLabel;
        this.requestParams["currencyCode"] = this.ccyCombo.selectedLabel;
        this.requestParams["privatePublic"] = this.publicprivate.selectedValue;
        this.requestParams["type"] = this.type.selectedValue;
        this.requestParams["name"] = this.groupName.text;
        this.requestParams["mainAgentText"] = this.mainAgentText.text;
        this.requestParams["description"] = this.groupDescription.text;
        this.requestParams["defaultLegendText"] = this.idName.selectedValue;
        this.requestParams["allowReporting"] = this.allowReportingCheckBox.selected ? "Y" : "N";
        this.requestParams["collectNetCumPos"] = this.collectNetCumPosCheckBox.selected ? "Y" : "N";
        this.requestParams["correspondentBank"] = this.correspondentBankCheckBox.selected ? "Y" : "N";

        this.requestParams["throughputRatio"] = this.throughputCheckBox.selected ? "Y" : "N";
        this.requestParams["thresh1Time"] = this.startTime.text;
        this.requestParams["thresh2Time"] = this.endTime.text;
        this.requestParams["thresh1Percent"] = this.startTarget.text;
        this.requestParams["thresh2Percent"] = this.endTarget.text;
        let currencyFormat =  ExternalInterface.call('eval','ccyPattern');

        this.firstMinimum.text = StringUtils.unformatAmount(this.firstMinimum.text,Number(currencyFormat)).replace(".00","").replace(",00","");
        this.requestParams["firstMinimum"] = this.firstMinimum.text;

        this.secondMinimum.text = StringUtils.unformatAmount(this.secondMinimum.text,Number(currencyFormat)).replace(".00","").replace(",00","");
        this.requestParams["secondMinimum"] = this.secondMinimum.text;

        this.firstMaximum.text = StringUtils.unformatAmount(this.firstMaximum.text,Number(currencyFormat)).replace(".00","").replace(",00","");
        this.requestParams["firstMaximum"] = this.firstMaximum.text;
        this.secondMaximum.text = StringUtils.unformatAmount(this.secondMaximum.text,Number(currencyFormat)).replace(".00","").replace(",00","");
        this.requestParams["secondMaximum"] = this.secondMaximum.text;

        this.netMinimum.text = StringUtils.unformatAmount(this.netMinimum.text,Number(currencyFormat)).replace(".00","").replace(",00","");
        this.requestParams["minNcpThreshold"] = this.netMinimum.text;
        this.netMaximum.text = StringUtils.unformatAmount(this.netMaximum.text,Number(currencyFormat)).replace(".00","").replace(",00","");
        this.requestParams["maxNcpThreshold"] = this.netMaximum.text;

        this.requestParams["filterCondition"]= this.filterCondition.text;
        // Define accounts that will be added as well that will be deleted from group
        if(this.typeFixed.selected){
          //this.manageMoveAccounts();
          this.accountsAddDelete();
          this.requestParams["accountsToDelete"]= this.accountInGroupToDelete.toString();
          this.requestParams["accountsToAdd"]=this.accountInGroupToAdd.toString();
        }

        if (this.allowReportingCheckBox.selected)
        {
          if (this.methodName == "change" && this.allowReportingOnLoad)
          {
            this.saveData.send(this.requestParams);
            return;
          }
          this.swtAlert.warning(SwtUtil.getPredictMessage('ilmScenario.alert.allowReporting', null), 'Warning', Alert.OK | Alert.CANCEL, null, this.okHandler.bind(this));
        }
        else
        {
          this.saveData.send(this.requestParams);
        }
      }
      else
      {
        this.swtAlert.warning(ExternalInterface.call('getBundle','text','label-acctGrpRequired','Account Group Id is required'),ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
      }
    }
    catch (e)
    {
      console.log('error', e)
    }
  }
  okHandler(event): void {
    if (event.detail == Alert.OK)
    {
      this.saveData.send(this.requestParams);
    }
  }
   export(event):void {
    ExternalInterface.call('onExport', this.groupIdTextInput.text, event.data.toString());
  }

  enableMoveButtons():void
  {

    //TO DO :Manage (enable/disable) move buttons
    if (this.ownGridCanvas.enabled == true && this.otherGridCanvas.enabled == true)
    {
      this.buttonsContainer.enabled=true;
      if (this.leftGrid.getFilteredData().length > 0 ||this.leftGrid.getFilteredItems().length > 0){
        this.buttonMoveRight.enabled= this.leftGrid.selectedIndex >= 0;
        this.buttonMoveAllRight.enabled = true;
        this.buttonMoveLeft.enabled = false;
        this.buttonMoveAllLeft.enabled = false;
      }else{
        this.buttonMoveAllRight.enabled = false;
        this.buttonMoveRight.enabled = false;
      }

      if (this.rightGrid.getFilteredData().length > 0 || this.rightGrid.getFilteredItems().length > 0){
        this.buttonMoveLeft.enabled= this.rightGrid.selectedIndex >= 0;
        this.buttonMoveAllLeft.enabled=true;
      }

   }

  }

  disableMoveButtons():void
  {
    this.buttonMoveRight.enabled=false;
    this.buttonMoveAllRight.enabled=false;
    this.buttonMoveLeft.enabled=false;
    this.buttonMoveAllLeft.enabled=false;
    this.buttonsContainer.enabled=false;

  }
  enableAllButtons() {
    if(this.leftGrid.selectedIndex == -1) {

    }
  }


  filterConditionValidation():void
  {
    if (this.filterCondition.text.length > 0)
    {

      this.inputData.cbStart= this.startOfComms.bind(this);
      this.inputData.cbStop= this.endOfComms.bind(this);
      this.inputData.cbResult= (event) => {
        this.inputDataSecondGridResult(event);
      };
      this.inputData.cbFault= this.inputDataFault.bind(this);
      this.inputData.encodeURL=false;
      this.entityId= this.entityCombo.selectedLabel;
      this.currencyCode=this.ccyCombo.selectedLabel;
      /*Define the result format*/
      this.actionMethod="method=getFilterConditionTestResult";
      this.requestParams["query"]= this.filterCondition.text;
      this.requestParams["entityId"]= this.entityId;
      this.requestParams["currencyCode"]= this.currencyCode;
      this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
      //Make initial request
      this.inputData.send(this.requestParams);

    }


  };



  filtringLeftGrid( search: string ): void {

    try {
      this.isChangedQuickSearchLeft = true;
      this.leftTextOfInput = search;
      this.updateFilter();

    }
    catch ( error ) {
      console.log('error', error);
    }
  }
  updateFilter() {
    try {
      this.leftGrid.dataviewObj.beginUpdate();
      this.leftGrid.dataviewObj.setItems(this.leftGrid.gridData);
      this.leftGrid.dataviewObj.setFilterArgs({
        searchString: this.leftTextOfInput,
        currentFilter :this.leftGrid.getFilteredGridColumns()
      });
      this.leftGrid.dataviewObj.setFilter(this.filterFunction);
      this.leftGrid.dataviewObj.endUpdate();
      this.leftGrid.dataviewObj.refresh();
      if(this.methodName != "view")
        this.enableMoveButtons();
      this.updateCountGroupMember();
    } catch (e) {
      console.log('error in update', e)
    }

  }
    refreshLeftHeader() {
    if(this.isChangedQuickSearchLeft) {
      this.leftGrid.refrehHeaderFiters();
      this.waitingRefreshLeft = false;
    }

  }
  refreshRightHeader() {
    if(this.isChangedQuickSearchRight) {
      this.rightGrid.refrehHeaderFiters();
      this.waitingRefresh = false;
    }

  }
 filtringRightGrid(search: string):void
  {
    try {
      this.isChangedQuickSearchRight = true
      this.rightTextOfInput = search;
      this.updateFilterRight();
    }
    catch ( error ) {
      console.log('error', error);
    }
  }
  updateFilterRight() {

    this.rightGrid.dataviewObj.beginUpdate();
    this.rightGrid.dataviewObj.setItems(this.rightGrid.gridData);
    this.rightGrid.dataviewObj.setFilterArgs({
      searchString: this.rightTextOfInput,
      currentFilter :this.rightGrid.getFilteredGridColumns()
    });
    this.rightGrid.dataviewObj.setFilter(this.filterFunction);
    this.rightGrid.dataviewObj.endUpdate();
    this.rightGrid.dataviewObj.refresh();
    this.updateCountGroupMember();
    if(this.methodName != "view")
      this.enableMoveButtons();
  }
  /**
   * LeftGrid dataprovider filter function
   *
   * */



  filterFunction(item, args) {
    let filterArray = args.currentFilter.split('@||@');
    if (args.currentFilter) {
      if (filterArray[0] != 'All') {
        if (item["account_id_name"] !== filterArray[0] || item["account_id_name"].toLowerCase().indexOf(args.searchString.toLowerCase()) == -1) {
          return false;
        }
        if (filterArray[1] != 'All') {
          if (item["type"] !== filterArray[1]) {
            return false;
          }
        }
        if (filterArray[2] != 'All') {
          if (item["class"] !== filterArray[2]) {
            return false;
          }
        }
        if (filterArray[3] != 'All') {
          if (item["level"] !== filterArray[3]) {
            return false;
          }
        }
        return true;
      } else {

        if (filterArray[1] != 'All') {
          if (item["type"] !== filterArray[1]) {
            return false;
          }
        }
        if (filterArray[2] != 'All') {
          if (item["class"] !== filterArray[2]) {
            return false;
          }
        }
        if (filterArray[3] != 'All') {
          if (item["level"] !== filterArray[3]) {
            return false;
          }
        }
        if (item["account_id_name"].toLowerCase().indexOf(args.searchString.toLowerCase()) == -1) {
          return false;
        }
        return true;
      }
    } else {

      try {
        if (item["account_id_name"].toLowerCase().indexOf(args.searchString.toLowerCase()) == -1) {
          return false;
        }
        return true;
      } catch (e) {
        console.log('ee', e)
      }
    }
  }

  enableDisableRNCPC_CorrBank (event:Event){

    if(this.allowReportingCheckBox.selected){
      this.correspondentBankCheckBox.enabled = true;
      this.collectNetCumPosCheckBox.enabled=true;
    }else{
      this.correspondentBankCheckBox.enabled = false;
      this.collectNetCumPosCheckBox.enabled=false;
      this.collectNetCumPosCheckBox.selected=false;
      this.correspondentBankCheckBox.selected=false;
    }
  }
  closeHandler() : void {
    ExternalInterface.call('close')
  }
  validateGroupId() : boolean {
    let message = "Please enter a valid alphanumeric value, the underscore and the hypen characters are allowed";
    let alphaNumPatWithHyphenAndUnderScore: RegExp = /^[a-zA-Z0-9\-_]+$/;
    if (!(alphaNumPatWithHyphenAndUnderScore.test(this.groupIdTextInput.text))) {
      this.swtAlert.confirm(message, 'Alert', Alert.OK, null);
      return false;
    }
    return true

  }
 radioTypeChangeHandler():void
  {
    if (this.type.selectedValue == "D")
    {
      this.filterCondition.enabled=true;
      this.ownGridCanvas.enabled=false;
      this.otherGridCanvas.enabled=false;
      this.secondGridGroupsCombo.enabled=false;
      this.disableMoveButtons();
      this.leftGridQuickSearch.enabled=false;
      this.leftGrid.dataProvider = [];
      this.rightGrid.dataProvider = [];
      this.testButton.enabled = true;
      this.accountNotInThisGroupTextLabel.text= " (" + this.leftGrid.dataProvider.length + ")";
    }
    else
    {
      this.filterCondition.text="";
      this.ownGridCanvas.enabled=true;
      this.otherGridCanvas.enabled=true;
      this.secondGridGroupsCombo.enabled=true;
      this.buttonsContainer.enabled=true;
      this.rightGridQuickSearch.enabled=true;
      this.leftGridQuickSearch.enabled=true;
      this.filterCondition.enabled=false;
      this.testButton.enabled=false;
      this.rightGrid.dataProvider = [];
      this.accountInThisGroupTextLabel.text= " (" + this.rightGrid.dataProvider.length + ")";
      this.showSecondGroupGrid();
    }
    // Clear all XML dataproviders from memory

    /*for each(let key:String in dpMap.getKeys()){
    if(dpMap.getValue(key))
      delete (dpMap.getValue(key));
  }

    // Clear the shared map entries
    dpMap.clear();*/
  }
  doHelp() {
    ExternalInterface.call("help");
  }
  report(type: string) {
    ExternalInterface.call('onExport', this.groupIdTextInput.text, type.toString());
  }
  validateTime(textInput): any {
    let validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text  && validateFormatTime(textInput) == false) {
      this.swtAlert.warning(validTimeMessage, null );
      textInput.text = "";
      return false;
    } else  {
      textInput.text = textInput.text.substring(0,5);
      this.isTimeValid();
      return true;

    }
  }
  isTimeValid() {
    let start;
    let end;
    if(this.startTime.text)
      start = moment(this.startTime.text, 'HH:mm');
    if(this.endTime.text)
      end = moment(this.endTime.text, 'HH:mm');
    if (start !== undefined && end !== undefined && end.isBefore(start)  ) {
      this.swtAlert.warning("Second Time must be greater than First time");
      return false;
    }
    return true;
  }

  }
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: IlmAccountGroupDetails }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [IlmAccountGroupDetails],
  entryComponents: []
})
export class IlmAccountGroupDetailslModule {}

