<SwtModule (close)='popupClosed()' (creationComplete)='onLoad()' height='100%' width='680'>
  <VBox  width='100%' height='100%'>
      <SwtCanvas width='100%' height="20%">
        <VBox paddingTop="10" verticalGap="0">
        <HBox width='100%'>
            <SwtLabel #testOrderLabel  width="90"></SwtLabel>
            <SwtNumericInput id="testOrderNumericInput"
                          #testOrderNumericInput width="90" textAlign="right" height="21" >
            </SwtNumericInput>
          <spacer width="20"></spacer>
          <SwtLabel #logTextLabel width="70"></SwtLabel>
          <SwtTextInput #logText width="200" maxChars="50" height="21" ></SwtTextInput>
        </HBox>
        <HBox>
          <SwtLabel #cutOffTimeLabel width="90">
          </SwtLabel>
          <SwtTextInput id="cutOffTimeInput"
                        #cutOffTimeInput
                        width="60" height="21" textAlign="center" maxChars="5"
                        pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$" (focusOut)="validateTime(cutOffTimeInput)">
          </SwtTextInput>
        </HBox>
        </VBox>
      </SwtCanvas>
      <SwtCanvas width='100%' height='60%'>
        <VBox width="100%" height="100%">
          <SwtLabel #cutOffExpressionBuilderLabel></SwtLabel>
        <HBox width="100%" height="95%">
          <SwtTextArea id='queryText'
                       #queryText height="100%" width="80%" editable="false" >
          </SwtTextArea>
          <VBox horizontalAlign="center" verticalAlign="middle" width="20%" height="95%">
            <SwtButton id='expressionBuilderButton'
                       #expressionBuilderButton
                       [buttonMode]='true'
                       width="100"
                       (click)='doAddRule()'>
            </SwtButton>
          </VBox>
        </HBox>
        </VBox>
      </SwtCanvas>
      <SwtCanvas width="100%"  id="canvasContainer">
        <HBox>
          <SwtButton #okButton
                     (click)="save()"
                     id="okButton"
                     width="70" [buttonMode]="false" enabled="true"></SwtButton>
          <SwtButton buttonMode="true"
                     id="cancelButton"
                     width="70"
                     #cancelButton
                     (click)="popupClosed();"></SwtButton>
        </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>
