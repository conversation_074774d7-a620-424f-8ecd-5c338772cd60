import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CommonService, ExternalInterface, HTTPComms, JSONReader, Logger, ScreenVersion, StringUtils, SwtAlert, SwtButton, SwtCanvas, SwtComboBox, SwtDataExport, SwtLabel, SwtLoadingImage, SwtModule, SwtTabNavigator, SwtTextInput, SwtToolBoxModule, SwtTreeCommonGrid, SwtUtil, Tab } from 'swt-tool-box';

@Component({
  selector: 'app-cash-rsvr-bal-managmnt',
  templateUrl: './CashRsvrBalManagmnt.html',
  styleUrls: ['./CashRsvrBalManagmnt.css'],
  encapsulation: ViewEncapsulation.None,
})
export class CashRsvrBalManagmnt extends SwtModule implements OnInit {
  currencyPattern: any;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Cash Reserve Balance Management', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('entity') entity: SwtLabel;
  @ViewChild('currency') currency: SwtLabel;
  @ViewChild('account') account: SwtLabel;
  @ViewChild('accountType') accountType: SwtLabel;
  @ViewChild('entityDesc') entityDesc: SwtLabel;
  @ViewChild('currencyDesc') currencyDesc: SwtLabel;
  @ViewChild('accountDesc') accountDesc: SwtLabel;
  @ViewChild('startDateLbl') startDateLbl: SwtLabel;
  @ViewChild('endDateLbl') endDateLbl: SwtLabel;
  @ViewChild('targtAvgBalLbl') targtAvgBalLbl: SwtLabel;
  @ViewChild('minTarBalanceLbl') minTarBalanceLbl: SwtLabel;
  
  @ViewChild('fillDaysLbl') fillDaysLbl: SwtLabel;
  @ViewChild('fillBalLbl') fillBalLbl: SwtLabel;
  @ViewChild('exportContainer') exportContainer: SwtDataExport;
  @ViewChild('targtAvgBalTxtInput') targtAvgBalTxtInput: SwtTextInput;
  @ViewChild('fillDaysTxtInput') fillDaysTxtInput: SwtTextInput;
  @ViewChild('fillBalTxtInput') fillBalTxtInput: SwtTextInput;
  @ViewChild('minTarBalanceTxtInput') minTarBalanceTxtInput: SwtTextInput;

  

  @ViewChild('currencyCombo') currencyCombo: SwtComboBox;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('accountCombo') accountCombo: SwtComboBox;
  @ViewChild('accountTypeCombo') accountTypeCombo: SwtComboBox;

  @ViewChild('startDateField') startDateField: SwtTextInput;
  @ViewChild('endDateField') endDateField: SwtTextInput;

  @ViewChild('tabs') tabs: SwtTabNavigator;

  @ViewChild("displayContainerToday") displayContainerToday: Tab;
  @ViewChild("displayContainerTodayMinus") displayContainerTodayMinus: Tab;
  @ViewChild("displayContainerTodayMinusMinus") displayContainerTodayMinusMinus: Tab;

  @ViewChild('displaycontainer') displaycontainer: SwtCanvas;
  @ViewChild('excel') excel: SwtButton;
  @ViewChild('pdf') pdf: SwtButton;
  private inputData: HTTPComms = new HTTPComms(this.commonService);
  private summaryData: HTTPComms = new HTTPComms(this.commonService);
  private updateRefreshRate: HTTPComms = new HTTPComms(this.commonService);
  public sendData: HTTPComms = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private scenarioSummaryActionMethod = "";
  private actionPath = "";
  private scenarioSummaryActionPath = "";
  private requestParams = [];
  private swtAlert: SwtAlert;
  private logger: Logger;
  private _invalidComms: string;
  private menuAccessId;
  private defaultEntity;
  private defaultCurrency;
  private defaultAcctType;
  /**
   * Data Objects
   **/
  private jsonReader: JSONReader = new JSONReader();
  private summaryXMLReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private lastRecievedSummaryJSON;
  private prevRecievedSummaryJSON;
  public lastReceivedWidthJSON;
  public screenVersion = new ScreenVersion(this.commonService);
  public showJSONPopup: any;
  private arrayOftabs = [];
  private cashRsvrBalGrid: SwtTreeCommonGrid;
  private dateFormat: string;

  ngOnInit() {
    this.entity.text = SwtUtil.getPredictMessage('cashRsvrBal.entity', null);
    this.currency.text = SwtUtil.getPredictMessage('cashRsvrBal.currency', null);
    this.account.text = SwtUtil.getPredictMessage('cashRsvrBal.account', null);
    this.accountType.text = SwtUtil.getPredictMessage('cashRsvrBal.accountType', null);
    this.startDateLbl.text = SwtUtil.getPredictMessage('cashRsvrBal.startDate', null);
    this.endDateLbl.text = SwtUtil.getPredictMessage('cashRsvrBal.endDate', null);
    this.targtAvgBalLbl.text = SwtUtil.getPredictMessage('cashRsvrBal.targtAvgBal', null);
    this.fillDaysLbl.text = SwtUtil.getPredictMessage('cashRsvrBal.fillDays', null);
    this.fillBalLbl.text = SwtUtil.getPredictMessage('cashRsvrBal.fillBal', null);
    this.minTarBalanceLbl.text = SwtUtil.getPredictMessage('cashRsvrBal.minTargetBal', null);

    

    this.entityCombo.toolTip = SwtUtil.getPredictMessage('cashRsvrBal.tooltip.entity', null);
    this.currencyCombo.toolTip = SwtUtil.getPredictMessage('cashRsvrBal.tooltip.currency', null);
    this.accountCombo.toolTip = SwtUtil.getPredictMessage('cashRsvrBal.tooltip.account', null);
    this.accountTypeCombo.toolTip = SwtUtil.getPredictMessage('cashRsvrBal.tooltip.accountType', null);
    this.startDateField.toolTip = SwtUtil.getPredictMessage('cashRsvrBal.tooltip.startDate', null);
    this.endDateField.toolTip = SwtUtil.getPredictMessage('cashRsvrBal.tooltip.endDate', null);
    this.targtAvgBalTxtInput.toolTip = SwtUtil.getPredictMessage('cashRsvrBal.tooltip.targtAvgBal', null);
    this.fillBalTxtInput.toolTip = SwtUtil.getPredictMessage('cashRsvrBal.tooltip.fillDays', null);
    this.fillDaysTxtInput.toolTip = SwtUtil.getPredictMessage('cashRsvrBal.tooltip.fillBal', null);
    $('<form id="exportDataForm" target="tmp" method="post">				<input type="hidden" name="data" id="exportData" /> <input type="hidden" name="screen" id="exportDataScreen" value="CashRsvrBalManagmnt" />			</form>').appendTo('body')
  }

  


  drawRowBackground( dataContext, dataIndex, color, dateField): string {
    let colorFlag: string;
    try {
      if(dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.minBalReached && "balanceTarget"==dateField){
        colorFlag = '#ff6961';
      }else{
        colorFlag = (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.rowColor) ? dataContext.slickgrid_rowcontent.rowColor.content : "";
      }
      
      
      // if (colorFlag == "D") {
      //   rColor = "#AAAAAA";
      // } else if(colorFlag == "L") {
      //   rColor = "#DDDDDD";
      // } else {

      // }
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return colorFlag;
  }

  /**
   * onLoad
   * Upon completion of loading into the flash player this method is called
   **/
  onLoad(): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // this.cashRsvrBalGrid = <SwtCommonGrid>this.displaycontainer.addChild(SwtCommonGrid);

      this.cashRsvrBalGrid = this.displaycontainer.addChild(SwtTreeCommonGrid) as SwtTreeCommonGrid;
      this.cashRsvrBalGrid.listenHorizontalScrollEvent = true;
      this.cashRsvrBalGrid.hideHorizontalScrollBar = true;
      this.cashRsvrBalGrid.uniqueColumn = "seqN";
      this.cashRsvrBalGrid.useDummyColumn= true;
      setTimeout(() => {
        this.cashRsvrBalGrid.customContentFunction = this.gridsContentItemRender.bind(this);
      }, 0);

      this.cashRsvrBalGrid.rowColorFunction = ( dataContext, dataIndex, color, dateField ) => {
        return this.drawRowBackground( dataContext, dataIndex, color, dateField );
      };

      errorLocation = 10;
      this.requestParams = [];
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      errorLocation = 20;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 30;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      errorLocation = 40;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "cashRsrvBalManagmnt.do?";
      this.actionMethod = "method=data";
      this.requestParams["selectedTab"] = "current";
      this.requestParams['acctType'] = "O";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 50;
      this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'CashRsvrBalManagmnt.ts', "onLoad", errorLocation);
    }
  }

  /**
 * inputDataResult
 * @param event:ResultEvent
 * Communication Result Methods
 **/
  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.lastRecievedJSON = (event);
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON !== this.prevRecievedJSON)) {
          this.defaultCurrency = this.jsonReader.getSingletons().defaultCurrency;
          this.defaultEntity = this.jsonReader.getSingletons().defaultEntity;
          this.defaultAcctType = this.jsonReader.getSingletons().defaultAcctType;
          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.currencyCombo.setComboData(this.jsonReader.getSelects());
          this.accountCombo.setComboData(this.jsonReader.getSelects());
          this.accountTypeCombo.setComboData(this.jsonReader.getSelects());
          this.entityCombo.selectedLabel = this.defaultEntity;
          this.currencyCombo.selectedLabel = this.defaultCurrency;
          this.accountTypeCombo.selectedValue = this.defaultAcctType;
          this.entityDesc.text = this.entityCombo.selectedValue;
          this.currencyDesc.text = this.currencyCombo.selectedValue;
          this.accountDesc.text = this.accountCombo.selectedValue;
          this.dateFormat = this.jsonReader.getSingletons().dateFormat;
          // this.startDateField.formatString = this.dateFormat.toLowerCase();
          // this.endDateField.formatString = this.dateFormat.toLowerCase();
          this.startDateField.text = this.jsonReader.getSingletons().startDate;
           this.endDateField.text = this.jsonReader.getSingletons().endDate;
           this.fillBalTxtInput.text = this.jsonReader.getSingletons().fillBalance;
           this.targtAvgBalTxtInput.text = this.jsonReader.getSingletons().targetAvgBal;
           this.fillDaysTxtInput.text= this.jsonReader.getSingletons().fillDays;
           this.minTarBalanceTxtInput.text= this.jsonReader.getSingletons().minTarBalance;

          // Set the tab label to container
          // if (this.lastRecievedJSON.groupmonitor.tabs != "") {
          if (this.arrayOftabs.length == 0) {
            if (this.tabs.getTabChildren().length > 0) {
              for (let i = 0; i < this.arrayOftabs.length; i++) {
                this.tabs.removeChild(this.arrayOftabs[i]);
              }
            }
            this.displayContainerToday = <Tab>this.tabs.addChild(Tab);
            this.displayContainerTodayMinus = <Tab>this.tabs.addChild(Tab);
            this.displayContainerTodayMinusMinus = <Tab>this.tabs.addChild(Tab);
            // this.displayContainerTodayMinus.width = "150";
            // this.displayContainerTodayMinusMinus.width = "150";

            this.arrayOftabs = [];
            this.arrayOftabs = [this.displayContainerToday, this.displayContainerTodayMinus,
            this.displayContainerTodayMinusMinus];
            this.arrayOftabs[0].label = "Prior-1";
            this.arrayOftabs[1].label = "Prior";
            this.arrayOftabs[2].label = "Current";
            

            this.arrayOftabs[0].toolTip = "Show balances for the period before the previous maintenance period";
            this.arrayOftabs[1].toolTip = "Show balances for the previous maintenance period";
            this.arrayOftabs[2].toolTip = "Show balances and target balances for the current maintenance period";
            



            this.arrayOftabs[0].id = "priorprior";
            this.arrayOftabs[1].id = "prior";
            this.arrayOftabs[2].id = "current";
  
            this.arrayOftabs[0].setTabHeaderStyle("color", "black");
            this.arrayOftabs[1].setTabHeaderStyle("color", "black");
            this.arrayOftabs[2].setTabHeaderStyle("color", "black");
  
            this.displayContainerToday.setTabHeaderStyle("text-align","center");
  
            this.displayContainerToday.setTabHeaderStyle("width","150px");
            this.displayContainerTodayMinus.setTabHeaderStyle("width","150px");
            this.displayContainerTodayMinusMinus.setTabHeaderStyle("width","150px");
            this.tabs.selectedIndex=2;

          }

          this.displayContainerToday.enabled = true;
          this.displayContainerTodayMinus.enabled = true;
          if (!this.jsonReader.getSingletons().startDatePrior1State) {
            this.displayContainerToday.enabled = false;
            this.displayContainerTodayMinus.enabled = false;  
          } else if (!this.jsonReader.getSingletons().startDatePrior2State) {
            this.displayContainerToday.enabled = false;
          }

         

          // this.displayContainerTodayMinus.setTabHeaderStyle("width","150px");
          // this.displayContainerTodayMinusMinus.setTabHeaderStyle("width","150px");


          /*for (var i = 0; i < this.lastRecievedJSON.groupmonitor.tabs.predictdate.length; i++) {
            this.arrayOftabs[i].label = this.lastRecievedJSON.groupmonitor.tabs.predictdate[i].dateLabel;
            this.arrayOftabs[i].businessday = this.lastRecievedJSON.groupmonitor.tabs.predictdate[i].businessday;
            /*if(this.arrayOftabs[i].businessday ==false){
                this.arrayOftabs[i].setTabHeaderStyle("color","darkgray");
            }else
              this.arrayOftabs[i].setTabHeaderStyle("color","black");
          }*/
          //}

          // const obj = { columns: this.lastRecievedJSON.PreAdviceInput.preAdviceInputGrid.metadata.columns };
          // this.cashRsvrBalGrid.CustomGrid(obj);
          // var gridRows = this.lastRecievedJSON.PreAdviceInput.preAdviceInputGrid.rows;
          // if (gridRows.size > 0) {

          //   this.cashRsvrBalGrid.gridData = gridRows;

          //   this.cashRsvrBalGrid.setRowSize = this.jsonReader.getRowSize();
          // }
          // else {
          //   this.cashRsvrBalGrid.gridData = { size: 0, row: [] };
          // }
          this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
          let jsonList = this.jsonReader.getColumnData();
          let totalColumnData = JSON.parse(JSON.stringify(jsonList));
            for (let i = 0; i < totalColumnData.column.length; i++) {
              // header = SwtUtil.getAMLMessages(jsonList.column[i].heading);
            if (totalColumnData.column[i].dataelement == "valueDate") {
                totalColumnData.column[i].type = "str";
                break;
              }
            }
          const obj = { columns: jsonList };
          if (this.cashRsvrBalGrid === null || this.cashRsvrBalGrid === undefined) {
            this.cashRsvrBalGrid.componentID = this.jsonReader.getSingletons().screenid;
          }
          this.cashRsvrBalGrid.doubleClickEnabled = true;
        this.cashRsvrBalGrid.currencyFormat = this.currencyPattern;
          this.cashRsvrBalGrid.CustomGrid(obj);
          if (this.jsonReader.getGridData().size > 0) {
            this.cashRsvrBalGrid.dataProvider = null;
            this.cashRsvrBalGrid.gridData = this.jsonReader.getGridData();
            this.cashRsvrBalGrid.setRowSize = this.jsonReader.getRowSize();
            this.cashRsvrBalGrid.doubleClickEnabled = true;
          }else{
            this.cashRsvrBalGrid.gridData = {row: [], size: 0};
            this.cashRsvrBalGrid.dataProvider = null;
            this.cashRsvrBalGrid.selectedIndex = -1;
          }


        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'CashRsvrBalManagmnt.ts', "inputDataResult", errorLocation);
    }
  }

  public resetTree(){
    this.cashRsvrBalGrid.collapseAllTreeLevels();
  }

  public gridsContentItemRender(raw: any, dataField: string, value: string, type: string): string {
    let tmpValue = "";
    try {
      
     var  colorFlag = (raw.slickgrid_rowcontent && raw.slickgrid_rowcontent.rowColor) ? raw.slickgrid_rowcontent.rowColor.content : "";
      var originalValue: string = "" + value;
      if ((!raw["__collapsed"] && raw["indent"] == "0") || StringUtils.isTrue(raw["totalOrSweep"])) {
        if(raw.slickgrid_rowcontent && raw.slickgrid_rowcontent.minBalReached && "balanceTarget"==dataField)
          tmpValue += "<b style='background-color:#ff6961'>"+originalValue+"</b>"  ;
        else
          tmpValue += "<b style='background-color:"+colorFlag+"'>"+originalValue+"</b>"  ;
        
      }else{
        tmpValue = value;
      }
      return tmpValue;
    } catch (e) {

    }
  }



  validateDateFieldValue() {

  }
  checkDates() {

    /*try {
      let startDate: any;
      let endDate: any;
      let isOk= true;
      if (this.startDate.text) {
        startDate = moment(this.startDate.text, this.dateFormatValue.toUpperCase(), true);
      }
      if (this.endDate.text) {
        endDate = moment(this.endDate.text, this.dateFormatValue.toUpperCase(), true);
      }

      if (!startDate && endDate) {
        isOk = false;
      }

      if (startDate && endDate && endDate.isBefore(startDate)) {
        isOk = false;
      }

      return isOk;
    } catch (error) {
      // SwtUtil.logError(error, this.moduleId, "className", "changeEndDate", this.errorLocation);

    }*/
  }

  refreshComboList(fromField) {
    this.accountDesc.text = "";
    setTimeout(() => {
      this.updateData(fromField);
    }, 100);
  }

  updateData(fromField) {
    this.requestParams = [];
    this.requestParams["entityId"] = this.entityCombo.selectedItem ? this.entityCombo.selectedItem.content : "";
    this.requestParams["currencyCode"] = this.currencyCombo.selectedItem ? this.currencyCombo.selectedItem.content : "";
    this.requestParams["accountId"] = this.accountCombo.selectedItem ? this.accountCombo.selectedItem.content : "";


    this.requestParams["startDate"] = this.startDateField.text;
    this.requestParams["endDate"] = this.endDateField.text;
    this.requestParams['entityChanged'] = fromField.id == 'entityCombo' ? true : false;
    this.requestParams['ccyChanged'] = fromField.id == 'currencyCombo' ? true : false;
    this.requestParams['accChanged'] = fromField.id == 'accountCombo' ? true : false;
    if (fromField.id =='entityCombo' || fromField.id =='currencyCombo' || fromField.id =='accountCombo')
     {  this.requestParams['selectedTab'] = this.arrayOftabs[2].id;
        this.tabs.selectedIndex=2;
      }
    else this.requestParams['selectedTab'] = this.tabs.selectedTab.id;

    this.requestParams['acctType'] = this.accountTypeCombo.selectedValue;

    this.actionPath = "cashRsrvBalManagmnt.do?";
    this.actionMethod = "method=data";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


  /**                                                                                                                  
   * If a fault occurs with the connection with the server then display the lost connection label                      
   * @param event:FaultEvent                                                                                           
   **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  closeHandler(): void {
   
    ExternalInterface.call("close");
  }

  report(reportType){
    let selects = [];

    this.currencyCombo.selectedItem.content;
 this.accountCombo.selectedItem.content;
this.startDateField.text;
 this.endDateField.text;

 
    selects.push("Entity Id =" + this.entityCombo.selectedItem.content );
    selects.push("Currency Code =" + this.currencyCombo.selectedItem.content);
    selects.push("Account Id =" + this.accountCombo.selectedItem.content);
    selects.push("Maintenance period=");
    selects.push("     Start Date =" + this.startDateField.text);
    selects.push("     End Date =" + this.endDateField.text);
    selects.push("     Fill Balance =" + this.fillBalTxtInput.text);
    selects.push("     Target Average Balance =" + this.targtAvgBalTxtInput.text);
    selects.push("     Fill Days =" + this.fillDaysTxtInput.text);
    

    this.fillBalTxtInput.text = this.jsonReader.getSingletons().fillBalance;
    this.targtAvgBalTxtInput.text = this.jsonReader.getSingletons().targetAvgBal;
    this.fillDaysTxtInput.text= this.jsonReader.getSingletons().fillDays;


   this. exportContainer.convertData(this.lastRecievedJSON.CashRsvrBalManagmnt.grid.metadata.columns, this.cashRsvrBalGrid, null, selects, reportType, true, true);

  }

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: CashRsvrBalManagmnt }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CashRsvrBalManagmnt],
  entryComponents: []
})
export class CashRsvrBalManagmntModule { }
