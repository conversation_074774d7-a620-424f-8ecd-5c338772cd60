import { Router } from '@angular/router';
import { Component, ElementRef, ViewChild, OnInit, OnDestroy, NgModule } from '@angular/core';
import { Alert, CommonService, focusManager, HTTPComms, JSONReader, Keyboard, SwtAlert, SwtButton, SwtCanvas, SwtCommonGrid, SwtLoadingImage, SwtModule, SwtPopUpManager, SwtTextInput, SwtUtil, HBox, TitleWindow, SwtPanel, SwtComboBox, SwtLabel, ExternalInterface, SwtHelpWindow, XML, StringUtils, SwtToolBoxModule, ModuleLoader, ModuleEvent } from 'swt-tool-box';
//import data from '../../jsonFiles/dummySpreadProfilesMaintenanceAdd.json';
import { SpreadDetails } from './SpreadDetails/SpreadDetails';
import { FourEyesProcess } from '../../FourEyesProcess/FourEyesProcess';
import { Routes, RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/compiler/src/core';
declare var instanceElement: any;
@Component({
  selector: 'app-spread-profiles-maintenance-add',
  templateUrl: './SpreadProfilesMaintenanceAdd.html',
  styleUrls: ['./SpreadProfilesMaintenanceAdd.css']
})
export class SpreadProfilesMaintenanceAdd extends SwtModule implements OnInit, OnDestroy {

  private swtAlert: SwtAlert;
  private spreadDetailsGrid: SwtCommonGrid;
  private aggregateAccountsGrid: SwtCommonGrid;

  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];

  public screenName = null;
  public helpURL = null;
  public spreadId = null;
  public spreadName = null;
  public spreadCurrency = null;
  public errorLocation = 0;

  public time = null;
  public targetValue = null;
  public processCategories = null;
  public categories = null;
  public processName = null;
  public processPointsList: any ;
  public operationsList: XML = new XML( "<operationsList/>");
  
  private win:TitleWindow;

  /* - START -- Screen Name and Version number ---- */
  private moduleName = 'Spread Profile Maintenance - Add Details';
  private versionNumber = '1.00.00';
  private releaseDate = '12 March 2019';
  private moduleId = 'PCM';
  private timesList:Array<string>;
  private namesList:Array<string>;
  // private fourEyesRquired = true;
  private  maintEventId = null;
  public processId = null;
  public authOthers = false;

  private actionFromParent = null;
  private parentMenuAccess = null;
  private requireAuthorisation = true;
  private  canAmendFacility= false;
  /************SwtTextInput********/
  @ViewChild('spreadNameTxtInput') spreadNameTxtInput: SwtTextInput;
  @ViewChild('spreadIdTxtInput') spreadIdTxtInput: SwtTextInput;
  /***LodingImage*******/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /***********SwtCombo***************/
  @ViewChild('currencyComboBox') currencyComboBox: SwtComboBox;
  /*********SWtButton*************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('expressionBuilderButton') expressionBuilderButton: SwtButton;

  /*********SwtCanvas*************/
  @ViewChild('spreadDetailsCanvasGrid') spreadDetailsCanvasGrid: SwtCanvas;
  @ViewChild('aggregateAccountsCanvasGrid') aggregateAccountsCanvasGrid: SwtCanvas;
  /*********SwtPanel*************/
  @ViewChild('processPointsPanel') processPointsPanel: SwtPanel;
  /***********Swtlabel**************/
  @ViewChild('ccyLabel') ccyLabel: SwtLabel;
  /***Module****/
  @ViewChild('swtModule') swtModule: SwtModule;

  @ViewChild('hboxID') hboxID: HBox;


  @ViewChild('acceptButton') acceptButton: SwtButton;
  @ViewChild('rejectButton') rejectButton: SwtButton;
  @ViewChild('amendButton') amendButton: SwtButton;

  @ViewChild('cancelAmendButton') cancelAmendButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  constructor(private commonService: CommonService, private element: ElementRef, private router: Router) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }



  ngOnInit(): void {

    this.acceptButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.label', null);
    this.acceptButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.tooltip', null);

    this.rejectButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.label', null);
    this.rejectButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.tooltip', null);
    
    this.amendButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.amend.label', null);
    this.amendButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.amend.tooltip', null);

    this.cancelAmendButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.cancelAmendButton.toolTip = SwtUtil.getPredictMessage('tooltip.CancelChanges', null);

    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.entityMonitor.close', null);

    
    let paramsFromParent = [];
    if(window.opener.instanceElement) {
      paramsFromParent =  window.opener.instanceElement.getParamsFromParent();
      if(paramsFromParent) {
        this.screenName =  paramsFromParent[0].screenName;
        this.spreadId =  paramsFromParent[0].spreadId;
        if(paramsFromParent[0].maintEventId)
          this.maintEventId  = paramsFromParent[0].maintEventId;
        if(paramsFromParent[0].action)
          this.actionFromParent =paramsFromParent[0].action; 
        if(paramsFromParent[0].parentMenuAccess)
          this.parentMenuAccess =paramsFromParent[0].parentMenuAccess; 

          if(paramsFromParent[0].authOthers)
           this.authOthers =paramsFromParent[0].authOthers; 

           if(paramsFromParent[0].amendInFacilityAccess)
             this.canAmendFacility =paramsFromParent[0].amendInFacilityAccess; 
           


      }
    }else {
    this.screenName =  'change';
      this.spreadId =  'testFull';
      this.maintEventId = '138'
    }
    // this.screenName =  'add';
    instanceElement = this;


    this.addButton.label = "Add";
    this.changeButton.label = "Change";
    this.viewButton.label = "View";
    this.deleteButton.label = "Delete";
    this.saveButton.label = "Save";
    this.cancelButton.label = "Cancel";

    this.disableOrEnableButtons(false);

    if (this.screenName == "add") {
      this.spreadIdTxtInput.setFocus();
      this.addButton.enabled = true;
    } else if (this.screenName == "view") {
      this.spreadNameTxtInput.enabled = false;
      this.spreadIdTxtInput.enabled = false;
      this.saveButton.visible = this.saveButton.includeInLayout = false;
      this.addButton.enabled = false;
    } else if (this.screenName == "change") {
      this.spreadNameTxtInput.enabled = true;
      this.spreadIdTxtInput.enabled = false;
      this.spreadNameTxtInput.setFocus();
      this.addButton.enabled = true;
    }
  }

  destoyAllTooltips(){
    $(".ui-tooltip" ).each(function( index ) {
        $(this).remove();
    });
  }

  amendEventHandler(){
    this.destoyAllTooltips();
    window.opener.instanceElement.setViewOrAmendSubScreenFromChild('change');
    const currentUrl = this.router.url;
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigateByUrl("/spreadProfilesAdd");
    });

  }

  cancelAmendEventHandler(){
    this.destoyAllTooltips();
    window.opener.instanceElement.setViewOrAmendSubScreenFromChild('view');
    const currentUrl = this.router.url;
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigateByUrl("/spreadProfilesAdd");
    });

  }

  /**
   * onLoad
   * Initializer (multilingual, fetch list details )
   */
  onLoad(): void {
    try {

     
      if(this.maintEventId && StringUtils.isTrue(this.authOthers) && this.screenName != "change"){
        this.rejectButton.visible = true;
        this.acceptButton.visible = true;
      }

      if(this.maintEventId  && this.screenName == "view"){
        this.amendButton.visible = true;
        this.amendButton.includeInLayout = true;
        this.saveButton.visible = false;
        this.saveButton.includeInLayout = false;
        if(StringUtils.isTrue(this.canAmendFacility)){
          this.amendButton.enabled = true;
          this.rejectButton.visible = true;
        }else {
          this.amendButton.enabled = false;
        }
      }else {
        if(this.maintEventId){
          this.saveButton.visible = true;
          this.saveButton.includeInLayout = true;
          this.cancelAmendButton.visible = true;
          this.cancelAmendButton.includeInLayout = true;

          this.closeButton.visible = true;
          this.closeButton.includeInLayout = true;

          this.cancelButton.visible = false;
          this.cancelButton.includeInLayout = false;

          this.amendButton.visible = false;
          this.amendButton.includeInLayout = false;
        }
      }
      
      this.timesList = [];
      this.namesList = [];
      this.spreadDetailsGrid = <SwtCommonGrid>this.spreadDetailsCanvasGrid.addChild(SwtCommonGrid);
      this.aggregateAccountsGrid = <SwtCommonGrid>this.aggregateAccountsCanvasGrid.addChild(SwtCommonGrid);
      this.spreadDetailsGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };
      this.spreadDetailsGrid.rowColorFunction = ( dataContext, dataIndex, color, dataField ) => {
        return this.drawRowBackground( dataContext, dataIndex, color , dataField);
      };
      this.requestParams = [];
      this.actionPath = 'spreadProfilesPCM.do?';
      this.requestParams["moduleId"] = this.moduleId;
      if (this.screenName != "add") {
        this.requestParams["spreadId"] = this.spreadId;
        this.actionMethod = "method=displayChangeOrView";
      } else {
        this.actionMethod = "method=displayAdd";
      }

      this.requestParams["isFromMaintenanceEvent"] = this.maintEventId?"true":"false";
      this.requestParams["maintEventId"] = this.maintEventId;

      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);

      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      //this.inputDataResult(data);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "onLoad", this.errorLocation);
    }
  }


  drawRowBackground( dataContext, dataIndex, color,dataField ): string {
  
    let rColor: string;
    try {
      let colorFlag: string;
    // if(dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.entity){
      if('Y'==dataContext.slickgrid_rowcontent[dataField].isDeletedRow){
        rColor = "#ff808a";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isNewRow){
        rColor = "#c6efce";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isUpdatedRow){
        rColor = "#ee82ee";
     }
      
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }
  /**
   * startOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    this.disableComponents(false);
    this.loadingImage.setVisible(true);
  }

  /**
   * endOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.disableComponents(true);
  }

  /**
   * inputDataResult
   * param event: ResultEvent
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(data): void {
    try {
      let jsonList = null;
      let header: string;
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        /* Get result as xml */
        this.lastRecievedJSON = data;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        /* Condition to check lastRecievedXML not equal to prevRecievedXML */
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          /* Condition to check request reply status is true*/
          if (this.jsonReader.getRequestReplyMessage()) {
            //gets the help url
            if (!this.jsonReader.isDataBuilding()) {
              if (this.screenName != "add") {
                this.spreadName = this.jsonReader.getSingletons().spreadName;
                this.spreadCurrency = this.jsonReader.getSingletons().ccy;
              }
              // this.fourEyesRquired = this.jsonReader.getScreenAttributes()["fourEyesRequired"];
              this.requireAuthorisation = this.jsonReader.getScreenAttributes()["requireAuthorisation"];
              // Currency Combobox
              this.currencyComboBox.setComboData(this.jsonReader.getSelects(), false);
              this.ccyLabel.text = this.currencyComboBox.selectedItem.value;
              // Spread Details Grid
              this.spreadDetailsGrid.doubleClickEnabled = false;
              this.spreadDetailsGrid.CustomGrid(data.SpreadProfilesMaintenanceAdd.gridSpreadProcessPoints.metadata);
              if (data.SpreadProfilesMaintenanceAdd.gridSpreadProcessPoints.rows.size != undefined) {
                this.spreadDetailsGrid.gridData = data.SpreadProfilesMaintenanceAdd.gridSpreadProcessPoints.rows;
                this.spreadDetailsGrid.setRowSize = data.SpreadProfilesMaintenanceAdd.gridSpreadProcessPoints.rows.size;
              } else {
                this.spreadDetailsGrid.dataProvider = [];
              }
              if (this.screenName != "add") {
                // Aggregate Account Grid
                this.aggregateAccountsGrid.CustomGrid(data.SpreadProfilesMaintenanceAdd.gridAccountGroups.metadata);
                this.aggregateAccountsGrid.doubleClickEnabled = false;
                if (data.SpreadProfilesMaintenanceAdd.gridAccountGroups.rows.size != undefined) {
                  this.aggregateAccountsGrid.gridData = data.SpreadProfilesMaintenanceAdd.gridAccountGroups.rows;
                  this.aggregateAccountsGrid.setRowSize = data.SpreadProfilesMaintenanceAdd.gridAccountGroups.rows.size;
                } else {
                  this.aggregateAccountsGrid.dataProvider = null;
                  this.aggregateAccountsGrid.selectedIndex = -1;
                }
              }

              if (this.screenName != "add") {
                this.spreadNameTxtInput.text = this.spreadName;
                if(this.maintEventId && this.jsonReader.getSingletons().spreadName_oldValue != this.jsonReader.getSingletons().spreadName){
                  this.spreadNameTxtInput.toolTipPreviousValue = this.jsonReader.getSingletons().spreadName_oldValue;
                }
                this.spreadIdTxtInput.text = this.spreadId;
                this.currencyComboBox.selectedLabel = this.spreadCurrency;
                this.ccyLabel.text = this.currencyComboBox.selectedItem.value;
              }
                this.swtModule.subscribeSpy([this.spreadDetailsGrid]);
                this.swtModule.onSpyChange.subscribe((e) => {
                  this.processPointsList = this.spreadDetailsGrid.changes;
                });
            }
          }
        }
      }
    } catch (error) {
      console.log("error:   ", error);
      SwtUtil.logError(error, this.moduleId, "className", "inputDataResult", this.errorLocation);
    }
  }

  /**
   * inputDataFault
   * param event:  FaultEvent
   * This is a callback , used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    this.swtAlert.error(event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail);
  }

  saveEventHandler() {
    if (this.spreadIdTxtInput.text.length == 0 || this.spreadNameTxtInput.text.length == 0) {
      this.swtAlert.warning('Please fill all mandatory fields (marked with *)');
      return;
    }
    if (this.spreadNameTxtInput.text.trim() == "") {
      this.swtAlert.warning('Spread Profile Name can not be saved as just spaces');
      return;
    }
    if (this.spreadDetailsGrid.dataProvider.length == 0) {
      this.swtAlert.warning('Cannot add a spread profile without any process point !');
      return;
    }
    // if (this.fourEyesRquired == true) {
    //   this.win = SwtPopUpManager.createPopUp(this, FourEyesProcess, {
    //     title: SwtUtil.getPredictMessage("label.fourEyes", null),
    //   });
    //   this.win.enableResize = false;
    //   this.win.width = '510';
    //   this.win.height = '215';
    //   this.win.showControls = true;
    //   this.win.isModal = true;
    //   this.win.onClose.subscribe((res) => {
    //     if (this.win.getChild().result) {
    //       if (this.win.getChild().result.login == "SUCCESS") {
    //         this.save();
    //       }
    //     }
    //   });
    //   this.win.display();
    // } else {
      this.save();
    // }

  }
  /**
   * save()
   * Method called on save button clicked
   */
  save(): void {
    // Convert required data of the spread process point grid to XML before sending
    this.xmlDataProcessPoints();
    try {
      this.actionMethod = "method=save";

      this.logicUpdate.cbResult = (event) => {
        this.logicUpdateResult(event);
      };
      this.requestParams = [];
      if("I" == this.actionFromParent)
        this.requestParams["screenName"] = "add";
      else 
        this.requestParams["screenName"] = this.screenName;
        
      this.requestParams["spreadProfileId"] = this.spreadIdTxtInput.text;
      this.requestParams["spreadProfileName"] = this.spreadNameTxtInput.text;
      this.requestParams["currencyCode"] = this.currencyComboBox.selectedLabel;
      this.requestParams['xmlData'] = this.operationsList.toString();
      this.requestParams["maintEventId"] = this.maintEventId;
      this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
      this.logicUpdate.send(this.requestParams);

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "save", this.errorLocation);
    }
  }

  /**
   * Method called to get an XML list of changed/addded/deleted data of spread process points 
   * 
   */
  xmlDataProcessPoints(): any {
    let row = [];
    this.operationsList = new XML( "<operationsList/>");
    if (this.processPointsList != undefined) {
      for (var i = 0; i < this.processPointsList.getValues().length; i++) {
        row = [];
        row['PROCESS_ID'] = this.processPointsList.getValues()[i].crud_data.processId;
        row['PROCESS_NAME'] = this.processPointsList.getValues()[i].crud_data.processName;
        row['TIME'] = this.processPointsList.getValues()[i].crud_data.time;
        row['TARGET'] = this.processPointsList.getValues()[i].crud_data.target;
        row['PROCESS'] = this.processPointsList.getValues()[i].crud_data.process;
        row['CATEGORIES'] = this.processPointsList.getValues()[i].crud_data.categories;
        if (this.processPointsList.getValues()[i].crud_operation == "I") {
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_SPREAD_PROCESS_POINT', 'I', 'M'));
        }
        if (this.processPointsList.getValues()[i].crud_operation.substring(0, 1) == "U") {
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_SPREAD_PROCESS_POINT', 'U', 'M'));
        }
        if (this.processPointsList.getValues()[i].crud_operation.substring(0, 1) == "D") {
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_SPREAD_PROCESS_POINT', 'D', 'M'));
        }
      }
    }
  }

  /**
   * logicUpdateResult
   *
   * @param event: ResultEvent
   *
   * Method to get result of the group rules
   */
  logicUpdateResult(event): void {
    try {
      const JsonResponse = event;
      const JsonResult: JSONReader = new JSONReader();
      JsonResult.setInputJSON(JsonResponse);
      if (JsonResult.getRequestReplyStatus()) {
        this.updateData();
      } else {
        if (JsonResult.getRequestReplyMessage() ==  "errors.DataIntegrityViolationExceptioninAdd") {
          this.swtAlert.error("Unable to save, spread Profile Id already exists");
        } else if (JsonResult.getRequestReplyMessage() ==  "errors.processPointTimeNotInRange") {
          let finalAlert = "Could not add a spread process point with time outside <br>"
          + "kick-off and End of Intraday Release Phase for the following account groups : <br>";
          let accountGroups: String = JsonResult.getRequestReplyLocation();
          let accountGroupsList = [];
          let index = 0;
          if (accountGroups.indexOf(',') != -1) {
            accountGroupsList[0] = accountGroups.substr(0, accountGroups.indexOf(',') + 1);
            accountGroups = accountGroups.substr(accountGroups.indexOf(',') + 1);
            index = accountGroups.indexOf(',');
            if (accountGroups.indexOf(',') == -1) {
              accountGroupsList[0] += accountGroups;
            }
          } else {
            accountGroupsList[0] = accountGroups;
          }
          let i = 0;
          let totalNumber = 1;
          while (accountGroups.indexOf(',') != -1) {
            if (totalNumber == 3) {
              i++;
              accountGroupsList[i] = "";
              totalNumber = 0;
            }
            accountGroupsList[i] += accountGroups.substr(0, accountGroups.indexOf(',') + 1);
            totalNumber++;
            accountGroups = accountGroups.substr(index + 1);
            index = accountGroups.indexOf(',');
            if (index == -1) {
              accountGroupsList[i] += accountGroups;
            }
          }
          for (let index = 0; index < accountGroupsList.length; index++) {
            finalAlert +=accountGroupsList[index] + "<br>";
          }
          this.swtAlert.error(finalAlert);
        } else {
          this.swtAlert.error(JsonResult.getRequestReplyMessage());
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'logicUpdateResult', this.errorLocation);
    }
  }


  /**
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  public updateData(): void {
    try {
      if(StringUtils.isTrue(this.requireAuthorisation)){
        this.swtAlert.show(SwtUtil.getPredictMessage("maintenanceevent.details.alert.actionneedauthorisation", null), "Warning", Alert.OK, null, this.closeWindow.bind(this));
      }else {
      if(this.maintEventId){
        if(window.opener && window.opener.opener && window.opener.opener.instanceElement) {
          window.opener.opener.instanceElement.updateData();
        }
        if(window.opener && window.opener.instanceElement) {
          window.opener.close();
        }
    
        window.close();
      }else {
        if (window.opener.instanceElement) {
          window.opener.instanceElement.updateData();
        }
        this.closeBtn();
      }
    }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'ClassName', 'updateData', this.errorLocation);
    }
  }


  /**
   * disableComponents()
   *
   * Method called to disable components
   *
   */
  disableComponents(value): void {
    try {

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'disableComponents', this.errorLocation);
    }
  }

  /**
   * keyDownEventHandler
   * param event: KeyboardEvent
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  keyDownEventHandler(event: KeyboardEvent): void {
    try {
      //Currently focussed property name
      let eventString: string = Object(focusManager.getFocus()).name;
      if ((event.keyCode == Keyboard.ENTER)) {
        if (eventString == "saveButton") {
          this.saveEventHandler();
        } else if (eventString == "cancelButton") {
          this.closeBtn();
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "className", "keyDownEventHandler");
    }
  }

  cellClickEventHandler(event: Event): void {
    try {
      if (this.spreadDetailsGrid.selectedIndices.length === 1 && this.spreadDetailsGrid.selectable) {
        // Condition to check menuacess is not full
        this.disableOrEnableButtons(true);
      } else {
        this.disableOrEnableButtons(false);
      }
      event.stopPropagation();
      this.addButton.setFocus();
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'cellClickEventHandler', this.errorLocation);
    }
  }

  disableOrEnableButtons(isRowSelected: Boolean): void {
    if (isRowSelected) {
      this.changeButton.enabled = (this.screenName != 'view') ? true : false;
      this.viewButton.enabled = true;
      this.deleteButton.enabled = (this.screenName != 'view') ? true : false;
    } else {
      this.changeButton.enabled = false;
      this.viewButton.enabled = false;
      this.deleteButton.enabled = false;
    }
  }

  /**
   * doAddSpreadDetails
   *
   * @param event: Event
   *
   * Method to open Spread details screen in add mode
   */
  doAddSpreadDetails(event): void {
    try {
      this.timesList = [];
      this.namesList = [];
      for (let index = 0; index < this.spreadDetailsGrid.dataProvider.length; index++) {
        this.timesList.push(this.spreadDetailsGrid.dataProvider[index].time);
        this.namesList.push(this.spreadDetailsGrid.dataProvider[index].processName);
      }
      this.win = SwtPopUpManager.createPopUp(this);
      this.win.title = "Spread Process Points - Add";
      this.win.isModal = true;
      this.win.width = "620";
      this.win.height = "500";
      this.win.id = "spreadDetails";
      this.win.showControls = true;
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win['screenName'] = 'add';
      this.win['moduleId'] = this.moduleId;
      this.win['timesList'] = this.timesList;
      this.win['namesList'] = this.namesList;

      const mLoader = new ModuleLoader(this.commonService);
      mLoader.addEventListener(ModuleEvent.READY, (event) => this.moduleReadyEventHandler(event, 'add'));
      mLoader.loadModule("spreadDetails");
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error add', e);
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doAddSpreadDetails', this.errorLocation);
    }
  }

  /**
   * doChangeSpreadDetails
   *
   * @param event: Event
   *
   * Method to open Spread details screen in change mode
   */
  doChangeSpreadDetails(event): void {
    try {
      let itemIndex = this.spreadDetailsGrid.dataProvider.findIndex(x=>x.num == this.spreadDetailsGrid.selectedItem.num.content);
      this.timesList = [];
      this.namesList = [];
      this.time = this.spreadDetailsGrid.dataProvider[itemIndex].time;
      this.targetValue = this.spreadDetailsGrid.dataProvider[itemIndex].target;
      this.processCategories = this.spreadDetailsGrid.dataProvider[itemIndex].process;
      this.categories = this.spreadDetailsGrid.dataProvider[itemIndex].categories;
      this.processName = this.spreadDetailsGrid.dataProvider[itemIndex].processName;
      this.processId = this.spreadDetailsGrid.dataProvider[itemIndex].processId;

      for (let index = 0; index < this.spreadDetailsGrid.dataProvider.length; index++) {
        
        if (this.spreadDetailsGrid.dataProvider[index].time != this.time) {
          this.timesList.push(this.spreadDetailsGrid.dataProvider[index].time);
        }
        if (this.spreadDetailsGrid.dataProvider[index].processName != this.processName) {
          this.namesList.push(this.spreadDetailsGrid.dataProvider[index].processName);
        }
      }

      this.win = SwtPopUpManager.createPopUp(this);
      this.win.title = "Spread Process Points - Change";
      this.win.width = "620";
      this.win.height = "500";
      this.win.id = "spreadDetails";
      this.win.isModal = true;
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win['screenName'] = 'change';
      this.win['moduleId'] = this.moduleId;
      this.win['processName'] = this.processName;
      this.win['selectedDataRow'] =  this.spreadDetailsGrid.dataProvider[itemIndex];
      this.win['spreadId'] = this.spreadId;
      this.win['processId'] = this.processId;
      this.win['maintEventId'] = this.maintEventId;
      this.win['time'] = this.time;
      this.win['targetValue'] = this.targetValue;
      this.win['processCategories'] = this.processCategories;
      this.win['categories'] = this.categories;
      this.win['timesList'] = this.timesList;
      this.win['namesList'] = this.namesList;

      const mLoader = new ModuleLoader(this.commonService);
      mLoader.addEventListener(ModuleEvent.READY, (event) => this.moduleReadyEventHandler(event, 'change'));
      mLoader.loadModule("spreadDetails");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doChangeSpreadDetails', this.errorLocation);     
    }
  }

  /**
   * doViewSpreadDetails
   *
   * @param event: Event
   *
   * Method to open Spread details screen in view mode
   */
  doViewSpreadDetails(event): void {
    try {
      let itemIndex = this.spreadDetailsGrid.dataProvider.findIndex(x=>x.num == this.spreadDetailsGrid.selectedItem.num.content);
      this.time = this.spreadDetailsGrid.dataProvider[itemIndex].time;
      this.targetValue = this.spreadDetailsGrid.dataProvider[itemIndex].target;
      this.processCategories = this.spreadDetailsGrid.dataProvider[itemIndex].process;
      this.categories = this.spreadDetailsGrid.dataProvider[itemIndex].categories;
      this.processName = this.spreadDetailsGrid.dataProvider[itemIndex].processName;
      

      this.win = SwtPopUpManager.createPopUp(this);
      this.win.title = "Spread Process Points - View";
      this.win.width = "620";
      this.win.height = "500";
      this.win.id = "spreadDetails";
      this.win.isModal = true;
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win['screenName'] = 'view';
      this.win['moduleId'] = this.moduleId;
      this.win['processName'] = this.processName;
      this.win['time'] = this.time;
      this.win['targetValue'] = this.targetValue;
      this.win['processCategories'] = this.processCategories;
      this.win['categories'] = this.categories;

      const mLoader = new ModuleLoader(this.commonService);
      mLoader.addEventListener(ModuleEvent.READY, (event) => this.moduleReadyEventHandler(event, 'view'));
      mLoader.loadModule("spreadDetails");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doViewSpreadDetails', this.errorLocation);
    }
  }

  private moduleReadyEventHandler(event, screenName) {
    this.win.addChild(event.target);
    this.win.display();
    if (screenName == 'add') {
      this.win.onClose.subscribe(() => {
        if (this.win.getChild().result != undefined && this.win.getChild().result != null) {
          var newRow = {
            'time': { 'content': String(this.win.getChild().result.time) },
            'processName': { 'content': this.win.getChild().result.processName },
            'process': { 'content': this.win.getChild().result.process },
            'categories': { 'content': this.win.getChild().result.categories },
            'target': { 'content': this.win.getChild().result.target },
          };
          this.spreadDetailsGrid.appendRow(newRow);
          this.changeButton.enabled = true;
          this.viewButton.enabled = true;
          this.deleteButton.enabled = true;
        }
      });
    } else if (screenName == 'change') {
      let itemIndex = this.spreadDetailsGrid.dataProvider.findIndex(x => x.num == this.spreadDetailsGrid.selectedItem.num.content);
      let previousSelectedIndex = this.spreadDetailsGrid.selectedIndex;
      this.win.onClose.subscribe(() => {
        if (this.win.getChild().result != undefined && this.win.getChild().result != null) {
          this.spreadDetailsGrid.dataProvider[itemIndex].processName = this.win.getChild().result.processName;
          this.spreadDetailsGrid.dataProvider[itemIndex].time = this.win.getChild().result.time;
          this.spreadDetailsGrid.dataProvider[itemIndex].target = this.win.getChild().result.target;
          this.spreadDetailsGrid.dataProvider[itemIndex].process = this.win.getChild().result.process;
          this.spreadDetailsGrid.dataProvider[itemIndex].categories = this.win.getChild().result.categories;
          this.spreadDetailsGrid.dataProvider[itemIndex].slickgrid_rowcontent.processName.content = this.win.getChild().result.processName;
          this.spreadDetailsGrid.dataProvider[itemIndex].slickgrid_rowcontent.time.content = this.win.getChild().result.time;
          this.spreadDetailsGrid.dataProvider[itemIndex].slickgrid_rowcontent.target.content = this.win.getChild().result.target;
          this.spreadDetailsGrid.dataProvider[itemIndex].slickgrid_rowcontent.process.content = this.win.getChild().result.process;
          this.spreadDetailsGrid.dataProvider[itemIndex].slickgrid_rowcontent.categories.content = this.win.getChild().result.categories;
          this.spreadDetailsGrid.refresh();
          this.spreadDetailsGrid.selectedIndex = previousSelectedIndex;
        }
      });
    }
  }


  /**
   *
   * Delete selected spread details from the grid
   *
   */
  doDeleteSpreadDetails(): void {
    try {
      this.swtAlert.confirm('Do you wish to delete this row', 'Alert', Alert.YES | Alert.NO, null, this.gridRowRemove.bind(this));
    } catch (e) {
      console.log('error in delete', e);
    }
  }

  /**
   * Remove the row from the grid after confirming by user
   *
   * @param {*} event
   */
  gridRowRemove(event): void {
    if (event.detail === Alert.YES) {
      for (let index = 0; index < this.timesList.length; index++) {
        if (this.timesList[index] == this.spreadDetailsGrid.dataProvider[this.spreadDetailsGrid.selectedIndex].time) {
          this.timesList.splice(index, 1); 
          break;
        }
      }
      for (let index = 0; index < this.namesList.length; index++) {
        if (this.namesList[index] == this.spreadDetailsGrid.dataProvider[this.spreadDetailsGrid.selectedIndex].processName) {
          this.namesList.splice(index, 1);
          break;
        }
      }
      this.spreadDetailsGrid.removeSelected();
      //this.spreadDetailsGrid.refresh();
      this.spreadDetailsGrid.selectedIndex = -1;
      this.changeButton.enabled = false;
      this.deleteButton.enabled = false;
      this.viewButton.enabled = false;

    }
  }

  /**
   * popupClosed
   * Method to close child windows when this screen is closed
   */
  popupClosed(): void {
    this.dispose();
  }

  /**
   * dispose
   * This is a event handler, used to close the current tab/window
   */
  dispose():void {
    try {
      this.requestParams=null;
      this.baseURL=null;
      this.actionMethod=null;
      this.actionPath=null;
      if(this.titleWindow){
        this.close();
      }else {
        window.close();
      }
    } catch (error) {
      console.log(error, this.moduleId, "className", "dispose");
    }
  }

  /**
   * close
   * param event
   *  called on click of close button to close the window
   */
  closeBtn(): void {
    try {
      this.dispose();
    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "closeBtn", this.errorLocation);
    }
  }

  changeComboCurrency(event): void {
    this.ccyLabel.text = this.currencyComboBox.selectedItem.value;
  }

  /**
   * printPage
   *
   * param event
   *
   * Method to get call the action to get reports
   */
  printPage(): void {
    try {
      this.actionMethod = "type=" + "pdf";
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod = this.actionMethod + "&currentModuleId=" + this.moduleId;
      this.actionMethod = this.actionMethod + "&print=" + "PAGE";
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", this.errorLocation);
    }
  }

  /**
  * doHelp
  * Function is called when "Help" button is click. Displays help window
  */
  doHelp(): void {
    try {
      SwtHelpWindow.open(this.baseURL + "help/aml/fr/index.html#page=groupes-des-regles.html");
      SwtHelpWindow.resizable(true);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }
  }


  acceptEventEventHandler(): void {
    // const message = SwtUtil.getPredictMessage('alert.columndelete', null);
    const message = SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoaccept', null);
    this.swtAlert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.acceptStatusHandler.bind(this), null);
}

rejectEventEventHandler(): void {
  // const message = SwtUtil.getPredictMessage('alert.columndelete', null);
  const message = SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoreject', null);
  this.swtAlert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.rejectStatusHandler.bind(this), null);
}

acceptStatusHandler(closeEvent): void {
    if (closeEvent.detail == Alert.YES) {
      if (window.opener && window.opener.instanceElement) {
        this.changeStatusHandler('A');
      }
    }
  }
  rejectStatusHandler(closeEvent): void {
    if (closeEvent.detail == Alert.YES) {
      if (window.opener && window.opener.instanceElement) {
        this.changeStatusHandler('R');
      }
    }
  }




updateMaintenanceEventStatusResult(event):void {
  let errorLocation = 0;
  try {
  // Checks the inputData and stops the communication
  if (this.inputData.isBusy()) {
    this.inputData.cbStop();
  } else {
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    errorLocation = 10;
    //this.dataExport.enabled = true;
    if (this.jsonReader.getRequestReplyStatus()) {
      this.swtAlert.show(SwtUtil.getPredictMessage("maintenanceevent.details.alert.actionperfermored", null), "Warning", Alert.OK, null, this.closeWindow.bind(this));
      
    } else {
      if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }

      }
    }

  } catch (error) {
    // log the error in ERROR LOG
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "cellClickEventHandler", errorLocation);
  }
    
}


closeWindow(event) {
  if(event.detail == Alert.OK) {
    //refresh parent
    if(this.maintEventId){
      if(window.opener && window.opener.opener && window.opener.opener.instanceElement) {
        window.opener.opener.instanceElement.updateData();
      }
      if(window.opener && window.opener.instanceElement) {
        window.opener.close();
      }
  
      window.close();
    }else {
      if (window.opener.instanceElement) {
        window.opener.instanceElement.updateData();
      }
      this.closeBtn();
    }
  }
}




changeStatusHandler(action) {
  let errorLocation = 0;
  try {
    this.actionPath = "maintenanceEvent.do?";
    this.actionMethod = 'method=updateMaintenanceEventStatus';
    errorLocation = 50;
    this.requestParams = [];
    this.requestParams['menuAccessId'] = this.parentMenuAccess;
    errorLocation = 60;
    this.requestParams['maintEventId'] =  this.maintEventId;
    errorLocation = 70;
    this.requestParams['action'] =  action;

    this.inputData.cbResult = (event) => {
      this.updateMaintenanceEventStatusResult(event);
    };
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 100;
    this.inputData.send(this.requestParams);
  } catch (error) {

    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "onLoad", errorLocation);
  }


}

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: SpreadProfilesMaintenanceAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SpreadProfilesMaintenanceAdd],
  entryComponents: []
})
export class SpreadProfilesMaintenanceAddModule { }
