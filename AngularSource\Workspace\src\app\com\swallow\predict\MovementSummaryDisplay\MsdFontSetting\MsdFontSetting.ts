import { Component, ElementRef, ViewChild } from '@angular/core';
import {
  SwtModule,
  CommonService,
  Logger,
  SwtUtil,
  SwtButton,
  SwtLabel,
  SwtRadioItem, SwtRadioButtonGroup, SwtCheckBox, SwtComboBox, SwtAlert
} from "swt-tool-box";

declare var instanceElement2: any;

@Component({
  selector: 'app-font-setting',
  templateUrl: './MsdFontSetting.html',
  styleUrls: ['./MsdFontSetting.css']
})
export class MsdFontSetting extends SwtModule {
  /*----------------------------------------declare here all the screen private variable.-------------------------------------*/

  private logger: Logger;
  public fontSizeValue = 0;
  public openFlag=false;
  private swtAlert: SwtAlert;
  /*----------------------------------------import all the screen controls here.-------------------------------------*/
  // import close button

  @ViewChild("secondsLabel") secondsLabel: SwtLabel;
  @ViewChild("fonSizeLabel") fonSizeLabel: SwtLabel;

  @ViewChild("fontSize") fontSize: SwtRadioButtonGroup;
  @ViewChild("normalFontSize") normalFontSize: SwtRadioItem;
  @ViewChild("smallFontSize") smallFontSize: SwtRadioItem;

  @ViewChild("okButton") okButton: SwtButton;
  @ViewChild("cancelButton") cancelButton: SwtButton; 
  @ViewChild("configButton") configButton: SwtButton;

  @ViewChild("addColsLabel") addColsLabel: SwtButton;
  @ViewChild("addColscheck") addColscheck: SwtCheckBox;
  @ViewChild("addColsCombo") addColsCombo: SwtComboBox;

  showCloseButton: boolean;
  public addColsGridData=[];
// Screen constructor.
  constructor(private element: ElementRef, private commonService: CommonService) {
    super(element, commonService);
    // instantiate the logger object.
    this.logger = new Logger("FontSetting", commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit(){
    //instanceElement= this;
    this.addColsCombo.toolTip= SwtUtil.getPredictMessage('msdAdditionalColumns.profileComboTooltip', null);
    this.configButton.label= SwtUtil.getPredictMessage('button.msd.configure.label', null);
    this.configButton.toolTip= SwtUtil.getPredictMessage('button.tooltip.msd.configure', null);
    this.addColscheck.toolTip= SwtUtil.getPredictMessage('msdAdditionalColumns.useAddColsCheckTooltip', null); 
  }
  // Method called in starting of the screen
  onLoad() {
    instanceElement2= this;

    this.logger.info("Method onLoad - Start.");
    // Variable for errorLocation
    let errorLocation=0;
    try {
      this.addColsCombo.dataProvider=this.parentDocument.profilesList?this.parentDocument.profilesList:[];
      this.addColsCombo.setComboData(this.parentDocument.profilesList?this.parentDocument.profilesList:[]);
      this.addColsCombo.selectedLabel=this.parentDocument.currentProfile?this.parentDocument.currentProfile:"<None>";
      this.addColscheck.selected= this.parentDocument.useAddColsCheck =="N"?false:true;
      this.okButton.toolTip= SwtUtil.getPredictMessage('tooltip.save', null);
      this.okButton.label= SwtUtil.getPredictMessage('button.ok', null);
      this.cancelButton.label= SwtUtil.getPredictMessage('button.close', null);
      this.cancelButton.toolTip= SwtUtil.getPredictMessage('tooltip.close', null);
      this.cancelButton.setStyle("styleName","flexButton");
      this.logger.info("Method onLoad End.");
      
      if(this.fontSizeValue == 0) {
        this.fontSize.selectedValue = "N";
      } else {
        this.fontSize.selectedValue = "S";
      }
    } catch(error) {
      console.log("onLoad====",error);
      
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this)  , "init", errorLocation);
    }
  }

  removePopUp() {

    this.close();
  }
  saveResult(): void {
    let fontLabel;
    if(this.fontSize.selectedValue == "N") {
      fontLabel = this.normalFontSize.label;
    } else {
      fontLabel = this.smallFontSize.label;
    }
    this.result = {fontSize:  {value: this.fontSize.selectedValue , label:fontLabel }};
    this.removePopUp();
    this.parentDocument.saveLastProfile(this.addColsCombo.selectedLabel, this.addColscheck.selected?"Y":"N");
    this.parentDocument.addColumnsGridData = this.addColsGridData;
  }

  openAdditionalColsScreen(){
    this.parentDocument.addColumn(this.addColsCombo.selectedLabel, "settingScreen");
  }

  updateProfileCombo(updatedList, selectedProfile){
    this.addColsCombo.dataProvider=updatedList?updatedList:this.parentDocument.profilesList;
    this.addColsCombo.setComboData(updatedList?updatedList:this.parentDocument.profilesList);
    this.addColsCombo.selectedLabel=this.parentDocument.currentProfile?this.parentDocument.currentProfile:"<None>";
    //update parent profile list
    if(updatedList)
    this.parentDocument.profilesList=updatedList;
    //this.addColsCombo.selectedLabel= selectedProfile;
  }


  public static ngOnDestroy(): any {
    instanceElement2 = null;
  }

}

 
