
<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%'>
    <SwtCanvas id="dataGridContainer" #dataGridContainer width="100%" height="90%"></SwtCanvas>
    <SwtCanvas id="cvSaveOrCancel" #cvSaveOrCancel width="100%" height="7%">
      <HBox width="100%" height="100%">
        <HBox width="100%" height="100%">
        <SwtButton id="btnOk" #btnOk
                   (click)="saveHandle()"></SwtButton>
        <SwtButton id="addSumButton" #addSumButton
                   (click)="sumWindowAdd()"></SwtButton>
        <SwtButton id="changeButton" #changeButton
                   enabled="false"
                   (click)="sumWindowChange()"></SwtButton>
        <SwtButton id="deleteButton" #deleteButton
                   enabled="false"
                   (click)="sumDelete()"></SwtButton>
        <SwtButton id="btnCancel" #btnCancel
                   buttonMode="true"
                   (click)="cancelHandle()"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right">
          <SwtLabel #lostConnectionText visible="false" styleName="red"></SwtLabel>
          <SwtHelpButton (click)="doHelp()"></SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
