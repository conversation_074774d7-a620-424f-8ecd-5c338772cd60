<SwtModule (creationComplete)="onLoad()" width="480" height="100%">
  <VBox width="100%" height="100%" styleName="vboxOuter" class="popup" >
    <HBox #filterHbox height="{{this.filterHbox.visible == true ? '10%' : '0%'}}">
      <SwtLabel #labelFilter></SwtLabel>
      <SwtTextInput #filterText (change)="filtringGrid(filterText.text)" width="200"></SwtTextInput>
    </HBox>
    <SwtCanvas #customGrid height="{{this.filterHbox.visible == true ? '80%' : '88%'}}" width="100%">
      <!-- <SwtCommonGrid #customGrid></SwtCommonGrid> -->
    </SwtCanvas>
    <SwtCanvas width="100%" height="10%" >
        <HBox width="100%">
      <HBox width="100%">
      <SwtButton #okButton label="OK" width="60" (click)="getSelectedValues($event)" enabled="false"></SwtButton>
      <SwtButton #closeButton label="Close" width="60" (click)="cancelHandler($event)"></SwtButton>
      </HBox>
      <HBox horizontalAlign="right" paddingRight="10">
        <SwtHelpButton #help id="help" enabled="true" (click)="doHelp()"></SwtHelpButton>
      </HBox>
        </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
