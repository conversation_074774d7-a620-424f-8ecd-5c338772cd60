import { Component, OnInit, ModuleWithProviders, NgModule, OnDestroy, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, CommonService, Logger, SwtAlert, SwtLabel, SwtFieldSet, SwtTextInput, SwtUtil, SwtButton, SwtTabNavigator, Tab, SwtCanvas, SwtCommonGrid, ExternalInterface, HTTPComms, JSONReader, SwtLoadingImage, SwtComboBox, SwtDateField, SwtRadioButtonGroup, SwtRadioItem, Alert } from 'swt-tool-box';
import moment from 'moment';
declare var instanceElement: any;
@Component({
  selector: 'app-emailtemplate', templateUrl: './EmailTemplateMaintenance.html',
  styleUrls: ['./EmailTemplateMaintenance.css']
})
export class EmailTemplateMaintenance extends SwtModule implements OnInit, OnDestroy {
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /***********SwtCanvas***********/
  @ViewChild('emailTemplateMaintenanceGridContainer') emailTemplateMaintenanceGridContainer: SwtCanvas;
  /***********SwtDateField***********/
  /***********SwtButton***********/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  private swtAlert: SwtAlert;
  private menuAccessId;
  private logger: Logger = null;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  private currencyPattern: string;
  public initReceivedJSON;
  public operation: string;
  private selectedtRow;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  private dateFormat: string;

  private emailTemplateMaintenanceGrid: SwtCommonGrid;
  // Variable to hold _attributeId                                                          
  private templateId: string = null;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Account Currency Period maintenance', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
    this.logger.info("method [constructor] - START/END ");
  }

  ngOnInit() {
    instanceElement = this;


    this.emailTemplateMaintenanceGrid = <SwtCommonGrid>this.emailTemplateMaintenanceGridContainer.addChild(SwtCommonGrid);
    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.addButton', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.changeButton', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.viewButton', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.deleteButton', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
  }
  onLoad() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      this.menuAccessId = "0";//ExternalInterface.call('eval', 'menuAccessId');
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 10;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      errorLocation = 20;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      // Condition to check menuAccess is not full access                                                       
      if (this.menuAccessId != "0") {
        this.addButton.enabled = false;
      }

      //action url	                                                                                          
      this.actionPath = "emailTemplateMaintenance.do?method=";
      //Then declare the action method:					                                                      
      this.actionMethod = "displayEmailTemplateList";
      this.requestParams['menuAccessId'] = this.menuAccessId;

      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      errorLocation = 30;
      this.emailTemplateMaintenanceGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "onLoad", errorLocation);
    }
  }
  /** This method is called when selecting a row from bottom grid**/

  cellClickEventHandler(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.emailTemplateMaintenanceGrid.refresh(); errorLocation = 10;
      if (this.emailTemplateMaintenanceGrid.selectedIndex >= 0) {
        this.changeButton.enabled = true;
        this.changeButton.buttonMode = true;
        this.viewButton.enabled = true;
        this.viewButton.buttonMode = true;
        this.deleteButton.enabled = true;
        this.deleteButton.buttonMode = true;
        errorLocation = 20;
        this.selectedtRow = this.emailTemplateMaintenanceGrid.selectedItem;
        this.templateId = this.selectedtRow.templateId.content;

      } else {
        errorLocation = 30;
        this.changeButton.enabled = false;
        this.changeButton.buttonMode = false;
        this.viewButton.enabled = false;
        this.viewButton.buttonMode = false;
        this.deleteButton.enabled = false;
        this.deleteButton.buttonMode = false;
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, '', "cellClickEventHandler", errorLocation);
    }
  }

  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        errorLocation = 10;
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        //this.dataExport.enabled = true;
        errorLocation = 20;
        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            //reset grid selection and buttons status

            this.emailTemplateMaintenanceGrid.selectedIndex = -1;
            this.changeButton.enabled = false;
            this.viewButton.enabled  = false;
            this.deleteButton.enabled = false;
            errorLocation = 30;
            this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
            errorLocation = 40;
            this.dateFormat = this.jsonReader.getSingletons().dateformat;
            this.emailTemplateMaintenanceGrid.dateFormat = this.dateFormat;
            errorLocation = 50; errorLocation = 60; errorLocation = 100; if (!this.jsonReader.isDataBuilding()) {
              const obj = { columns: this.lastRecievedJSON.emailTemplateMaintenance.emailTemplateMaintenanceGrid.metadata.columns };
              errorLocation = 110;
              this.emailTemplateMaintenanceGrid.CustomGrid(obj);
              errorLocation = 120;
              var gridRows = this.lastRecievedJSON.emailTemplateMaintenance.emailTemplateMaintenanceGrid.rows;
              if (gridRows.size > 0 && gridRows.row) {
                this.emailTemplateMaintenanceGrid.gridData = gridRows;
                errorLocation = 130;
                this.emailTemplateMaintenanceGrid.setRowSize = this.jsonReader.getRowSize();
                errorLocation = 140;
                this.emailTemplateMaintenanceGrid.refresh();
                //this.dataExport.enabled = true;                                                                           
              }
              else {
                this.emailTemplateMaintenanceGrid.gridData = { size: 0, row: [] };
                //this.dataExport.enabled = false;                                                                          
              }
              this.prevRecievedJSON = this.lastRecievedJSON;
            }
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }

        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, '', "inputDataResult", errorLocation);
    }
  }
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


  /**                                                                                                                  
   * If a fault occurs with the connection with the server then display the lost connection label                      
   * @param event:FaultEvent                                                                                           
   **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  closeHandler(): void {
    ExternalInterface.call("close");
  }



  updateData() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      errorLocation = 10;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 20;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      errorLocation = 30;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;


      this.actionPath = "emailTemplateMaintenance.do?method=";
      //Then declare the action method:					                                                      
      this.actionMethod = "displayEmailTemplateList";
      this.requestParams['menuAccessId'] = this.menuAccessId;

      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [updateData] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, '', "updateData", errorLocation);
    }
  }
  addEmailTemplateHandler() {
    //Variable for errorLocation                                                                                           
    let errorLocation = 0;
    try {
      this.operation = "add";
      let params = [];
      errorLocation = 10;
      ExternalInterface.call('subEmailTemplateMaintenance', "add", "");

         
      /*var newWindow = window.open("/EmailTemplateMaintenanceDetails", 'EmailTemplateMaintenanceDetails Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
      if (window.focus) {
        newWindow.focus();
      }*/

    } catch (error) {
      // log the error in ERROR LOG                                                                                        
      this.logger.error('method [addEmailTemplateHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, '', "addEmailTemplateHandler", errorLocation);
    }
  }
  changeEmailTemplateHandler() {

    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.operation = "change";
      let params = [];
      params.push({
        templateId: this.selectedtRow.templateId.content,
      });
      errorLocation = 10;
      console.log("params",params)
      ExternalInterface.call('subEmailTemplateMaintenance', "change", JSON.stringify(params));
   /* var newWindow = window.open("/EmailTemplateMaintenanceDetails", 'EmailTemplateMaintenanceDetails Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
    if (window.focus) {
      newWindow.focus();
    }*/
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [changeEmailTemplateHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, '', "changeEmailTemplateHandler", errorLocation);
    }

  }
  viewEmailTemplateHandler() {
    let errorLocation = 0;
    try {
      this.operation = "view";
      let params = [];
      params.push({
        templateId: this.selectedtRow.templateId.content,
      });
      errorLocation = 10;
      console.log("params",params)
      ExternalInterface.call('subEmailTemplateMaintenance', "view", JSON.stringify(params));
   /* var newWindow = window.open("/EmailTemplateMaintenanceDetails", 'EmailTemplateMaintenanceDetails Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
    if (window.focus) {
      newWindow.focus();
    }*/
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [viewEmailTemplateHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, '', "viewEmailTemplateHandler", errorLocation);
    }
  }

  public deleteCheck(): void {
    this.swtAlert.confirm(SwtUtil.getPredictMessage('confirm.delete', null),
      SwtUtil.getPredictMessage('alert.deletion.confirm', null),
      SwtAlert.YES | SwtAlert.NO,
      null,
      this.deleteEmailTemplateHandler.bind(this),
      null);
  }



  deleteResult(event) {

    let message = SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninDelete', null);
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyMessage() && this.jsonReader.getRequestReplyMessage().indexOf("RECORD_CANNOT_DELETED") != -1) {
        this.swtAlert.error(message, null, Alert.OK, null, () => {
        });
      }else {
        this.updateData();
      }
    }
  }



  deleteEmailTemplateHandler(event) {

    if (event.detail == SwtAlert.YES) {
      //Variable for errorLocation
      let errorLocation = 0;
      try {
        this.requestParams = [];
        this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
        if (this.menuAccessId) {
          if (this.menuAccessId !== "") {
            this.menuAccessId = Number(this.menuAccessId);
          }
        }
        errorLocation = 10;
        this.inputData.cbStart = this.startOfComms.bind(this);
        this.inputData.cbStop = this.endOfComms.bind(this);
        this.inputData.cbResult = (event) => {
          this.deleteResult(event);
        };
        errorLocation = 20;
        this.inputData.cbFault = this.inputDataFault.bind(this);
        this.inputData.encodeURL = false;

        this.actionPath = "emailTemplateMaintenance.do?method=";
        //Then declare the action method:					                                                      
        this.actionMethod = "deleteEmailTemplate";
        this.requestParams['menuAccessId'] = this.menuAccessId;
        this.requestParams['templateId'] = this.selectedtRow.templateId.content;
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
        errorLocation = 40;
        this.inputData.send(this.requestParams);
      } catch (error) {
        // log the error in ERROR LOG

        this.logger.error('method [deleteEmailTemplateHandler] - error: ', error, 'errorLocation: ', errorLocation);
        SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, '', "delete", errorLocation);
      }
    }
  }
}
//Define lazy loading routes                                                         
const routes: Routes = [
  { path: '', component: EmailTemplateMaintenance }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module                               
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [EmailTemplateMaintenance],
  entryComponents: []
})
export class EmailTemplateMaintenanceModule { }                                            