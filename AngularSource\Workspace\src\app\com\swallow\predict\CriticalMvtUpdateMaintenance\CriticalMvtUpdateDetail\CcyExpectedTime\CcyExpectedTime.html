<SwtModule  width="100%" height="100%" (creationComplete)="onLoad()">
  <VBox paddingLeft="5" paddingRight="5" paddingTop="5" paddingBottom="5" width="100%" height="100%">
    <Grid width="100%" height="75" paddingLeft="5">

      <GridRow width="100%" height="28">
            <GridItem width="150">
              <SwtLabel id="ccyLbl" #ccyLbl></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="ccyCombo" #ccyCombo width="100" dataLabel="currencyList"> </SwtComboBox>
            </GridItem>
      </GridRow>

      <GridRow width="100%" height="28">
        <GridItem width="150">
          <SwtLabel id="expectedTimeLbl" #expectedTimeLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput #expTimeTxt pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"
          (focusOut)="validateTime(expTimeTxt)" width="70" textAlign="center" maxChars="5"></SwtTextInput>
        </GridItem>
       </GridRow>

      </Grid>
      
    <SwtCanvas width="100%" height="35">
       <HBox width="100%">
          <SwtButton [buttonMode]="true" id="okButton" #okButton (click)="okHandler()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="cancelButton" #cancelButton (click)="closeHandler()">
          </SwtButton>
        </HBox>

    </SwtCanvas>
  </VBox>
</SwtModule>