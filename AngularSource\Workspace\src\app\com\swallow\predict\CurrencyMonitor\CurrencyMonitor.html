<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas  minWidth="900" width="100%" height="58">
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow width="100%" height="50%">
         <GridItem width="65%">
           <GridItem width="115">
             <SwtLabel textDictionaryId="entity.id" ></SwtLabel>
           </GridItem>
           <GridItem>
             <SwtComboBox id="entityCombo" #entityCombo width="180" dataLabel="entity"
                           (change)="changeCombo($event)"  (open)="openedCombo($event)"
                          (close)="closedCombo($event)"> </SwtComboBox>
           </GridItem>
           <GridItem>
             <SwtLabel #selectedEntity fontWeight="normal" text=" " paddingLeft="10"></SwtLabel>
           </GridItem>
         </GridItem>
         <GridItem>
           <GridItem width="80">
             <SwtLabel  textDictionaryId="currencyInterest.fromDate"></SwtLabel>
           </GridItem>
           <GridItem minWidth="110">
             <SwtDateField id="fromDate"
                           (open)="openedCombo($event)"
                           toolTip="Enter start date (if applicable)" #fromDate width="70"
                           (change)="validateStartDate($event,'change')"></SwtDateField>
           </GridItem>
           <GridItem width="40" marginLeft="40" >
             <SwtLabel textDictionaryId="text.showdays"></SwtLabel>
           </GridItem>
           <GridItem marginLeft="10" >
             <SwtNumericInput id="showDays" #showDays width="30" maxChars="2"   (keypress)="keyDownInNumberOfDays($event)"
                              (focusOut)="validateShowDaysValue($event)">
             </SwtNumericInput>
             <GridItem marginLeft="10">
               <SwtLabel textDictionaryId="text.day" id="daysLabel" #daysLabel >
               </SwtLabel>
             </GridItem>
           </GridItem>
         </GridItem>
        </GridRow>
        <GridRow width="100%" height="50%">
          <GridItem width="65%">
            <GridItem width="115">
              <SwtLabel textDictionaryId="currency.group" ></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="ccyCombo" #ccyCombo width="180" dataLabel="currencygroup"
                           (change)="changeCombo($event)"  (open)="openedCombo($event)"
                           (close)="closedCombo($event)">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel #selectedCcy fontWeight="normal" paddingLeft="10"></SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem>
            <GridItem>
              <SwtLabel width="80" textDictionaryId="currMonitor.breakdown"  paddingTop="2"></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtRadioButtonGroup #breakdown id="breakdown" align="horizontal">

                <SwtRadioItem #accountRadio id="accountRadio" selected="true" value="A" label="Account"
                              groupName="breakdown"></SwtRadioItem>


                <SwtRadioItem #movementRadio id="movementRadio" value="M" label="Movement"
                              groupName="breakdown"></SwtRadioItem>

                <SwtRadioItem #bookRadio id="bookRadio" value="B" label="Book"
                              groupName="breakdown"></SwtRadioItem>

                <SwtRadioItem #groupRadio id="groupRadio" value="G" label="Group"
                              groupName="breakdown"></SwtRadioItem>

                <SwtRadioItem #meatagroupRadio id="meatagroupRadio" value="MG" label="MetaGroup"
                              groupName="breakdown"></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <VBox width="100%" height="100%" verticalGap="1" minWidth="900">
        <SwtCanvas styleName="canvasWithGreyBorder" id="displaycontainer" border="false"  width="100%" height="100%" #displaycontainer >
        </SwtCanvas>
        <SwtCanvas styleName="canvasWithGreyBorder" id="totalsContainer" border="false"  width="100%" height="40" #totalsContainer >
        </SwtCanvas>
    </VBox>
    <SwtCanvas width="100%" marginBottom="0" height="40" minWidth="900">
      <HBox width="100%" top="1">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true" id="refreshButton" #refreshButton enabled="false"
            (click)="updateDatafromRefresh($event)" >
          </SwtButton>
          <SwtButton [buttonMode]="true" id="optionsButton" #optionsButton enabled="false"
            (click)="optionHandler()" >
          </SwtButton>
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
        </HBox>
        <HBox width="100%" horizontalAlign="right" paddingRight="10">
          <SwtLabel visible="false" text="DATA BUILD IN PROGRESS" color="red" #dataBuildingText id="dataBuildingText" right="155"
            height="16"></SwtLabel>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">


          <SwtLabel visible="false" text="CONNECTION ERROR" color="red" #lostConnectionText id="lostConnectionText" (click)="connError($event)" right="155" height="16">
          </SwtLabel>
          <SwtLabel textDictionaryId="screen.lastRefresh"  height="16" fontWeight="normal"> </SwtLabel>
          <SwtLabel id="lastRefTime" #lastRefTime height="16" styleName="labelLeftRefTime" fontWeight="normal">
          </SwtLabel>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">
          <div>
          <DataExport  #dataExport id="dataExport" ></DataExport>
        </div>
          <SwtHelpButton (click)="doHelp()">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>

      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
