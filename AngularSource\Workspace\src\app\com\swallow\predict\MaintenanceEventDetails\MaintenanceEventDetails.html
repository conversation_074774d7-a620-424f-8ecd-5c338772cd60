<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingTop="5">
    <Grid width="100%" height="80" paddingLeft="5">
      <GridRow width="100%" height="20" >
        <GridItem width="100%">
          <GridItem width="400">
            <SwtLabel id="facilityLabel" width ="100"  #facilityLabel text="Facility"></SwtLabel>
            <SwtLabel id="facilityValue" fontWeight="normal" width ="300" #facilityValue ></SwtLabel>
          </GridItem>
            <GridItem  width="100%">
              <HBox horizontalAlign="right" width="100%" paddingRight="10" marginTop="5">
               <SwtLabel id="statusLabel" #statusLabel width ="57" text="Status"></SwtLabel>
               <SwtLabel id="statusValue" fontWeight="normal" #statusValue width ="316" ></SwtLabel>
              </HBox>
            </GridItem>
        </GridItem>
      </GridRow>
      <GridRow width="100%" height="20" >
        <GridItem width="100%">
          <GridItem  >
            <SwtLabel id="recordLabel" width ="100"  #recordLabel text="Record"></SwtLabel>
            <SwtLabel id="recordValue" fontWeight="normal" width ="300" #recordValue ></SwtLabel>
          </GridItem>
            <GridItem  width="100%">
              <HBox horizontalAlign="right" width="100%" paddingRight="10" marginTop="5">
               <SwtLabel id="requestUserIdLabel" #requestUserIdLabel width ="93" text="Requested By"></SwtLabel>
               <SwtLabel id="requestUserIdValue" fontWeight="normal" #requestUserIdValue width ="80" ></SwtLabel>
             <SwtLabel id="onLabelRequested" #onLabelRequested width ="20" text="On"></SwtLabel>
               <SwtLabel id="requestDateValue" fontWeight="normal" #requestDateValue width ="200" ></SwtLabel>
              </HBox>
            </GridItem>
        </GridItem>
      </GridRow>


      <GridRow width="100%" height="20" >
        <GridItem width="100%">
          <GridItem  >
            <SwtLabel id="typeLabel" width ="100"  #typeLabel text="Type"></SwtLabel>
            <SwtLabel id="typeValue" fontWeight="normal" width ="300" #typeValue ></SwtLabel>
          </GridItem>
            <GridItem  width="100%">
              <HBox horizontalAlign="right" width="100%" paddingRight="10" marginTop="5">
               <SwtLabel id="authUserIdLabel" #authUserIdLabel width ="150" text="Accepted/Rejected by"></SwtLabel>
               <SwtLabel id="authtUserIdValue" fontWeight="normal" #authtUserIdValue width ="80" ></SwtLabel>
             <SwtLabel id="onLabelAuth" #onLabelAuth width ="20" text="On"></SwtLabel>
               <SwtLabel id="authDateValue"  fontWeight="normal" #authDateValue width ="200" ></SwtLabel>
              </HBox>
            </GridItem>
        </GridItem>
      </GridRow>


      <GridRow width="100%" height="20" >
        <GridItem width="100%">
          <GridItem  >
            <SwtLabel id="maintEventIdLabel" width ="100"  #maintEventIdLabel text="Event Id"></SwtLabel>
            <SwtLabel id="maintEventIdValue" fontWeight="normal" width ="300" #maintEventIdValue ></SwtLabel>
          </GridItem>
        </GridItem>
      </GridRow>



    </Grid>

    
    <GridRow width="100%" height="130" paddingBottom="5">
      <SwtCanvas #logGridContainer id="logGridContainer" styleName="canvasWithGreyBorder" width="100%"
        height="100%" border="false"></SwtCanvas>
    </GridRow>
    <GridRow width="100%" height="25">
      <GridItem width="65%">
        <GridItem width="50">
          <SwtLabel id="fromDateLabel" text="Changes recorded for selected record:" #fromDateLabel></SwtLabel>
        </GridItem>
      </GridItem>
      </GridRow>
    <GridRow width="100%" height="100%" paddingBottom="10">
      <VDividedBox height="100%" width="100%" #vDivider minHeight="250">
        <SwtCanvas class="top" width="100%" height="50%" minWidth="640">
          <SwtCanvas #viewLogGridContainer id="viewLogGridContainer" styleName="canvasWithGreyBorder" width="100%"
            border="false" height="100%" width="100%"></SwtCanvas>
        </SwtCanvas>
        <SwtCanvas width="100%" height="50%" class="bottom" minWidth="640">
          <VBox width="100%" height="100%">
            <HBox width="100%" height="20">
              <SwtLabel id="fullDetailsLbl" #fullDetailsLbl></SwtLabel>
            </HBox>
            <HBox width="100%" height="20">
              <SwtLabel id="oldValLbl" #oldValLbl fontWeight="normal" width="49%"></SwtLabel>
              <SwtLabel id="newValLbl" #newValLbl fontWeight="normal" width="51%"></SwtLabel>
            </HBox>
      
            <div class="row" height="100%">
              <div class="col-md-12 editor editor-size" style="height: 100%;">
            <ngx-monaco-diff-editor id="diffeditor" [options]="diffOptions" [originalModel]="originalModel"
                  [modifiedModel]="modifiedModel" style="height: 100%"></ngx-monaco-diff-editor>
          </div>
        </div>
          </VBox>
    </SwtCanvas>
      </VDividedBox>
    </GridRow>
   
    <SwtCanvas width="100%" height="35">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" enabled ="false" id="viewInFacilityButton" label="View in Facility" #viewInFacilityButton (click)="viewHandler('view')">
          </SwtButton>
          <!-- <SwtButton [buttonMode]="true" enabled ="false" id="amendInFacilityButton" label="Amend in Facility" #amendInFacilityButton (click)="viewHandler('change')" >
          </SwtButton> -->
          <SwtButton [buttonMode]="true" enabled ="false" id="acceptButton" label="Accept" #acceptButton (click)="acceptButtonHandler()" >
          </SwtButton>
          <SwtButton [buttonMode]="true" enabled ="false" id="rejectButton" label="Reject" #rejectButton (click)="rejectButtonHandler()" >
          </SwtButton>
         
        </HBox>
        <HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
          <SwtButton [buttonMode]="true" id="closeButton" label="Close" #closeButton (click)="popupClosed()">
          </SwtButton>
        </HBox>
    
      </HBox>
    </SwtCanvas>
</VBox>
</SwtModule>
