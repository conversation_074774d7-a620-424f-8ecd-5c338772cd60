import {CommonService, SwtButton, SwtModule, SwtTextInput} from 'swt-tool-box';
import {Component, ElementRef, ViewChild} from '@angular/core';


@Component({
    selector: 'app-Progress-Bar',
    templateUrl: './ProgressBar.html',
    styleUrls: ['./ProgressBar.css']
})

export class ProgressBar extends SwtModule {

    @ViewChild("cancelButton") public cancelButton: SwtButton;

    refreshText: string;

    constructor(private element: ElementRef, private commonService: CommonService) {
        super(element, commonService);
    }

  
  initData() {
  }

  cancelProgress(){

  }


 public closePopup() {
      try {
        if(this.titleWindow) {
          this.close();
        } else {
          window.close();
        }
      } catch (error) {
        console.log(error, "RatePopUp", "closePopup");
      }
    }

}
