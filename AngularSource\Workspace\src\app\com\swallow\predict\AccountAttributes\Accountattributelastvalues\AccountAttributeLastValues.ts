import {
  Component, ElementRef, NgModule, OnDestroy, OnInit, ViewChild
} from '@angular/core';
import {
  HTTPComms,
  SwtButton,
  SwtCommonGrid,
  JSONReader,
  SwtAlert,
  SwtUtil,
  CommonService,
  SwtCanvas, ExternalInterface, SwtModule, SwtToolBoxModule,
  ScreenVersion, ContextMenuItem, SwtPopUpManager,
  JSONViewer, SwtLabel, SwtTextInput
} from 'swt-tool-box';

import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";

declare var instanceElement: any;

@Component({
  selector: 'app-attribute-lastvalues',
  templateUrl: './AccountAttributeLastValues.html',
  styleUrls: ['./AccountAttributeLastValues.css']
})
export class AccountAttributeLastValues extends  SwtModule implements  OnInit, OnDestroy {


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }

  /*********SwtLabel*********/
  @ViewChild('accountIdLabel') accountIdLabel: SwtLabel;
  @ViewChild('accountNameLabel') accountNameLabel: SwtLabel;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('entityNameLabel') entityNameLabel: SwtLabel;

  /************SwtTextInput********/
  @ViewChild('entityIdField') entityIdField: SwtTextInput;
  @ViewChild('accountIdField') accountIdField: SwtTextInput;
  /************SwtCanvas********/
  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  /********SwtButton*********************/
  @ViewChild('displayButton') displayButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  /**
   * Display Objects
   **/
  private accountAttributeLastValuesGrid: SwtCommonGrid;

  /**
   * Data Objects
   **/
  private jsonReader: JSONReader = new JSONReader();
  private lastReceivedJSON;
  private prevReceivedJSON;


  private  versionNumber = "1.0";
  private versionDate = "04/11/2019";
  private showJSONPopup: any;
  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  private invalidComms = "";

  private swtAlert: SwtAlert;
  private errorLocation = 0;
  public moduleId = 'Predict';
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private screenName: string = null;
  private entityId ="";
  private entityName ="";
  private currencyCode ="";
  private accountId ="";
  private accountName ="";
  private attributeId ="";
  private effectiveDate ="";

  ngOnDestroy(): any {
    instanceElement  = null;
  }
  ngOnInit(): void {
    instanceElement  = this;
    this.screenName = SwtUtil.getPredictMessage('label.accountattributelastvalues.title.window', null) ;
    this.displayButton.label = SwtUtil.getPredictMessage('button.display', null);
    this.displayButton.toolTip = SwtUtil.getPredictMessage('button.display', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('button.close', null);
    this.entityLabel.text = SwtUtil.getPredictMessage('entity.id', null);
    this.accountIdLabel.text = SwtUtil.getPredictMessage('acct.id', null);

  }


  onLoad() {
    try {
      this.entityId = ExternalInterface.call('eval', 'entityId');
      this.entityName = ExternalInterface.call('eval', 'entityName');
      this.accountId = ExternalInterface.call('eval', 'accountId');
      this.accountName = ExternalInterface.call('eval', 'accountName');
      this.currencyCode = ExternalInterface.call('eval', 'currencyCode');

      this.accountAttributeLastValuesGrid  = this.canvasGrid.addChild(SwtCommonGrid) as SwtCommonGrid;
      this.accountAttributeLastValuesGrid.uniqueColumn = "sequenceKey";
      this.accountAttributeLastValuesGrid.onFilterChanged = this.cellLogic.bind(this);
      this.accountAttributeLastValuesGrid.onRowClick = (event) => {
        this.cellLogic(event);
      };

      this.initializeMenus();
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionMethod = 'method=displayAttributesFlex';
      this.actionPath = 'accountAttribute.do?';
      this.requestParams = [];
      this.requestParams["entityId"] = this.entityId;
      this.requestParams["accountId"] = this.accountId;
      this.requestParams["loadFlex"] = "true";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'accountAttributeLastValuesGrid', 'onLoad');
    }

  }


  /**The function initializes the menus in the right click event on the metagroup monitor screen.
   * The links are redirected to their respective pages.
   */
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName , this.versionNumber, this.versionDate);
    let addMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }


  /** This function is used to display the JSON
   *    for data showed in grid
   */
  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastReceivedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.display();
  }



  /**
   * inputDataResult
   * @param data: ResultEvent
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(data): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastReceivedJSON = data;
        this.jsonReader.setInputJSON(this.lastReceivedJSON);
        if (!(JSON.stringify(this.lastReceivedJSON) === JSON.stringify(this.prevReceivedJSON))) {
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {
            if (!this.jsonReader.isDataBuilding()) {
              this.entityIdField.text = this.entityId;
              this.accountIdField.text = this.accountId;
              this.entityNameLabel.text = this.entityName;
              this.accountNameLabel.text = this.accountName;
              let columnData = data.accountattribute.grid.metadata;
              this.accountAttributeLastValuesGrid.CustomGrid(columnData);
              this.accountAttributeLastValuesGrid.colWidthURL ( this.baseURL+"accountAttribute.do?&screenName=accountAttributeValue");
              this.accountAttributeLastValuesGrid.colOrderURL ( this.baseURL+"accountAttribute.do?&screenName=accountAttributeValue");
              this.accountAttributeLastValuesGrid.saveWidths = true;
              this.accountAttributeLastValuesGrid.saveColumnOrder = true;

              if (this.jsonReader.getGridData()) {
                if (this.jsonReader.getGridData().size > 0) {
                  this.accountAttributeLastValuesGrid.gridData  = this.jsonReader.getGridData();
                  this.accountAttributeLastValuesGrid.setRowSize  = this.jsonReader.getRowSize();
                  this.accountAttributeLastValuesGrid.refreshFilters();
                } else {
                  this.accountAttributeLastValuesGrid.dataProvider = null;
                  this.accountAttributeLastValuesGrid.selectedIndex = -1;
                }
              } else {
                this.accountAttributeLastValuesGrid.dataProvider = null;
                this.accountAttributeLastValuesGrid.selectedIndex = -1;
              }
            }
            this.prevReceivedJSON = this.lastReceivedJSON;

          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'AccountAttributeLastValue', 'inputDataResult', this.errorLocation);
    }
  }

  /**
   * inputDataFault
   * @param event:  FaultEvent
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    try {
      this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
      this.swtAlert.error("fault "+ this.invalidComms);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'AccountAttributeLastValue', 'inputDataFault', this.errorLocation);
    }
  }


  /**
   * close the window from the close button
   **/
   closeHandler(): void {
    ExternalInterface.call("close");
  }

  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call("help");
    } catch (e) {
      SwtUtil.logError(e,   this.moduleId, 'accountAttributeLastValuesGrid', 'doHelp',   this.errorLocation);
    }
  }


  /**
   * cellLogic
   * @param event: Event
   * This method is used to manumberain the button status when a row is clicked
   */
  cellLogic(event): void {
    try {
      let selectedRow: number = this.accountAttributeLastValuesGrid.selectedIndex;
      if (selectedRow > -1 && this.accountAttributeLastValuesGrid.selectable) {
        this.attributeId =  this.accountAttributeLastValuesGrid.selectedItem.attributeid.content;
        this.displayButton.enabled = true;
      } else {
        this.displayButton.enabled = false;
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'accountAttributeLastValuesGrid', 'cellLogic', this.errorLocation);
    }
  }


   displayButtonClickHandler(event): void {
    let selectedRow: number = this.accountAttributeLastValuesGrid.selectedIndex;
    this.attributeId = this.accountAttributeLastValuesGrid.selectedItem.attributeid.content;
    this.effectiveDate = this.accountAttributeLastValuesGrid.dataProvider[selectedRow].effectiveDate;
     /* Url to load Account Attribute Maintenance screen */
    this.actionMethod="accountAttributesMaintenance";
    this.actionMethod+="&entityId="+this.entityId;
    this.actionMethod+="&accountId="+this.accountId;
    this.actionMethod+="&attributeId="+this.attributeId;
    this.actionMethod+="&effectiveDate="+this.effectiveDate;
    this.actionMethod+="&parentScreen=accountAttributeLastvalue";
    this.actionMethod+="&menuAccessId=1";
    this.actionMethod+="&currencyCode="+this.currencyCode;
    ExternalInterface.call("openChildWindow", this.actionMethod);
  }

}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: AccountAttributeLastValues }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AccountAttributeLastValues],
  entryComponents: []
})
export class AccountAttributeLastValuesModule {}
