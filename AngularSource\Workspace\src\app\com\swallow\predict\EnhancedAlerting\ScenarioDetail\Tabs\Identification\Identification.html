<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%">
    <SwtFieldSet style="height:100%; padding-top: 10px; padding-right: 5px; color: blue; padding-left: 5px" legendText="Base Query">
      <VBox width="100%" height="100%">
        <SwtTextArea #baseQuery width="100%" height="70%"   minHeight="75"
        (focusOut)="updateChangeDataFlag()" paddingTop="5"></SwtTextArea>
        <Grid paddingTop="10">
          <GridRow width="100%">
            <GridItem width="161">
              <SwtLabel #hostColLabel></SwtLabel>
            </GridItem>
            <SwtImage width="19"  height="19" id="hostLockImg" #hostLockImg 
            (click)="changeComboStatus(hostColCombo, hostLockImg)" 
            visible="false" includeInLayout="true">
            </SwtImage>  
            <GridItem width="220">
              <SwtComboBox #hostColCombo (change)="onHostColumnChange('hostCol', hostColCombo)"></SwtComboBox>
            </GridItem>
            <HBox width="70%" horizontalAlign="right">
              <SwtImage width="19"  height="19" id="BaseQueryLockImg" #BaseQueryLockImg 
              (click)="changeTxtAreaStatus()" 
              visible="false" includeInLayout="true">
              </SwtImage>              
              <SwtButton #testButton (click)="sendQueryTest()"></SwtButton>
            </HBox>
          </GridRow>
          <GridRow>
            <GridItem width="161">
              <SwtLabel #entityColLbl></SwtLabel>
            </GridItem>
            <SwtImage width="19"  height="19" id="entityLockImg" #entityLockImg 
            (click)="changeComboStatus(entityColCombo,entityLockImg)" 
            visible="false" includeInLayout="true">
            </SwtImage> 
            <GridItem width="220">
              <SwtComboBox #entityColCombo enabled="false"  (change)="onEntityColumnChange('entityCol', entityColCombo)"></SwtComboBox>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="161">
              <SwtLabel #ccyColLbl></SwtLabel>
            </GridItem>
            <SwtImage width="19"  height="19" id="ccyLockImg" #ccyLockImg 
            (click)="changeComboStatus(ccyColCombo, ccyLockImg)" 
            visible="false" includeInLayout="true">
            </SwtImage> 
            <GridItem width="220">
              <SwtComboBox #ccyColCombo enabled="false" (change)="onCurrencyColumnChange('ccyCol', ccyColCombo)"></SwtComboBox>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="161">
              <SwtLabel #amounColtLbl></SwtLabel>
            </GridItem>
            <SwtImage width="19"  height="19" id="amountLockImg" #amountLockImg 
            (click)="changeComboStatus(amountColCombo, amountLockImg)" 
            visible="false" includeInLayout="true">
            </SwtImage> 
            <GridItem width="220">
              <SwtComboBox #amountColCombo (change)="onAmountColumnChange('amountCol', amountColCombo)"></SwtComboBox>
            </GridItem>
          </GridRow>
        </Grid>
      </VBox>
    </SwtFieldSet>
    <SwtFieldSet  style="height:65px; padding-right: 5px; padding-left: 5px; color: blue" legendText="Workflow Monitor">
      <Grid width="100%" height="90%">
        <GridRow>
          <GridItem width="180">
            <SwtLabel #defGroupLbl></SwtLabel>
          </GridItem>
          <GridItem width="220">
            <SwtComboBox #defGroupCombo dataLabel="summaryGroupingList" (change)="checkDefGroup()"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="180">
            <SwtLabel #genericLbl></SwtLabel>
          </GridItem>
          <GridItem width="30%">
           <SwtCheckBox #genericCheck (change)="updateFacilityList()"></SwtCheckBox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtFieldSet>
    <SwtFieldSet style="height:180px; padding-right: 5px; padding-left: 5px; color: blue" legendText="Link Facility Detail">
      <Grid width="100%" height="100%">
        <GridRow width ="100%" height="25">
          <GridItem width="180">
            <SwtLabel #facilityLbl></SwtLabel>
          </GridItem>
          <GridItem width="220">
            <SwtComboBox #facilityCombo dataLabel="facilityList" (change)="fillFacilityProperties()"></SwtComboBox>
          </GridItem>
          <GridItem width="300">
            <SwtLabel #selectedFacility fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridRow>
        <GridRow width ="100%" height="25">
          <GridItem width="180">
            <SwtLabel #refTableLbl></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtLabel #refTableTxt fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridRow>
        <GridRow width ="100%" height="25">
          <GridItem width="180">
            <SwtLabel #reqRefLbl></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtLabel #reqRefTxt fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridRow>
        <GridRow width ="100%" height="35">
          <GridItem width="167">
            <SwtLabel #refColumnLbl paddingTop="7"></SwtLabel>
          </GridItem>
          <GridItem width="85%">
            <SwtMultiselectCombobox #refColumnCombo id='refColumnCombo' width="620" height="100" dropHeight="25" showAbove="true" visible ="true"></SwtMultiselectCombobox>
          </GridItem>
        </GridRow>
        <GridRow width ="100%" height="25">
          <GridItem width="180">
            <SwtLabel #facilityParamLbl></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtLabel #facilityParamTxt fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridRow>
        <GridRow width ="100%" height="25">
          <GridItem width="180">
            <SwtLabel #paramValueLbl></SwtLabel>
          </GridItem>
          <GridItem width="85%">
            <SwtTextInput #paramValueTxt   maxChars="4000" width="620"></SwtTextInput>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtFieldSet>
  </VBox>
</SwtModule>
