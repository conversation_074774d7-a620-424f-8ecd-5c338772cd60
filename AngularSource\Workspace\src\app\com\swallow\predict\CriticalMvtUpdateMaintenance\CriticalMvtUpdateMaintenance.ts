import { Component, OnInit, <PERSON>ementRef, ViewChild, ModuleWithProviders, NgModule } from '@angular/core';
import { SwtUtil, CommonService, Logger, SwtAlert, SwtModule, Alert, SwtLoadingImage, SwtCanvas, SwtComboBox, SwtLabel, SwtButton, SwtToolBoxModule, JSONReader, HTTPComms, ExternalInterface, SwtCommonGrid, StringUtils } from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
declare var instanceElement: any;

@Component({
  selector: 'app-critical-mvt-update-maintenance',
  templateUrl: 'CriticalMvtUpdateMaintenance.html',
  styleUrls: ['./CriticalMvtUpdateMaintenance.css']
})
export class CriticalMvtUpdateMaintenance extends SwtModule implements OnInit {

  private logger: Logger;
  private swtAlert: SwtAlert;
  private moduleId = "Predict";
  private errorLocation = 0;
  private menuAccessId;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  private deleteData = new HTTPComms(this.commonService);
  public checkAuthData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  private criticalPayTypeGrid: SwtCommonGrid;
  private userRoleAccessId;
  private entityAccessId;
  private requireAuthorisation = true;
  private doDeleterecordAction = false;
  private faciltiyId = null;
  private screenNameWindow=null;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('gridCanvas') gridCanvas: SwtCanvas;

  /*********Combobox*********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;

  /*********SwtLabel*********/
  @ViewChild('entityLabel') entityLabel: SwtLabel; 
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;

    /*********SwtButton*********/
  @ViewChild("addButton") addButton: SwtButton;
  @ViewChild("changeButton") changeButton: SwtButton;
  @ViewChild("viewButton") viewButton: SwtButton;
  @ViewChild("deleteButton") deleteButton: SwtButton;
  @ViewChild("closeButton") closeButton: SwtButton;


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Critical Movement Update Maintenance Screen', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.criticalPayTypeGrid = <SwtCommonGrid>this.gridCanvas.addChild(SwtCommonGrid);
  
    this.entityLabel.text = SwtUtil.getPredictMessage('entity.id', null);
    this.entityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntity', null);

    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);

    this.addButton.toolTip = SwtUtil.getPredictMessage('tooltip.add', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('tooltip.change', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('tooltip.view', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('tooltip.Critical.Mvt.delete', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
  }


  updateData() {
    this.requestParams = [];
    this.doDeleterecordAction =false;
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "criticalPaymentType.do?";
    this.actionMethod = 'method=display';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

  }


  cellClickEventHandler(maintEventExist: boolean): void {

      let entityAccessId= this.criticalPayTypeGrid.selectedItem.access.content;
      if(entityAccessId="true" && this.userRoleAccessId=="0" ){
         if(maintEventExist){
            this.changeButton.enabled=false;
            this.changeButton.buttonMode=false;
            this.deleteButton.enabled=false;
            this.deleteButton.buttonMode=false;
         }else{
            this.changeButton.enabled=true;
            this.changeButton.buttonMode=true;
            this.deleteButton.enabled=true;
            this.deleteButton.buttonMode=true; 
      }
      
      }else{
      this.changeButton.enabled=false;
      this.changeButton.buttonMode=false;
      this.deleteButton.enabled=false;
      this.deleteButton.buttonMode=false;
      }

      this.viewButton.enabled= true;
      this.viewButton.buttonMode= true;

  }


  doOpenChildWindow(screen){
    let params=[];
    this.screenNameWindow = screen;
    /*if (screen != 'add') {     
      params.push ({
      entityId : this.criticalPayTypeGrid.selectedItem.entity.content,
      type : this.criticalPayTypeGrid.selectedItem.type.content,
      desc : this.criticalPayTypeGrid.selectedItem.description.content,
      category : this.criticalPayTypeGrid.selectedItem.category.content,
      orderInCateg : this.criticalPayTypeGrid.selectedItem.orderInCateg.content,
      enabled : this.criticalPayTypeGrid.selectedItem.enabled.content
     });
     ExternalInterface.call("openDetailScreen", "subScreen", screen, JSON.stringify(params));

    } else {*/
    ExternalInterface.call("openDetailScreen", "subScreen",screen);
    //}
  }


  inputDataResult(event): void {

    try {
    // Checks the inputData and stops the communication
    if (this.inputData && this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) { 
          if(this.doDeleterecordAction){
            if(StringUtils.isTrue(this.requireAuthorisation))
              this.swtAlert.show("This action needs second user authorisation", "Warning", Alert.OK);
            this.doDeleterecordAction = false;
          }

          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.entityCombo.selectedLabel = this.jsonReader.getSingletons().defaultEntity;
          this.userRoleAccessId= this.jsonReader.getSingletons().userAccess;
          this.selectedEntity.text = this.entityCombo.selectedValue;

          if (!this.jsonReader.isDataBuilding()) {
            const obj = { columns: this.lastRecievedJSON.CriticalPayType.criticalPayTypeGrid.metadata.columns };
            this.criticalPayTypeGrid.CustomGrid(obj);
            var gridRows = this.lastRecievedJSON.CriticalPayType.criticalPayTypeGrid.rows;
            if (gridRows.size > 0) {

              this.criticalPayTypeGrid.gridData = gridRows;

              this.criticalPayTypeGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              this.criticalPayTypeGrid.gridData = { size: 0, row: [] };
            }

          this.requireAuthorisation = this.jsonReader.getScreenAttributes()["requireAuthorisation"];
          this.faciltiyId = this.jsonReader.getScreenAttributes()["faciltiyId"];
            
        }
        this.prevRecievedJSON = this.lastRecievedJSON;
      }
       }

      }
    

    }catch (e) {
    console.log('error in inputData', e);
  }
}


    /**
     * doDeleteType
     *
     * @param event: Event
     *
     * Method to pop up delete confirmation
     *
     */
    doDelete(): void {
      try {
        Alert.yesLabel = "Yes";
        Alert.noLabel = "No";
        const message = SwtUtil.getPredictMessage('confirm.delete');
        this.swtAlert.confirm(message, 'Alert', Alert.OK | Alert.CANCEL, null, this.deleteType.bind(this));
      } catch (e) {
        // log the error in ERROR LOG
        SwtUtil.logError(e, this.moduleId, 'CriticalMvtUpdateMaintenance', 'doDeleteAccount', this.errorLocation);
      }
    }


    deleteType(event): void {
      try {
        // Condition to check Ok Button is selected
        if (event.detail === Alert.OK) {
          this.deleteAfterCheckFourEyes();
          // result event
         /* this.deleteData.cbResult = (event) => {
            this.deleteDataResult(event);
          };

          this.actionPath = "criticalPaymentType.do?method=";
          this.actionMethod = "deleteType";
          this.requestParams["entityId"] = this.criticalPayTypeGrid.selectedItem.entity.content;
          this.requestParams["criticalPayId"] = this.criticalPayTypeGrid.selectedItem.type.content;

          this.deleteData.url = this.baseURL + this.actionPath + this.actionMethod;
          this.deleteData.send(this.requestParams);*/

        }
      } catch (e) {
        SwtUtil.logError(e, this.moduleId, 'CriticalMvtUpdateMaintenance', 'deleteType', this.errorLocation);
      }
    }
  
    deleteAfterCheckFourEyes(): void {
    this.doDeleterecordAction = true;
    this.actionMethod = 'method=deleteType';
    this.actionPath = 'criticalPaymentType.do?';
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
            this.inputDataResult(event);
    };
    this.requestParams['method']= 'delete';
    this.requestParams["CpEntityId"] = this.criticalPayTypeGrid.selectedItem.entity.content;
    this.requestParams["criticalPayId"] = this.criticalPayTypeGrid.selectedItem.type.content;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.inputData.url =  this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.criticalPayTypeGrid.selectedIndex = -1;
    this.changeButton.enabled=false;
    this.viewButton.enabled=false;
    this.deleteButton.enabled=false;
  }

    onLoad() {
      this.requestParams = [];
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      
      this.actionPath = "criticalPaymentType.do?";
      this.actionMethod = 'method=display';
      this.requestParams['menuAccessId'] = this.menuAccessId;
      this.requestParams['entityId'] = "All";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.criticalPayTypeGrid.onRowClick = (event) => {
        this.checkIfMaintenanceEventExist(event);
      }; 
  
    }

    ngOnDestroy(): any {
      instanceElement = null;
    }
  
  
  
  
  
    startOfComms(): void {
      this.loadingImage.setVisible(true);
    }
  
    /**
     * Part of a callback function to all for control of the loading swf from the HTTPComms Object
     */
    endOfComms(): void {
      this.loadingImage.setVisible(false);
    }
  
  
    /**                                                                                                                  
     * If a fault occurs with the connection with the server then display the lost connection label                      
     * @param event:FaultEvent                                                                                           
     **/
    private inputDataFault(event): void {
      this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
      this.swtAlert.show("fault " + this._invalidComms);
    }
  
    closeHandler(): void {
      ExternalInterface.call("close");
    }


    deleteDataResult(event): void {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Parse result json
        this.jsonReader.setInputJSON(event);
        if (!this.jsonReader.getRequestReplyStatus()) {
          if ("DataIntegrityViolationExceptioninDelete" == this.jsonReader.getRequestReplyMessage()) {
            this.swtAlert.error(SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninDelete') + SwtUtil.getPredictMessage('alert.ContactSysAdm'), 'Error');
          } else {
  
            this.swtAlert.error('Error occurred, Please contact your System Administrator: \n'
              + this.jsonReader.getRequestReplyMessage(), 'Error');
          }
        } else {
          this.updateData();
        }
      }
    }
  

checkIfMaintenanceEventExist(event:Event): void {
  if(this.criticalPayTypeGrid.selectedIndex >=0){
    try {
      let actionMethod = 'method=checkIfMaintenenanceEventExist';
      let actionPath = 'maintenanceEvent.do?';
      this.checkAuthData.cbStart = this.startOfComms.bind(this);
      this.checkAuthData.cbStop = this.endOfComms.bind(this);
      this.checkAuthData.cbResult = (data) => {
        this.checkResult(data);
      };
      this.requestParams['recordId']= this.criticalPayTypeGrid.selectedItem.type.content;
      this.requestParams['facilityId']= this.faciltiyId;
      this.checkAuthData.cbFault = this.inputDataFault.bind(this);
      this.checkAuthData.encodeURL = false;
      this.checkAuthData.url =  this.baseURL + actionPath + actionMethod;
      this.checkAuthData.send(this.requestParams);
    } catch (e) {
      console.log(e);
      // log the error in ERROR LOG
      //SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'ClassName', 'updateData',  this.errorLocation);
    }
  }else{
    this.changeButton.enabled=false;
    this.changeButton.buttonMode=false;
    this.viewButton.enabled=false;
    this.viewButton.buttonMode=false;
    this.deleteButton.enabled=false;
    this.deleteButton.buttonMode=false; 
  }
  }

    public checkResult(data): void {

    try {
      if (this.checkAuthData && this.checkAuthData.isBusy()) {
        this.checkAuthData.cbStop();
      } else {
        this.jsonReader.setInputJSON(data);

          
        if (this.jsonReader.getRequestReplyMessage() == "RECOD_EXIST") {
          const message = SwtUtil.getPredictMessage('maintenanceEvent.alert.cannotBeAmended', null);
          this.swtAlert.error(message);
          this.cellClickEventHandler(true);
        }  else {
          this.cellClickEventHandler(false);
        }

      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }

  updateDataFromChild(): void {
    try {
      this.requestParams =[];
      this.actionMethod = 'method=display';
      this.doDeleterecordAction =false;
      this.actionPath = 'criticalPaymentType.do?';
      this.requestParams['method']= 'display';
      this.requestParams['menuAccessId'] = this.menuAccessId;
      this.requestParams['entityId'] = this.entityCombo.selectedLabel;
      this.inputData.url =  this.baseURL +  this.actionPath +  this.actionMethod;
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
        this.criticalPayTypeGrid.refresh();
        this.criticalPayTypeGrid.refreshFilters();
      };
      this.criticalPayTypeGrid.selectedIndex = -1;
      this.changeButton.enabled=false;
      this.changeButton.buttonMode=false;
      this.viewButton.enabled=false;
      this.viewButton.buttonMode=false; 
      this.deleteButton.enabled=false;
      this.deleteButton.buttonMode=false; 
      this.inputData.send( this.requestParams);
    } catch (e) {
      // log the error in ERROR LOG
      //SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'ClassName', 'updateData',  this.errorLocation);
    }
  }


  getParams(): any {
    let params = [];
    if (this.screenNameWindow == "add") {
      params = [this.screenNameWindow];
    } else {
      params = [this.screenNameWindow, this.criticalPayTypeGrid.selectedItem.type.content, this.criticalPayTypeGrid.selectedItem.entity.content];
    }
    return params;
  }

}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: CriticalMvtUpdateMaintenance }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CriticalMvtUpdateMaintenance],
  entryComponents: []
})
export class CriticalMvtUpdateMaintenanceModule { }