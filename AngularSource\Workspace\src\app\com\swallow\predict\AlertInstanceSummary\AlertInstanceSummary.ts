import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ImportPreAdvices } from '../PreAdviceInput/ImportPreAdvices/ImportPreAdvices';
import { SwtToolBoxModule, CommonService, Logger, SwtAlert, SwtButton, SwtLabel, SwtComboBox, SwtCheckBox, SwtLoadingImage, DataExportMultiPage, SwtText, SwtCanvas, SwtTabNavigator, Tab, SwtSummary, SwtRadioButtonGroup, SwtRadioItem, SwtModule, SwtUtil, SwtCommonGrid, HBox, JSONReader, HTTPComms, ScreenVersion, ExternalInterface, HDividedEndResizeEvent, StringUtils, CustomTree, ContextMenuItem, SwtPopUpManager, JSONViewer, SwtDateField, Timer } from 'swt-tool-box';
import { CustomSummary } from './CustomSummary/CustomSummary';
import { AttributeXML } from './AttributeXML/AttributeXML';
declare var instanceElement: any;
declare var require: any;
var parser = require('fast-xml-parser');
var prettyData = require('pretty-data');
@Component({
  selector: 'app-alert-instance-summary',
  templateUrl: './AlertInstanceSummary.html',
  styleUrls: ['./AlertInstanceSummary.css']
})
export class AlertInstanceSummary extends SwtModule implements OnInit, OnDestroy {



  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Scenario Summary', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }


  @ViewChild('resolveButton') resolveButton: SwtButton;
  @ViewChild('reActiveButton') reActiveButton: SwtButton;
  @ViewChild('goToButton') goToButton: SwtButton;  
  @ViewChild('detailsButton') detailsButton: SwtButton;  
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  //@ViewChild('rateButton') rateButton: SwtButton;
  @ViewChild('lblCombo') lblCombo: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('currLabel') currLabel: SwtLabel;
  @ViewChild('zeroLabel') zeroLabel: SwtLabel;
  @ViewChild('alertLabel') alertLabel: SwtLabel;
  @ViewChild('resolvedOnLbl') resolvedOnLbl: SwtLabel;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('currBox') currBox: SwtCheckBox;
  @ViewChild('zeroBox') zeroBox: SwtCheckBox;
  @ViewChild('alertBox') alertBox: SwtCheckBox;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('exportContainer') exportContainer: DataExportMultiPage;
  @ViewChild('lostConnectionText') lostConnectionText: SwtText;
  @ViewChild('lastRefTime') lastRefTime: SwtText;
  @ViewChild('lastRefTimeLabel') lastRefTimeLabel: SwtText;
  /*********Tab*********/
  @ViewChild('tabCategoryList') tabCategoryList: SwtTabNavigator;
  @ViewChild('displayContainerPredict') displayContainerPredict: Tab;
  @ViewChild('displayContainerPCM') displayContainerPCM: Tab;
  //New components
  
  //@ViewChild('thresholdLabel') thresholdLabel: SwtLabel;
  @ViewChild('statusLabel') statusLabel: SwtLabel;
 // @ViewChild('amountThresholdBox') amountThresholdBox: SwtCheckBox;
  /***********SwtRadioButtonGroup***********/
  @ViewChild('status') status: SwtRadioButtonGroup;
  @ViewChild('all') all: SwtRadioItem;
  @ViewChild('active') active: SwtRadioItem;
  @ViewChild('resolved') resolved: SwtRadioItem;
  @ViewChild('pending') pending: SwtRadioItem;
  @ViewChild('overdue') overdue: SwtRadioItem;
  @ViewChild('allOpen') allOpen: SwtRadioItem;
  @ViewChild('summaryGridContainer') summaryGridContainer: SwtCanvas;
  //@ViewChild('CustomSummary') CustomSummary: CustomSummary;
  @ViewChild('summaryHbox') summaryHbox: HBox;
  /***********SwtDateField***********/
  @ViewChild('resolvedOnDate') resolvedOnDate: SwtDateField;


  private swtAlert: SwtAlert;
  private logger: Logger;
  private customSummary:CustomSummary;

  /**
   * Data Objects
   */

  public  jsonReader: JSONReader = new JSONReader();
  public summaryJSONReader: JSONReader = new JSONReader();
  public prevRecievedSummaryJSON;
  public lastRecievedSummaryJSON;
  public prevRecievedJSON;
  public lastRecievedJSON;
  public lastReceivedWidthJSON;
public statusChanged = false;
  /**
   * Communication Objects
   */
  private  inputData: HTTPComms = new HTTPComms(this.commonService);
  private  detailsData: HTTPComms = new HTTPComms(this.commonService);
  private  summaryData: HTTPComms = new HTTPComms(this.commonService);
  public sendData: HTTPComms = new HTTPComms(this.commonService);
  private requestParams = [];
  private  invalidComms = "";
  private  baseURL = SwtUtil.getBaseURL();
  private  actionPath = "";
  private  actionMethod = "";
  private currDividerPosition: number;
  tooltipCurrencyCode;
  tooltipEntityId; 
  tooltipSelectedDate;
  tooltipOtherParams = [];
  tooltipSelectedAccount;
  tooltipMvtId;
  tooltipMatchId;
  source;
  /**
   * Refresh Strings
   */
  public  TREE = 'tree';
  public  SCENARIO = 'scenario';
  private formatIso : string = "yyyy-mm-dd";
  private formatIsoTime : string = "yyyy-mm-dd hh24:mi:ss";
  /**
   * Combo Flags
   */

  private  comboOpen = false;
  private  comboChange = false;

  /**
   * Popup Objects
   */

  private  showJSON: any;
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private showXMLPopup: any;
  /* -- START -- Screen Name and Version Number ---- */
  private  screenName = SwtUtil.getPredictMessage('scenarioSummary.title', null); // "Scenario Summary";
  private  versionNumber = "1.0.0001";
  private  releaseDate = '16 Apr 2020';
  /* -- END -- Screen Name and Version Number ---- */

  private  currentUser = "";
  private  menuAccessId = "";
  private  callerMethod = "";
  private  flashScenarios = "";
  private  popupScenarios = "";
  private  emailScenarios = false;
  private  lastSelectedId = "" ;
  private  lastSelectedNode = "" ;
  private  firstLoad = true;
  public  tabDataCategory = [];
  /**
   * Summary Tree Objects
   */
  public  scenarioTitle = '';

  private  fisrtTablastSelectedId: string = null;
  private  secondTablastSelectedId: string= null;
  private  firstTabTreeOpenedItems = [];
  private  firstTabTreeClosedItems = [];
  private  secondTabTreeOpenedItems = [];
  private  secondTabTreeClosedItems = [];
  private  previousSelectedTabIndex = -1;
  private scenarioId="";
  private win: any;
  private attributeJSON;
  private facilityGui="";
  private sqlParams = "";
  private selectedNodeId = "";
  private treeLevelValue = "";
  private optionIdTypes;
  private fromScreenFacility = false;
  private dateFormat: string;
  private currencyFormat: string;
  
  private displayedDate;
  private selectedIndex;
  /**
   * Timer Objects
   **/
  private  autoRefresh: Timer = null;
  private  refreshRate=60;


  ngOnInit() {
    instanceElement = this;
    this.customSummary = <CustomSummary>this.summaryHbox.addChild(CustomSummary);
    this.customSummary.width="100%";
    this.customSummary.height="100%";
    this.customSummary.summaryGrid.clientSideSort = false;
    this.customSummary.summaryGrid.clientSideFilter = false;
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.close', null);
    this.resolveButton.toolTip = SwtUtil.getPredictMessage('tooltip.resolveButton', null);
    this.resolveButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.resolve', null);
    this.reActiveButton.toolTip = SwtUtil.getPredictMessage('tooltip.reActivateButton', null);
    this.reActiveButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.reActivate', null);
    this.goToButton.toolTip = SwtUtil.getPredictMessage('tooltip.goTo', null);
    this.goToButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.goTo', null);
    this.detailsButton.toolTip = SwtUtil.getPredictMessage('tooltip.details', null);
    this.detailsButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.details', null);

    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refresh', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.refresh', null);
    //this.rateButton.toolTip = SwtUtil.getPredictMessage('tooltip.rate', null);
    //this.rateButton.label = SwtUtil.getPredictMessage('button.rate', null);
    this.resolvedOnLbl.text= SwtUtil.getPredictMessage('scenarioSummary.resolvedOn', null); 
    this.lblCombo.text = SwtUtil.getPredictMessage('scenarioSummary.Entity', null);
    this.lastRefTimeLabel.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);
    this.entityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.alertLabel.text = SwtUtil.getPredictMessage('scenarioSummary.alertableScen', null);
    this.zeroLabel.text = SwtUtil.getPredictMessage('scenarioSummary.zeroTotals', null);
    this.currLabel.text = SwtUtil.getPredictMessage('scenarioSummary.applyCcy', null);
    this.lostConnectionText.text = SwtUtil.getPredictMessage('screen.connectionError', null);
    //this.thresholdLabel.text = SwtUtil.getPredictMessage('scenarioSummary.amountThreshold', null);
    this.all.label = SwtUtil.getPredictMessage('scenarioSummary.all', null);
    this.active.label = SwtUtil.getPredictMessage('scenarioSummary.active', null);
    this.resolved.label = SwtUtil.getPredictMessage('scenarioSummary.resolved', null);
    this.pending.label = SwtUtil.getPredictMessage('scenarioSummary.pending', null);
    this.overdue.label = SwtUtil.getPredictMessage('scenarioSummary.overdue', null);
    this.allOpen.label = SwtUtil.getPredictMessage('scenarioSummary.allOpen', null);
    this.statusLabel.text = SwtUtil.getPredictMessage('scenarioSummary.status', null);
    //label section
    this.resolvedOnDate.toolTip = SwtUtil.getPredictMessage('tooltip.resolvedOnDate', null);

  }
  ngOnDestroy(): any {
    instanceElement = null;

  }

  /**
   * The functon is used when the page loads initially the generic display screen
   */
  onLoad(): void {
    try {
      this.logger.info('method [onLoad] - START ');
      let fromFacility = false;
      this.initializeMenus();
      this.callerMethod = ExternalInterface.call('eval', 'callerMethod');
     


      this.currentUser = ExternalInterface.call('eval', 'currentUser');
      this. menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      this.selectedNodeId = ExternalInterface.call('eval', 'selectedNodeId'); 
      this.treeLevelValue = ExternalInterface.call('eval', 'treeLevelValue'); 
      //get facilityId and sqlParams from parent
      let paramsFromParent: any;
      if (window.opener && window.opener.instanceElement && window.opener.instanceElement.getParamsFromParent) {
        this.status.enabled = false;
        paramsFromParent = window.opener.instanceElement.getParamsFromParent();
        this.facilityGui= paramsFromParent["facilityId"];
        this.sqlParams= paramsFromParent["sqlParams"];
        if(paramsFromParent['selectedNodeId']  != null){
          this.callerMethod = '___';
          fromFacility = true;
          this.selectedNodeId = paramsFromParent['selectedNodeId']; 
          this.treeLevelValue = paramsFromParent['treeLevelValue']; 
          this.tooltipCurrencyCode = paramsFromParent['tooltipCurrencyCode']
          this.tooltipEntityId = paramsFromParent['tooltipEntityId'];
          this.tooltipSelectedDate = paramsFromParent['tooltipSelectedDate'];

          this.tooltipOtherParams = paramsFromParent['tooltipOtherParams'];
          this.tooltipSelectedAccount = paramsFromParent['tooltipSelectedAccount'];
          this.tooltipMvtId = paramsFromParent['tooltipMvtId'];
          this.tooltipMatchId = paramsFromParent['tooltipMatchId'];
          if(this.tooltipOtherParams != null && this.tooltipOtherParams["currencythresholdFlag"]){
            this.currBox.selected = this.tooltipOtherParams["currencythresholdFlag"] == 'Y'
          }
          this.fromScreenFacility = true;

        }else {
          this.facilityGui = 'SCENARIO_INSTANCE_MONITOR';
          this.status.selectedValue ='';
        }
      }
      else{
        this.facilityGui = 'SCENARIO_INSTANCE_MONITOR';
        this.status.selectedValue ='';
      }

      if(!this.callerMethod)
        this.callerMethod = "_Y_";

      this.customSummary.tree.ITEM_CLICK.subscribe(( item ) => {
        this.summaryTreeEventHandler( item );
      });

      this.customSummary.summaryGrid.ITEM_CLICK.subscribe((selectedRowData) => {
        //this.cellClickEventHandler(selectedRowData);
        this.cellLogic(selectedRowData);
      });

      //update buttons status
      this.customSummary.summaryGrid.onRowClick = (event) => {
        if(event && event.num && event.num.content != undefined &&  event.num.content != null){
          this.selectedIndex=event.num.content;
        }
        if (this.customSummary.rcdScenInstance == "Y") {
          this.handleButtonsStatus();
        }

      };

      this.customSummary.summaryGrid.columnWidthChanged.subscribe((event) => {
        this.updateWidths(event);
      });

      this.customSummary.summaryGrid.columnOrderChanged.subscribe((event) => {
        this.updateOrders(event);
      });


      HDividedEndResizeEvent.subscribe((event) => {
        const widthLeft = ''+this.customSummary.divBox.widthLeft;
        this.currDividerPosition = Number(widthLeft.substr(0,widthLeft.length-1));
        this.updateWidths(event);
      });

      this.customSummary.summaryGrid.onFilterChanged = this.doUpdateSortFilter.bind(this);
      this.customSummary.summaryGrid.onSortChanged = this.doUpdateSortFilter.bind(this);
      this.requestParams = [];
      this.requestParams["currThreshold"] = this.currBox.selected == true ? "Y": "N";
      if(this.tooltipEntityId != null){
        this.requestParams["entityId"] = this.tooltipEntityId;
        this.entityCombo.enabled = false;
      }
      if(fromFacility)
        this.requestParams["alertScenarios"] = "false";

      this.actionPath = 'scenarioSummary.do?';
      this.actionMethod = 'method=summaryScreenInfo';
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.requestParams["callerMethod"] = this.callerMethod;

      
      // this.requestParams["entityId"] = "RABONL2U";



      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.logger.info('method [onLoad] - END ');
    } catch (error) {
      console.log(error);
      
      this.logger.error('GenericDisplay - method [onLoad] - error ', error);

    }
  }

  handleButtonsStatus() {
    if (this.customSummary.summaryGrid.selectedIndex > -1 && this.customSummary.summaryGrid.selectedItem) {
      if (this.customSummary.rcdScenInstance == "Y") {
        this.checkUserAccess();
      } else {
        this.disableButtons();
      }

    } else {
      this.disableButtons();
    }
  }

  changeStatus() {
    //enable resolvedOnDate only when all or resolved are selected
    if (this.status.selectedValue == "All" || this.status.selectedValue == "R") {
      this.resolvedOnDate.enabled = true;
    } else {
      this.resolvedOnDate.enabled = false;
    }
    this.statusChanged = true; 
    this.lastSelectedNode = "";
    this.customSummary.tree.selectedIndex = -1;
    if(this.customSummary.summaryGrid){
      this.customSummary.summaryGrid.resetFilter();
      this.customSummary.summaryGrid.filteredGridColumns="";
      this.customSummary.resetGridData();
    }
    this.updateData('tree');
    this.statusChanged = false;
  }

  /**
   * updateDividerPosition
   * This method is called to save the position of the divider
   **/
  updateDividerPosition(): void {
    let mainWidth: number=this.customSummary.mainHGroup.width;
    let trWidth: number=this.customSummary.treeContainer.width;
    this.currDividerPosition =(mainWidth - trWidth);
  }


  /**
   * comboFocusOutHandler
   * @param e:Event
   * This method is called on FocusOut Event occurs on entity Combo Box
   */
  comboFocusOutHandler(e): void {
    if (StringUtils.trim(this.entityCombo.selectedItem.text).length === 0) {
      this.entityCombo.setComboData(this.jsonReader.getSelects());
    }
  }


  updateWidths(event): void {
    let columnWidth=[];
    let isScenInstance="N";
    let scenarioId=this.getScenarioId(this.customSummary.tree.selectedItem,this.customSummary.tree.selectedItem.level);

    let cols = this.customSummary.summaryGrid.gridObj.getColumns().slice(0);
    if(this.customSummary.tree && this.customSummary.tree.selectedItem && this.customSummary.tree.selectedItem['record_scenario_instances']) {
      if (this.customSummary.tree.selectedItem['record_scenario_instances'] == "Y") {
        isScenInstance= "Y"
      }else{
        isScenInstance= "N"
      }
    }
    let columnCount = cols.length;
    for (let i=0; i< columnCount-1; i++) {
      if(cols[i].id == "expand") {
        cols[i].width = 5;
      } else {
        if (cols[i].field != null) {
          columnWidth.push(cols[i].field + "=" + cols[i].width);
        }
      }
    }
    // add divider value
    columnWidth.push("divider=" + this.currDividerPosition);
    this.requestParams=[];
    this.sendData.encodeURL = false;
    this.requestParams["width"]=columnWidth.join(",");
    this.requestParams["isScenInstance"]=isScenInstance;
    this.requestParams["scenarioId"]=scenarioId;
    this.actionPath = 'scenarioSummary.do?';
    this.actionMethod = 'method=saveColumnWidth&';
    this.sendData.cbStart= this.startOfComms.bind(this);
    this.sendData.cbStop= this.endOfComms.bind(this);
    this.sendData.cbResult = ( event) => {
      this.inputDataResultColumnsChange(event);
    };
    this.sendData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.sendData.send(this.requestParams);
  }


  updateOrders(event): void {
    let columnOrder=[];
    let isScenInstance="N";
    let scenarioId=this.getScenarioId(this.customSummary.tree.selectedItem,this.customSummary.tree.selectedItem.level);
    let cols = this.customSummary.summaryGrid.gridObj.getColumns().slice(0);
    if(this.customSummary.tree && this.customSummary.tree.selectedItem && this.customSummary.tree.selectedItem['record_scenario_instances']) {
      if (this.customSummary.tree.selectedItem['record_scenario_instances'] == "Y") {
        isScenInstance= "Y"
      }else{
        isScenInstance= "N"
      }
    }
    let columnCount = cols.length;
    for (let i=0; i< columnCount-1; i++) {
          columnOrder.push(cols[i].field);
    }
    // add divider value
    columnOrder.push("divider");
    this.requestParams=[];
    this.sendData.encodeURL = false;
    this.requestParams["order"]=columnOrder.join(",");
    this.requestParams["isScenInstance"]=isScenInstance;
    this.requestParams["scenarioId"]=scenarioId;
    this.actionPath = 'scenarioSummary.do?';
    this.actionMethod = 'method=saveColumnOrder&';
    this.sendData.cbStart= this.startOfComms.bind(this);
    this.sendData.cbStop= this.endOfComms.bind(this);
    this.sendData.cbResult = ( event) => {
    };
    this.sendData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.sendData.send(this.requestParams);
  }


  inputDataResultColumnsChange(event): void {
    let index: any;
    if (this.sendData.isBusy()) {
      this.sendData.cbStop();
    } else {
      this.lastReceivedWidthJSON = event;
      let jsonResponse: JSONReader = new JSONReader();
      jsonResponse.setInputJSON(this.lastReceivedWidthJSON);
      if (jsonResponse.getRequestReplyMessage() !== "Column width saved ok") {
        this.swtAlert.error(SwtUtil.getPredictMessage('error.contactAdmin', null) + '\n' + jsonResponse.getRequestReplyMessage());
      }
    }
  }

    /**
   * enableInterface
   * Enable interface, turn on certain UI elements when a request is made to the server
   */
  enableInterface(): void {
    // enable refresh button
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
    // enbale checkboxes controls
    this.currBox.enabled = true;
    this.zeroBox.enabled = true;
    this.alertBox.enabled = true;
    this.customSummary.enableTree();
    // this.summary.tree.addEventListener(CustomTreeEvent.ITEMCLICK, this.summaryTreeEventHandler, false, 0, true);

  }

    /**
   * disableInterface
   * Disable interface, turn off certain UI elements when a request is made to the server
   */
  disableInterface(): void {
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
    this.currBox.enabled = false;
    this.zeroBox.enabled = false;
    this.alertBox.enabled = false;
    this.customSummary.disableTree();
    this.customSummary.tree.removeEventListener('treeItemClick', this.summaryTreeEventHandler, false);
  }

  extractParentFilterDataFromNode(parentData,sql){
    if(parentData != null && parentData != undefined && parentData.treeLevelName!=null ){
      if(parentData.treeLevelName == 'category_id' || parentData.treeLevelName == 'CATEGORY_ID'){
        sql+=" AND P_SCENARIO."+parentData.treeLevelName+" = '"+parentData.treeLevelValue+"'"
      }else{
        let isDate = new RegExp(/^\d{4}-([0]\d|1[0-2])-([0-2]\d|3[01])$/).test(parentData.treeLevelValue);
        if(isDate)
          sql+="  AND PSI."+parentData.treeLevelName+" =  TO_DATE('"+parentData.treeLevelValue+"','YYYY-MM-DD')";
        else
          sql+=" AND PSI."+parentData.treeLevelName+" = '"+parentData.treeLevelValue+"'"
      }
      sql = this.extractParentFilterDataFromNode(parentData.parentData,sql);
    }


    return sql;
  }
  

  
  /**
   * loadSummary
   * This function is called to load data relevent to alerts
   */
   loadSummary(): void {
    // Initialize communication objects
    let selectedTreeItemQuery = "";
    let selectedFilter = null;
    let selectedSort = null;
    selectedTreeItemQuery = this.extractParentFilterDataFromNode(this.customSummary.tree.selectedItem,selectedTreeItemQuery);
    this.summaryData.cbStart = this.startOfComms.bind(this);
    this.summaryData.cbStop = this.endOfComms.bind(this);
    this.summaryData.cbResult = (data) => {
      this.summaryDataResult(data);
    };
    this.summaryData.cbFault = this.inputDataFault.bind(this);
    this.summaryData.encodeURL = false;    
    this.requestParams["status"] = this.status.selectedValue;
    this.requestParams["resolvedOnDate"] = this.resolvedOnDate.text;
    if (this.firstLoad) {
      this.requestParams["firstLoad"] = 'true';
    } else {
      this.requestParams["firstLoad"] = 'false';
      this.customSummary.saveTreeOpenState();
      if (this.previousSelectedTabIndex === 0 ) {
        this.firstTabTreeOpenedItems = this.customSummary.treeOpenedItems.concat();
        this.firstTabTreeClosedItems = this.customSummary.treeClosedItems.concat();
      } else {
        this.secondTabTreeOpenedItems = this.customSummary.treeOpenedItems.concat();
        this.secondTabTreeClosedItems = this.customSummary.treeClosedItems.concat();
      }
    }
    // set hasfocus
    const hasFocus: string = ExternalInterface.call('eval', 'document.hasFocus()');
    this.requestParams["hasFocus"] = hasFocus;
    this.requestParams["selectedTab"] = this.tabCategoryList.getSelectedTab().id;

    if (this.tabCategoryList.selectedIndex !== -1) {
      this.requestParams["selectedCategory"] = this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName;
    }
    this.requestParams["fromWorkFlow"]="false";
    this.requestParams["currThreshold"] = this.currBox.selected == true ? "Y": "N";
    this.requestParams["facilityGuiId"] =this.facilityGui;
    this.requestParams["sqlParams"] =this.sqlParams;
    this.requestParams["scenarioId"] =this.firstLoad?this.treeLevelValue:this.scenarioId;
    this.requestParams["isScenarioAlertable"] = this.customSummary.getIsScenarioAlertable();

      this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
      this.requestParams["fromScreenFacility"] =this.fromScreenFacility;
      
    this.requestParams['currencyCode'] = this.tooltipCurrencyCode;
    // this.requestParams['facilityId'] = this.tooltipFacilityId;
    this.requestParams['selectedDate'] = this.tooltipSelectedDate;
    this.requestParams['selectedAccountId'] = this.tooltipSelectedAccount;
    this.requestParams['selectedMvtId'] = this.tooltipMvtId;
    this.requestParams['selectedMatchId'] = this.tooltipMatchId;
    this.requestParams['selectedTreeItemQuery'] = selectedTreeItemQuery;
    this.requestParams['statusChanged'] = this.statusChanged;
    if(this.customSummary.tree && this.customSummary.tree.selectedItem && this.customSummary.tree.selectedItem['record_scenario_instances']) {
      if (this.customSummary.tree.selectedItem['record_scenario_instances'] == "Y") {
        this.requestParams["selectedSort"] = this.customSummary.summaryGrid.sortedGridColumnId+"|"+ (this.customSummary.summaryGrid.sortDirection ? "DESC" : "ASC");
        selectedFilter = this.getfilteredGridColumnsForInstanceGrid();
      }else{
        selectedSort = this.customSummary.getSortedGridColumn();
        this.requestParams["selectedSort"] = selectedSort;
        selectedFilter = this.getfilteredGridColumns();
      }

    }else {
      selectedSort = this.customSummary.getSortedGridColumn();
      this.requestParams["selectedSort"] = selectedSort;
      selectedFilter = this.getfilteredGridColumns();
    }
    this.requestParams["selectedFilter"] = selectedFilter;
    
    if(this.tooltipOtherParams != null){
        for (var key in this.tooltipOtherParams) {
            if (this.tooltipOtherParams.hasOwnProperty(key))
              this.requestParams[key] = this.tooltipOtherParams[key];
          }

    }

    // this.lastSelectedNode=this.customSummary.tree.selectedItem.id;
    this.actionPath = 'scenarioSummary.do?';
    this.actionMethod = 'method=getScenarioSummaryDetails';
    this.summaryData.encodeURL=false;
    this.summaryData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.summaryData.send(this.requestParams);
    if(this.customSummary.tree.selectedItem != null)
      this.lastSelectedValue = this.customSummary.tree.selectedItem.id
  }

 /**
   * updateScenarioDetails
   * This function is called to load data relevent to scenario
   */
  updateScenarioDetails(): void {
    // Initialize communication objects
    this.summaryData.cbStart = this.startOfComms.bind(this);
    this.summaryData.cbStop = this.endOfComms.bind(this);
    let selectedTreeItemQuery = "";
    selectedTreeItemQuery = this.extractParentFilterDataFromNode(this.customSummary.tree.selectedItem,selectedTreeItemQuery);
    this.summaryData.cbResult = (data) => {
      this.summaryDataResult(data);
    };
    this.summaryData.cbFault = this.inputDataFault.bind(this);
    this.summaryData.encodeURL = false;
    this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
    if (this.firstLoad) {
      this.requestParams["firstLoad"] = 'true';
    } else {
      this.requestParams["firstLoad"] = 'false';
    }
    // set hasfocus
    const hasFocus: string = ExternalInterface.call('eval', 'document.hasFocus()');
    this.requestParams["hasFocus"] = hasFocus;
    this.requestParams["selectedTab"] = this.tabCategoryList.getSelectedTab().id;

    if (this.tabCategoryList.selectedIndex !== -1) {
      this.requestParams["selectedCategory"] = this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName;
    }
    this.requestParams["fromScreenFacility"] =this.fromScreenFacility;
    this.requestParams["facilityGuiId"] =this.facilityGui;
    this.requestParams["sqlParams"] =this.sqlParams;
    this.requestParams["scenarioId"] =this.scenarioId;
    this.requestParams['selectedTreeItemQuery'] = selectedTreeItemQuery;
    this.actionPath = 'scenarioSummary.do?';
    this.actionMethod = 'method=getScenarioSummaryDetails';
    this.summaryData.encodeURL=false;
    this.summaryData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.summaryData.send(this.requestParams);
  }

  /**
   * inputDataResult
   * @param event:ResultEvent
   * This method is called when result event occurs.
   */
  inputDataResult(event): void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.lostConnectionText.visible = false;
      if (this.jsonReader.getScreenAttributes().displayedDate !== '') {
          this.displayedDate = this.jsonReader.getScreenAttributes()["displayedDate"];
      }
      if (this.jsonReader.getScreenAttributes().dateFormat !== '') {
          //resolvedOnDate
          this.dateFormat = this.jsonReader.getScreenAttributes()["dateFormat"];
          this.resolvedOnDate.formatString = this.dateFormat.toLowerCase();
      }
      if (this.jsonReader.getScreenAttributes().currencyFormat !== '') {
        //resolvedOnDate
        this.currencyFormat = this.jsonReader.getScreenAttributes()["currencyFormat"];
    }
      
      if (this.jsonReader.getScreenAttributes().lastRefTime !== '') {
        const lastRef = this.jsonReader.getScreenAttributes()["lastRefTime"];
        this.lastRefTime.text = lastRef.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');
        this.lastRefTime.visible = true;
      }
      if (this.jsonReader.getRequestReplyStatus()) {
        if (this.lastRecievedJSON !== this.prevRecievedJSON) {

          if (this.autoRefresh !== null) {
            this.autoRefresh.stop();
          }
          this.resolvedOnDate.text=this.displayedDate;
          //disable buttons
          this.disableInterface();
          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.selectedEntity.text = this.entityCombo.selectedItem.value;
          this.currBox.selected = this.jsonReader.getScreenAttributes().currencythreshold === 'Y';
          this.zeroBox.selected = Boolean(this.jsonReader.getScreenAttributes().hidezerocounts);
          this.alertBox.selected =Boolean( this.jsonReader.getScreenAttributes().alertablescenarios);
          this.popupScenarios = this.jsonReader.getScreenAttributes().popupScenarios === 'true' ? 'true' : 'false' ;
          this.flashScenarios = this.jsonReader.getScreenAttributes().flashScenarios === 'true' ? 'true' : 'false' ;
          this.emailScenarios = this.jsonReader.getScreenAttributes().emailScenarios === 'true' ;

          if (this.firstLoad) {
            this.tabDataCategory = [];

            if (this.tabCategoryList.getTabChildren().length === 0) {
              if (this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab) {
                for (let i = 0; i < 2; i++) {
                  this.tabDataCategory[i] = this.tabCategoryList.addChild(Tab) as Tab;
                  this.tabDataCategory[i].id = this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].tabId;
                  this.tabDataCategory[i].label = this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].tabName;
                  this.tabDataCategory[i].tabName = this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].tabName;
                  this.tabDataCategory[i].count = this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].count;
                }

              }
            }
          }
          const index = Number(this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.selectedIndex.index);
          this.tabCategoryList.selectedIndex = index;
        }
      } else {
        SwtUtil.getPredictMessage('error.contactAdmin', null);
        this.swtAlert.error(SwtUtil.getPredictMessage('error.contactAdmin', null) + '\n' + this.jsonReader.getRequestReplyMessage() + '\n' + this.jsonReader.getRequestReplyLocation());
      }   
      
      //this.refreshRate= parseInt(this.jsonReader.getScreenAttributes().autoRefreshRate);
      // if main Timer is not initialized
      if (this.autoRefresh == null) {
        if (this.refreshRate < 5) {
          this.refreshRate = 60;
        }
        // initilaize main timer
        this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
        this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
      }
      this.prevRecievedJSON = this.lastRecievedJSON;
      this.loadSummary();
    }
    this.previousSelectedTabIndex = this.tabCategoryList.selectedIndex;
 

    if (this.autoRefresh != null) {
      if (!this.autoRefresh.running) {
        this.autoRefresh.start();
      }
    }
  }

  /**
   * summaryDataResult
   * @param event:ResultEvent
   * This method is called when result event occurs.
   */
  summaryDataResult(event) {
    let isFirstLoad = false;
    if (this.summaryData.isBusy()) {
      this.summaryData.cbStop();
    } else {
      this.lastRecievedSummaryJSON = (event);
      this.summaryJSONReader.setInputJSON(this.lastRecievedSummaryJSON);
      if (this.summaryJSONReader.getRequestReplyStatus()) {
        if (this.lastRecievedSummaryJSON !== this.prevRecievedSummaryJSON) {
          //initialize grid selection
          this.customSummary.summaryGrid.selectedIndex=-1;
          //get option ID types combo values
          this.optionIdTypes = this.summaryJSONReader.getSelects()['select'].find(x => x.id == "otherIdTypeCombo").option;
          this.customSummary.dataProvider(this.lastRecievedSummaryJSON);
          if (this.summaryJSONReader.getSingletons().scenarioTotalForTab1) {
            this.tabCategoryList.getChildAt(0).label = this.tabDataCategory[0].tabName + '(' + this.summaryJSONReader.getSingletons().scenarioTotalForTab1 + ')';
          }
          if (this.summaryJSONReader.getSingletons().scenarioTotalForTab2 ) {
            this.tabCategoryList.getChildAt(1).label =  this.tabDataCategory[1].tabName + '(' + this.summaryJSONReader.getSingletons().scenarioTotalForTab2 + ')';
          }
          if (this.tabCategoryList.selectedIndex === 0 ) {
            this.lastSelectedId = this.fisrtTablastSelectedId;
            this.customSummary.treeOpenedItems = this.firstTabTreeOpenedItems.concat();
            this.customSummary.treeClosedItems = this.firstTabTreeClosedItems.concat();
          } else {
            this.lastSelectedId = this.secondTablastSelectedId;
            this.customSummary.treeOpenedItems = this.secondTabTreeOpenedItems.concat();
            this.customSummary.treeClosedItems = this.secondTabTreeClosedItems.concat();
          }

          if (this.customSummary.treeOpenedItems.length === 0) {
            this.customSummary.tree.openItems = [];
          }

          if(this.customSummary.treeClosedItems.length === 0) {
            this.customSummary.tree.closeItems = [];
          }
          this.customSummary.setBaseURL(this.baseURL);
          this.customSummary.setActionPath(this.actionPath);
          // this.customSummary.tree.selectedItem=this.lastRecievedSummaryJSON.scenarioinstancedetails.tree.root.node[0].node
          //this.customSummary.dataProvider(this.lastRecievedSummaryJSON);
          if (this.firstLoad) {
            this.customSummary.tree.expandAll(CustomTree.LEVEL_2_STR);
            if(this.selectedNodeId)
              this.customSummary.tree.selectNodeByAttribute("id", this.selectedNodeId);
            else
              this.customSummary.tree.selectNodeByAttribute('levelAsInt','2',true)
            //this.customSummary.setFirstSubNode(this.lastRecievedSummaryJSON);
            this.firstLoad = false;
            isFirstLoad = true;
          } else {
            this.customSummary.openTreeItems();
            // this.customSummary.tree.expandAll(CustomTree.LEVEL_2_STR);
            this.customSummary.tree.selectNodeByAttribute("id", this.lastSelectedNode?this.lastSelectedNode:"N_"+this.scenarioId);
          }
           if (this.summaryHbox.visible === false) {
            this.summaryHbox.visible = true;
          }

        }
      } else {
        this.swtAlert.error(SwtUtil.getPredictMessage('error.contactAdmin', null) + '\n' + this.summaryJSONReader.getRequestReplyMessage() + '\n' + this.summaryJSONReader.getRequestReplyLocation());
      }
      this.prevRecievedSummaryJSON = this.lastRecievedSummaryJSON;
    }
    this.refreshTreeItemRender();
    setTimeout(() => {
      
      this.handleButtonsStatus();
    }, 100);
    this.previousSelectedTabIndex = this.tabCategoryList.selectedIndex;
    this.customSummary.summaryGrid.selectedIndex= this.selectedIndex;
    if(isFirstLoad && this.customSummary.tree && this.customSummary.tree.selectedItem){

      this.scenarioId=this.getScenarioId(this.customSummary.tree.selectedItem,this.customSummary.tree.selectedItem.level);
      this.requestParams["refreshGridOnly"] = 'true';
      this.loadSummary();
    }
  }


  /**
   * refreshTreeItemRender
   * This method is called to ref+resh tree itemRenders if any update occurs on the summary tree
   */
  refreshTreeItemRender(): void {
    // this.summary.tree.invalidateList();
  }

  /**
   * summaryTreeEventHandler
   * @param event:Event
   * This method is called when Event occurs on the tree tree
   */

  private lastSelectedValue = null;
  summaryTreeEventHandler(event): void {
    // this.customSummary.summaryGrid.resetFilter();
   
    // this.customSummary.summaryGrid.filteredGridColumns="";
    let refreshNeeded = false;
    this.customSummary.summaryGrid.selectedIndex=-1;
    //let newSelectedNode = this.customSummary.tree.selectedItem.data.treeLevelValue;
    let newSelectedNode = this.customSummary.tree.selectedItem.data.treeLevelValue;
    if(newSelectedNode != this.lastSelectedValue)
       refreshNeeded = true;
    
    this.lastSelectedValue = newSelectedNode;
       
    this.lastSelectedNode=this.customSummary.tree.selectedItem.id;
    let level=event.level;
    let newScenarioId=this.getScenarioId(event,level);

    if (refreshNeeded) {
      this.requestParams["refreshGridOnly"] = 'true';
      this.scenarioId = newScenarioId;
      this.customSummary.summaryGrid.resetFilter();
      this.customSummary.summaryGrid.filteredGridColumns="";
      this.customSummary.resetGridData();
      this.loadSummary();
    }
  }


  getScenarioId(event, level) {
    var scenarioId = null;

    switch (level) {
      case "Level1":
        scenarioId =this.scenarioId;
        break;
      case "Level2":
        scenarioId =event.data.treeLevelValue;
        break;
      case "Level3":
        scenarioId = event.data.parentData.treeLevelValue;
        break;
      case "Level4":
        scenarioId = event.data.parentData.parentData.treeLevelValue ;
        break;
      case "Level5":
        scenarioId = event.data.parentData.parentData.parentData.treeLevelValue;
        break;
      case "Level6":
          scenarioId = event.data.parentData.parentData.parentData.parentData.treeLevelValue;
          break;
      case "Level7":
          scenarioId = event.data.parentData.parentData.parentData.parentData.parentData.treeLevelValue;
        break;
      default:
        break;
    }
    return scenarioId;
  }

  cellLogic(selectedRowData): void {
    if(this.customSummary.summaryGrid.selectedIndex>-1){
    this.scenarioId=selectedRowData.target.data.scenarioId;
    let fieldName  = selectedRowData.target.field;
    let data = selectedRowData.target.data;
    if(data.slickgrid_rowcontent.attributesXml){
    this.attributeJSON=data.slickgrid_rowcontent.attributesXml.code;

    if(this.attributeJSON && this.isValidJSON(this.attributeJSON)){
    let jsonData=  JSON.parse( this.attributeJSON);
    jsonData = JSON.stringify(jsonData, (key, value) => {
      if (key !== "" && key !== "rowset" && key !== "row" && typeof value === "object") {
        return Object.entries(value).reduce((acc, [k, v]) => {
          acc[k] = JSON.stringify(v);
          return acc;
        }, {});
      }
      return value;
    }, 4);
    this.attributeJSON =this.htmlEntities(jsonData.replace(/\\"/g, '"').replace(/"{/g, '{').replace(/}"/g, '}'));
    }
  }
    let isClickable = (data.slickgrid_rowcontent[fieldName]) ? data.slickgrid_rowcontent[fieldName].clickable : null;
    if (isClickable) {
      if(this.customSummary.rcdScenInstance=="Y")
      this.clickLink('fromLink');
      else
      this.openFacility(data,fieldName);
    }
  }
  }


  htmlEntities(str) {
    try {
      return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
      replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
    } catch (e) {
      console.log('error', e, str)
    }
  }
  
  /*clickLink(): void {
    try {
      this.win =  SwtPopUpManager.createPopUp(this, AttributeXML, {
        title: "Scenario Instance Attributes",
        attributeXmlText:this.attributeXML,
      });
      this.win.isModal = true;
      this.win.enableResize = false;
      this.win.width = '400';
      this.win.height = '500';
      this.win.showControls = true;
      this.win.id = "AttributeXML";
      this.win.display();

    }
    catch (error) {
    }
  }*/

    clickLink(src): void {
      this.requestParams = [];
      this.detailsData.cbStart = this.startOfComms.bind(this);
      this.detailsData.cbStop = this.endOfComms.bind(this);
      this.detailsData.cbFault = this.inputDataFault.bind(this);
      this.detailsData.encodeURL = false;
      // Define the action to send the request
      this.actionPath = "scenarioSummary.do?";
      // Define method the request to access
      this.actionMethod = 'method=getInstanceXml';
      this.requestParams['menuAccessId'] = this.menuAccessId;
      this.requestParams['instanceId'] = this.customSummary.summaryGrid.selectedItem.id.content;
      this.detailsData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.detailsData.cbResult = (event) => {
        this.getInstanceDetails(event);
      };
      this.detailsData.send(this.requestParams);
      this.source=src;
  }

  getInstanceDetails(event){
    if (this.detailsData.isBusy()) {
      this.detailsData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyStatus()) {
        if (this.lastRecievedJSON !== this.prevRecievedJSON) {
          let json= (this.jsonReader.getSingletons().instanceXml).replace(/#/g, ">");
          if(json && this.isValidJSON(json)){
          let jsonData=  JSON.parse(json);
          jsonData = JSON.stringify(jsonData, (key, value) => {
            if (key !== "" && key !== "rowset" && key !== "row" && typeof value === "object") {
              return Object.entries(value).reduce((acc, [k, v]) => {
                acc[k] = JSON.stringify(v);
                return acc;
              }, {});
            }
            return value;
          }, 4);
          this.attributeJSON =this.htmlEntities(jsonData.replace(/\\"/g, '"').replace(/"{/g, '{').replace(/}"/g, '}'));
          }else{
           this.attributeJSON= json; 
          }

          if(this.source=='fromLink'){
          this.win =  SwtPopUpManager.createPopUp(this, AttributeXML, {
            title: "Scenario Instance Attributes",
            attributeXmlText:this.attributeJSON,
          });
          this.win.isModal = true;
          this.win.enableResize = false;
          this.win.width = '450';
          this.win.height = '500';
          this.win.showControls = true;
          this.win.id = "AttributeXML";
          this.win.display();
        }else{
          this.openInstDetails(json);
        }
        }
      }
    }
  }

  /**
   * filteredGridColumns
   * This function is used to get the filtered grid columns
   */
  private  getfilteredGridColumns(): string {
    try {
      let selectedFilter='';
      let filteredColumnsFields=[];
      if (this.customSummary.summaryGrid.filteredGridColumns !== '') {
        let gridColumns=this.customSummary.summaryGrid.getFilterColumns();
        let filteredColumns=this.customSummary.summaryGrid.filteredGridColumns;
        for (let i=0; i < gridColumns.length; i++) {
          filteredColumnsFields[i] = gridColumns[i].field;
        }
        if(filteredColumns != '') {
          let filterdValues = filteredColumns.split('|');
          for (let i=0; i < filteredColumnsFields.length-1; i++) {
            selectedFilter=selectedFilter+filterdValues[i]+'|';
          }
        } else {
          selectedFilter = this.customSummary.summaryGrid.getFilteredGridColumns();
        }
      } else {
        selectedFilter = "";
        return selectedFilter;
      }
      return selectedFilter.slice(4,selectedFilter.length);
    } catch (error) {
      // SwtUtil.logError(error, SwtUtil.INPUT_MODULE_ID, this.getQualifiedClassName(this)  , "getfilteredGridColumns", this.errorLocation);
    }
  }


 /**
   * filteredGridColumns
   *
   *
   * This function is used to get the filtered grid columns
   */
  private  getfilteredGridColumnsForInstanceGrid():string
  {

    var selectedFilter: string = "";
    var filteredColumnsFields=[];
    var gridColumns=this.customSummary.summaryGrid.getFilterColumns();
    var filteredColumns=this.customSummary.summaryGrid.filteredGridColumns;
    let databaseDateFormat = "dd/MM/yyyy" == this.dateFormat?"dd/mm/yyyy hh24:mi:ss":"mm/dd/yyyy hh24:mi:ss" ;

    try
    {
      // Iterate for gridColumns
      for (var i=0; i < gridColumns.length; i++)
      {
        filteredColumnsFields[i] = gridColumns[i].field;
      }
      if(filteredColumns != ''){
        let filterdValues = filteredColumns.split('|');
        for (var i=0; i < filteredColumnsFields.length; i++)
        {
    if(filterdValues[i] != "") {
      if(filterdValues[i] != "All" && filterdValues[i] != undefined) {
        var underscoreExist: string = filteredColumnsFields[i][filteredColumnsFields[i].length -1] ;
        if (underscoreExist == "_" ) {
          filteredColumnsFields[i] = filteredColumnsFields[i].slice(0, -1);
        }

        if( filteredColumnsFields[i] == "_id" ){
          selectedFilter= selectedFilter + "ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "scenarioId" ){
          selectedFilter= selectedFilter + "SCENARIO_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "uniqueIdentifier" ){
          selectedFilter= selectedFilter + "UNIQUE_IDENTIFIER"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "status" ){

          let statusCode = '';
          switch (filterdValues[i]) {
            case 'Active':
              statusCode = "A";
              break;
            case 'Resolved':
              statusCode = "R";
              break;
            case 'Pending':
              statusCode = "P";
              break;
            case 'Overdue':
              statusCode = "O";
              break;
              default:
                statusCode = "A";
          }



          selectedFilter= selectedFilter + "STATUS"+ "=" +"'"+ statusCode+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "raisedDatetime" ){
          // selectedFilter= selectedFilter + "RAISED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
          selectedFilter= selectedFilter  +  "RAISED_DATETIME"+ "="  + "TO_DATE ('"+filterdValues[i]  +"' , '"+databaseDateFormat+ "')" + " and " ;
        }else  if( filteredColumnsFields[i] == "lastRaisedDatetime" ){
          // selectedFilter= selectedFilter + "LAST_RAISED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
          selectedFilter= selectedFilter  +  "LAST_RAISED_DATETIME"+ "="  + "TO_DATE ('"+filterdValues[i]  +"' , '"+databaseDateFormat+ "')" + " and " ;
        }else  if( filteredColumnsFields[i] == "resolvedDatetime" ){
          // selectedFilter= selectedFilter + "RESOLVED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
          selectedFilter= selectedFilter  +  "RESOLVED_DATETIME"+ "="  + "TO_DATE ('"+filterdValues[i]  +"' , '"+databaseDateFormat+ "')" + " and " ;
        }else  if( filteredColumnsFields[i] == "resolvedByUser" ){
          selectedFilter= selectedFilter + "RESOLVED_BY_USER"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "eventsLaunchStatus" ){
          let eventLunchStatusCode = '';
          switch (filterdValues[i]) {
            case 'No events to launch':
              eventLunchStatusCode = "N";
              break;
            case 'Waiting to Launch':
              eventLunchStatusCode = "W";
              break;
            case 'Launched':
              eventLunchStatusCode = "L";
              break;
            case 'Failed':
              eventLunchStatusCode = "F";
                // expected output: "Mangoes and papayas are $2.79 a pound."
              break;
              default:
                eventLunchStatusCode = "W";
          }
          selectedFilter= selectedFilter + "EVENTS_LAUNCH_STATUS"+ "=" +"'"+ eventLunchStatusCode+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "hostId" ){
          selectedFilter= selectedFilter + "HOST_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "entityId" ){
          selectedFilter= selectedFilter + "ENTITY_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "currencyCode" ){
          selectedFilter= selectedFilter + "CURRENCY_CODE"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "accountId" ){
          selectedFilter= selectedFilter + "ACCOUNT_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "amount" ){
          let amount;
          if(filterdValues[i] != null && filterdValues[i] != undefined) {
            if (this.currencyFormat == "currencyPat2") {
              amount = Number(filterdValues[i].replace(/\./g, '').replace(/,/g, '.'));
            }else {
              amount = Number(filterdValues[i].replace(/,/g, ''));
            }
            
          }

          selectedFilter= selectedFilter + "AMOUNT"+ "=" +"'"+ amount+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "sign" ){
          selectedFilter= selectedFilter + "SIGN"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "overThreshold" ){
          selectedFilter= selectedFilter + "OVER_THRESHOLD"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "movementId" ){
          selectedFilter= selectedFilter + "MOVEMENT_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "matchId" ){
          selectedFilter= selectedFilter + "MATCH_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "sweepId" ){
          selectedFilter= selectedFilter + "SWEEP_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "paymentId" ){
          selectedFilter= selectedFilter + "PAYMENT_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "valueDate" ){
          // selectedFilter= selectedFilter + "VALUE_DATE"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
          selectedFilter= selectedFilter  +  "VALUE_DATE"+ "="  + "TO_DATE ('"+filterdValues[i]  +"' , '"+databaseDateFormat+ "')" + " and " ;
        }else  if( filteredColumnsFields[i] == "otherId" ){
          selectedFilter= selectedFilter + "OTHER_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else  if( filteredColumnsFields[i] == "otherIdType" ){
          selectedFilter= selectedFilter + "OTHER_ID_TYPE"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }


      }
    }
    else {
      // selectedFilter= selectedFilter + filteredColumnsFields[i] + " is null" +" and ";


      var underscoreExist: string = filteredColumnsFields[i][filteredColumnsFields[i].length -1] ;
        if (underscoreExist == "_" ) {
          filteredColumnsFields[i] = filteredColumnsFields[i].slice(0, -1);
        }
        if( filteredColumnsFields[i] == "scenarioId" ){
          selectedFilter= selectedFilter + "SCENARIO_ID"+  " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "uniqueIdentifier" ){
          selectedFilter= selectedFilter + "UNIQUE_IDENTIFIER" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "status" ){

          selectedFilter= selectedFilter + "STATUS" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "raisedDatetime" ){
          // selectedFilter= selectedFilter + "RAISED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
          selectedFilter= selectedFilter  +  "RAISED_DATETIME" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "lastRaisedDatetime" ){
          // selectedFilter= selectedFilter + "LAST_RAISED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
          selectedFilter= selectedFilter  +  "LAST_RAISED_DATETIME" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "resolvedDatetime" ){
          // selectedFilter= selectedFilter + "RESOLVED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
          selectedFilter= selectedFilter  +  "RESOLVED_DATETIME" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "resolvedByUser" ){
          selectedFilter= selectedFilter + "RESOLVED_BY_USER" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "eventsLaunchStatus" ){
          selectedFilter= selectedFilter + "EVENTS_LAUNCH_STATUS" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "hostId" ){
          selectedFilter= selectedFilter + "HOST_ID" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "entityId" ){
          selectedFilter= selectedFilter + "ENTITY_ID" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "currencyCode" ){
          selectedFilter= selectedFilter + "CURRENCY_CODE" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "accountId" ){
          selectedFilter= selectedFilter + "ACCOUNT_ID" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "amount" ){

          selectedFilter= selectedFilter + "AMOUNT" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "sign" ){
          selectedFilter= selectedFilter + "SIGN" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "overThreshold" ){
          selectedFilter= selectedFilter + "OVER_THRESHOLD" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "movementId" ){
          selectedFilter= selectedFilter + "MOVEMENT_ID" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "matchId" ){
          selectedFilter= selectedFilter + "MATCH_ID" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "sweepId" ){
          selectedFilter= selectedFilter + "SWEEP_ID" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "paymentId" ){
          selectedFilter= selectedFilter + "PAYMENT_ID" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "valueDate" ){
          // selectedFilter= selectedFilter + "VALUE_DATE"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
          selectedFilter= selectedFilter  +  "VALUE_DATE" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "otherId" ){
          selectedFilter= selectedFilter + "OTHER_ID" + " is null" +" and ";
        }else  if( filteredColumnsFields[i] == "otherIdType" ){
          selectedFilter= selectedFilter + "OTHER_ID_TYPE" + " is null" +" and ";
        }


    }


    
        }
      }

      selectedFilter = selectedFilter.substring(0, selectedFilter.length - 5);
      return selectedFilter;
    }
    catch (error)
    {
      console.log('error', error)
    }
  }
  

  /**
   * doUpdateSortFilter()
   * @param event:Event
   * Method called when update filter and/or sort.
   **/
  private  doUpdateSortFilter(): void {
    let selectedSort: string = null;
    let selectedFilter: string =null;
    const zeroBalances: string = this.zeroBox.selected.toString();
    const currThreshold: boolean = this.currBox.selected;
    const alertScenarios: string = this.alertBox.selected.toString();

    try {
      this.customSummary.summaryGrid.selectedItem = null;
      this.requestParams=[];
      this.firstLoad = false;
      this.requestParams["refreshGridOnly"] = 'true';
      
      if (this.customSummary.rcdScenInstance == "Y") {
        this.requestParams["selectedSort"] = this.customSummary.summaryGrid.sortedGridColumnId+"|"+ (this.customSummary.summaryGrid.sortDirection ? "DESC" : "ASC");
        selectedFilter = this.getfilteredGridColumnsForInstanceGrid();
      }else{
        selectedSort = this.customSummary.getSortedGridColumn();
        this.requestParams["selectedSort"] = selectedSort;
        selectedFilter = this.getfilteredGridColumns();
      }

      this.requestParams["selectedFilter"] = selectedFilter;
      this.requestParams["callerMethod"] = this.callerMethod;
      this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
      this.requestParams["userId"] = this.currentUser;
      this.requestParams["menuAccessId"] = this.menuAccessId;
      this.requestParams["zeroBalances"] = zeroBalances;
      this.requestParams["currThreshold"] = currThreshold == true ? "Y": "N";
      this.requestParams["selectedScenario"] = this.customSummary.getSelectedItemID() ;
      this.requestParams["isScenarioAlertable"] = this.customSummary.getIsScenarioAlertable();
      this.requestParams["dividerPosition"] = this.customSummary.getDividerPosition();
      // this.requestParams["selectedSort"] = this.summary.getSortedGridColumn();
      this.requestParams["alertScenarios"] = alertScenarios;
      this.requestParams["flashScenarios"] = this.flashScenarios;
      this.requestParams["popupScenarios"] = this.popupScenarios;
      this.requestParams["emailScenarios"] = this.emailScenarios.toString();
      this.requestParams["fromScreenFacility"] =this.fromScreenFacility;
      this.customSummary.saveTreeOpenState();
      if (this.previousSelectedTabIndex === 0 ) {
        this.firstTabTreeOpenedItems = this.customSummary.treeOpenedItems.concat();
        this.firstTabTreeClosedItems = this.customSummary.treeClosedItems.concat();
      } else {
        this.secondTabTreeOpenedItems = this.customSummary.treeOpenedItems.concat();
        this.secondTabTreeClosedItems = this.customSummary.treeClosedItems.concat();
      }
      this.loadSummary();

    } catch (error) {

    }
  }


  /**
   * updateData
   * @param refresh:String
   * Update the data, this is called whenever a fresh of the data is required.
   */
  updateData(refresh: string): void {
    this.selectedIndex=this.customSummary.summaryGrid.selectedIndex; 
    const zeroBalances: string = this.zeroBox.selected.toString();
    const currThreshold: boolean = this.currBox.selected;
    const alertScenarios: string = this.alertBox.selected.toString();
    //const isActive: boolean = this.amountThresholdBox.selected;
    this.requestParams = [];
    this.requestParams["callerMethod"] = this.callerMethod;
    this.requestParams["resolvedOnDate"]= this.resolvedOnDate.text;
    this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
    this.requestParams["userId"] = this.currentUser;
    this.requestParams["menuAccessId"] = this.menuAccessId;
    this.requestParams["zeroBalances"] = zeroBalances;
    this.requestParams["currThreshold"] = currThreshold == true ? "Y": "N";
    //this.requestParams["isActive"] = isActive == true ? "Y": "N";
    this.requestParams["selectedScenario"] = this.customSummary.getSelectedItemID() ;
    this.requestParams["isScenarioAlertable"] = this.customSummary.getIsScenarioAlertable();
    this.requestParams["dividerPosition"] = this.customSummary.getDividerPosition();
    this.requestParams["selectedSort"] = this.customSummary.getSortedGridColumn();
    this.requestParams["alertScenarios"] = alertScenarios;
    this.requestParams["flashScenarios"] = this.flashScenarios;
    this.requestParams["popupScenarios"] = this.popupScenarios;
    this.requestParams["emailScenarios"] = this.emailScenarios.toString();
    if(this.customSummary.tree.selectedItem != null)
      this.scenarioId=this.getScenarioId(this.customSummary.tree.selectedItem,this.customSummary.tree.selectedItem.level); 
    else 
      this.scenarioId = "";
    this.requestParams["scenarioId"] =this.scenarioId;
    this.requestParams["selectedTab"] = this.tabCategoryList.getSelectedTab().id;
    this.requestParams["fromScreenFacility"] =this.fromScreenFacility;
    if (this.tabCategoryList.selectedIndex !== -1) {
      this.requestParams["selectedCategory"] = this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName;
    }
    if(this.customSummary.tree.selectedItem) {
      if (this.previousSelectedTabIndex === 0 ) {
        this.fisrtTablastSelectedId = this.customSummary.tree.selectedItem.id;
      } else {
        this.secondTablastSelectedId= this.customSummary.tree.selectedItem.id;
      }
    } else {
      if (this.previousSelectedTabIndex === 0 ) {
        this.fisrtTablastSelectedId = null;
      } else {
        this.secondTablastSelectedId= null;
      }
    }

    // this.customSummary.tree.selectedIndex = -1 ;
    this.customSummary.saveTreeOpenState();
    if (this.previousSelectedTabIndex === 0 ) {
      this.firstTabTreeOpenedItems = this.customSummary.treeOpenedItems.concat();
      this.firstTabTreeClosedItems = this.customSummary.treeClosedItems.concat();
    } else {
      this.secondTabTreeOpenedItems = this.customSummary.treeOpenedItems.concat();
      this.secondTabTreeClosedItems = this.customSummary.treeClosedItems.concat();
    }
    if (refresh === this.TREE) {
      this.loadSummary();
    } else if (refresh === this.SCENARIO) {
      let selectedTreeItemQuery = "";
      if(this.customSummary.tree.selectedItem) {
        selectedTreeItemQuery = this.extractParentFilterDataFromNode(this.customSummary.tree.selectedItem,selectedTreeItemQuery);
      }
      this.requestParams["isScenarioAlertable"] = this.customSummary.getIsScenarioAlertable();
      this.requestParams['selectedTreeItemQuery'] = selectedTreeItemQuery;
      this.inputData.send(this.requestParams);
    }
    this.previousSelectedTabIndex = this.tabCategoryList.selectedIndex;
    this.customSummary.summaryGrid.selectedIndex= this.selectedIndex;

  }  



  /**
   * openedCombo
   * @param event:DropDownEvent
   * When a combobox is open then any requests to the server need to be cancelled
   */
  openedCombo(event): void {
    this.comboOpen = true;
    if (this.inputData.isBusy()) {
      this.inputData.cancel();
      (event.currentTarget as SwtComboBox).interruptComms = true;
    }
  }


  /**
   * closedCombo
   * @param event :DropdownEvent
   * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
   */
  closedCombo(event): void {
    this.comboOpen = false;
    if ((event.triggerEvent !== null) && (event.triggerEvent.type === 'mouseDownOutside')) {
      if ((event.currentTarget as SwtComboBox).interruptComms) {
        (event.currentTarget as SwtComboBox).interruptComms = false;
        this.updateData('scenario');

      }
    }
  }

  /**
   * entityComboChange
   * @param event :Event
   * When there is a change in the in one of the combo's
   */
  entityComboChange(event): void {
    this.comboChange = true;
    this.updateData('scenario');
  }

  /**
   * startOfComms
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.disableInterface();
  }

  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.enableInterface();
  }

  /**
   * inputDataFault
   * @param event:FaultEvent
   * If a fault occurs with the connection with the server then display the lost connection label
   */
  inputDataFault(event): void {
    this.lostConnectionText.visible = true;
    this.invalidComms = event.fault.faultString + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail;
    this.swtAlert.error(this.invalidComms);
    if (this.autoRefresh !== null) {
      if (!this.autoRefresh.running) {
        this.autoRefresh.start();
      }
    }
  }

  /**
   * disableInterface
   * Disable interface, turn off certain UI elements when a request is made to the server
   */
  disableButtons(): void {
    this.resolveButton.enabled = false;
    this.resolveButton.buttonMode = false;
    this.reActiveButton.enabled = false;
    this.reActiveButton.buttonMode = false;
    this.detailsButton.enabled = false;
    this.detailsButton.buttonMode = false;
    this.goToButton.enabled = false;
    this.goToButton.buttonMode = false;
  }
  /**
   * enableInterface
   * Enable interface, turn on certain UI elements when a request is made to the server
   */
  enableButtons(): void {
    this.detailsButton.enabled = true;
    this.detailsButton.buttonMode = true;
    if (this.customSummary.facilityId && this.customSummary.facilityId != "None") {
    this.goToButton.enabled = true;
    this.goToButton.buttonMode = true;
    } else {
      this.goToButton.enabled = false;
      this.goToButton.buttonMode = false;
    }
  }


  /**
   * The function initializes the menus in the right click event on the generic display monitor screen.
   * The links are redirected to their respective pages.
   */
    /**
   * The function initializes the menus in the right click event on the generic display monitor screen.
   * The links are redirected to their respective pages.
   */
     initializeMenus(): void {
      // this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
      // let screenItem: ContextMenuItem = new ContextMenuItem('Show screen details JSON');
      // screenItem.MenuItemSelect = this.showXMLSelect.bind(this);
      // this.screenVersion.svContextMenu.customItems.push(screenItem);
      // let scenarioItem: ContextMenuItem = new ContextMenuItem('Show summary details JSON');
      // scenarioItem.MenuItemSelect = this.showSummaryXMLSelect.bind(this);
      // this.screenVersion.svContextMenu.customItems.push(scenarioItem);
      // this.contextMenu = this.screenVersion.svContextMenu;
  
      this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
      this.screenVersion.svContextMenu.customItems = [];
      let screenItem: ContextMenuItem = new ContextMenuItem('Expand All Groups');
      screenItem.MenuItemSelect = this.contextMenuAction.bind(this,'expandAll');
      this.screenVersion.svContextMenu.customItems.push(screenItem);
      let screenItem2: ContextMenuItem = new ContextMenuItem('Expand To Level 1');
      screenItem2.MenuItemSelect = this.contextMenuAction.bind(this,'expandLevel1');
      this.screenVersion.svContextMenu.customItems.push(screenItem2);
      let screenItem3: ContextMenuItem = new ContextMenuItem('Expand To Level 2');
      screenItem3.MenuItemSelect = this.contextMenuAction.bind(this,'expandLevel2');
      this.screenVersion.svContextMenu.customItems.push(screenItem3);
      let scenarioItem: ContextMenuItem = new ContextMenuItem('Collapse All Groups');
      scenarioItem.MenuItemSelect = this.contextMenuAction.bind(this,'collapseAll');
      this.screenVersion.svContextMenu.customItems.push(scenarioItem);
      this.contextMenu = this.screenVersion.svContextMenu;
    }
  
  
    contextMenuAction(action){
      if('expandAll' == action){
        this.customSummary.tree.expandAll();
      }else if('expandLevel1' == action){
        this.customSummary.tree.collapseAll();
        this.customSummary.tree.expandAll('1');
      }else if('expandLevel2' == action){
        this.customSummary.tree.collapseAll();
        this.customSummary.tree.expandAll('2');
      }else if('collapseAll' == action){
        this.customSummary.tree.collapseAll();
      }
  
      
    }


  /**
   * This function is used to display the XML for the generic's data screen
   * param event
   */
  showXMLSelect(even): void {
    if (this.lastRecievedJSON !== null) {
      this.showXMLPopup = SwtPopUpManager.createPopUp(this,
        JSONViewer,
        {
          jsonData: this.lastRecievedJSON,
        });
      this.showXMLPopup.width = '700';
      this.showXMLPopup.title = 'Summary Details JSON';
      this.showXMLPopup.height = '350';
      this.showXMLPopup.enableResize = false;
      this.showXMLPopup.showControls = true;
      this.showXMLPopup.display();
    } else {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.scenarioSummary.noData', null), SwtUtil.getPredictMessage('alert.scenarioSummary.noData.title', null));
    }

  }


  /**
   * This function is used to show XML screen
   * @param event:ContextMenuEvent
   */
  showSummaryXMLSelect(event): void {

    if (this.lastRecievedSummaryJSON !== null) {
      this.showJSON = SwtPopUpManager.createPopUp(this,
        JSONViewer,
        {
          jsonData: this.lastRecievedSummaryJSON,
        });
      this.showXMLPopup.width = '700';
      this.showXMLPopup.title = 'Summary Details JSON';
      this.showXMLPopup.height = '350';
      this.showXMLPopup.enableResize = false;
      this.showXMLPopup.showControls = true;
      this.showXMLPopup.display();
    } else {
      this.swtAlert.show( SwtUtil.getPredictMessage('alert.scenarioSummary.noData', null), SwtUtil.getPredictMessage('alert.scenarioSummary.noData.title', null));
    }
  }

  /**
   * close the window from the close button
   * @param e:Event
   */
  closeHandler(e): void {
    ExternalInterface.call('close');
  }
  /**
   * refreshFromJsp
   * Refresh the screen if it is already opened and the user try to show the alerts
   */
  refreshFromJsp(): void {
    this.updateData('scenario');
  }
  /**
   * tabBarLabel
   * @param item:Object
   * Method is used to trim the year in the tab bar label
   */
  tabBarLabelCategory(item): string {
    const label: string = item.label;
    return label;
  }



  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call('help');
    } catch (e) {
      SwtUtil.logError(e, 'Predict', 'ScenarioSummary', 'doHelp', 0);
    }
  }

  //get intstances according to selected status
  changeInstStatus(){
     // Initialize communication objects
     let selectedTreeItemQuery = "";
      selectedTreeItemQuery = this.extractParentFilterDataFromNode(this.customSummary.tree.selectedItem,selectedTreeItemQuery);
     this.summaryData.cbStart = this.startOfComms.bind(this);
     this.summaryData.cbStop = this.endOfComms.bind(this);
     this.summaryData.cbResult = (data) => {
       this.summaryDataResult(data);
     };
     this.summaryData.cbFault = this.inputDataFault.bind(this);
     this.summaryData.encodeURL = false;
     this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
     if (this.firstLoad) {
       this.requestParams["firstLoad"] = 'true';
     } else {
       this.requestParams["firstLoad"] = 'false';
     }
     // set hasfocus
     const hasFocus: string = ExternalInterface.call('eval', 'document.hasFocus()');
     this.requestParams["hasFocus"] = hasFocus;
     this.requestParams["selectedTab"] = this.tabCategoryList.getSelectedTab().id;
     this.requestParams["refreshGridOnly"] = 'false';
     if (this.tabCategoryList.selectedIndex !== -1) {
       this.requestParams["selectedCategory"] = this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName;
     }
     this.requestParams["status"] = this.status.selectedValue;
     this.requestParams["resolvedOnDate"]= this.resolvedOnDate.text;
     this.requestParams["fromScreenFacility"] =this.fromScreenFacility;
     this.requestParams['selectedTreeItemQuery'] = selectedTreeItemQuery;
     this.actionPath = 'scenarioSummary.do?';
    /* this.actionMethod = 'method=getScenarioSummaryDetails';
     this.summaryData.encodeURL=false;
     this.summaryData.url = this.baseURL + this.actionPath + this.actionMethod;
     this.summaryData.send(this.requestParams); */
  }

  openInstDetails(xml) {
    let params=[]; 
    params.push ({scenarioId :this.checkColumnsValue("scenarioId"),
    instanceId :this.checkColumnsValue("id"),
    status : this.checkColumnsValue("status"),
    eventStatus : this.checkColumnsValue("eventsLaunchStatus"),
    firstRaisedDate : this.checkColumnsValue("raisedDatetime"),
    lastRaisedDate : this.checkColumnsValue("lastRaisedDatetime"),
    resolvedDate : this.checkColumnsValue("resolvedDatetime"),
    resolvedUser : this.checkColumnsValue("resolvedByUser"),
    uniqueIdent : this.checkColumnsValue("uniqueIdentifier"),
    hostId : this.checkColumnsValue("hostId"),
    entityId : this.checkColumnsValue("entityId"),
    ccyCode : this.checkColumnsValue("currencyCode"),
    acctId : this.checkColumnsValue("accountId"),
    valueDate : this.checkColumnsValue("valueDate"),
    amount : this.checkColumnsValue("amount"),
    sign : this.checkColumnsValue("sign"),
    mvtId : this.checkColumnsValue("movementId"),
    matchId : this.checkColumnsValue("matchId"),
    sweepId : this.checkColumnsValue("sweepId"),
    payment : this.checkColumnsValue("paymentId"),
    otherId : this.checkColumnsValue("otherId"),
    otherIdTypesList : this.optionIdTypes,
    scenOtherIdType : this.checkColumnsValue("otherIdType"),
    firstRaisedUser : this.checkColumnsValue("raisedUser"),
    lastRaisedUser : this.checkColumnsValue("lastRaisedUser"),
    attributesXml : xml,
    currBox : this.currBox.selected === true ? 'Y' : 'N',
    scenarioTitle : this.customSummary.scenarioTitle,
    useGeneric : this.customSummary.useGeneric,
    facilityId : this.customSummary.facilityId,
    facilityName : this.customSummary.facilityName});
    //Mantis 6163 
    ExternalInterface.call("openInstDetails", "openInstanceDetails" , StringUtils.encode64(JSON.stringify(params)));
  }

checkColumnsValue(dataElement){
  let columnVal;
  let column;
  if(this.customSummary.summaryGrid.selectedItem){
    column= this.customSummary.summaryGrid.selectedItem[dataElement];
  }
  if(column){
    columnVal=column.content;
  }else{
    columnVal="";
  }
return columnVal;
}

  goTo(event): void {
    let hostId = this.customSummary.summaryGrid.selectedItem.hostId.content;
    let entityId = this.customSummary.summaryGrid.selectedItem.entityId.content;
    let matchId = this.customSummary.summaryGrid.selectedItem.matchId.content;
    let currencyId = this.customSummary.summaryGrid.selectedItem.currencyCode.content;
    let mvtId = this.customSummary.summaryGrid.selectedItem.movementId.content;
    let sweepId = this.customSummary.summaryGrid.selectedItem.sweepId.content;
    let additionalParams = this.customSummary.summaryGrid.selectedItem.otherId.content;
    ExternalInterface.call("goTo", this.customSummary.facilityId, hostId, entityId, matchId, currencyId, mvtId, sweepId, additionalParams);

  }

    updateStatus(status){
         // Initialize communication objects
         this.summaryData.cbStart = this.startOfComms.bind(this);
         this.summaryData.cbStop = this.endOfComms.bind(this);
         this.summaryData.cbResult = (data) => {
          this.requestParams["refreshGridOnly"] = 'true';
           this.loadSummary();
         };
         this.summaryData.cbFault = this.inputDataFault.bind(this);
         this.summaryData.encodeURL = false;
         this.requestParams["id"] =  this.customSummary.summaryGrid.selectedItem.id.content;
         this.requestParams["oldStatus"] = this.customSummary.summaryGrid.selectedItem.status.content;
         this.requestParams["newStatus"] = status;
         this.actionPath = 'scenarioSummary.do?';
        this.actionMethod = 'method=updateScenInstanceStatus';
         this.summaryData.encodeURL=false;
         this.summaryData.url = this.baseURL + this.actionPath + this.actionMethod;
         this.summaryData.send(this.requestParams); 
    }

    openFacility(data, fieldName){
      const entityId: string = data.slickgrid_rowcontent['entity'].content;
      const currencyId: string = data.slickgrid_rowcontent['ccy'].content;
      const count: string = data.slickgrid_rowcontent[fieldName].content;
      const facilityId: string = this.customSummary.facilityId;
      const facilityName: string = this.customSummary.facilityName;
      const useGeneric: string = this.customSummary.useGeneric;
      const scenarioTitle: string = this.customSummary.scenarioTitle;
      const applyCurrencyThreshold: string = this.currBox.selected === true ? 'Y' : 'N';
      const scenarioId: string = this.customSummary.selectedscenario;
      ExternalInterface.call('openFacility', scenarioTitle, useGeneric, facilityId, facilityName, scenarioId, entityId, currencyId, applyCurrencyThreshold, count);
    }

    checkUserAccess(){
      if(this.customSummary.summaryGrid.selectedIndex>-1){
      // Initialize communication objects
      this.summaryData.cbStart = this.startOfComms.bind(this);
      this.summaryData.cbStop = this.endOfComms.bind(this);
      this.summaryData.cbResult = (data) => {
        this.getUserAccess(data);
      };
      this.summaryData.cbFault = this.inputDataFault.bind(this);
      this.summaryData.encodeURL = false;
      this.requestParams["instanceId"] =  this.customSummary.summaryGrid.selectedItem.id.content;
      this.requestParams["scenarioId"] = this.customSummary.summaryGrid.selectedItem.scenarioId.content;
      this.requestParams["entityId"] =  this.customSummary.summaryGrid.selectedItem.entityId.content;
      this.requestParams["ccyCode"] = this.customSummary.summaryGrid.selectedItem.currencyCode.content;
      this.requestParams["hostId"] = this.customSummary.summaryGrid.selectedItem.hostId.content;
      this.requestParams["fromWorkflow"] = "false";
      this.actionPath = 'scenarioSummary.do?';
      this.actionMethod = 'method=checkUserInstAccess';
      this.summaryData.encodeURL=false;
      this.summaryData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.summaryData.send(this.requestParams);
      }
    }
  
    getUserAccess(event) {
      let hasAccess = event.scenarioDetails.singletons.hasAccess;
      if (hasAccess == 'true') {
        this.handleRctRslvButtons();
      } else {
        this.resolveButton.enabled = false;
        this.resolveButton.buttonMode = false;
        this.reActiveButton.enabled = false;
        this.reActiveButton.buttonMode = false;
      }
      this.enableButtons();
      this.customSummary.summaryGrid.selectedIndex= this.selectedIndex;
    }

  handleRctRslvButtons() {
    let status = this.customSummary.summaryGrid.selectedItem.status.content;
    if (status == "Resolved") {
      this.resolveButton.enabled = false;
      this.resolveButton.buttonMode = false;
      this.reActiveButton.enabled = true;
      this.reActiveButton.buttonMode = true;
    } else if (status == "Pending" || status == "Active") {
      this.resolveButton.enabled = true;
      this.resolveButton.buttonMode = true;
      this.reActiveButton.enabled = false;
      this.reActiveButton.buttonMode = false;
    } else {
      this.resolveButton.enabled = true;
      this.resolveButton.buttonMode = true;
      this.reActiveButton.enabled = true;
      this.reActiveButton.buttonMode = true;
    }
  }
  
    /**
   * Timing result methods
   **/
  dataRefresh(event): void {
    if (!this.comboOpen) {
      this.updateData('scenario');
    }
    this.autoRefresh.stop();
}

isValidJSON(jsonStr: string): boolean {
  try {
    jsonStr = JSON.parse(jsonStr);
    return true;
  } catch (e) {
    return false;
  }
}

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: AlertInstanceSummary }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AlertInstanceSummary],
  entryComponents: []
})
export class AlertInstanceSummaryModule { }