<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" minWidth="1100">
      <HBox width="100%" height="100%" paddingLeft="5" paddingRight="5">
        <VBox width="100%" height="100%" verticalGap="0">
          <HBox width="100%" height="25">
            <HBox width="100%">
              <SwtLabel #startDateLabel id="startDateLabel" styleName="labelBold"></SwtLabel>
              <SwtDateField id="fromDateChooser" #fromDateChooser (change)="validateDateField(fromDateChooser)" width="70"></SwtDateField>
              <SwtLabel #endDateLabel id="endDateLabel" styleName="labelBold"></SwtLabel>
              <SwtDateField id="toDateChooser" #toDateChooser (change)="validateDateField(toDateChooser)" width="70"></SwtDateField>
            </HBox>
            <HBox #pageBox horizontalAlign="right" width="100%" visible="false">
              <SwtCommonGridPagination #numstepper></SwtCommonGridPagination>
            </HBox>
          </HBox>
        </VBox>
      </HBox>
    </SwtCanvas>

    <SwtCanvas id="dataGridContainer" #dataGridContainer paddingBottom="5" styleName="canvasWithGreyBorder"
      marginTop="10" border="false" width="100%" height="100%" minHeight="100" minWidth="1100"></SwtCanvas>

      <SwtCanvas id="canvasButtons" minWidth="1100" width="100%" height="35" marginTop="5">
        <HBox width="100%">
          <HBox paddingLeft="5" paddingTop="2" width="100%" >
            <SwtButton #refreshButton id="refreshButton" (click)="updateData()"></SwtButton>
            <SwtButton #closeButton width="70" (click)="closeHandler()" id="closeButton"></SwtButton>
          </HBox>
          <HBox horizontalAlign="right" width="100%" paddingTop="2" >
            <SwtLabel visible="false" color="red" #dataBuildingText></SwtLabel>
            <SwtLabel visible="false" color="red" #lostConnectionText></SwtLabel>
            <SwtLabel #lastRefTimeLabel fontWeight="normal"></SwtLabel>
            <SwtLabel #lastRefTime fontWeight="normal"></SwtLabel>
      
            <HBox horizontalAlign="right" paddingRight="10" paddingTop="2" >
              <DataExport #dataExport id="dataExport"></DataExport>
              <SwtHelpButton id="helpIcon" #helpIcon (click)="doHelp()"></SwtHelpButton>
              <SwtLoadingImage #loadingImage></SwtLoadingImage>
            </HBox>
          </HBox>
        </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>