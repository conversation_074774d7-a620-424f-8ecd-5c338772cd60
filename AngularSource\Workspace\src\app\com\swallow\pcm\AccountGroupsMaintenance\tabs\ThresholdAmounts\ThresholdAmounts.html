<SwtModule (close)=' popupClosed()' (creationComplete)='onLoad()' height='100%' width='450'>
  <VBox  width='90%' height='100%'>
    <SwtCanvas width="100%" height="80%">
        <VBox width="100%" verticalGap="0">
          <HBox width="100%" >
            <SwtLabel #timeLabel  width="120">
            </SwtLabel>
            <SwtTextInput id="timeInput"
                          #timeInput
                          pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$" maxChars="5" (focusOut)="validateTime(timeInput)" width="100" textAlign="center">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" >
            <SwtLabel #reserveLabel width="120">
            </SwtLabel>
            <SwtTextInput id="reserveInput"
                          #reserveInput textAlign="right"
                          (focusOut)="validateReserve()"
                          width="200">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" >
            <SwtLabel #creditLineLabel width="120">
            </SwtLabel>
            <SwtCheckBox id="useCLCheckBox"
                         #useCLCheckBox>
            </SwtCheckBox>
          </HBox>
        </VBox>
    </SwtCanvas>
      <SwtCanvas id="canvasContainer"
                 width="100%" height="20%">
        <HBox>
          <SwtButton #okButton
                     (click)="save()"
                     id="okButton"
                     width="70" [buttonMode]="false" enabled="true"></SwtButton>
          <SwtButton buttonMode="true"
                     id="cancelButton"
                     width="70"
                     #cancelButton
                     (click)="popupClosed();"></SwtButton>
        </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>
