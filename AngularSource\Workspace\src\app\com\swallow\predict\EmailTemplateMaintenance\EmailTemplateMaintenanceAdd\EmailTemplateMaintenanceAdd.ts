import { Encry<PERSON><PERSON>, SwtCheckBox, SwtRichTextEditor } from 'swt-tool-box';
import { Component, OnInit, ModuleWithProviders, NgModule, ViewChild, ElementRef, OnDestroy, ViewEncapsulation } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, SwtCanvas, SwtLabel, SwtComboBox, SwtDateField, SwtRadioButtonGroup, SwtRadioItem, SwtButton, JSONReader, HTTPComms, SwtUtil, SwtCommonGrid, CommonService, SwtAlert, SwtTextInput, ExternalInterface, SwtLoadingImage, GridRow, Logger, Alert, StringUtils } from 'swt-tool-box';
import moment from 'moment';
declare function validateCurrencyPlaces(strField, strPat, currCode): any;
declare function checkCurrencyPlaces(strField, strPat, currCode): any;
declare var instanceElement: any;

declare const tinymce: any;
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/autoresize';
import 'tinymce/themes/silver';


@Component({
  selector: 'app-emailtemplate-add', templateUrl: './EmailTemplateMaintenanceAdd.html',
  styleUrls: ['./EmailTemplateMaintenanceAdd.css'],
  encapsulation: ViewEncapsulation.None
})
export class EmailTemplateMaintenanceAdd extends SwtModule implements OnInit, OnDestroy {
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /***********SwtCanvas***********/
  /***********SwtDateField***********/
  @ViewChild('templateIdLabel') templateIdLabel: SwtLabel;
  @ViewChild('templateIdTxtInput') templateIdTxtInput: SwtTextInput;
  @ViewChild('descriptionLabel') descriptionLabel: SwtLabel;
  @ViewChild('descriptionTxtInput') descriptionTxtInput: SwtTextInput;
  @ViewChild('subjectContentLabel') subjectContentLabel: SwtLabel;
  //@ViewChild('subjectContentTxtInput') subjectContentTxtInput: SwtTextInput;
  @ViewChild('bodyContentLabel') bodyContentLabel: SwtLabel;
  /***********SwtButton***********/
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  //@ViewChild("bodyContentTxtInput") bodyContentTxtInput: SwtRichTextEditor;


  @ViewChild('keywordsComboLabel') keywordsComboLabel: SwtLabel;
  @ViewChild('keywordsCombo') keywordsCombo: SwtComboBox;

  private swtAlert: SwtAlert;
  private logger: Logger = null;
  private menuAccessId;
  private screenName;
  private parameters;
  /**
  * Data Objects
  **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  public operation: string;
  private currencyPattern: string;
  private systemDate: string;
  private dateFormat: string;

  private emailTemplateMaintenanceGrid: SwtCommonGrid;
  // Variable to hold _attributeId                                                          
  private templateId: string = null;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Account Currency Period maintenance', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }



  showText() {
    //  console.log("text = ", this.bodyContentTxtInput.text);
  }
  showhtmlText() {
    ///console.log("htmlText = ", this.bodyContentTxtInput.htmlText);
  }

  ngOnInit() {




    instanceElement = this;
    this.descriptionLabel.text = SwtUtil.getPredictMessage('emailTemplateMaintenance.description', null);
    this.descriptionTxtInput.toolTip = SwtUtil.getPredictMessage('emailTemplateMaintenance.tooltip.description', null);
    this.subjectContentLabel.text = SwtUtil.getPredictMessage('emailTemplateMaintenance.subjectContent', null);
    //this.subjectContentTxtInput.toolTip = SwtUtil.getPredictMessage('emailTemplateMaintenance.tooltip.subjectContent', null);
    this.bodyContentLabel.text = SwtUtil.getPredictMessage('emailTemplateMaintenance.bodyContent', null);
    //this.bodyContentTxtInput.toolTip = SwtUtil.getPredictMessage('emailTemplateMaintenance.tooltip.bodyContent', null);
    this.templateIdLabel.text = SwtUtil.getPredictMessage('emailTemplateMaintenance.templateId', null);
    this.templateIdTxtInput.toolTip = SwtUtil.getPredictMessage('emailTemplateMaintenance.tooltip.templateId', null);
    this.keywordsComboLabel.text = SwtUtil.getPredictMessage('emailTemplateMaintenance.keywordsCombo', null);

    this.keywordsCombo.toolTip = SwtUtil.getPredictMessage('emailTemplateMaintenance.tooltip.keywordsCombo', null);


    this.templateIdTxtInput.required = true;
    this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    this.saveButton.toolTip = SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.save', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.cancel', null);
  }
  
  initTinyMCE() {
    console.log("🚀 ~ EmailTemplateMaintenanceAdd ~ initTinyMCE ~ initTinyMCE:")
    try {
      
    tinymce.init({
      selector: '#emailContent',
      skin: false,
      content_css: './assets/css/tinymce/SwtTextArea.css',
      body_class: "swtTextArea",
      plugins: ['advlist', 'lists'],
      menubar: false,
      branding: false,
      width: "100%",
      height: "100%",
      toolbar: ["fontselect | fontsizeselect | bold italic underline | forecolor | alignleft aligncenter alignright alignjustify | numlist"],
      setup: (editor) => {
        let savedRange = null; // V
        editor.on('focus', () => {
          this.lastFocusedElement = 'content';
        });
        editor.on('click', function (e) {
               // Before clicking on the keyword-remove element, save the caret position (selection range)
          savedRange = editor.selection.getRng(); // Get the current selection range

          if (e.target.classList.contains('keyword-remove')) {
            e.target.parentNode.remove();
            // After removing the element, restore the caret position
            if (savedRange) {
              editor.selection.setRng(savedRange); // Restore the selection range
            }
          }
        });
      },
      content_style: `
      .keyword {
          display: inline-block;
          background-color: #e1f5fe;
          border: 1px solid #81d4fa;
          border-radius: 4px;
          padding: 2px 5px;
          margin: 0 2px;
          font-size: 0.8em;
          cursor: pointer;
      }
      .keyword:hover {
          background-color: #b3e5fc;
      }
      .keyword-remove {
          color: #e74c3c;
          margin-left: 5px;
          font-weight: bold;
      }
      .swtTextArea {
          line-height: 2.2;
      }

      .mce-offscreen-selection {
    display: none !important;
  }
    
  `

    });

    // Initialize TinyMCE for subject
    tinymce.init({
      selector: '#subjectContent',
      skin: false,
      content_css: './assets/css/tinymce/SwtTextArea.css',
      body_class: "swtTextArea",
      plugins: [],
      menubar: false,
      branding: false,
      width: "100%",
      height: "23",
      toolbar: false,
      statusbar: false,
      setup: (editor) => {
        let savedRange = null; // V
        editor.on('focus', () => {
          this.lastFocusedElement = 'subject';
        });
        editor.on('click', function (e) {
          // Before clicking on the keyword-remove element, save the caret position (selection range)
     savedRange = editor.selection.getRng(); // Get the current selection range

      if (e.target.classList.contains('keyword-remove')) {
        e.target.parentNode.remove();
        // After removing the element, restore the caret position
        if (savedRange) {
          editor.selection.setRng(savedRange); // Restore the selection range
        }
      }
    });
      },
      content_style: `
     .swtTextArea {
        height: 100%;
        overflow: hidden; /* Prevent scrolling */
        white-space: nowrap; /* Keep text in one line */
    }
      .keyword {
          display: inline-block;
          background-color: #e1f5fe;
          border: 1px solid #81d4fa;
          border-radius: 4px;
          padding: 2px 5px;
          margin: 0 2px;
          font-size: 0.8em;
          cursor: pointer;
      }
      .keyword:hover {
          background-color: #b3e5fc;
      }
      .keyword-remove {
          color: #e74c3c;
          margin-left: 5px;
          font-weight: bold;
      }

      .mce-offscreen-selection {
    display: none !important;
  }

  `

    });

    
  } catch (error) {
      console.log(error);
  }
  }
  ngAfterViewInit(): void {
  console.log("🚀 ~ EmailTemplateMaintenanceAdd ~ ngAfterViewInit ~ ngAfterViewInit:")

    this.initTinyMCE();

    $('.close').on('click', function() {
        $(this).closest('.modal').hide();
    });

    $(window).on('click', function(event) {
        if ($(event.target).hasClass('modal')) {
            $('.modal').hide();
        }
    });


  }


  focusChange() {
    console.log('focus Change')
  }
  onLoad() {
    console.log("🚀 ~ EmailTemplateMaintenanceAdd ~ onLoad ~ onLoad:")
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
    /*  this.screenName = "change"//ExternalInterface.call('eval', 'screenName');
      this.parameters = [
        {
          "templateId": "aze",
          "description": "xxx",
          "subjectContent": "gg",
          "bodyContent": "zzz"
        }
      ];//ExternalInterface.call('eval', 'params') ? JSON.parse(ExternalInterface.call('eval', 'params')) : "";
      this.menuAccessId = "0"//ExternalInterface.call('eval', 'menuAccessId');
      */
      
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      this.screenName = ExternalInterface.call('eval', 'screenName');
      this.parameters = ExternalInterface.call('eval', 'params') ? JSON.parse(ExternalInterface.call('eval', 'params')) : "";


      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 10;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      errorLocation = 20;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "emailTemplateMaintenance.do?";
      this.actionMethod = 'method=displayEmailTemplate';
      this.requestParams['screenName'] = this.screenName;
      this.requestParams['menuAccessId'] = this.menuAccessId;
      this.requestParams['templateId'] = this.screenName == 'change' || this.screenName == 'view' ? this.parameters[0].templateId : "";
      console.log("🚀 ~ EmailTemplateMaintenanceAdd ~ onLoad ~ this.requestParams:", this.requestParams)
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 30;
      this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'EmailTemplateMaintenanceAdd.ts', "onLoad", errorLocation);

      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintAdd.ts', "onLoad", errorLocation);
    }

  }
  changeKeywords() {
    console.log("tjis", this.keywordsCombo.selectedLabel)
    if (this.keywordsCombo.selectedLabel) {
      this.insertField(this.keywordsCombo.selectedLabel);
      this.keywordsCombo.selectedIndex = -1;
    }
  }

  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        errorLocation = 10;
        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {

            this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
            errorLocation = 20;
            this.dateFormat = this.jsonReader.getSingletons().dateformat;
            this.systemDate = this.jsonReader.getSingletons().systemDate;

            this.keywordsCombo.setComboData(this.jsonReader.getSelects(), false);
            //  this.keywordsCombo.text = this.keywordsCombo.selectedItem.value;


            errorLocation = 30;

            this.templateIdTxtInput.text = this.jsonReader.getSingletons().templateId.content;
            this.descriptionTxtInput.text = this.jsonReader.getSingletons().description.content;
            this.setTinyMCEContentWithPlaceholders(Encryptor.decode64(this.jsonReader.getSingletons().subjectContent.content), true);
            this.setTinyMCEContentWithPlaceholders(Encryptor.decode64(this.jsonReader.getSingletons().bodyContent.content), false);
            // this.bodyContentTxtInput.htmlText = Encryptor.decode64(this.jsonReader.getSingletons()..content);
            errorLocation = 50;
            console.log("🚀 ~ EmailTemplateMaintenanceAdd ~ inputDataResult ~ this.screenName:", this.screenName)
            if (this.screenName == 'change' || this.screenName == 'view') {
              errorLocation = 100;
              this.disableComponents();
            }

            if (!this.jsonReader.isDataBuilding()) {
              this.prevRecievedJSON = this.lastRecievedJSON;
            }
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }

        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'EmailTemplateMaintenanceAdd.ts', "inputDataResult", errorLocation);
    }
  }

  disableComponents() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
        this.templateIdTxtInput.enabled = false;
        this.descriptionTxtInput.enabled = false;
        if("view"==this.screenName){
          tinymce.get('subjectContent').setMode('readonly');
          tinymce.get('emailContent').setMode('readonly');
          this.saveButton.enabled = false;
        }
        //     this.subjectContentTxtInput.enabled = false;
        //this.bodyContentTxtInput.enabled = false;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [populateValues] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintAdd.ts', "populateValues", errorLocation);
    }
  }

  refreshComboList() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.setComboLists(event);
      };
      errorLocation = 10;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      errorLocation = 20;

      this.actionPath = "emailTemplateMaintenance.do?";
      this.actionMethod = 'method=getLists';
      errorLocation = 30;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      errorLocation = 40;

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [refreshComboList] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, '', "refreshComboList", errorLocation);
    }
  }


  setComboLists(event) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        errorLocation = 10;
        this.inputData.cbStop();
      } else {
        errorLocation = 20;
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        errorLocation = 30;
        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            errorLocation = 40;
            if (!this.jsonReader.isDataBuilding()) {
              this.prevRecievedJSON = this.lastRecievedJSON;
            }
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [setComboLists] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintAdd.ts', "setComboLists", errorLocation);
    }
  }
  saveHandler() {
    this.showhtmlText();
    this.showText();
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      errorLocation = 10;
      let action = this.screenName == 'change' ? 'update' : 'save';
      errorLocation = 40;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.saveResult(event);
      };
      errorLocation = 50;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "emailTemplateMaintenance.do?";
      this.actionMethod = 'method=saveEmailTemplate';
      errorLocation = 60;
      this.requestParams['description'] = this.descriptionTxtInput.text;
        this.requestParams['subjectContent'] = Encryptor.encode64(this.getTinyMCEContentWithReplacements(true));
       this.requestParams['bodyContent'] = Encryptor.encode64(this.getTinyMCEContentWithReplacements(false));
       
      this.requestParams['templateId'] = this.templateIdTxtInput.text;
      errorLocation = 130;
      this.requestParams['screenName'] = action;
      errorLocation = 140;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 150;
      this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [saveHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, '.ts', "saveHandler", errorLocation);
    }
  }


  saveResult(event) {
    let message = SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninAdd', null);
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyMessage() && this.jsonReader.getRequestReplyMessage().indexOf("RECORD_EXIST") != -1) {
        this.swtAlert.error(message, null, Alert.OK, null, () => {
          // ExternalInterface.call('close');
        });
      } else if (!this.jsonReader.getRequestReplyStatus()) {
        this.inputDataFault(event);
      } else {
        //refresh parent
        window.opener.instanceElement.updateData(true);
        ExternalInterface.call("close");
      }
    }

  }

  cancelHandler() {
    ExternalInterface.call('close')
  }

  popupClosed(): void {
    window.close();
  }

  closePopup(popupName):void{
    if(popupName=="previewDialog"){
      $('#previewDialog').hide();
    }else{
      $('#previewModal').hide();
    }
    
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


  /**                                                                                                                  
   * If a fault occurs with the connection with the server then display the lost connection label                      
   * @param event:FaultEvent                                                                                           
   **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  private lastFocusedElement = null;

  insertField(field: string) {
    console.log("🚀 ~ EmailTemplateMaintenanceAdd ~ insertField ~ field:", field);
    const dynamicField = `<span class="keyword" contenteditable="false">P\${${field}}<span class="keyword-remove">×</span></span>`;

    if (this.lastFocusedElement === 'subject') {
        tinymce.get('subjectContent').insertContent(dynamicField);
        tinymce.get('subjectContent').focus();
    } else {
        tinymce.get('emailContent').insertContent(dynamicField);
        tinymce.get('emailContent').focus();
    }

    // Prevent clicks on the non-editable span from triggering new insertions
}

getTinyMCEContentWithReplacements(isSubject) {
    if (isSubject) {
        const subjectContent = tinymce.get('subjectContent').getContent({ format: 'text' });;
        console.log("🚀 ~ EmailTemplateMaintenanceAdd ~ getTinyMCEContentWithReplacements ~ subjectContent:", subjectContent)
        const subjectText = this.replaceSpansWithTemplates(subjectContent);
        console.log("🚀 ~ EmailTemplateMaintenanceAdd ~ getTinyMCEContentWithReplacements ~ subjectText:", subjectText)
        return subjectText;
    } else {
        const emailContent = tinymce.get('emailContent').getContent();
        const emailText = this.replaceSpansWithTemplates(emailContent);
        console.log("🚀 ~ EmailTemplateMaintenanceAdd ~ getTinyMCEContentWithReplacements ~ emailText:", emailText)
        return emailText;
    }
}

replaceSpansWithTemplates(content: string): string {
  return content.replace(/<span class="keyword" contenteditable="false">(.*?)<span class="keyword-remove">&times;<\/span><\/span>/g, '$1').replace(/×/g, '');
}


replaceTemplatesWithSpans(content: string): string {
    return content.replace(/P\$\{(.*?)\}/g, '<span class="keyword" contenteditable="false">P\${$1}<span class="keyword-remove">×</span></span>');
}

// Helper method to replace the dynamic spans
replaceDynamicFields(content: string): string {
    return content.replace(/<span class="keyword" contenteditable="false">P\$\{(.*?)\}<span class="keyword-remove">×<\/span><\/span>/g, (match, p1) => `P\${${p1}}`);
}

// Method to set content in TinyMCE and replace placeholders with spans
setTinyMCEContentWithPlaceholders(content: string, isSubject) {
    const modifiedContent = this.replaceTemplatesWithSpans(content);

    if (isSubject) {
        tinymce.get('subjectContent').setContent(modifiedContent);
        tinymce.get('subjectContent').focus();
    } else {
        tinymce.get('emailContent').setContent(modifiedContent);
        tinymce.get('emailContent').focus();
    }
}

// Helper method to replace placeholders with spans
replacePlaceholdersWithSpans(content: string): string {
    return content.replace(/P\$\{(\w+)\}/g, (match, p1) => {
        return `<span class="keyword" contenteditable="false">P\${${p1}}<span class="keyword-remove">×</span></span>`;
    });
}

showPreviewDialog() {
    const subjectContent = tinymce.get('subjectContent').getContent({ format: 'text' });;
    const bodyContent = tinymce.get('emailContent').getContent();
    const allContent = subjectContent + ' ' + bodyContent;
    const fields = allContent.match(/P\$\{([^}]+)\}/g) || [];
    
    const uniqueFields = Array.from(new Set(fields.map(f => f.slice(3, -1))));

    let inputs = '';
    uniqueFields.forEach(field => {
        inputs += `<div class="input-group-2">
                     <label>${field} </label>
                     <input class="input-content" type="text" id="${field}">
                   </div>`;
    });

    $('#fieldInputs').html(inputs);
    $('#previewDialog').show();
}

 escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Escapes special characters
}
generatePreview() {
    try {
        let subjectContent = tinymce.get('subjectContent').getContent({ format: 'text' });;
        let bodyContent = tinymce.get('emailContent').getContent();

        const fields = this.replaceSpansWithTemplates(subjectContent + ' ' + bodyContent).match(/P\$\{([^}]+)\}/g) || [];
        
        const uniqueFields = Array.from(new Set(fields.map(f => f.slice(3, -1))));
        
        uniqueFields.forEach(field => {
          const value = $(`#${this.escapeRegExp(field)}`).val();
          const regex = new RegExp(`P\\$\\{${this.escapeRegExp(field)}\\}`, 'g');
          subjectContent = subjectContent.replace(regex, value);
          bodyContent = bodyContent.replace(regex, value);
      });
        
        $('#previewSubject').html(subjectContent);
        $('#previewContent').html(bodyContent);
        $('#previewDialog').hide();
        $('#previewModal').show();
    } catch (error) {
        console.error(error);
    }
}

}


//Define lazy loading routes
const routes: Routes = [

  { path: '', component: EmailTemplateMaintenanceAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [EmailTemplateMaintenanceAdd], entryComponents: []
})
export class EmailTemplateMaintenanceAddModule { } 
