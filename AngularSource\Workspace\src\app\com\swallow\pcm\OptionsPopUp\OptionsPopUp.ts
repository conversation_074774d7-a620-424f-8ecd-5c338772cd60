import {CommonService, SwtButton, SwtModule, SwtTextInput} from "swt-tool-box";
import {Component, ElementRef, ViewChild} from "@angular/core";

@Component({
    selector: 'app-options-pop-up',
    templateUrl: './OptionsPopUp.html',
    styleUrls: ['./OptionsPopUp.css']
})

export class OptionsPopUp extends SwtModule {

    @ViewChild("okButton")  okButton: SwtButton;
    @ViewChild("cancelButton")  cancelButton: SwtButton;
    @ViewChild("refresh")  refresh: SwtTextInput;

    public refreshText: string;

    constructor(private element: ElementRef, private commonService: CommonService) {
        super(element, commonService);
    }

  onLoad() {
    this.refresh.text=this.refreshText +"";
  }

  closePopup() {
      try {
        if(this.titleWindow) {
          this.close();
        } else {
          window.close();
        }
      } catch (error) {
        console.log(error, "OptionPopUp", "closePopup");
      }
    }

    okHandler() {
        try {
          this.parentDocument.saveRefreshRate(this.refresh.text);
          this.closePopup();
        } catch(error) {
            console.error('error :',error);
        }
    }
}
