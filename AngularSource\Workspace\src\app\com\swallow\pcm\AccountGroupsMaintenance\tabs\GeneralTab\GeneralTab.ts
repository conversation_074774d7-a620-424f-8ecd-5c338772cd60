import {Component, ElementRef, ViewChild, OnInit} from '@angular/core';
import {
  HTT<PERSON>om<PERSON>,
  SwtButton,
  J<PERSON><PERSON>ead<PERSON>,
  SwtAlert,
  SwtUtil,
  SwtTextInput,
  CommonService,
  SwtModule,
  SwtComboBox,
  Alert,
  SwtCanvas,
  SwtCommonGrid,
  VBox,
  SwtLabel,
} from 'swt-tool-box';
import {SpreadingTab} from "../SpreadingTab/SpreadingTab";
@Component({
  selector: 'PCGeneralTab',
  templateUrl: './GeneralTab.html',
  styleUrls: ['./GeneralTab.css']
})
export class GeneralTab extends SwtModule implements OnInit{

  @ViewChild('leftCanvas') leftCanvas: SwtCanvas;
  @ViewChild('rightCanvas') rightCanvas: SwtCanvas;
  /**********SwtTextInput*************/
  @ViewChild('ordinalNumericInput') ordinalNumericInput: SwtTextInput;
  @ViewChild('filterTextLeft') filterTextLeft: SwtTextInput;
  @ViewChild('filterTextRight') filterTextRight: SwtTextInput;
  /*********SWtButton*************/
  @ViewChild('buttonMoveAllRight') buttonMoveAllRight: SwtButton;
  @ViewChild('buttonMoveRight') buttonMoveRight: SwtButton;
  @ViewChild('buttonMoveLeft') buttonMoveLeft: SwtButton;
  @ViewChild('buttonMoveAllLeft') buttonMoveAllLeft: SwtButton;
  /***********Swtlabel**************/
  //@ViewChild('ccyLabel') ccyLabel: SwtLabel;
  @ViewChild('countIDInGroup') countIDInGroup: SwtLabel;
  @ViewChild('countIDNotInGroup') countIDNotInGroup: SwtLabel;
  @ViewChild('processingOrderLabel') processingOrderLabel: SwtLabel;
  @ViewChild('quickCategoryLabel') quickCategoryLabel: SwtLabel;
  @ViewChild('defaultCategoryLabel') defaultCategoryLabel: SwtLabel;
  @ViewChild('accountsNotInGroup') accountsNotInGroup: SwtLabel;
  @ViewChild('quickSearchLabel') quickSearchLabel: SwtLabel;
  @ViewChild('quickSearchLabel2') quickSearchLabel2: SwtLabel;
  @ViewChild('accountsInGRoupLabel') accountsInGRoupLabel: SwtLabel;

  /***********SwtCombo***************/
  //@ViewChild('currencyComboBox') currencyComboBox: SwtComboBox;
  @ViewChild('defaultCategoryCombo') defaultCategoryCombo: SwtComboBox;
  @ViewChild('quickCategoryCombo') quickCategoryCombo: SwtComboBox;
  /**********VBox*************/
  @ViewChild('buttonsContainer') buttonsContainer: VBox;
  @ViewChild('spreadingTab') spreadingTab: SpreadingTab;
  /**
   * Data Objects
   **/
  public jsonReader= new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  private  inputData = new HTTPComms(this.commonService);
  private baseURL =  SwtUtil.getBaseURL();
  private  actionMethod= "";
  private  actionPath= "";
  private  requestParams = [];
  public screenName:string;

  public  title:string= null;
  public rightGrid: SwtCommonGrid;
  public leftGrid: SwtCommonGrid;
  private swtAlert: SwtAlert;

  private lastOrdinal: string;

  public parentDocument: any;
  public leftTextOfInput: string = "";
  public rightTextOfInput:string = "";

  private firstLoad: boolean = true;
  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  ngOnInit(): void {
    /***Tooltip****/
    this.processingOrderLabel.text = SwtUtil.getPredictMessage('acctGroupsMaintenanceDetails.label.processingOrder', null);
    this.ordinalNumericInput.toolTip = SwtUtil.getPredictMessage('acctGroupsMaintenanceDetails.tooltip.processingOrder', null);
    this.quickCategoryLabel.text = SwtUtil.getPredictMessage('generalTab.label.quickCategory', null);
    this.quickCategoryCombo.toolTip= SwtUtil.getPredictMessage('generalTab.tooltip.quickCategory', null);
    this.defaultCategoryLabel.text = SwtUtil.getPredictMessage('generalTab.label.defaultCategory', null);
    this.defaultCategoryCombo.toolTip = SwtUtil.getPredictMessage('generalTab.tooltip.defaultCategory', null);
    this.accountsNotInGroup.text = SwtUtil. getPredictMessage('generalTab.label.countIDNotInGroup', null);
    this.accountsNotInGroup.toolTip = SwtUtil.getPredictMessage('generalTab.tooltip.countIDNotInGroup', null);
    this.quickSearchLabel.text= SwtUtil.getPredictMessage('generalTab.label.filterText', null);
    this.quickSearchLabel2.text= SwtUtil.getPredictMessage('generalTab.label.filterText', null);
    this.filterTextLeft.toolTip = SwtUtil.getPredictMessage('generalTab.tooltip.filterText', null);
    this.filterTextRight.toolTip = SwtUtil.getPredictMessage('generalTab.tooltip.filterText', null);
    this.buttonMoveRight.toolTip = SwtUtil.getPredictMessage('generalTab.toolTip.buttonMoveRight', null);
    this.buttonMoveAllRight.toolTip = SwtUtil.getPredictMessage('generalTab.toolTip.buttonMoveAllRight', null);
    this.buttonMoveLeft.toolTip = SwtUtil. getPredictMessage('generalTab.toolTip.buttonMoveLeft', null);
    this.buttonMoveAllLeft.toolTip = SwtUtil.getPredictMessage('generalTab.toolTip.buttonMoveAllLeft', null);
    this.accountsInGRoupLabel.text = SwtUtil.getPredictMessage('generalTab.label.countIDInGroup', null);
    this.accountsInGRoupLabel.toolTip = SwtUtil.getPredictMessage('generalTab.tooltip.countIDInGroup', null);


    this.rightGrid.rowColorFunction = ( dataContext, dataIndex, color, dataField ) => {
      return this.drawRowBackground( dataContext, dataIndex, color , dataField);
    };

        this.leftGrid.rowColorFunction = ( dataContext, dataIndex, color, dataField ) => {
      return this.drawRowBackground( dataContext, dataIndex, color , dataField);
    };


    // this.leftGrid.customTooltipFunction = (dataContext,row,dataField) => {
    //   return this.setTooltipMessage(dataContext,row,dataField);
    // };


  }


  drawRowBackground( dataContext, dataIndex, color,dataField ): string {

    let rColor: string;
    try {
      let colorFlag: string;
    // if(dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.entity){
      if('Y'==dataContext.slickgrid_rowcontent[dataField].isDeletedRow){
        rColor = "#ff808a";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isNewRow){
        rColor = "#c6efce";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isUpdatedRow){
        rColor = "#ee82ee";
  }
      // else {
      //   rColor = "#ee82ee";
      // }
      
    // }
      
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }

  private setTooltipMessage(dataContext,row,dataField): any {
    // Variable to hold error location
    var errorLocation: number = 0;
    var message= "";
    // Color
    var displayedMsg ="";
    try {
      errorLocation = 10;
      // dataContext.slickgrid_rowcontent[dataField].content
      if (dataContext.slickgrid_rowcontent[dataField].content == 'PNBPUS30NYC'){
        displayedMsg ="modiaaafied"
      }
      // message=dataContext.slickgrid_rowcontent.TooltipMsg? dataContext.slickgrid_rowcontent.TooltipMsg[0]: "";
      // if (message) {
      //   message = message.split(" ").join("$#$");
      //   message = message.split(",").join("&@&");
      //   message = message.split(";").join("&_&");
      //   errorLocation = 20;
      //   displayedMsg = message; //0xDCDCDC not clear 

      // }
      // else {
      //   displayedMsg = "";
      // }
    }
    catch (error) {
      // log the error in ERROR LOG
      // SwtUtil.logError(error, this.moduleId, this.getQualifiedClassName(this), "setTooltipMessage", errorLocation);
    }
    return displayedMsg;
  }



  onLoad() {
    try{
      if(this.parentDocument) {
        this.screenName = this.parentDocument.screenName;


        this.rightGrid.onRowClick = () => {
          this.cellClickEventHandler();
        };
        this.leftGrid.onRowClick = () => {
          this.cellClickEventHandler();
        };

      }
      /*this.rightGrid.parentTabId = "general";
      this.leftGrid.parentTabId = "general";*/





    } catch(error){
      console.log('errro onload', error)
    }
  }




  enableMoveButtons():void
  {
    if (this.leftGrid.dataProvider.length > 0){
      this.buttonMoveRight.enabled= this.leftGrid.selectedIndices.length > 0;
      this.buttonMoveAllRight.enabled = this.screenName != "view";
      this.buttonMoveLeft.enabled = false;
      this.buttonMoveAllLeft.enabled = true;
    } else{
      this.buttonMoveAllRight.enabled = false;
      this.buttonMoveRight.enabled = false;
    }
    if (this.rightGrid.dataProvider.length > 0){
      this.buttonMoveLeft.enabled= this.rightGrid.selectedIndices.length > 0;
      this.buttonMoveAllLeft.enabled= this.screenName != "view";
    } else {
      this.buttonMoveLeft.enabled = false;
      this.buttonMoveAllLeft.enabled = false;
    }

  }
  cellClickEventHandler(): void {
    if (this.screenName != 'view') {
      this.enableMoveButtons();
    }
  }
  /**
   * This function allow user to move account from left grid to right grid
   * */
  moveToRight(event, moveAll: boolean): void {
    let rightList = [...this.rightGrid.dataProvider];
    let dpList = [...this.leftGrid.getFilteredItems()];
    if(this.firstLoad) {
      this.firstLoad = false;
    }
    let countMovedLeft: number;

    try
    {
      if (moveAll) {
        countMovedLeft = dpList.length;
        for (let i= 0; i< dpList.length; i++) {
          rightList.push(dpList[i]);
          this.leftGrid.angularGridInstance.gridService.deleteItem( dpList[i] );
        }
        for (let i =0 ; i < rightList.length; i++) {
          rightList[i].id = i;
        }
        this.rightGrid.gridData = {row: rightList, size: rightList.length};
        this.leftGrid.refresh();
        this.buttonMoveAllRight.enabled = false;
        this.buttonMoveRight.enabled = false;
        this.buttonMoveAllLeft.enabled = true;
      }
      else{
        let items =[];
        countMovedLeft =  this.leftGrid.selectedIndices.length;
        for (let i= 0; i < this.leftGrid.selectedIndices.length ; i++) {
           items.push(this.leftGrid.angularGridInstance.gridService.getDataItemByRowNumber( this.leftGrid.selectedIndices[i] ));
           rightList.push(this.leftGrid.selectedItems[i]);
        }
        for (let i =0 ; i < rightList.length; i++) {
          rightList[i].id = i;
        }
        this.leftGrid.angularGridInstance.gridService.deleteItems( items );
        this.leftGrid.refresh();
        this.rightGrid.gridData = {row: rightList, size: rightList.length};

      }
        let countRight = Number(this.countIDInGroup.text.replace('(', '').replace(')', ''));
        this.countIDInGroup.text = '('+ (countRight +countMovedLeft) +')';
        let countLeft = Number(this.countIDNotInGroup.text.replace('(', '').replace(')', ''));
        this.countIDNotInGroup.text = '('+ ( countLeft - countMovedLeft)  +')';



      this.leftGrid.selectedIndex=-1;
    } catch (error)
    {
      console.log('erorrrr--------', error)
    }
  }
  checkAccountId(accountsArray): void {
    this.requestParams = [];
    this.actionMethod = 'method=checkAccountInPayment';
    this.actionPath = 'accountGroupsPCM.do?';
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.requestParams['accountsId'] = accountsArray.toString();
    this.requestParams['method'] = 'checkAccountInPayment';
    this.inputData.encodeURL = false;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }
  /**
   * This function allow user to move account from left grid to right grid
   * */
  moveToLeft(event, moveAll: boolean): void {
    let rightList : any = [...this.rightGrid.getFilteredItems()];
    let leftList = this.leftGrid.dataProvider;
    let countMovedRight: number;
    let accountsArray = [];
    try {
      if (moveAll) {
        if(this.screenName == 'change') {
          for (let i= 0; i < rightList.length ; i++) {
            accountsArray.push(rightList[i].account_id_name.split(' -')[0] + ";"+ rightList[i].entity);
            this.rightGrid.selectedIndices[i] = i;
            this.rightGrid.selectedItems[i] = this.rightGrid.getFilteredItems()[i];
          }
          this.checkAccountId(accountsArray);
        } else {
        countMovedRight = rightList.length;
        for (let i= 0; i< rightList.length; i++)
        {
          leftList.push(rightList[i]);
          this.rightGrid.angularGridInstance.gridService.deleteItem( rightList[i] );

        }
        for (let i =0 ; i < leftList.length; i++) {
          leftList[i].id = i
        }
        this.leftGrid.gridData = {row: leftList, size: leftList.length};
        this.buttonMoveAllLeft.enabled = false;
        this.buttonMoveLeft.enabled = false;
        this.buttonMoveAllRight.enabled = true;

        let countRight = Number(this.countIDInGroup.text.replace('(', '').replace(')', ''));
        this.countIDInGroup.text = '('+ (countRight -countMovedRight) +')';
        let countLeft = Number(this.countIDNotInGroup.text.replace('(', '').replace(')', ''));
        this.countIDNotInGroup.text = '('+ ( countLeft + countMovedRight)  +')';
        this.rightGrid.selectedIndex=-1;

        }
      }
      else{
        let items =[];

        countMovedRight =  this.rightGrid.selectedIndices.length;
        if(this.screenName == 'change') {
          for (let i= 0; i < this.rightGrid.selectedIndices.length ; i++) {
            accountsArray.push(this.rightGrid.selectedItems[i].account_id_name.content.split(' -')[0] + ";"+ rightList[i].entity);
          }
          this.checkAccountId(accountsArray);

        } else {
        for (let i= 0; i < this.rightGrid.selectedIndices.length ; i++) {
          items.push(this.rightGrid.angularGridInstance.gridService.getDataItemByRowNumber( this.rightGrid.selectedIndices[i] ));
          leftList.push(this.rightGrid.selectedItems[i]);
        }
        this.rightGrid.angularGridInstance.gridService.deleteItems( items );
        this.rightGrid.refresh();
        for (let i =0 ; i < leftList.length; i++) {
          leftList[i].id = i;
        }

          this.leftGrid.gridData = {row: leftList, size: leftList.length};
          let countRight = Number(this.countIDInGroup.text.replace('(', '').replace(')', ''));
          this.countIDInGroup.text = '('+ (countRight -countMovedRight) +')';
          let countLeft = Number(this.countIDNotInGroup.text.replace('(', '').replace(')', ''));
          this.countIDNotInGroup.text = '('+ ( countLeft + countMovedRight)  +')';
          this.rightGrid.selectedIndex=-1;
        }

      }




    } catch (error)
    {
      console.log('error in Move left', error);
    }
  }

  inputDataResult(event) : void {
    let leftList = this.leftGrid.dataProvider;
    let countMovedRight = 0;
    let items =[];
    let arrayOfIdExist = [];
    let accountIdArray = [];
    let existArray = [];
    let exist: boolean = false;
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        /* Get result as xml */
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        let jsonSing: string = this.jsonReader.getSingletons().isAccountInPayReq;
        arrayOfIdExist.push(jsonSing.split(';'));
        arrayOfIdExist = arrayOfIdExist[0];
        for (let i = 0; i < arrayOfIdExist.length; i++) {
          existArray.push(arrayOfIdExist[i].split('=')[1]);
          if(existArray[i] == "Y") {
            exist = true;
            accountIdArray.push(arrayOfIdExist[i].split('=')[0]);
          }
        }
        for (let i= 0; i < this.rightGrid.selectedIndices.length ; i++) {
          if(existArray[i] == "N") {
            countMovedRight ++;
          items.push(this.rightGrid.angularGridInstance.gridService.getDataItemByRowNumber( this.rightGrid.selectedIndices[i] ));
          leftList.push(this.rightGrid.selectedItems[i]);
        }
        }
        this.rightGrid.angularGridInstance.gridService.deleteItems( items );
        this.rightGrid.refresh();
        for (let i =0 ; i < leftList.length; i++) {
          leftList[i].id = i;
        }
        this.leftGrid.gridData = {row: leftList, size: leftList.length};
        if(exist) {
          if(accountIdArray.length == 1)
            this.swtAlert.error("The following account ("+ accountIdArray.toString()+") cannot be moved because of waiting or stopped payment requests." );
          else
            this.swtAlert.error("The following accounts ("+ accountIdArray.toString()+") cannot be moved because of waiting or stopped payment requests." );

        }
        let countRight = Number(this.countIDInGroup.text.replace('(', '').replace(')', ''));
        this.countIDInGroup.text = '('+ (countRight -countMovedRight) +')';
        let countLeft = Number(this.countIDNotInGroup.text.replace('(', '').replace(')', ''));
        this.countIDNotInGroup.text = '('+ ( countLeft + countMovedRight)  +')';
        this.rightGrid.selectedIndex=-1;
      }
    } catch(e) {

    }

  }

  filtringLeftGrid( search: string ): void {

    try {
      this.leftTextOfInput = search;
      this.updateFilter();

    }
    catch ( error ) {
      console.log('error', error);
    }
  }
  updateFilter() {
    this.leftGrid.dataviewObj.beginUpdate();
    this.leftGrid.dataviewObj.setItems(this.leftGrid.gridData);
    this.leftGrid.dataviewObj.setFilterArgs({
      searchString: this.leftTextOfInput,
    });
    this.leftGrid.dataviewObj.setFilter(this.filterFunction);
    this.leftGrid.dataviewObj.endUpdate();
    this.leftGrid.dataviewObj.refresh();
    this.countIDNotInGroup.text = '('+ this.leftGrid.getFilteredItems().length  +')';
  }

  updateFilterRight() {

    this.rightGrid.dataviewObj.beginUpdate();
    this.rightGrid.dataviewObj.setItems(this.rightGrid.gridData);
    this.rightGrid.dataviewObj.setFilterArgs({
      searchString: this.rightTextOfInput,
    });
    this.rightGrid.dataviewObj.setFilter(this.filterFunction);
    this.rightGrid.dataviewObj.endUpdate();
    this.rightGrid.dataviewObj.refresh();

    this.countIDInGroup.text = '('+this.rightGrid.getFilteredItems().length +')';
  }

  /**
   * filtringRightGrid()
   *
   * @param search:string
   *
   * Method used to filter the data from grid
   **/
  filtringRightGrid( search: string ): void {

    try {
      this.rightTextOfInput = search;
      this.updateFilterRight();
    }
    catch ( error ) {
      console.log('error', error);
    }
  }
  filterFunction(item, args) {
    try {
        return (item["account_id_name"].toLowerCase().indexOf(args.searchString.toLowerCase()) != -1 );
    } catch (e) {
      console.log('ee', e)
    }

  }

  /**
   * focusOutOrdinalInput
   * param event: KeyboardEvent
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  ordinalChangeHandler(): void {

    let ordianlArray = this.ordinalExist();
    let message = 'This order already exists with the selected currency';
    if(Number(this.ordinalNumericInput.text) <= 0) {
      this.swtAlert.warning('Order must be higher than 0');
    }
    if (ordianlArray.indexOf(Number(this.ordinalNumericInput.text)) !== -1 && this.screenName == 'add' && Number(this.ordinalNumericInput.text) != 0) {
      this.swtAlert.confirm(message, 'Alert', Alert.OK, null, this.reOrderGrid.bind(this));

    } else if (ordianlArray.indexOf(Number(this.ordinalNumericInput.text)) !== -1 && this.screenName == 'change' && this.parentDocument.lastOrdinal != this.ordinalNumericInput.text ) {
      this.swtAlert.confirm(message, 'Alert', Alert.OK, null, this.reOrderGrid.bind(this));

    }
  }

  reOrderGrid(event): void {
    if (event.detail == 4) {
      if (this.screenName == 'add') {
        this.ordinalNumericInput.text = this.parentDocument.ordinalFromJson;
      } else {
        this.ordinalNumericInput.text = this.parentDocument.lastOrdinal;
      }

    }
  }
  changeCombo(): void {

  }
  ordinalExist(): any{
    let arryOfOrdinal : any;
    arryOfOrdinal = this.parentDocument.listOfOrder;
    arryOfOrdinal = arryOfOrdinal.replace("[", "").replace("]", "");
    arryOfOrdinal = arryOfOrdinal.split(',').map(Number);

    return arryOfOrdinal;

  }
}
