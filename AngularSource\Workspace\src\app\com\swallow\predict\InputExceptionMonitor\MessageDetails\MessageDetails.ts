import { Component, ElementRef, ModuleWithProviders, NgModule, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {
  Alert,
  CommonService,
  ExternalInterface,
  focusManager,
  HashMap,
  HBox,
  HTTPComms,
  JSONReader,
  Keyboard,
  StringUtils,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCheckBox, SwtComboBox,
  SwtCommonGrid,
  SwtCommonGridPagination,
  SwtLabel,
  SwtLoadingImage,
  SwtModule,
  SwtTextArea,
  SwtTextInput,
  SwtToolBoxModule,
  SwtUtil,
  XML
} from 'swt-tool-box';
declare var instanceElement: any;
declare var require: any;
var parser = require('fast-xml-parser');
var prettyData = require('pretty-data');
@Component({
  selector: 'app-message-details',
  templateUrl: './MessageDetails.html',
  styleUrls: ['./MessageDetails.css']
})
export class MessageDetails extends SwtModule implements OnInit, OnDestroy {

  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /********SwtButton*********************/
  @ViewChild('reprocessButton') reprocessButton: SwtButton;
  @ViewChild('suppressButton') suppressButton: SwtButton;
  @ViewChild('rejectButton') rejectButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('csv') csv: SwtButton;
  @ViewChild('excel') excel: SwtButton;
  @ViewChild('pdf') pdf: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  /***********Swtlabel**************/
  @ViewChild('daysLabel') daysLabel: SwtLabel;
  @ViewChild('messageTypelbl') messageTypelbl: SwtLabel;
  @ViewChild('messageTypeValue') messageTypeValue: SwtLabel;
  @ViewChild('messageStatuslbl') messageStatuslbl: SwtLabel;
  @ViewChild('messageStatusValue') messageStatusValue: SwtLabel;
  @ViewChild('messageTypeCombo') messageTypeCombo: SwtComboBox;
  /***********TextInput**************/
  @ViewChild('showDays') showDays: SwtTextInput;

  @ViewChild('messageForm') messageForm: SwtTextArea;
  @ViewChild('autoFormatXMLCombo') autoFormatXMLCombo: SwtCheckBox;
  @ViewChild('autoFormatXMLContainer') autoFormatXMLContainer: HBox;
  @ViewChild('pageBox') pageBox: HBox;
  @ViewChild('pagination') pagination: SwtCommonGridPagination;

  private cGrid:SwtCommonGrid;
  private params: Object = new Array();
  private d: any;
  private currentPage: number = 1;
  private maxPage: number = 1;
  private totalAvailableMessages: number;
  private extra: number = 0;
  private selectedMessageSeqId: string = "";
  private seqIdArray = [];
  private messageType: string;
  private descending: string = "false";
  private prevSortingColumn: string;
  private sortingColumn: string = null;
  private startDate: string;
  private endDate: string;
  private messagesPerPage: string;
  private status: string;
			
  private gridData: any;
  private prevRawData: any;
  private columnData: any;
  private prevColumnSort = [];
  private isFiltered: Boolean = false;
  private filteredColumns = [];
  private filteredColumn: String = "";
  private filteredValue: String = "";
  private selectedFilteredColumns = [];
  private requested: Boolean = false;
  //private showXML: ShowXML;
  //Screenversion- Changed from 1.1.0018 to 1.1.0019 for Mantis 1355:Entity Monitor[Toolbox changes] by bala on 18-MAR-2011
  //Screenversion- Changed from 1.1.0019 to 1.1.0020 for Mantis 1416 by Marshal 06-Apr-2011
  /* Screenversion- Changed from 1.1.0020 to 1.1.0021 for Mantis 1462:Input Exceptions[Toolbox changes] or Input Exceptions export by Chidambaranathan on 20-May-2011--*/
  /* Screenversion- Changed from 1.1.0021 to 1.1.0022 for Mantis 1413:forecast monitor[Toolbox changes] by Bala on 27-July-2011--*/
  /* Screenversion- Changed from 1.1.0022 to 1.1.0023 for Mantis 1446:GUI changes in Predict for Smart Input v6  by krishna on 14-Jul-2011--*/
  private versionNumber: String = "1.0.0023";
  private screenName: String = "Input Exceptions Message Details - SMART Predict";//ExternalInterface.call('getBundle', 'text', 'label-inputExceptionMessage', 'Input Exceptions Message Details - SMART Predict');
  private sendReq: Boolean = true;

  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
			
  private systemDate:string;

  private swtAlert: SwtAlert;
  
  public rowsRPC = new HTTPComms(this.commonService);
  public messageRPC = new HTTPComms(this.commonService);
  public reprocessRPC = new HTTPComms(this.commonService);
  public suppressOrRejectRPC = new HTTPComms(this.commonService);
  public deleteRPC = new HTTPComms(this.commonService);
  public colWidth = new HTTPComms(this.commonService);

  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private invalidComms: string = "";
  public lastNumber: Number = 0;
  private currencyCode: string = null;
  public fromDashboard: string;
  private fromPCM: string;
  private auxMessage: string = "";
  private currencyPattern: string;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }



  ngOnInit(): void {
    instanceElement = this;
    this.cGrid = <SwtCommonGrid>this.canvasGrid.addChild(SwtCommonGrid);
    this.cGrid.allowMultipleSelection = true;
    this.cGrid.lockedColumnCount = 1;
    this.cGrid.uniqueColumn = "msgid";
    this.cGrid.clientSideSort = false;
    this.cGrid.clientSideFilter = false;
    // Assining properties for controls
    this.reprocessButton.label = SwtUtil.getPredictMessage("inputException.reprocess", null);
    this.reprocessButton.toolTip = SwtUtil.getPredictMessage("inputexceptions.tooltip.button_rep", null);
    this.suppressButton.label = SwtUtil.getPredictMessage("inputException.suppress", null);
    this.suppressButton.toolTip = SwtUtil.getPredictMessage("inputexceptions.tooltip.button_supp", null);
    this.rejectButton.label = SwtUtil.getPredictMessage("inputException.reject", null);
    this.rejectButton.toolTip = SwtUtil.getPredictMessage("inputexceptions.tooltip.button_rej", null);
    this.closeButton.label = SwtUtil.getPredictMessage("inputException.close", null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage("tooltip.close", null);
    this.autoFormatXMLCombo.label = SwtUtil.getPredictMessage("inputexceptions.label.autoFormatXML", null);
    this.autoFormatXMLCombo.toolTip = SwtUtil.getPredictMessage("inputexceptions.tooltip.autoFormatXML", null);
  }

  /**
	  * Upon completion of loading into the flash player this method is called
		**/
  onLoad() {
    this.hideShowAutoFormatXMLCombo(false );
    try {
      this.d=this.getUrlParams();
      this.fromPCM = ExternalInterface.call('eval', 'fromPCM');
      this.fromDashboard = ExternalInterface.call('eval', 'fromDashboard');
      if(this.fromDashboard == 'yes') {
        this.totalAvailableMessages=ExternalInterface.call('eval', 'm');
        this.messageTypeValue.visible = false;
        this.messageTypeCombo.visible = true;
      } else {
        this.totalAvailableMessages=parseInt(this.d["m"]);
        this.messageTypeValue.visible = true;
        this.messageTypeCombo.visible = false;
      }
      this.messageType=this.d["type"];
      this.currencyCode= (this.d["currencyCode"]) ? this.d["currencyCode"] : null;
      this.rowsRPC.url =this. rowsRPC.url + 'fromPCM=' + this.fromPCM + '&' + 'fromDashboard=' + this.fromDashboard + '&' + 'currencyCode=' + this.currencyCode + '&';
      this.messageRPC.url = this.messageRPC.url + 'fromPCM=' + this.fromPCM + '&' + 'currencyCode=' + this.currencyCode + '&';
      this.reprocessRPC.url = this.reprocessRPC.url + 'fromPCM=' + this.fromPCM + '&' + 'currencyCode=' + this.currencyCode + '&';
      this.suppressOrRejectRPC.url = this.suppressOrRejectRPC.url + 'fromPCM=' + this.fromPCM + '&' + 'currencyCode=' + this.currencyCode + '&';
      this.deleteRPC.url = this.deleteRPC.url + 'fromPCM=' + this.fromPCM + '&' + 'currencyCode=' + this.currencyCode + '&';
      this.colWidth.url = this.colWidth.url + 'fromPCM=' + this.fromPCM + '&' + 'currencyCode=' +this.currencyCode + '&';
      this.rejectButton.includeInLayout= false;
      this.rejectButton.visible=false;
      this.suppressButton.includeInLayout= false;
      this.suppressButton.visible=false;
      // rowsRPC
      this.actionPath = 'inputexceptionsmessages.do?';
      this.actionMethod = 'method=messagesData';
      this.rowsRPC.cbStart = this.startLoader.bind(this);
      this.rowsRPC.cbStop = this.stopLoader.bind(this);
      this.rowsRPC.cbResult = (data) => {
        this.rowsRPCResult(data);
      };
      this.rowsRPC.cbFault = this.inputDataFault.bind(this);
      this.rowsRPC.encodeURL = false;
      this.rowsRPC.url = this.baseURL + this.actionPath + this.actionMethod;

      var statusVerbose: string;
      switch (parseInt(this.d["status"])) {
        case 1:
          statusVerbose = "Awaiting";
          break;
        case 3:
          statusVerbose = "Accepted";
          break;
        case 4:
          statusVerbose = "Rejected";
          this.suppressButton.includeInLayout = true;
          this.suppressButton.visible = true;
          break;
        case 7:
          statusVerbose = "Submitted";
          break;
        case 9:
          statusVerbose = "Suppressed";
          this.rejectButton.includeInLayout = true;
          this.rejectButton.visible = true;
          break;
          case 10:
            if(this.fromPCM == 'yes') {
              statusVerbose = "Repair";
              this.rejectButton.includeInLayout = true;
              this.rejectButton.visible = true;
            }
          break;
      }
      this.startDate = this.d["fromDate"];
      this.endDate = this.d["toDate"];
      this.currentPage = this.d["p"];
      this.messagesPerPage = this.d["n"];
      this.status = this.d["status"];
      var totalPages: number = this.totalAvailableMessages / 50;
      this.extra = this.totalAvailableMessages % 50;
      if (this.extra > 0) {
        totalPages = Math.ceil(totalPages)
       // totalPages++;
      }

      this.maxPage = totalPages;
      if (totalPages > 1) {
        this.pageBox.visible = true;
        //Sets the Numeric stepper maximum value
        this.pagination.maximum = Number(this.maxPage);
        //Gets the current page from xml
        this.pagination.value = Number(this.currentPage);
        //TODO:pagination.addEventListener(KeyboardEvent.KEY_DOWN, keyDownPager);
      }
      else {
        this.pageBox.visible = false;
      }

      this.messageTypeValue.text = unescape(this.messageType);
      this.messageStatusValue.text = statusVerbose;

      this.reprocessButton.enabled = false;
      this.rejectButton.enabled = false;
      this.suppressButton.enabled = false;

      this.params = { fromDate: this.startDate, toDate: this.endDate, status: this.status, type: this.messageType, p: this.currentPage, n: this.messagesPerPage, m: this.totalAvailableMessages.toString(), currencyCode: this.currencyCode, fromPCM: this.fromPCM, fromDashboard: this.fromDashboard };

      if (this.sendReq) {
        this.rowsRPC.send(this.params);
      }

      //Add the event listener to listen for a cell click on a datagrid, be it the main one or a totals grid
      this.cGrid.onRowClick = (event) =>{
        this.obtainCell(event);
      }
      /*this.cGrid.ITEM_CLICK.subscribe( (event) =>{
        this.obtainCell(event);
      });*/
      this.cGrid.paginationComponent = this.pagination;
      this.cGrid.onPaginationChanged = this.paginationChanged.bind(this);
      this.cGrid.onSortChanged = this.globalSort.bind(this);
      //Make initial request
      this.requestParams["systemDate"] = this.systemDate;
      this.requestParams["autoRefresh"] = "no";
    } catch (error) {
        console.log("error", error);
    }
  }

  paginationChanged(event) {
    this.next();
  }

  /**
   * Method to handle the up/down key click in the numeric stepper for pagination
   * Page number will not incremented beyond maxPage and decremented less than 1
   **/
  next(): void {
    try {
    if (this.pagination.value > 0) {
      if (((this.pagination.value <= this.pagination.maximum) && (this.pagination.value != this.lastNumber) && (this.pagination.value != 0))) {
        this.hideShowAutoFormatXMLCombo(false);
        this.messageForm.text = '';
        this.currentPage = this.pagination.value;
        this.d = this.getUrlParams();
        this.params = { fromDate: this.startDate, toDate: this.endDate, status: this.status, type: this.messageType, p: this.currentPage, n: this.messagesPerPage, m: this.totalAvailableMessages, desc: this.descending, order: this.sortingColumn, currencyCode: this.currencyCode, fromPCM: this.fromPCM, fromDashboard: this.fromDashboard};
        this.requested = true;
        this.lastNumber = this.currentPage;
        if (this.sendReq)
          this.rowsRPC.send(this.params);
      }
    }
    } catch(e) {
      console.log('error', e)
    }
  }

  rowsRPCResult(data): void {

    // Get result as JSON
    try {
    this.lastRecievedJSON = data;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    if (this.jsonReader.getRequestReplyStatus()) {
      this.messageTypeCombo.setComboData(this.jsonReader.getSelects());
      this.currencyPattern = (this.jsonReader.getScreenAttributes()["currencyformat"]);
      this.cGrid.currencyFormat = this.currencyPattern;
      this.selectedMessageSeqId = "";
     // this.cGrid.allowMultipleSelection = true;
      const obj = {columns: this.jsonReader.getColumnData()};
        this.cGrid.CustomGrid(obj);
      if (this.jsonReader.getGridData().size > 0) {

          this.cGrid.gridData =  this.jsonReader.getGridData();
          this.cGrid.setRowSize = this.jsonReader.getRowSize();

        this.cGrid.colWidthURL(this.baseURL + "inputexceptionsmessages.do?fromPCM="+this.fromPCM+"&");
        this.cGrid.saveWidths = true;

      } else {
        this.cGrid.dataProvider = [];
        //this.cGrid.selectedIndex = -1
      }
     // this.cGrid.paginationComponent = this.pagination;
     // this.cGrid.id = "main";
    }
    else {
      //SwtAlert.getInstance().show(result.request_reply.message, ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    }

    } catch (e) {
      console.log('error in input ', e)
    }
  }

  public messageAsXML: any;
  public message: string;
  messageRPCResult(data): void {
    if(this.cGrid.selectedIndex == -1) {
      this.seqIdArray = [];
    }
   // this.messageForm.text = "";
    var messageResult = data;
    let returnType: string;
    this.message = messageResult.inputexceptions.message;
    this.message = this.message.split("$#$").join("\n");
    this.message = this.message.split("&@&").join("&nbsp;");
    try {
      if(this.cGrid.selectedIndex >= 0) {
        this.auxMessage = "";
      this.messageAsXML  = prettyData.pd.xml(this.message.split('&nbsp;').join(' '));

      if( parser.validate(this.messageAsXML) !== true){
        this.hideShowAutoFormatXMLCombo(false);
        this.messageForm.htmlText = (this.message);

      }
      else {
        this.hideShowAutoFormatXMLCombo(true);
        if (this.autoFormatXMLCombo.selected) {
          this.messageAsXML =  this.htmlEntities(this.messageAsXML);
          this.messageForm.htmlText = this.messageAsXML;
        }
        else {
          this.messageForm.text = (this.message).replace(/\r/g, '');
        }
      }

    }
    }
    catch (error) {
      console.log('error in ctach', error);
      this.hideShowAutoFormatXMLCombo(false);
      this.messageForm.text = (this.message).replace(/\n/g, '');
    }
  }

  reprocessRPCResult(data): void {
    try {
      this.hideShowAutoFormatXMLCombo(false);
      this.messageForm.htmlText = SwtUtil.getPredictMessage("label.updateResponse", null)+ "<br/>";
      this.cGrid.selectedIndex = -1
      this.messageForm.htmlText += "--------------------<br/><br/>";
      this.messageForm.htmlText += data.request_reply.message;
      this.auxMessage = this.messageForm.htmlText;
      this.setRefresh();
      this.totalAvailableMessages -= this.seqIdArray.length;
      var totalPages: number = this.totalAvailableMessages / 50;
      this.extra = this.totalAvailableMessages % 50;
      if (this.extra > 0) {
        totalPages = Math.ceil(totalPages);
        totalPages++;
      }
      if (totalPages < this.maxPage) {
        this.maxPage = totalPages;
        this.pagination.maximum = this.maxPage;
      }
      if (this.currentPage > this.maxPage) {
        this.currentPage--;
        this.pagination.value = this.currentPage;

      }
      this.params = { fromDate: this.startDate, toDate: this.endDate, status: this.status, type: this.messageType, p: this.currentPage, n: this.messagesPerPage, m: this.totalAvailableMessages, desc: this.descending, order: this.sortingColumn,  fromPCM: this.fromPCM, fromDashboard: this.fromDashboard, currency: this.currencyCode };
      this.requested = true;
      if (this.sendReq)
        this.rowsRPC.send(this.params);
    } catch (e) {
      console.log('error in reprocessRPCResult', e)
    }

  }

  openDeleteAlert(): void {
    var delAlert: Alert;
    this.swtAlert.confirm(SwtUtil.getPredictMessage("label.areyouSure", null), SwtUtil.getPredictMessage("label.deleteMessages", null), Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this));

  }

  alertListener(event): void {
    if (event.detail === Alert.YES) {
      this.deleteMessages();
    }
  }

  deleteMessages(): void {
    var delParams = [];
    this.reprocessButton.enabled = false;
    this.reprocessButton.enabled = false;
    this.rejectButton.enabled = false;
    if (this.seqIdArray.length != 0) {
      var k: Object;
      for (k in this.seqIdArray) {
        delParams.push(this.seqIdArray[k].msgid);
      }

      if (this.sendReq)
        this.deleteRPC.arraySend(delParams, "seqid", false);
    }
    else {
      this.swtAlert.show(SwtUtil.getPredictMessage("label.noMessageSelected", null),  'Error');
    }
  }

  setRefresh(): void {
    ExternalInterface.call("refreshParent");
    if(window.opener && window.opener.instanceElement )
      window.opener.instanceElement.updateDatafromRefresh( );
  }

  
  startLoader(): void {
    this.loadingImage.visible = true;
    this.pagination.enabled = false;

    this.sendReq = false;
  }

  stopLoader(): void {
    if (!this.rowsRPC.isBusy() && !this.messageRPC.isBusy() && !this.reprocessRPC.isBusy() && !this.suppressOrRejectRPC.isBusy() && !this.deleteRPC.isBusy() && !this.colWidth.isBusy()) {
      this.loadingImage.visible = false;
      this.pagination.enabled = true;

      this.sendReq = true;
    }

  }

  /**
			 * If a fault occurs with the connection with the server then display the lost connection label
			 * @param event
			 **/
			inputDataFault(event):void
			{
				this.sendReq=true;
				this.pagination.enabled=true;
			}

  reprocessMessages(): void {
    var reproParams = [];
    this.reprocessButton.enabled = false;
    this.suppressButton.enabled = false;
    this.rejectButton.enabled = false;
    if (this.seqIdArray.length != 0) {

      for (var k = 0; k < this.seqIdArray.length; k++) {
        reproParams.push(this.seqIdArray[k].msgid.content);
      }

      if (this.sendReq) {
        this.requestParams = [];
        this.actionPath = 'inputexceptionsmessages.do?';
        this.actionMethod = 'method=reprocessRequest';
        this.reprocessRPC.cbStart = this.startLoader.bind(this);
        this.reprocessRPC.cbStop = this.stopLoader.bind(this);
        this.reprocessRPC.cbResult = (data) => {
          this.reprocessRPCResult(data);
        };
        this.requestParams['fromPCM']= this.fromPCM;
        this.requestParams['currencyCode'] = this.currencyCode;
        this.requestParams['seqid[]'] = reproParams.toString();
        this.reprocessRPC.url = this.baseURL + this.actionPath + this.actionMethod;
        this.reprocessRPC.url = this.reprocessRPC.url + '&fromPCM=' + this.fromPCM + '&' + 'currencyCode=' + this.currencyCode + '&' + 'seqid=' + reproParams.toString();
        this.reprocessRPC.send(this.requestParams);
      }

    }
    else {
      this.swtAlert.show(SwtUtil.getPredictMessage("label.noMessageSelected", null),  'Error');
    }
  }
			
  suppressOrRejectMessages(option: String): void {
    var reproParams = [];
    this.reprocessButton.enabled = false;
    this.suppressButton.enabled = false;
    this.rejectButton.enabled = false;
    this.hideShowAutoFormatXMLCombo(false);
    if (this.seqIdArray.length != 0) {
      for (var k = 0; k < this.seqIdArray.length; k++) {
        reproParams.push(this.seqIdArray[k].msgid.content);
      }

      if (this.sendReq) {
        this.requestParams = [];
        this.actionPath = 'inputexceptionsmessages.do?';
        this.actionMethod = 'method=suppressOrRejectRequest';
        this.suppressOrRejectRPC.cbStart = this.startLoader.bind(this);
        this.suppressOrRejectRPC.cbStop = this.stopLoader.bind(this);
        this.suppressOrRejectRPC.cbResult = (data) => {
          this.reprocessRPCResult(data);
        };
        this.requestParams['fromPCM']= this.fromPCM;
        this.requestParams['currencyCode'] = this.currencyCode;
        this.requestParams['option'] = option;
        this.requestParams['seqid[]'] = reproParams.toString();
        this.suppressOrRejectRPC.url = this.baseURL + this.actionPath + this.actionMethod;
        this.suppressOrRejectRPC.url = this.suppressOrRejectRPC.url + '&fromPCM=' + this.fromPCM + '&' + 'currencyCode=' + this.currencyCode + '&' + 'option=' + option+ '&' +'seqid[]=' + reproParams.toString();
        this.suppressOrRejectRPC.send(this.requestParams);
      }

    }
    else {
      this.swtAlert.show( SwtUtil.getPredictMessage("label.noMessageSelected", null), 'Error');
    }
  }
  
  /**
			 * keyDownEventHandler
			 *
			 * @param event:  KeyboardEvent
			 *
			 * This is a key event listener, used to perform the operation
			 * when hit the enter key based on the currently focused property(button)
			 */
  keyDownEventHandler(event: KeyboardEvent): void {
    //Currently focussed property name
    var eventString: String = Object(focusManager.getFocus()).id;
    if ((event.keyCode == Keyboard.ENTER)) {
      if (eventString == "reprocessButton") {
        this.reprocessMessages();
      }
      else if (eventString == "closeButton") {
        close();
      }
    }
  }

  /**
   * close the window from the close button
   **/
  closeHandler(event): void {
    ExternalInterface.call("close");
  }

  getUrlParams(): {} {
    var urlParams: HashMap;
    var fullUrl: String = ExternalInterface.call("document_location_href");
    var paramStr: String = fullUrl.split('?')[1];

    // we'll store the parameters here
    var obj = {};

    // if query string exists
    if (paramStr) {

      // stuff after # is not part of query string, so get rid of it
      paramStr = paramStr.split('#')[0];
      // split our query string into its component parts
      var arr = paramStr.split('&');
      for (var i = 0; i < arr.length; i++) {
        // separate the keys and the values
        var a = arr[i].split('=');
        // set parameter name and value (use 'true' if empty)
        var paramName = a[0];
        var paramValue = typeof (a[1]) === 'undefined' ? true : a[1];
        //if (typeof paramValue === 'string') paramValue = paramValue.toLowerCase();
        // if the paramName ends with square brackets, e.g. colors[] or colors[2]
        if (paramName.match(/\[(\d+)?\]$/)) {
          // create key if it doesn't exist
          var key = paramName.replace(/\[(\d+)?\]/, '');
          if (!obj[key]) obj[key] = [];

          // if it's an indexed array e.g. colors[2]
          if (paramName.match(/\[\d+\]$/)) {
            // get the index value and add the entry at the appropriate position
            var index = /\[(\d+)\]/.exec(paramName)[1];
            obj[key][index] = paramValue;
          } else {
            // otherwise add the value to the end of the array
            obj[key].push(paramValue);
          }
        } else {
          // we're dealing with a string
          if (!obj[paramName]) {
            // if it doesn't exist, create property
            obj[paramName] = paramValue;
          } else if (obj[paramName] && typeof obj[paramName] === 'string') {
            // if property does exist and it's a string, convert it to an array
            obj[paramName] = [obj[paramName]];
            obj[paramName].push(paramValue);
          } else {
            // otherwise add the property
            obj[paramName].push(paramValue);
          }
        }
      }
    }
    return obj;
  }

  /**
			 * Hide or show the auto fomat XML combobox, it depends on the value of show boolean. 
			 * If it is true then the combo will be shown. If it is true the combo will be hidden.
			 * 
			 * @param show:Boolean
			 **/
  hideShowAutoFormatXMLCombo(show: Boolean): void {
    if (show) {
      this.autoFormatXMLContainer.visible = true;
    }
    else {
      this.autoFormatXMLContainer.visible = false;
    }
  }

  /**
			 * Event listener handled when the auto format XML combobox is changed in selection
			 * 
			 * @param event:Event
			 **/
  autoFormatXMLMessage(event): void {
    if (this.autoFormatXMLCombo.selected) {
      this.messageForm.htmlText = this.messageAsXML;
    }
    else {
      this.messageForm.text = (this.message).replace(/\n/g, '').replace(/&<;/ig, '&#60 ').replace(/>/g, '&#62 ');
    }
  }
   obtainCell(selectedRow):void
  {
  if(this.cGrid.selectedIndex >= 0) {
   var keyColumnValue: string= "";
   var currentStatus = selectedRow.statusnotes.content; // selectedRow.target.data.statusnotes;
    if (keyColumnValue != ("No Messages"))
    {
      if ((currentStatus != "SUCCESS") && (currentStatus != "PROCESSING") && (parseInt(this.d["status"]) != 7))
      {
        this.reprocessButton.enabled=true;
        this.suppressButton.enabled=true;
        this.rejectButton.enabled=true;
      }
      if (this.fromPCM == 'yes') {
        if (parseInt(this.d["status"]) == 10) {
          this.reprocessButton.enabled=true;
          this.rejectButton.enabled=true;
        }
        else
        {
          this.reprocessButton.enabled=false;
          this.rejectButton.enabled=false;
        }
      }
      this.seqIdArray= this.cGrid.selectedItems;
      if (this.seqIdArray.length > 0)
      {
        if (this.seqIdArray.length == 1)
        {
          if(this.autoFormatXMLContainer.visible) {
            this.autoFormatXMLCombo.enabled = true;
          }
          this.requestParams = [];
          if (this.sendReq) {
            this.actionPath = 'inputexceptionsmessages.do?';
            this.actionMethod = 'method=messageData';
            this.messageRPC.cbResult = (event) => {
                this.messageRPCResult(event)
            };
            this.requestParams["fromPCM"] = this.fromPCM;
            this.requestParams["currencyCode"] = this.currencyCode;
            this.requestParams["seqid"] = this.seqIdArray[0].msgid.content.toString();
            this.messageRPC.url = this.baseURL + this.actionPath + this.actionMethod;

            this.messageRPC.send(this.requestParams);
          }

        }
        else
        {
          let message = ""
          if(this.autoFormatXMLContainer.visible) {
            this.autoFormatXMLCombo.enabled = false;
          }
          message=ExternalInterface.call('getBundle', 'text', 'label-multipleMessageSelected', 'Multiple message selected') +"<br/>" ;

          for (var k = 0; k < this.seqIdArray.length; k++)
          {
            message+= ExternalInterface.call('getBundle', 'text', 'label-messageID', 'Message ID')+" "+ this.seqIdArray[k].msgid.content +"<br/>" ;
          }
          this.messageForm.htmlText = message;
        }
      }

    }
    else
    {
      this.messageForm.text=ExternalInterface.call('getBundle', 'text', 'label-noMessage', 'No Message Selected');
      this.reprocessButton.enabled=false;
      this.suppressButton.enabled=false;
      this.rejectButton.enabled=false;
    }
    } else {
    if(this.auxMessage == "")
      this.messageForm.text="";
   this.hideShowAutoFormatXMLCombo(false);
   this.reprocessButton.enabled=false;
   this.suppressButton.enabled=false;
   this.rejectButton.enabled=false;
  }


  }
  htmlEntities(str) {
    try {
    return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
    replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
    } catch (e) {
      console.log('error', e, str)
    }
  }
  globalSort(e):void
  {
    if (this.sendReq)
    {
      for(let i = 0; i< this.cGrid.sorters.length; i++) {

        this.sortingColumn = this.cGrid.sorters[i].columnId;
        this.descending = this.cGrid.sorters[i].direction ? "false": "true";
      }

      this.params={fromDate: this.startDate, toDate: this.endDate, status: this.status, type: this.messageType, p: this.currentPage, n: this.messagesPerPage, m: this.totalAvailableMessages, desc: this.descending, order: this.sortingColumn, fromPCM: this.fromPCM, currencyCode: this.currencyCode, fromDashboard: this.fromDashboard};
      this.requested=true;

      this.rowsRPC.send(this.params);
    }
  }
  /**
   * When there is a change in the in one of the combo's
   * @param event :Event
   **/
  changeCombo(event):void
  {
    this.messageForm.text="";
    this.messageType = this.messageTypeCombo.selectedLabel;
    this.params={fromDate: this.startDate, toDate: this.endDate, status: this.status, type: this.messageType, p: this.currentPage, n: this.messagesPerPage, m: this.totalAvailableMessages, currencyCode: this.currencyCode, fromPCM: this.fromPCM, fromDashboard: this.fromDashboard};

    if (this.sendReq)
      this.rowsRPC.send(this.params);
  }

}




//Define lazy loading routes
const routes: Routes = [
  { path: '', component: MessageDetails }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [MessageDetails],
  entryComponents: []
})
export class MessageDetailsModule { }
