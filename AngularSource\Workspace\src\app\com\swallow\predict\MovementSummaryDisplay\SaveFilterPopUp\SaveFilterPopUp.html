<SwtModule (creationComplete)="onLoad()" width="100%" height="185">
  <VBox  width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5" verticalGap="0">
    <SwtCanvas width="100%" height="120">
      <HBox paddingLeft="5" paddingTop="5" paddingRight="5" paddingBottom="5" height="100%" width="100%">
        <VBox verticalGap="0">
          <SwtLabel text="Filter Name"></SwtLabel>
          <SwtEditableComboBox id="profileCombo" #profileCombo width="100%"
                      maxlength="50"></SwtEditableComboBox>
          <SwtLabel text="Search Date behaviour"></SwtLabel>
          <HBox>
            <SwtRadioButtonGroup #date id="date" groupName = "date" align="horizontal">
              <SwtRadioItem #fixedRadio id="fixedRadio" label="Fixed" toolTip="Date parameters will be saved as absolute values" value="F" selected="true" groupName="date">
              </SwtRadioItem>
              <SwtRadioItem #relativeRadio id="relativeRadio" toolTip="Date parameters will be saved as relative to current date" label="Relative" value="R" groupName="date"></SwtRadioItem>
            </SwtRadioButtonGroup>
          </HBox>
        </VBox>
      </HBox>
    </SwtCanvas>
    <SwtCanvas width="100%" height="38">
      <HBox>
        <SwtButton id="okButton" #okButton label="Save" (click)="saveFilterClickHandler()"></SwtButton>
        <SwtButton id="cancelButton" #cancelButton label="Cancel" (click)="close()"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
