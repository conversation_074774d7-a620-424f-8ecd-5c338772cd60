<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="vBox1" width="100%" height="100%" paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" height="33%">
      <VBox width="100%" height="100%">
        <Grid>
          <GridRow>
            <GridItem width="10%">
              <SwtLabel id="entityLabel" fontWeight="bold" #entityLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width="50%">
              <SwtComboBox id="entityCombo" #entityCombo
                           (change)="changeCombo($event)"
                           dataLabel="entity"
                           width="135">
              </SwtComboBox>
            </GridItem>
            <GridItem width="20%">
              <SwtLabel
                id="selectedEntity"
                #selectedEntity
                textAlign="left"
                fontWeight="normal">

              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="10%">
              <SwtLabel id="currencyLabel" fontWeight="bold"
                        #currencyLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width="50%">
              <SwtComboBox id="ccyCombo" #ccyCombo
                           (change)="changeCombo($event)"
                           dataLabel="currency"
                           width="85">
              </SwtComboBox>
            </GridItem>
            <GridItem width="20%">
              <SwtLabel id="selectedCcy"
                        #selectedCcy
                        fontWeight="normal">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow><GridItem width="10%">
            <SwtLabel id="accountLabel" fontWeight="bold"
                      #accountLabel>
            </SwtLabel>
          </GridItem>
            <GridItem width="50%">
              <SwtComboBox id="accountCombo" #accountCombo
                           (change)="changeCombo($event)"
                           dataLabel="accounts"
                           width="300">
              </SwtComboBox>
            </GridItem>
            <GridItem width="20%">
              <SwtLabel id="selectedAccount"
                        #selectedAccount
                        fontWeight="normal">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="10%">
              <SwtLabel #attributeLabel
                        id="attributeLabel">
              </SwtLabel>
            </GridItem>
            <GridItem width="50%">
              <SwtComboBox  id="attributeCombo" #attributeCombo
                            (change)="CheckEffectiveDateRaquiredValue()"
                            prompt = "Please select ..."
                            dataLabel="attributes"
                            width="300">
              </SwtComboBox>
            </GridItem>
            <GridItem width="20%">
              <SwtLabel  id="selectedAttribute"
                         #selectedAttribute
                         fontWeight="normal">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="10%">
              <SwtLabel #startDateLabel id="startDateLabel" fontWeight="bold">
              </SwtLabel>
            </GridItem>
            <GridItem width="15%">
              <SwtDateField id="startDate"  #startDate
                            width="70"
                            (change)="validateDateFieldValue()"
                            [editable]="true"
                            restrict="0-9/">
              </SwtDateField>
            </GridItem>
            <GridItem width="10%">
              <SwtLabel #endDateLabel id="endDateLabel"  fontWeight="bold">
              </SwtLabel>
            </GridItem>
            <GridItem width="25%">
              <SwtDateField id="endDate" #endDate
                            width="70"
                            (change)="validateDateFieldValue()"
                            [editable]="true"
                            restrict="0-9/">
              </SwtDateField>
            </GridItem>
            <GridItem width="20%">
              <SwtButton [buttonMode]="true"
                         id="goButton"
                         #goButton
                         (click)="populateGrid()">
              </SwtButton>
            </GridItem>
          </GridRow>

        </Grid>
      </VBox>
    </SwtCanvas>
    <SwtCanvas #gridCanvas id="gridCanvas" width="100%" height="60%" border="false">
    </SwtCanvas>
    <SwtCanvas  id="canvasButtons"  height="7%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true"
                   id="addButton"
                   #addButton
                   (click)="addChangeClickHandler('Add')">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                   id="changeButton"
                   #changeButton
                   (click)="addChangeClickHandler('Change')">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                     id="viewButton"
                     #viewButton
                     (click)="addChangeClickHandler('View')">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                   id="deleteButton"
                   #deleteButton
                   (click)="deleteHandler()">
        </SwtButton>
          <SwtButton [buttonMode]="true"
                   id="closeButton"
                   #closeButton
                   (click)="closeHeader($event)">
        </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="5" paddingTop="5">
          <SwtLoadingImage id="loadingImage" #loadingImage></SwtLoadingImage>
          <SwtHelpButton id="helpIcon"
                         #helpIcon
                         (click)="doHelp()">
          </SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
