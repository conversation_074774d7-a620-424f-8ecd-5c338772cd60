<SwtModule (creationComplete)='onLoad()' height='100%' width="100%">
  <VBox id="vBox1" height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas width="100%" height="94%">
      <VBox width="100%" height="100%">
      <SwtPanel #headerPanel width="100%" height="27%">
        <VBox paddingLeft="10" verticalGap="1" paddingTop="5">
          <HBox width="100% "  height="20%">
            <HBox width="40%" height="100%">
              <SwtLabel text="Stop Rule ID*" width="150"></SwtLabel>
              <SwtTextInput required="true" restrict="a-zA-Z0-9\-_" toolTip="Enter a unique ID for the STOP Rule"
                #ruleIdTextInput width="150" maxChars="20"></SwtTextInput>
            </HBox>
            <HBox width="60%" height="100%">
              <SwtLabel text="Stop Rule Name*" width="120"></SwtLabel>
              <SwtTextInput toolTip="Enter the STOP Rule Name" #ruleNameTextInput required="true" 
                restrict="A-Za-z0-9\d_ !\&quot;#$%&'()*+,\-.\/:;&lt;=&gt;?@[\\\]^`{|}~" width="250" maxChars="50">
              </SwtTextInput>
            </HBox>
          </HBox>

          <HBox height="20%">
              <SwtLabel text="Active" width="150"></SwtLabel>
              <SwtCheckBox #activeCheckBox selected="true" toolTip="Select whether Rule is Active or not"></SwtCheckBox>
          </HBox>
          <HBox height="20%">
            <SwtLabel text="Action On Deactivation" width="150"></SwtLabel>
              <SwtRadioButtonGroup #rulesActionOnDeactivation id="rulesActionOnDeactivation" align="horizontal">
                <SwtRadioItem #radioSetWaiting id="radioSetWaiting" value="W" label="Set To Initial Status"
                  groupName="rulesActionOnDeactivation" width="160"></SwtRadioItem>
                <SwtRadioItem #radioLeaveStoped id="radioLeaveStoped" value="S" label="Leave Stopped"
                  groupName="rulesActionOnDeactivation" width="160"></SwtRadioItem>
              </SwtRadioButtonGroup>
          </HBox>
          <HBox height="20%">
            <SwtLabel text="Type" width="150"></SwtLabel>
              <SwtRadioButtonGroup #rulesType id="rulesType" align="horizontal" (change)="changeRadioButton()">
                <SwtRadioItem #radioquickExpression id="radioquickExpression" value="Q" label="Quick Expression"
                  groupName="rulesType" width="160"></SwtRadioItem>
                <SwtRadioItem #radioAdvancedExpression id="radioAdvancedExpression" value="A" label="Advanced Expression"
                  groupName="rulesType" width="160"></SwtRadioItem>
              </SwtRadioButtonGroup>
          </HBox>

          <HBox height="20%">
              <SwtLabel  width="155" text="Apply to Value Dates"></SwtLabel>
              <SwtLabel width="80" text="From"></SwtLabel>
              <SwtDateField id="startDatePicker" toolTip="Enter start date (if applicable)" #startDatePicker width="70"
                (change)="validateDateField(startDatePicker)"></SwtDateField>

              <spacer width="60"></spacer>

              <SwtLabel width="30" text="To"></SwtLabel>
              <SwtDateField id="endDatePicker" toolTip="Enter end date (if applicable)" #endDatePicker width="70"
                (change)="validateDateField(endDatePicker)"></SwtDateField>
          </HBox>
        </VBox>
      </SwtPanel>
      <HBox paddingLeft="10" >
        <SwtCheckBox #stopAllCheckbox styleName="checkbox" toolTip="Click if all Payment Requests are to be stopped"
          (change)="stopAllPayments()"></SwtCheckBox>
        <SwtLabel class="labelForm" width="150" text="Stop ALL">
        </SwtLabel>
      </HBox>
      <SwtPanel #quickExpressionPanel width="100%" height="43%" title="Quick Expression">
        <VBox paddingLeft="10" verticalGap="1" paddingTop="5">
          <HBox height="30">
            <!-- <SwtCheckBox #paymentinCcyCheckBox styleName="checkbox"toolTip="Select whether condition is Active or not"  (change)="selectCheckBox(paymentinCcyCheckBox, paymentinCcyComboBox, paymentinCcyLabel, ccyMoreItemsButton)"></SwtCheckBox> -->
            <SwtLabel class="labelForm" width="150" text="Currency">
            </SwtLabel>
            <SwtComboBox id="paymentinCcyComboBox" dataLabel="currencyList" #paymentinCcyComboBox width="250"
              toolTip="Select for Currency"
              (change)="changeCombo(paymentinCcyComboBox, ccyMoreItemsButton, paymentinCcyLabel)">
            </SwtComboBox>
            <SwtButton buttonMode="false" enabled="false" id="ccyMoreItemsButton" #ccyMoreItemsButton width="21"
              (click)="multipleListSelect(paymentinCcyComboBox.id, paymentinCcyLabel);"
              (keyDown)="keyDownEventHandler($event)"> </SwtButton>
            <SwtLabel id="paymentinCcyLabel" #paymentinCcyLabel fontWeight="normal"> </SwtLabel>
          </HBox>
          <HBox height="30">
            <!-- <SwtCheckBox #paymentinCcyCheckBox styleName="checkbox"toolTip="Select whether condition is Active or not"  (change)="selectCheckBox(paymentinCcyCheckBox, paymentinCcyComboBox, paymentinCcyLabel, ccyMoreItemsButton)"></SwtCheckBox> -->
            <SwtLabel class="labelForm" width="150" text="Amount">
            </SwtLabel>
            <HBox horizontalGap="1">
              <SwtComboBox id="amountOperatorComboBox"  enabled ="false" (change)="updateQueryTextForStaticRule()" dataLabel="amountOperatorList" #amountOperatorComboBox width="50"
                           toolTip="Select for Amount Operator">
              </SwtComboBox>
              <SwtTextInput id="amountOperatorTextInput"  toolTip="Enter an Amount"  enabled ="false"
                            (focusOut)="validateAmount();"
                            textAlign="right" #amountOperatorTextInput width="195">
              </SwtTextInput>
              <SwtLabel id="paymentinAmountLabel"  #paymentinAmountLabel fontWeight="normal"> </SwtLabel>
            </HBox>

          </HBox>
          <HBox height="30">
            <!-- <SwtCheckBox #paymentToCountryCheckBox styleName="checkbox"toolTip="Select whether condition is Active or not"  (change)="selectCheckBox(paymentToCountryCheckBox, paymentToCountryComboBox, paymentToCountryLabel, countryMoreItemsButton)"></SwtCheckBox> -->
            <SwtLabel class="labelForm" width="150" text="Country">
            </SwtLabel>
            <SwtComboBox id="paymentToCountryComboBox" dataLabel="country" #paymentToCountryComboBox width="250"
              toolTip="Select for Country"
              (change)="changeCombo(paymentToCountryComboBox, countryMoreItemsButton, paymentToCountryLabel)">
            </SwtComboBox>
            <SwtButton buttonMode="false" enabled="false" id="countryMoreItemsButton" #countryMoreItemsButton width="21"
              (click)="multipleListSelect(paymentToCountryComboBox.id, paymentToCountryLabel);"
              (keyDown)="keyDownEventHandler($event)"></SwtButton>
            <SwtLabel id="paymentToCountryLabel" #paymentToCountryLabel fontWeight="normal"></SwtLabel>
          </HBox>
          <HBox height="30">
            <!-- <SwtCheckBox #paymentToCounterPartyCheckBox styleName="checkbox"toolTip="Select whether condition is Active or not"  (change)="selectCheckBox(paymentToCounterPartyCheckBox, paymentToCounterPartyTextInput, paymentToCounterPartyLabel ,null)"></SwtCheckBox> -->
            <SwtLabel class="labelForm" width="150" text="Party BIC">
            </SwtLabel>
            <SwtTextInput id="paymentToCounterPartyTextInput" (change)="updateQueryTextForStaticRule()"
              toolTip="Select for Counterparty" #paymentToCounterPartyTextInput width="250" toolTip="Enter a Party BIC">
            </SwtTextInput>
            <SwtLabel paddingLeft="30" id="paymentToCounterPartyLabel" #paymentToCounterPartyLabel fontWeight="normal">
            </SwtLabel>
          </HBox>
          <HBox height="30">
            <!-- <SwtCheckBox #paymentfromSourceCheckBox styleName="checkbox"toolTip="Select whether condition is Active or not"  (change)="selectCheckBox(paymentfromSourceCheckBox, paymentfromSourceCombobox, paymentfromSourceLabel, sourceMoreItemsButton)"></SwtCheckBox> -->
            <SwtLabel class="labelForm" width="150" text="Source">
            </SwtLabel>
            <SwtComboBox id="paymentfromSourceCombobox" dataLabel="source" #paymentfromSourceCombobox width="250"
              toolTip="Select for Payment Source"
              (change)="changeCombo(paymentfromSourceCombobox, sourceMoreItemsButton,paymentfromSourceLabel )">
            </SwtComboBox>
            <SwtButton buttonMode="false" enabled="false" id="sourceMoreItemsButton" #sourceMoreItemsButton width="21"
              (click)="multipleListSelect(paymentfromSourceCombobox.id, paymentfromSourceLabel);"
              (keyDown)="keyDownEventHandler($event)"></SwtButton>
            <SwtLabel id="paymentfromSourceLabel" #paymentfromSourceLabel fontWeight="normal"></SwtLabel>
          </HBox>
          <HBox height="30">
            <!-- <SwtCheckBox #paymentfromMessageTypeCheckBox styleName="checkbox"toolTip="Select whether condition is Active or not"  (change)="selectCheckBox(paymentfromMessageTypeCheckBox, paymentfromMessageTypeComboBox, paymentfromMessageTypeLabel, paymentMoreItemsButton)"></SwtCheckBox> -->
            <SwtLabel class="labelForm" width="150" text="Message Type">
            </SwtLabel>
            <SwtComboBox id="paymentfromMessageTypeComboBox" dataLabel="messageType" #paymentfromMessageTypeComboBox
              width="250" toolTip="Select for Message Type"
              (change)="changeCombo(paymentfromMessageTypeComboBox, paymentMoreItemsButton, paymentfromMessageTypeLabel)">
            </SwtComboBox>
            <SwtButton buttonMode="false" enabled="false" id="paymentMoreItemsButton" #paymentMoreItemsButton width="21"
              (click)="multipleListSelect(paymentfromMessageTypeComboBox.id,paymentfromMessageTypeLabel );"
              (keyDown)="keyDownEventHandler($event)"></SwtButton>
            <SwtLabel id="paymentfromMessageTypeLabel" #paymentfromMessageTypeLabel fontWeight="normal"></SwtLabel>
          </HBox>

          <HBox height="30">
            <!-- <SwtCheckBox #accountGroupCheckBox styleName="checkbox"toolTip="Select whether condition is Active or not"  (change)="selectCheckBox(accountGroupCheckBox, acctGrpComboBox, acctGrpLabel, acctGrpButton)"></SwtCheckBox> -->
            <SwtLabel class="labelForm" width="150" text="Account Group">
            </SwtLabel>
            <SwtComboBox id="acctGrpComboBox" dataLabel="AcctGrpList" #acctGrpComboBox width="250"
              toolTip="Select for Account Groups" (change)="changeCombo(acctGrpComboBox, acctGrpButton, acctGrpLabel)">
            </SwtComboBox>
            <SwtButton buttonMode="false" enabled="false" id="acctGrpButton" #acctGrpButton width="21"
              (click)="multipleListSelect(acctGrpComboBox.id,acctGrpLabel);" (keyDown)="keyDownEventHandler($event)">
            </SwtButton>
            <SwtLabel id="acctGrpLabel" #acctGrpLabel fontWeight="normal"></SwtLabel>
          </HBox>
        </VBox>
      </SwtPanel>
      <SwtPanel #advancedExpressionPanel width="100%" height="21%" title="Rule Text">
          <HBox width="100%" height="100%">
            <SwtTextArea id='queryText' #queryText editable="false" height='100%' width="80%">
            </SwtTextArea>
            <VBox width="20%" height="90%" horizontalAlign="center" verticalAlign="middle">
              <SwtButton id='expressionBuilderButton' #expressionBuilderButton [buttonMode]='true' label='Rule Builder'
                width="100" (click)="expressionButtonClick();">
              </SwtButton>
            </VBox>
          </HBox>
      </SwtPanel>
      <SwtPanel #activationInfoPanel width="100%" height="14%" title="Activation Info">
        <VBox paddingLeft="10" paddingTop="5">
          <HBox>
            <SwtLabel text="Activated At" width="150"></SwtLabel>
            <SwtTextInput #activatedOnTextInput width="200"></SwtTextInput>
            <spacer width="30"></spacer>
            <SwtLabel text="By" width="40"></SwtLabel>
            <SwtTextInput #activatedByTextInput width="200"></SwtTextInput>
          </HBox>
          <HBox>
            <SwtLabel text="De-Activated At" width="150"></SwtLabel>
            <SwtTextInput #deactivatedOnTextInput width="200"></SwtTextInput>
            <spacer width="30"></spacer>
            <SwtLabel text="By" width="40"></SwtLabel>
            <SwtTextInput #deactivatedByTextInput width="200"></SwtTextInput>
          </HBox>
        </VBox>
      </SwtPanel>
      </VBox>
    </SwtCanvas>
    <SwtCanvas id="canvasContainer"  height="6%" width="100%">
      <HBox width="100%">
        <HBox width="100%" paddingLeft="5">
          <SwtButton #saveButton (click)="saveEventHandler()" (keyDown)="keyDownEventHandler($event)" id="saveButton"
            width="70"></SwtButton>
            <SwtButton #amendButton width="70"        label="Amend"          visible="false" includeInLayout="false"         (click)="amendEventHandler()"
            (keyDown)="keyDownEventHandler($event)"         id="amendButton"></SwtButton>

            <SwtButton buttonMode="true"
            id="cancelAmendButton"
            width="70"   
            visible="false" includeInLayout="false"
            #cancelAmendButton
            (click)="cancelAmendEventHandler();"
            (keyDown)="keyDownEventHandler($event)"></SwtButton>
<SwtButton buttonMode="true"
            id="closeButton"
            visible="false" includeInLayout="false"
            #closeButton
            width="70"   
            marginLeft="5"
            (click)="popupClosed();"
            (keyDown)="keyDownEventHandler($event)"></SwtButton>

          <SwtButton buttonMode="true" id="cancelButton" marginLeft="5" width="70" #cancelButton (click)="closeBtn();"
            (keyDown)="keyDownEventHandler($event)"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">
          <SwtButton #acceptButton (click)="acceptEventEventHandler()" visible="false" label="Accept" (keyDown)="keyDownEventHandler($event)" id="acceptButton"
            width="70"></SwtButton>
          <SwtButton buttonMode="true" id="rejectButton" width="70" visible="false" label="Reject" #rejectButton (click)="rejectEventEventHandler();"
            (keyDown)="keyDownEventHandler($event)"></SwtButton>
          <SwtHelpButton id="helpIcon" [buttonMode]="true" enabled="true"  helpFile="groups-of-rules" (click)="doHelp()">
          </SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
