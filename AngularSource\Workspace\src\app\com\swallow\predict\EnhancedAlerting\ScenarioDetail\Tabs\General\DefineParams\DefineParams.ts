import { Component, OnInit, NgModule, ModuleWithProviders, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, CommonService, SwtAlert, SwtLabel, SwtCommonGrid, SwtUtil, SwtTextInput, SwtCanvas, SwtButton, Alert, StringUtils } from 'swt-tool-box';
declare var require: any;
var prettyData = require('pretty-data');
let convert = require('xml-js');
@Component({
  selector: 'app-define-params',
  templateUrl: './DefineParams.html',
  styleUrls: ['./DefineParams.css']
})

export class DefineParams extends SwtModule implements OnInit {

  @ViewChild('scenarioIdLbl') scenarioIdLbl: SwtLabel;
  @ViewChild('paramDesc') paramDesc: SwtLabel;
  @ViewChild('scenarioIdtxt') scenarioIdtxt: SwtTextInput;
  @ViewChild('paramCanvas') paramCanvas: SwtCanvas;
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('scanQuery') scanQuery: SwtButton;
  
  
  private deletedRow = -1;
  private paramArray=[];
  private sceduleGridData=[];
  public xml="";
  private paramsEventsFromParent: any;
  private paramGrid: SwtCommonGrid;
  public enabledRow: number = -1;
  private swtalert: SwtAlert;
  private defParamsData=[];
  private gridParams;
  private methodName;
  private rowIndex;
  private dataField;
  private oldVal;
  private newVal;
  private parentGridData=[];
  private nameChangedFlag=false;
  private rowDeletedFlag=false;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.paramGrid = <SwtCommonGrid>this.paramCanvas.addChild(SwtCommonGrid);
    this.scenarioIdLbl.text = SwtUtil.getPredictMessage('scenario.scenarioId', null);
    this.paramDesc.text = SwtUtil.getPredictMessage('scenario.paramDesc', null);
    this.cancelButton.label = SwtUtil.getPredictMessage("button.cancel", null);
    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    //tooltips part
    this.scenarioIdtxt.toolTip = SwtUtil.getPredictMessage('scenario.scenarioId', null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage("tooltip.cancelbutton", null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('tooltip.defineParams.add', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('tooltip.defineParams.delete', null);
    this.okButton.toolTip = SwtUtil.getPredictMessage('tooltip.ok', null);
    this.paramGrid.editable=true;
  }

  onLoad() {
    this.defParamsData = [];
    if (window.opener && window.opener.instanceElement) {
      this.paramsEventsFromParent = window.opener.instanceElement.sendGeneralDataToDefParams();
      this.scenarioIdtxt.text = this.paramsEventsFromParent.scenarioId;
      this.paramGrid.CustomGrid(this.paramsEventsFromParent.gridData.metadata);
      this.gridParams = this.paramsEventsFromParent.xmlData;
      this.methodName= this.paramsEventsFromParent.methodName;
      this.parentGridData=this.paramsEventsFromParent.genaralGridData;
    }
    if(this.gridParams){
    this.populateData();
    }

    this.paramGrid.ITEM_CHANGED.subscribe((item) => {
      this.alertUser(item);
    });

    this.paramGrid.onRowClick = (event) => {
      this.cellClickEventHandler();
    };

  }

  cellClickEventHandler(){
    if (this.paramGrid.selectedIndex >= 0){
      this.deleteButton.enabled=true;
      this.deleteButton.buttonMode=true;
    }else{
      this.deleteButton.enabled=false;
      this.deleteButton.buttonMode=false;
    }
  }


  alertUser(event){
    this.rowIndex = event.rowIndex;
    this.dataField = event.dataField;
    this.oldVal = event.listData.oldValue;
    this.newVal = event.listData.newValue;
    if(this.methodName=="change" && this.dataField=="name" && this.oldVal && this.parentGridData.length>0){
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('scenario.existingConfigAlert', null)));
      this.swtalert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.updateParam.bind(this));
  }
  }

  updateParam(event) {
    if (event.detail == Alert.YES) {
      this.nameChangedFlag=true;
    } else {
      this.paramGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content = this.oldVal;
      this.paramGrid.dataProvider[this.rowIndex][this.dataField] = this.oldVal;
      this.paramGrid.refresh();
    }
  }

  
  populateData(){
    this.convertXml();
    let metadata = this.paramsEventsFromParent.gridData.metadata;
    for (let index = 0; index < metadata.columns.column.length; index++) {
      metadata.columns.column[index]['editable']= true;
    }
    this.paramGrid.CustomGrid(metadata);
    this.paramGrid.gridData={ size: this.defParamsData.length, row:this.defParamsData };
    this.paramGrid.enableDisableCells=(row) => {
      return false;
    };
    this.paramGrid.refresh();
  }

convertXml(){
  if(this.gridParams) {
    let options = {
      object: false,
      reversible: false,
      coerce: false,
      sanitize: true,
      trim: true,
      arrayNotation: false,
      alternateTextNode: false,
      compact: true
    };
    let jsonData = convert.xml2js(this.gridParams, options);
    let data = jsonData.requiredParameters.parameter;
    if (data) {
    if (!data.length)
        data = [data];
      for (let i = 0; i < data.length; i++) {
        this.defParamsData.push({ name: { clickable: false, content: data[i].name._cdata, negative: false }, description: { clickable: false, content: data[i].description._cdata, negative: false } });

      }
  }
}
}

  enableRow() {
    try {
    this.deleteButton.enabled=true;
    this.deleteButton.buttonMode=true;
    let index=this.methodName == "add" && this.enabledRow!=-1?this.enabledRow:this.paramGrid.gridData.length-1;
    if (this.paramGrid.gridData.length > 0 && this.paramGrid.gridData[index] && this.paramGrid.gridData[index].name == "") {
      this.swtalert.error(SwtUtil.getPredictMessage('scenario.alert.emptyRowAlreadyAdded', null));
    } else {
var metadata = this.paramsEventsFromParent.gridData.metadata;
      this.deletedRow = -1;
  this.enabledRow++;
      if (this.methodName == "add") {
        if (this.enabledRow == 0 && this.paramGrid.gridData.length==0) {
  for (let index = 0; index < metadata.columns.column.length; index++) {
            metadata.columns.column[index]['editable'] = true;
  }
  this.paramGrid.CustomGrid(metadata);
          this.paramGrid.gridData = { row: this.paramsEventsFromParent.gridData.rows.row, size: this.paramsEventsFromParent.gridData.rows.size };
          this.paramGrid.enableDisableCells = (row, field) => {
    return this.enableDisableRow(row, field);
  };
  this.paramGrid.refresh();
          this.paramGrid.selectedIndex = this.enabledRow;
        }else{
          let item: any;
          item = {
            'name': { 'content': "" },
            'description': { 'content': "" }
          }
          this.paramGrid.appendRow(item, true);
          this.paramGrid.enableDisableCells = (row, field) => {
            return this.enableDisableRow(row, field);
          };
          this.paramGrid.refresh();
          this.paramGrid.selectedIndex =this.paramGrid.gridData.length-1;
          this.enabledRow=this.paramGrid.gridData.length-1;
}
      } else {
        if (this.paramGrid.gridData < 1) {
          for (let index = 0; index < metadata.columns.column.length; index++) {
            metadata.columns.column[index]['editable'] = true;
          }
          this.paramGrid.CustomGrid(metadata);
          this.paramGrid.gridData = { row: this.paramsEventsFromParent.gridData.rows.row, size: this.paramsEventsFromParent.gridData.rows.size };
          this.paramGrid.enableDisableCells = (row, field) => {
            if (row.id == 0)
              return true;
            else
              return false;
          };
          this.paramGrid.refresh();
          this.paramGrid.selectedIndex = 0;
        }else{
    let item: any;
    item = {
      'name': { 'content': "" },
      'description': { 'content': "" }
        }
  this.paramGrid.appendRow(item, true);
        this.paramGrid.selectedIndex = this.paramGrid.gridData.length - 1;
 /* if (lastIndex!=-1){
  this.defParamsData[lastIndex].name.content=this.paramGrid.gridData[lastIndex].name;
  this.defParamsData[lastIndex].description.content=this.paramGrid.gridData[lastIndex].description;

  this.paramGrid.appendRow(this.defParamsData[lastIndex], true);

  }
  this.defParamsData.push({name: { clickable: false, content: "", negative: false },description: { clickable: false, content: "", negative: false }});
  this.paramGrid.gridData={ size: this.defParamsData.length, row:this.defParamsData };*/
  this.paramGrid.refresh();
        this.paramGrid.enableDisableCells = (row) => {
    return this.enableRowByIndex(row);
  };
}
    }
    }

    
  } catch (error) {
      
  }
}



  private enableRowByIndex(row: any): boolean {
    let ref=this.deletedRow!=-1?this.paramGrid.gridData.length:this.paramGrid.gridData.length-1;
    if (row.id==this.paramGrid.gridData.length-1)
      return true;
    else return false;
  }

  private enableDisableRow(row: any, field: string): boolean {
    if (row.id == this.enabledRow) {
      return true;
    } else {
    return false;
  }
}

  deleteRow() {
    if (window.opener.instanceElement.schedGrid.gridData.length > 0) {
    Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
    Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
    var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('scenario.existingConfigAlert', null)));
    this.swtalert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.proceedWithDelete.bind(this));
    } else {
      this.refreshParamGrid(false);
    }
  }
  proceedWithDelete(event) {
    if (event.detail == Alert.YES) {
      this.refreshParamGrid(true);
    }
  }

  refreshParamGrid(flag) {
    try {
      this.rowDeletedFlag = flag;
      this.deletedRow = this.paramGrid.selectedIndex;
      this.paramGrid.removeSelected();
        this.paramGrid.enableDisableCells = (row, field) => {
          if (row.description == "" && row.name == "") {
            return true;
          } else {
          return false;
          }
        };
      this.paramGrid.refresh();
      this.deleteButton.enabled=false;
      this.deleteButton.buttonMode=false;
      
    } catch (error) {
      console.log("DefineParams -> refreshParamGrid -> error", error)
    }
  }

closePopup(){
  window.close();
}

gridParamsToXml(){
  if (this.paramGrid.gridData.length > 0){
    this.xml = "<requiredParameters>" ;
    for (let i = 0; i < this.paramGrid.gridData.length; i++) {
      if(this.paramGrid.gridData[i].name){
        this.sceduleGridData.push({parameter: { clickable: false, content: this.paramGrid.gridData[i].name, negative: false },value: { clickable: false, content: "", negative: false }});
        this.xml += "<parameter>" ;
        this.xml +="<name><![CDATA["  + this.paramGrid.gridData[i].name + "]]></name>";
        this.xml +="<description><![CDATA["  + this.paramGrid.gridData[i].description + "]]></description>";
        this.xml += "</parameter>" ;
      }
    }
    this.xml += "</requiredParameters>" ;

  }
  this.xml = prettyData.pd.xml(this.xml);
  if (window.opener && window.opener.instanceElement) {
    window.opener.instanceElement.xmlParams=this.xml;
    window.opener.instanceElement.scheduleGridData=this.sceduleGridData;
  }
  if(this.nameChangedFlag || this.rowDeletedFlag){
    window.opener.instanceElement.schedGrid.gridData= { size: 0, row: [] };
    window.opener.instanceElement.schedGrid.refresh();
    window.opener.instanceElement.oldSchedRows=[];
  }
  this.nameChangedFlag=false;
  this.rowDeletedFlag=false;
  window.close();
}
private paramsList  =[];
scanQueryForParams(){
  if (window.opener && window.opener.instanceElement) {
    const queryText = window.opener.instanceElement.parentDocument.identificationTab.baseQuery.text;
    if(queryText){
      this.paramsList = []
      let rxp = /p{([^}]+)}/gi,
      // str = "a {string} with {curly} braces",
      curMatch;
  
    while( curMatch = rxp.exec( queryText ) ) {
      if(this.paramsList.indexOf(curMatch[1]) == -1)
      this.paramsList.push( curMatch[1] );
    }
      if(this.paramsList.length>0){
        const message = "Scenario Query contain some parameters, do you want to override the actual Parameters Definition?"
        this.swtalert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.confirmListener.bind(this));
      }
      
    }
  
  
   
      }
    }


  confirmListener(event) {
    try {
      
  
    /* Condition to check Ok button is selected */
    if (event.detail == Alert.YES) {
      this.paramGrid.gridData = { size: 0, row: [] };
      setTimeout(() => {
        
        for (let index = 0; index < this.paramsList.length; index++) {
          const element = this.paramsList[index];
          let item: any;
          item = {
            'name': { 'content': element },
            'description': { 'content': "" }
          }
          this.paramGrid.appendRow(item, true);
          this.enabledRow++;
        }
        this.deletedRow = -1;
       

        this.paramGrid.selectedIndex = 0;
        this.paramGrid.editable =true;
        this.paramGrid.editedItemPosition = { rowIndex: 0 , columnIndex: 0 }; 
        this.paramGrid.editedItemPosition = { rowIndex: 0 , columnIndex: 1 };
        this.methodName= "change";
      }, 300);



    } else {
    }

  } catch (error) {
  console.log("🚀 ~ file: DefineParams.ts ~ line 333 ~ confirmListener ~ error", error)
      
  }
  }



}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: DefineParams }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [DefineParams],
  entryComponents: []
})
export class DefineParamsModule {}