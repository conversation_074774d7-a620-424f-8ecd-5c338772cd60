import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {
  SwtModule, CommonService, SwtAlert, HTTPComms, JSONReader, SwtUtil, ExternalInterface,
  SwtLabel, SwtCanvas, SwtButton, SwtComboBox, SwtTextInput, SwtCommonGrid, SwtCommonGridPagination,
  SwtHelpButton, SwtLoadingImage, HBox, VBox, ExportEvent, // Assuming VBox is available or similar used
  SwtToolBoxModule
} from 'swt-tool-box';
import { PaginationChangedArgs } from 'angular-slickgrid'; // Assuming SwtCommonGrid uses this or similar

// For functions like refreshAliasDetails that might be called from child legacy windows
declare var window: any;

@Component({
  selector: 'app-party-maintenance',
  templateUrl: './PartyMaintenance.html',
   styleUrls: ['./PartyMaintenance.css'] // If you have specific CSS
})
export class PartyMaintenance extends SwtModule implements OnInit {

  // ViewChild declarations for UI elements
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('entityDescLabel') entityDescLabel: SwtLabel;
  @ViewChild('partyIdLabel') partyIdLabel: SwtLabel;
  @ViewChild('partyIdInput') partyIdInput: SwtTextInput;
  @ViewChild('partyNameLabel') partyNameLabel: SwtLabel;
  @ViewChild('partyNameInput') partyNameInput: SwtTextInput;
  @ViewChild('searchButton') searchButton: SwtButton;
  @ViewChild('resetButton') resetButton: SwtButton; // Reset button ViewChild

  @ViewChild('pageBoxTop') pageBoxTop: HBox;
  @ViewChild('numStepperTop') numStepperTop: SwtCommonGridPagination;

  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
  private mainGrid: SwtCommonGrid;

  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('aliasButton') aliasButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('helpButton') helpButton: SwtHelpButton;
  @ViewChild('printButton') printButton: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  // Data and state properties
  private actionPath: string;
  private actionMethod: string = "";
  private requestParams: any = {};
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON: any;
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private deleteData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();

  private currentSort: string = "0|false"; // Default from JSP (partyId ascending)
  private currentFilter: string = "All|All|All|All|All"; // Default from JSP
  private maxPage: number = 1;
  private currentPage: number = 1;
  private totalCount: number = 0;
  private menuEntityCurrGrpAccess: string = "1"; // Default to restricted if not found
  private initialButtonStates: any = {}; // To store flags like ADD_BUT_STS

  // For selected row data
  private selectedPartyData: any = null;

  // Constants for column mapping if backend expects original indices for sort/filter
  // Based on JSP: PartyID, PartyName, Type, ParentParty, NoOfAliases
  STATIC_COLUMN_INDEX: Record<string, number> = {
    partyid: 0,       // Assuming 'party.id.partyId' maps to 'partyid' in grid data
    partyname: 1,     // Assuming 'party.partyName'
    partytypedesc: 2, // Assuming 'party.header' / 'partyDescription'
    parentparty: 3,
    noofaliases: 4
  };


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
     window["Main"] = this; // For refreshAliasDetails if called externally
  }

  ngOnInit() {
    // Initialize labels from i18n
    this.entityLabel.text = SwtUtil.getPredictMessage('party.entityId', null);
    this.partyIdLabel.text = SwtUtil.getPredictMessage('party.partyId', null);
    this.partyNameLabel.text = SwtUtil.getPredictMessage('party.partyName', null);
    this.searchButton.label = SwtUtil.getPredictMessage('button.search', null);
    this.searchButton.toolTip = SwtUtil.getPredictMessage('tooltip.executeSearch', null);

    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('tooltip.addnewparty', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('tooltip.changeSelParty', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('tooltip.deleteSeletedParty', null);
    this.aliasButton.label = SwtUtil.getPredictMessage('button.alias', null);
    this.aliasButton.toolTip = SwtUtil.getPredictMessage('tooltip.partyAlias', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    // this.printButton.label might not be needed if it's icon only
    this.printButton.toolTip = SwtUtil.getPredictMessage('tooltip.printScreen', null);
    this.helpButton.toolTip = SwtUtil.getPredictMessage('tooltip.helpScreen', null);
    this.resetButton.label = SwtUtil.getPredictMessage('button.reset', null);
    this.resetButton.toolTip = SwtUtil.getPredictMessage('tooltip.resetSearch', null);
    this.entityCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.partyIdInput.toolTip= SwtUtil.getPredictMessage('tooltip.enterPartyId', null);
    this.partyNameInput.toolTip= SwtUtil.getPredictMessage('tooltip.enterPartyName', null);
 
    this.resetButton.enabled = true; // Enable reset button by default


    // Initialize Grid
    this.mainGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.mainGrid.clientSideSort = false;
    this.mainGrid.clientSideFilter = false;
    this.mainGrid.paginationComponent = this.numStepperTop; // Link pagination

    // Grid event subscriptions
    this.mainGrid.ITEM_CLICK.subscribe((event) => this.onGridItemClick(event));
    this.mainGrid.onSortChanged = () => this.fetchDataWithCurrentState(1,false); // Reset to page 1 on sort
    this.mainGrid.onFilterChanged = () => this.fetchDataWithCurrentState(1, false); // Reset to page 1 on filter
    this.mainGrid.onPaginationChanged = (args: PaginationChangedArgs) => {
      console.log("🚀 ~ ngOnInit ~ this.currentPage:", this.currentPage)
      if (this.numStepperTop.value !== this.currentPage) {
        this.fetchDataWithCurrentState(this.numStepperTop.value, false);
      }
    };

    // Initial state for buttons
    this.updateButtonStates();
    this.onSearchInputChange(); // Initial check for search button
  }

  refreshGridData(): void {
   this.fetchDataWithCurrentState(this.numStepperTop.value, false);
  }

  onLoad() {
    this.loadingImage.setVisible(false);


    // Apply initial button states from server, then update based on selection/permissions
    this.applyInitialButtonPermissions();
    


    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => this.initialInputDataResult(event); // Special handler for first load
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;

    

    this.deleteData.cbStart = this.startOfComms.bind(this);
    this.deleteData.cbStop = this.endOfComms.bind(this);
    this.deleteData.cbResult = (event) => this.deleteDataResult(event); // Special handler for first load
    this.deleteData.cbFault = this.inputDataFault.bind(this);
    this.deleteData.encodeURL = false;


    this.actionPath = "party.do?";
    // Assuming a default method to load initial data (entities, and maybe first set of parties)
    this.actionMethod = 'method=displayAngular'; // Or a new method for Angular initial load
    this.requestParams = {
      selectedSort: this.currentSort,
      selectedFilter: this.currentFilter,
      currentPage: this.currentPage,
      // entityId: this.entityCombo.selectedLabel, // May not be selected yet
      partyId: this.partyIdInput.text,
      partyName: this.partyNameInput.text
    };
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    // Check for JSP's parentFormRefresh logic (simplified)
    if (ExternalInterface.call('eval', 'parentFormRefresh') === 'yes') {
      ExternalInterface.call('openerSubmitForm', "displayList"); // Assumes opener has this method exposed
      ExternalInterface.call("close");
      return;
    }

    // Check for criteriaNotMatch alert
    if (ExternalInterface.call('eval', 'criteriaNotMatch') === 'yes') {
      this.swtAlert.error(SwtUtil.getPredictMessage('party.alert.criteriaNotMatch', null));
    }
  }

  private deleteDataResult(event: any): void {
     if (this.deleteData.isBusy()) {
      this.deleteData.cbStop();
    } else {

      // Parse result json
      this.jsonReader.setInputJSON(event);
      if (!this.jsonReader.getRequestReplyStatus()) {
        if ("DataIntegrityViolationExceptioninDelete" == this.jsonReader.getRequestReplyMessage()) {
          this.swtAlert.error(SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninDelete') + SwtUtil.getPredictMessage('alert.ContactSysAdm'), 'Error');
        } else {

          this.swtAlert.error('Error occurred, Please contact your System Administrator: \n'
            + this.jsonReader.getRequestReplyMessage(), 'Error');
        }
      } else {
        this.refreshGridData();
      }
    }
  }

  private initialInputDataResult(event: any): void {
    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);

    if (this.jsonReader.getRequestReplyStatus()) {
      // Populate Entity ComboBox
      console.log("🚀 ~ initialInputDataResult ~azea eaz  event:", this.jsonReader.getSelects())
      this.entityCombo.setComboData(this.jsonReader.getSelects(), false); // Assuming 'entityList' is in selects
      const defaultEntity = this.jsonReader.getSingletons().defaultEntityId; // Assuming this key
      console.log("🚀 ~ initialInputDataResult ~ defaultEntity:", defaultEntity)
      if (defaultEntity) {
        this.entityCombo.selectedLabel = defaultEntity;
      }
      this.entityDescLabel.text = this.entityCombo.selectedValue || '';

      // Initial PartyId and PartyName if provided
      this.partyIdInput.text = this.jsonReader.getSingletons().partyId || '';
      this.partyNameInput.text = this.jsonReader.getSingletons().partyName || '';
      this.menuEntityCurrGrpAccess = this.jsonReader.getSingletons().menuEntityCurrGrpAccess || '2';
      console.log("🚀 ~ initialInputDataResult ~ this.menuEntityCurrGrpAccess:", this.menuEntityCurrGrpAccess)
      
      this.initialButtonStates.add =this.jsonReader.getSingletons().swt_add_btn_sts === 'true'; // Assuming ADD_BUT_STS is exposed this way
      this.initialButtonStates.change =this.jsonReader.getSingletons().swt_chg_btn_sts === 'true';
      this.initialButtonStates.delete =this.jsonReader.getSingletons().swt_del_btn_sts  === 'true';


      this.onSearchInputChange(); // Update search button state

      // Process grid data (same as regular inputDataResult)
      this.processGridData(event);
      this.applyInitialButtonPermissions(); // Re-apply after data load potentially changes context

    } else {
      this.handleRequestError();
    }
    // Switch to regular result handler for subsequent calls
    this.inputData.cbResult = (evt) => this.regularInputDataResult(evt);
  }

  private regularInputDataResult(event: any): void {
    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    if (this.jsonReader.getRequestReplyStatus()) {
      this.processGridData(event);
    } else {
      this.handleRequestError();
    }
  }

  private processGridData(event: any): void {
    console.log("🚀 ~ processGridData ~ event:", event)
    // Default values
    this.currentPage = 1;
    this.maxPage = 1;
    this.totalCount = 0;
  
    // Check for partyList and nested properties
    if (event && event.partyList && event.partyList.grid && event.partyList.grid.paging) {
      const pagingData = event.partyList.grid.paging;
      this.currentPage = Number(pagingData.currentpage || 1);
      this.maxPage = Number(pagingData.maxpage || 1);
      this.totalCount = Number( this.jsonReader.getRowSize() || 0);
    }
  
    if (this.numStepperTop) {
      this.numStepperTop.value = this.currentPage;
      this.numStepperTop.maximum = this.maxPage;
      this.numStepperTop.minimum = 1; // Ensure minimum is set
    }
  
    if (this.pageBoxTop) {
      this.pageBoxTop.visible = this.maxPage > 1;
    }
  
    // Apply sort/filter info if returned from backend
    if (event && event.partyList && event.partyList.grid) {
      const gridInfo = event.partyList.grid;
      this.currentSort = gridInfo.currentSort || this.currentSort;
      this.currentFilter = gridInfo.currentFilter || this.currentFilter;
      // TODO: If your SwtCommonGrid component has methods to imperatively set
      // the visual state of sorting/filtering based on these strings, call them here.
      // Example:
      // if (this.mainGrid && typeof this.mainGrid.setSortIndicator === 'function') this.mainGrid.setSortIndicator(this.currentSort);
      // if (this.mainGrid && typeof this.mainGrid.applyFilterIndicators === 'function') this.mainGrid.applyFilterIndicators(this.currentFilter);
    }
  
    // Assuming jsonReader and its methods are correctly used elsewhere or this logic needs adjustment
    // For this revision, I'm focusing on removing '?.' from accessing 'event' properties.
    // The logic for 'isDataBuilding' would depend on how your 'jsonReader' or 'event' signals this.
    // If 'isDataBuilding' is a method on 'this.jsonReader' it's fine.
    if (!this.jsonReader.isDataBuilding()) {
      let gridMetadata = null;
      if (event && event.partyList && event.partyList.grid && event.partyList.grid.metadata) {
        gridMetadata = event.partyList.grid.metadata.columns;
      }
      console.log("🚀 ~ processGridData ~ gridMetadata:", gridMetadata)
  
      if (gridMetadata && this.mainGrid) {
        this.mainGrid.CustomGrid({ columns: gridMetadata });
      }
  
      let gridRows = null;
      if (event && event.partyList && event.partyList.grid && event.partyList.grid.rows) {
        gridRows = event.partyList.grid.rows;
      }
      console.log("🚀 ~ processGridData ~ gridRows:", gridRows)
  
      if (gridRows && typeof gridRows.size === 'number' && gridRows.size > 0 && this.mainGrid) {
        this.mainGrid.gridData = gridRows;
        // Safely assign to setRowSize, checking if it's a function or property
        if (typeof (this.mainGrid as any).setRowSize === 'function') {
          (this.mainGrid as any).setRowSize(this.totalCount);
        } else if (this.mainGrid.hasOwnProperty('setRowSize')) {
          (this.mainGrid as any).setRowSize = this.totalCount;
        }
       
      } else if (this.mainGrid) {
        this.mainGrid.gridData = { size: 0, row: [] };
        if (typeof (this.mainGrid as any).setRowSize === 'function') {
          (this.mainGrid as any).setRowSize(0);
        } else if (this.mainGrid.hasOwnProperty('setRowSize')) {
          (this.mainGrid as any).setRowSize = 0;
        }
      }
    }
    
    this.selectedPartyData = null; // Clear selection
    this.mainGrid.selectedIndex = -1; // Reset selected index
    this.onGridItemClick(null); // Clear any previous selection state
    this.updateButtonStates();    // Update button states based on new data
  }

  private handleRequestError(): void {
    if (this.lastRecievedJSON && this.lastRecievedJSON.hasOwnProperty("request_reply")) {
      this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
    } else {
      this.swtAlert.error("An unknown error occurred while fetching data.", "Error");
    }
  }

  fetchDataWithCurrentState(page: number = 1, resetValues): void {
    this.currentPage = page;
    this.requestParams = {
      selectedSort: resetValues?this.currentSort:this.buildSortString(), // Use build methods for consistency
      selectedFilter: resetValues?this.currentFilter:this.buildFilterString(),
      currentPage: this.currentPage,
      entityId: this.entityCombo.selectedLabel,
      partyId: this.partyIdInput.text.trim(),
      partyName: this.partyNameInput.text.trim(),
      filterFromSerach: "true" // As per JSP logic for subsequent loads
    };
    console.log("🚀 ~ fetchDataWithCurrentState ~ this.requestParams.this.buildSortString():", this.buildSortString())
    this.actionMethod = 'method=displayAngular'; // Or specific method for list refresh
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  onEntityChange(): void {
    this.entityDescLabel.text = this.entityCombo.selectedValue || '';
    this.partyIdInput.text = "";
    this.partyNameInput.text = "";
    this.onSearchInputChange();
    this.currentFilter = "All|All|All|All|All"; // Reset filter
    this.currentSort = "0|false"; // Reset sort
    // this.mainGrid.clearSortFilter(); // If SwtCommonGrid supports this
    this.fetchDataWithCurrentState(1, true);
  }

  onSearchInputChange(): void {
    const partyId = this.partyIdInput.text || "";
    console.log("🚀 ~ onSearchInputChange ~ partyId:", partyId)
    const partyName = this.partyNameInput.text || "";
    console.log("🚀 ~ onSearchInputChange ~ partyName:", partyName)
    this.searchButton.enabled = partyId.trim() !== "" || partyName.trim() !== "";
    console.log("🚀 ~ onSearchInputChange ~ this.searchButton.enabled:", this.searchButton.enabled)
  }

  onSearchClick(): void {
    if (this.searchButton.enabled) {
      this.currentFilter = "All|All|All|All|All"; // Reset filter on new search
      this.currentSort = "0|false"; // Reset sort
      this.fetchDataWithCurrentState(1, true);
    }
  }

  onResetClick(): void {
    this.partyIdInput.text = '';
    this.partyNameInput.text = '';
    this.currentFilter = "All|All|All|All|All";
    this.currentSort = "0|false";
    this.onSearchInputChange();
    this.fetchDataWithCurrentState(1, true);
  }

  onGridItemClick(data: any): void {
    this.selectedPartyData = data;
    console.log("🚀 ~ onGridItemClick ~ data:", data)
    // Assuming data contains: partyId, partyName, parentParty (for selectedCustodianCode, selectedCustodianName, selectedParentPartyId)
    // Example:
    // this.selectedPartyData.selectedCustodianCode = data.partyId.content;
    // this.selectedPartyData.selectedCustodianName = data.partyName.content;
    // this.selectedPartyData.selectedParentPartyId = data.parentParty.content;
    this.updateButtonStates();
  }

  private applyInitialButtonPermissions(): void {
    // Based on JSP: <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
    // This logic needs server data (initialButtonStates)
    if (this.menuEntityCurrGrpAccess === "0") {
        this.addButton.enabled = this.initialButtonStates.add !== undefined ? this.initialButtonStates.add : true; // Default to true if flag not present
    } else {
        this.addButton.enabled = false;
    }
    this.updateButtonStates(); // General update based on selection after this
}


  private updateButtonStates(): void {
    const isSelected = this.mainGrid.selectedItem !== null && this.mainGrid.selectedItem !== undefined;
    console.log("🚀 ~ updateButtonStates ~ isSelected:", isSelected)

    // Default states based on JSP logic (menuEntityCurrGrpAccess == "0" is permissive)
    const canInteractGenerally = this.menuEntityCurrGrpAccess === "0";
    console.log("🚀 ~ updateButtonStates ~ canInteractGenerally:", canInteractGenerally)

    // Add button: depends on initial permissions, not selection typically, but JSP toggles it.
    // Assuming add button is generally available if permissions allow, or specific logic from JSP bodyOnLoad.
    // For now, let's use the initial server-driven state if menuAccess is 0.
    if (canInteractGenerally) {
        this.addButton.enabled = this.initialButtonStates.add !== undefined ? this.initialButtonStates.add : true;
    } else {
        this.addButton.enabled = false;
    }

    this.changeButton.enabled = isSelected && (canInteractGenerally && (this.initialButtonStates.change !== undefined ? this.initialButtonStates.change : true) );
    this.deleteButton.enabled = isSelected && (canInteractGenerally && (this.initialButtonStates.delete !== undefined ? this.initialButtonStates.delete : true) );
    this.aliasButton.enabled = isSelected; // Alias seems to depend only on selection in JSP
  }


  onAddClick(): void {
    if (!this.addButton.enabled) return;
    const params = {
      method: 'add', // Assuming a new Angular-friendly add screen method
      entityCode: this.entityCombo.selectedLabel,
      entityName: this.entityCombo.selectedValue,
      // Pass other necessary params like parentScreenPartyId from search criteria
      parentScreenPartyId: this.partyIdInput.text.trim(),
      parentScreenPartyName: this.partyNameInput.text.trim(),
      // Current state for return navigation if needed
      currentPage: this.currentPage,
      filterCriteria: this.buildFilterString(),
      selectedSort: this.buildSortString(),
      selectedFilter: this.buildFilterString(), // Re-evaluate if filterCriteria or selectedFilter is better
      maxPage: this.maxPage
    };
    const url = `party.do?${this.buildQueryString(params)}`;
    ExternalInterface.call("openLegacyWindow", url, 'partymaintenanceaddWindow', 'left=50,top=190,width=700,height=290,toolbar=0,status=1', true);
  }

  onChangeClick(): void {
    if (!this.changeButton.enabled || !this.selectedPartyData) return;
        console.log("🚀 ~ onChangeClick ~ this.selectedPartyData:", this.selectedPartyData)

    const params = {
      method: 'change', // Assuming a new Angular-friendly change screen method
      entityCode: this.entityCombo.selectedLabel,
      entityName: this.entityCombo.selectedValue,
      partyId: this.selectedPartyData.target.data.partyId, // Assuming partyId is in selected data
      partyName: this.selectedPartyData.target.data.partyName, // Assuming partyName
      // Current state for return navigation
      currentPage: this.currentPage,
      filterCriteria: this.buildFilterString(),
      selectedSort: this.buildSortString(),
      selectedFilter: this.buildFilterString(),
      maxPage: this.maxPage,
      parentScreenPartyId: this.partyIdInput.text.trim(),
      parentScreenPartyName: this.partyNameInput.text.trim()
    };
    const url = `party.do?${this.buildQueryString(params)}`;
    console.log("🚀 ~ onChangeClick ~ url:", url)
    ExternalInterface.call("openLegacyWindow", url, 'partymaintenanceaddWindow', 'left=50,top=190,width=700,height=290,toolbar=0,status=1', true);
  }

  async onDeleteClick(): Promise<void> {
    if (!this.deleteButton.enabled || !this.selectedPartyData) return;

    const partyCanBeDeleted = await this.checkPartyUse(this.selectedPartyData.target.data.partyId);
    if (!partyCanBeDeleted) {
      this.swtAlert.error(SwtUtil.getPredictMessage('alert.checkParty', null));
      return;
    }



    const confirmMessage = typeof ExternalInterface.call === 'function' ?
    ExternalInterface.call('getLocalizedMessage', 'confirm.delete') :
    SwtUtil.getPredictMessage('confirm.delete', null);
    const confirmTitle = SwtUtil.getPredictMessage('screen.alert.confirm', null);

    this.swtAlert.confirm(confirmMessage, confirmTitle, SwtAlert.YES | SwtAlert.NO, null, this.handleDeleteConfirmation.bind(this), null);

  }

  handleDeleteConfirmation(result: any): void {

    
    console.log("🚀 ~ handleDeleteConfirmation ~ result:", result)
    if (result.detail == SwtAlert.YES) {
      try {
          this.requestParams = {
            entityId: this.entityCombo.selectedLabel,
            partyId: this.selectedPartyData.target.data.partyId,
            partyName: this.selectedPartyData.target.data.partyName, // Though name might not be needed for delete
            // Include current filter/sort/page so the list refreshes correctly
            selectedFilter: this.buildFilterString(),
            selectedSort: this.buildSortString(),
            currentPage: this.currentPage,
            maxPage: this.maxPage,
            filterCriteria: this.buildFilterString(), // Or specific criteria from search if different
            filterFromSerach: "true"
          };

        }catch (error) {
          console.error("🚀 ~ handleDeleteConfirmation ~ error:", error);
        }
          const actionMethod = 'method=deletePartyAngular';
          this.deleteData.url = this.baseURL + this.actionPath + actionMethod;
          this.deleteData.send(this.requestParams);
        }
     
      }



  

  private async checkPartyUse(partyId: string): Promise<boolean> {
    // This ideally becomes an async call.
    // For now, mimicking ExternalInterface if such a bridge exists, or direct HTTPComms
    return new Promise((resolve) => {
        const checkParams = {
            method: 'checkPartyUseAngular', // New method for Angular
            entityId: this.entityCombo.selectedLabel,
            partyId: partyId
        };
        const tempInputData = new HTTPComms(this.commonService);
        tempInputData.cbResult = (event) => {
            const reader = new JSONReader();
            reader.setInputJSON(event);
            // Assuming backend returns { "partyCheckFlag": "true" or "false" }
            resolve(reader.getSingletons().partyCheckFlag === "true");
        };
        tempInputData.cbFault = (event) => {
            this.inputDataFault(event);
            resolve(false); // Fail safe
        };
        tempInputData.url = this.baseURL + "party.do?" + this.buildQueryString(checkParams);
        tempInputData.send({}); // No additional body params needed for GET-like check
    });
  }

  onAliasClick(): void {
    if (!this.aliasButton.enabled || !this.selectedPartyData) return;
    const params = {
      method: 'displayAliasDetails',
      selectedEntityId: this.entityCombo.selectedLabel,
      selectedPartyId: this.selectedPartyData.target.data.partyId,
      partyName: this.selectedPartyData.target.data.partyName,
      entityDesc: this.entityCombo.selectedValue,
      menuAccessId: this.menuEntityCurrGrpAccess
    };
    const url = `party.do?${this.buildQueryString(params)}`;
    ExternalInterface.call("openLegacyWindow", url, 'partymaintenancealiasWindow', 'left=50,top=190,width=785,height=525,toolbar=0,resizable=yes,scrollbars=yes', true);
  }

  // Called from child window via ExternalInterface bridge
  public refreshAliasCell(partyId: string, noOfAliases: string): void {
    if (this.mainGrid && this.mainGrid.gridData && this.mainGrid.gridData.row) {
      const rowIndex = this.mainGrid.gridData.row.findIndex(r => {
        // Explicitly check if r and r.partyId exist before accessing r.partyId.content
        return r && r.partyId && r.partyId.content === partyId;
      });

      if (rowIndex > -1) {
        // Create a new row object to trigger change detection if gridData is immutable
        const originalRow = this.mainGrid.gridData.row[rowIndex];
        const updatedRow = { ...originalRow }; // Shallow copy the original row

        // Ensure noOfAliasesAsString property is handled correctly
        updatedRow.noOfAliasesAsString = { content: noOfAliases + ' ' }; // Match JSP's extra space

        // Create a new gridData object and a new row array for change detection
        const newRowArray = [...this.mainGrid.gridData.row];
        newRowArray[rowIndex] = updatedRow;

        this.mainGrid.gridData = {
          ...this.mainGrid.gridData, // Copy other properties of gridData (like size)
          row: newRowArray
        };

        // Or if SwtCommonGrid has a method to update a cell/row:
        // if (typeof this.mainGrid.updateRowItem === 'function') {
        //   // Assuming originalRow or updatedRow has an 'id' property suitable for the grid
        //   const rowId = originalRow.id; // Or however the grid identifies rows
        //   if (rowId !== undefined) {
        //     this.mainGrid.updateRowItem(rowId, updatedRow);
        //   }
        // }
      }
    }
  }

      /**
  * printPage
  *
  * param event
  *
  * Method to get call the action to get reports
  */
 printPage(): void {
  let errorLocation = 0;
  try {
    ExternalInterface.call('printPage');

  } catch (error) {
    // log the error in ERROR LOG
    SwtUtil.logError(error, "Predict", "className", "printPage", errorLocation);
  }
}

  onCloseClick(): void {
    ExternalInterface.call("confirmClose", 'P'); // Assuming 'P' is a parameter for the type of close
  }

  onHelpClick(): void {
    // JSP uses: openWindow(buildPrintURL('print','Party Maintenance'),'sectionprintdwindow',...)
    // This seems to be a generic help screen utility.
    // Assuming buildPrintURL is a global JS function accessible via ExternalInterface or a new Angular service.
    // For simplicity, if it's just opening a URL:
    const helpUrl = ExternalInterface.call('buildPrintURL', 'print', 'Party Maintenance');
    if (helpUrl) {
      ExternalInterface.call("openLegacyWindow", helpUrl, 'sectionprintdwindow', 'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes,status=yes,scrollbars=no', true);
    }
  }

  onPrintClick(): void {
    // JSP's printParty() function collected many form values.
    // This should be simplified if possible, or replicated.
    const params = {
        // method: 'printPartyAngular', // If backend handles print generation
        selectedFilter: this.buildFilterString(),
        selectedSort: this.buildSortString(),
        partyId: this.partyIdInput.text.trim(),
        partyName: this.partyNameInput.text.trim(),
        filterCriteria: this.buildFilterString(), // Or specific search criteria if different
        // selectedFilterStatus: this.mainGrid.getFilterStatus(), // If applicable
        filterFromSerach: "true",
        currentPage: this.currentPage,
        maxPages: this.maxPage,
        entityId: this.entityCombo.selectedLabel // Added entityId
    };
    // Assuming a generic print utility or a specific backend endpoint
    ExternalInterface.call("printScreenWithParams", "party.do", params);
  }

  private buildSortString(): string {
    const id = (this.mainGrid.sortedGridColumnId || '').toLowerCase();
    if (!id) return this.currentSort || "0|false"; // Default if nothing from grid
    const idx = this.STATIC_COLUMN_INDEX[id];
    console.log("🚀 ~ buildSortString ~ idx:", idx)
    if (idx === undefined) return this.currentSort || "0|false";

   /* const dir = this.mainGrid.sortDirection || '';
    const asc = dir.toString().toUpperCase() === 'ASC'; // Or 'TRUE' depending on grid*/

    return `${idx}|${this.mainGrid.sortDirection}`;
  }

  private buildFilterString(): string {
    const maxIdx = Math.max(...Object.values(this.STATIC_COLUMN_INDEX));
    const filterArr: string[] = Array(maxIdx + 1).fill("All");

    const filters = this.mainGrid.filters || [];
    filters.forEach(f => {
      const colId = (f.columnId || '').toLowerCase();
      const idx = this.STATIC_COLUMN_INDEX[colId];
      if (idx !== undefined && f.operator === 'EQ' && f.searchTerms && f.searchTerms.length > 0) {
        filterArr[idx] = f.searchTerms[0];
      }
    });
    return filterArr.join('|');
  }

  private buildQueryString(params: any): string {
    return Object.keys(params)
      .map(k => `${encodeURIComponent(k)}=${encodeURIComponent(params[k])}`)
      .join('&');
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  private inputDataFault(event: any): void {
    const faultInfo = event.fault || {};
    const errorMessage = `${faultInfo.faultString || 'Communication fault.'}\n${faultInfo.faultCode || ''}\n${faultInfo.faultDetail || ''}`;
    this.swtAlert.error(errorMessage, "Error");
    this.endOfComms();
  }
}

// NgModule definition
const routes: Routes = [
  { path: '', component: PartyMaintenance }
];
export const routing: ModuleWithProviders<RouterModule> = RouterModule.forChild(routes); // Use ModuleWithProviders<RouterModule> for newer Angular

@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PartyMaintenance],
})
export class PartyMaintenanceModule { }