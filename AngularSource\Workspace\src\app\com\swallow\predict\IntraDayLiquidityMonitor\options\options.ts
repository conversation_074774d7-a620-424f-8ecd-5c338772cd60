import {Component, OnInit, NgModule, ModuleWithProviders, ViewChild, ElementRef} from '@angular/core';
import {
  SwtToolBoxModule,

  CommonService,
  SwtAlert,
  JSONReader,
  SwtModule,
  SwtCanvas,
  SwtCommonGrid,
  HTTPComms,
  SwtUtil,
  SwtLoadingImage,
  SwtButton,
  ExternalInterface,
  SwtDataExport,
  SwtTreeCommonGrid, ExportEvent, Alert
} from 'swt-tool-box';
import {Routes, RouterModule} from '@angular/router';

declare var require: any;
var prettyData = require('pretty-data');

@Component({
  selector: 'app-options',
  templateUrl: './options.html',
  styleUrls: ['./options.css']
})
export class Options extends SwtModule implements OnInit {

  @ViewChild('canvasOption') canvasOption: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('orderButton') orderButton: SwtButton;
  @ViewChild('exportContainer') exportContainer: SwtDataExport;
  private swtAlert: SwtAlert;
  private optionGrid: SwtTreeCommonGrid;
  public inputData = new HTTPComms(this.commonService);
  public sendData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private summaryList = [];
  private summaryCcyChecked: number = 0;
  private globalList = [];
  private groupAnalysisList = [];
  private combinedViewList = [];
  private tabDateList = [];
  private ilmTabEntityList = [];
  private ilmTabAcctGroupList = [];
  private orderCcyList = [];
  private orderEntiyList = [];
  private isSaveMethod: string;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.optionGrid = <SwtTreeCommonGrid>this.canvasOption.addChild(SwtTreeCommonGrid);
    this.optionGrid.editable = true;
    this.optionGrid.enableRowSelection = false;
  this.optionGrid.lockedColumnCount = 1;
    this.optionGrid.uniqueColumn = "ccyEntityGrp"
  }

  onLoad() {
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.inputDataResult(data);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;

    this.sendData.cbStart = this.startOfComms.bind(this);
    this.sendData.cbStop = this.endOfComms.bind(this);
    this.sendData.cbResult = (data) => {
      this.sendDataResult(data);
    };
    this.sendData.cbFault = this.inputDataFault.bind(this);
    this.sendData.encodeURL = false;

    this.actionPath = "ilmAnalysisMonitor.do?";
    this.actionMethod = "method=getOptionData";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.optionGrid.ITEM_CLICK.subscribe((selectedCell) => {
      this.cellClick(selectedCell);
    });

    ExportEvent.subscribe((type) => {
      this.report(type)
    });
    this.optionGrid.enableDisableCells=(row, field) => {
      return this.enableDisableRow(row, field);
    };
    this.optionGrid.showHideCells = (row, field) => {
      return this.showHideCells(row, field);
    };

  }
  showHideCells(row:any, field:string): boolean {
    try {
      if(field == "globalChart" || field =="groupAnalysis" || field == "combinedChart"  || field == "tabDate" || field == "ilmTab") {
        return (row.currencyParent != undefined && row.entityParent == undefined)

      }
      else if(field == "order") {
        return (row.entityParent == undefined)
      }
      else
        return true;
    } catch(e) {
      console.log('error in showHideCells', e)
    }

  }

  private enableDisableRow(row: any, field: string): boolean {
    if ((row.globalChart == "N" || row.groupAnalysis == "N") && field == "combinedChart")
      return false;
    else if(row.ilmTab == "N" && (field == "globalChart"|| field == "groupAnalysis"))
      return false;
    else if(row.ccyEntityGrp == "All" && (field == "combinedChart"|| field == "groupAnalysis" || field=="summary"))
      return false;
    else if(row.ccyEntityGrp == "Global Groups")
      return false;
    else return true;
  }
  cellClick(selectedCell) {
    if(selectedCell.target.field == "globalChart" ||  selectedCell.target.field == "groupAnalysis") {

      this.enableDisableRow(this.optionGrid.selectedIndex, "combinedChart")
      if(selectedCell.target.data.combinedChart == "Y" && (selectedCell.target.data.globalChart != "Y" || selectedCell.target.data.groupAnalysis !="Y") )
        selectedCell.target.data.combinedChart = "N";
        this.optionGrid.gridObj.invalidateRow(this.optionGrid.selectedIndex)
        this.optionGrid.gridObj.render();
    } else if(selectedCell.target.field  == "ilmTab") {
      this.enableDisableRow(this.optionGrid.selectedIndex, "");
      if(selectedCell.target.data.ilmTab != "Y" &&(selectedCell.target.data.globalChart == "Y" || selectedCell.target.data.groupAnalysis =="Y")) {
        selectedCell.target.data.globalChart = "N";
        selectedCell.target.data.groupAnalysis = "N";
      }
      this.optionGrid.gridObj.invalidateRow(this.optionGrid.selectedIndex)
      this.optionGrid.gridObj.render();
      // this.optionGrid.angularSlickGrid.grid.render()
      // this.optionGrid.refresh();
    } else if(selectedCell.target.field == "summary") {
      let ccyName = selectedCell.target.data.ccyEntityGrp.length == 3 ?  selectedCell.target.data.ccyEntityGrp : "";
      //remove checked entity and group when ccy is unchecked
      if(ccyName != "" && selectedCell.target.data.summary =="N") {
        for(let i = 0; i < this.optionGrid.dataProvider.length; i++) {
          if(ccyName!= "" && this.optionGrid.dataProvider[i].currencyParent != undefined &&  ccyName == this.optionGrid.dataProvider[i].currencyParent && this.optionGrid.dataProvider[i].summary == "Y"  ) {
            this.optionGrid.dataProvider[i].summary = "N";
            this.optionGrid.gridObj.invalidateRow(this.optionGrid.selectedIndex)
            this.optionGrid.gridObj.render();
          }

        }
      }
      //remove group when entity is unckeckd
      let entityName = (selectedCell.target.data.currencyParent != undefined) ? selectedCell.target.data.ccyEntityGrp : ""
      if(entityName != "" && selectedCell.target.data.summary =="N") {
        for(let i = 0; i < this.optionGrid.dataProvider.length; i++) {
          if(entityName!= "" && this.optionGrid.dataProvider[i].entityParent != undefined &&  entityName == this.optionGrid.dataProvider[i].entityParent && this.optionGrid.dataProvider[i].summary == "Y"  ) {
            this.optionGrid.dataProvider[i].summary = "N";
            this.optionGrid.gridObj.invalidateRow(this.optionGrid.selectedIndex)
            this.optionGrid.gridObj.render();
          }

        }
      }

    }

  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.exportContainer.enabled = false;
    this.saveButton.enabled = false;
    this.cancelButton.enabled = false;
    this.orderButton.enabled = false;
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.exportContainer.enabled = true;
    this.saveButton.enabled = true;
    this.cancelButton.enabled = true;
    this.orderButton.enabled = true;
  }

  inputDataFault() {
    this.swtAlert.error('Generic exception error')
  }

  inputDataResult(event) {
    try {
      if (this.inputData.isBusy())
        this.inputData.cbStop();
      else {
        //Retrieves the XML from ResultEvent
        this.lastRecievedJSON = event;
        //Pass the XML received to this.jsonReader
        this.optionGrid.forceExpandAllItems = true;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        this.optionGrid.gridComboDataProviders(event.ILMOptions.selects);
        this.optionGrid.CustomGrid(event.ILMOptions.grid.metadata);
        this.optionGrid.gridData = this.jsonReader.getGridData();
        this.optionGrid.setRowSize = this.jsonReader.getRowSize();
        if(this.jsonReader.getSingletons().summaryList != "") {
          this.summaryList = this.jsonReader.getSingletons().summaryList.split(',');
          for(let i = 0; i< this.summaryList.length; i++) {
            if(this.summaryList[i].split('&&').length == 2)
              this.summaryCcyChecked ++
          }
        }
        if(this.jsonReader.getSingletons().globalList != "")
          this.globalList = this.jsonReader.getSingletons().globalList.split(',');
        if(this.jsonReader.getSingletons().groupAnalysisList != "")
         this.groupAnalysisList = this.jsonReader.getSingletons().groupAnalysisList.split(',');
        if(this.jsonReader.getSingletons().combinedViewList != "")
         this.combinedViewList = this.jsonReader.getSingletons().combinedViewList.split(',');
        if(this.jsonReader.getSingletons().tabDateList != "")
          this.tabDateList = this.jsonReader.getSingletons().tabDateList.split(',');
        if(this.jsonReader.getSingletons().ilmTabEntity != "")
          this.ilmTabEntityList = this.jsonReader.getSingletons().ilmTabEntity.split(',');
        if(this.jsonReader.getSingletons().ilmTabEntityGrp != "")
          this.ilmTabAcctGroupList = this.jsonReader.getSingletons().ilmTabEntityGrp.split(',');
        if(this.jsonReader.getSingletons().orderEntityList != "")
          this.orderEntiyList = this.jsonReader.getSingletons().orderEntityList.split(',');
        if(this.jsonReader.getSingletons().orderCcyList != "")
          this.orderCcyList = this.jsonReader.getSingletons().orderCcyList.split(',');

        this.isSaveMethod = this.jsonReader.getSingletons().isSaveMethod;
      }
    } catch (e) {
      console.log('error in input data result', e)
    }
  }

  saveHandler() {
    this.optionGrid.refresh();
    setTimeout(() => {
      
    
    let changes = this.optionGrid.changes.getValues();
    let index = this.summaryCcyChecked;
    for (let i = 0; i < changes.length; i++) {
      let ccyEntityName: any;
      ccyEntityName = changes[i].crud_data["currencyParent"] + "&&" + changes[i].crud_data["ccyEntityGrp"] ;
      /****Summary changes********/
      if (changes[i].crud_operation.indexOf("summary") != -1) {
        //add changes
        let isCurrencyChecked = changes[i].crud_data["ccyEntityGrp"].length == 3 ||( changes[i].crud_data["currencyParent"] != undefined && this.summaryList.indexOf(changes[i].crud_data["currencyParent"]));
        if(isCurrencyChecked && changes[i].crud_original_data["summary"] =="N") {
          index ++;
          let ccyName = "";
          if(isCurrencyChecked )
            ccyName = (changes[i].crud_data["currencyParent"] != undefined) ? changes[i].crud_data["currencyParent"] : changes[i].crud_data["ccyEntityGrp"];
          for (let j=0; j <changes.length; j++) {
            if(changes[j].crud_data["currencyParent"] != undefined && changes[j].crud_data["entityParent"] == undefined  &&  ccyName == changes[j].crud_data["currencyParent"]  ) {
              this.summaryList.push(ccyName +"&&"+changes[j].crud_data["ccyEntityGrp"])
            }else if(changes[j].crud_data["entityParent"] != undefined &&  ccyName == changes[j].crud_data["currencyParent"]  ) {
              this.summaryList.push(ccyName +"&&"+ changes[j].crud_data["entityParent"] +"&&" +changes[j].crud_data["ccyEntityGrp"])
            }
            }

        }
        //remove changes
        if(changes[i].crud_data["summary"] =="N") {
          let ccyEntityGrp = "";
          let ccyEntity = "";
          if(changes[i].crud_data["entityParent"] != undefined && changes[i].crud_data["currencyParent"] != undefined  ) {
            ccyEntityGrp = changes[i].crud_data["currencyParent"] + "&&" + changes[i].crud_data["entityParent"] + "&&" + changes[i].crud_data["ccyEntityGrp"];
            if(this.summaryList.indexOf(ccyEntityGrp) != -1) {
              this.summaryList = this.arrayRemove(this.summaryList, ccyEntityGrp)
            }

          }
          if(changes[i].crud_data["entityParent"] == undefined && changes[i].crud_data["currencyParent"] != undefined  ) {
            ccyEntity = changes[i].crud_data["currencyParent"] + "&&" + changes[i].crud_data["ccyEntityGrp"];
            if(this.summaryList.indexOf(ccyEntity) != -1) {
              index --;
              this.summaryList = this.arrayRemove(this.summaryList, ccyEntity)
            }

          }

        }
        //check if ccy is only checked
        // if(this.summaryList.length < index) {
        //   this.swtAlert.warning('Select at least one entity for currency in summary column');
        //   return;
        // }

      }


      /*******Order changes*********/
      if (changes[i].crud_operation.indexOf("order") != -1) {
        if(changes[i].crud_data["currencyParent"] != undefined) {
          if (this.orderEntiyList.indexOf(ccyEntityName + "=" + changes[i].crud_original_data["order"]) == -1) {
            if(!SwtUtil.isEmpty(changes[i].crud_data["order"]))
            this.orderEntiyList.push(ccyEntityName + "=" + changes[i].crud_data["order"])
          }else {
            this.orderEntiyList= this.arrayRemove(this.orderEntiyList,ccyEntityName + "=" + changes[i].crud_original_data["order"] )
            if(!SwtUtil.isEmpty(changes[i].crud_data["order"]))
            this.orderEntiyList.push(ccyEntityName + "=" + changes[i].crud_data["order"])
          }
        } else {
          //CcyOrder
          let currencyName = changes[i].crud_data["ccyEntityGrp"];
          if (this.orderCcyList.indexOf(currencyName + "=" + changes[i].crud_original_data["order"]) == -1) {
            if(!SwtUtil.isEmpty(changes[i].crud_data["order"]))
            this.orderCcyList.push(currencyName + "=" + changes[i].crud_data["order"])
          }else {
            this.orderCcyList= this.arrayRemove(this.orderCcyList,currencyName + "=" + changes[i].crud_original_data["order"] )
            if(!SwtUtil.isEmpty(changes[i].crud_data["order"]))
            this.orderCcyList.push(currencyName + "=" + changes[i].crud_data["order"])
          }
        }


      }
      /*ILM Tab changes**/

      if (changes[i].crud_operation.indexOf("ilmTab") != -1) {
        let ccyEntityGrp = "";
        if(changes[i].crud_data["entityParent"] != undefined)
         ccyEntityGrp = changes[i].crud_data["currencyParent"] + "&&" +changes[i].crud_data["entityParent"] +  "&&" + changes[i].crud_data["ccyEntityGrp"]

        if( this.ilmTabEntityList.indexOf(ccyEntityName) == -1 && changes[i].crud_data["entityParent"] == undefined) {
          //entity level
          this.ilmTabEntityList.push(ccyEntityName)
        }
        if (this.ilmTabEntityList.indexOf(ccyEntityName) != -1 && changes[i].crud_data["ilmTab"] == "N") {
          this.ilmTabEntityList = this.arrayRemove(this.ilmTabEntityList,ccyEntityName );
        }
        //Grp Level
        if(ccyEntityGrp!= "" && this.ilmTabAcctGroupList.indexOf(ccyEntityGrp) == -1  )
          this.ilmTabAcctGroupList.push(ccyEntityGrp );
        if (this.ilmTabAcctGroupList.indexOf(ccyEntityGrp) != -1 && changes[i].crud_data["ilmTab"] == "N") {
          this.ilmTabAcctGroupList = this.arrayRemove(this.ilmTabAcctGroupList,ccyEntityGrp );
        }

      }
      /*Tab Date changes**/
      if (changes[i].crud_operation.indexOf("tabDate") != -1) {
        if (this.tabDateList.indexOf(ccyEntityName + "=" + changes[i].crud_original_data["tabDate"]) == -1) {
          this.tabDateList.push(ccyEntityName + "=" + changes[i].crud_data["tabDate"])
        }else {
          this.tabDateList= this.arrayRemove(this.tabDateList,ccyEntityName + "=" + changes[i].crud_original_data["tabDate"] )
          this.tabDateList.push(ccyEntityName + "=" + changes[i].crud_data["tabDate"])
        }
       
      }
      /***Global Chart changes**/
      if (changes[i].crud_operation.indexOf("globalChart") != -1) {
        if (this.globalList.indexOf(ccyEntityName) == -1) {
          this.globalList.push(ccyEntityName)
        }
        if (this.globalList.indexOf(ccyEntityName) != -1 && changes[i].crud_data["globalChart"] == "N") {
          this.globalList = this.arrayRemove(this.globalList,ccyEntityName );
        }
      }
      /***Group analysis changes**/
      if (changes[i].crud_operation.indexOf("groupAnalysis") != -1) {
        if (this.groupAnalysisList.indexOf(ccyEntityName) == -1) {
          this.groupAnalysisList.push(ccyEntityName)
        }
        if (this.groupAnalysisList.indexOf(ccyEntityName) != -1 && changes[i].crud_data["groupAnalysis"] == "N") {
          this.groupAnalysisList = this.arrayRemove(this.groupAnalysisList,ccyEntityName );
        }
      }
      /***combined View changes**/
      if (changes[i].crud_operation.indexOf("combinedChart") != -1) {
        if (this.combinedViewList.indexOf(ccyEntityName) == -1) {
          this.combinedViewList.push(ccyEntityName)
        }
        if (this.combinedViewList.indexOf(ccyEntityName) != -1 && changes[i].crud_data["combinedChart"] == "N") {
          this.combinedViewList= this.arrayRemove(this.combinedViewList,ccyEntityName );
        }
      }

    }

    const ilmTabsGroups = this.getFields(this.optionGrid.dataviewObj.getItems(), "groupAnalysis");
    const ilmTabsGlobals = this.getFields(this.optionGrid.dataviewObj.getItems(), "globalChart");
    const ilmTabsCombined = this.getFields(this.optionGrid.dataviewObj.getItems(), "combinedChart");
    const ilmTabsSelected = this.getFields(this.optionGrid.dataviewObj.getItems(), "ilmTab", false,true);
    const currencyListInSummary = this.getFields(this.optionGrid.dataviewObj.getItems(), "summary", true);
    const ilmTabAccountGroupsSelected = this.getFields(this.optionGrid.dataviewObj.getItems(), "ilmTab", false, false, true);
    const summaryFromField = this.getFields(this.optionGrid.dataviewObj.getItems(), "summary", false, false, false, true);
    
    for (let index = 0; index < currencyListInSummary.length; index++) {
      const element = currencyListInSummary[index];
      if(summaryFromField.toString().indexOf(element) == -1){
        this.swtAlert.warning('Select at least one entity for currency in summary column');
        return
      }
      
    }
    
    let allTabsCombined = this.removeDuplicatesFromArray(ilmTabsGroups.concat(ilmTabsGlobals));
    if(!this.arraysEqual(ilmTabsSelected, allTabsCombined)){
      this.swtAlert.warning('Please select Group Analysis or Global View for ILM Tab');
      return;
    }
    let xml = "";
    if (this.summaryList.length > 0)
      xml = "<Summary>" + this.removeDuplicatesFromArray(summaryFromField).toString() + "</Summary>";
    if (this.ilmTabEntityList.length > 0)
      xml += "<ILMTabEntity>" + this.removeDuplicatesFromArray(ilmTabsSelected).toString() + "</ILMTabEntity>";
    if (this.ilmTabAcctGroupList.length > 0)
      xml += "<ILMTabAcctGrp>" + this.removeDuplicatesFromArray(this.ilmTabAcctGroupList).toString() + "</ILMTabAcctGrp>";
    if (this.groupAnalysisList.length > 0)
      xml += "<GroupAnalysis>" + this.removeDuplicatesFromArray(ilmTabsGroups).toString() + "</GroupAnalysis>";
    if (this.globalList.length > 0)
      xml += "<GlobalView>" + this.removeDuplicatesFromArray(ilmTabsGlobals).toString() + "</GlobalView>";
    if (this.combinedViewList.length > 0)
      xml += "<CombinedView>" + this.removeDuplicatesFromArray(ilmTabsCombined).toString() + "</CombinedView>";
    if ( this.tabDateList.length > 0)
      xml += "<TabDate>" +  this.removeDuplicatesFromArray(this.tabDateList).toString() + "</TabDate>";
    if ( this.orderCcyList.length > 0)
      xml += "<orderCcy>" +  this.removeDuplicatesFromArray(this.orderCcyList).toString() + "</orderCcy>";
    if ( this.orderEntiyList.length > 0)
      xml += "<orderEntity>" +  this.removeDuplicatesFromArray(this.orderEntiyList).toString() + "</orderEntity>";

    xml = prettyData.pd.xml(xml);

    this.actionPath = "ilmAnalysisMonitor.do?";
    this.actionMethod = "method=saveILMOption";
    this.sendData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.requestParams['xmlConfig'] = xml;
    this.requestParams['methodName'] = (this.isSaveMethod == "true") ? "save" : "update";
    
    if(changes.length > 0)
     this.sendData.send(this.requestParams);
    else
      ExternalInterface.call('close');
  }, 0);
  }

  getFields(input, field , parentccyUndefined = false, parentEntityUndefined = false, accountGroupsOnly = false, getAllLevels=false) {
    var output = [];
    for (var i=0; i < input.length ; ++i){
      if(parentccyUndefined){
            if("Y" == input[i][field] && input[i]["currencyParent"]== undefined){
            output.push(input[i]["ccyEntityGrp"]);
          }
        }
      else if(parentEntityUndefined){
            if("Y" == input[i][field] && input[i]["entityParent"]== undefined){
            output.push(input[i]["currencyParent"] + "&&" + input[i]["ccyEntityGrp"]);
          }
        }
    else if(accountGroupsOnly){
      if("Y" == input[i][field] ){
        if(input[i]["currencyParent"] && input[i]["entityParent"] && input[i]["accountGroupId"])
          output.push(input[i]["currencyParent"] + "&&" + input[i]["entityParent"]+"&&"+input[i]["accountGroupId"])
      }
    }
    else if(getAllLevels){
      if("Y" == input[i][field] ){
        if(input[i]["currencyParent"] && input[i]["entityParent"] && input[i]["accountGroupId"])
          output.push(input[i]["currencyParent"] + "&&" + input[i]["entityParent"]+"&&"+input[i]["accountGroupId"])
        else if(input[i]["currencyParent"] && input[i]["ccyEntityGrp"] )
          output.push(input[i]["currencyParent"] + "&&" + input[i]["ccyEntityGrp"]);
    }

    } else {
            if("Y" == input[i][field] ){
              if(input[i]["currencyParent"] && input[i]["ccyEntityGrp"])
            output.push(input[i]["currencyParent"] + "&&" + input[i]["ccyEntityGrp"]);
          }
        }

    }
    return output;
  }

  removeDuplicatesFromArray(a) {
    return Array.from(new Set(a));
 }
  sendDataResult(event) {
    if (this.sendData.isBusy())
      this.sendData.cbStop();
    else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        // this.swtAlert.warning('Options are saved')
        
        // if (this.ilmTabEntityList.length > 0) {
          if (window.opener && window.opener.Main){
            // window.opener.Main.createTabs(this.ilmTabEntityList);
            // window.opener.Main.refreshTabList();
            window.opener.Main.updateData('refreshButton');
          }
        // }
        setTimeout(() => {
          this.closeHandler();
        }, 0);
      }
    }
  }

   arrayRemove(arr, value)
   { return arr.filter(function(ele){
     return ele != value; });
   }

  cancelHandler() {
    let changes = this.optionGrid.changes.getValues();
    if(changes.length > 0){
      const  message = SwtUtil.getPredictMessage('general.closeConfirm', null); 
      this.swtAlert.confirm(message, "", Alert.YES |  Alert.CANCEL, null, this.confirmClose.bind(this));
    }else {
      this.closeHandler();
    }
    
  }


  confirmClose(event){
    if (event.detail === Alert.YES) {
      this.closeHandler();
    }
  }


  orderHandler() {

    this.optionGrid.angularGridInstance.sortService.updateSorting([
      // orders matter, whichever is first in array will be the first sorted column
      { columnId: 'order', direction: 'ASC' },
    ]);

    this.optionGrid.sortTreeGrid();

  }

  closeHandler() {
    ExternalInterface.call('close')
  }
  report(type) : void {
    this.exportContainer.convertData(this.lastRecievedJSON.ILMOptions.grid.metadata.columns , this.optionGrid, null , null,type, false);
  }
   arraysEqual(_arr1, _arr2) {
    if (
      !Array.isArray(_arr1)
      || !Array.isArray(_arr2)
      || _arr1.length !== _arr2.length
    ) {
      return false;
    }

    const arr1 = _arr1.concat().sort();
    const arr2 = _arr2.concat().sort();

    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i]) {
        return false;
      }
    }

    return true;
  }
}

//Define lazy loading routes
const routes: Routes = [
  {path: '', component: Options}
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [Options],
  entryComponents: []
})
export class OptionsModule {
}
