<SwtModule (creationComplete)='onLoad()'  width="100%" height="100%">
  <VBox  width="100%" height="100%" paddingLeft="10" paddingRight="10" paddingBottom="10" paddingTop="10">
    <SwtCanvas id="canvasGrid"
               #canvasGrid
               width="100%"
               height="93%">
    </SwtCanvas>
    <SwtCanvas width="100%" height="6%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true"
                   id="addButton"
                   #addButton
                   (click)="doAddCategory($event)"
                   (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                   id="changeButton"
                   #changeButton
                   (click)="doChangeCategory($event)"
                   (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                   id="viewButton"
                   #viewButton
                   (click)="doViewCategory($event)"
                   (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                   id="deleteButton"
                   #deleteButton
                   (click)="doDeleteCategory($event)"
                   (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                   id="closeButton"
                   #closeButton
                   (click)="closeCurrentTab($event)"
                   (keyDown)="keyDownEventHandler($event)">
        </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="5">
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
        <SwtHelpButton id="helpIcon"
                       [buttonMode]="true"
                       enabled="true"
                       (click)="doHelp()">
        </SwtHelpButton>
      </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
