import { PromtParamsPopup } from './PromtParamsPopup/PromtParamsPopup';
import { Component, ElementRef, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Alert, CommonService, ExternalInterface, HTTPComms, JSONReader, StringUtils, SwtAlert, SwtButton, SwtCheckBox, SwtComboBox, SwtLabel, SwtModule, SwtMultiselectCombobox, SwtPopUpManager, SwtTextArea, SwtTextInput, SwtUtil, TitleWindow, SwtImage } from 'swt-tool-box';
declare var instanceElement3: any;
declare var require: any;
const $ = require('jquery');
@Component({
  selector: 'app-identifaction',
  templateUrl: './Identification.html',
  styleUrls: ['./Identification.css'],
  encapsulation: ViewEncapsulation.None
})
export class Identification extends SwtModule implements OnInit {

  @ViewChild('hostColLabel') hostColLabel: SwtLabel;
  @ViewChild('entityColLbl') entityColLbl: SwtLabel;
  @ViewChild('ccyColLbl') ccyColLbl: SwtLabel;
  @ViewChild('amounColtLbl') amounColtLbl: SwtLabel;
  @ViewChild('defGroupLbl') defGroupLbl: SwtLabel;
  @ViewChild('genericLbl') genericLbl: SwtLabel;
  @ViewChild('facilityLbl') facilityLbl: SwtLabel;
  @ViewChild('selectedFacility') selectedFacility: SwtLabel;
  @ViewChild('refTableLbl') refTableLbl: SwtLabel;
  @ViewChild('refTableTxt') refTableTxt: SwtLabel;
  @ViewChild('reqRefLbl') reqRefLbl: SwtLabel;
  @ViewChild('reqRefTxt') reqRefTxt: SwtLabel;
  @ViewChild('refColumnLbl') refColumnLbl: SwtLabel;
  @ViewChild('facilityParamLbl') facilityParamLbl: SwtLabel;
  @ViewChild('facilityParamTxt') facilityParamTxt: SwtLabel;
  @ViewChild('paramValueLbl') paramValueLbl: SwtLabel;


  @ViewChild('refColumnCombo') refColumnCombo: SwtMultiselectCombobox;
  @ViewChild('paramValueTxt') paramValueTxt: SwtTextInput;

  @ViewChild('hostColCombo') hostColCombo: SwtComboBox;
  @ViewChild('entityColCombo') entityColCombo: SwtComboBox;
  @ViewChild('ccyColCombo') ccyColCombo: SwtComboBox;
  @ViewChild('amountColCombo') amountColCombo: SwtComboBox;
  @ViewChild('defGroupCombo') defGroupCombo: SwtComboBox;
  @ViewChild('facilityCombo') facilityCombo: SwtComboBox;

  @ViewChild('baseQuery') baseQuery: SwtTextArea;

  @ViewChild('testButton') testButton: SwtButton;

  @ViewChild('genericCheck') genericCheck: SwtCheckBox;

  @ViewChild('BaseQueryLockImg') BaseQueryLockImg: SwtImage;
  @ViewChild('hostLockImg') hostLockImg: SwtImage;
  @ViewChild('entityLockImg') entityLockImg: SwtImage;
  @ViewChild('ccyLockImg') ccyLockImg: SwtImage;
  @ViewChild('amountLockImg') amountLockImg: SwtImage;

  private swtalert: SwtAlert;
  private requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionPath = "";
  private actionMethod = "";
  private inputData: HTTPComms = new HTTPComms(this.commonService);
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  private noneLabel: string ;
  public queryColumnsArray = [];
  public queryColumns= [];
  private updateDataFlag=false;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }
  public static ngOnDestroy(): any {
    instanceElement3 = null;
  }

  ngOnInit() {
    instanceElement3 = this;
    this.hostColLabel.text = SwtUtil.getPredictMessage("scenarioAdvanced.hostColumn", null);
    this.entityColLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.entityColumn", null);
    this.ccyColLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.currencyColumn", null);
    this.amounColtLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.amountColumn", null);
    this.defGroupLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.defaultGrouping", null);
    this.genericLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.useGenericDisplay", null);
    this.facilityLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.facilityID", null);
    this.refTableLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.refTable", null);
    this.reqRefLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.reqRefCols", null);
    this.refColumnLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.refColumns", null);
    this.facilityParamLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.FacilityParams", null);
    this.paramValueLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.parameterValues", null);
    this.testButton.label = SwtUtil.getPredictMessage("button.test", null);
    /**Toolitps***/
    this.baseQuery.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioQueryText", null);
    this.hostColCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioHostColumn", null);
    this.ccyColCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioCurrencyColmun", null);
    this.entityColCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioEntityColumn", null);
    this.amountColCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioAmountColumn", null);
    this.defGroupCombo.toolTip = SwtUtil.getPredictMessage("tooltip.selectGrouping", null);
    this.genericCheck.toolTip = SwtUtil.getPredictMessage("tooltip.selectUseGeneric", null);
    this.facilityCombo.toolTip = SwtUtil.getPredictMessage("tooltip.selectFacility", null);
    this.refColumnCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioFacilityReferenceColumn", null);
    this.paramValueTxt.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioFacilityParameter", null);
    this.testButton.toolTip = SwtUtil.getPredictMessage("tooltip.test", null);
    this.refColumnCombo.placeholder="Select columns";
    /**MultiSelectCombo item limit */
    this.refColumnCombo.itemLimit=3;

  }

  onLoad() {
    //this.fillFacilityProperties();
    this.refColumnCombo.DESELECT_ALL.subscribe((target) => {
      this.refColumnCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioFacilityReferenceColumn", null);
    });

    this.refColumnCombo.ITEM_DESELECT.subscribe((target) => {
      if(!target.tooltip.toString())
      this.refColumnCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioFacilityReferenceColumn", null);
    });

  }

  fillFacilityProperties() {
    this.noneLabel = ExternalInterface.call('eval', 'noneLabel');
    this.selectedFacility.text = (this.facilityCombo.selectedValue) ? this.facilityCombo.selectedValue : "";
    if (this.facilityCombo.selectedLabel == this.noneLabel){
      this.refTableTxt.text = this.noneLabel;
      this.reqRefTxt.text = this.noneLabel;
      this.facilityParamTxt.text = this.noneLabel;
      this.refColumnCombo.isDropdownDisabled= true;
      this.paramValueTxt.enabled = false;
    }else {
      this.requestParams = [];
      this.actionPath = 'scenMaintenance.do?';
      this.actionMethod = 'method=getFacilityIdProperties';
      this.requestParams["facilityId"] = this.facilityCombo.selectedItem.content;
      this.inputData.cbResult = (event) => {
        this.getFacilityProp(event);
      };
      this.inputData.cbStart = this.parentDocument.startOfComms.bind(this.parentDocument);
      this.inputData.cbStop = this.parentDocument.endOfComms.bind(this.parentDocument);
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      //this.testButton.enabled = false;
    }
  }

  getFacilityProp(event) {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.testButton.enabled = this.parentDocument.methodName != "view" ;//&& !this.parentDocument.systemCheck.selected;
      this.lastRecievedJSON = event;
      if (this.lastRecievedJSON == null) {
        this.refTableTxt.text = "None";
        this.reqRefTxt.text = "None";
        this.facilityParamTxt.text = "None";
      } else {
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        let result = this.jsonReader.getSingletons().facilityProperties;
        let refTable = result.split("$#$")[0];
        let refColumn = result.split("$#$")[1];
        let refParams= result.split("$#$")[2];
        this.refTableTxt.text = (refTable != "null"  && refTable!="" ) ? refTable: "None";
        this.reqRefTxt.text = (refColumn != "null" && refColumn!="") ? refColumn: "None";
        this.facilityParamTxt.text = (refParams!= "null" && refParams!="") ? refParams: "None";
        if(refColumn== "null" || refColumn=="" ) {
          this.refColumnCombo.defaultSelectedItems=[];
          //this.alertInstanceColumnCombo.defaultSelectedItems=[];
        }else{
          this.refColumnCombo.defaultSelectedItems=this.parentDocument.defaultVal;
          //this.alertInstanceColumnCombo.defaultSelectedItems=this.parentDocument.defaultVal;
        }
          this.refColumnCombo.isDropdownDisabled = !((refColumn != "null" && refColumn!="") && !this.parentDocument.systemCheck.selected && this.parentDocument.methodName != "view");
          //this.alertInstanceColumnCombo.isDropdownDisabled = !((refColumn != "null" && refColumn!="") && !this.parentDocument.systemCheck.selected && this.parentDocument.methodName != "view");
          this.paramValueTxt.enabled = ((refParams != "null" && refParams!="") && !this.parentDocument.systemCheck.selected && this.parentDocument.methodName != "view");
      }

    }

  }



  updateFacilityList() {
    this.requestParams = [];
    this.actionPath = 'scenMaintenance.do?';
    this.actionMethod = 'method=updateFacilityList';
    this.requestParams["genericDisplaySelected"] = this.genericCheck.selected;
    this.inputData.cbResult = (event) => {
      this.updateFacilityListResult(event);
    };
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }
  updateFacilityListResult(event) {

    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.facilityCombo.setComboData(this.jsonReader.getSelects());
      this.selectedFacility.text = (this.facilityCombo.selectedValue) ? this.facilityCombo.selectedValue : "";
      this.refTableTxt.text = "None";
      this.reqRefTxt.text = "None";
      this.facilityParamTxt.text = "None";
      this.refColumnCombo.defaultSelectedItems=[];
      this.refColumnCombo.isDropdownDisabled=true;
      //this.alertInstanceColumnCombo.defaultSelectedItems=[];
      this.paramValueTxt.text = "";
    }

  }

  onHostColumnChange(comboCol:string, limitField) {
    //update instance alert combo dataprovider
    if (limitField.selectedLabel) {
      this.parentDocument.updateAlertInstProvider('"HOST_ID"');
    } else {
      //we need also reset entity and currency combo values
      this.entityColCombo.selectedLabel = "";
      this.ccyColCombo.selectedLabel = "";
      this.defGroupCombo.selectedValue = "N";
      //call parent deleteColFromTreeProvider method to delete entity id and currency code from provider array
      this.parentDocument.deleteColFromTreeProvider('"ENTITY_ID"', 'entityCol');
      this.parentDocument.deleteColFromTreeProvider('"CURRENCY_CODE"', 'ccyCol');
      this.parentDocument.deleteColFromTreeProvider('"ACCOUNT_ID"', 'acctId');
      this.parentDocument.deleteColFromAlertInstProvider('"HOST_ID"', comboCol);
      //delete entity and currency options as they are automatically  reset when we reset host combo
      this.parentDocument.deleteColFromAlertInstProvider('"ENTITY_ID"', 'entityCol');
      this.parentDocument.deleteColFromAlertInstProvider('"CURRENCY_CODE"', 'ccyCol');

    }
    this.entityColCombo.enabled = this.checkEmpty(limitField);
    this.ccyColCombo.enabled = this.checkEmpty(this.entityColCombo);
    this.parentDocument.refreshGridGuiHighlight();
  }

  onEntityColumnChange(comboCol:string, limitField) {
    //update treeBreakDown combo dataprovider
    if (limitField.selectedLabel) {
      this.parentDocument.updateTreeProvider('"ENTITY_ID"', comboCol);
      this.parentDocument.updateAlertInstProvider('"ENTITY_ID"');
    } else {
      this.ccyColCombo.selectedLabel = "";
      this.parentDocument.deleteColFromTreeProvider('"ENTITY_ID"', comboCol);
      this.parentDocument.deleteColFromTreeProvider('"CURRENCY_CODE"', 'ccyCol');
      this.parentDocument.deleteColFromAlertInstProvider('"ENTITY_ID"', comboCol);
      //delete  currency option as it is automatically  reset when we reset host combo
      this.parentDocument.deleteColFromAlertInstProvider('"CURRENCY_CODE"', 'ccyCol');

    }
    this.ccyColCombo.enabled =  this.checkEmpty(limitField);
    /*this.defGroupCombo.disableItem(0);
    this.defGroupCombo.enableItem(1);
    this.defGroupCombo.disableItem(2);*/
    if(this.ccyColCombo.selectedValue){
      this.defGroupCombo.selectedLabel = "Currency";
      this.parentDocument.checkSummaryGrouping();
    }
    else if(this.entityColCombo.selectedValue){
      this.defGroupCombo.selectedLabel = "Entity";
      this.parentDocument.checkSummaryGrouping();

    }else{
      this.defGroupCombo.selectedLabel = "None";
      this.parentDocument.checkSummaryGrouping();

    }
    this.parentDocument.refreshGridGuiHighlight();
  }

  onCurrencyColumnChange(comboCol:string, limitField) {
    //update treeBreakDown combo dataprovider
    if (limitField.selectedLabel) {
      this.parentDocument.updateTreeProvider('"CURRENCY_CODE"', comboCol);
      this.parentDocument.updateAlertInstProvider('"CURRENCY_CODE"');
    } else {
      this.parentDocument.deleteColFromTreeProvider('"CURRENCY_CODE"', comboCol);
      this.parentDocument.deleteColFromAlertInstProvider('"CURRENCY_CODE"', comboCol);

    }   

   if(!limitField.selectedValue) {
    // this.defGroupCombo.disableItem(2);
     this.defGroupCombo.selectedLabel = "Entity";
     this.parentDocument.checkSummaryGrouping();

   } else{
     this.defGroupCombo.selectedLabel = "Currency";
     this.parentDocument.checkSummaryGrouping();

   }
   this.parentDocument.refreshGridGuiHighlight();
  }

  onAmountColumnChange(comboCol: string, limitField) {
    //update alert instance combo dataprovider
    if (limitField.selectedLabel) {
      this.parentDocument.updateAlertInstProvider('"AMOUNT"');
    } else {
      this.parentDocument.deleteColFromAlertInstProvider('"AMOUNT"', comboCol);
    }
    this.parentDocument.refreshGridGuiHighlight();
  }

  checkDefGroup(){
    this.parentDocument.checkSummaryGrouping();
  }

  //Retrieve columns from the query set by the user
  getQueryColumns() {
    if (this.updateDataFlag) {
    this.requestParams = [];
    this.saveDataBeforeUpdate();
    this.actionPath = 'scenMaintenance.do?';
    this.actionMethod = 'method=getQueryColumns';
    this.requestParams["baseQuery"] = StringUtils.encode64(this.lastUsedQuery);
    this.inputData.cbResult = (event) => {
      this.populateComboValues(event);
    };
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }
  }

  saveDataBeforeUpdate(){
    this.parentDocument.saveComboValues();
  }

  //Set HOST, ENTITY, CURRENCY, AMOUNT combo values
  populateComboValues(event){
  this.queryColumns  = [];
  this.queryColumns = event.scenarioDetails.selects.select[0].option;
  if (!this.queryColumns.length)
  this.queryColumns = [this.queryColumns];
  if(this.queryColumns){
  this.queryColumns.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content  
  this.queryColumns.unshift({type: "", value: '', selected: 0, content: ""});
}

  let numberValues = (event && event.scenarioDetails && event.scenarioDetails.selects && 
    event.scenarioDetails.selects.select && event.scenarioDetails.selects.select[1] && 
    event.scenarioDetails.selects.select[1].option) || [];

  let txtValues = (event && event.scenarioDetails && event.scenarioDetails.selects && 
  event.scenarioDetails.selects.select && event.scenarioDetails.selects.select[2] && 
  event.scenarioDetails.selects.select[2].option) || [];

  let dateValues = (event && event.scenarioDetails && event.scenarioDetails.selects && 
  event.scenarioDetails.selects.select && event.scenarioDetails.selects.select[3] && 
  event.scenarioDetails.selects.select[3].option) || [];

if (!numberValues.length)
  numberValues = [numberValues];

  if(numberValues){
  numberValues.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
  numberValues.unshift({type: "", value: '', selected: 0, content: ""});
}

  if (!txtValues.length)
  txtValues = [txtValues];

  if(txtValues){
  txtValues.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content  
  txtValues.unshift({type: "", value: '', selected: 0, content: ""});
}

  if (!dateValues.length)
  dateValues = [dateValues];

  if(dateValues){
  dateValues.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content  
  dateValues.unshift({type: "", value: '', selected: 0, content: ""});
}

  txtValues = Array.isArray(txtValues) ? txtValues : [txtValues];
  numberValues = Array.isArray(numberValues) ? numberValues : [numberValues];
  dateValues = Array.isArray(dateValues) ? dateValues : [dateValues];
  
  const emptyValue = {type: "", value: '', selected: 0, content: ""};

  if (!txtValues.length || txtValues[0].content !== "") {
    txtValues.unshift(emptyValue);
}
if (!numberValues.length || numberValues[0].content !== "") {
    numberValues.unshift(emptyValue);
}
if (!dateValues.length || dateValues[0].content !== "") {
    dateValues.unshift(emptyValue);
}


  this.hostColCombo.setComboData(txtValues);
  this.hostColCombo.dataProvider = txtValues;
  this.hostColCombo.selectedLabel= this.parentDocument.savedHostId;
  this.entityColCombo.setComboData(txtValues);
  this.entityColCombo.dataProvider = txtValues;
  this.entityColCombo.selectedLabel= this.parentDocument.savedEntityId;
  this.ccyColCombo.setComboData(txtValues);
  this.ccyColCombo.dataProvider = txtValues;
  this.ccyColCombo.selectedLabel= this.parentDocument.savedCcyId;
  this.amountColCombo.setComboData(numberValues);
  this.amountColCombo.dataProvider = numberValues;
  this.amountColCombo.selectedLabel= this.parentDocument.savedAmount;
  const copyData = $.extend(true, [], this.queryColumns);
  copyData.splice(0, 1);
  this.refColumnCombo.dataProvider=copyData;
  this.refColumnCombo.defaultSelectedItems=this.parentDocument.savedIdentRefCols;
  //fill instance tab comboBoxes
  this.parentDocument.fillInstanceCombo(event.scenarioDetails.selects);
    //fill gui highlighting tab comboBoxes
  this.parentDocument.fillGuiHighlightData(event.scenarioDetails.selects);
  //fill sub event grid combo
  this.parentDocument.gridComboVal=this.queryColumns;
  this.parentDocument.gridComboTxtVal=txtValues;
  this.parentDocument.gridComboNbrVal=numberValues;
  this.parentDocument.gridComboDateVal=dateValues;
  //fill event Tab
  this.parentDocument.fillEventRefColsCombo(event.scenarioDetails.selects);
  //clear other id type combo description
  //this.parentDocument.clearTypeDesc();
    // check entity and currency combo status in case of change action
    if (this.parentDocument.methodName == "change") {
    this.onHostColumnChange('hostCol', this.hostColCombo);
    this.onEntityColumnChange('entityCol',this.entityColCombo); 
    }
  }

  getQueryResult(event) {
    let pageSize = ExternalInterface.call('eval', 'pageSize');
    let onlyOneRecord = false;

    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      if(this.lastRecievedJSON) {
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        let recordDetailsAsString = this.jsonReader.getSingletons().result;
        if(recordDetailsAsString ) {
          let recordDetailsAsArray = recordDetailsAsString.split('$#$');
          let  noRecords = recordDetailsAsArray[0];
          let timeLeft =  recordDetailsAsArray[1];
          let exception = recordDetailsAsArray[2];
          //let totaltime = recordDetailsAsArray[3];
          if (noRecords<pageSize){
            pageSize=noRecords;
            if(noRecords==1)
              onlyOneRecord = true;
          }
          if (noRecords=='-1' || isNaN(noRecords)) {
            if(typeof exception != 'undefined' && exception != null){
              this.swtalert.show(SwtUtil.getPredictMessage('scenario.testQuery.error', null) + exception);
            }else
              this.swtalert.show(SwtUtil.getPredictMessage('label.errorContactSystemAdmin', null));

          }
          else if (noRecords=='0'){
            this.swtalert.show(SwtUtil.getPredictMessage('alert.scenarioQueryWasTestedSuccessfully', null)+timeLeft+" "+ SwtUtil.getPredictMessage('label.milliseconds', null));
            this.getQueryColumns();
          }
          else{
            // in case of success get columns from the query entered by the user
            this.swtalert.show(SwtUtil.getPredictMessage('alert.scenarioQueryTested', null)+" "+pageSize+" "+
              (onlyOneRecord? SwtUtil.getPredictMessage('label.record', null): SwtUtil.getPredictMessage('label.records', null))+
              " "+SwtUtil.getPredictMessage('label.fromTotalOf', null)+" "+noRecords+" "+ SwtUtil.getPredictMessage('label.in', null)+
              " "+timeLeft+" "+SwtUtil.getPredictMessage('label.milliseconds', null)+ SwtUtil.getPredictMessage('alert.scenarioQueryTested2', null), null, Alert.OK | Alert.CANCEL, null, this.goToDisplay.bind(this) );
            this.getQueryColumns();
          }
        }
      } else  {
        this.swtalert.show(SwtUtil.getPredictMessage('scenario.testQueryEmpty.error', null))
      }



    }
  }
   buildGenericDisplayURL(){
     let param = 'genericdisplay.do?method=genericDisplay';
     param+='&scenarioID='+this.parentDocument.scenarioIdTxt.text;
     param+='&scenarioTitle='+this.parentDocument.titleTxt.text;
     param+='&facilityID='+this.facilityCombo.selectedLabel;
     param+='&facilityName='+((this.facilityCombo.selectedValue) ? this.facilityCombo.selectedValue : "");
     param+='&refColumns='+JSON.stringify(this.refColumnCombo.selects);
     param+='&facilityRefColumns='+ this.reqRefTxt.text;
     param+='&refParams='+ StringUtils.encode64(this.paramValueTxt.text);
     param+='&facilityRefParams='+ StringUtils.encode64(this.facilityParamTxt.text);
     param+='&basequery='+StringUtils.encode64(this.lastUsedQuery);
   ExternalInterface.call('openGenericDisplay', param);

  }
  goToDisplay(event) {
    if(event.detail == Alert.OK) {
      this.buildGenericDisplayURL();
    }
  }
  private win: TitleWindow;
  private lastUsedQuery :string;
  sendQueryTest() {

    if(this.parentDocument.getIsScheduledSelected() && !this.pramsToken && this.baseQuery.text && this.baseQuery.text.indexOf("{")>=0){
        var found = [],          // an array to collect the strings that are found
        rxp = /p{([^}]+)}/gi,
        // str = "a {string} with {curly} braces",
        curMatch;
    
      while( curMatch = rxp.exec( this.baseQuery.text ) ) {
        if(found.indexOf(curMatch[1]) == -1)
          found.push( curMatch[1] );
        }

        try {
          this.win = SwtPopUpManager.createPopUp(this, PromtParamsPopup, {
            title: "Prompt Params Popup",
            paramList: found,
            lastUsedParams:this.paramResult
          });
          this.win.width = '500';
          this.win.id = "PromptParams";
          this.win.showControls = true;
          this.win.enableResize = false;
          this.win.isModal = true;
          this.win.display();
        } catch (error) {
        SwtUtil.logError(error, 'Predict',"Scenario Maintenance"  , "rateHandler", 0);
      }
      return;

      
    }
    let newQueryWithPrams:string =this.baseQuery.text;
    if(this.pramsToken){
      for (var key in this.paramResult) {
        if (this.paramResult.hasOwnProperty(key)) {
          newQueryWithPrams=  this.replaceAll(newQueryWithPrams,"P{"+key+"}" ,"'"+this.paramResult[key]+"'" )

        }
      }
      
      this.pramsToken=false;

    }

    this.lastUsedQuery = newQueryWithPrams;
    
    this.requestParams = [];
      this.actionPath = 'genericdisplay.do?';
      this.actionMethod = 'method=testBaseQuery';
      this.requestParams["baseQuery"] = StringUtils.encode64(newQueryWithPrams);
      this.inputData.cbResult = (event) => {
        this.getQueryResult(event);
      };
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.inputData.cbStart = this.parentDocument.startOfComms.bind(this.parentDocument);
      this.inputData.cbStop = this.parentDocument.endOfComms.bind(this.parentDocument);
  }
  private pramsToken = false;
  private paramResult = [];
  saveParamListAndExecute(paramList){
    this.paramResult = paramList;
    this.pramsToken=true;
    this.sendQueryTest();
  }


  replaceAll(string, search, replace) {
    return string.split(search).join(replace);
  }



  updateChangeDataFlag() {
    if ((this.baseQuery.text).trimEnd() != (this.parentDocument.savedQuery).trimEnd() ) {
      this.updateDataFlag = true;
    } else {
      this.updateDataFlag = false;
    }
  }

  sendColumns(columns){
    let row = {};
    this.queryColumnsArray = [];
    for(let i =0; i <columns.columns.column.length; i++) {
      row = {"value":columns.columns.column[i].heading, "content": columns.columns.column[i].heading };
      this.queryColumnsArray.push(row)
    }
    /*this.defGroupCombo.dataProvider = this.queryColumnsArray;
    this.defGroupCombo.selectedLabel = this.queryColumnsArray.filter(function(ele){
      return ele.content.toString().toLowerCase().indexOf("entity") != -1})[0].content*/
  }

  checkEmpty(combo) {
    if (combo.selectedLabel) {
      return true;
    } else {
      return false;
    }
  }

  changeTxtAreaStatus(){
    if(this.BaseQueryLockImg.source==this.baseURL + this.parentDocument.lockIcon){
    this.baseQuery.editable=true;
    this.BaseQueryLockImg.source =this.baseURL + this.parentDocument.unlockIcon;
    }else{
    this.baseQuery.editable=false;
    this.BaseQueryLockImg.source =this.baseURL + this.parentDocument.lockIcon; 
    }
  }

  changeComboStatus(combo, comboImage){
    if(comboImage.source==this.baseURL + this.parentDocument.lockIcon){
    combo.enabled=true;
    comboImage.source =this.baseURL + this.parentDocument.unlockIcon;
    }else{
    combo.enabled=false;
    comboImage.source =this.baseURL + this.parentDocument.lockIcon; 
    }
  }

}
