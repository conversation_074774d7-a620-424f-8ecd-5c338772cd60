import { StringUtils } from 'swt-tool-box';
import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService, ExternalInterface,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas, SwtComboBox, SwtCommonGrid,
  SwtLabel,
  SwtLoadingImage, SwtModule, SwtPopUpManager, SwtUtil, TitleWindow,  SwtToolBoxModule,
} from 'swt-tool-box';
import {FourEyesProcess} from "../../FourEyesProcess/FourEyesProcess";
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";
declare var instanceElement: any;
@Component({
  selector: 'app-pcaccount-groups',
  templateUrl: './AccountGroups.html',
  styleUrls: ['./AccountGroups.css']
})
export class AccountGroups extends  SwtModule {

  @ViewChild('accountGroupsCanvas') accountGroupsCanvas: SwtCanvas;

  /********SwtButton*********************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  /*********LodingImage*************/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /**********SwtCombo***********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  /*******Swtlabel*********/
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public checkAuthData = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;
  private swtAlert: SwtAlert;
  private accountGroupsGrid: SwtCommonGrid;
  public menuAccessId: number = 2;
  private win: TitleWindow ;
  private idOrderBeforeMove: Map<number, string> = new Map<number, string>();
  public screenNameWindow : string;

  
  private requireAuthorisation = true;
  private doDeleterecordAction = false;
  private faciltiyId = null;

  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  public static ngOnDestroy(): any {
    instanceElement = null;
  }
  ngOnInit(): void {
    instanceElement = this;
  }
  disableButtons() {
    if(this.accountGroupsGrid.selectedIndex == -1) {
      this.enableChangeButton(false);
      this.enableDeleteButton(false);
      this.enableViewButton(false);
    }

  }
  onLoad() {
    this.requestParams = [];
    this.accountGroupsGrid = <SwtCommonGrid> this.accountGroupsCanvas.addChild(SwtCommonGrid);
    this.accountGroupsGrid.onFilterChanged = this.disableButtons.bind(this);
    this.accountGroupsGrid.onSortChanged = this.disableButtons.bind(this);
    this.accountGroupsGrid.uniqueColumn = "AccGpId";
    this.enableChangeButton(false);
    this.enableDeleteButton(false);
    this.enableViewButton(false);

    try {
      this.actionMethod = 'method=display';
      this.actionPath = 'accountGroupsPCM.do?';
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      //this.requestParams['method']= 'display';
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url =  this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.accountGroupsGrid.onRowClick = (event) => {
        this.checkIfMaintenanceEventExist(event);
      };
      this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
      this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
      this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
      this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
      this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
      this.addButton.toolTip = SwtUtil.getPredictMessage('acctGroupsMaintenance.tooltip.add', null);
      this.changeButton.toolTip = SwtUtil.getPredictMessage('acctGroupsMaintenance.tooltip.change', null);
      this.deleteButton.toolTip = SwtUtil.getPredictMessage('acctGroupsMaintenance.tooltip.delete', null);
      this.viewButton.toolTip = SwtUtil.getPredictMessage('acctGroupsMaintenance.tooltip.view', null);
      this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    } catch (e) {
      console.log('errr', e);
    }
  }
  updateDataFromChild(): void {
    try {
      this.requestParams =[];
      this.actionMethod = 'method=display';
      this.doDeleterecordAction =false;
      this.actionPath = 'accountGroupsPCM.do?';
      //this.requestParams['method']= 'display';
      this.inputData.url =  this.baseURL +  this.actionPath +  this.actionMethod;
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
        this.accountGroupsGrid.refresh();
        this.accountGroupsGrid.refreshFilters();
      };
      this.accountGroupsGrid.selectedIndex = -1;
      this.enableChangeButton(false);
      this.enableDeleteButton(false);
      this.enableViewButton(false);
      this.inputData.send( this.requestParams);
    } catch (e) {
      // log the error in ERROR LOG
      //SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'ClassName', 'updateData',  this.errorLocation);
    }
  }


  
  public checkResult(data): void {

    try {
      if (this.checkAuthData && this.checkAuthData.isBusy()) {
        this.checkAuthData.cbStop();
      } else {
        this.jsonReader.setInputJSON(data);

          
        if (this.jsonReader.getRequestReplyMessage() == "RECOD_EXIST") {
          const message = SwtUtil.getPredictMessage('maintenanceEvent.alert.cannotBeAmended', null);
          this.swtAlert.error(message);
          this.cellClickEventHandler(true);
        }  else {
          this.cellClickEventHandler(false);
        }

      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }



  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  public inputDataResult(data): void {

    try {
      if (this.inputData && this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = data;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {

          
        if (this.jsonReader.getRequestReplyStatus()) {

          if(this.doDeleterecordAction){
            if(StringUtils.isTrue(this.requireAuthorisation))
              this.swtAlert.show("This action needs second user authorisation", "Warning", Alert.OK);
            this.doDeleterecordAction = false;
          }

          if (!this.jsonReader.isDataBuilding()) {
            const obj = {columns: this.jsonReader.getColumnData()};
            this.accountGroupsGrid.CustomGrid(obj);
            if (this.jsonReader.getGridData().size > 0) {
              this.accountGroupsGrid.gridData = this.jsonReader.getGridData();
              this.accountGroupsGrid.setRowSize = this.jsonReader.getRowSize();

              for (let i = 0; i < this.accountGroupsGrid.dataProvider.length; i++) {
                this.idOrderBeforeMove.set(this.accountGroupsGrid.dataProvider[i].ordinal, this.accountGroupsGrid.dataProvider[i].AccGpId);

              }
            } else {
              this.accountGroupsGrid.dataProvider = [];
              this.accountGroupsGrid.selectedIndex = -1;
            }
            /***Enable this code when getting data from server side*************/
            this.menuAccessId = this.jsonReader.getScreenAttributes()["menuaccess"];
            this.requireAuthorisation = this.jsonReader.getScreenAttributes()["requireAuthorisation"];
            this.faciltiyId = this.jsonReader.getScreenAttributes()["faciltiyId"];
            
            if(this.menuAccessId  == 1) {
              this.enableAddButton(false);
            }

          }
          this.prevRecievedJSON = this.lastRecievedJSON;
        }
         }

      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }


  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  inputDataFault(): void {
    this.swtAlert.error('generic_exception');

  }


  doAddAccountGroups(): void {
    try {
      this.screenNameWindow = 'add';
      ExternalInterface.call('openChildWindow', 'addScreen', 'add');


    } catch (e) {
      // log the error in ERROR LOG
      console.log('error add', e);
    }

  }
  getParams(): any {
    let params = [];
    if (this.screenNameWindow == "add") {
      params = [this.screenNameWindow];
    } else {
      params = [this.screenNameWindow, this.accountGroupsGrid.selectedItem.AccGpId.content];
    }
    return params;
  }
  doChangeAccountGroups(): void {
    try {
      this.screenNameWindow = 'change';
      ExternalInterface.call('openChildWindow', 'addScreen', 'change');
     
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error add', e);
    }
  }
  doDeleteAccountGroups(): void {
    let message = SwtUtil.getPredictMessage('alert.deleteAccountGroup', null);
    Alert.yesLabel = "Yes";
    Alert.noLabel = "No";
    this.requestParams = [];
    try {
      this.swtAlert.question(message, 'Alert', Alert.YES | Alert.NO, null, this.accountGroupsRowRemove.bind(this));
    } catch (e) {
      console.log('e', e)
    }
  }
  accountGroupsRowRemove(event): void {
    if (event.detail === Alert.YES) {
      // if(this.fourEyesRequired)     {
      //   this.win =  SwtPopUpManager.createPopUp(this, FourEyesProcess, {
      //     title: SwtUtil.getPredictMessage("label.fourEyes", null),
      //   });

      //   this.win.enableResize = false;
      //   this.win.width = '510';
      //   this.win.height = '215';
      //   this.win.showControls = true;
      //   this.win.isModal = true;
      //   this.win.onClose.subscribe(() => {
      //     if (this.win.getChild().result) {
      //       if(this.win.getChild().result.login == "SUCCESS") {
      //         this.deleteAfterCheckFourEyes();
      //       }
      //     }

      //   });
      //   this.win.display();
      // }else {
        this.deleteAfterCheckFourEyes();
      // }

    }
  }
  deleteAfterCheckFourEyes(): void {
    this.doDeleterecordAction = true;
    this.actionMethod = 'method=delete';
    this.actionPath = 'accountGroupsPCM.do?';
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.inputDataResult(data);
    };
    //this.requestParams['method']= 'delete';
    this.requestParams['groupId']= this.accountGroupsGrid.selectedItem.AccGpId.content;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.inputData.url =  this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.accountGroupsGrid.selectedIndex = -1;
    this.enableChangeButton(false);
    this.enableDeleteButton(false);
    this.enableViewButton(false);
  }
  doDispalyAccountGroups(): void {
    try {
      this.screenNameWindow = 'view';
      ExternalInterface.call('openChildWindow', 'viewScreen', 'view');

    } catch (e) {
      // log the error in ERROR LOG
      console.log('error add', e);
    }
  }
  doHelp() : void {
    ExternalInterface.call('help');
  }

  closeAccountGroups(): void {
    ExternalInterface.call('close');

  }

  checkIfMaintenanceEventExist(event:Event): void {
    try {
      let actionMethod = 'method=checkIfMaintenenanceEventExist';
      let actionPath = 'maintenanceEvent.do?';
      this.checkAuthData.cbStart = this.startOfComms.bind(this);
      this.checkAuthData.cbStop = this.endOfComms.bind(this);
      this.checkAuthData.cbResult = (data) => {
        this.checkResult(data);
      };
      this.requestParams['recordId']= this.accountGroupsGrid.selectedItem.AccGpId.content;
      this.requestParams['facilityId']= this.faciltiyId;
      this.checkAuthData.cbFault = this.inputDataFault.bind(this);
      this.checkAuthData.encodeURL = false;
      this.checkAuthData.url =  this.baseURL + actionPath + actionMethod;
      this.checkAuthData.send(this.requestParams);
    } catch (e) {
      console.log(e);
      // log the error in ERROR LOG
      //SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'ClassName', 'updateData',  this.errorLocation);
    }
  }



  /**
   * cellClickEventHandler
   *
   * @param event: Event
   *
   * This method is used to maintain the button status when a row is clicked
   */
  cellClickEventHandler(maintEventExist: boolean): void {
    try {
      if(this.menuAccessId == 0) {
        this.enableAddButton(true);
        if (this.accountGroupsGrid.selectedIndex >=0) {
          if(maintEventExist){
            this.enableChangeButton(false);
            this.enableDeleteButton(false);
          }else {
            this.enableChangeButton(true);
            this.enableDeleteButton(true);
          }
          this.enableViewButton(true);
        } else {
          this.enableChangeButton(false);
          this.enableDeleteButton(false);
          this.enableViewButton(false);
        }
      } else if(this.menuAccessId  == 1) {
        if (this.accountGroupsGrid.selectedIndex >=0) {
          this.enableViewButton(true);
        }
      }

    } catch (e) {
      console.log('error event click', e);

    }
  }

  /**
   * enableAddButton
   *
   */
  enableAddButton(value: boolean): void {
    this.addButton.enabled = value;
    this.addButton.buttonMode = value;
  }
  /**
   * enableChangeButton
   *
   */
  enableChangeButton(value: boolean): void {
    this.changeButton.enabled = value;
    this.changeButton.buttonMode = value;
  }

  /**
   * enableDeleteButton
   *
   */
  enableDeleteButton(value: boolean): void {
    this.deleteButton.enabled = value;
    this.deleteButton.buttonMode = value;
  }
  enableViewButton(value: boolean): void {
    this.viewButton.enabled = value;
    this.viewButton.buttonMode = value;
  }
  /**
   * findMaxOrderToAdd
   * Method used to find the new order to add in the grid
   *
   * */
  findMaxOrderToAdd(grid: SwtCommonGrid): any
  {
    let order: number=1;
    if (grid.dataProvider) {
      for (let  item = 0; item < grid.dataProvider.length; item ++)
      {
        if(Number(grid.dataProvider[item].ordinal)>=order)
          order = Number(grid.dataProvider[item].ordinal);
      }
      order += 1;
    } else {
      order = 1
    }

    return order;
  }
}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: AccountGroups }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AccountGroups],
  entryComponents: []
})
export class AccountGroupsModule {}
