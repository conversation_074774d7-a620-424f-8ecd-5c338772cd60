
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonService, JSONReader, SwtAlert, SwtButton, SwtCheckBox, SwtLabel, SwtModule, SwtPopUpManager, SwtTextInput, SwtUtil } from 'swt-tool-box';
declare  function validateCurrencyPlaces(strField, strPat, currCode): any;
declare function validateFormatTime(strField): any;
@Component({
  selector: 'app-pcthreshold-amounts',
  templateUrl: './ThresholdAmounts.html',
  styleUrls: ['./ThresholdAmounts.css']
})
export class ThresholdAmounts extends SwtModule implements OnInit{


  @ViewChild('timeInput') timeInput: SwtTextInput;
  @ViewChild('reserveInput') reserveInput: SwtTextInput;
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('useCLCheckBox') useCLCheckBox: SwtCheckBox;
  @ViewChild('timeLabel') timeLabel: SwtLabel;
  @ViewChild('reserveLabel') reserveLabel: SwtLabel;
  @ViewChild('creditLineLabel') creditLineLabel: SwtLabel;

  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  public title: string = null;
  private swtalert: SwtAlert;
  public screenName: string;
  public dataProviderSelectedIndex: any;
  public dataProviderReserve: any;
  public currencyPattern: string;
  public currency: string;
  private originalTime: string = null;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }
ngOnInit(): void {
  this.timeLabel.text = SwtUtil.getPredictMessage('thresholdAmounts.label.time', null);
  this.timeInput.toolTip = SwtUtil.getPredictMessage('thresholdAmounts.tooltip.time', null);
  this.reserveLabel.text = SwtUtil.getPredictMessage('thresholdAmounts.label.reserve', null);
  this.reserveInput.toolTip = SwtUtil.getPredictMessage('thresholdAmounts.tooltip.reserve', null);
  this.creditLineLabel.text = SwtUtil.getPredictMessage('thresholdAmounts.label.creditLine', null);
  this.okButton.label =  SwtUtil.getPredictMessage('button.ok', null);
  this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
  this.useCLCheckBox.toolTip = SwtUtil.getPredictMessage('thresholdAmounts.tooltip.creditLine', null);
}

  onLoad() {
    if (this.screenName != 'view') {
      this.enableDisableComponent(true);
    } else {
      this.enableDisableComponent(false);
    }
    if (this.screenName != "add") {
      this.timeInput.text = this.dataProviderSelectedIndex.time.content;
      if(this.dataProviderSelectedIndex.time.previousValue)
        this.timeInput.toolTipPreviousValue=this.dataProviderSelectedIndex.time.previousValue;
      this.originalTime = this.dataProviderSelectedIndex.time.content;
      this.reserveInput.text = this.dataProviderSelectedIndex.reserve.content;
      if(this.dataProviderSelectedIndex.reserve.previousValue)
        this.reserveInput.toolTipPreviousValue=this.dataProviderSelectedIndex.reserve.previousValue;
      this.useCLCheckBox.selected = (this.dataProviderSelectedIndex.useCL.content == "Y")
      if(this.dataProviderSelectedIndex.useCL.previousValue)
        this.useCLCheckBox.toolTipPreviousValue=this.dataProviderSelectedIndex.useCL.previousValue;
    }
    this.timeInput.required = true;
    this.reserveInput.required = true;

  }


  popupClosed() {
    // this.close();
    SwtPopUpManager.getPopUpById('addReserveWindow').close();
  }

  enableDisableComponent(value: boolean): void {
    this.timeInput.enabled = value;
    this.reserveInput.enabled = value;
    this.useCLCheckBox.enabled = value

  }



  validateTime(textInput): any {
    let arrayOfTimes = [];
    let messageTime = SwtUtil.getPredictMessage('alert.validTime', null);
    let timeAlreadyExist = SwtUtil.getPredictMessage('alert.TimeAlreadyExist', null);


    for (let i =0; i < this.dataProviderReserve.length; i ++) {
      arrayOfTimes.push (this.dataProviderReserve[i].time);
    }
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text && validateFormatTime(textInput) == false) {
      //textInput.text = "";
      this.swtalert.warning(messageTime, null);
      return "false1";
    }else if(arrayOfTimes.indexOf(textInput.text) !== -1 && textInput.text !== this.originalTime) {
      this.swtalert.warning(timeAlreadyExist);
      return "false2";
    } else{
      textInput.text = textInput.text.substring(0,5);
      return true;

    }
  }
  validateReserve (): any {
    let validAmount= SwtUtil.getPredictMessage('alert.validAmount', null);
    if (!(validateCurrencyPlaces(this.reserveInput, this.currencyPattern, this.currency)) ) {
      this.swtalert.warning(validAmount);
      return false;
    }
  }
  save(): any {
    let mandatoryField = SwtUtil.getPredictMessage('alert.mandatoryField', null);
    let messageTime = SwtUtil.getPredictMessage('alert.validTime', null);
    let validAmount = SwtUtil.getPredictMessage('alert.validAmount', null);
    let timeAlreadyExist = SwtUtil.getPredictMessage('alert.TimeAlreadyExist', null);
     if (!this.timeInput.text || !this.reserveInput.text ) {
      this.swtalert.warning(mandatoryField);
   } else if(this.validateTime(this.timeInput) == "false1" ) {
     this.swtalert.warning(messageTime);
   } else if(this.validateTime(this.timeInput) == "false2" ) {
     this.swtalert.warning(timeAlreadyExist);
     return;
   }
   else if(!(validateCurrencyPlaces(this.reserveInput, this.currencyPattern, this.currency))) {
     this.swtalert.warning(validAmount);
  }
    else {
      if(this.screenName == "add") {
        this.result = {"time": this.timeInput.text, "reserve": this.reserveInput.text, "useCL": (this.useCLCheckBox.selected)? "Y" : "N", "idReserve" : Math.random().toString(36).substring(7)};
      } else {
        this.result = {"time": this.timeInput.text, "reserve": this.reserveInput.text, "useCL": (this.useCLCheckBox.selected)? "Y" : "N"};
      }

    //  this.close();
       if(this.titleWindow){
         this.close();
       }else {
         window.close();
       }
   // SwtPopUpManager.getPopUpById('addReserveWindow').close();
   }
  }
}

