<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%">
      <HBox width="100%" height="100%" paddingLeft="5" paddingRight="5">
        <VBox width="100%" height="100%" verticalGap="0">
          <HBox width="100%" height="25">
          <HBox width="100%" height="25">
            <SwtLabel id="entityLabel" #entityLabel width="120">
            </SwtLabel>
            <SwtComboBox id="entityCombo" #entityCombo dataLabel="entityList" width="135">
            </SwtComboBox>
            <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal" paddingLeft="10">
            </SwtLabel>
          </HBox>
        </HBox>
            <HBox width="100%" height="25">
              <SwtLabel id="currencyLabel" #currencyLabel width="120">
              </SwtLabel>
              <SwtComboBox id="currencyCombo" #currencyCombo dataLabel="currencyGroupList" width="135">
              </SwtComboBox>
              <SwtLabel id="selectedCurrency" #selectedCurrency fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </HBox>
            <HBox width="100%" height="25">
              <SwtLabel id="acctTypeLabel" #acctTypeLabel width="120"></SwtLabel>
              <SwtTextInput #acctTypeText id="acctTypeText" width="72"></SwtTextInput>
            </HBox>
        </VBox>
      </HBox>
    </SwtCanvas>
    <SwtCanvas id="dataGridContainer" #dataGridContainer paddingBottom="5" styleName="canvasWithGreyBorder"
      marginTop="10" border="false" width="100%" height="100%" minHeight="100"></SwtCanvas>

    <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5">
      <HBox width="100%">
        <HBox paddingLeft="5" width="50%">
          <SwtButton #viewButton width="70" id="viewButton" enabled="false" (click)="openSweepDetail()"></SwtButton>
          <SwtButton #closeButton width="70" (click)="closeHandler()" id="closeButton"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" width="50%" >
          <SwtLabel visible="false" color="red" #dataBuildingText></SwtLabel>
          <SwtLabel visible="false" color="red" #lostConnectionText></SwtLabel>
          <SwtHelpButton id="helpIcon" [buttonMode]="true" enabled="true" helpFile="spread-profile" (click)="doHelp()">
          </SwtHelpButton>
          <SwtButton [buttonMode]="true" #printButton id="printButton" styleName="printIcon" (click)="printPage()"
            (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>