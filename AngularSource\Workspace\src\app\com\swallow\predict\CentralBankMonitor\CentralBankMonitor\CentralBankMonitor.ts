
import {Component, ViewChild, ModuleWithProviders, NgModule, ElementRef} from '@angular/core';
import {
  SwtCanvas,
  SwtCommonGrid,
  CommonService,
  HTTPComms,
  SwtUtil,
  JSONReader,
  SwtLoadingImage,
  SwtComboBox,
  SwtTextInput,
  SwtDateField,
  SwtLabel,
  SwtModule,
  SwtPopUpManager,
  SwtButton,
  SwtAlert,
  ExternalInterface,
  SwtToolBoxModule,
  SwtRadioButtonGroup,
  SwtRadioItem,
  Alert,
  CommonUtil,
  SwtDataExport,
  ExportEvent,
  focusManager,
  SwtNumericInput, ContextMenuItem, JSONViewer, ScreenVersion
} from "swt-tool-box";
import {RouterModule, Routes} from "@angular/router";
import moment from "moment";
import {OptionsPopUp} from "../../../pcm/OptionsPopUp/OptionsPopUp";

@Component({
  selector: 'app-central-bank-monitor',
  templateUrl: './CentralBankMonitor.html',
  styleUrls: ['./CentralBankMonitor.css']
})
export class CentralBankMonitor extends SwtModule {
  private swtAlert: SwtAlert;
  @ViewChild('lblEntity') lblEntity: SwtLabel;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('lblCurrency') lblCurrency: SwtLabel;
  @ViewChild('currLimitText') currLimitText: SwtTextInput;
  @ViewChild('multiplier') multiplier: SwtLabel;
  @ViewChild('lblWeek') lblWeek: SwtLabel;
  @ViewChild('fromDate') fromDate: SwtDateField;
  @ViewChild('lblDays') lblDays: SwtLabel;
  @ViewChild('showDays') showDays: SwtNumericInput;
  @ViewChild('daysLabel') daysLabel: SwtLabel;
  @ViewChild('lblbreak') lblbreak: SwtLabel;
  @ViewChild('breakdown') breakdown: SwtRadioButtonGroup;
  @ViewChild('accountRadio') accountRadio: SwtRadioItem;
  @ViewChild('movementRadio') movementRadio: SwtRadioItem;
  @ViewChild('cbCanvas') cbCanvas: SwtCanvas;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('rateButton') rateButton: SwtButton;
  @ViewChild('optionButton') optionButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('dataBuildingText') dataBuildingText: SwtLabel;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
  @ViewChild('lastRefText') lastRefText: SwtLabel;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('dataExport') dataExport: SwtDataExport;
  private centralGrid: SwtCommonGrid;
  /* Data Objects
  **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private updateRefreshRate = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;
  private dateFormat: string;
  private testDate: string;

  private  d1: any;
  private  d2: any;
  private  prevChosenDate: any;
  private  prevNumberOfDays: string;
  private  entityFromDate: any;
  private  entityToDate: any;
  private  onLoadFlag=true;
  private  comboChange=false;
  private  valueBeforeEdit: string=null;
  private  valueAfterEdit: string=null;
  private  clkColumnHeader: string=null;
  private  refreshStatus="N";
  private  currencyCode: string;
  private  accountId: string;
  private interval = null;
  private  refreshRate=10;
  public win: any;
  private currentFontSize: string;
  private  versionNumber="1.1.021";
  public screenVersion  = new ScreenVersion(this.commonService);
  public showJSONPopup: any;
  private showBuildInProgress = false;
  private arrayOfDates = []
  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    window['Main'] = this;
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.centralGrid = this.cbCanvas.addChild(SwtCommonGrid) as SwtCommonGrid;
    this.centralGrid.lockedColumnCount = 1;
    this.centralGrid.editable= true;
    this.dateFormat= ExternalInterface.call('eval', 'dateFormat');
    this.testDate= ExternalInterface.call('eval', 'testDate');
    // Date format needs to be set first on the logic object
    this.fromDate.formatString=this.dateFormat;
    if (this.dateFormat == "DD/MM/YYYY") {
      this.fromDate.toolTip=ExternalInterface.call('getBundle', 'tip', 'datefromDDMMYY', 'Select From date(\'DD/MM/YYYY\')');
    } else {
      this.fromDate.toolTip=ExternalInterface.call('getBundle', 'tip', 'datefromMMDDYY', 'Select From date(\'MM/DD/YYYY\')');
    }
    // this.showDays.toolTip= ExternalInterface.call('getBundle', 'tip', 'showdays', 'Number of days to show');
    this.accountRadio.toolTip = ExternalInterface.call('getBundle', 'tip', 'acctbrkdown', 'Refresh Account Breakdown Grid');
    this.movementRadio.toolTip = ExternalInterface.call('getBundle', 'tip', 'mvmntbrkdown', 'Select Movement to view Movement Summary Detail');
   /* this.fromDate.focusOutEventOutPut.subscribe(() => {
      this.validateDateFocusOut(event);
    })*/
  }
  onLoad() {
    this.initializeMenus();
    this.requestParams= [];

    this.inputData.cbStart=this.startOfComms.bind(this);
    this.inputData.cbStop=this.endOfComms.bind(this);
    // result event
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    // fault event
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    this.actionPath="centralBankMonitor.do?";
    this.actionMethod="method=unspecified";
    this.requestParams["centralMonitor.currTimeStamp"]=ExternalInterface.call('eval', 'timeStamp');
    this.centralGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.cellLogic(selectedRowData);
    });
    this.centralGrid.enableDisableCells=(row, field) => {
      return this.enableDisableRow(row, field);
    };
    this.centralGrid.ITEM_CHANGED.subscribe((event) => {
      this.changeCurrLimit(event);
    });

    // ExternalInterface.addCallback("AutoRefreshStart", startAutoRefresh);
    // ExternalInterface.addCallback("RefreshParent", refreshParent);
    // Then apply them to the url member of the HTTPComms object:
    this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
    // Make initial request
    this.inputData.send(this.requestParams);
    ExportEvent.subscribe((type) => {
      this.report(type);
    });
  }
  private enableDisableRow(row:any, field:string):boolean {
    return (row.balance == "What If Analysis")
  }
  startOfComms(): void {
    if (this.showBuildInProgress == true) {
      this.dataBuildingText.visible = true;
    } else {
      this.dataBuildingText.visible = false;
    }
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  inputDataFault(): void {
    let message = SwtUtil.getPredictMessage('label.genericException', null);
    this.swtAlert.error(message);
  }
  /**
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   * */
   inputDataResult(event): void {
    // get the received xml
    this.lastRecievedJSON=event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    /*exportContainer.isEnabled=true;
    exportContainer.addEventListener(ExportEvent.EXPORT_CLICK, export);*/
    this.lastRefTime.text= this.jsonReader.getScreenAttributes()["lastRefTime"];
    this.lostConnectionText.visible=false;
    if (this.jsonReader.getRequestReplyStatus()) {
      if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
        this.fromDate.showToday=false;

        this.d1 = moment(this.jsonReader.getScreenAttributes()["from"], this.dateFormat.toUpperCase());
        this.prevChosenDate = this.d1;
        this.d2 = moment(this.jsonReader.getScreenAttributes()["to"], this.dateFormat.toUpperCase());
        this.fromDate.text = this.jsonReader.getScreenAttributes()["from"];
        let daysInMilliseconds: number = 1000 * 60 * 60 * 24;
        this.showDays.text = Math.round((this.d2.diff(this.d1)/daysInMilliseconds )+1);
        this.prevNumberOfDays = this.showDays.text;
        this.updateDayLabel();

        // get the entity from date
        this.entityFromDate=  moment(this.jsonReader.getScreenAttributes()["entityfromdate"], this.dateFormat.toUpperCase());
        // get the entity to date
        this.entityToDate= moment(this.jsonReader.getScreenAttributes()["entitytodate"], this.dateFormat.toUpperCase());
        let fromDateMoment = moment(this.fromDate.text, this.dateFormat.toUpperCase());
        if (this.onLoadFlag || this.comboChange) {
          let toDate =  moment(this.fromDate.text, this.dateFormat.toUpperCase()).add(Number(this.showDays.text) -1, 'days');
          this.checkDateRange(fromDateMoment, toDate);
          this.onLoadFlag=false;
          this.comboChange=false;

        }
        // set the currency Limit from received xml
        this.currLimitText.text=String(this.jsonReader.getScreenAttributes()["currencylimit"]);
        // get the currency code ...
        this.currencyCode=String(this.jsonReader.getScreenAttributes()["currencycode"]);
        // get the account id  ...
        this.accountId=String(this.jsonReader.getScreenAttributes()["accountid"]);
        // set the currency with multiplier
        this.multiplier.text=String(this.jsonReader.getScreenAttributes()["multiplier"]);
        // set the entity combobox
        this.entityCombo.setComboData(this.jsonReader.getSelects());
        // set the selected entity text
        this.selectedEntity.text=this.entityCombo.selectedValue;
        if (!this.jsonReader.isDataBuilding()) {
          // If the code has reached this point then the database is not databuilding, turn off the dataBuildingText
          this.dataBuildingText.visible=false;

          const obj = {columns: this.jsonReader.getColumnData()};
          for (let i = 1; i<this.jsonReader.getColumnData().column.length; i++) {
            this.arrayOfDates[this.jsonReader.getColumnData().column[i].dataelement] = this.jsonReader.getColumnData().column[i].date;
          }
          this.centralGrid.CustomGrid(obj);
          this.centralGrid.gridData=this.jsonReader.getGridData();
          this.centralGrid.setRowSize=this.jsonReader.getRowSize();
          this.centralGrid.entityID=this.entityCombo.selectedLabel;
          this.centralGrid.colWidthURL ( this.baseURL+"centralBankMonitor.do?");
          this.centralGrid.saveWidths = true;

          this.currentFontSize=String(this.jsonReader.getScreenAttributes()["currfontsize"]);
          // Sets the data grid style based on the font size
          if (this.currentFontSize == "Normal") {
            this.centralGrid.styleName="dataGridNormal";
            this.centralGrid.rowHeight=18;
          } else if (this.currentFontSize == "Small") {
            this.centralGrid.styleName="dataGridSmall";
            this.centralGrid.rowHeight=15;
          }
          this.showBuildInProgress = false;
        } else {
          this.dataBuildingText.visible=true;
        }
        /**AutoRefresh******************/

          // get the refresh rate
        this.refreshRate = parseInt(this.jsonReader.getRefreshRate(),10);
          // Instanitate the timer
        let timeLeft = this.refreshRate;
        clearInterval(this.interval);
        this.interval = setInterval(() => {
          this.updateData('yes', true, false);
        }, timeLeft*1000);

        this.centralGrid.rowColorFunction = ( dataContext, dataIndex, color ) => {
          return this.drawRowBackground( dataContext, dataIndex, color );
        };
        this.prevRecievedJSON=this.lastRecievedJSON;
      }
    } else {

      this.swtAlert.warning(
        this.jsonReader.getRequestReplyMessage(), // message
        ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'), Alert.OK, null,
        () => {this.closeHandler();} );
    }

  }
  drawRowBackground( dataContext, dataIndex, color ): string {

    let rColor: string;
    try {
      if (dataIndex == 11 || dataIndex == 13 || dataIndex == 15) {
        rColor = "#FBEAD8";
      }
    } catch ( error ) {

    }
    return rColor;
  }
 checkDateRange(fromDate: any, toDate: any): void {
    let fromDateFlag=false;
    let toDateFlag=false;
    // calculate days for from date in limit
    if ( fromDate.diff(this.entityFromDate) < 0 || fromDate.diff(this.entityToDate) >0 ) {
      fromDateFlag=true;
    }
    // calculate days for to date in limit
    if ( this.entityToDate.diff(toDate) <0 || toDate.diff(this.entityFromDate) <0) {
      toDateFlag=true;
    }
    // show warning message
    if (fromDateFlag && toDateFlag) {
      this.swtAlert.warning(ExternalInterface.call('getBundle', 'text', 'label-fromToOutsideRange', 'From date and To date are outside the defined range'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
      return;
    }
    if (fromDateFlag) {
      this.swtAlert.warning(ExternalInterface.call('getBundle', 'text', 'label-fromOutsideRange', 'From date is outside the defined range'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
    }
    if (toDateFlag) {
      this.swtAlert.warning(ExternalInterface.call('getBundle', 'text', 'label-ToOutsideRange', 'To date is outside the defined range'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
    }
  }

  analysisFunc(clkColumnHeader: string, valueBeforeEdit: string, valueAfterEdit: string): any {
    this.valueBeforeEdit=valueBeforeEdit;
    this.valueAfterEdit=valueAfterEdit;
    this.clkColumnHeader=clkColumnHeader;

    // call the saveMonitorDetails
    let result: string =ExternalInterface.call("saveMonitorDetails", clkColumnHeader, valueBeforeEdit, valueAfterEdit, (this.entityCombo.selectedLabel).toString(), true);
    if (result == "true") {
      return 0;
    } else {
      this.swtAlert.warning(result.toString(),ExternalInterface.call('getBundle', 'text', 'label-whatIfAnalysis', 'Central Bank Monitor - What If Analsysis') , Alert.OK | Alert.CANCEL, this.alertListener.bind(this), null);
      return 1;
    }
  }
  /**
   * Listener used to save the monitor details
   * @param eventObj:CloseEvent
   **/
  alertListener(eventObj): void {
    if (eventObj.detail == Alert.OK) {
      let result: string=ExternalInterface.call("saveMonitorDetails", this.clkColumnHeader, this.valueBeforeEdit, this.valueAfterEdit, (this.entityCombo.selectedLabel).toString(), false);
    } else {
      this.updateData();
    }
  }
 dataRefresh(): void {
    this.refreshStatus="Y";
    // Code Modified for Mantis 1600: DataBuild in progress not coming properly by Bala on 03-10-2011
    // Check on the comboOpen flag, do not want to update if there is a combobox open as this would cause it to close
   /* if (!this.comboOpen && !this.onChangeLimit && this.centralGrid != null )
    {
      if((Object(focusManager.getFocus()).id=="showDays" || Object(focusManager.getFocus()).id=="fromDate") && this.validateDate(null, fromDate, fromDate.value)&& centralLogic.validateNumberOfDays(showDays)){
        if ((showDays.text != prevNumberOfDays) || (fromDate.selectedDate.toDateString() != prevChosenDate.toDateString()))
          callLater(updateData,['yes']);
        else
          callLater(updateData,['no',true]);

        this.refreshButton.setFocus();
      }
      else if (Object(focusManager.getFocus()).id!="showDays" && Object(focusManager.getFocus()).id!="fromDate"  && commonLogic.validateDate(null, fromDate, fromDate.value)&& centralLogic.validateNumberOfDays(showDays))
        this.updateData('no',true);
    }*/
    clearInterval(this.interval);
  }
   startAutoRefresh(refreshStart: string): void {
    if (refreshStart == "Y") {
      this.updateData('yes',true);
      // autoRefresh.start();
    }
  }
  refreshParent(): void {
    this.updateData('yes', true, false);
  }
   updateData(checkLocalDateRange: string='no', fromCheckDateRange: boolean=false, cancelled: boolean=false): void {
    this.requestParams= [];
    this.d1 = moment(this.fromDate.text, this.dateFormat.toUpperCase());
    // this.d2 = new Date(this.fromDate.selectedDate);

    this.d2= moment(this.fromDate.text, this.dateFormat.toUpperCase()).add(Number(this.showDays.text) -1 , 'days');
    let DateToStr =  moment(this.d2).format(this.dateFormat.toUpperCase());
    // d2.date = d2.date + parseInt(showDays.text) - 1;

    if (cancelled) {
      /*this.fromDate.selectedDate = this.prevChosenDate;
      this.d1 = this.prevChosenDate;*/
      this.showDays.text = this.prevNumberOfDays;
      // d2.date = d2.date + parseInt(showDays.text) - 1;
      this.showDays.setFocusAndSelect();
      return;
    }
    if (!fromCheckDateRange&&this.checkDateRangeCommon(checkLocalDateRange, this.d1,this.d2, this.showDays.text, ExternalInterface.call('eval', 'testDate'), this.dateFormat)) {
      return;
    }


          // parameter for fromDateAsString
    if(this.validateDateField(this.fromDate)) {
      if(checkLocalDateRange == "no") {
           this.checkDateRange(this.d1, this.d2);
      }

      this.requestParams["centralMonitor.fromDateAsString"]=this.fromDate.text;
          // parameter for toDateAsString
      this.requestParams["centralMonitor.toDateAsString"]=DateToStr;
          // parameter for entityId
      this.requestParams["centralMonitor.entityId"]= this.entityCombo.selectedItem.content;
      if (this.comboChange) {
          // parameter for currencyLimit
            this.requestParams["centralMonitor.currencyLimit"]="";
      } else {
          // parameter for currencyLimit
            this.requestParams["centralMonitor.currencyLimit"]= this.currLimitText.text;
      }
          // parameter for currTimeStamp
      this.requestParams["centralMonitor.currTimeStamp"]=ExternalInterface.call('eval', 'timeStamp');
          // send the request
    }
        // }

      // }
   // }
    this.inputData.send(this.requestParams);

  }
  /*validateshowdaysValue() : boolean {
   if(this.showDays.text) {
   if(Number(this.showDays.text) <2 || Number(this.showDays.text) > 14) {
     this.swtAlert.warning('Value must be between 2 and 14.');
     return false;
   }
   }
   return true;
  }*/
  validateShowDaysValue(): void {
    try {
      let fromDateAsString = moment(this.fromDate.text,this.dateFormat.toUpperCase());
      let prevChosenDate = moment(this.prevChosenDate ,this.dateFormat.toUpperCase());
      this.updateDayLabel();
      /*if (Object(focusManager.getFocus()).id == "closeButton") {
        event.stopImmediatePropagation();
        this.closeHandler();
      }
      else if (Object(focusManager.getFocus()).id == "fromDate") {
        event.stopImmediatePropagation();
      }
      else {*/

      let showDayText = "" + this.showDays.text;
      if (
          (showDayText == "")
          ||
          (showDayText.indexOf('0') != 0 && showDayText != "")
          ||
          (showDayText.indexOf('0') == 0 && showDayText.indexOf("0", 1) == 1)
          ||
          (showDayText.indexOf('0') == 0 && showDayText.indexOf("0", 1) == -1)
        ) {
         if (this.validateNumberOfDays(this.showDays) && (this.validateDateField(this.fromDate))) {
            if ((showDayText != this.prevNumberOfDays) ||(fromDateAsString.diff(prevChosenDate) != 0)) {
              setTimeout(() => {
                this.updateData('yes');
              }, 0);
              // this.autoRefresh.stop();
            }
         }
       // }
      }
      // this.datePickerOpen = false;
    } catch (e) {
      console.log('error in focusout', e);
    }

  }

  /**
   * close the window from the close button
   **/
 closeHandler(): void {
    // call for close window
    ExternalInterface.call("close");
  }
  optionHandler(): void {
    // call for open monitor options window
    ExternalInterface.call("openOptionsWindow", this.entityCombo.selectedLabel);
    clearInterval(this.interval);
    this.refreshStatus="N";

  }
  cellLogic(selectedRowData): void {
    let fieldName = selectedRowData.target.field;
    let isClickable = (selectedRowData.target.data.slickgrid_rowcontent[fieldName] ) ? selectedRowData.target.data.slickgrid_rowcontent[fieldName].clickable : null;
    if (isClickable) {
      // call the link for account monitor or movement summary display
      this.clickLink((this.entityCombo.selectedLabel).toString(), this.selectedEntity.text, this.currencyCode, this.accountId, selectedRowData.target.data.balance, selectedRowData.target.data.slickgrid_rowcontent[fieldName].date , this.breakdown.selectedValue);
    }
  }
  clickLink(sEntityId: string,sEntityName: string, sCurrencyCode: string,sAccountId: string,sBalanceName: string, sColumnDate: string, sActionCode: string): void {
    try {
      ExternalInterface.call("clickLink", sEntityId,sEntityName, sCurrencyCode,sAccountId, sBalanceName,sColumnDate, sActionCode);
    } catch (e) {
      console.log('error', e);
    }

  }
   updateDayLabel(): void {
    if (parseInt(this.showDays.text,10)==0 || parseInt(this.showDays.text,10)==1) {
      this.daysLabel.text = ExternalInterface.call('getBundle', 'text', 'day', 'Day');
    } else {
      this.daysLabel.text = ExternalInterface.call('getBundle', 'text', 'days', 'Days');
    }
  }
  changeCombo(): void {
    if(!this.validateDateField(this.fromDate) || this.entityCombo.selectedLabel == null) {
      return;
    } else {
      this.updateData();
    }

  }
  validateStartDate(event: Event): void {

    /*if (event.type == FocusEvent.FOCUS_OUT)
    {


      if (Object(focusManager.getFocus()).id == "closeButton")
      {
        event.stopImmediatePropagation();
       // closeHandler();

      }

      else if ((Object(focusManager.getFocus()).id == "showDays") && commonLogic.validateDate(event as FocusEvent, fromDate, fromDate.value))
      {
       // event.stopImmediatePropagation();

      }*/
     /* else if (commonLogic.validateDate(event as FocusEvent, fromDate, fromDate.value))
      {

        updateData("no",true);

      }

    }
    else if (event.type == Event.CHANGE && commonLogic.validateDate(event as FocusEvent, fromDate, fromDate.value))
    {
      showDays.setFocus();
    }*/
  }
  validateDateEnterPress(event): void {
    if(event.keyCode ==13) {
    if(this.validateDateField(this.fromDate)) {
      this.updateData("no", true);
      }
    }
  }
  validateDateFocusOut(event) {
    if(this.validateDateField(this.fromDate)) {
      this.showDays.setFocusAndSelect();
       // this.updateData("no", true);
    }
  }


  validateDateField(dateField) {
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      if(dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase() , true);

        if(!date.isValid()) {
          // this.swtAlert.warning(alert+ this.dateFormat.toUpperCase());
          this.swtAlert.error(alert, null, null, null, () => {
            this.setFocusDateField(dateField);
          });
          clearInterval(this.interval);
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          this.setFocusDateField(dateField);
        });
        clearInterval(this.interval);
        return false;
      }
      dateField.selectedDate = date.toDate();
    } catch(error) {
      console.log('error in validateDateField', error);
    }

    return true;
  }
  setFocusDateField(dateField) {
    dateField.setFocus();
    dateField.text = this.jsonReader.getScreenAttributes()["from"];
  }

  /**
   * Refresh popup screen
   **/
   rateHandler(): void {
    this.win =  SwtPopUpManager.createPopUp(this, OptionsPopUp, {
      title: "Refresh Rate",
      refreshText: this.refreshRate.toString()
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '340';
    this.win.height = '150';
    this.win.showControls = true;
    this.win.id = "optionsWindow";
    this.win.display();

  }
  /*Called from Option pop up screen*/
  saveRefreshRate(res): void {
    let minRate = "Refresh rate selected was below minimum.\nSet to 5 seconds";
    if (res == '' || res == null) {
      this.swtAlert.error(ExternalInterface.call('getBundle', 'text', 'label-validNumber', 'Not Valid Number'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    } else {
      let selectedRateBelowMinimum =false;
      this.refreshRate=Number(res);
      // refreshRate=Number(refreshRatePopup.refresh.text);
      if (this.refreshRate < 5) {
        // set the default rate
        this.refreshRate=5;
        selectedRateBelowMinimum=true;
      }
      /*if (autoRefresh)
        autoRefresh.delay=(refreshRate * 1000);*/
      // call the 	getUpdateRefreshRequest
      let request: string =ExternalInterface.call("getUpdateRefreshRequest", this.refreshRate);
      if (request != null && request != "") {
        this.updateRefreshRate.url = this.baseURL + request;
        this.updateRefreshRate.send();
      }
      // remove the popup window
      // PopUpManager.removePopUp(refreshRatePopup);
      // check rate is minimum
      if (selectedRateBelowMinimum) {
        this.swtAlert.error(minRate);
      }
    }
  }

  report(type: string): void {
// Initialize
    let selects = [];
    let isTotalGrid = false;
    // add the entity
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-entity', 'Entity')+"="+ this.entityCombo.selectedLabel);
    // add the currency limit
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-currencyLimit', 'Currency Limit')+"="+this.currLimitText.text);
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-currencyCode', 'Currency Code')+"="+ this.currencyCode);
    let mulStr: string= this.multiplier.text;
    let mulStart: number =mulStr.indexOf("(");
    let mulEnd: number =mulStr.indexOf(")");
    let multiplierStr: string=mulStr.substring(mulStart + 1, mulEnd);
    if (mulStart != -1) {
      selects.push(ExternalInterface.call('getBundle', 'text', 'label-currencyMultiplier', 'Currency Multiplier')+"="+ multiplierStr);
    } else {
      selects.push(ExternalInterface.call('getBundle', 'text', 'label-currencyMultiplier', 'Currency Multiplier')+"=None");
    }
    let sD1="";
    let sD2="";

    if (this.dateFormat.toLowerCase() == "dd/mm/yyyy") {

      sD1 = CommonUtil.formatDate(this.d1, "DD/MM/YYYY");
      sD2 = CommonUtil.formatDate(this.d2, "DD/MM/YYYY");

    } else {

      sD1 = CommonUtil.formatDate(this.d1, "MM/DD/YYYY");
      sD2 = CommonUtil.formatDate(this.d2, "MM/DD/YYYY");

    }
    // add the from date
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-fromDate', 'From Date')+"="+ sD1);
    // add the to date
    // To Date
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-toDate', 'To Date')+"="+ sD2);
    if(this.lastRecievedJSON.centralbankmonitor.grid.totals) {
      isTotalGrid = true;
    }
    this.dataExport.convertData(this.lastRecievedJSON.centralbankmonitor.grid.metadata.columns, this.centralGrid, this.centralGrid.gridData, selects, type, isTotalGrid);

  }
  doHelp(): void {
     ExternalInterface.call('help');
  }
  keyDownInNumberOfDays(event): void {
    let fromDateAsString = moment(this.fromDate.text,this.dateFormat.toUpperCase());
    let prevChosenDate = moment(this.prevChosenDate ,this.dateFormat.toUpperCase());
    if (event.keyCode == 13) {
      if(this.validateNumberOfDays(this.showDays) && this.validateDateField(this.fromDate)) {
        if (this.showDays.text != this.prevNumberOfDays || (fromDateAsString.diff(prevChosenDate) != 0 )) {
          this.updateData('no');
        } else {
          this.updateData('no',true);
        }

      }
    }
  }
  validateNumberOfDays(showDays): boolean {
    let parsedDays = parseInt(showDays.text, 10);
    if (isNaN(parsedDays) || parsedDays <= 1 || parsedDays > 14 ) {
      this.showAlertForNumberOfDays(showDays);
      return false;
    } else {
      return true;
    }
  }
  showAlertForNumberOfDays(showDays): void {
    this.swtAlert.show(" 'Show' value must be between 2 and 14. ", "Error", Alert.OK, null,  this.resetShowDay.bind(this));
  }
  resetShowDay(event) {
    if(event.detail == Alert.OK) {
      this.showDays.setFocusAndSelect();
      clearInterval(this.interval);
      this.showDays.text = this.prevNumberOfDays;
    }
  }
  checkDateRangeCommon(autoRefreshOrcheckLocalDateRange: string, fromDate, toDate, showDays: SwtNumericInput, systemDate: string, dateFormat: string): boolean {
    let dateRangeExceeded = false;
    let nDaysPriorToToday: number = ExternalInterface.call('eval', 'nDaysPriorToToday');
    let priorDate = moment(systemDate, this.dateFormat.toUpperCase()).subtract(nDaysPriorToToday, 'days');

    let nDaysAheadToToday: number = ExternalInterface.call('eval', 'nDaysAheadToToday');
    let  aheadDate = moment(systemDate, this.dateFormat.toUpperCase()).add(nDaysAheadToToday, 'days');
   // aheadDate.setDate(aheadDate.getDate() - nDaysAheadToToday);
    if (fromDate.diff(priorDate) <0 || toDate.diff(aheadDate) > 0 ) {
      dateRangeExceeded = true;
      let warningMessage: string = "The data for this date range selection may not be available " + "<br/>" +
        "in the cache and will take time to be calculated. Do you want to continue?";


      this.swtAlert.confirm(warningMessage, "", Alert.OK | Alert.CANCEL, null, this.checkDateRangeListener.bind(this));
      // this.swtAlert.show(warningMessage, null, Alert.OK | Alert.CANCEL, null,
      // 	function(event:CloseEvent):void
      // 	{
      // 		if (event.detail == Alert.OK){
      // 			// Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
      // 			this.showBuildInProgress = true;
      // 			this.updateData(autoRefreshOrcheckLocalDateRange, true);
      // 		}else
      // 			updateDataFunction(autoRefreshOrcheckLocalDateRange, true, true);
      // 	}
      // 	, null, Alert.OK);
    }

    return dateRangeExceeded;
  }
  checkDateRangeListener(event): any {
    try {
      if (event.detail == Alert.OK) {
        // Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
        this.showBuildInProgress = true;
        this.updateData("no", true);

      } else {
        this.updateData("no", null, true);
      }
      // else
      //   updateDataFunction(autoRefreshOrcheckLocalDateRange, true, true);
    } catch (e) {

    }
  }
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, 'Central Bank Monitor', this.versionNumber, "");
    let addMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }


  /** This function is used to display the JSON
   *    for data showed in grid
   */
  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }
   changeCurrLimit(event):void {
     if (event.listData.newValue != event.listData.oldValue) {


       let currBal: string;
       //get the currency limit
       currBal = event.listData.newValue;
       let changedBal: string = ExternalInterface.call("formatCurrency", currBal);
       if (currBal == "0") {
         this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-amountGreaterThanZero', 'Amount should be greater than zero'), "", Alert.OK, null, null);
         this.centralGrid.dataProvider[event.rowIndex].slickgrid_rowcontent[event.dataField].content =event.listData.oldValue;
         this.centralGrid.dataProvider[event.rowIndex][event.dataField] =event.listData.oldValue;
         this.centralGrid.refresh();

       }
       else if (changedBal == "invalid" || changedBal.length <= 0) {
         this.swtAlert.warning(ExternalInterface.call('getBundle', 'text', 'label-validAmount', 'Please enter a valid amount'), "", Alert.OK, null, null);
         this.centralGrid.dataProvider[event.rowIndex].slickgrid_rowcontent[event.dataField].content =event.listData.oldValue;
         this.centralGrid.dataProvider[event.rowIndex][event.dataField] =event.listData.oldValue;
         this.centralGrid.refresh();
       } else {
         let result: string=ExternalInterface.call("saveMonitorDetails", this.arrayOfDates[event.dataField], event.listData.oldValue, changedBal , (this.entityCombo.selectedLabel).toString(), true);
         if(result != "")
          this.updateData('yes', true, false);
       }
     }
   }

}
// Define lazy loading routes
const routes: Routes = [
  { path: '', component: CentralBankMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CentralBankMonitor],
  entryComponents: []
})
export class CentralBankMonitorModule {}
