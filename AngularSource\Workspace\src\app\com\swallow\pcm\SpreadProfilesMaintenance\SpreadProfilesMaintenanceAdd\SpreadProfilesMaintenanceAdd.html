<SwtModule #swtModule (close)=' popupClosed()' (creationComplete)='onLoad()' height='100%' width='100%'>
  <VBox width='100%' height='100%' paddingLeft="10" paddingRight="10" paddingBottom="10" paddingTop="10">
    <SwtCanvas width='100%' height="89%">
      <VBox width='100%' height="100%">
      <HBox width="100%" height="10%" marginTop="5" marginLeft="5">
        <HBox width="29%">
          <SwtLabel text="Spread ID*" paddingRight="15">
          </SwtLabel>
          <SwtTextInput id="spreadIdTxtInput" 
                        #spreadIdTxtInput
                        toolTip="Spread ID*"
                        height="22"
                        width="150"
                        maxChars="20"
                        restrict="a-zA-Z0-9\-_"
                        required="{{screenName == 'add' ? 'true' : 'false'}}">
          </SwtTextInput>
        </HBox>
        <HBox width="38%">
          <SwtLabel text="Spread Name*" paddingRight="15">
          </SwtLabel>
          <SwtTextInput id="spreadNameTxtInput" 
                        restrict="A-Za-z0-9\d_ !\&quot;#$%&'()*+,\-.\/:;&lt;=&gt;?@[\\\]^`{|}~"
                        #spreadNameTxtInput 
                        toolTip="Spread Name" 
                        height="22" 
                        width="210"
                        maxChars="30"
                        required="true">
          </SwtTextInput>
        </HBox>
        <HBox width="33%">
            <SwtLabel text="Currency" paddingLeft="10" paddingRight="15"></SwtLabel>
            <SwtComboBox id="currencyComboBox"
                              #currencyComboBox
                              dataLabel="currencyList"
                              toolTip="Select Currency"
                              width="70"
                              (change)="changeComboCurrency($event)"
                              enabled= "{{screenName == 'add' ? 'true' : 'false'}}">
            </SwtComboBox>
            <SwtLabel #ccyLabel fontWeight="normal"  text=" " paddingLeft="10"></SwtLabel>
        </HBox>
      </HBox>
      <SwtPanel id="processPointsPanel" #processPointsPanel height="{{screenName == 'add' ? '89%' : '49%'}}"
                width="100%" title="Spread Process Points" paddingBottom="10" paddingTop="5">
        <HBox width="100%" height="{{screenName == 'add' ? '345' : '180'}}">
          <SwtCanvas id="spreadDetailsCanvasGrid"
                      #spreadDetailsCanvasGrid
                      height="100%"
                      width="90%"
                      border="false">
          </SwtCanvas>
          <VBox width="10%" height="100%" paddingLeft="5" paddingTop="5" verticalAlign="middle" horizontalAlign="center">
            <SwtButton #addButton 
                        id="addButton"
                        width="70"
                        (click)="doAddSpreadDetails($event)" 
                        (keyDown)="keyDownEventHandler($event)"  marginBottom="6"></SwtButton>
            <SwtButton #changeButton 
                        id="changeButton"
                        width="70"
                        (click)="doChangeSpreadDetails($event)"
                        (keyDown)="keyDownEventHandler($event)"  marginBottom="6"></SwtButton>
            <SwtButton buttonMode="true" 
                        id="viewButton"
                        width="70"
                        #viewButton 
                        (click)="doViewSpreadDetails($event)"
                        (keyDown)="keyDownEventHandler($event)"  marginBottom="6"></SwtButton>
            <SwtButton buttonMode="true" 
                        id="deleteButton"
                        width="70"
                        #deleteButton 
                        (click)="doDeleteSpreadDetails()"
                        (keyDown)="keyDownEventHandler($event)"></SwtButton>
          </VBox>
        </HBox>
      </SwtPanel>
      <HBox width="100%" height="{{screenName == 'add' ? '0%' : '40%'}}" visible="{{screenName == 'add' ? 'false' : 'true'}}" >
        <SwtPanel width='100%' 
                  height='98%' 
                  title="Associated Account Groups" paddingTop="5">
          <SwtCanvas id="aggregateAccountsCanvasGrid" 
                      #aggregateAccountsCanvasGrid 
                      height="150" 
                      width="100%"
                      border="false">
          </SwtCanvas>
        </SwtPanel>
      </HBox>
      </VBox>
    </SwtCanvas>

    <SwtCanvas width='100%' height="9%">
      <HBox width="100%">
        <HBox width="100%" height="100%" style="margin-top: 3px; margin-left: 5px">
          <SwtButton #saveButton id="saveButton" (click)="saveEventHandler()" (keyDown)="keyDownEventHandler($event)"></SwtButton>
          <SwtButton #amendButton width="70"        label="Amend"          visible="false" includeInLayout="false"         (click)="amendEventHandler()"
         (keyDown)="keyDownEventHandler($event)"         id="amendButton"></SwtButton>
      
      <SwtButton buttonMode="true"
                   id="cancelAmendButton"
                   width="70"   
                   visible="false" includeInLayout="false"
                   #cancelAmendButton
                   (click)="cancelAmendEventHandler();"
                   (keyDown)="keyDownEventHandler($event)"></SwtButton>
      <SwtButton buttonMode="true"
                   id="closeButton"
                   visible="false" includeInLayout="false"
                   #closeButton
                   width="70"   
                   marginLeft="5"
                   (click)="popupClosed();"
                   (keyDown)="keyDownEventHandler($event)"></SwtButton>

          <SwtButton buttonMode="true" id="cancelButton"  marginLeft="5" #cancelButton (click)="closeBtn();"
            (keyDown)="keyDownEventHandler($event)"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10" marginTop="5">
          <SwtButton #acceptButton (click)="acceptEventEventHandler()" visible="false" label="Accept" (keyDown)="keyDownEventHandler($event)" id="acceptButton"
            width="70"></SwtButton>
          <SwtButton buttonMode="true" id="rejectButton" width="70" visible="false" label="Reject" #rejectButton (click)="rejectEventEventHandler();"
            (keyDown)="keyDownEventHandler($event)"></SwtButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
          <SwtButton [buttonMode]="true" 
                      #printButton 
                      id="printButton" 
                      styleName="printIcon" 
                      (click)="printPage()"
                      (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtHelpButton id="helpIcon" 
                          [buttonMode]="true" 
                          enabled="true" 
                          helpFile="spread-profile" 
                          (click)="doHelp()">
          </SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
