<SwtModule #swtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5">
    <SwtCanvas width="100%" minWidth="960">
    <Grid width="100%" height="75" paddingLeft="5">
      <GridRow width="100%" height="28">
          <GridItem width="300">
            <GridItem width="120">
              <SwtLabel id="entityLbl" #entityLbl></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="entityCombo" #entityCombo width="200" dataLabel="entityList"> </SwtComboBox>
            </GridItem>
          </GridItem>
          <GridItem paddingLeft="50">
            <SwtLabel id="entityDesc" #entityDesc fontWeight="normal"></SwtLabel>
          </GridItem>
      </GridRow>

      <GridRow width="100%" height="25">
          <GridItem width="300">
            <GridItem width="120">
              <SwtLabel id="criticalTypeLbl" #criticalTypeLbl></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput id='criticalTypeTxt' #criticalTypeTxt maxChars="20" width="250" height="20"
                (focusOut)="validateField(criticalTypeTxt,'Number 2','alphaNumPat')"></SwtTextInput>
            </GridItem>
          </GridItem>
      </GridRow>

      <GridRow width="100%" height="25">
            <GridItem width="120">
              <SwtLabel id="descLbl" #descLbl></SwtLabel>
            </GridItem>
            <GridItem width="500">
              <SwtTextInput id='descTxt' #descTxt maxChars="50" width="100%" height="20"
                (focusOut)="validateField(descTxt,'section.sectionName','ascii_standard')"></SwtTextInput>
            </GridItem>
      </GridRow>
    </Grid>
  </SwtCanvas>

  <HBox width="100%" height="100%" minWidth="960">
    <Grid width="70%" height="100%">
      <GridRow width="100%" height="30">
            <GridItem width="250">
              <SwtLabel id="categoryLbl" #categoryLbl></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="categoryCombo" #categoryCombo width="200" dataLabel="categoryList"> </SwtComboBox>
            </GridItem>
      </GridRow>

      <GridRow width="100%" height="30">
            <GridItem width="250">
              <SwtLabel id="orderInCategoryLbl" #orderInCategoryLbl></SwtLabel>
            </GridItem>
            <GridItem>
                <SwtNumericInput #orderInCategoryTxt  width="70" textAlign="right" minimum="1" maximum="999" maxChars="3"></SwtNumericInput>
            </GridItem>
      </GridRow>

      <GridRow width="100%" height="30">
        <GridItem width="250">
          <SwtLabel id="sumToCategLbl" #sumToCategLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtCheckBox id="sumToCategCheck" #sumToCategCheck></SwtCheckBox>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="30">
        <GridItem width="250">
          <SwtLabel id="sumToTotalLbl" #sumToTotalLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtCheckBox id="sumToTotalCheck" #sumToTotalCheck></SwtCheckBox>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="30">
        <GridItem width="250">
          <SwtLabel id="reportableLbl" #reportableLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtCheckBox id="reportableCheck" #reportableCheck></SwtCheckBox>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="30">
        <GridItem width="250">
          <SwtLabel id="reportIndivPayLbl" #reportIndivPayLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtCheckBox id="reportIndivPayCheck" #reportIndivPayCheck></SwtCheckBox>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="30">
        <GridItem width="250">
          <SwtLabel id="enableUpdateProcessingLbl" #enableUpdateProcessingLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtCheckBox id="enableUpdateProcessingCheck" #enableUpdateProcessingCheck></SwtCheckBox>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="30">
        <GridItem width="250">
          <SwtLabel id="sqlUpdateQueryLbl" #sqlUpdateQueryLbl></SwtLabel>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="30">
        <GridItem width="100%" paddingLeft="20">
          <SwtLabel id="updateMvtLbl" #updateMvtLbl></SwtLabel>
        </GridItem>
        <GridItem width="120" horizontalAlign="right">
          <SwtButton [buttonMode]="true" id="validateQueryButton" #validateQueryButton enabled='false'
          (click)="validateQuery()"></SwtButton>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="90">
        <GridItem width="120" paddingLeft="80">
          <SwtLabel id="setLbl" #setLbl></SwtLabel>
        </GridItem>
        <GridItem width="100%" height="100%" >
          <SwtTextArea #setTxtArea width="100%" height="100%"   (change)='enableField("setTxtArea")' minHeight="75" paddingTop="5"  paddingBottom="10"></SwtTextArea>       
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="150">
        <GridItem width="120" paddingLeft="60">
          <SwtLabel id="whereLbl" #whereLbl></SwtLabel>
        </GridItem>
        <GridItem width="100%" height="100%" >
          <SwtTextArea #whereTxtArea width="100%" height="100%"   (change)='enableField("whereTxtArea")' minHeight="75" paddingTop="5"></SwtTextArea>        </GridItem>
      </GridRow>     
    </Grid>

    <Grid width="30%" height="100%">
      <GridRow width="100%" height="100%">
        <GridItem width="100%" height="100%">
          <SwtCanvas #ccyGridContainer id="ccyGridContainer" styleName="canvasWithGreyBorder" width="100%"
            height="100%" border="false"></SwtCanvas>
        </GridItem>
      </GridRow> 
      <GridRow width="100" height="30" paddingTop="5">
        <HBox>
        <GridItem width="100%" height="100%"  paddingLeft="5" >
        <SwtButton [buttonMode]="true" id="addButton" #addButton (click)="subScreenHandler('add')"> </SwtButton>
        </GridItem>
        <GridItem width="100%" height="100%"  paddingLeft="5" >
        <SwtButton [buttonMode]="true" id="changeButton" #changeButton paddingLeft="5" (click)="subScreenHandler('change')">
        </SwtButton>
      </GridItem>
      <GridItem width="100%" height="100%"  paddingLeft="5" >
        <SwtButton [buttonMode]="true" id="deleteButton" #deleteButton (click)="deleteCcyHandler()">
        </SwtButton>
      </GridItem>
    </HBox>
      </GridRow>  
    </Grid>

  </HBox>

  <SwtFieldSet style="height: 80; padding-right: 5px; padding-left: 5px;  padding-bottom: 10px; color:blue;" minWidth="960" legendText="Scheduling">
    <Grid width="100%" height="100%">

      <GridRow>
        <GridItem width="200">
          <SwtLabel #scheduledTimeFrameLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <VBox width="160" height="100%">
            <SwtRadioButtonGroup #timeFrameGroup   id="timeFrameGroup"
                                 align="vertical"  >
              <SwtRadioItem #radioSystem id="radioSystem" value="S"    selected="true" groupName="timeFrameGroup"></SwtRadioItem>
               <SwtRadioItem #radioEntity id="radioEntity" value="E" groupName="timeFrameGroup" ></SwtRadioItem>
            </SwtRadioButtonGroup>

          </VBox>
          <HBox marginTop="15" marginLeft="5">
            <SwtCheckBox id="workingDayOnlyCheck" #workingDayOnlyCheck ></SwtCheckBox>
            <SwtLabel #workingDayOnlyLbl></SwtLabel>
          </HBox>
        </GridItem>
      </GridRow>

      <GridRow>
        <GridItem width="200">
          <SwtLabel #runSqlUpdateLbl></SwtLabel>
        </GridItem>
        <GridItem  width="90" >
          <SwtNumericInput #runSqlUpdateTxt  width="70" textAlign="right"  maxChars="4"
          (focusOut)="checkRunSqlNumber()"></SwtNumericInput>
        </GridItem>
        <GridItem width="200">
          <SwtLabel #unitLbl></SwtLabel>
        </GridItem>
      </GridRow>

      <GridRow width="100%">
        <GridItem width="60%">
        <GridItem width="200">
          <SwtLabel #activeBetweenLbl></SwtLabel>
        </GridItem>
        <GridItem  width="90" >
          <SwtTextInput #startTimeTxt pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"
          (focusOut)="validateTime(startTimeTxt)" width="70" textAlign="center" maxChars="5"></SwtTextInput>
        </GridItem>
        <GridItem width="40">
          <SwtLabel #andLbl></SwtLabel>
        </GridItem>
        <GridItem  width="90" >
          <SwtTextInput #endTimeTxt pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"
          (focusOut)="validateTime(endTimeTxt)" width="70" textAlign="center" maxChars="5"></SwtTextInput>
        </GridItem>
        </GridItem>

        <GridItem   horizontalAlign="right" width="40%">
          <GridItem paddingRight="5"><SwtLabel #lastExecLbl></SwtLabel></GridItem>
          <GridItem><SwtLabel #lastExecTime fontWeight="normal"></SwtLabel></GridItem>
        </GridItem>
      </GridRow>
    </Grid>
  </SwtFieldSet>

    <SwtCanvas width="100%" height="35" minWidth="960">
      <HBox width="100%">
          <SwtButton [buttonMode]="true" id="saveButton" #saveButton (click)="saveHandler()" width="70">
          </SwtButton>

          <SwtButton #amendButton
          label="Amend"
          width="70"
          marginRight="8"
          visible="false" includeInLayout="false"
          (click)="amendEventHandler()"
          id="amendButton"></SwtButton> 

          <SwtButton [buttonMode]="true" id="cancelButton" #cancelButton (click)="closeHandler()">
          </SwtButton>
      <SwtButton buttonMode="true"
                   id="cancelAmendButton"
                   visible="false" includeInLayout="false"
                   #cancelAmendButton
                   (click)="cancelAmendEventHandler();"></SwtButton>
      <SwtButton buttonMode="true"
                   id="closeButton"
                   marginLeft="5"
                   visible="false" includeInLayout="false"
                   #closeButton
                   (click)="popupClosed();"></SwtButton>

       </HBox>

      <SwtLoadingImage #loadingImage></SwtLoadingImage>

      <HBox horizontalAlign="right" paddingRight="10">
      <SwtButton #acceptButton (click)="acceptEventEventHandler()" visible="false" label="Accept"  id="acceptButton"
                   width="70"></SwtButton>
      <SwtButton buttonMode="true" id="rejectButton" width="70" visible="false" label="Reject" #rejectButton (click)="rejectEventEventHandler();"
                   ></SwtButton>

      </HBox>

    </SwtCanvas>
  </VBox>
</SwtModule>