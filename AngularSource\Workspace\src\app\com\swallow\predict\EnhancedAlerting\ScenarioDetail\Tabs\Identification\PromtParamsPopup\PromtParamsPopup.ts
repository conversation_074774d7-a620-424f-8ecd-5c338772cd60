import {CommonService, SwtButton, SwtModule,VBox,HBox, SwtTextInput,SwtLabel} from 'swt-tool-box';
import {Component, ElementRef, ViewChild} from '@angular/core';


@Component({
    selector: 'app-prompt-params-pop-up',
    templateUrl: './PromtParamsPopup.html',
    styleUrls: ['./PromtParamsPopup.css']
})

export class PromtParamsPopup extends SwtModule {

    @ViewChild("okButton") public okButton: SwtButton;
    @ViewChild("cancelButton") public cancelButton: SwtButton;
    @ViewChild("refresh")  public refresh: SwtTextInput;
    @ViewChild("paramsContainer")  public paramsContainer: VBox;
    
    paramList: string;
    listIds :[];
    textInputList = [];
    resultList = [];
    lastUsedParams = [];
    
    constructor(private element: ElementRef, private commonService: CommonService) {
        super(element, commonService);
    }

  onLoad() {
    for (let index = 0; index < this.paramList.length; index++) {
      


      var box = <HBox>this.paramsContainer.addChild(HBox);

      box.id = "dynamicPram" + index;
      // box.name = this.paramList[index]
      let label = <SwtLabel>box.addChild(SwtLabel);
      let textInput = <SwtTextInput>box.addChild(SwtTextInput);
      textInput.name = this.paramList[index];
      this.textInputList.push(textInput);
      if(this.lastUsedParams && this.lastUsedParams[this.paramList[index]]){
        textInput.text = this.lastUsedParams[this.paramList[index]];
      }

      // checkbox.name = xml.group_id;
      // checkbox.change = this.showHideThresholdsEvent.bind(this);

      label.text = this.paramList[index];
      label.paddingTop = "3";
      label.width = "100";
      textInput.width = "350";
      label.fontWeight = "normal";
      box.horizontalGap = '15';


    }
  }

  closePopup() {
      try {
        if(this.titleWindow) {
          this.close();
        } else {
          window.close();
        }
      } catch (error) {
        console.log(error, "RatePopUp", "closePopup");
      }
    }

    okHandler() {

        try {
          for (let index = 0; index < this.textInputList.length; index++) {
            this.resultList[this.textInputList[index].name] = this.textInputList[index].text ;
          }
          this.parentDocument.saveParamListAndExecute(this.resultList);
          this.closePopup();
        } catch(error) {
            console.error('error :',error);
        }
    }
}
