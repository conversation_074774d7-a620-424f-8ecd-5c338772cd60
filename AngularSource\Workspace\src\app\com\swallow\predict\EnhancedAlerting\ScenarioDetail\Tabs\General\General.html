<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%">
    <Grid paddingTop="5" paddingLeft="10" height="80">
      <GridRow height="25">
        <GridItem width="240">
          <SwtLabel #recordInsLbl></SwtLabel>
        </GridItem>
        <GridItem width="80">
          <SwtCheckBox id="recordCheck" #recordCheck selected="false" (change)="changeScenarioCheck()"></SwtCheckBox>
        </GridItem>
      </GridRow>

      <GridRow height="30">
        <GridItem width="240">
          <SwtLabel #categoryLbl></SwtLabel>
        </GridItem>
        <GridItem width="220">
          <SwtComboBox #categoryCombo dataLabel="categoryList" (change)="changeCategoryList()"></SwtComboBox>
        </GridItem>
        <GridItem>
          <SwtLabel #selectedCategLbl fontWeight="normal"></SwtLabel>
        </GridItem>
      </GridRow>

      <GridRow height="30">
        <GridItem width="240">
          <SwtLabel #orderLbl></SwtLabel>
        </GridItem>
        <GridItem width="80">
          <SwtNumericInput #orderTxt textAlign="right" maxChars="3"></SwtNumericInput>
        </GridItem>
      </GridRow>
    </Grid>
      <SwtFieldSet id="fieldSet" #fieldSet style="height: 85%; width: 100%; color:blue; padding-bottom: 10px; padding-right: 5px; padding-left: 5px;">
        <Grid width="100%" height="100%">
        <!--<GridRow width="100%" height="100%">-->
          <SwtRadioButtonGroup #typeOptions id="typeOptions"  align="vertical" width="100%" height="100%" (change) ="enableDisableComponents()">
            <SwtRadioItem value="C" width="200" groupName="typeOptions" enabled ="true" selected="true" id="cyclic" #cyclic>
            </SwtRadioItem>
            <GridRow>
              <GridItem width="238" paddingLeft="20">
                <SwtLabel #runLbl fontWeight="bold" ></SwtLabel>
              </GridItem>
              <GridItem width="7%">
                <SwtTextInput #runTxt textAlign="right" maxChars="8" width="120" (focusOut)="validateTimeOfRun(runTxt)"></SwtTextInput>
              </GridItem>
            </GridRow>
            <GridRow>
              <GridItem width="238" paddingLeft="20">
                <SwtLabel #startLbl fontWeight="bold" ></SwtLabel>
              </GridItem>
              <GridItem width="7%">
                <SwtTextInput #startTxt textAlign="right" maxChars="5" width="120"  (focusOut)="validateTime(startTxt)"></SwtTextInput>
              </GridItem>
            </GridRow>
            <GridRow>
              <GridItem width="238" paddingLeft="20">
                <SwtLabel #endLbl fontWeight="bold" ></SwtLabel>
              </GridItem>
              <GridItem width="7%">
                <SwtTextInput #endTxt textAlign="right" maxChars="5" width="120"  (focusOut)="validateTime(endTxt)"></SwtTextInput>
              </GridItem>
            </GridRow>
            <GridRow width="100%" paddingBottom="10">
            <GridItem width="90">
            <SwtRadioItem enabled='false'  value="S" width="200" groupName="typeOptions" id="scheduled" #scheduled
            enabled ="false"></SwtRadioItem>
            </GridItem>
            <GridItem width="90%">
              <SwtLabel #schedParamsDesc id='schedParamsDesc' fontWeight="normal"></SwtLabel>
            </GridItem>
            <HBox horizontalAlign="right" width="10%">
              <SwtButton [buttonMode]="true" id="defParams" #defParams (click)="openDefParamScreen()" enabled="false"> </SwtButton>
            </HBox>
          
          </GridRow>
              <!--<GridRow width ="100%" height="130" paddingBottom="10" paddingLeft="20">-->
              <SwtCanvas #schedGridContainer id="schedGridContainer" styleName="canvasWithGreyBorder" width="100%" height="45%" minHeight="130"
                border="false"></SwtCanvas>
             <!--- </GridRow>-->
                <GridRow width="100%" paddingBottom="10">
                  <GridItem width="50%" paddingLeft="20">
                    <SwtLabel #scheduledDesc fontWeight="normal" ></SwtLabel>
                  </GridItem>
                  <HBox horizontalAlign="right" width="50%">
                  <GridItem width="80">
                    <SwtButton [buttonMode]="true" id="addButton" #addButton (click)="addScheduleDetail()"  enabled="false" > </SwtButton>
                  </GridItem>
                  <GridItem width="80">
                    <SwtButton [buttonMode]="true" id="changeButton" #changeButton (click)="changeScheduleDetail()"  enabled="false"> </SwtButton>
                  </GridItem>
                  <GridItem width="80">
                    <SwtButton [buttonMode]="true" id="deleteButton" #deleteButton (click)="deleteRow()"  enabled="false"> </SwtButton>
                  </GridItem>
                  </HBox>
                </GridRow>
            <GridRow width="100%">
            <GridItem width="84.5%">
            <SwtRadioItem value="A" width="200" groupName="typeOptions" id="createInst" #createInst enabled ="false"></SwtRadioItem>
            </GridItem>
            <HBox horizontalAlign="right" width="10%">
              <SwtButton [buttonMode]="true" id="configButton" #configButton (click)="openConfigScreen()"  enabled="false"> </SwtButton>
            </HBox>
          
          </GridRow>
          <GridRow width="100%" >
            <GridItem width="450" paddingLeft="20">
              <SwtLabel #CreateInsDesc fontWeight="normal" ></SwtLabel>
            </GridItem>
          </GridRow>
          </SwtRadioButtonGroup>
        <!--</GridRow>-->
      
      
        </Grid>
      </SwtFieldSet>



 
    <Grid width="100%" paddingLeft="10" height="6%">
      <GridRow>
        <GridItem width="240">
          <SwtLabel #emailLbl></SwtLabel>
        </GridItem>
        <GridItem width="80">
          <SwtNumericInput #emailTxt textAlign="right" maxChars="3"></SwtNumericInput>
        </GridItem>
        <GridItem>
          <SwtLabel text="%" fontWeight="normal"></SwtLabel>
        </GridItem>
      </GridRow>
      </Grid>

  </VBox>
</SwtModule>
