<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingTop="5">
    <GridRow width="100%" height="100%" paddingBottom="10">
      <SwtCanvas #roleBasedControlGridContainer id="roleBasedControlGridContainer" styleName="canvasWithGreyBorder"
        width="100%" height="100%" border="false" minHeight="90" minWidth="270"></SwtCanvas>
    </GridRow>

    <SwtCanvas width="100%" height="35" minWidth="270">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="okButton" #okButton (click)="saveHandler()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="cancelButton" #cancelButton (click)=" cancelHandler()">
          </SwtButton>
        </HBox>
        <HBox width="10%" horizontalAlign="right" paddingRight="5">
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>

      </HBox>
    </SwtCanvas>

  </VBox>
</SwtModule>