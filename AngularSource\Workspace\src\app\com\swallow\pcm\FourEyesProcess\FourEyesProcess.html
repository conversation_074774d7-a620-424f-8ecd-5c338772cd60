<SwtModule (creationComplete)='onLoad()' width="500" height="95%" >
  <VBox height="100%" width="100%" paddingLeft="15" paddingTop="5" paddingRight="15" >
    <SwtCanvas width="100%" height="80%" >
        <VBox  paddinLeft="10"  paddingTop="25" height="100%">
            <HBox >
              <SwtLabel text="Validator ID*" width="120"></SwtLabel>
              <SwtTextInput required="true" restrict="a-zA-Z0-9\-_" #userNameTextInput width="250" toolTip="Enter User ID of validating user" ></SwtTextInput>
            </HBox>
            <HBox >
              <SwtLabel text="Password*" width="120"></SwtLabel>
              <SwtTextInput displayAsPassword="true" #passwordTextInput restict="A-Za-z0-9\d_ !\&quot;#$%&'()*+,\-.\/:;&lt;=&gt;?@[\\\]^`{|}~" width="250" maxChars="50" toolTip="Enter password associated with Validator ID"></SwtTextInput>
          </HBox>
          <HBox horizontalCenter="0">
             <!-- <SwtLabel  text="Unauthorised Access Prohibited" width="250"></SwtLabel> -->
         </HBox>
        </VBox>
    </SwtCanvas>
    <SwtCanvas id="canvasContainer" height="20%" width="100%">
        <HBox horizontalCenter="0">
          <SwtButton #loginButton  (click)="login()" (keyDown)="keyDownEventHandler($event)" id="loginButton" width="70"></SwtButton>
          <SwtButton buttonMode="true" id="cancelButton" (click)="closeCurrentTab($event)" width="70" #cancelButton  (keyDown)="keyDownEventHandler($event)"></SwtButton>
        </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>
