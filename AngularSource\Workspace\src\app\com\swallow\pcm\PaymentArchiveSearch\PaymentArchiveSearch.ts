import { <PERSON>mponent, OnInit, ViewChild, OnDestroy, ElementRef, ModuleWithProviders, NgModule } from '@angular/core';
import { SwtLabel, SwtModule, SwtAlert, JSONReader, HTTPComms, SwtUtil, TitleWindow, CommonService, SwtTextInput, Keyboard, SwtButton, SwtComboBox, SwtDateField, Alert, SwtRadioButtonGroup, SwtCheckBox, ExternalInterface, focusManager, SwtPopUpManager, SwtRadioItem, SwtToolBoxModule } from 'swt-tool-box';
import moment from "moment";
import { PartySearch } from '../PartySearch/PartySearch';
import { RouterModule, Routes } from '@angular/router';

/* declare var require: any;
 const $ = require('jquery');

 const select2 = require('select2');*/
 declare  function validateCurrencyPlaces(strField, strPat, currCode): any;
 declare function validateFormatTime(strField): any;

declare var instanceElement: any;
@Component({
  selector: 'payment-archive-search',
  templateUrl: './PaymentArchiveSearch.html',
  styleUrls: ['./PaymentArchiveSearch.css']
})
export class PaymentArchiveSearch extends SwtModule implements OnInit, OnDestroy {
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('messageFormatCombo') messageFormatCombo: SwtComboBox;
  @ViewChild('sourceCombo') sourceCombo: SwtComboBox;
  @ViewChild('categoryCombo') categoryCombo: SwtComboBox;
  // @ViewChild('statusCombo') statusCombo: SwtComboBox;
  @ViewChild('accountCombo') accountCombo: SwtComboBox;
  // @ViewChild('blokedCombo') blokedCombo: SwtComboBox;
  @ViewChild('acctGrpCombo') acctGrpCombo: SwtComboBox;

  @ViewChild('senderBicCombo') senderBicCombo: SwtTextInput;
  @ViewChild('receiverBicCombo') receiverBicCombo: SwtTextInput;
  @ViewChild('orderingInstBicCombo') orderingInstBicCombo: SwtTextInput;
  @ViewChild('beneficiaryInstBicCombo') beneficiaryInstBicCombo: SwtTextInput;


  @ViewChild('archiveCombo') archiveCombo: SwtComboBox;


  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('acagLabel') acagLabel: SwtLabel;
  @ViewChild('statusLabel') statusLabel: SwtLabel;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedAccount') selectedAccount: SwtLabel;
  @ViewChild('selectedAcctGrp') selectedAcctGrp: SwtLabel;
  @ViewChild('selectedCategory') selectedCategory: SwtLabel;
  @ViewChild('selectedSource') selectedSource: SwtLabel;
  @ViewChild('selectedMessageType') selectedMessageType: SwtLabel;
  @ViewChild('selectedArchive') selectedArchive: SwtLabel;

  @ViewChild('receiverBicButton') receiverBicButton: SwtButton;
  @ViewChild('beneficiaryInstBicButton') beneficiaryInstBicButton: SwtButton;
  @ViewChild('orderingInstBicButton') orderingInstBicButton: SwtButton;
  @ViewChild('senderBicButton') senderBicButton: SwtButton;

  @ViewChild('frmDateChooser') frmDateChooser: SwtDateField;
  @ViewChild('toDateChooser') toDateChooser: SwtDateField;


  @ViewChild('amountFromTextInput') amountFromTextInput: SwtTextInput;
  @ViewChild('amountToTextInput') amountToTextInput: SwtTextInput;
  @ViewChild('inputDatefrmDateChooser') inputDatefrmDateChooser: SwtDateField;
  @ViewChild('inputDateTimeFromTextInput') inputDateTimeFromTextInput: SwtTextInput;
  @ViewChild('inputDateTimeToTextInput') inputDateTimeToTextInput: SwtTextInput;


  @ViewChild('statusGroup') statusGroup: SwtRadioButtonGroup;
  @ViewChild('typeGroup') typeGroup: SwtRadioButtonGroup;



  @ViewChild('inlcudeLike') inlcudeLike: SwtCheckBox;
  @ViewChild('inlcudeSource') inlcudeSource: SwtCheckBox;
  @ViewChild('inlcudeFront') inlcudeFront: SwtCheckBox;
  @ViewChild('inlcudeBack') inlcudeBack: SwtCheckBox;
  @ViewChild('inlcudePayment') inlcudePayment: SwtCheckBox;
  @ViewChild('inlcudeRelated') inlcudeRelated: SwtCheckBox;
  @ViewChild('excludeLike') excludeLike: SwtCheckBox;
  @ViewChild('excludeSource') excludeSource: SwtCheckBox;
  @ViewChild('excludeFront') excludeFront: SwtCheckBox;
  @ViewChild('excludeBack') excludeBack: SwtCheckBox;
  @ViewChild('excludePayment') excludePayment: SwtCheckBox;
  @ViewChild('excludeRelated') excludeRelated: SwtCheckBox;


  @ViewChild('includeTextInput') includeTextInput: SwtTextInput;
  @ViewChild('excludeTextInput') excludeTextInput: SwtTextInput;


  @ViewChild('searchButton') searchButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  
  @ViewChild('timeFrameRadioGroup') timeFrameRadioGroup: SwtRadioButtonGroup ;
  @ViewChild('radioC') radioC: SwtRadioItem ;
  @ViewChild('radioE') radioE: SwtRadioItem ;
  @ViewChild('radioS') radioS: SwtRadioItem ;





  
  private swtAlert: SwtAlert;

  private dateFormat ;
  private currencyPattern ;

  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private saveData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];
  public screenName: string = null;
  public helpURL: string = null;
  private message: string = null;
  public title: string = null;
  private groupId: string = null;
  public searchQuery = "";
  public errorLocation = 0;
  private win: TitleWindow;

  private moduleId;


private includeRefValue;
private excludeRefValue; 

private inlcudeLikeFlag;
private excludeLikeFlag; 
private inlcudeSourceFlag;
private excludeSourceFlag;
private inlcudeFrontFlag;
private excludeFrontFlag;
private inlcudeBackFlag;
private excludeBackFlag;
private inlcudePaymentFlag;
private excludePaymentFlag;
private inlcudeRelatedFlag;
private excludeRelatedFlag;
  public dateFormatUpper: string;
  private selectedTimeFrame : string;
  private userDefaultEntity : string;
  
  
  
  
  
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }


  ngOnInit(): void {
    instanceElement = this; 
    this.receiverBicButton.label = "...";
    this.beneficiaryInstBicButton.label = "...";
    this.orderingInstBicButton.label = "...";
    this.senderBicButton.label = "...";

  }
  
  /**
   * inputDataFault
   * param event:  FaultEvent
   * This is a callback , used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    this.swtAlert.error(event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail);
  }

  onLoad(): void {
    try {

        this.requestParams = [];
        this.actionPath = "paymentSearchPCM.do?";
        this.actionMethod = "method=display";
        this.inputData.cbStart = this.startOfComms.bind(this);
        this.inputData.cbStop = this.endOfComms.bind(this);
        this.inputData.cbResult = (data) => {
          this.inputDataResult(data);
        };
        // this.inputDataResult(data);
        this.inputData.cbFault = this.inputDataFault.bind(this);
        this.inputData.encodeURL = false;
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);


        // this.inputDataResult(data);
        this.saveData.cbFault = this.inputDataFault.bind(this);
        this.saveData.encodeURL = false;
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "onLoad", this.errorLocation);
    }

  }
  changeLocalCombo(event): void {
    try {
      this.selectedAccount.text = this.accountCombo.selectedItem.value;
      this.selectedCategory.text = this.categoryCombo.selectedItem.value;
      this.selectedMessageType.text = this.messageFormatCombo.selectedItem.value;
      this.selectedSource.text = this.sourceCombo.selectedItem.value;
      this.selectedArchive.text = this.archiveCombo.selectedItem.value;

    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "onLoad", this.errorLocation);
    }

  }


  changeCombo(selectedCombo): void {
    try {

        this.requestParams = [];
        /*this.actionPath = "paymentSearchPCM.do?";
        this.actionMethod = "method=display";
        this.inputData.cbResult = (data) => {
          this.inputDataResult(data);
        };
        // this.inputDataResult(data);
        this.inputData.cbFault = this.inputDataFault.bind(this);
        this.inputData.encodeURL = false;
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;*/

        this.requestParams['currencyCode'] = this.ccyCombo.selectedItem.content;
        this.requestParams['accountGroup'] = this.acctGrpCombo.selectedItem.content;
        this.requestParams['entity'] = this.entityCombo.selectedItem.content;
        this.requestParams['account'] = this.accountCombo.selectedItem.content;

        this.requestParams['category'] = this.categoryCombo.selectedItem.content;
        this.requestParams['source'] = this.sourceCombo.selectedItem.content;
        this.requestParams['message'] = this.messageFormatCombo.selectedItem.content;
        this.requestParams['archive'] = this.archiveCombo.selectedItem.content;
        
        if(selectedCombo == "entityCombo"){
          this.requestParams['entityChanged'] = true;
        }else {
          this.requestParams['entityChanged'] = false;
        }

        this.inputData.send(this.requestParams);


        // this.inputDataResult(data);
        this.saveData.cbFault = this.inputDataFault.bind(this);
        this.saveData.encodeURL = false;
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "onLoad", this.errorLocation);
    }

  }



  inputDataResult(event): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {

        /* Get result as xml */
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        /* Condition to check lastRecievedXML not equal to prevRecievedXML */
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          /* Condition to check request reply status is true*/
          if (this.jsonReader.getRequestReplyMessage()) { 
            //gets the help url
            this.helpURL = this.jsonReader.getSingletons().helpurl;
            this.dateFormat =  this.jsonReader.getScreenAttributes()["dateformat"];
            this.dateFormatUpper = this.dateFormat.toUpperCase();
            this.currencyPattern =  this.jsonReader.getScreenAttributes()["currencyPattern"];
            this.userDefaultEntity = this.jsonReader.getScreenAttributes()["userDefaultEntity"];

            this.frmDateChooser.formatString = this.dateFormat.toLowerCase();
            this.toDateChooser.formatString = this.dateFormat.toLowerCase();
            this.inputDatefrmDateChooser.formatString = this.dateFormat.toLowerCase();


            if (!this.jsonReader.isDataBuilding()) {

              this.fillComboData();

              } else {
              }

            }
          }
      }
    } catch (error) {
      console.log("error:   ", error);
      SwtUtil.logError(error, this.moduleId, "className", "inputDataResult", this.errorLocation);
    }
  }

  fillComboData(): void {

    try{
    this.ccyCombo.setComboData( this.jsonReader.getSelects(), false);
    this.acctGrpCombo.setComboData(this.jsonReader.getSelects(), false);
    this.accountCombo.setComboData(this.jsonReader.getSelects(), false);
    this.sourceCombo.setComboData(this.jsonReader.getSelects(), false);
    this.categoryCombo.setComboData(this.jsonReader.getSelects(), false);
    
    this.messageFormatCombo.setComboData(this.jsonReader.getSelects(), false);
    this.entityCombo.setComboData( this.jsonReader.getSelects(), false);
    this.archiveCombo.setComboData( this.jsonReader.getSelects(), false);
    /*********Labels********/
    
    
    
    this.selectedAccount.text = this.accountCombo.selectedItem.value;
    this.selectedAcctGrp.text =  this.acctGrpCombo.selectedItem.value;
    this.selectedCcy.text = this.ccyCombo.selectedItem.value;
    this.selectedEntity.text = this.entityCombo.selectedItem.value;

    this.selectedCategory.text = this.categoryCombo.selectedItem.value;
    this.selectedMessageType.text = this.messageFormatCombo.selectedItem.value;
    this.selectedSource.text = this.sourceCombo.selectedItem.value;
    this.selectedArchive.text = this.archiveCombo.selectedItem.value;


    /*var parties ={"party":[{"label":"Auto created from Calypso","id":"ALLCGBG1"},{"label":"Auto created from Calypso","id":"ALLCGBL1"}]};

    


	  // init select 2
    $('#parentPartyCombo').select2({
      data: parties.party,
      placeholder: '',
      allowClear: true,   // Shows an X to allow the user to clear the value.
      multiple: false,
      // query with pagination
      query: function(q) {
        var pageSize,
          results,
          that = this;
        pageSize = 20; // or whatever pagesize
        results = [];
        if (q.term && q.term !== '') {
          // HEADS UP; for the _.filter function i use underscore (actually lo-dash) here
          results = _.filter(that.data, function(e) {
            if(e.text) {
              var text = ""+e.text;
                return text.toUpperCase().indexOf(q.term.toUpperCase()) >= 0;
            }else 
            {
              return false;
            }
          });
        } else if (q.term === '') {
          results = that.data;
        }
        q.callback({
          results: results.slice((q.page - 1) * pageSize, q.page * pageSize),
          more: results.length >= q.page * pageSize,
        });
      },
    });*/








    
    }catch(err) {
  console.log('err',err);
  }
  }


    /**
   * startOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    // this.loadingImage.setVisible(true);
  }

  /**
   * endOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    // this.loadingImage.setVisible(false);
  }

  doSearch() :void{

    if(!this.checkDates()) {
      this.swtAlert.warning('End Date must be later than Start date');
      return;
    }

    if(!this.checkAmounts()) {
      this.swtAlert.warning('Amount To must be greater than Amount From');
      return;
    }
    
    // let newWindow = window.open("/DashboardDetails", 'Dashboard Details', 'height=700,width=1250,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
    // if (window.focus) {
    //   newWindow.focus();
    // }

    ExternalInterface.call('openChildWindow', 'dashbordDetails');

  }

  formatDate(date:Date, format:String) {
    if(format && date) {
      if(format.toLowerCase()==="dd/mm/yyyy") {
        return (date.getDate())+"/"+(date.getMonth()+1)+"/"+date.getFullYear();
      }else {
        return (date.getMonth()+1)+"/"+(date.getDate())+"/"+date.getFullYear();
      }
    }else 
      return "";
  }
  

  getParamsFromParent() {
     // return this.screenName;

     let amountFrom: any = '' + this.amountFromTextInput.text;
     let amountTo: any = '' + this.amountToTextInput.text;
 
 
     if (this.currencyPattern == "currencyPat2") {
     if(amountFrom)
       amountFrom = Number(amountFrom.replace(/\./g, '').replace(/,/g, '.'));
      if(amountTo)
       amountTo = Number(amountTo.replace(/\./g, '').replace(/,/g, '.'));
     } else if (this.currencyPattern == "currencyPat1") {
      if(amountFrom)
       amountFrom = Number(amountFrom.replace(/,/g, ''));
      if(amountTo)
       amountTo = Number(amountTo.replace(/,/g, ''));
     }
 
     let fromDate = this.frmDateChooser.text;
     let toDate = this.toDateChooser.text;
 
    let messageFormat = this.messageFormatCombo.selectedItem.content;
    let source = this.sourceCombo.selectedItem.content;
    let category = this.categoryCombo.selectedItem.content;
 
     let senderBic = this.senderBicCombo.text;
     let receiverBic = this.receiverBicCombo.text;
     let orderingInstBic = this.orderingInstBicCombo.text;
     let beneficiaryInst = this.beneficiaryInstBicCombo.text;
 
     let inputTimeFrom = this.inputDatefrmDateChooser.text + " " + this.inputDateTimeFromTextInput.text;
     let inputTimeTo = this.inputDatefrmDateChooser.text + " " + this.inputDateTimeToTextInput.text;
 
    let inputTimeFromDateFormat = "";
    let inputTimeToDateFormat = "";

      inputTimeFromDateFormat = this.dateFormat.toLowerCase(); 
      inputTimeToDateFormat = this.dateFormat.toLowerCase(); 

        inputTimeFromDateFormat+= " HH24:MI";
        inputTimeToDateFormat+= " HH24:MI";

 
     let paymentStatus = this.statusGroup.selectedValue;
     let typePayment = this.typeGroup.selectedValue;
 
 
 
     let gridInitialFilter = "";
 
    if(amountFrom)
      gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "amount", amountFrom, '>=');

    if(amountTo)
       gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "amount", amountTo, '<=');
    
    
    if(fromDate) {
      gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "value_date", "TO_DATE ('"+fromDate +"' , '"+this.dateFormat.toLowerCase()+"')", ">=");
    }
    if(toDate) {
      gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "value_date", "TO_DATE('"+toDate+"', '"+this.dateFormat.toLowerCase()+"')", "<=");
    }

    this.selectedTimeFrame = this.timeFrameRadioGroup.selectedValue;



    if (this.inputDatefrmDateChooser.text) {
      if (!this.inputDateTimeFromTextInput.text.trim()) {
        inputTimeFrom = this.inputDatefrmDateChooser.text + " 00:00"
      }
          if(this.selectedTimeFrame === 'E') {
            gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "input_date", "PKG_PC_Tools.Fn_Get_Offset_Date_Ent(TO_DATE ('"+inputTimeFrom +"' , '"+inputTimeFromDateFormat+"'),'"+this.userDefaultEntity+"', 'REV')", ">=");
          }else if(this.selectedTimeFrame === 'C') {
              gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "input_date", "PKG_PC_TOOLS.FN_GET_DATE_IN_CCY_TIME(pr.ENTITY_ID, pr.CURRENCY_CODE, TO_DATE ('"+inputTimeFrom +"' , '"+inputTimeFromDateFormat+"'), 'REV')", ">=");
          }else{
              gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "input_date", "TO_DATE ('"+inputTimeFrom +"' , '"+inputTimeFromDateFormat+"')", ">=");
          }
      

      if (!this.inputDateTimeToTextInput.text.trim()) {
        inputTimeTo = this.inputDatefrmDateChooser.text + " 23:59";
      }
          if(this.selectedTimeFrame === 'E') {
            gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "input_date", "PKG_PC_Tools.Fn_Get_Offset_Date_Ent(TO_DATE ('"+inputTimeTo +"' , '"+inputTimeToDateFormat+"'),'"+this.userDefaultEntity+"', 'REV')", "<=");
          }else if(this.selectedTimeFrame === 'C') {
              gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "input_date", "PKG_PC_TOOLS.FN_GET_DATE_IN_CCY_TIME(pr.ENTITY_ID, pr.CURRENCY_CODE, TO_DATE ('"+inputTimeTo +"' , '"+inputTimeToDateFormat+"'), 'REV')", "<=");
          }else{
            gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "input_date", "TO_DATE ('"+inputTimeTo +"' , '"+inputTimeToDateFormat+"')", "<=");
          }

        }else {

      if (inputTimeFrom.trim() || inputTimeTo.trim()) {
        if (!this.inputDateTimeFromTextInput.text.trim()) {
          inputTimeFrom = "00:00";
        }else {
          inputTimeFrom  = inputTimeFrom.replace(/^\s+/,"");
      }
        if (!this.inputDateTimeToTextInput.text.trim()) {
          inputTimeTo = "23:59"
        }else {
          inputTimeTo  = inputTimeTo.replace(/^\s+/,"");
      }


          if(this.selectedTimeFrame === 'E') {
          gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "TO_CHAR (PKG_PC_Tools.Fn_Get_Offset_Date_Ent(input_date, '"+this.userDefaultEntity+"'),'HH24:MI')", "'"+inputTimeFrom+"'", ">=");
          }else if(this.selectedTimeFrame === 'C') {
          gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "TO_CHAR (PKG_PC_TOOLS.FN_GET_DATE_IN_CCY_TIME (pr.ENTITY_ID, pr.CURRENCY_CODE, input_date),'HH24:MI')", "'"+inputTimeFrom+"'" , ">=");
          }else{
          gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "input_date", "TO_DATE (TO_CHAR(pr.input_date, '" + this.dateFormat.toLowerCase() + "')||'" + inputTimeFrom + "' , '" + inputTimeFromDateFormat + "')", ">=");
        }
          

          if(this.selectedTimeFrame === 'E') {
          gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "TO_CHAR (PKG_PC_Tools.Fn_Get_Offset_Date_Ent(input_date, '"+this.userDefaultEntity+"'),'HH24:MI')", "'"+inputTimeTo+"'", "<=");
          }else if(this.selectedTimeFrame === 'C') {
          gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "TO_CHAR (PKG_PC_TOOLS.FN_GET_DATE_IN_CCY_TIME (pr.ENTITY_ID, pr.CURRENCY_CODE, input_date),'HH24:MI')",  "'"+inputTimeTo+"'", "<=");
          }else{
          gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "input_date", "TO_DATE (TO_CHAR(pr.input_date, '" + this.dateFormat.toLowerCase() + "')||'" + inputTimeTo + "' , '" + inputTimeToDateFormat + "')", "<=");
        }
        }
        
    }

 
     gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "message_type","'"+messageFormat +"'", "=");
     gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "source_id","'"+source +"'", "=");
    gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "cat.category_id","'"+category +"'", "=");
     gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "sender_bic","'"+senderBic +"'", "=");
     gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "receiver_bic","'"+receiverBic +"'" , "=");
     gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "ordering_inst_bic","'"+orderingInstBic +"'" , "=");
     gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "beneficiary_inst_bic","'"+beneficiaryInst +"'" , "=");
    if(typePayment != "B")
      gridInitialFilter = this.addConditionToFilter(gridInitialFilter, "acc.account_type","'"+typePayment +"'" , "=");
 
 

     let params = [];
     params = [
       {
        screenName: "search", currencyCode: this.ccyCombo.selectedItem.content,
        accountGroup: this.acctGrpCombo.selectedItem.content,
        account: this.accountCombo.selectedItem.content,
        entity: this.entityCombo.selectedItem.content,
         valueDate: null,
        status: paymentStatus != "A"? paymentStatus: "All",
        initialFilter: gridInitialFilter,
        refFilter:this.getxmlRef(),
        archive:this.archiveCombo.selectedItem.content,
        timeFrame:this.selectedTimeFrame
 
       },
     ]; 
    return params;


  }

  addConditionToFilter(filter, columnName, columnValue, condition) {
    
    if (!columnName || !columnValue || columnValue == "'All'"|| columnValue == "All" || columnValue == "''") {
      return filter;
    }

    if (!filter) {
      filter = columnName + " "+condition+" " + columnValue;
    } else {
      filter = filter + " AND " + columnName + " "+condition+" " + columnValue;
    }
    return filter;

  }


  //Return a string that contains a full reference chosen by user (includes and excludes)
// It will be like <refparams><include ref1="N" ref2="Y" ref3="Y" ref4="Y" like="Y">ABCD</include><exclude ref1="Y" ref2="Y" ref3="Y" ref4="Y" like="Y">EFGH</exclude></refparams>
getxmlRef() {

  this.includeRefValue = this.includeTextInput.text;
  this.excludeRefValue = this.excludeTextInput.text;


  this.inlcudeLikeFlag = this.inlcudeLike.selected?"Y":"N";
  this.excludeLikeFlag =  this.excludeLike.selected?"Y":"N";
  this.inlcudeSourceFlag = this.inlcudeSource.selected?"Y":"N";
  this.excludeSourceFlag = this.excludeSource.selected?"Y":"N";
  this.inlcudeFrontFlag = this.inlcudeFront.selected?"Y":"N";
  this.excludeFrontFlag = this.excludeFront.selected?"Y":"N";
  this.inlcudeBackFlag = this.inlcudeBack.selected?"Y":"N";
  this.excludeBackFlag = this.excludeBack.selected?"Y":"N";
  this.inlcudePaymentFlag = this.inlcudePayment.selected?"Y":"N";
  this.excludePaymentFlag = this.excludePayment.selected?"Y":"N";
  this.inlcudeRelatedFlag = this.inlcudeRelated.selected?"Y":"N";
  this.excludeRelatedFlag = this.excludeRelated.selected?"Y":"N";
  

	var includeArray = [this.inlcudeSourceFlag, this.inlcudeFrontFlag, this.inlcudeBackFlag, this.inlcudePaymentFlag, this.inlcudeRelatedFlag];
	var excludeArray = [this.excludeSourceFlag, this.excludeFrontFlag, this.excludeBackFlag, this.excludePaymentFlag, this.excludeRelatedFlag];

	var xmlDocument = "<refparams>";
	var includeNode = "<include ";
	var excludeNode = "<exclude ";
	for (var i=0;i<5;i++) {
	 	includeNode +=  "ref" + (i+1) + "=\"" + includeArray[i] + "\" ";
	    excludeNode +=  "ref" + (i+1) + "=\"" + excludeArray[i] + "\" ";
	}
	if (this.inlcudeLikeFlag == 'Y') {
		includeNode += "like=\"Y\" ><![CDATA[" + this.includeRefValue + "]]></include>";
	} else {
		includeNode += "like=\"N\" ><![CDATA[" + this.includeRefValue + "]]></include>";
	}
	if (this.excludeLikeFlag == 'Y') {
		excludeNode += "like=\"Y\" ><![CDATA[" + this.excludeRefValue + "]]></exclude>";
	} else {
		excludeNode += "like=\"N\" ><![CDATA[" + this.excludeRefValue + "]]></exclude>";
	}
	xmlDocument += includeNode + excludeNode;
	return (xmlDocument + "</refparams>");
}


  validateTime(textInput): any {
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text && validateFormatTime(textInput) == false) {
      this.swtAlert.warning('Please enter a valid time', null, Alert.OK , null, this.closeAlert.bind(this));
      return false;
    }  else {
      textInput.text = textInput.text.substring(0,5);
      return true;
    }

  }

  closeAlert(event): any {
    if(!validateFormatTime(this.inputDateTimeFromTextInput.text))
      this.inputDateTimeFromTextInput.setFocus();
    else if(!validateFormatTime(this.inputDateTimeToTextInput.text))
      this.inputDateTimeToTextInput.setFocus();
    }




  checkDates() {
    
    try {
      var startDate: any;
      var endDate: any;
      if (this.frmDateChooser.text) {
        startDate = moment(this.frmDateChooser.text, this.dateFormat.toUpperCase() , true);
      }
      if (this.toDateChooser.text) {
        endDate = moment(this.toDateChooser.text, this.dateFormat.toUpperCase() , true);
      }
      
      if(!startDate && endDate) {
        return false;
      }

      if (startDate && endDate && endDate.isBefore(startDate)) {
        return false;
      }

      return true;
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "checkDates", this.errorLocation);
   
    }
  }
  checkAmounts() {
    
    try {
      var amountFrom: any;
      var amountTo: any;


      amountFrom = ''+this.amountFromTextInput.text ;
      amountTo = ''+this.amountToTextInput.text ;

      if ((!amountFrom && amountTo) || (amountFrom && !amountTo) ||  (!amountFrom && !amountTo)) {
        return true;
      } else {

      if(this.currencyPattern == "currencyPat2"){
        amountFrom = Number(amountFrom.replace(/\./g, '').replace(/,/g, '.'));
        amountTo = Number(amountTo.replace(/\./g, '').replace(/,/g, '.'));
      }else if(this.currencyPattern == "currencyPat1") {
        amountFrom = Number(amountFrom.replace(/,/g, ''));
        amountTo = Number(amountTo.replace(/,/g, ''));
      }

        if(Number(amountTo)<Number(amountFrom)) {
          return false;
        }
      }


      return true;
    } catch (error) {
      console.log('err',error);
      SwtUtil.logError(error, this.moduleId, "className", "checkAmounts", this.errorLocation);
   
    }
  }


  validateAmount (input): any {

    
    if(input == "amountFromTextInput") {
      
      if (!(validateCurrencyPlaces(this.amountFromTextInput, this.currencyPattern, this.ccyCombo.selectedItem.value)) ) {
        this.swtAlert.warning('Please enter a valid amount');
        return false;
      }
    }else  {
      if (!(validateCurrencyPlaces(this.amountToTextInput, this.currencyPattern, this.ccyCombo.selectedItem.value)) ) {
        this.swtAlert.warning('Please enter a valid amount');
        return false;
      }
    }
    
   
  }



  /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(Button)
   */
  keyDownEventHandler(event): void {
    try {
      const eventstring = Object(focusManager.getFocus()).name;
      if ((event.keyCode === Keyboard.ENTER)) {
        if (eventstring === 'searchButton') {
          this.doSearch();
        } else if (eventstring === 'closeButton') {
          this.closeCurrentTab(event);
        } else if (eventstring === 'helpIcon') {
          this.doHelp();
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'ClassName', 'keyDownEventHandler',   this.errorLocation);
    }
  }


  partySelect(combo, label) {

   /* let comboList:String = combo.id;
    
    this.win = SwtPopUpManager.createPopUp(this, PartySearch, {
      title:'Party Search',
      viewOnly: false,
    });
    this.win.enableResize = false;
    this.win.id = "listValuesPopup";
    this.win.width = '600';
    this.win.height = '500';
    this.win.showControls = true;
    this.win.isModal = true;
    this.win.onClose.subscribe((res) => {
      if (this.win.getChild().result) {
        if (comboList == 'currencyList') {
        } else if (comboList == 'country') {
        } else if (comboList == 'counterParties') {
        } else if (comboList == 'source') {
        } else if (comboList == 'messageType') {
        } else if (comboList == 'AcctGrpList') {
        }
      }

    });
    this.win.display();
*/

this.selectedPartyCombo = combo;

//  var newWindow = window.open("/partySearch", 'Party Search', 'height=700,width=560,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
//       if (window.focus) {
//         newWindow.focus();
//       }

      ExternalInterface.call('openChildPartyWindow', 'partySearch');

      
  }

  private selectedPartyCombo : SwtTextInput ;

  setSelectedPartieItems(selectedItem){
    
    this.selectedPartyCombo.text  = selectedItem;
  }
  
  doHelp() {
    try {
      ExternalInterface.call("help");
    } catch (error) {
        SwtUtil.logError(error, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }
    
  }


  /**
   * closeCurrentTab
   *
   * Function called when close button is called
   *
   * @param event:Event
   */
  closeCurrentTab(event): void {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'ClassName', 'refreshGrid', this.errorLocation);
    }

  }

   /**
   * dispose
   *
   * This is an event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      ExternalInterface.call("close");
      if(this.titleWindow){
        this.close();
      }else {
        window.close();
      }
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'dispose', this.errorLocation);
    }
  }
}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: PaymentArchiveSearch }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PaymentArchiveSearch],
  entryComponents: []
})
export class PaymentArchiveSearchModule { }
