import {Component, ElementRef, ViewChild, OnInit} from '@angular/core';
import {
  Swt<PERSON>utton,
  JSONReader,
  SwtAlert,
  SwtUtil,
  CommonService,
  SwtModule,
  Alert,
  SwtCanvas,
  SwtCommonGrid, SwtPopUpManager, TitleWindow
} from 'swt-tool-box';
import {ThresholdAmounts} from "../ThresholdAmounts/ThresholdAmounts";
@Component({
  selector: 'PCLiquidityTab',
  templateUrl: './LiquidityTab.html',
  styleUrls: ['./LiquidityTab.css']
})
export class LiquidityTab extends SwtModule implements OnInit{

  @ViewChild('reserveCanvas') reserveCanvas: SwtCanvas;
  /*********SWtButton*************/
  @ViewChild('addRowBtn') addRowBtn: SwtButton;
  @ViewChild('changeRowBtn') changeRowBtn: SwtButton;
  @ViewChild('removeRowBtn') removeRowBtn: SwtButton;

  /***********SwtTextInput************/
  /*******Vbox********/
  /**
   * Data Objects
   **/
  public jsonReader= new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public screenName:string = null;
  public  title:string= null;
  public selectedRow: string;
  public reserveGrid: SwtCommonGrid;
  private swtAlert: SwtAlert;

  private win:TitleWindow;
  public parentDocument: any;


  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
ngOnInit(): void {
  this.addRowBtn.label = SwtUtil.getPredictMessage('button.add', null);
  this.changeRowBtn.label = SwtUtil. getPredictMessage('button.change', null);
  this.removeRowBtn.label = SwtUtil.getPredictMessage('button.delete', null);
}

  onLoad() {
    try{

      this.screenName = this.parentDocument.screenName;
     /* this.reserveGrid = <SwtCommonGrid>this.reserveCanvas.addChild(SwtCommonGrid);*/
      if (this.screenName == 'view') {
        this.addRowBtn.enabled = false;
      }

      this.reserveGrid.onRowClick = () => {
        this.cellClickEventHandler();
      };
      // this.reserveGrid.parentTabId = "liquidity";
    } catch(error){
    }

    this.reserveGrid.rowColorFunction = ( dataContext, dataIndex, color, dataField ) => {
      return this.drawRowBackground( dataContext, dataIndex, color , dataField);
    };
    
  }
  drawRowBackground( dataContext, dataIndex, color,dataField ): string {

    let rColor: string;
    try {
      let colorFlag: string;
    // if(dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.entity){
      if('Y'==dataContext.slickgrid_rowcontent[dataField].isDeletedRow){
        rColor = "#ff808a";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isNewRow){
        rColor = "#c6efce";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isUpdatedRow){
        rColor = "#ee82ee";
     }
      
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }

  cellClickEventHandler(): void {
    if (this.screenName != 'view') {
      if (this.reserveGrid.selectedIndex >=0 ) {
        this.changeRowBtn.enabled = true;
        this.removeRowBtn.enabled = true;

      } else {
        this.changeRowBtn.enabled = false;
        this.removeRowBtn.enabled = false;
      }
    }
     else {
      this.addRowBtn.enabled = false;
      this.changeRowBtn.enabled = false;
      this.removeRowBtn.enabled = false;
    }

  }
  addRow(): void {
  let messageSelectCurrency = SwtUtil.getPredictMessage('liquidityTab.alert.selectCurrency', null);
    try {
      if(this.parentDocument.currencyComboBox.selectedItem) {
        this.win =  SwtPopUpManager.createPopUp(this, ThresholdAmounts, {
          title: SwtUtil.getPredictMessage('liquidityTab.title.addReserve', null), // childTitle,
          screenName: 'add',
          dataProviderReserve: this.reserveGrid.dataProvider,
          currencyPattern : this.parentDocument.currencyPattern,
          currency : this.parentDocument.currencyComboBox.selectedLabel
        });
        this.win.isModal = true;
        this.win.enableResize = false;
        this.win.width = '500';
        this.win.height = '200';
        this.win.id = "addReserveWindow";
        this.win.showControls = true;
        this.win.onClose.subscribe(() => {
          if (this.win.getChild().result) {
            let newRow ={
              'time' :{ 'content' : String(this.win.getChild().result.time )},
              'reserve' :{ 'content' : String(this.win.getChild().result.reserve)},
              'useCL' :{ 'content' : String(this.win.getChild().result.useCL )},
              'idReserve' :{ 'content' : String(this.win.getChild().result.idReserve) }
          };
            this.reserveGrid.appendRow(newRow);
            this.changeRowBtn.enabled = true;
            this.removeRowBtn.enabled = true;


          }
        });

        this.win.display();
      } else {
        this.swtAlert.warning(messageSelectCurrency);
      }

    } catch(error) {
    }
  }
  changeRow(): void {
    try {
      this.win =  SwtPopUpManager.createPopUp(this, ThresholdAmounts, {
          title: SwtUtil.getPredictMessage('liquidityTab.title.addReserve', null), // childTitle,
          screenName: 'change',
          dataProviderSelectedIndex: this.reserveGrid.selectedItem,
         dataProviderReserve: this.reserveGrid.dataProvider,
        currencyPattern : this.parentDocument.currencyPattern,
        currency : this.parentDocument.currencyComboBox.selectedLabel
     });
      this.win.isModal = true;
      this.win.enableResize = false;
      this.win.width = '500';
      this.win.height = '200';
      this.win.id = "addReserveWindow";
      this.win.showControls = true;
      this.win.onClose.subscribe(() => {
          if (this.win.getChild().result) {
            let item = this.reserveGrid.getFilteredItems().find(x=>x.idReserve == this.reserveGrid.selectedItem.idReserve.content);
            item.time = this.win.getChild().result.time;
            item.reserve = this.win.getChild().result.reserve;
            item.useCL = this.win.getChild().result.useCL;

            item['slickgrid_rowcontent']['time'].content = this.win.getChild().result.time ;
            item['slickgrid_rowcontent']['reserve'].content = this.win.getChild().result.reserve;
            item['slickgrid_rowcontent']['useCL'].content = this.win.getChild().result.useCL ;
            this.reserveGrid.refresh();
            this.reserveGrid.selectedIndex = item.id;
            this.changeRowBtn.enabled = true;
            this.removeRowBtn.enabled = true;

          }
      });
      this.win.display();
    } catch(error) {
      console.log('error while changing row');
    }
  }
  removeRow(): void {
    let deleteRow = SwtUtil.getPredictMessage('cutOff.alert.deleteRow', null);
    try {
      this.swtAlert.confirm(deleteRow, 'Alert', Alert.YES | Alert.NO, null, this.gridRowRemove.bind(this));
    } catch (e) {
      console.log('error in delete', e);
    }
  }
  gridRowRemove(event): void {
    if (event.detail === Alert.YES) {
      this.reserveGrid.removeSelected();
      //this.reserveGrid.refresh();
      this.reserveGrid.selectedIndex = -1;
      this.changeRowBtn.enabled = false;
      this.removeRowBtn.enabled = false;
    }
  }

}
