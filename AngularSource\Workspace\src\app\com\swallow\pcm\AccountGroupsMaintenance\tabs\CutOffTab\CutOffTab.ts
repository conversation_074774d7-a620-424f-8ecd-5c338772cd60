import {Component, ElementRef, ModuleWithProviders, ViewChild, NgModule, OnInit} from '@angular/core';
import {
  HTTPComms,
  SwtButton,
  JSONReader,
  SwtAlert,
  SwtUtil,
  SwtTextInput,
  CommonService,
  SwtModule,
  Alert,
  SwtCanvas,
  SwtCommonGrid,
  VBox,
  SwtLabel,
  SwtPopUpManager, TitleWindow
} from 'swt-tool-box';
import {CutOffBuildQuery} from "../CutOffBuildQuery/CutOffBuildQuery";
import moment from "moment";
declare  function validateFormatTime(strField): any;
@Component({
  selector: 'PCCutOffTab',
  templateUrl: './CutOffTab.html',
  styleUrls: ['./CutOffTab.css']
})
export class CutOffTab extends SwtModule implements OnInit{
  @ViewChild('cutOffCanvas') cutOffCanvas: SwtCanvas;
  /******SwtButtons**********/
  @ViewChild('addRow') addRow: SwtButton;
  @ViewChild('changeRow') changeRow: SwtButton;
  @ViewChild('deleteRow') deleteRow: SwtButton;
  @ViewChild('viewRow') viewRow: SwtButton;
  /*********SwtTimeInput*************/
  @ViewChild('kickoffTimeInput') kickoffTimeInput: SwtTextInput;
  @ViewChild('eodBeginTimeInput') eodBeginTimeInput: SwtTextInput;
  @ViewChild('cobTimeInput') cobTimeInput: SwtTextInput;
  /******VBOx************/
  @ViewChild('gridButtons') gridButtons: VBox;
  /****SwtLabel*********/
  @ViewChild('kickoffLabel') kickoffLabel: SwtLabel;
  @ViewChild('eodLabel') eodLabel: SwtLabel;
  @ViewChild('cobLabel') cobLabel: SwtLabel;
  @ViewChild('intradayLabel') intradayLabel: SwtLabel;
  @ViewChild('endOfDayLabel') endOfDayLabel: SwtLabel;


  /**
   * Data Objects
   **/
  public jsonReader= new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public screenName:string = null;
  public  title:string= null;
  public cutOffGrid: SwtCommonGrid;
  private swtAlert: SwtAlert;
  private win:TitleWindow;
  public parentDocument: any;

  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  ngOnInit(): void {
    this.kickoffLabel.text = SwtUtil.getPredictMessage('cutOffTab.label.kickOff', null) ;
    this.kickoffTimeInput.toolTip = SwtUtil. getPredictMessage('cutOffTab.tooltip.kickOff', null);
    this.eodLabel.text = SwtUtil.getPredictMessage('cutOffTab.label.intradayReleasePhase', null);
    this.eodBeginTimeInput.toolTip = SwtUtil.getPredictMessage('cutOffTab.tooltip.intradayReleasePhase', null);
    this.cobLabel.text = SwtUtil.getPredictMessage('cutOffTab.label.cob', null);
    this.cobTimeInput.toolTip = SwtUtil.getPredictMessage('cutOffTab.tooltip.cob', null);
    this.intradayLabel.text = SwtUtil.getPredictMessage('cutOff.label.intraday', null);
    this.endOfDayLabel.text = SwtUtil.getPredictMessage('cutOff.label.endOfDay', null);
    this.addRow.label = SwtUtil.getPredictMessage('button.add', null);
    this.changeRow.label = SwtUtil.getPredictMessage('button.change', null);
    this.deleteRow.label = SwtUtil.getPredictMessage('button.delete', null);

  }

  onLoad() {

    try{
      this.screenName = this.parentDocument.screenName;
      this.cutOffGrid.onRowClick = () => {
        this.cellClickEventHandler();
      };

      if (this.screenName == 'view') {
        this.enableDisableComponent(false);
        this.addRow.enabled = false;
      } else {
        this.enableDisableComponent(true);
      }
      //this.cutOffGrid.parentTabId = "cutOff";
    } catch(error){

    }

    this.cutOffGrid.rowColorFunction = ( dataContext, dataIndex, color, dataField ) => {
      return this.drawRowBackground( dataContext, dataIndex, color , dataField);
    };
  }


  drawRowBackground( dataContext, dataIndex, color,dataField ): string {

    let rColor: string;
    try {
      let colorFlag: string;
    // if(dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.entity){
      if('Y'==dataContext.slickgrid_rowcontent[dataField].isDeletedRow){
        rColor = "#ff808a";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isNewRow){
        rColor = "#c6efce";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isUpdatedRow){
        rColor = "#ee82ee";
     }
      
    // }
      
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }

  enableDisableComponent(value: boolean): void {
    this.kickoffTimeInput.enabled = value;
    this.eodBeginTimeInput.enabled = value;
    this.cobTimeInput.enabled = value;
  }
  addRowCutOff(): void {
    this.lastOperation = "add";
    let validTimeMessage = SwtUtil.getPredictMessage('cutOff.alert.time', null);

    try {
      if (this.validateTime(this.cobTimeInput) && this.cobTimeInput.text ) {
        this.win =  SwtPopUpManager.createPopUp(this, CutOffBuildQuery, {
          title: SwtUtil.getPredictMessage('cutOff.title.addRule', null), // childTitle,
          screenName: 'add',
          dataProviderCutOff: this.cutOffGrid.dataProvider
        });
        this.win.isModal = true;
        this.win.enableResize = false;
        this.win.width = '700';
        this.win.height = '400';
        this.win.showControls = true;
        this.win.id = "addCutoFFTitleWindow";
        this.win.display();
      }
      else {
        if(this.cobTimeInput.text == ""  ) {
          this.swtAlert.warning(validTimeMessage);
        } else {
          return;
        }

      }


    } catch (e) {
      console.log('error add', e);
    }
  }

  refreshParent(testOrder, cutOff, queryText, ruleQuery, logText, tabConditions, tabToJoin) {
    let index = 0;
    if(this.lastOperation == 'add') {
      let newRow ={
        'testOrder' : {'content' : String(testOrder)},
        'cutOffTime' :{'content' :  String(cutOff)},
        'ruleText' :{'content' :  String(queryText)},
        'ruleQuery' :{'content' :  String(ruleQuery)},
        'logText' :{'content' :  String(logText)},
        'ruleCondition' :{'content' : String(tabConditions)},
        'tabToJoin' :{'content' :  String(tabToJoin)},
      };
      this.cutOffGrid.appendRow(newRow);
      if(this.cutOffGrid.dataProvider.length ==1) {
        this.changeRow.enabled = true;
        this.deleteRow.enabled = true;
        this.viewRow.enabled = true;
      }
    }else{

      index =   this.cutOffGrid.dataProvider.findIndex(x=>x.num == this.cutOffGrid.selectedItem.num.content );
      this.cutOffGrid.dataProvider[index].testOrder= String(testOrder);
      this.cutOffGrid.dataProvider[index]['slickgrid_rowcontent']['testOrder']=  {content: String(testOrder)} ;
      this.cutOffGrid.dataProvider[index].cutOffTime = cutOff;
      this.cutOffGrid.dataProvider[index]['slickgrid_rowcontent']['cutOffTime'] =  {content: cutOff } ;
      this.cutOffGrid.dataProvider[index].ruleText = queryText;
      this.cutOffGrid.dataProvider[index]['slickgrid_rowcontent']['ruleText']=  {content: queryText };
      this.cutOffGrid.dataProvider[index].ruleQuery = ruleQuery;
      this.cutOffGrid.dataProvider[index]['slickgrid_rowcontent']['ruleQuery']=  {content: ruleQuery} ;
      this.cutOffGrid.dataProvider[index].logText = logText;
      this.cutOffGrid.dataProvider[index]['slickgrid_rowcontent']['logText']=  {content: logText };
      this.cutOffGrid.dataProvider[index].ruleCondition = tabConditions;
      this.cutOffGrid.dataProvider[index]['slickgrid_rowcontent']['ruleCondition']=  {content: tabConditions} ;
      this.cutOffGrid.dataProvider[index].tabToJoin = tabToJoin;
      this.cutOffGrid.refresh();
      this.cutOffGrid.selectedIndex = index ;
    }
  }

  private lastOperation = "";

  changeRowCutOff(): void {
    try {
      let index =   this.cutOffGrid.dataProvider.findIndex(x=>x.num == this.cutOffGrid.selectedItem.num.content );
      this.lastOperation = "change";
      this.win =  SwtPopUpManager.createPopUp(this, CutOffBuildQuery, {
        title: SwtUtil.getPredictMessage('cutOff.title.changeRule', null), // childTitle,
        screenName: 'change',
        dataProviderSelectedIndex: this.cutOffGrid.dataProvider[index],
        dataProviderCutOff: this.cutOffGrid.dataProvider

      });
      this.win.isModal = true;
      this.win.enableResize = false;
      this.win.width = '700';
      this.win.height = '400';
      this.win.showControls = true;
      this.win.id = "addCutoFFTitleWindow";

      this.win.display();

    } catch (e) {
      console.log('error add', e);
    }

  }
  deleteRowCutOff(): void {
    try {
      let message = SwtUtil.getPredictMessage('cutOff.alert.deleteRow', null);
      Alert.yesLabel = "Yes";
      Alert.noLabel = "No";
      this.swtAlert.confirm(message, 'Alert', Alert.YES | Alert.NO, null, this.cutOffGridRowRemove.bind(this));
    } catch (e) {
      console.log('error in delete', e);
    }

  }
  cutOffGridRowRemove(event) {
    if (event.detail === Alert.YES) {
      this.cutOffGrid.removeSelected();
      this.cutOffGrid.selectedIndex = -1;
      this.changeRow.enabled = false;
      this.deleteRow.enabled = false;
      this.viewRow.enabled = false;
    }
  }
  viewRowCutOff(): void {
    try {
      this.lastOperation = "view";
      let index =   this.cutOffGrid.dataProvider.findIndex(x=>x.num == this.cutOffGrid.selectedItem.num.content );
      this.win =  SwtPopUpManager.createPopUp(this, CutOffBuildQuery, {
        title: SwtUtil.getPredictMessage('cutOff.title.viewRule', null), // childTitle,
        screenName: 'view',
        dataProviderSelectedIndex: this.cutOffGrid.dataProvider[index],
        dataProviderCutOff: this.cutOffGrid.dataProvider

      });
      this.win.isModal = true;
      this.win.enableResize = false;
      this.win.width = '700';
      this.win.height = '400';
      this.win.showControls = true;
      this.win.display();
    } catch (e) {
      console.log('error add', e);
    }
  }
  cellClickEventHandler(): void {
    if (this.screenName !== 'view') {
      if ( this.cutOffGrid.selectedIndex >=0) {
        this.changeRow.enabled = true;
        this.deleteRow.enabled = true;
        this.viewRow.enabled = true;

      } else {
        this.changeRow.enabled = false;
        this.deleteRow.enabled = false;
        this.viewRow.enabled = false;
      }
    } else {
      this.addRow.enabled = false;
      this.changeRow.enabled = false;
      this.deleteRow.enabled = false;
      this.viewRow.enabled = this.cutOffGrid.selectedIndex >=0;
    }

  }


  validateTime(textInput): any {
    let kickOffasTime;
    let eodAsTime;
    let cobAsTime;
    let validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
    let eodSupKickOff = SwtUtil.getPredictMessage('alert.eodSupKickOff', null);
    let cobSupEod = SwtUtil.getPredictMessage('alert.cobSupEod', null);
    let cobSupKickOff = SwtUtil.getPredictMessage('alert.cobSupKickOff', null);
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text  && validateFormatTime(textInput) == false) {
      this.swtAlert.warning(validTimeMessage, null );
      //textInput.text = "";
      return false;
    } else  {
     textInput.text = textInput.text.substring(0,5);
      if(this.kickoffTimeInput.text)
        kickOffasTime = moment(this.kickoffTimeInput.text, 'HH:mm');
      if(this.eodBeginTimeInput.text)
        eodAsTime = moment(this.eodBeginTimeInput.text, 'HH:mm');
      if(this.cobTimeInput.text)
        cobAsTime  = moment(this.cobTimeInput.text, 'HH:mm');
      if (eodAsTime !== undefined && kickOffasTime !== undefined && (eodAsTime.isBefore(kickOffasTime) || this.eodBeginTimeInput.text == this.kickoffTimeInput.text) ) {
        this.swtAlert.warning(eodSupKickOff);
        return false;
      }
      else if(cobAsTime !== undefined && eodAsTime !== undefined && (cobAsTime.isBefore(eodAsTime)) ) {
        this.swtAlert.warning(cobSupEod);
        return false;
      }
      else if(cobAsTime !== undefined && kickOffasTime !== undefined && cobAsTime.isBefore(kickOffasTime)) {
        this.swtAlert.warning(cobSupKickOff);
        return false;
      } else  {
        return true;
      }

    }
  }

}
