import { J<PERSON><PERSON>eader, CommonService } from 'swt-tool-box';
import { ElementRef } from '@angular/core';

/**
 * new Json reader class for generic due to some additional s for pages, filter, sort
 * and intial input screen
 * 
 */
export class <PERSON>ricJSONReader extends J<PERSON><PERSON>eader {



    public GenericJSONReader() {

    }

    public getPages(): any {
        return this.getInputJSON().value.pages;
    }

    public getMaxPages(): number {
        return this.getInputJSON().value.pages.maxPage;
    }

    public getCurrent_Page(): number {
        return this.getInputJSON().value.pages.currentPage;
    }
    public getSelectedFilter(): string {

        return this.getInputJSON().value.selectedFilter;
    }

    public getInitialInputScreen(): string {
        return this.getInputJSON().value.initialinputscreen;
    }

    public getSelectedSort(): string {
        return this.getInputJSON().value.selectedSort;
    }

    /**
		 * This function gets the font size from the generated xml
		 */
    public getFontSize(): string {
        return this.getInputJSON().value.currfontsize;
    }
}