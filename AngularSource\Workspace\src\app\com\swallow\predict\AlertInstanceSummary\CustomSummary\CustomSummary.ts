import { CustomTreeItem } from 'swt-tool-box/src/app/modules/swt-toolbox/com/swallow/controls/swt-custom-tree.component';
import { Component, OnInit, ModuleWithProviders, NgModule, OnDestroy, ElementRef, Input, ViewChild, Renderer2 } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AlertInstanceSummary } from '../AlertInstanceSummary';
import { SwtToolBoxModule, SwtModule, CommonService, Logger, SwtAlert, SwtLabel, HDividedBox, CustomTree, VBox, SwtCanvas, SwtCommonGrid, JSONReader, SwtUtil, CustomTreeEvent, Keyboard, StringUtils } from 'swt-tool-box';
import { element } from '@angular/core/src/render3';
declare var require: any;
var _ = require('lodash');
@Component({
  selector: 'app-custom-summary',
  templateUrl: './CustomSummary.html',
  styleUrls: ['./CustomSummary.css']
})
export class CustomSummary extends SwtModule implements OnInit, OnDestroy { 

  @Input('styleName') styleName: string;
    @Input('IDField') IDField: string;
    @Input('expandFirstLoad') expandFirstLoad: boolean = true;
    @ViewChild('gridTitle') gridTitle: SwtLabel;
    @ViewChild('treeTitle') treeTitle: SwtLabel;
    @ViewChild('lastRanLbl') lastRanLbl: SwtLabel;
    @ViewChild("divBox") divBox: HDividedBox;
    @ViewChild('tree') tree: CustomTree;
    @ViewChild('mainHGroup') mainHGroup: VBox;
    @ViewChild('treeContainer') treeContainer: VBox;
    @ViewChild('customGrid') customGrid: SwtCanvas;
    @ViewChild('summaryGrid') summaryGrid: SwtCommonGrid;
    @ViewChild('gridContainer') gridContainer: VBox;

    /**
     * JSON Objects
     **/
    private  jsonReader=new JSONReader();
    private  lastRecievedJSON;
    private  prevRecievedJSON;

    /**
     * Communication Objects
     **/
    public baseURL: string = "";
    public actionMethod: string = "";
    public actionPath: string = "";
   private facilityAccess: boolean =false;
    private supportAllCurrency: boolean =false;
    private supportAllEntity: boolean = false;

    /**
     * Summary Tree Objects
     **/
    public treeOpenedItems =[];
    public  treeClosedItems=[];
    private  gridOpenedItems=[];
    private  gridVisibleItems=[];
    private  verticalScrollPosition:number;
    public lastSelectedItem:any;
    public currentDivPos=-1;
    public  facilityId="";
    public  facilityName="";
    public  useGeneric="";
    public  scenarioTitle="";
    public selectedscenario = "";
    public rcdScenInstance = "";
    private resetFilter=false;
    private swtAlert: SwtAlert;
    private logger: Logger;
    private columnData = [];

    //-------constructor-----------------------------------------------------------//
    constructor(private elem: ElementRef, private commonService: CommonService, private _renderer: Renderer2) {
        super(elem, commonService);
        this.logger = new Logger('SwtSummary', this.commonService.httpclient);
        this.swtAlert = new SwtAlert(commonService);
    }

    onLoad(){
        
    }
    public ngOnDestroy(): void {
        this.logger.info('method [ngOnDestroy] : unscbscribe all Observables.  - START/END -');
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.summaryGrid.uniqueColumn = "expand";
        this.summaryGrid.lockedColumnCount = 1;
        this.summaryGrid.selectable = true;
        this.summaryGrid.enableRowSelection = false;

        this.setLabelField("label");
        this.hideRoot(true);
        this.showTreeDataTips(true);
        this.tree.saveTreeStateBasedOn = "id";
        
        this.tree.level0Order = 'disp_order_cat=ASC';
        this.tree.level1Order = 'disp_order_scen=ASC';
        this.IDField="id";
        this.gridTitle.text = SwtUtil.getPredictMessage('scenarioSummary.selectedScen', null);
        this.gridTitle.toolTip = SwtUtil.getPredictMessage('tooltip.selectedScen', null);
        this.treeTitle.toolTip =  SwtUtil.getPredictMessage('tooltip.scenTotals', null);
        this.treeTitle.text =  SwtUtil.getPredictMessage('scenarioSummary.scenTotals', null);

        this.tree.addEventListener(CustomTreeEvent.ITEMRENDER, (item) => {
            let name:string = item.data.name;
            let ALERTABLE = "Y";
            let alert = item.data.alert;
            let isBranch= item.data.isBranch;
            if (isBranch == true){
                item.setStyle("fontWeight", "normal");
                item.setStyle("fontSize", 12);
                item.setStyle("color", "black");
            }else{
                if (alert == ALERTABLE){
                    item.setStyle("fontSize", 12);
                    setTimeout(() => {
                        $($(item.node.span).find(".fancytree-custom-icon")).css('background-image', "url('assets/images/predict_images/alert.png')");
                    }, 0);
                }else{
                    item.setStyle("fontWeight", "normal");
                    item.setStyle("fontSize", 12);
                    setTimeout(() => {
                        $($(item.node.span).find(".fancytree-custom-icon")).css('background-image', "url('assets/images/predict_images/treefile.png')");
                        
                    }, 0);

                }
            }
        });

        // adding listener on expand or collapse icon click on summaryGrid control
        this.tree.addEventListener(CustomTreeEvent.ITEMDOUBLECLICK, (item: CustomTreeItem) => {
            
            this.treeKeyDownHandler(item);
        });

        this.summaryGrid.ITEM_CLICK.subscribe((event) => {
            if(this.rcdScenInstance!="Y"){
            this.changeTreeData(event);
            }
        });

    }


   setLabelField(label:string):void {
       //this.tree.labelField='@' + label;
    }

  hideRoot(hide:boolean):void {
        if (hide == true){
            this.tree.showRoot=false;
        } else {
            this.tree.showRoot=true;
        }
    }

    /**
     * getSortedGridColumn
     * This method is called to get the selected sort in the datagrid
     **/
   public getSortedGridColumn():string {
        var selectedSort:string=null;
        var direction:string=null;
        direction= this.summaryGrid.sortDirection ? "DESC" : "ASC";
        if(this.summaryGrid.getMsdSortedGridColumn() && this.summaryGrid.getMsdSortedGridColumn().split('|').length>1){
            selectedSort=this.summaryGrid.getMsdSortedGridColumn().split('|')[0] + "|" + direction;
        }else{
            selectedSort="1|DESC"
        }

        return selectedSort;
    }

    /**
     * getIsScenarioAlertable
     * This method is used to found out that selected scenario is alertable or not
     **/
   public getIsScenarioAlertable():string {
        var isAlertable:string ="";
       if(this.tree.selectedItem){
           var value = this.tree.selectedItem.data;
           isAlertable = value.alert=='Y'?'Y':'N' ;
       }
        return isAlertable;
    }

    /**
     * getIsBranch
     *
     * This method is used to found out that seletced node is branch or not
     **/
    public getIsBranch():string {
        var isBranch:string ="";
        var value = this.tree.selectedItem.data;
        isBranch = value.isBranch =='true'?'true':'false';

        return isBranch;
    }

    /**
     * getDividerPosition
     *
     * This method is called to get the divider current position
     **/
   public getDividerPosition():string {
        return this.currentDivPos.toString();
    }
    /**
     * setActionPath
     *
     * @param _actionPath:String
     *
     * This method is called to set actionPath to save controls user prefrerence
     **/
    public setActionPath(actionPath:string):void {
        this.actionPath=actionPath;
    }

    /**
     * setBaseURL
     *
     * This method is called to set baseURL
     **/
    public setBaseURL(baseURL:string):void {
        this.baseURL=baseURL;
    }


    public setAccessRules(facilityAcc: boolean, entityAcc: boolean, ccyAcc: boolean): void {
        // cellRenderer.properties={facilityAccess:facilityAcc,_supportAllEntity:entityAcc,_supportAllCurrency:ccyAcc};
    }

    /**
     * getSelectedItemID
     *
     * This method is called to get the ID of the selected item on the tree
     **/
    public  getSelectedItemID():string {
        // var to hold the tree selected item
        var selectedItem:CustomTreeItem = this.tree.selectedItem;

        /* Check if  the selectedItem is not null*/
        if (selectedItem != null)
        {
            // check if the node is not branch
            if(selectedItem.data.isBranch == false)
                return selectedItem.data.id;
        }

        return null;
    }




    /**
     * Return the first subNode of the tree
     **/
    public  setFirstSubNode():void {
        if (this.jsonReader.getScreenAttributes()["selectedscenario"] !== "") {
            if(this.jsonReader.getTreeData()[0].root.node[0]){
                this.lastSelectedItem = this.jsonReader.getTreeData()[0].root.node[0].node;
            } else {
                this.lastSelectedItem = this.jsonReader.getTreeData()[0].root.node.node;
            }
            this.tree.selectedIndex = 1
        }
    }

    /**
     * enableTree
     *
     * This method is used to enable selection on tree control
     **/
    public enableTree():void {
        this.tree.selectable=true;
    }

    /**
     * disableTree
     *
     * This method is used to disable selection on tree control
     **/
    public disableTree():void {
        // disable the tree selection
        this.tree.selectable = false;
    }



    /**
     * showTreeDataTips
     * @param:boolean
     * This method is used enable/disable tooltips on tree node
     **/
    showTreeDataTips(show:boolean):void {
        // this.tree.showDataTips = show;
        this.tree.dataTipFunction = this.dataTipFunction;
    }

    /**
     * dataTipFunction
     * @param: Object
     * This function is used to set tooltip of tree node
     */
   dataTipFunction(item:CustomTreeItem):string  {
        return (item.data.desc)? item.data.desc: "";
    }

    /**
     * treeNodeEventHandler
     * @param event:Event
     * This method is called when opening or closing a tree node
     **/
    treeNodeEventHandler(event):void {
        this.saveTreeOpenState();
    }

    /**
     * saveTreeOpenState
     *
     * used to save the tree node states
     */
    public  saveTreeOpenState():void {
        // Initialise the open list array
        this.treeOpenedItems =[];
        this.treeClosedItems=[];
        if (this.tree.selectedItem != null) {
            if(this.lastSelectedItem){
                if (this.tree.selectedItem.id !== this.lastSelectedItem.id){
                    this.lastSelectedItem = this.tree.selectedItem ;
                    this.resetFilter = true;
                } else {
                    this.resetFilter = false;
                }
            }
        }
        this.tree.saveTreeOpenState();
        if(this.tree.openItems.length > 0) {
            for (let i=0; i < this.tree.openItems.length; i++) {
                this.treeOpenedItems.push(this.tree.openItems[i]);
            }
        }

        if(this.tree.closeItems.length > 0) {
            for (let i=0; i < this.tree.closeItems.length; i++) {
                this.treeClosedItems.push(this.tree.closeItems[i]);
            }
        }
    }


    /**
     * openTreeItems
     * used to maintain the tree node
     */
    public openTreeItems():void{
            this.tree.openItems = [];
            for (let i=0; i < this.treeOpenedItems.length; i++) {
                this.tree.openItems.push(this.treeOpenedItems[i]);
            }
            this.tree.closeItems = [];
            for (let i=0; i < this.treeClosedItems.length; i++) {
                this.tree.closeItems.push(this.treeClosedItems[i]);
            }
            this.tree.reOpenSavedState();
    }


    /**
     * treeScrollEventHandler
     * @param event:ScrollEvent
     * This method is called when a scroll event occurs on the tree
     **/
    treeScrollEventHandler(event):void {
        this.verticalScrollPosition=this.tree.verticalScrollPosition;
    }


    /**
     * treeItemDoubleClickHandler
     *
     * @param event:ListEvent
     *
     * This method is used to expand tree when double clicking on a node
     **/
    treeItemDoubleClickHandler(event): void {
        let node =this.tree.selectedItem;
        let isOpen: boolean = this.isItemOpen(node);
        this.tree.expandItem(node, !isOpen);
    }

    isItemOpen(item): boolean {
        return item.isExpanded();
    }





    /**
     * changeTreeData
     *
     * @param: TreeEvent
     *
     * This method is called when the expand/collapse icon is clicked on summaryGrid
     */
    changeTreeData(event): void {
        let colIndex: number = event.columnIndex;
        let expand: boolean = event.target.data.slickgrid_rowcontent['expand'].content == "Y";
        if (colIndex == 0 && expand ) {
            let entity:string = event.target.data.entity;
            for (let i =0; i < this.lastRecievedJSON.scenarioinstancedetails.grid.rows.size; i++) {
                var xmlElement =  this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row[i];

                if (xmlElement && xmlElement['entity'] && entity == xmlElement['entity'].content) {
                        this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row[i].expand.opened = ! xmlElement.expand.opened;
                }
                if (this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row[i][this.summaryGrid.GroupId] && entity == this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row[i][this.summaryGrid.GroupId].content && this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row[i].expand.content != 'Y') {
                        this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row[i].visible = !xmlElement.visible;
                }
            }

            if (this.lastRecievedJSON.scenarioinstancedetails.grid.rows.size != 1 ){
                let grid2dataset = [];
                JSONReader.jsonpath(this.lastRecievedJSON,'$.scenarioinstancedetails.grid.rows.row.*').forEach(function (value) {
                    if(value.visible == true || value.visible == "true"){
                        if(value === Object(value)){
                            grid2dataset.push(value);
                        }
                    }
                });
                this.summaryGrid.gridData  = { row: grid2dataset, size: grid2dataset.length };
            }else{
                this.summaryGrid.gridData  =  this.jsonReader.getGridData();
                this.summaryGrid.setRowSize  =  this.jsonReader.getRowSize();
            }
        }
    }


    treeKeyDownHandler(event): void {

        let node = this.tree.selectedItem;
        let itemIsBranch: boolean = node.data.isBranch;
        // Check if event key is ENTER
        if (event.keyCode == Keyboard.ENTER ||event.keyCode==Keyboard.UP|| event.keyCode==Keyboard.DOWN) {
            if (!itemIsBranch) {
                // this.summaryGrid.applyFilterSort = true;
                dispatchEvent(new Event("treeItemClick"));

            } else {
                this.resetGridData();
            }
        }
    }

    /**
     * resetGridData
     *
     * This method is used to empty the summary grid
     **/
    resetGridData(): void {
        // nullify the gridData
        this.summaryGrid.gridData = null;
        // validate properties
        // this.summaryGrid.validateProperties();
    }


    /**
     * dataProvider
     *
     * @param data:XML
     *
     * This method used to set data for swtSummary controls
     **/
    private previousTreeData = null;
    dataProvider(data): void {
        try {
        let dividerPos: string;
        let lastSelectedIndex;
        if (data != null) {
            this.lastRecievedJSON = data;
            this.jsonReader.setInputJSON(this.lastRecievedJSON);
                this.summaryGrid.forceHeaderRefresh=true;
                if(this.lastRecievedJSON.scenarioinstancedetails.tree){
                    let  treeData = this.lastRecievedJSON.scenarioinstancedetails.tree.root.node;
                    if (!(_.isEqual(treeData, this.previousTreeData))) {                    
                        this.previousTreeData =treeData;
                        if (this.tree.openItems.length > 0) {
                            // this.saveTreeOpenState();
                        }else{
                            if(this.expandFirstLoad){
                                // expand tree to first level
                                setTimeout(()=>{
                                    this.tree.expandAll(CustomTree.LEVEL_1_STR);
                                },0);
                            }
                        }
                        if(treeData == undefined){
                            treeData = []  ;
                        }
                        this.tree.dataProvider = treeData;
                        this.setTreeTotals(this.jsonReader.getSingletons()["total"]);
                        // this.tree.validateProperties();
                        if (this.treeOpenedItems != null) {
                            if ( this.treeOpenedItems.length > 0) {
                                this.openTreeItems();
                            }
                            // this.selectTreeItem(treeData);
                        }

                        this.tree.verticalScrollPosition = this.verticalScrollPosition;
                        // validate display properties
                        // this.tree.validateProperties();
                    }else {
                        if(treeData == undefined){
                            treeData = []  ;
                            this.tree.dataProvider = treeData;
                            this.setTreeTotals(this.jsonReader.getSingletons()["total"]);
                        }
                    }
             }

                setTimeout(() => {
                    if (this.tree.selectedItem && this.prevRecievedJSON && this.lastSelectedItem != null && this.lastSelectedItem.id ==  this.tree.selectedItem.id) {
                        try {
                            //  if (!this.resetFilter) {
                            // this.summaryGrid.applyFilterSort=true;
                            if (this.rcdScenInstance != "Y" && this.jsonReader.getGridData().size>0 && this.prevRecievedJSON.scenarioinstancedetails.grid.rows && this.prevRecievedJSON.scenarioinstancedetails.grid.rows.row) {
                                this.saveGridOpenState(this.prevRecievedJSON.scenarioinstancedetails.grid.rows.row);
                                this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row = this.openGridItems(this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row);
                                if (this.summaryGrid.isFiltered) {
                                    this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row = this.showVisibleItems(this.lastRecievedJSON.scenarioinstancedetails.grid.rows.row);
                                }
                            }
                        } catch (error) {
                            console.log("SwtSummary -> dataProvider -> error", error)
                        }
                    }else{
                        this.summaryGrid.isFiltered=false;
                        this.summaryGrid.sortColumnIndex = -1;
                    }
                    this.lastSelectedItem  = this.deepCopy(this.tree.selectedItem);
                    this.rcdScenInstance = this.jsonReader.getSingletons()["rcdScenInstance"];
                    this.facilityAccess =(this.jsonReader.getSingletons()["facilityAccess"]!=="2");
                    this.supportAllCurrency = StringUtils.isTrue(this.jsonReader.getSingletons()["supportAllCurrency"]);
                    this.supportAllEntity = StringUtils.isTrue(this.jsonReader.getSingletons()["supportAllEntity"]);
                    // this.summaryGrid.setAccessRules(facilityAccess,supportAllEntity,supportAllCurrency);
                    let jsonlist = this.jsonReader.getColumnData().column;
                    data.scenarioinstancedetails.grid.metadata.columns.column = jsonlist;
                    this.columnData = data.scenarioinstancedetails.grid.metadata;

                    for (let nonFiltrable in this.columnData['columns']['column']) {
                        if (this.columnData['columns']['column'][nonFiltrable].dataelement === "expand") {
                            this.columnData['columns']['column'][nonFiltrable].filterable = false;
                            this.columnData['columns']['column'][nonFiltrable].draggable = false;
                            this.columnData['columns']['column'][nonFiltrable].heading ="";
                        }
                    }

                    this.summaryGrid.CustomGrid(this.columnData);

                    this.summaryGrid.rowClickableFunction = ( dataContext, dataIndex, color ) => {
                        return this.setClickable( dataContext, dataIndex, color );
                    };
                    if(this.rcdScenInstance!="Y")
                    this.summaryGrid.GroupId = 'entity';
                    let rowData = this.lastRecievedJSON.scenarioinstancedetails.grid.rows;
                    for (let i = 0; i < this.summaryGrid.columnDefinitions.length; i++) {
                        let column = this.summaryGrid.columnDefinitions[i];
                        if (column.field == "entity") {
                            if(rowData.size > 1){
                                for (let j = 0; j < rowData.row.length; j++) {

                                    rowData.row[j].entity.bold = Boolean(rowData.row[j].entity.haschildren);
                                }
                            }else if(rowData.size == 1 ){
                                if(rowData.row.entity){
                                    rowData.row.entity.bold = rowData.row.entity.content == "All";
                                }else {
                                    rowData.row[0].entity.bold = rowData.row[0].entity.content == "All";
                                }
                            }
                        }
                    }

                    if (this.jsonReader.getGridData()) {
                        if (this.jsonReader.getGridData().size > 0) {
                            lastSelectedIndex = this.summaryGrid.selectedIndex ;
                            if (this.jsonReader.getGridData().size > 1 && this.rcdScenInstance!="Y" ){
                                let grid2dataset = [];
                                JSONReader.jsonpath(data,'$.scenarioinstancedetails.grid.rows.row.*').forEach(function (value) {
                                    if(value.visible == true){
                                        if(value === Object(value)){
                                            grid2dataset.push(value);
                                        }
                                    }
                                });
                                this.summaryGrid.gridData  = { row: grid2dataset, size: grid2dataset.length };

                            }else{
                                this.summaryGrid.gridData  =  this.jsonReader.getGridData();
                                this.summaryGrid.setRowSize  =  this.jsonReader.getRowSize();
                                this.summaryGrid.refresh();
                            }
                            this.summaryGrid.saveColumnOrder = false;
                            this.summaryGrid.saveWidths = false;
                            if(this.summaryGrid.allowMultipleSelection)
                                this.summaryGrid.allowMultipleSelection = false;
                            this.summaryGrid.doubleClickEnabled  = true;
                            this.summaryGrid.rowHeight = 20;
                            this.summaryGrid.styleName = 'dataGridNormal';
                            this.summaryGrid.colWidthURL(this.baseURL + this.actionPath);
                            this.summaryGrid.colOrderURL(this.baseURL + this.actionPath);


                        } else {
                            this.summaryGrid.gridData = { row: [], size: 0 };
                        }
                    } else {
                        this.summaryGrid.gridData = { row: [], size: 0 };
                    }
                    setTimeout(()=>{
                        this.summaryGrid.validateNow();
                        this.summaryGrid.selectedIndex = lastSelectedIndex;
                    },0);

                    this.facilityId=this.jsonReader.getSingletons()["facilityId"];
                    this.facilityName=this.jsonReader.getSingletons()["facilityName"];
                    this.useGeneric=this.jsonReader.getSingletons()["useGeneric"];
                    this.scenarioTitle = this.jsonReader.getSingletons()["scenarioTitle"];
                    this.selectedscenario = this.jsonReader.getSingletons()["selectedscenario"];
                    //dividerPos = this.jsonReader.getScreenAttributes()["dividerposition"];
                    /* check if the divider position from response xml is not empty */
                   /* if (dividerPos != "") {
                        if( this.currentDivPos == -1){
                            if(!parseFloat(dividerPos)){
                                this.currentDivPos = parseFloat('60');
                            }else{
                                this.currentDivPos = parseFloat(dividerPos);
                            }
                            this.divBox.setWidthLeftWithoutEvent(this.currentDivPos+'%');
                        }
                    } else {
                        // set default percent width of the summary grid
                        // this.gridContainer.width= this.currentDivPos * ( this.stage.width / 100 );
                        // validate the  grid content
                        // this.gridContainer.validateNow();
                    }*/

                    if (this.jsonReader.getSingletons()["selectedscenariolastran"] != "") {
                        this.lastRanLbl.visible=true;
                        this.setScenarioLastRan(this.jsonReader.getSingletons()["selectedscenariolastran"]);
                    } else {
                        this.lastRanLbl.visible=false;
                    }
                    // set previous XML
                    this.prevRecievedJSON = this.lastRecievedJSON;
                }, 0);


        }
          } catch (error) {
            console.log("🚀 ~ file: CustomSummary.ts ~ line 663 ~ CustomSummary ~ dataProvider ~ error", error)
          }
    }

    private  setScenarioLastRan(lastRan:String):void {
        this.lastRanLbl.text = StringUtils.trim(SwtUtil.getPredictMessage('scenarioSummary.selectedScenLastRan', null)) + " " + lastRan.replace('taking',SwtUtil.getPredictMessage('label.taking', null)).replace('seconds',SwtUtil.getPredictMessage('label.seconds', null));
    }

    setClickable( dataContext, index, dataElement ): boolean {
        const value = dataContext.slickgrid_rowcontent.count.content;
        let isClickable = false;
       // if value is greater than 0
        if (parseInt(value,10) > 0)
        {


            if(this.facilityAccess==false)
            {
                //set clickable to false
                isClickable = false;
            }
            else
            {
                const entity = dataContext.slickgrid_rowcontent.entity.content;
                const ccy = dataContext.slickgrid_rowcontent.ccy.content;

                //if entiy value or currency value equal "All" we need to verify if the facility supports "All" as Currency or "All" as Entity
                if(entity=="All" || ccy=="All")
                {
                    if((entity=="All")&&(ccy!="All"))
                    {
                        isClickable = this.supportAllEntity;
                    }
                    else if((entity!="All")&&(ccy=="All"))
                    {
                        isClickable = this.supportAllCurrency;
                    }
                    else if((entity=="All")&&(ccy=="All"))
                    {
                        isClickable= this.supportAllCurrency&&this.supportAllEntity;
                    }


                }
                else
                {
                    isClickable = true;
                }
            }
        }
        else
        {
            isClickable = false;
        }
        return isClickable;
    }


    /**
     * showVisibleItems
     * @param dataProvider:XMLList
     * This method is called to show visible rows in the datagrid after filter selection
     **/
    showVisibleItems(dataProvider): any {
        if(dataProvider){
            for (let i=0; i < this.gridVisibleItems.length; i++) {
                for (let j=0; j < dataProvider.length; j++) {
                    let element = dataProvider[j];
                    element.visible = true;
                    if (element == this.gridVisibleItems[i]) {
                        dataProvider[j] = element;
                    }
                }
            }
        }

        return dataProvider;
    }

    public saveGridVisibleState(e:Event):void {
        let visibleItems = null;
        this.gridVisibleItems=[];
        if(this.lastRecievedJSON  && this.lastRecievedJSON.alert && this.lastRecievedJSON.alert.grid.rows ) {
            visibleItems = this.deepCopy(this.lastRecievedJSON.alert.grid.rows.row).filter(x=>x.visible== true);
            for (var i=0; i < visibleItems.length; i++) {
                this.gridVisibleItems[i] = visibleItems[i];

            }
        }
    }

    /**
     * saveGridOpenState
     * @param newXML
     * used to save opened grid items
     */
    saveGridOpenState(newXML): void {
        this.gridOpenedItems=[];
        if (newXML) {
             this.gridOpenedItems = this.deepCopy(newXML).filter(x=>x.expand.content== "Y" && x.expand.opened == true);
        }

    }


    deepCopy( mainObj ) {
        const objCopy = []; // objCopy will store a copy of the mainObj
        let key;

        for ( key in mainObj ) {
            objCopy[key] = mainObj[key]; // copies each property to the objCopy object
        }
        return objCopy;
    }

    /**
     * openGridItems
     *
     * @param dataProvider:XMLList
     *
     * used to maintain opened grid items when refreshing
     */
    private openGridItems(dataProvider): any{
        if(dataProvider){
            if ( !( dataProvider instanceof Array ) ) {
                var array = [];
                array[0] = dataProvider;
                dataProvider = array;
            }
            if(this.gridOpenedItems && this.gridOpenedItems.length >0){
                for (let i=0; i < this.gridOpenedItems.length; i++){
                    if( dataProvider.filter(x=> x.entity.haschildren == true && x.entity.isopen== false && x.entity.content.toString() == this.gridOpenedItems[i].entity.content).length > 0){
                        if(dataProvider.filter(x=> x.entity.haschildren == true && x.entity.isopen == false && x.entity.content.toString() == this.gridOpenedItems[i].entity.content)[i]){
                            dataProvider.filter(x=> x.entity.haschildren == true && x.entity.isopen == false && x.entity.content.toString() == this.gridOpenedItems[i].entity.content)[i].expand.opened = true;
                            for (let j=0; j <  dataProvider.filter(x=>x.entity.content.toString() == this.gridOpenedItems[i].entity.content).length; j++){
                                dataProvider.filter(x=>x.entity.content.toString() == this.gridOpenedItems[i].entity.content)[j].visible = true;

                            }
                        }

                    }
                }
            }
        }
        return dataProvider;
    }

    setTreeTotals(total: string): void {
        this.treeTitle.text = SwtUtil.getPredictMessage('scenarioSummary.scenTotals', null) + " (" + total + ") ";

    }

    
  

}
