
import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {
  CommonService, HTTPComms, JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtComboBox,
  SwtCommonGrid,
  SwtLabel,
  SwtModule, SwtPopUpManager, SwtRadioButtonGroup, SwtRadioItem, SwtUtil
} from "swt-tool-box";

@Component({
  selector: 'app-exportpages',
  templateUrl: './ExportPages.html',
  styleUrls: ['./ExportPages.css']
})
export class ExportPages extends SwtModule implements OnInit{

  /***Buttons********/

  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('exportButtonGroup') exportButtonGroup: SwtRadioButtonGroup;
  @ViewChild('radioC') radioC: SwtRadioItem;
  @ViewChild('radioA') radioA: SwtRadioItem;

  /***Variable*****/

  private swtAlert: SwtAlert;
  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  ngOnInit(): void {
    this.radioC.label = SwtUtil.getPredictMessage('exportPages.label.current', null);
    this.radioA.label = SwtUtil.getPredictMessage('exportPages.label.allPages', null);
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
  }

  onLoad() {

  }
  exportType() : void {
    if(this.radioC.selected) {
      this.result = "current";
    } else {
      this.result = "all";
    }
    this.close();

  }



  popupClosed(): void {
    this.close();
  }

}
