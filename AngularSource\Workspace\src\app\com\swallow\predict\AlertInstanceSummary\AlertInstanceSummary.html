<SwtModule (creationComplete)='onLoad()' width="100%"  height="100%">
  <VBox  width="100%" height="100%" paddingBottom="5" paddingTop="5" paddingLeft="5" paddingRight="5" verticalGap="0">
    <SwtCanvas width="100%" height="15%" minWidth="1110">
      <VBox width="100%" height="100%">
        <HBox width="100%" height="100%">
          <VBox width="100%" height="100%">
            <HBox width="100%" height="40">
              <SwtLabel id="lblCombo"
                      #lblCombo
                      fontWeight="bold" width="50">
            </SwtLabel>
              <SwtComboBox  #entityCombo
                          id="entityCombo"
                          dataLabel="entity"
                          (change)="entityComboChange($event)">
            </SwtComboBox>
              <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal"
                      paddingLeft="10"
                      text=""
                      textAlign="left">
            </SwtLabel>
            </HBox>


            <HBox width="100%" height="30">
              <SwtLabel id="statusLabel" #statusLabel fontWeight="bold" width="55">
              </SwtLabel>
              <SwtRadioButtonGroup #status id="status"  align="horizontal" width="430" (change)="changeStatus()">
                <SwtRadioItem value="All" width="50" groupName="status"  id="all" #all>
                </SwtRadioItem>
                <SwtRadioItem value=""  width="80" groupName="status" id="allOpen" selected="true" #allOpen></SwtRadioItem>
                <SwtRadioItem value="A" width="65" groupName="status" id="active" #active >
                </SwtRadioItem>
                <SwtRadioItem value="P" width="80" groupName="status" id="pending" #pending></SwtRadioItem>
                <SwtRadioItem value="O" width="80" groupName="status" id="overdue" #overdue></SwtRadioItem>
                <SwtRadioItem value="R" width="80" groupName="status" id="resolved" #resolved>
                </SwtRadioItem>
              </SwtRadioButtonGroup>
              <SwtLabel id="resolvedOnLbl" #resolvedOnLbl fontWeight="normal" width="85">
                </SwtLabel>
              <SwtDateField id="resolvedOnDate" #resolvedOnDate (change)="updateData('tree')"
              width="70" enabled="false"></SwtDateField>
            </HBox>


            <HBox width="100%" height="20">
                <SwtTabNavigator #tabCategoryList id="tabCategoryList"  width="100%" height="100%"  (onChange)="updateData('tree')" borderBottom="false"> </SwtTabNavigator>
            </HBox>
          </VBox>
          <VBox width="20%" height="100%"  verticalGap="0">
          </VBox>



          <VBox  width="30%" height="100%"  verticalGap="0">

            <HBox width="100%" height="30" horizontalAlign="right">
              <SwtLabel width="200" fontWeight="bold" #currLabel></SwtLabel>
              <SwtCheckBox  #currBox  id="currBox" selected="false" value ="N"
              (change)="updateData('scenario')"></SwtCheckBox>
            </HBox>
            <HBox width="100%" height="30" horizontalAlign="right">
              <SwtLabel width="200" #zeroLabel  fontWeight="bold"></SwtLabel>
              <SwtCheckBox   #zeroBox  id="zeroBox" selected="false"   value ="N"
              (change)="updateData('scenario')">
              </SwtCheckBox>
            </HBox>
            <HBox width="100%" height="30" horizontalAlign="right">
              <SwtLabel width="200" #alertLabel  fontWeight="bold"></SwtLabel>
              <SwtCheckBox #alertBox  id="alertBox" selected="false" value ="N"
              (change)="updateData('scenario')" >
              </SwtCheckBox>
            </HBox>
          </VBox>
        </HBox>

      </VBox>
    </SwtCanvas>
    <HBox #summaryHbox id="summaryHbox" width="100%" minWidth="1110" height="73%">
    </HBox>
    <HBox width="100%" height="30" minWidth="1110">
      <HBox width="60%" height="100%" paddingLeft="5">
      <SwtButton #detailsButton id="detailsButton" enabled="false" (click)="clickLink('frDetailsBtn')">
      </SwtButton>
      <SwtButton #goToButton id="goToButton" enabled="false" (click)="goTo($event)">
      </SwtButton>
      <SwtButton #reActiveButton id="reActiveButton" enabled="false" (click)="updateStatus('A')">
      </SwtButton>
      <SwtButton #resolveButton id="resolveButton" enabled="false" (click)="updateStatus('R')">
      </SwtButton>

    </HBox>
    </HBox>
    <SwtCanvas width="100%"  height="6%"  minWidth="1110">
      <HBox id="controlContainer" width="100%" height="100%">
        <HBox paddingLeft="8" width="40%" height="100%">
          <SwtButton #refreshButton id="refreshButton"  enabled="false" (click)="updateData('tree')" >
          </SwtButton>
          <!--<SwtButton #rateButton id="rateButton"  enabled="false" (click)="rateHandler()" >
          </SwtButton>-->
          <SwtButton #closeButton id="closeButton" (click)="closeHandler($event)" >
          </SwtButton>
        </HBox>
        <HBox width="50%" horizontalAlign="right">
          <SwtText
            visible="false"
            text="CONNECTION ERROR"
            styleName="redText"
            #lostConnectionText
            id="lostConnectionText"
            right="45"
            height="16">
          </SwtText>
          <SwtText #lastRefTimeLabel
                   id="lastRefTimeLabel"
                   fontWeight="normal"
                   right="65"
                   height="16">
          </SwtText>
          <SwtText #lastRefTime
                   id="lastRefTime"
                   visible="false"
                   width="90"
                   right="65"
                   height="16" >
          </SwtText>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>

