import { HttpClient } from "@angular/common/http";
import { Component, ElementRef, OnInit, ViewChild, NgModule } from '@angular/core';
import { PaginationChangedArgs } from 'angular-slickgrid';
import { CommonService, HBox, HTTPComms, JSONReader, Logger, SwtAlert, SwtButton, SwtCanvas, SwtCommonGrid, SwtCommonGridPagination, SwtLoadingImage, SwtModule, SwtPopUpManager, SwtTextInput, SwtUtil, ExternalInterface, SwtToolBoxModule } from "swt-tool-box";
import { Routes, RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/compiler/src/core';

@Component({
  selector: 'app-party-search',
  templateUrl: './PartySearch.html',
  styleUrls: ['./PartySearch.css']
})
export class PartySearch extends SwtModule implements OnInit {
  rows: any;
  _selectedRowData: any;
  listValues: SwtCommonGrid;
  //**********************  Data Grid ***************************************************/ 
  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  //**********************  TextInput ***************************************************/ 
  @ViewChild('partyIdInput') partyIdInput: SwtTextInput;
  @ViewChild('partyNameInput') partyNameInput: SwtTextInput;
  //**********************  Buttons ****************************************************/ 
  @ViewChild('okButton', { read: SwtButton }) okButton: SwtButton;
  @ViewChild('searchButton', { read: SwtButton }) searchButton: SwtButton;
  @ViewChild('closeButton', { read: SwtButton }) closeButton: SwtButton;
  /*********HBox*****************/
  @ViewChild('filterHbox') filterHbox: HBox;
  @ViewChild('numstepper') numstepper: SwtCommonGridPagination;
  gridJSONList: any;
  gridJSONListValue: any;
  // get code of the selected column in search screen
  private columnCode: string = null;
  // get the current operation used in search screen
  private operation: string = null;
  // get label of the selected column in search screen
  private columnLabel: string = null;
  //To hold the help message
  private message: string = null;
  //To hold the dataSource
  private dataSource: string = null;
  private entityId: string = null;
  /** 
* Private parameters 
**/
  private logger: Logger;
  private programId: string = "404";  //set program
  private componentId: string = null; //hold componentId
  private menuAccess: string = "0";//To store menu access
  private alertMsg: string;
  private alertMsgToShow: string;
  /**
   * Data Objects
   **/
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  private errorLocation = 0;
  public moduleReportURL: string = null;
  public moduleId = '';
  public viewOnly: boolean = false;
  private searchPartyGrid: SwtCommonGrid;
  //Variable to store the last page no
  public lastNumber: Number = 0;
  // flag variable to display the first record if page number is changed

  dataProviderTemp: any;
  constructor(private element: ElementRef, httpClient: HttpClient, private commonService: CommonService) {
    super(element, commonService);
    this.logger = new Logger("ListValues", httpClient);
  }


  ngOnInit(): void {
    if (this.dataSource == 'fromStopRules') {
      this.filterHbox.visible = false;
    }

    if (this.viewOnly) {
      this.okButton.enabled = false;
    }
  }
  onLoad() {
    this.entityId =ExternalInterface.call('eval', 'entityId');
    this.searchPartyGrid = <SwtCommonGrid>this.canvasGrid.addChild(SwtCommonGrid);
    try {
      this.title = SwtUtil.getAMLMessages('pcpriorityMaintenanceScreen.windowtitle.help_screen');
      this.message = SwtUtil.getAMLMessages('pcpriorityMaintenanceScreen.message.help_message');
      this.actionPath = 'preadviceinput.do?';
      this.actionMethod = 'method=partySearchDisplay';
      this.requestParams['moduleId'] = this.moduleId;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      //this.inputDataResult(data);

      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.searchPartyGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };
      this.searchPartyGrid.onPaginationChanged = (event) => {
        this.paginationChanged(event);
      };
      this.searchPartyGrid.onSortChanged = (event) => {
        this.paginationChanged(event);
      };
      this.searchPartyGrid.onFilterChanged = (event) => {
        this.paginationChanged(event);
      };
      
      // Assining properties for controls
      this.okButton.label = "Ok";//String(SwtUtil.getCommonMessages('button.add'));
      this.searchButton.label = "Search";//String(SwtUtil.getCommonMessages('button.search'));
      this.closeButton.label = "Close";//String(SwtUtil.getCommonMessages('button.close'));
    } catch (error) {

    }
  }


  /**
  * inputDataResult
  *
  * @param data: ResultEvent
  *
  * This is a callback method, to handle result event
  *
  */
  public inputDataResult(data): void {
    let maxPage: String = null;
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {
              //Gets the current page from xml
              this.numstepper.value = Number(data.PartySearch.grid.paging.currentpage);
              //Gets the maximum no of pages value
              maxPage = data.PartySearch.grid.paging.maxpage;
              //Sets the Numeric stepper maximum value
              this.numstepper.maximum = Number(maxPage);

              this.componentId = this.lastRecievedJSON.screenid;
              this.searchPartyGrid.CustomGrid(data.PartySearch.grid.metadata);
              this.searchPartyGrid.paginationComponent = this.numstepper;
              if (this.jsonReader.getGridData().size > 0 && Number(maxPage) > 0) {
                this.searchPartyGrid.gridData = this.jsonReader.getGridData();
                this.searchPartyGrid.setRowSize = this.jsonReader.getRowSize();
                this.searchPartyGrid.doubleClickEnabled = true;
                this.searchPartyGrid.gridData = data.PartySearch.grid.rows;
              } else {
                this.searchPartyGrid.dataProvider = [];
                this.searchPartyGrid.selectedIndex = -1;
              }
              this.menuAccess = this.jsonReader.getScreenAttributes()["menuaccess"];
            }

            this.searchPartyGrid.selectedIndex = -1;
            this.prevRecievedJSON = this.lastRecievedJSON;

          } else {
            if (this.jsonReader.getRequestReplyMessage() == "errors.DataIntegrityViolationExceptioninDelete") {
              this.swtAlert.error("Unable to delete, this spread profile is linked to an existing account group");
            } else {
              this.swtAlert.error(SwtUtil.getCommonMessages('alert.generic_exception'));
            }
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error inputDataResult', e);
    }
  }

  cellClickEventHandler(event: Event): void {
    try {
      if (this.searchPartyGrid.selectedIndices.length === 1 && this.searchPartyGrid.selectable) {
        this.disableOrEnableButtons(true);
      } else {
        this.disableOrEnableButtons(false);
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'cellClickEventHandler', this.errorLocation);
    }
  }

  paginationChanged(event: PaginationChangedArgs) {
    this.numstepper.processing = true;
    this.doRefreshPage();
  }

  private doRefreshPage(): void {
    this.logger.info("method [doRefreshPage] - START");
    //To hold selected sort value
    var selectedSort: string = null;

    //To hold current page
    var currentPage: string = null;

    try {
      if (this.numstepper.value > 0) {
        if (((this.numstepper.value <= this.numstepper.maximum) && (this.numstepper.value != this.lastNumber) && (this.numstepper.value != 0))) {

          // Get the SortedGridColumn
          selectedSort = this.searchPartyGrid.sortedGridColumn;

          //Gets the current page value
          currentPage = (this.numstepper.value).toString();

          //Initialising the request params
          this.requestParams = [];
          //Adding request params
          this.requestParams["currentPage"] = currentPage;
          this.requestParams["selectedsort"] = selectedSort;
          this.requestParams['partyId'] = this.partyIdInput.text;
          this.requestParams['partyName'] = this.partyNameInput.text;
          this.requestParams['entityId'] = this.entityId;

          this.actionPath = 'preadviceinput.do?';
          this.actionMethod = 'method=partySearchResult';
          //set http url
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;

          //send request to server
          this.inputData.send(this.requestParams);
        }
      }
      this.logger.info("method [doRefreshPage] - END");
    }
    catch (error) {
      this.logger.error("Error Occurred: ", error);
    }

  }

  disableOrEnableButtons(isRowSelected: Boolean): void {
    if (isRowSelected) {
      this.okButton.enabled = true;
    } else {
      this.okButton.enabled = false;
    }
  }

  /**
   * inputDataFault
   *
   * @param event:  FaultEvent
   *
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    try {
      this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'inputDataFault', this.errorLocation);
    }
  }

  /**
   * getSelectedValues()
   * 
   * @param even:Event
   * 
   * Method called to get selected rows in the datagrid
   **/
  getSelectedValues(event): any {
    try {
      // this.close();
      
      if (window.opener.instanceElement) {
        if(window.opener.instanceElement.setSelectedPartieItems){
        window.opener.instanceElement.setSelectedPartieItems(this.searchPartyGrid.selectedItem.PartyId.content);
        }else if (window.opener.instanceElement.setSelectedPartieItemsForPreadvice){
        window.opener.instanceElement.setSelectedPartieItemsForPreadvice(this.searchPartyGrid.selectedItem.PartyId.content, this.searchPartyGrid.selectedItem.PartyName.content );
        window.close();
        }else if (window.opener.instanceElement.setSelectedPartieItemsForAccount){
        window.opener.instanceElement.setSelectedPartieItemsForAccount(this.searchPartyGrid.selectedItem.PartyId.content, this.searchPartyGrid.selectedItem.PartyName.content );
        window.close();
      }
      }else {
        this.result = { res: this.searchPartyGrid.selectedItem, buttonClicked: 'okButton' };
        SwtPopUpManager.getPopUpById("listValuesPopPoup").close();
        this.close();
      }


    }
    catch (error) {
      this.logger.error("Error Occurred: ", error);
    }
  }


  search(event): any {
    //alert(this.partyIdInput.text + "--" + this.partyNameInput.text)
    this.actionPath = 'preadviceinput.do?';
    this.actionMethod = 'method=partySearchResult';
    this.requestParams = [];
    this.requestParams['partyId'] = this.partyIdInput.text;
    this.requestParams['partyName'] = this.partyNameInput.text;
    this.requestParams['entityId'] = this.entityId;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  doHelp() {

  }

  /**
   * close
   *
   * @param  event:Event
   *
   * Method to close this module
   */
  cancelHandler(event): void {
    //Variable for errorLocation
    var errorLocation = 0;
    try {
      ExternalInterface.call("close");
    }
    catch (error) {
      this.logger.error("method [cancelHandler] - error : ", error, "- errorLocation :", errorLocation);

      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this) + ".ts", "cancelHandler", errorLocation);
    }
  }

  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    try {
      this.loadingImage.setVisible(true);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'startOfComms', this.errorLocation);
    }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    try {
      this.loadingImage.setVisible(false);
      this.searchPartyGrid.selectedIndex = -1;
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'endOfComms', this.errorLocation);
    }
  }
}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: PartySearch }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PartySearch],
  entryComponents: []
})
export class PartySearchModule { }