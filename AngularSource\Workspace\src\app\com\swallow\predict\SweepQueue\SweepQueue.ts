import { Component, OnInit, ElementRef, ModuleWithProviders, NgModule, ViewChild } from '@angular/core';
import { SwtModule, CommonService, SwtAlert, SwtToolBoxModule, SwtLabel, SwtComboBox, SwtCanvas, SwtButton, SwtLoadingImage, JSONReader, HTTPComms, SwtUtil, SwtCommonGrid, SwtGroupedCommonGrid, ExternalInterface, Alert, StringUtils } from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
declare var instanceElement: any;

@Component({
  selector: 'app-sweep-queue-cancel',
  templateUrl: './SweepQueue.html',
  styleUrls: ['./SweepQueue.css']
})
export class SweepQueue extends SwtModule implements OnInit {
  
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('ccyGroupLabel') ccyGroupLabel: SwtLabel;
  @ViewChild('selectedCcyGroup') selectedCcyGroup: SwtLabel;
  @ViewChild('accountTypeLabel') accountTypeLabel: SwtLabel;
  @ViewChild('selectedAcctType') selectedAcctType: SwtLabel;
  @ViewChild('lastRefTimeLabel') lastRefTimeLabel: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
   
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyGroupCombo') ccyGroupCombo: SwtComboBox;
  @ViewChild('acctTypeCombo') acctTypeCombo: SwtComboBox;
  
  @ViewChild('dataGridContainer1') dataGridContainer1: SwtCanvas;
  @ViewChild('dataGridContainer2') dataGridContainer2: SwtCanvas;

  @ViewChild('submitButton') submitButton: SwtButton;
  @ViewChild('authButton') authButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('searchButton') searchButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  

  private actionPath;
  private actionMethod: string = "";
  private requestParams;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private topGrid: SwtCommonGrid;
  private bottomGrid: SwtCommonGrid;
  private defaultEntity;
  private defaultCcyGrp;
  private defaultAcctType;
  private lastRefTimeValue;
  private menuAccessId;
  private queueName;
  private totalCount;
  private currentFilter;
  private currentSort;
  private currPage; 
  private msgsList;
  private _invalidComms: string;
  public moduleId = "Predict";
  private errorMsg;
  private  errorSweepAmount;
  private errorCutOff;
  private errorAccountBreach;
  private errorSweeps;
  private bypassChangedSweep;
  private bypassCutOff;
  private bypassAccountBreach;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.topGrid = <SwtGroupedCommonGrid>this.dataGridContainer1.addChild(SwtGroupedCommonGrid);
    this.bottomGrid = <SwtGroupedCommonGrid>this.dataGridContainer2.addChild(SwtGroupedCommonGrid);
    this.topGrid.allowMultipleSelection = true;
    this.bottomGrid.allowMultipleSelection = true;
    this.entityLabel.text= SwtUtil.getPredictMessage('sweep.entity', null); 
    this.ccyGroupLabel.text= SwtUtil.getPredictMessage('sweep.currencyGroup', null); 
    this.accountTypeLabel.text= SwtUtil.getPredictMessage('sweep.accountType', null); 
    this.entityCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.ccyGroupCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectCurrencyCode', null);
    this.acctTypeCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectAccountType', null);
    this.submitButton.label = SwtUtil.getPredictMessage('sweep.submit', null);
    this.submitButton.toolTip = SwtUtil.getPredictMessage('tooltip.SubmitSelSweep', null);
    this.authButton.label = SwtUtil.getPredictMessage('sweep.authorize', null);
    this.authButton.toolTip = SwtUtil.getPredictMessage('tooltip.AuthorizeSelSweep', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('tooltip.change', null);this.refreshButton.label = SwtUtil.getPredictMessage('sweep.refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refreshScreen', null);
    this.searchButton.label = SwtUtil.getPredictMessage('sweep.search', null);
    this.searchButton.toolTip = SwtUtil.getPredictMessage('tooltip.searchSweep', null);
    this.closeButton.label = SwtUtil.getPredictMessage('sweep.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.lastRefTimeLabel.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);

  }

  onLoad(){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
    errorLocation = 10;
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId'); 
    this.queueName= ExternalInterface.call('eval', 'queueName');
    errorLocation = 20;
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    errorLocation = 30;
    this.inputData.cbStop = this.endOfComms.bind(this);
    errorLocation = 40;
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 50;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    errorLocation = 60;
    this.inputData.encodeURL = false;
    this.actionPath = "sweepqueue.do?";
    this.actionMethod = 'method=displayAngular';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['queueName'] = this.queueName;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    errorLocation = 70;
    if (this.queueName =="U"){
        this.authButton.includeInLayout = true;
        this.authButton.visible = true;
        this.submitButton.includeInLayout = false;
        this.submitButton.visible = false;
      }else{
        this.authButton.includeInLayout = false;
        this.authButton.visible = false;
        this.submitButton.includeInLayout = true;
        this.submitButton.visible = true;
      }
    errorLocation = 80;
    this.topGrid.onRowClick = (event) => {

      this.onMultiSelectTableRow();

    };
    errorLocation = 90;
    this.bottomGrid.selectable=false;
  }catch (error) {
      SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "onLoad", errorLocation);
   }
  }



  inputDataResult(event): void {
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      errorLocation = 20;
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      errorLocation = 30;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      errorLocation = 40;
      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) { 
          errorLocation = 50;
          //enable refresh button
          this.refreshButton.enabled= true;
          this.refreshButton.buttonMode= true;

          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.ccyGroupCombo.setComboData(this.jsonReader.getSelects());
          this.acctTypeCombo.setComboData(this.jsonReader.getSelects());
          errorLocation = 60;
          this.defaultEntity = this.jsonReader.getSingletons().defaultEntity;
          this.defaultCcyGrp = this.jsonReader.getSingletons().defaultCcyGrp;
          this.defaultAcctType = this.jsonReader.getSingletons().defaultAcctType;
          errorLocation = 70;
          this.lastRefTime.text= this.jsonReader.getSingletons().lastRefTime;
          errorLocation = 80;
          this.entityCombo.selectedLabel = this.defaultEntity;
          if (this.defaultEntity != undefined)
            this.entityCombo.selectedLabel = this.defaultEntity;

          if (this.defaultCcyGrp != undefined)
            this.ccyGroupCombo.selectedLabel = this.defaultCcyGrp;

          if (this.defaultAcctType != undefined)
            this.acctTypeCombo.selectedValue = this.defaultAcctType;  
            errorLocation = 90;
          this.selectedEntity.text = this.entityCombo.selectedValue;
          this.selectedCcyGroup.text = this.ccyGroupCombo.selectedValue;
          this.selectedAcctType.text= this.jsonReader.getSingletons().accountDesp;
          errorLocation = 100;
          if (!this.jsonReader.isDataBuilding()) {
            errorLocation = 110;
            const obj = { columns: this.lastRecievedJSON.SweepQueue.authoriseQueueGrid.metadata.columns };
            //Authorise queue grid
            this.topGrid.CustomGrid(obj);
            errorLocation = 120;
            var gridRows = this.lastRecievedJSON.SweepQueue.authoriseQueueGrid.rows;
            if (gridRows.size > 0) {
              errorLocation = 130;
              this.topGrid.gridData = gridRows;

              this.topGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              errorLocation = 140;
              this.topGrid.gridData = { size: 0, row: [] };
            }
            //View queue grid
            const obj1 = { columns: this.lastRecievedJSON.SweepQueue.viewQueueGrid.metadata.columns };
            this.bottomGrid.CustomGrid(obj1);
            errorLocation = 150;
            var gridRows = this.lastRecievedJSON.SweepQueue.viewQueueGrid.rows;
            errorLocation = 160;
            if (gridRows.size > 0) {
              errorLocation = 170;
              this.bottomGrid.gridData = gridRows;
              errorLocation = 180;
              this.bottomGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              errorLocation = 190;
              this.bottomGrid.gridData = { size: 0, row: [] };
            }
            errorLocation = 200;
            this.prevRecievedJSON = this.lastRecievedJSON;

          }
        }
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }
      }

    }
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "inputDataResult", errorLocation);
 }
  }

  public checkResult(event): void {
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    if (this.inputData.isBusy()) {
      errorLocation = 20;
      this.inputData.cbStop();
    } else {
      errorLocation = 30;
      this.lastRecievedJSON = event;
      errorLocation = 40;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        errorLocation = 50;
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          errorLocation = 60; 
          this.msgsList= this.jsonReader.getSingletons().listOfMsgs;
          this.errorSweepAmount= this.jsonReader.getSingletons().errorSweepAmount;
          this.errorCutOff= this.jsonReader.getSingletons().errorCutOff;
          this.errorAccountBreach= this.jsonReader.getSingletons().errorAccountBreach;
          this.errorSweeps= this.jsonReader.getSingletons().errorSweeps;
          this.bypassCutOff= this.jsonReader.getSingletons().bypassCutOff;
          this.bypassChangedSweep= this.jsonReader.getSingletons().bypassChangedSweep;
          this.bypassAccountBreach= this.jsonReader.getSingletons().bypassAccountBreach;
          errorLocation = 70;
          if (!this.jsonReader.isDataBuilding()) {
            errorLocation = 80;
            const obj = { columns: this.lastRecievedJSON.SweepQueue.authoriseQueueGrid.metadata.columns };
            errorLocation = 90;
            //Authorise queue grid
            this.topGrid.CustomGrid(obj);
            errorLocation = 100;
            var gridRows = this.lastRecievedJSON.SweepQueue.authoriseQueueGrid.rows;
            errorLocation = 110;
            if (gridRows.size > 0) {
              errorLocation = 120;
              this.topGrid.gridData = gridRows;
              errorLocation = 130;
              this.topGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              errorLocation = 140;
              this.topGrid.gridData = { size: 0, row: [] };
            }
            //View queue grid
            const obj1 = { columns: this.lastRecievedJSON.SweepQueue.viewQueueGrid.metadata.columns };
            errorLocation = 150;
            this.bottomGrid.CustomGrid(obj1);
            errorLocation = 160;
            var gridRows = this.lastRecievedJSON.SweepQueue.viewQueueGrid.rows;
            errorLocation = 170;
            if (gridRows.size > 0) {
              errorLocation = 180;
              this.bottomGrid.gridData = gridRows;
              errorLocation = 190;
              this.bottomGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              errorLocation = 200;
              this.bottomGrid.gridData = { size: 0, row: [] };
            }


          }
          this.prevRecievedJSON = this.lastRecievedJSON;
          errorLocation = 210;
          if(this.errorSweeps){
            errorLocation = 220;
            if(this.msgsList){
              errorLocation = 230;
              this.swtAlert.confirm(this.msgsList, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.yesSubmit.bind(this));
              }
          }else{
            errorLocation = 240;
          if(this.msgsList){
            errorLocation = 250;
          this.swtAlert.show(this.msgsList, "Warning", Alert.OK, null, () => {
            errorLocation = 260;
            this.refreshButton.enabled= true;
            this.refreshButton.buttonMode= true;
            this.authButton.enabled= false;
            this.authButton.buttonMode= false;
            this.submitButton.enabled= false;
            this.submitButton.buttonMode= false;
            this.topGrid.selectedIndex= -1;
           });        
          } 
          }

        }
      } else {

        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
       }
      }

    }
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "checkResult", errorLocation);
 }
  }



  /*change data grid*/
  updateData(event, combo): void {
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    errorLocation = 20;
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        errorLocation = 30;
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    errorLocation = 40;
    this.inputData.cbStop = this.endOfComms.bind(this);
    errorLocation = 50;
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 60;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "sweepqueue.do?";
    this.actionMethod = 'method=displayList';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['selectedList'] = this.getSelectedList();
    this.requestParams['bypassChangedSweep'] = "N";
    this.requestParams['bypassAccountBreach'] = "N";
    this.requestParams['bypassCutOff'] = "N";
    this.requestParams['queueName'] = this.queueName;
    this.requestParams['accountType'] = this.acctTypeCombo.selectedValue;
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['currencyCode'] = this.ccyGroupCombo.selectedLabel; 
    this.requestParams['parentScreen'] = "";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 70;
    this.inputData.send(this.requestParams);
    errorLocation = 80;
    //disable refresh button
    this.refreshButton.enabled= false;
    this.refreshButton.buttonMode= false;
    //Update changed selected value
    this.UpdateSelectedValue(combo);
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "updateData", errorLocation);
 }
  }


  UpdateSelectedValue(combo){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
  if(combo=="entity"){
    this.selectedEntity.text = this.entityCombo.selectedValue;
  }
  if(combo=="currency"){
    this.selectedCcyGroup.text = this.ccyGroupCombo.selectedValue;
  }
  if(combo=="account"){
    this.selectedAcctType.text = this.ccyGroupCombo.selectedValue;
  }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, this.commonService.getQualifiedClassName(this), "UpdateSelectedValue", errorLocation);
    }
  }


  submit(event){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    errorLocation = 20;
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        errorLocation = 30;
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    errorLocation = 40;
    this.inputData.cbStop = this.endOfComms.bind(this);
    errorLocation = 50;
    this.inputData.cbResult = (event) => {
      this.checkResult(event);
    };
    errorLocation = 60;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "sweepqueue.do?";
    this.actionMethod = 'method=submit';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['selectedList'] = this.getSelectedList();
    this.requestParams['queueName'] = this.queueName;
    this.requestParams['accountType'] = this.acctTypeCombo.selectedValue;
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['currencyCode'] = this.ccyGroupCombo.selectedLabel;
    this.requestParams['bypassChangedSweep'] = "N";
    this.requestParams['bypassAccountBreach'] = "N";
    this.requestParams['bypassCutOff'] = "N"; 
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 70;
    this.inputData.send(this.requestParams);
    errorLocation = 80;
    this.refreshButton.enabled= false;
    this.refreshButton.buttonMode= false;
  } catch (error) {
    SwtUtil.logError(error, this.moduleId, this.commonService.getQualifiedClassName(this), "submit", errorLocation);
  }
  }

  getSelectedSweepID(){ 
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10; 
    let selectedRows = this.topGrid.selectedItems;
    errorLocation = 20; 
    let selectedOtherRows = this.bottomGrid.selectedItems;
    errorLocation = 30; 
    var selectedList = "";
    for (let i=0; i < selectedRows.length; i++) 
    {
      errorLocation = 40; 
     if( selectedRows[i]){
      errorLocation = 50; 
          selectedList = selectedList + selectedRows[i].sweepId.content;
       }
    } 
    
    if(selectedList == ""){
      errorLocation = 60; 
      for (let j=0; j < selectedOtherRows.length; j++) 
      {
        errorLocation = 70; 
        selectedList = selectedList + selectedOtherRows[j].sweepId.content;
      }
    }
   
    return selectedList;
  } catch (error) {
    SwtUtil.logError(error, this.moduleId, this.commonService.getQualifiedClassName(this), "getSelectedSweepID", errorLocation);
  }
   }

  getSelectedEntityID() {
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10; 
      let selectedRows = this.topGrid.selectedItems;
      errorLocation = 20; 
      let selectedOtherRows = this.bottomGrid.selectedItems;
      errorLocation = 30; 
      var entityIdCr = "";

      for (let i=0; i < selectedRows.length; i++) 
      {
        errorLocation = 40;
       if( selectedRows[i]){
        errorLocation = 50;
        entityIdCr = entityIdCr + selectedRows[i].entityDr.content;
      }
      } 
      
      if(entityIdCr == ""){
        errorLocation = 60;
        for (let j=0; j < selectedOtherRows.length; j++) 
        {
        errorLocation = 70;
        entityIdCr = entityIdCr + selectedOtherRows[j].entityDr.content;
        }
      }

    return entityIdCr;
  } catch (error) {
    SwtUtil.logError(error, this.moduleId, this.commonService.getQualifiedClassName(this), "getSelectedEntityID", errorLocation);
  }
  }

   
  getSelectedList(){ 
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10; 
    let selectedRows = this.topGrid.selectedItems;
    errorLocation = 20;
    var selectedList = "";
    for (let i=0; i < selectedRows.length; i++) 
    {
      errorLocation = 30;
     if( selectedRows[i]){
          errorLocation = 40;
          selectedList = selectedList + selectedRows[i].sweepId.content + ",";
       }
    }  		
    return selectedList;
  } catch (error) {
    SwtUtil.logError(error, this.moduleId, this.commonService.getQualifiedClassName(this), "getSelectedList", errorLocation);
  }
   }

  closeHandler() {
    ExternalInterface.call("close");
  }
  
  buildViewSweepDisplayURL(){
    ExternalInterface.call('buildViewSweepDisplayURL', this.getSelectedSweepID(), this.getSelectedEntityID());
  }

  openSearch(){
    ExternalInterface.call("openSearch", "displaysearch", this.entityCombo.selectedLabel, this.acctTypeCombo.selectedLabel, this.ccyGroupCombo.selectedLabel, this.queueName, "U"==this.queueName?"sweepauthorisequeue":"sweepsubmitqueue");
  }

  openSweepQueueDetail(){
    ExternalInterface.call('openSweepQueueDetail', this.topGrid.selectedItem.sweepId.content, this.entityCombo.selectedLabel, this.queueName);
  }


  onMultiSelectTableRow(){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    if(this.topGrid.selectedIndex >=0){
      errorLocation = 20;
      let selectedRows= this.topGrid.selectedItems
      errorLocation = 30;
      let selectedList = "";
      let flag = "true";
      let entityId = this.entityCombo.selectedLabel;
      for (let i=0; i < selectedRows.length; i++) {
        errorLocation = 40;
            let ccyCode = selectedRows[i].currencyCode.content;
            let entityIdCr = selectedRows[i].entityCr.content;
            errorLocation = 40;
            let accountIdCr = selectedRows[i].accountIdCr.content;
            let entityIdDr = selectedRows[i].entityDr.content;
            errorLocation = 80;
            let accountIdDr = selectedRows[i].accountIdDr.content;
            errorLocation = 50;
              flag = this.getAccess(entityId, entityIdCr, entityIdDr, ccyCode, accountIdCr, accountIdDr);
              errorLocation = 60;
              if (flag == "false"){
                break;
              }
              if (flag == "true" && this.menuAccessId == 0 ){
                if(this.topGrid.selectedItems.length == 1) {
                errorLocation = 70;
                this.changeButton.enabled = true;
                this.changeButton.buttonMode = true;
                }else{
                  this.changeButton.enabled = false;
                  this.changeButton.buttonMode = false;  
                } 
                this.authButton.enabled = true;
                this.authButton.buttonMode = true;  
                this.submitButton.enabled = true;
                this.submitButton.buttonMode = true;              
              } else{
                errorLocation = 80;
                this.changeButton.enabled = false;
                this.changeButton.buttonMode = false;  
                this.authButton.enabled = false;
                this.authButton.buttonMode = false;  
                this.submitButton.enabled = false;
                this.submitButton.buttonMode = false;   
              }      
              
             /* if (flag == "true" && this.menuAccessId == 0 && this.topGrid.selectedItems.length >0) {
                errorLocation = 90;
                this.authButton.enabled = true;
                this.authButton.buttonMode = true;  
                this.submitButton.enabled = true;
                this.submitButton.buttonMode = true;           
              } else{
                errorLocation = 100;
                this.authButton.enabled = false;
                this.authButton.buttonMode = false;  
                this.submitButton.enabled = false;
                this.submitButton.buttonMode = false;     
              } */  
      }
      errorLocation = 110;
      //this.accountAccess();
      errorLocation = 120;
    }else{
      errorLocation = 130;
      this.submitButton.enabled = false;
      this.submitButton.buttonMode = false;   
      this.authButton.enabled = false;
      this.authButton.buttonMode = false;
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false; 
    }
  } catch (error) {
    SwtUtil.logError(error, this.moduleId, this.commonService.getQualifiedClassName(this), "onMultiSelectTableRow", errorLocation);
  }
  }


    accountAccess() {
      // Variable to hold error location
      var errorLocation: number = 0;
      try {
        errorLocation = 10;
      let selectedRows= this.topGrid.selectedItems
      errorLocation = 20;
      let selectedList = "";
      let flag = "true";
      for (let i=0; i < selectedRows.length; i++) {
        errorLocation = 30;
            let entityIdCr = selectedRows[i].entityCr.content;
            errorLocation = 40;
            let accountId = selectedRows[i].accountIdCr.content;
            errorLocation = 50;
              flag = this.accountAccessConfirm(accountId.trim(), entityIdCr);
              errorLocation = 60;
              if (flag == "false") {
                errorLocation = 70;
                this.submitButton.enabled = false;
                this.submitButton.buttonMode = false;   
                this.authButton.enabled = false;
                this.authButton.buttonMode = false; 
                this.changeButton.enabled = false;
                this.changeButton.buttonMode = false;            
              }
            let entityIdDr = selectedRows[i].entityDr.content;
            errorLocation = 80;
              accountId = selectedRows[i].accountIdDr.content;
              errorLocation = 90;
              flag = this.accountAccessConfirm(accountId.trim(), entityIdDr);
              errorLocation = 100;
              if (flag == "false") {
                errorLocation = 110;
                    this.submitButton.enabled = false;
                    this.submitButton.buttonMode = false;   
                    this.authButton.enabled = false;
                    this.authButton.buttonMode = false;
                    this.changeButton.enabled = false;
                    this.changeButton.buttonMode = false;    
              }             
      }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, this.commonService.getQualifiedClassName(this), "accountAccess", errorLocation);
    }
  }


   accountAccessConfirm(accountId, entity) {
   let lockflag= "false";
   lockflag=ExternalInterface.call("accountAccessConfirm", accountId, entity);
   return lockflag;
  }

  getCurrencyAccess(entity, ccyCode) {
    let lockflag= "false";
    lockflag=ExternalInterface.call("getCurrencyAccess", entity, ccyCode);
    return lockflag;
   }


   getAccess(entity, entityIdCr, entityIdDr, ccyCode, accountIdCr, accountIdDr) {
    let lockflag= "false";
    lockflag=ExternalInterface.call("getAccess", entity, entityIdCr, entityIdDr, ccyCode, accountIdCr, accountIdDr);
    return lockflag;
   }

  yesSubmit(event){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    if (event.detail == Alert.YES) {
      errorLocation = 20;
    this.topGrid.selectedIndex=-1; 
    this.refreshButton.enabled= true;
    this.refreshButton.buttonMode= true;
    this.submitButton.enabled= false;
    this.submitButton.buttonMode= false; 
    this.authButton.enabled= false;
    this.authButton.buttonMode= false;
    this.changeButton.enabled= false;
    this.changeButton.buttonMode= false;  
    }else{
      errorLocation = 30;
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    errorLocation = 40;
    this.inputData.cbStop = this.endOfComms.bind(this);
    errorLocation = 50;
    this.inputData.cbResult = (event) => {
      this.checkResult(event);
    };
    errorLocation = 60;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    errorLocation = 70;
    this.inputData.encodeURL = false;
    this.actionPath = "sweepqueue.do?";
    this.actionMethod = 'method=submit';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['selectedList'] = this.getSelectedList();
    this.requestParams['queueName'] = this.queueName;
    errorLocation = 80;
    if(this.errorCutOff && this.errorCutOff=="Y"){
    this.requestParams['bypassCutOff'] = "Y";
    }
    errorLocation = 90;
    if(this.errorSweepAmount && this.errorSweepAmount=="Y"){
      this.requestParams['bypassChangedSweep'] = "Y";
      this.requestParams['bypassCutOff'] = this.bypassCutOff;
    }
    errorLocation = 100;
    if(this.errorAccountBreach && this.errorAccountBreach=="Y"){
    this.requestParams['bypassAccountBreach'] = "Y";
    this.requestParams['bypassCutOff'] = this.bypassCutOff;
    this.requestParams['bypassChangedSweep'] = this.bypassChangedSweep;
    }
    errorLocation = 110;
    this.requestParams['accountType'] = this.acctTypeCombo.selectedValue;
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['currencyCode'] = this.ccyGroupCombo.selectedLabel; 
    this.requestParams['parentScreen'] = "";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 120;
    this.inputData.send(this.requestParams);
  }
} catch (error) {
  SwtUtil.logError(error, this.moduleId, this.commonService.getQualifiedClassName(this), "yesSubmit", errorLocation);
} 
}

  doHelp(): void {
    ExternalInterface.call("help");
  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

    /**
   * enablePrintButton
   *
   */
  enablePrintButton(value): void {
    this.printButton.enabled = value;
    this.printButton.buttonMode = value;
  }

   /**
   * printPage
   *
   * param event
   *
   * Method to get call the action to get reports
   */
  printPage(): void {
    let errorLocation = 0;
    try {
      ExternalInterface.call('printPage');

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", errorLocation);
    }
  }

  keyDownEventHandler(event){

  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: SweepQueue }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SweepQueue],
  entryComponents: []
})
export class SweepQueueModule { }