import { Component, ElementRef, <PERSON>Child, ModuleWithProviders, NgModule } from '@angular/core';
import { CommonService, ExternalInterface, focusManager, HTTPComms, JSONReader, Keyboard, SwtAlert, SwtButton, SwtCanvas, SwtCheckBox, SwtComboBox, SwtCommonGrid, SwtHelpWindow, SwtLabel, SwtLoadingImage, SwtModule, SwtPopUpManager, SwtStepper, SwtTextInput, SwtUtil, SwtToolBoxModule } from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
declare function validateFormatTime(strField): any;
@Component({
  selector: 'app-spread-details',
  templateUrl: './SpreadDetails.html',
  styleUrls: ['./SpreadDetails.css']
})
export class SpreadDetails extends SwtModule {

  private swtAlert: SwtAlert;
  private categoryGrid: SwtCommonGrid;

  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];

  public screenName = null;
  public helpURL = null;
  private message = null;
  public title = null;
  public spreadId = null;
  public spreadName = null;
  public errorLocation = 0;

  public time = null;
  public targetValue = null;
  public processCategories = null;
  public categories:string = null;
  public processName = null;
  public processId = null;
  public timesList = null;
  public namesList = null;
  public maintEventId = null;
  public selectedDataRow = null;

  /* - START -- Screen Name and Version number ---- */
  private moduleName = 'Spread Details';
  private versionNumber = '1.00.00';
  private releaseDate = '13 March 2019';
  public moduleId = 'PCM';


  /************SwtTextInput********/
  @ViewChild('processNameInput') processNameInput: SwtTextInput;
  @ViewChild('timeInput') timeInput: SwtTextInput;
  /************SwtTextInput********/
  @ViewChild('targetInput') targetInput: SwtStepper;
  /************SwtTextInput********/
  @ViewChild('processCategoryComboBox') processCategoryComboBox: SwtComboBox;
  /************SwtTextInput********/
  @ViewChild("selectAll") selectAllCheckbox: SwtCheckBox;
  /***LodingImage*******/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********SWtButton*************/
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;

  /*********SwtCanvas*************/
  @ViewChild('categoryCanvasGrid') categoryCanvasGrid: SwtCanvas;
  
  /*********SwtLabel*************/
  @ViewChild('informationLabel') informationLabel: SwtLabel;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  /**
   * onLoad
   * Initializer (multilingual, fetch list details )
   */
  onLoad(): void {
    try {

      this.categoryGrid = <SwtCommonGrid>this.categoryCanvasGrid.addChild(SwtCommonGrid);
      //Checks the screen is add and calls the add action
      
      if (this.screenName != "view") {
        this.processNameInput.setFocus();
        this.okButton.enabled = true;
        this.categoryGrid.ITEM_CLICK.subscribe((selectedRowData) => {
          this.cellClickEventHandler(selectedRowData);
        });
      } else {
        this.okButton.visible = false;
        this.okButton.includeInLayout = false;
        this.enableDisableComponents(false);
      }
      this.categoryGrid.rowColorFunction = ( dataContext, dataIndex, color, dataField ) => {
        return this.drawRowBackground( dataContext, dataIndex, color , dataField);
      };
      this.requestParams = [];
      this.actionPath = 'spreadProfilesPCM.do?';
      this.requestParams["moduleId"] = this.moduleId;
      if (this.screenName != "add") {
        this.requestParams["spreadId"] = this.spreadId;
        this.requestParams["maintEventId"] = this.maintEventId;
        this.requestParams["processPointName"] = this.processName;
        this.requestParams["processId"] = this.processId;
        this.actionMethod = "method=displayChangeOrViewProcess";
      }
      else {
        this.actionMethod = "method=displayAddProcess";
      }
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);

      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      
      this.inputData.send(this.requestParams);
      if (this.screenName == "add") {
        //Gets the messge and title of add help screen
        this.message = SwtUtil.getAMLMessages('spreadMaintenanceScreen.message.add_help_message');
        this.title = SwtUtil.getAMLMessages('spreadMaintenanceScreen.add_help_title');
      } else if (this.screenName == "view") {
        //Gets the message and title of view help screen
        this.message = SwtUtil.getAMLMessages('spreadMaintenanceScreen.message.view_help_message');
        this.title = SwtUtil.getAMLMessages('spreadMaintenanceScreen.view_help_title');
      } else if (this.screenName == "change") {
        //Gets the message and title of change help screen
        this.message = SwtUtil.getAMLMessages('spreadMaintenanceScreen.message.change_help_message');
        this.title = SwtUtil.getAMLMessages('spreadMaintenanceScreen.change_help_title');
      }
      this.okButton.label = "Ok";
      this.cancelButton.label = "Cancel";//String(SwtUtil.getCommonMessages('button.cancel'));
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "onLoad", this.errorLocation);
    }
  }

  drawRowBackground( dataContext, dataIndex, color,dataField ): string {
  
    let rColor: string;
    try {
      let colorFlag: string;
    // if(dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.entity){
      if('Y'==dataContext.slickgrid_rowcontent[dataField].isDeletedRow){
        rColor = "#ff808a";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isNewRow){
        rColor = "#c6efce";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isUpdatedRow){
        rColor = "#ee82ee";
     }
      
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }
  /**
   * startOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * endOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
    if (this.screenName != "view") {
      this.enableDisableComponents(true);
    }  
  }

  /**
   * inputDataResult
   * param data: ResultEvent
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(data): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        this.lastRecievedJSON = data;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        // Condition to check lastRecievedXML not equal to prevRecievedXML
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {          
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {
            this.helpURL = this.jsonReader.getSingletons().helpurl;
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {
              //this.processCategoryComboBox.setComboData(this.jsonReader.getSelects()['select'].find(x => x.id === 'processCategoryList').option, false);
              this.processCategoryComboBox.setComboData(this.jsonReader.getSelects(), false);
              
              this.categoryGrid.CustomGrid(data.SpreadProcessPoints.grid.metadata);
              this.categoryGrid.gridData = data.SpreadProcessPoints.grid.rows;
              this.categoryGrid.setRowSize = data.SpreadProcessPoints.grid.rows.size;

              if (this.screenName != "add") {
                this.processNameInput.text = this.processName;
                this.timeInput.text = this.time;
                this.targetInput.text = this.targetValue;
                this.processCategoryComboBox.selectedLabel = this.processCategories;
                // categories
                // [isUpdatedRow: 'Y', previousValue: 'CAT_CALYPSO,CAT_AUD,CAT1', content: 'CAT1,CAT_CALYPSO,CAT_FROM_PAYMENT,CAT_IMMEDIATE']
                // num
                // {content: 1}
                // process
                // [isUpdatedRow: 'Y', previousValue: 'All Except']
                // processId
                // [isUpdatedRow: 'Y', content: '198']
                // processName
                // [isUpdatedRow: 'Y', content: 'proc1']
                // target
                // [isUpdatedRow: 'Y', content: '20']
                // time
                // [isUpdatedRow: 'Y', content: '15:00']
                if(this.selectedDataRow){
                  if(this.selectedDataRow.slickgrid_rowcontent.processName.previousValue)
                    this.processNameInput.toolTipPreviousValue=this.selectedDataRow.slickgrid_rowcontent.processName.previousValue;

                    if(this.selectedDataRow.slickgrid_rowcontent.time.previousValue)
                     this.timeInput.toolTipPreviousValue=this.selectedDataRow.slickgrid_rowcontent.time.previousValue;

                    if(this.selectedDataRow.slickgrid_rowcontent.target.previousValue)
                     this.targetInput.toolTipPreviousValue=this.selectedDataRow.slickgrid_rowcontent.target.previousValue;
                    
                    if(this.selectedDataRow.slickgrid_rowcontent.process.previousValue)
                      this.processCategoryComboBox.toolTipPreviousValue=this.selectedDataRow.slickgrid_rowcontent.process.previousValue;




                }
                
                if (this.screenName != "view") {
                  if (this.processCategoryComboBox.selectedValue == "ALLEXCEPT") {
                    this.informationLabel.text="Please select all categories which you do NOT wish to be processed";
                  } else if (this.processCategoryComboBox.selectedValue == "ONLY") {
                    this.informationLabel.text="Please select all categories which you wish to be processed";
                  } else {
                    this.informationLabel.text="";
                  }
                } else {
                  if (this.processCategoryComboBox.selectedValue == "ALLEXCEPT") {
                    this.informationLabel.text="The selected categories will NOT be processed";
                  } else if (this.processCategoryComboBox.selectedValue == "ONLY") {
                    this.informationLabel.text="The selected categories will be processed";
                  }
                }
                if (this.categories != "All") {
                  let categoriesArray = this.categories.split(",");
                  for (var item = 0; item < this.categoryGrid.dataProvider.length; item++) {
                    if (categoriesArray.indexOf(this.categoryGrid.dataProvider[item].categoryId) != -1) {
                      this.categoryGrid.dataProvider[item].Checked = "Y";
                    } else {
                      this.categoryGrid.dataProvider[item].Checked = "N";
                    }
                  }
                  this.categoryGrid.refresh();
                } else {
                  this.changeComboProcessCategory();
                }
                if (this.screenName == "view") {
                  this.categoryGrid.enableColumn("Checked", false);
                }
              } else {
                this.changeComboProcessCategory();
              }

            }
            this.prevRecievedJSON = this.lastRecievedJSON;
          }
        }
      }
    } catch (error) {
      console.log("error:   ", error);
      SwtUtil.logError(error, this.moduleId, "className", "inputDataResult", this.errorLocation);
    }
  }

  /**
   * inputDataFault
   * param event:  FaultEvent
   * This is a callback , used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    this.swtAlert.error(event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail);
  }

  /**
   * disableComponents()
   *
   * Method called to disable components
   *
   */
  enableDisableComponents(enable): void {
    try {
      if (enable) {
        this.processNameInput.enabled = true;
        this.timeInput.enabled = true;
        this.targetInput.enabled = true;
        this.processCategoryComboBox.enabled = true;
        this.selectAllCheckbox.enabled = true;
        this.categoryGrid.enableColumn("Checked", true);
      } else {
        this.processNameInput.enabled = false;
        this.timeInput.enabled = false;
        this.targetInput.enabled = false;
        this.processCategoryComboBox.enabled = false;
        this.selectAllCheckbox.enabled = false;
        this.categoryGrid.enableColumn("Checked", false);
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'disableComponents', this.errorLocation);
      console.log(e);
    }
  }

  /**
   * keyDownEventHandler
   * param event: KeyboardEvent
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  keyDownEventHandler(event: KeyboardEvent): void {
    try {
      //Currently focussed property name
      let eventString: string = Object(focusManager.getFocus()).name;
      if ((event.keyCode == Keyboard.ENTER)) {
        if (eventString == "saveButton") {
          this.save();
        } else if (eventString == "cancelButton") {
          //this.closeBtn();
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "className", "keyDownEventHandler");
    }
  }

  /**
   * Event Handler when clicking a cell in the category grid 
   *
   * @param {*} selectedRowData
   */
  cellClickEventHandler(selectedRowData): void {
    try {
      if (selectedRowData.target.field == "Checked") {
        let totalChecked = 0;
        for (var item = 0; item < this.categoryGrid.dataProvider.length; item++) {
          if (this.categoryGrid.dataProvider[item].Checked == "Y") {
            totalChecked++;
          }
          if (totalChecked == this.categoryGrid.dataProvider.length) {
            this.selectAllCheckbox.selected = true;
          } else {
            this.selectAllCheckbox.selected = false;
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'cellClickEventHandler', this.errorLocation);
    }
  }

  /**
   * Event handled when changing the Process category combobox value
   *
   * @param {*} event
   */
  changeComboProcessCategory() {
    if (this.processCategoryComboBox.selectedValue == "ALLEXCEPT" || this.processCategoryComboBox.selectedValue == "ONLY") {
      this.categoryGrid.enableColumn("Checked", true);
      this.selectAllCheckbox.enabled = true;
      this.selectAllCheckbox.selected = false;
      for (var item = 0; item < this.categoryGrid.dataProvider.length; item++) {
        this.categoryGrid.dataProvider[item].Checked = "N";
      }
      if (this.processCategoryComboBox.selectedValue == "ALLEXCEPT") {
        this.informationLabel.text="Please select all categories which you do NOT wish to be processed";
      } else {
        this.informationLabel.text="Please select all categories which you wish to be processed";

      }

    } else if (this.processCategoryComboBox.selectedValue == "ALL") {
      this.categoryGrid.enableColumn("Checked", false);
      this.selectAllCheckbox.enabled = false;
      this.selectAllCheckbox.selected = true;
      for (var item = 0; item < this.categoryGrid.dataProvider.length; item++) {
        this.categoryGrid.dataProvider[item].Checked = "Y";
      }
      this.informationLabel.text = "";
    }
  }

  /**
   * Select/Deselect all the categories in the grid
   * @param event 
   */
  selectDeselectAll(event) {
    for (var item = 0; item < this.categoryGrid.dataProvider.length; item++) {
      if (this.selectAllCheckbox.selected) {
        this.categoryGrid.dataProvider[item].Checked = "Y";
      } else {
        this.categoryGrid.dataProvider[item].Checked = "N";
      }
    }
    this.categoryGrid.refresh();
  }

  /**
   * Save added/updated spread detail
   *
   */
  save(): void {
    if (this.processNameInput.text.trim() == "") {
      this.swtAlert.warning('Process Name can not be saved as just spaces');
      return;
    }
    let isCategorySelected: boolean;
    let areAllCategoriesSelected: boolean;
    let totalCategoriesSelected = 0;
    for (var item = 0; item < this.categoryGrid.dataProvider.length; item++) {
      if (this.categoryGrid.dataProvider[item].Checked == 'Y') {
        totalCategoriesSelected++;
      }
    }
    isCategorySelected = (totalCategoriesSelected > 0) ? true : false;
    areAllCategoriesSelected = (totalCategoriesSelected == this.categoryGrid.dataProvider.length) ? true : false;
    if ((!this.timeInput.text) || (!this.targetInput.text)) {
      this.swtAlert.warning('Please fill all mandatory fields (marked with *)');
    } else if (this.timesList.indexOf(this.timeInput.text) == -1 && this.namesList.indexOf(this.processNameInput.text) == -1) {
      if (this.processCategoryComboBox.selectedValue == "ONLY" && !isCategorySelected) {
        this.swtAlert.warning('Based on selected process, you need to choose at least one category');
      } else if (this.processCategoryComboBox.selectedValue == "ALLEXCEPT" && areAllCategoriesSelected) {
        this.swtAlert.warning('Based on selected process category, you need to exclude at least one category');
      } else {
        let categoriesToReturn = "";
        let processCategoriesToReturn = "";
        if (this.selectAllCheckbox.selected) {
          categoriesToReturn = "All";
          processCategoriesToReturn = this.processCategoryComboBox.selectedValue == "ONLY" ? "All" : this.processCategoryComboBox.selectedLabel;
        } else {
          if (this.processCategoryComboBox.selectedValue == "ALLEXCEPT" && !isCategorySelected) {
            processCategoriesToReturn = "All";
            categoriesToReturn = "All";
          } else {
            processCategoriesToReturn = this.processCategoryComboBox.selectedLabel;
            for (var item = 0; item < this.categoryGrid.dataProvider.length; item++) {
              if (this.categoryGrid.dataProvider[item].Checked == "Y") {
                categoriesToReturn = categoriesToReturn + this.categoryGrid.dataProvider[item].categoryId + ","
              }
              
            }
            categoriesToReturn = categoriesToReturn.substring(0, categoriesToReturn.length - 1);
          }
        }
        this.result = {
          "time": this.timeInput.text, "target": this.targetInput.text+"", "process": processCategoriesToReturn,
          "categories": categoriesToReturn, "processName": this.processNameInput.text
        };
        SwtPopUpManager.getPopUpById("spreadDetails").close();
      }
    } else if (this.namesList.indexOf(this.processNameInput.text) == -1) {
      this.swtAlert.warning('Cannot have two process points being defined with same time');
    } else {
      this.swtAlert.warning('Cannot have two process points being defined with same name');
    }
  }

  /**
    * Used to validate time input (HH:mm)
    *
    * @param {SwtTextInput} textInput
    * @returns {boolean}
    */
  validateTime(textInput: SwtTextInput): boolean {
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text && validateFormatTime(textInput) == false) {
      this.swtAlert.warning('Please enter a valid time', null);
      textInput.text = "";
      return false;
    } else {
      textInput.text = textInput.text.substring(0,5);
      return true;
    }
  }

  /**
   * popupClosed
   * Method to close child windows when this screen is closed
   */
  popupClosed(): void {
    this.close();
  }

  /**
   * dispose
   * This is a event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.requestParams = null;
      this.baseURL = null;
      this.actionMethod = null;
      this.actionPath = null;
      this.close();
    } catch (error) {
      console.log(error, this.moduleId, "className", "dispose");
    }
  }

  /**
   * close
   * param event
   *  called on click of close button to close the window
   */
  closeBtn(): void {
    try {
      SwtPopUpManager.getPopUpById("spreadDetails").close();
    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "close", this.errorLocation);
    }
  }

  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      SwtHelpWindow.open(this.baseURL + "help/aml/fr/index.html#page=groupes-des-regles.html");
      SwtHelpWindow.resizable(true);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }
  }

  /**
   * printPage
   *
   * param event
   *
   * Method to get call the action to get reports
   */
  printPage(): void {
    try {
      this.actionMethod = "type=" + "pdf";
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod = this.actionMethod + "&currentModuleId=" + this.moduleId;
      this.actionMethod = this.actionMethod + "&print=" + "PAGE";
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", this.errorLocation);
    }
  }
}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: SpreadDetails }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SpreadDetails],
  entryComponents: [SpreadDetails],
  providers: [{provide: 'spreadDetails', useValue: SpreadDetails}]
})
export class SpreadDetailsModule { }
