<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%">
    <!--<HBox paddingLeft="15" paddingTop="5" height="4.5%">
      <SwtLabel #recScenarioLbl></SwtLabel>
      <SwtCheckBox #recScenarioCheck (change)="changeScenarioCheck()"></SwtCheckBox>
    </HBox>-->
    <SwtFieldSet style="height: 340px; padding-top: 5px; padding-right: 5px; padding-left: 5px; color:blue;" legendText="Populating data on Instance records">
      <Grid width="100%" height="75%">
       <GridRow width="100%" height="70" paddingTop="5" paddingBottom="5">
          <GridItem width="181">
            <SwtLabel #uniqExpLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="uniqExpLockImg" #uniqExpLockImg
          (click)="changeTxtAreaStatus()" 
          visible="false" includeInLayout="true">
          </SwtImage> 
          <GridItem width="80%" height="100%">
            <SwtTextArea #uniqueExpression width="100%" height="100%"></SwtTextArea>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="181">
            <SwtLabel #acctIdLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="acctLockImg" #acctLockImg 
          (click)="changeComboStatus(acctIdCombo,acctLockImg)" 
          visible="false" includeInLayout="true">
          </SwtImage> 
          <GridItem width="30%">
            <SwtComboBox #acctIdCombo (change)="changeComboBox('acctId','ACCOUNT_ID',acctIdCombo)"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow >
          <GridItem width="181">
            <SwtLabel #valueDateLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="valDateLockImg" #valDateLockImg 
          (click)="changeComboStatus(valueDateCombo,valDateLockImg)" 
          visible="false" includeInLayout="true">
          </SwtImage> 
          <GridItem width="30%">
            <SwtComboBox #valueDateCombo (change)="changeComboBox('valueDate','VALUE_DATE', valueDateCombo)"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="181">
            <SwtLabel #signColLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="signLockImg" #signLockImg 
          (click)="changeComboStatus(signColCombo,signLockImg)" 
          visible="false" includeInLayout="true">
          </SwtImage> 
          <GridItem width="30%">
            <SwtComboBox #signColCombo (change)="changeComboBox('signCol', 'SIGN', signColCombo)"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="181">
            <SwtLabel #mvtColLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="mvtLockImg" #mvtLockImg 
          (click)="changeComboStatus(mvtColCombo,mvtLockImg)" 
          visible="false" includeInLayout="true">
          </SwtImage> 
          <GridItem width="30%">
            <SwtComboBox #mvtColCombo (change)="changeComboBox('mvtCol', 'MOVEMENT_ID', mvtColCombo)"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="181">
            <SwtLabel #matchColLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="matchLockImg" #matchLockImg 
          (click)="changeComboStatus(matchColCombo,matchLockImg)" 
          visible="false" includeInLayout="true">
          </SwtImage> 
          <GridItem width="30%">
            <SwtComboBox #matchColCombo (change)="changeComboBox('matchCol', 'MATCH_ID', matchColCombo )"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="181">
            <SwtLabel #sweepColLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="sweepLockImg" #sweepLockImg 
          (click)="changeComboStatus(sweepColCombo,sweepLockImg)" 
          visible="false" includeInLayout="true">
          </SwtImage> 
          <GridItem width="30%">
            <SwtComboBox #sweepColCombo (change)="changeComboBox('sweepCol', 'SWEEP_ID', sweepColCombo)"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="181">
            <SwtLabel #payColLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="payLockImg" #payLockImg 
          (click)="changeComboStatus(payColCombo,payLockImg)" 
          visible="false" includeInLayout="true">
          </SwtImage> 
          <GridItem width="30%">
            <SwtComboBox #payColCombo (change)="changeComboBox('payCol', 'PAYMENT_ID', payColCombo)"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="181">
            <SwtLabel #otherIdColLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="otherIdLockImg" #otherIdLockImg 
          (click)="changeComboStatus(otherIdColCombo,otherIdLockImg)" 
          visible="false" includeInLayout="true">
          </SwtImage> 
          <GridItem width="30%">
            <SwtComboBox #otherIdColCombo (change)="changeComboBox('otherIdCol', 'OTHER_ID', otherIdColCombo )"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="181">
            <SwtLabel #otherIdTypeLbl></SwtLabel>
          </GridItem>
          <SwtImage width="19"  height="19" id="otherIdTypeLockImg" #otherIdTypeLockImg 
          (click)="changeComboStatus(otherIdTypeCombo,otherIdTypeLockImg)" 
          visible="false" includeInLayout="true">
          </SwtImage>
          <GridItem width="220">
            <SwtComboBox #otherIdTypeCombo (change)="changeComboBox('otherIdType', 'OTHER_ID_TYPE', otherIdTypeCombo)" dataLabel="otherIdTypeCombo" ></SwtComboBox>
          </GridItem>

          <GridItem width="200">
            <SwtLabel #otherIdTypeDesc id='otherIdTypeDesc' fontWeight="normal"></SwtLabel>
          </GridItem>

        </GridRow>
      </Grid>
    </SwtFieldSet>
        <SwtFieldSet style="height: 110px; padding-top: 5px; padding-right: 5px; padding-left: 5px; color:blue;" legendText="Additional workflow monitor parameters">
        <Grid width="100%" height="100%">
        <GridRow  height="35" paddingTop="5">
          <GridItem width="200">
            <SwtLabel #treeBreakDown1Lbl></SwtLabel>
          </GridItem>
          <GridItem width="30%">
            <SwtComboBox #treeBreakDown1Combo (change)="saveComboVal('treeBreakDown1Combo')"></SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow height="23">
          <GridItem width="200">
            <SwtLabel #treeBreakDown2Lbl></SwtLabel>
          </GridItem>
          <GridItem width="30%">
            <SwtComboBox #treeBreakDown2Combo (change)="saveComboVal('treeBreakDown2Combo')"></SwtComboBox>
          </GridItem>
        </GridRow>
        
        <GridRow>
          <GridItem width="187">
            <SwtLabel #alertInstanceColumnLbl paddingTop="5"></SwtLabel>
          </GridItem>
          <GridItem width="85%">
            <SwtMultiselectCombobox #alertInstanceColumnCombo id='alertInstanceColumnCombo' showAbove="true" width="620" height="100" dropHeight="25" visible ="true"></SwtMultiselectCombobox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtFieldSet>
    <SwtFieldSet style="height: 80; padding-right: 5px; padding-left: 5px; color:blue;" legendText="Expiry">
      <Grid width="100%" height="100%">
        <GridRow>
          <GridItem width="200">
            <SwtLabel #instExpLbl></SwtLabel>
          </GridItem>
          <GridItem width="30%">
            <SwtNumericInput #instExpTxt textAlign="right"></SwtNumericInput>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="200">
            <SwtLabel #raiseLbl></SwtLabel>
          </GridItem>
          <GridItem>
            <VBox width="100%" height="100%">
              <SwtRadioButtonGroup #raiseRadioGroup   id="raiseRadioGroup"
                                   align="vertical" (change)="requiredField()"  >
                <SwtRadioItem #radioNo id="radioNo" value="N"    selected="true" groupName="raiseRadioGroup"></SwtRadioItem>
                 <SwtRadioItem #radioAfter id="radioAfter" value="A" groupName="raiseRadioGroup" ></SwtRadioItem>
              </SwtRadioButtonGroup>

            </VBox>
            <HBox marginTop="15" marginLeft="5">
            <SwtNumericInput #afterMinTxt width="50"  height="21" textAlign="right" enabled="false"></SwtNumericInput>
            <SwtLabel #minLbl fontWeight="normal"></SwtLabel>
            </HBox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtFieldSet>
  </VBox>
</SwtModule>
