<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="appContainer" width="100%" height="100%" verticalGap="0" paddingTop="5" paddingBottom="5" paddingRight="5" paddingLeft="5">
      <SwtCanvas id="swtControlBar" height="13%" width="100%">
          <Grid width="100%" height="100%">
            <GridRow width="100%"  height="100%" paddingLeft="10">
              <GridItem width="15%" height="100%">
                <SwtLabel #entityLabel styleName="labelRight" paddingTop="5" paddingLeft="5" fontWeight="bold"></SwtLabel>
              </GridItem>
              <GridItem width="40%" height="100%">
                <SwtComboBox dataLabel="entity"
                             id="cbEntity"
                             #cbEntity
                             (change)="updateEntityData()">

                </SwtComboBox>
              </GridItem>
              <GridItem width="100%" height="100%">
                <SwtLabel id="selectedEntity" #selectedEntity
                         styleName="labelRight"
                         paddingTop="5"
                         fontWeight="normal"></SwtLabel>
              </GridItem>
            </GridRow>
            <GridRow width="100%"  height="100%" paddingLeft="10">
              <GridItem width="15%" height="100%">
                <SwtLabel #currencyLabel
                         styleName="labelRight"
                         paddingTop="5"
                         paddingLeft="5"
                         fontWeight="bold"></SwtLabel>
              </GridItem>
              <GridItem width="40%" height="100%">
                <SwtComboBox dataLabel="currency" id="cbCurrency"  #cbCurrency
                                (change)="changeCombo($event)">
                </SwtComboBox>
              </GridItem>
              <GridItem width="100%" height="100%">
                <SwtLabel id="selectedCurrency" #selectedCurrency
                         styleName="labelRight"
                         paddingTop="5"
                        fontWeight="normal"></SwtLabel>
              </GridItem>
            </GridRow>
          </Grid>
      </SwtCanvas>
      <SwtCanvas id="swtControl" height="85" width="100%">
        <Grid width="100%" height="100%" paddingLeft="5" paddingTop="8" >
          <GridRow width="100%" height="30%">
            <GridItem width="100%" paddingLeft="10" height="100%">
              <SwtCheckBox #multiplier id="multiplier"></SwtCheckBox>
            </GridItem>
            <GridItem width="100%" paddingLeft="2" height="100%">
              <SwtCheckBox #hideWeekend id="hideWeekend"></SwtCheckBox>
            </GridItem>
            <GridItem width="100%" paddingLeft="1" height="100%">
              <SwtCheckBox #cumulativeTotal id="cumulativeTotal"></SwtCheckBox>
            </GridItem>
          </GridRow>
          <GridRow width="100%" height="30%">
            <GridItem width="50%" paddingLeft="10" height="100%">
              <SwtCheckBox #hideZeroValue id="hideZeroValue"></SwtCheckBox>
            </GridItem>
            <GridItem width="100%" paddingLeft="2" height="100%">
              <SwtCheckBox #hideZeroSum id="hideZeroSum"></SwtCheckBox>
            </GridItem>
          </GridRow>
          <GridRow width="100%" height="30%">
            <GridItem width="100%" paddingLeft="10" height="100%">
              <SwtCheckBox id="hideTotal" #hideTotal></SwtCheckBox>
            </GridItem>
            <GridItem width="100%" paddingLeft="2" height="100%">
              <SwtCheckBox id="hideAssumption" #hideAssumption></SwtCheckBox>
            </GridItem>
            <GridItem width="100%" paddingLeft="1" height="100%">
              <SwtCheckBox id="hideScenario" #hideScenario ></SwtCheckBox>
            </GridItem>
          </GridRow>
        </Grid>
      </SwtCanvas>
      <HBox id="hbGridContainer" width="100%" height="100%">
        <SwtCanvas width="50%" height="99%" style="border: 1px solid gray" border="false">
          <VBox  width="100%" height="100%" styleName="controlBar">
              <SwtLabel #userTempLabel  paddingLeft="10" fontWeight="normal" height="15" ></SwtLabel>
            <SwtCanvas width="100%" height="100%" id="cvTemplateGridContainer" #cvTemplateGridContainer></SwtCanvas>
            <HBox paddingTop="8" styleName="hgroupTop" paddingLeft="15" width="50%" height="35" minHeight="50">
              <SwtButton id="btnTemplateAdd" #btnTemplateAdd
                        buttonMode="true"
                        (click)="templateOptionClick('add')"></SwtButton>
              <SwtButton id="btnTemplateChange"
                         #btnTemplateChange
                        buttonMode="true"
                        (click)="templateOptionClick('change')"></SwtButton>
              <SwtButton id="btnTemplateDelete" #btnTemplateDelete
                        buttonMode="true"
                        (click)="templateOptionClick('delete')"></SwtButton>
            </HBox>
          </VBox>
        </SwtCanvas>
        <SwtCanvas width="50%" height="99%" style="border: 1px solid gray" border="false">
            <VBox  width="100%" height="100%" styleName="controlBar">
                <SwtLabel #userBuckLabel paddingLeft="10" fontWeight="normal" height="15"></SwtLabel>
             <SwtCanvas width="100%" height="100%" id="cvBucketGridContainer" #cvBucketGridContainer></SwtCanvas>
             <HBox paddingTop="8" styleName="hgroupTop" paddingLeft="15" width="50%" height="34" minHeight="50" bottom="16">
              <SwtButton id="btnBucketAdd"
                         #btnBucketAdd
                        buttonMode="true"
                         (click)="addNewBucket()"></SwtButton>
              <SwtButton id="btnBucketChange"
                         #btnBucketChange
                        buttonMode="true"
                         (click)="changeBucket()"></SwtButton>
              <SwtButton id="btnBucketDelete" #btnBucketDelete
                         buttonMode="true"
                         (click)="deleteBucket()"></SwtButton>
            </HBox>
          </VBox>
      </SwtCanvas>
      </HBox>
      <SwtCanvas id="cvSaveOptions" width="100%" height="35">
        <HBox width="100%">
          <HBox width="90%">
          <SwtButton id="btnSave" #btnSave
                    (click)="updateData()"
                    buttonMode="true"></SwtButton>
          <SwtButton id="btnCancel" #btnCancel
                    (click)="closeHandler()"
                    buttonMode="true"></SwtButton>
        </HBox>
          <HBox horizontalAlign="right" width="5%">
          <SwtHelpButton id="helpIcon"
                         #helpIcon (click)="doHelp($event)">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
        </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>


