import { Component, OnInit, OnDestroy, ElementRef, NgModule, ViewChild } from '@angular/core';
import {
  SwtModule,
  SwtAlert,
  Logger,
  ScreenVersion,
  CommonService,
  SwtToolBoxModule,
  SwtUtil,
  SwtCommonGrid,
  JSONReader,
  HTTPComms,
  Timer,
  CommonLogic,
  ExternalInterface,
  SwtDateField,
  SwtLoadingImage,
  SwtCanvas,
  SwtButton,
  SwtText,
  SwtDataExport,
  ExportEvent,
  SwtLabel,
  focusManager,
  SwtPopUpManager, TitleWindow, Alert, CommonUtil, ContextMenuItem, JSONViewer
} from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/compiler/src/core';
import { SwtStatusItemRenderer } from './SwtStatusItemRenderer';
import {RatePopUp} from '../RatePopUp/RatePopUp';

declare var instanceElement: any;

@Component({
  selector: 'app-interface-monitor',
  templateUrl: './InterfaceMonitor.html',
  styleUrls: ['./InterfaceMonitor.css']
})
export class InterfaceMonitor extends SwtModule implements OnDestroy, OnInit {

  @ViewChild('startDate', { read: SwtDateField }) startDate: SwtDateField;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('dataInputContainer', { read: SwtCanvas }) dataInputContainer: SwtCanvas;
  @ViewChild('dataGridContainer', { read: SwtCanvas }) dataGridContainer: SwtCanvas;
  @ViewChild('storProcContainer', { read: SwtCanvas }) storProcContainer: SwtCanvas;
  @ViewChild('buttonContainer', { read: SwtCanvas }) buttonContainer: SwtCanvas;
  @ViewChild('refreshButton', { read: SwtButton }) refreshButton: SwtButton;
  @ViewChild('rateButton', { read: SwtButton }) rateButton: SwtButton;
  @ViewChild('closeButton', { read: SwtButton }) closeButton: SwtButton;
  @ViewChild('dataBuildingText', { read: SwtText }) dataBuildingText: SwtText;
  @ViewChild('lostConnectionText', { read: SwtText }) lostConnectionText: SwtText;
  @ViewChild('exportContainer') exportContainer: SwtDataExport;
  @ViewChild('dataExport') dataExport: SwtDataExport;
  @ViewChild('lastRefTime', { read: SwtText }) lastRefTime: SwtText;
  @ViewChild('lastRefText', { read: SwtText }) lastRefText: SwtText;


  @ViewChild('lblDate', { read: SwtLabel }) lblDate: SwtLabel;





  private swtAlert: SwtAlert;
  private logger: Logger;
  public moduleId = 'Predict';

  /* - START -- Screen Name and Version Number ---- */
  private screenName = 'Interface Monitor';
  private versionNumber = '1.1.00023';
  private releaseDate = '27 September 2019';
  /* - END -- Screen Name and Version Number ---- */

  /**
			 * Display Objects
			 **/
  private mainMonitorGrid: SwtCommonGrid = null;
  private specificsGrid: SwtCommonGrid;
  private bottomGrid: SwtCommonGrid;

  /**
   * Data Objects
   **/
  private lastRecievedJSON;
  private prevRecievedJSON;
  private detailedLastRecievedJSON;
  private detailedPrevRecievedJSON;
  private lastRecievedBottomJSON;
  private prevRecievedBottomJSON;
  private internalReceivedJSON;
  private jsonReader: JSONReader = new JSONReader();
  private detailedJsonReader: JSONReader = new JSONReader();
  private bottomJsonReader: JSONReader = new JSONReader();
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private showJSONPopup: any;
  /**
   * Communication Objects
   **/
  private updateRefreshRate: HTTPComms = new HTTPComms(this.commonService);
  private inputData: HTTPComms = new HTTPComms(this.commonService);
  private detailedData: HTTPComms = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];
  private tempFromDate: Date;

  /**
   * Timer Objects
   **/
  private autoRefresh: Timer;
  private refreshRate = 10;

  private win: TitleWindow;

  /**
   * Logic Objects
   **/

  private globalInterfaceId: string;
  private daysInMilliseconds: number = 1000 * 60 * 60 * 24;
  private systemDate: string;
  private sessionToDate: string;
  private dateCompare = "";
  private commonLogic: CommonLogic = new CommonLogic();
  private interfaceMonLogic: CommonLogic = new CommonLogic();

  /* Engine related objects */

  private tempSelectedIndex = -1;
  private fromPCM = null;
  private errorLocation = 0;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Interface Monitor Screen', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.lblDate.text = SwtUtil.getPredictMessage('interfacemonitor.date', null);
    this.startDate.toolTip = SwtUtil.getPredictMessage('tooltip.enterDate', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.Refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refreshWindow', null);
    this.rateButton.label = SwtUtil.getPredictMessage('accountmonitorbutton.Rate', null);
    this.rateButton.toolTip = SwtUtil.getPredictMessage('tooltip.rateButton', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.lastRefText.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);

    // this.screenName = "add";
    instanceElement = this;
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }






  /**
   * Upon completion of loading into the flash player this method is called
   **/
  onLoad(): void {
    try {
      this.mainMonitorGrid = this.dataGridContainer.addChild(SwtCommonGrid) as SwtCommonGrid;
      this.bottomGrid = this.storProcContainer.addChild(SwtCommonGrid) as SwtCommonGrid;
      this.initializeMenus();

      this.fromPCM = ExternalInterface.call('eval', 'fromPCM');
      this.interfaceMonLogic.dateFormat = ExternalInterface.call('eval', 'dateFormat');
      this.systemDate = ExternalInterface.call('eval', 'dbDate');
      this.interfaceMonLogic.testDate = this.systemDate;

      this.startDate.selectedDate = new Date(CommonUtil.parseDate(this.interfaceMonLogic.testDate, this.interfaceMonLogic.dateFormat.toUpperCase()));
      this.startDate.formatString = this.interfaceMonLogic.dateFormat;

      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      this.actionPath = "interfacemonitor.do?";
      this.actionMethod = "method=getInterfaceMonitorDetails";

      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.encodeURL = false;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.detailedData.cbStart = this.startOfComms.bind(this);
      this.detailedData.cbStop = this.endOfComms.bind(this);
      this.detailedData.cbResult = (data) => {
        this.detailededDataResult(data);
      };

      this.mainMonitorGrid.ITEM_CLICK.subscribe((selectedRowData) => {
        this.cellLogic(selectedRowData);
      });

      // this.backGroundTimer.addEventListener("BackgroundTimer", (function handle(evt): void {
      //   this.updateData("no");
      // }));

      ExportEvent.subscribe((type) => {
        this.export(type);
      });
      this.requestParams["fromPCM"] = this.fromPCM;
      this.requestParams["fromDate"] = this.interfaceMonLogic.convertDate(this.interfaceMonLogic.testDate);
      this.requestParams["toDate"] = this.interfaceMonLogic.convertDate(this.interfaceMonLogic.testDate);
      this.requestParams["systemDate"] = this.systemDate;
      this.requestParams["autoRefresh"] = "no";
      this.requestParams["internal"] = "true";

      this.inputData.send(this.requestParams);

      this.logger.info('method [onLoad] - END ');
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'InterfaceMonitor.ts', 'onLoad', this.errorLocation);
      this.logger.error('InterfaceMonitor - method [onLoad] - error ', error);

    }
  }


  /**
			 * Part of a callback function to all for control of the loading swf from the HTTPComms Object
			 **/
  private startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.disableInterface();
    this.startDate.enabled = false;

  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  private endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.enableInterface();
    this.startDate.enabled = true;
  }

  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   *
   * @param event:FaultEvent
   */
  private inputDataFault(event): void {
    this.lostConnectionText.visible = true;
    if (this.autoRefresh != null) {
      if (!this.autoRefresh.running) {
        this.autoRefresh.start();
      }
    }
  }

  /**
   * Disable interface, turn off certain UI elements when a request is made to the server
   **/
  private disableInterface(): void {
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
    this.exportContainer.enabled = false;
  }

  /**
   * Enable interface, turn on certain UI elements when a request is made to the server
   **/
  private enableInterface(): void {
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
    this.exportContainer.enabled = true;
  }

  /**
   * This function is used to load the main and the bottom grid on load of the screen.<br>
   *
   * @param event - ResultEvent
   */
  private inputDataResult(event): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.internalReceivedJSON = null;
        if (this.lastRecievedJSON.interfacemonitor && this.lastRecievedJSON.interfacemonitor.heartbeat != undefined) {
          this.internalReceivedJSON = new JSONReader();
          this.internalReceivedJSON = this.lastRecievedJSON.interfacemonitor.heartbeat;
        }
        // delete  this.lastRecievedJSON.interfacemonitor.heartbeat;

        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        this.lostConnectionText.visible = false;
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          if (this.jsonReader.getRequestReplyStatus()) {
            let lastRef = this.jsonReader.getScreenAttributes()["lastRefTime"];
            this.lastRefTime.text = lastRef.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');
            if (!this.jsonReader.isDataBuilding()) {
              this.dataBuildingText.visible = false;
              this.sessionToDate = this.jsonReader.getScreenAttributes()["sessionToDate"];
              this.systemDate = this.jsonReader.getScreenAttributes()["sysDateFrmSession"];
              this.dateCompare = (this.jsonReader.getScreenAttributes()["dateComparing"]);
              this.startDate.showToday = false;
              let dateFormat: string = this.jsonReader.getScreenAttributes()["dateformat"];
              let dateFrom: string = this.jsonReader.getScreenAttributes()["from"];
              this.startDate.selectedDate = new Date(CommonUtil.parseDate(dateFrom, dateFormat.toUpperCase()));
              this.startDate.formatString = dateFormat;
              this.tempFromDate = this.startDate.selectedDate;
              if (dateFormat == "dd/MM/yyyy") {
                this.startDate.toolTip = SwtUtil.getPredictMessage('tooltip.enterValueDate', null);
              } else {
                this.startDate.toolTip = SwtUtil.getPredictMessage('tooltip.ValueDateMMDDYY', null);
              }

              let columnData = event.interfacemonitor.grid.metadata;
              this.mainMonitorGrid.moduleId = this.moduleId;
              this.mainMonitorGrid.saveColumnOrder = true;
              this.mainMonitorGrid.saveWidths = true;
              this.mainMonitorGrid.uniqueColumn = "interfaceId";
              this.mainMonitorGrid.colWidthURL(this.baseURL + "interfacemonitor.do?fromPCM=" + ExternalInterface.call('eval', 'fromPCM') + "&");
              this.mainMonitorGrid.colOrderURL(this.baseURL + "interfacemonitor.do?fromPCM=" + ExternalInterface.call('eval', 'fromPCM') + "&");
              this.mainMonitorGrid.CustomGrid(columnData);

              let rowData = event.interfacemonitor.grid.rows;
              for (let i = 0; i < this.mainMonitorGrid.columnDefinitions.length; i++) {
                let column = this.mainMonitorGrid.columnDefinitions[i];
                if (column.field == "enabled") {
                  let greenLight = "./assets/images/new-tick.gif";
                  let redLight = "./assets/images/new-cross.gif";
                  column['properties'] = {
                    enabled: false,
                    columnName: 'enabled',
                    imageEnabled: greenLight,
                    imageDisabled: redLight,
                    _toolTipFlag: true,
                    style: ' display: block; margin-left: auto; margin-right: auto;'
                  };
                  this.mainMonitorGrid.columnDefinitions[i].editor = null;
                  this.mainMonitorGrid.columnDefinitions[i].formatter = SwtStatusItemRenderer;
                }


                if (column.field == "enginestatus") {
                  for (let j = 0; j < rowData.row.length; j++) {
                    if (rowData.row[j].enginestatus.content == "RUNNING") {
                      // // Sets the RUNNING
                      // _text = "RUNNING"
                      // // Sets the font color to black if the engine status is RUNNING
                      // engineStatus.setStyle("color", "Black");
                      // rowData.enginestatus = '<font color="Black">RUNNING</font>';
                      // rowData[j].enginestatus.setStyle("color", "Black");

                      rowData.row[j].enginestatus.negative = false;
                    } else if (rowData.row[j].enginestatus.content == "STOPPED") {
                      // // Sets the STOPPED
                      // _text = "STOPPED";
                      // // Sets the font color to red if the engine status is STOPPED
                      // engineStatus.setStyle("color", "Red");
                      // rowData[j].enginestatus = "<font color=\"red\">STOPPED</font>";
                      // rowData.enginestatus.setStyle("color", "Red");
                      // this.mainMonitorGrid.gridData = (data) => {
                      //   const row = data as SwtCommonGrid;
                      //   if (row.gridData.enginestatus === "STOPPED") {
                      //     row.setStyle("color", "Red");
                      //   }
                      // };
                      rowData.row[j].enginestatus.negative = true;
                    }
                  }

                }

              }

              this.mainMonitorGrid.gridData = rowData;
              this.mainMonitorGrid.setRowSize = this.jsonReader.getRowSize();

              // // Iterates through the columns and sets the ItemRenderer in the ClassFactory
              // for (var i: int = 0; i < this.columnCount; i++) {
              //   // Sets the InterfaceItemRenderer
              //   var iir: ClassFactory = new ClassFactory(CellItemRenderer);
              //   // Gets the column value
              //   (columns[i] as CustomColumn).itemRenderer = iir;
              //   // Checks the column type is bool
              //   if ((this.columns[i] as CustomColumn).type == "bool") {
              //     // Sets the SwtImage renderer if the column type is 'bool'
              //     var cir: ClassFactory = new ClassFactory(SwtImage);
              //     // Sets the corresponding properties for the Item Renderer
              //     cir.properties = { _dataField: (this.columns[i] as CustomColumn).dataField, _dataGrid: this };
              //     (columns[i] as CustomColumn).itemRenderer = cir;
              //   }
              //   // Checks the column type is booltext
              //   if ((this.columns[i] as CustomColumn).type == "booltext") {
              //     // Sets the SwtStatusItemRenerer in the ClassFactory if the column type is booltext
              //     var cir: ClassFactory = new ClassFactory(SwtStatusItemRenderer);
              //     // Sets the Coulumn data with the CustomColumn
              //     (columns[i] as CustomColumn).itemRenderer = cir;
              //   }
              //   /*Start: Added for Mantis 1676[Interface Monitor: Simplified to make more user friendly] by Marshal on 14-Dec-2011*/
              //   // Validates the empty(dummy) column and disable sorting for that
              //   if ((this.columns[i] as CustomColumn).type == "empty") {
              //     (this.columns[i] as CustomColumn).sortable = false;
              //   }
              //   /*End: Added for Mantis 1676[Interface Monitor: Simplified to make more user friendly] by Marshal on 14-Dec-2011*/
              // }

              // Listens to the collection change in the datagrid .
              // this.mainMonitorGrid.addEventListener(CollectionEvent.COLLECTION_CHANGE, modifyExportContainer, true);
              // set the current selected index to tempselectedindex
              this.mainMonitorGrid.selectedIndex = this.tempSelectedIndex;
              if (this.jsonReader.getRowSize() == 0) {
                this.globalInterfaceId = null;
              }
              this.inputData.cbResult = (data) => {
                this.inputDataBottomGridResult(data);
              };

              this.actionMethod = "method=getStoredProcudureDetails";
              this.inputData.url = this.baseURL + this.actionPath + this.actionMethod + "&fromPCM=" + this.fromPCM;
              this.inputData.cbFault = this.inputDataFault.bind(this);
              this.inputData.send(this.requestParams);
            } else {
              this.dataBuildingText.visible = true;
            }
            this.refreshRate = parseInt(this.jsonReader.getRefreshRate(),10);
            if (this.autoRefresh == null) {
              this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
              this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
            } else {
              this.autoRefresh.delay(this.refreshRate * 1000);
            }
            // Sets the previous received xml
            this.prevRecievedJSON = this.lastRecievedJSON;
          } else {
            if (this.startDate.selectedDate != null) {
              this.swtAlert.warning(SwtUtil.getPredictMessage('alert.warning.unavailableSI', null), SwtUtil.getPredictMessage('screen.error'), Alert.OK, null, this.alertListener, null);
            }
          }
          if (this.autoRefresh != null) {
            if (!this.autoRefresh.running) {
              this.autoRefresh.start();
            }
          }
        }
      }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'InterfaceMonitor.ts', 'inputDataResult', this.errorLocation);
      this.logger.error('InterfaceMonitor - method [inputDataResult] - error ', error);
    }
  }

  /**
   *  alertListener
   */
  public alertListener(eventObj): void {
    Alert.okLabel = "Ok";
    if (eventObj.detail == Alert.OK) {
      ExternalInterface.call('close');

    }
  }

  /**
			 * This function refresh the screen as per the auto refresh rate given.
			 *
			 * @param event:TimerEvent
			 */
   dataRefresh(event): void {
    this.updateData("no");
    this.autoRefresh.stop();
  }

  /**
			 * Update the data, this is called whenever a fresh of the data is required.
			 * This could be called from either a change in a combobox selection of from the timer
			 *
			 * @param autoRefresh : String
			 **/
  updateData(autoRefresh: string): void {
    this.logger.info('method [updateData] - START ');
    this.requestParams = [];
    try {

      this.requestParams["fromDate"] = this.startDate.text;
      this.requestParams["toDate"] = this.startDate.text;
      this.requestParams["sessionToDate"] = this.sessionToDate;
      this.requestParams["autoRefresh"] = autoRefresh;
      this.requestParams["systemDate"] = this.systemDate;
      this.requestParams["internal"] = "true";
      this.actionPath = "interfacemonitor.do?";
      this.actionMethod = "method=getInterfaceMonitorDetails";
      this.inputData.cbResult = (data) => {
            this.inputDataResult(data);
          };


      this.requestParams["autoRefresh"] = "yes";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod + "&fromPCM=" + this.fromPCM;
      this.inputData.send(this.requestParams);
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'InterfaceMonitor.ts', 'updateData', this.errorLocation);
      this.logger.error('InterfaceMonitor - method [updateData] - error ', error);
    }
  }


  /***
   * 
   * 
   */
  private detailededDataResult(event): void {
    this.logger.info('method [detailededDataResult] - START ');
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.detailedLastRecievedJSON = event;
        this.detailedJsonReader.setInputJSON(this.detailedLastRecievedJSON);
        if (!(JSON.stringify(this.detailedLastRecievedJSON) === JSON.stringify(this.detailedPrevRecievedJSON))) {
          if (this.detailedJsonReader.getRequestReplyStatus()) {
            if (!this.detailedJsonReader.isDataBuilding()) {
              this.specificsGrid.gridData = this.detailedJsonReader.getGridData();
              this.specificsGrid.setRowSize = this.detailedJsonReader.getRowSize();
              let singletonsXML = this.detailedJsonReader.getSingletons();
              let labelledSingletonsXML = this.detailedLastRecievedJSON.labelled_singletons;
              this.detailedPrevRecievedJSON = this.detailedLastRecievedJSON;

            }
          } else {
            this.swtAlert.error(this.detailedJsonReader.getRequestReplyMessage() + "\n" + this.detailedJsonReader.getRequestReplyLocation(),
              SwtUtil.getPredictMessage('screen.error'));
          }
        }
      }
    } catch (error) {

      SwtUtil.logError(error, this.moduleId, 'InterfaceMonitor.ts', 'detailededDataResult', this.errorLocation);
      this.logger.error('InterfaceMonitor - method [detailededDataResult] - error ', error);
    }
  }


  /**
   * This function serves when the rate button is clicked it opens the popup withthe
   * autorefresh rate set.
   * Refresh popup screen
   **/
  public rateHandler(): void {
    try {
      this.refreshRate = parseInt(this.jsonReader.getScreenAttributes()["refresh"],10);
      this.win = SwtPopUpManager.createPopUp(this, RatePopUp, {
        title: "Refresh Rate",
        refreshText: this.refreshRate
      });
      this.win.width = '340';
      this.win.height = '150';
      this.win.id = "optionsWindow";
      this.win.showControls = true;
      this.win.enableResize = false;
      this.win.isModal = true;
      this.win.display();
    } catch (error) {
    SwtUtil.logError(error, this.moduleId,"Interface Monitor"  , "rateHandler", this.errorLocation);
  }
  }


  /***
   * 
   * 
   */
  private inputDataBottomGridResult(event): void {
    this.logger.info('method [inputDataBottomGridResult] - START ');

    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedBottomJSON = event;
        this.bottomJsonReader.setInputJSON(this.lastRecievedBottomJSON);
        this.lostConnectionText.visible = false;
        if (this.bottomJsonReader.getRequestReplyStatus()) {
          if (!this.bottomJsonReader.isDataBuilding()) {
            this.dataBuildingText.visible = false;
            if ((this.lastRecievedBottomJSON != this.prevRecievedBottomJSON)) {
              if (this.bottomGrid == null) {
                this.bottomGrid.setStyle("fontSize", '11');
                this.bottomGrid.setStyle("paddingTop", "-1");
              }

              let columnData = event.interfacemonitor.grid.metadata;

              // Get datagrid column data 
              this.bottomGrid.moduleId = this.moduleId;
              this.bottomGrid.CustomGrid(columnData);
              this.bottomGrid.gridData = this.bottomJsonReader.getGridData();
              this.bottomGrid.setRowSize = this.bottomJsonReader.getRowSize();
              this.prevRecievedBottomJSON = this.lastRecievedBottomJSON;
            }
          } else {
            this.dataBuildingText.visible = true;
          }
        } else {
          this.swtAlert.show(this.bottomJsonReader.getRequestReplyMessage() + "\n" + this.bottomJsonReader.getRequestReplyLocation(),  SwtUtil.getPredictMessage('screen.error'));
        }
      }
    } catch (error) {

      SwtUtil.logError(error, this.moduleId, 'InterfaceMonitor.ts', 'inputDataBottomGridResult', this.errorLocation);
      this.logger.error('InterfaceMonitor - method [inputDataBottomGridResult] - error ', error);
    }
  }

  /**
			 * This function is called when the CellClick event is triggered.<br>
			 * This function is used for two purposes:<br>
			 * 1. To open Input Exceptions screen when the data is 'Filtered' and 'Bad' column is clicked.<br>
			 * 2. To display data in the bottom grid when any Interface Id is selected in Interface Monitor screen.<br>
			 *
			 * @param event - CellEvent
			 */
  private cellLogic(event): void {
    // Checks whether the column name of the clicked data is Filtered or Bad and provides the drill down option
    if (event.target.field == "filtered" || event.target.field == "bad") {
      let fieldName = event.target.field;
      let isClickable = (event.target.data.slickgrid_rowcontent[fieldName] ) ? event.target.data.slickgrid_rowcontent[fieldName].clickable : null;
      let value = (event.target.data.slickgrid_rowcontent[fieldName] ) ? event.target.data.slickgrid_rowcontent[fieldName].content : null;
      let status = (event.target.data.slickgrid_rowcontent[fieldName] ) ? event.target.data.slickgrid_rowcontent[fieldName].status : null;
      if (isClickable) {
        this.clickLink(fieldName,value, status);
      }
    }
    // Assigns the selected index to track the row selection
    this.tempSelectedIndex = this.mainMonitorGrid.selectedIndex;

  }


  /**
   * clickLink
   */
  clickLink(field,value, status) {
    try {
      let amount = 100;
      this.actionPath = 'interfaceexceptions.do?';
      this.actionMethod = 'fromPCM=' + this.fromPCM;
      this.actionMethod = this.actionMethod + '&m='+ value.toString();
      this.actionMethod = this.actionMethod + '&n='+ amount;
      this.actionMethod = this.actionMethod + '&fromFlex=true';
      this.actionMethod = this.actionMethod + '&type='+ ""+this.mainMonitorGrid.selectedItem.interfaceId.content;
      this.actionMethod = this.actionMethod + '&status=' + ""+status;
      this.actionMethod = this.actionMethod + '&p=1' ;
      this.actionMethod = this.actionMethod + '&fromDate=' + this.startDate.text;
      this.actionMethod = this.actionMethod + '&toDate=' +  this.startDate.text;
      ExternalInterface.call('clickMessages', this.actionPath + this.actionMethod);
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "InterfaceMonitor", "clickLink", this.errorLocation);
    }
  }

  /**
   * This function is used to validate and submit the changes made in From date value.
   *
   */
  updateFromDate(): void {
    this.logger.info('method [updateFromDate] - START');
    this.requestParams = [];
    let fromDate: Date = this.startDate.selectedDate;
    let toDate: Date = this.startDate.selectedDate;
    let formattedStartDate = "";
    let formattedEndDate = "";
    let datePattern: RegExp = null;
    try {
        this.requestParams["fromDate"] = this.startDate.text;
        this.requestParams["toDate"] = this.startDate.text;
        this.requestParams["systemDate"] = this.systemDate;
        this.requestParams["autoRefresh"] = "no";
        this.requestParams["internal"] = "true";
        this.actionPath = "interfacemonitor.do?";
        this.actionMethod = "method=getInterfaceMonitorDetails";
        this.inputData.cbResult = (data) => {
            this.inputDataResult(data);
          };

        this.requestParams["autoRefresh"] = "yes";
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod + "&fromPCM=" + this.fromPCM;

        this.inputData.send(this.requestParams);
        this.logger.info('method [updateFromDate] - END');
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'InterfaceMonitor.ts', 'onLoad', this.errorLocation);
      this.logger.error('method [updateFromDate] - error ', error);
    }
  }

  /**
   * This function is used to validate the From date value
   * @event:FocusEvent
   */
  validateStartDate(event: FocusEvent): void {
    this.logger.info('method [validateStartDate] - START');
    if (Object(focusManager.getFocus()).id == "closeButton") {
      event.stopImmediatePropagation();
      ExternalInterface.call("close");

    } else {

      this.commonLogic.validateDate(event, this.startDate, this.startDate.selectedDate);

    }
    this.logger.info('method [validateStartDate] - END');
  }

  /**
   * This function handles the submitted refresh rate and updates it.
   *
   * @param event:Event
   */
  private saveRefreshRate(res): void {
    if (isNaN(parseInt(res,10))) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceMonitor.rateNAN', null));
    } else {
      let selectedRateBelowMinimum = false;
      this.refreshRate = Number(res);
      if (this.refreshRate < 5) {
        this.refreshRate = 5;
        selectedRateBelowMinimum = true;
      }
      if (selectedRateBelowMinimum) {
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceMonitor.rateBelowMin', null), "Warning");
      }
      if (this.autoRefresh) {
        this.autoRefresh.delay(this.refreshRate * 1000);
      }
      let refreshRequest: string = ExternalInterface.call("getUpdateRefreshRequest", this.refreshRate);
      if (refreshRequest != null && refreshRequest != "") {
        this.updateRefreshRate.encodeURL = false;
        this.updateRefreshRate.url = this.baseURL + refreshRequest;
        this.updateRefreshRate.send();
      }
      this.updateData("no");
    }
  }


  optionDateResult() {
    console.log("optionDateResult");
  }
  /**
   * close the window from the close button
   **/
  closeHandle(): void {
    ExternalInterface.call("close");
  }

  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'AttributeUsageAdd', 'doHelp', this.errorLocation);
    }
  }


  /**
   * This function is used to initialize the menus related to Interface Monitor screen
   *
   */
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
    let summaryMenu: ContextMenuItem = new ContextMenuItem('Show JSON - Interface Summary Details');
    let storedProcMenu: ContextMenuItem = new ContextMenuItem('Show JSON - Stored Procedure Details');
    let internalMenu: ContextMenuItem = new ContextMenuItem('Show JSON - Internal');
    summaryMenu.MenuItemSelect = this.showJSONSummarySelect.bind(this);
    storedProcMenu.MenuItemSelect = this.showBottomJSONSelect.bind(this);
    internalMenu.MenuItemSelect = this.showJSONInternel.bind(this);
    this.screenVersion.svContextMenu.customItems.push(summaryMenu);
    this.screenVersion.svContextMenu.customItems.push(storedProcMenu);
    this.screenVersion.svContextMenu.customItems.push(internalMenu);
    this.contextMenu=this.screenVersion.svContextMenu;
  }


  /**
   * This function is used to show the Summary details in the Show XML screen
   */
    showJSONSummarySelect(event): void {
    let fromDate = this.startDate.selectedDate;
    let toDate = this.startDate.selectedDate;
    let formattedStartDate = "";
    let formattedEndDate="";
    // Checks the format for From and To date
    if (this.startDate.formatString == "dd/mm/yyyy") {
      // Sets the From date as per the format
      formattedStartDate=fromDate ? (fromDate.getDate() < 10 ? "0" + fromDate.getDate() : fromDate.getDate()) + "/" + (fromDate.getMonth() + 1 < 10 ? "0" + (fromDate.getMonth() + 1) : fromDate.getMonth() + 1) + "/" + fromDate.getFullYear() : null;
      formattedEndDate=toDate ? (toDate.getDate() < 10 ? "0" + toDate.getDate() : toDate.getDate()) + "/" + (toDate.getMonth() + 1 < 10 ? "0" + (toDate.getMonth() + 1) : toDate.getMonth() + 1) + "/" + toDate.getFullYear() : null;
    } else {
      // Sets the From date as per the format
      formattedStartDate=fromDate ? ((fromDate.getMonth() + 1) < 10 ? "0" + (fromDate.getMonth() + 1) : (fromDate.getMonth() + 1)) + "/" + (fromDate.getDate() < 10 ? "0" + (fromDate.getDate()) : fromDate.getDate()) + "/" + fromDate.getFullYear() : null;
      formattedEndDate=toDate ? ((toDate.getMonth() + 1) < 10 ? "0" + (toDate.getMonth() + 1) : (toDate.getMonth() + 1)) + "/" + (toDate.getDate() < 10 ? "0" + (toDate.getDate()) : toDate.getDate()) + "/" + toDate.getFullYear() : null;
    }

    if (this.lastRecievedJSON != null) {
      this.showJSONPopup = SwtPopUpManager.createPopUp(this,
        JSONViewer,
        {
          jsonData: this.lastRecievedJSON,
        });
      this.showJSONPopup.width = "700";
      this.showJSONPopup.height = "400";
      this.showJSONPopup.enableResize = false;
      this.showJSONPopup.showControls = true;
      this.showJSONPopup.title = "Summary details JSON";
      this.showJSONPopup.isModal = true;
      this.showJSONPopup.display();
    } else {
      // Alert if there is no data to show
      this.swtAlert.warning( SwtUtil.getPredictMessage('alert.interfaceMonitor.noData', null), SwtUtil.getPredictMessage('label.warningSummary', null));
    }
  }


  /**
   * This function is used to show the internal in the Show XML screen
   */
  showJSONInternel(event): void  {
    if (this.internalReceivedJSON != null) {
      this.showJSONPopup = SwtPopUpManager.createPopUp(this,
        JSONViewer,
        {
          jsonData: this.internalReceivedJSON,
        });
      this.showJSONPopup.width = "700";
      this.showJSONPopup.height = "400";
      this.showJSONPopup.enableResize = false;
      this.showJSONPopup.showControls = true;
      this.showJSONPopup.title = "Internal JSON";
      this.showJSONPopup.isModal = true;
      this.showJSONPopup.display();
    } else {
      // Alert if there is no data to show
      this.swtAlert.warning( SwtUtil.getPredictMessage('alert.interfaceMonitor.noData', null), SwtUtil.getPredictMessage('label.warningInternal', null));
    }
  }

  showBottomJSONSelect(event): void {
    if (this.lastRecievedBottomJSON != null) {
      this.showJSONPopup = SwtPopUpManager.createPopUp(this,
        JSONViewer,
        {
          jsonData: this.lastRecievedBottomJSON,
        });
      this.showJSONPopup.width = "700";
      this.showJSONPopup.height = "400";
      this.showJSONPopup.enableResize = false;
      this.showJSONPopup.showControls = true;
      this.showJSONPopup.title = "Bottomgrid details JSON";
      this.showJSONPopup.isModal = true;
      this.showJSONPopup.display();
    } else {
      // Alert if there is no data to show
      this.swtAlert.warning( SwtUtil.getPredictMessage('alert.interfaceMonitor.noData', null), SwtUtil.getPredictMessage('label.warningSoredProcedureDetails', null));
}

  }


  /**
			 * This function is called when collection change in the data grid.<br>
			 * The main purpose of this function is to Enable/Disable the
			 * Export icons based on the availability of records on the DataGrid.<br>
			 *
			 * @param e - CollectionEvent
			 */
  modifyExportContainer(e): void {
    // check the account grid has rows
    if (this.mainMonitorGrid != null && this.mainMonitorGrid.dataProvider != null && this.mainMonitorGrid.dataProvider.length > 0) {
      // enable the csv/pdf/excel button.
      this.exportContainer.enabled = true;
    } else {
      // disable csv/pdf/excel for empty datagrid row.
      this.exportContainer.enabled = false;
    }

  }

  /**
   * report
   *
   * @param type: String
   *
   * This is a report icon action handler method
   */
  export(event): void {
    try {

      // ExternalInterface.call("report", this.selectedItemsListEntity, this.selectedEntity.text, type, this.dateFormat, this.startDate.text, this.applyThresholdCheck.selected ? 'Y' : 'N', this.applyMultiplierCheck.selected ? 'Y' : 'N', this.spreadCheck.selected ? 'Y' : 'N', this.value.selected ? 'Y' : 'N');

      let selects = [];
      let filter = new Object();
      let formattedFromDate: string;
      let formattedToDate: string;
      let fromDate: Date = this.startDate.selectedDate;
      let toDate: Date = this.startDate.selectedDate;
      let isTotalGrid = false;

      if (this.startDate.formatString == "dd/mm/yyyy") {
        formattedFromDate = fromDate ? (fromDate.getDate() < 10 ? "0" + fromDate.getDate() : fromDate.getDate()) + "/" + (fromDate.getMonth() + 1 < 10 ? "0" + (fromDate.getMonth() + 1) : fromDate.getMonth() + 1) + "/" + fromDate.getFullYear() : null;
        formattedToDate = toDate ? (toDate.getDate() < 10 ? "0" + toDate.getDate() : toDate.getDate()) + "/" + (toDate.getMonth() + 1 < 10 ? "0" + (toDate.getMonth() + 1) : toDate.getMonth() + 1) + "/" + toDate.getFullYear() : null;
      } else {
        formattedFromDate = fromDate ? ((fromDate.getMonth() + 1) < 10 ? "0" + (fromDate.getMonth() + 1) : (fromDate.getMonth() + 1)) + "/" + (fromDate.getDate() < 10 ? "0" + (fromDate.getDate()) : fromDate.getDate()) + "/" + fromDate.getFullYear() : null;
        formattedToDate = toDate ? ((toDate.getMonth() + 1) < 10 ? "0" + (toDate.getMonth() + 1) : (toDate.getMonth() + 1)) + "/" + (toDate.getDate() < 10 ? "0" + (toDate.getDate()) : toDate.getDate()) + "/" + toDate.getFullYear() : null;
      }
      selects.push("Start Date=" + formattedFromDate);
      selects.push("End Date=" + formattedToDate);
      // Calls the function to export the data
      // Modified for Mantis 1676[Interface Monitor: Simplified to make more user friendly] by Marshal on 15-Dec-2011
      // this.exportContainer.convertData((lastRecievedXML.grid.metadata.columns.column.(@type!= "empty") as XMLList), mainMonitorGrid, (lastRecievedXML.grid.totals.total as XMLList), selects, event.data.toString());
      if(this.lastRecievedJSON.interfacemonitor.grid.totals) {
        isTotalGrid = true;
      }
      this.exportContainer.convertData(this.lastRecievedJSON.interfacemonitor.grid.metadata.columns, this.mainMonitorGrid, (this.lastRecievedJSON.interfacemonitor.grid.totals), selects, event.toString(), isTotalGrid);

    } catch (error) {
      console.log("error", error);
      SwtUtil.logError(error, this.moduleId, 'InterfaceMonitor - ', 'export', this.errorLocation);
    }
  }


}



// Define lazy loading routes
const routes: Routes = [
  { path: '', component: InterfaceMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [InterfaceMonitor],
  entryComponents: []
})
export class InterfaceMonitorModule { }
