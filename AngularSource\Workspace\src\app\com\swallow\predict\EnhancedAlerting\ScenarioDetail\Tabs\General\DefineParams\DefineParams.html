<SwtModule (creationComplete)="onLoad()"  width="100%" height="100%">
  <VBox width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingBottom="5" paddingRight="5">
    <SwtCanvas width="100%" height="90%">
      <VBox width="100%" height="100%">
    <Grid width="100%" height="20%" paddingTop="10">
      <GridRow paddingLeft="10">
        <GridItem width="25%">
          <SwtLabel #scenarioIdLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput #scenarioIdtxt width="200" enabled="false"></SwtTextInput>
        </GridItem>
      </GridRow>
      <GridRow paddingLeft="10">
        <GridItem>
          <SwtLabel #paramDesc fontWeight="normal"></SwtLabel>
        </GridItem>
      </GridRow>
    </Grid>
    <SwtCanvas #paramCanvas id="paramCanvas" width="100%" height="75%"></SwtCanvas>
      </VBox>

    </SwtCanvas>
    <SwtCanvas width="100%" height="10%" paddingTop="8">
      <HBox width="100%" horizontalGap="5" paddingLeft="10">
        <SwtButton #okButton enabled="true" (click)="gridParamsToXml()"></SwtButton>
        <SwtButton #cancelButton enabled="true" (click)="closePopup()"></SwtButton>
      </HBox>
      <HBox width="100%" horizontalGap="5" horizontalAlign="right" paddingRight="10" >
<SwtButton #scanQuery enabled="true" label='Scan Query' (click)="scanQueryForParams()" ></SwtButton>
        <SwtButton #addButton enabled="true" (click)="enableRow()" ></SwtButton>
        <SwtButton #deleteButton enabled="false" (click)="deleteRow()" ></SwtButton>
        
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
