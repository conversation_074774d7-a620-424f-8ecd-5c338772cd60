<SwtModule (creationComplete)="onLoad()" width="100%" height="100%" >
  <VBox  height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas width="100%" height="25%">
        <Grid width="100%" height="100%" verticalGap="2">
          <GridRow #gridRowEntity>
            <GridItem width="10%">
              <SwtLabel id="entityLabel" fontWeight="bold"
                        #entityLabel >
              </SwtLabel>
            </GridItem>
            <GridItem width="15%">
              <SwtComboBox id="entityCombo" #entityCombo
                           (change)="entityComboChange()"
                           dataLabel="entity"
                           width="135">
              </SwtComboBox>
            </GridItem>
            <GridItem width="10%">
              <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal"
                        textAlign="left">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow #gridRowCurrency>
            <GridItem width="10%">
              <SwtLabel id="currencyLabel" fontWeight="bold"
                        #currencyLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width="15%">
              <SwtComboBox id="ccyCombo" #ccyCombo
                           (change)="currencyComboChange()"
                           dataLabel="currency"
                           width="85">
              </SwtComboBox>
            </GridItem>
            <GridItem width="10%">
              <SwtLabel id="selectedCcy" #selectedCcy fontWeight="normal">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow #gridRowCalculateGroup height="26%">
            <GridItem width="10%">
              <SwtLabel #calculateGroupLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width="40%">
              <SwtRadioButtonGroup #calculateGroup   id="calculateGroup"
                                   (change) ="changeProcessOption()"
                                   align="vertical"
                                   width="100%">
                <SwtRadioItem #screens id="screens" value="M"  width="100%" marginBottom="5"  groupName="calculateGroup" ></SwtRadioItem>
                <SwtRadioItem #screenAndReports id="screenAndReports" value="A"  width="100%"  groupName="calculateGroup" selected="true" ></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
          </GridRow>
          <GridRow #gridRowDateGroup>
            <GridItem width="10%">
              <SwtLabel  #dateGroupLabel></SwtLabel>
            </GridItem>
            <GridItem width="40%">
              <SwtRadioButtonGroup #dateGroup   id="dateGroup"
                                   (change) ="changeDateGroup()"
                                   align="horizontal"
                                   width="100%">
                <SwtRadioItem #single id="single" value="S" width="145"  groupName="dateGroup" selected="true" ></SwtRadioItem>
                <SwtRadioItem #range id="range" value="R"  width="120" groupName="dateGroup"  ></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
          </GridRow>
          <GridRow #gridRowDates>
            <GridItem width="10%">
            </GridItem>
            <GridItem width="10%">
              <SwtDateField id="startDate"
                            #startDate
                            width="70"
                            (change)="ChangeDate(startDate,'fromDateAsString')"
                            restrict="0-9/">
              </SwtDateField>
            </GridItem>
            <GridItem #toHbox width="50%">
              <SwtLabel #toDateLabel id="toDateLabel"  fontWeight="normal" width="30">
              </SwtLabel>
              <SwtDateField id="toDate" #toDate
                            width="70"
                            (change)="ChangeDate(toDate, 'toDateAsString')"
                            restrict="0-9/">
              </SwtDateField>
            </GridItem>
          </GridRow>
        </Grid>
    </SwtCanvas>
    <SwtCanvas #gridCanvas width="100%" height="70%"></SwtCanvas>
    <SwtCanvas  id="canvasButtons"   width="100%" height="5%" marginTop="5">
      <HBox width="100%">
        <HBox paddingLeft="5" width="70%">
          <SwtButton [buttonMode]="true"
                     id="processButton"
                     #processButton
                     (click)="launchCalculationProcess()">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                     id="cancelButton"
                     #cancelButton
                     (click)="cancelCalculation()">
          </SwtButton>
        </HBox>
        <HBox paddingRight="5"  width="30%">
          <HBox width="90%">
            <SwtLabel id="labelStatus"
                      #labelStatus
                      fontSize="12"
                      fontWeight="bold">

            </SwtLabel>
            <SwtLoadingImage #loadingImage></SwtLoadingImage>
          </HBox>
          <HBox width="10%" paddingTop="2">
            <SwtHelpButton id="helpIcon"
                           #helpIcon
                           (click)="doHelp()">
            </SwtHelpButton>
          </HBox>

        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
