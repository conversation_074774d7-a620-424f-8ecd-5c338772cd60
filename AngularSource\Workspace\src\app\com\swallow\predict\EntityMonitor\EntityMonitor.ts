import {
  Component,
  Inject,
  OnInit,
  ViewChild,
  ModuleWithProviders,
  NgModule,
  ElementRef,
  ViewEncapsulation,
  HostListener
} from '@angular/core';
import {
  SwtCanvas,
  SwtCommonGrid,
  CommonService,
  HTTPComms,
  SwtUtil,
  JSONReader,
  SwtLoadingImage,
  SwtComboBox,
  SwtDateField,
  SwtLabel,
  SwtCheckBox,
  SwtModule,
  SwtPopUpManager,
  SwtButton,
  SwtAlert,
  ExternalInterface,
  SwtToolBoxModule,
  SwtRadioItem,
  SwtDataExport,
  SwtHelpButton,
  ContextMenuItem,
  JSONViewer,
  SwtRadioButtonGroup,
  ExportEvent,
  CommonUtil,
  SwtTotalCommonGrid,
  Timer,
  ScreenVersion,
  SwtGroupedCommonGrid, SwtGroupedTotalCommonGrid, HBox, VBox, EnhancedAlertingTooltip
} from "swt-tool-box";
import {RouterModule, Routes} from "@angular/router";
import moment from "moment";
import { AlertingRenderer } from '../EnhancedAlerting/Render/AlertingRenderer';
import { Observable } from 'rxjs';
import 'rxjs/add/observable/fromEvent';
declare var instanceElement: any;
@Component({
  selector: 'app-entity-monitor',
  templateUrl: './EntityMonitor.html',
  styleUrls: ['./EntityMonitor.css'],
  encapsulation: ViewEncapsulation.None
})
export class EntityMonitor extends SwtModule implements OnInit {
  @ViewChild('imgShowHideControlBar') imgShowHideControlBar: SwtButton;
  @ViewChild('imgShowHideButtonBar') imgShowHideButtonBar: SwtButton;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('optionsButton') optionsButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('swtControlBar') swtControlBar: SwtCanvas;
  @ViewChild('swtControlBarHbox') swtControlBarHbox: HBox;
  @ViewChild('cvGridContainer') cvGridContainer: SwtCanvas;
  @ViewChild('cvTotalsContainer') cvTotalsContainer: SwtCanvas;
  @ViewChild('swtButtonBar') swtButtonBar: SwtCanvas;
  @ViewChild('ccyLabel') ccyLabel: SwtLabel;
  @ViewChild('currGrpCombo') currGrpCombo: SwtComboBox;
  @ViewChild('selectedGroup') selectedGroup: SwtLabel;
  @ViewChild('dateLabel') dateLabel: SwtLabel;
  @ViewChild('startDate') startDate: SwtDateField;
  @ViewChild('chkEntityOffset') chkEntityOffset: SwtCheckBox;
  @ViewChild('breakdown') breakdown: SwtRadioButtonGroup;
  @ViewChild('accountRadio') accountRadio: SwtRadioItem;
  @ViewChild('movementRadio') movementRadio: SwtRadioItem;
  @ViewChild('bookRadio') bookRadio: SwtRadioItem;
  @ViewChild('groupRadio') groupRadio: SwtRadioItem;
  @ViewChild('metagroupRadio') metagroupRadio: SwtRadioItem;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('helpIcon') helpIcon: SwtHelpButton;
  @ViewChild('dataExport') dataExport: SwtDataExport;
  @ViewChild('lastRefText') lastRefText: SwtLabel;
  @ViewChild('dataBuildingText') dataBuildingText: SwtLabel;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
  @ViewChild('vboxCanvas') vboxCanvas: VBox;


  private swtAlert: SwtAlert;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public alertingData = new HTTPComms(this.commonService);
  
  public cancelExport = new HTTPComms(this.commonService);
  private lockMovements = new HTTPComms(this.commonService);
  public filterConfData = new HTTPComms(this.commonService);
  private updateRefreshRate = new HTTPComms(this.commonService);
  public baseURL:string = SwtUtil.getBaseURL();
  private actionMethod:string = "";
  private actionPath:string = "";
  private requestParams = [];
  private systemDate:string;
  private refreshStatus:string = "N";
  private selectIndex: number = -1;
  private ccyCurrency:string = "";
  private columnLength: number = 0;
  //private  backGroundTimer:BackgroundTimer=new BackgroundTimer();
  private tempFontSize:string = "";
  // iable holds the name of the screen
  private screenName:string = "Entity Monitor - SMART Predict"; //TODO ExternalInterface.call('getBundle', 'text', 'label-entityMonitor', 'Entity Monitor - SMART Predict');
  private testDate:string;
  private dateFormat:string;
  private entityMonitorGrid: SwtGroupedCommonGrid;
  private totalsGrid: SwtGroupedTotalCommonGrid;
  public showJSONPopup: any;
  private columnChangeFlag: boolean = false;
  private  controlBarHideFlag:boolean = false;
  private  buttonBarHideFlag:boolean = false;
  private  comboChange:boolean = false;
  private  updateDateFlag:boolean = false;
  private autoRefresh: Timer;
  private refreshRate = 10;
  public screenVersion  = new ScreenVersion(this.commonService);
  private  versionNumber:string="1.1.0008";
  private comboOpen: boolean = false;
  private widthData = new HTTPComms(this.commonService);
  private currentFontSize: string = "";


  tooltipEntityId = null;
  tooltipCurrencyCode = null;
  tooltipFacilityId = null;
  tooltipSelectedDate = null;
  private positionX:number;
  private positionY:number;
  private hostId;
  private entityId;
  private currencyId;
  private selectedNodeId = null;
  private treeLevelValue = null;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }

  public static ngOnDestroy(): any {
    instanceElement = null;
    window['Main'] = null;
  }

ngOnInit(): void {
  instanceElement = this;
  this.entityMonitorGrid = <SwtGroupedCommonGrid>this.cvGridContainer.addChild(SwtGroupedCommonGrid);
  this.totalsGrid = <SwtGroupedTotalCommonGrid>this.cvTotalsContainer.addChild(SwtGroupedTotalCommonGrid);
  this.entityMonitorGrid.lockedColumnCount = 2;
  this.entityMonitorGrid.uniqueColumn = "ccy";
  this.totalsGrid.lockedColumnCount = 2;
  this.entityMonitorGrid.hideHorizontalScrollBar = true;
  this.entityMonitorGrid.listenHorizontalScrollEvent = true;
  this.totalsGrid.fireHorizontalScrollEvent = true;
  this.entityMonitorGrid.forceSameColumnSize = true;
  this.entityMonitorGrid.forceSameColumnException =['ccy'];
  this.entityMonitorGrid.addPreHeaderBackGroundColor = true;
  this.totalsGrid.selectable = false;
  this.currGrpCombo.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.selectCcyGroup", null)
  this.startDate.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.date", null);
  this.accountRadio.label = SwtUtil.getPredictMessage("label.entityMonitor.accountId", null);
  this.accountRadio.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.accountMonitor", null);
  this.movementRadio.label = SwtUtil.getPredictMessage("label.entityMonitor.movementId", null);
  this.movementRadio.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.mvmntBrkdown", null);
  this.bookRadio.label = SwtUtil.getPredictMessage("label.entityMonitor.bookCode", null);
  this.bookRadio.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.bookBrkdown", null);
  this.groupRadio.label = SwtUtil.getPredictMessage("label.entityMonitor.group", null);
  this.groupRadio.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.viewGroupMonitor", null);
  this.metagroupRadio.label = SwtUtil.getPredictMessage("label.entityMonitor.metagroup", null);
  this.metagroupRadio.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.viewMetagroupMonitor", null);
  this.refreshButton.label = SwtUtil.getPredictMessage("button.entityMonitor.refresh",null);
  this.refreshButton.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.refreshWindow",null);
  this.optionsButton.label = SwtUtil.getPredictMessage("button.entityMonitor.option",null);
  this.optionsButton.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.option",null);
  this.closeButton.label = SwtUtil.getPredictMessage("button.entityMonitor.close",null);
  this.closeButton.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.close",null);
  this.chkEntityOffset.label = SwtUtil.getPredictMessage("label.entityMonitor.entityOffset", null)
  this.chkEntityOffset.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.entityOffset", null)
  this.dataBuildingText.text = SwtUtil.getPredictMessage("screen.buildInProgress", null);
  this.lostConnectionText.text = SwtUtil.getPredictMessage("screen.connectionError", null);
  this.lastRefText.text = SwtUtil.getPredictMessage("screen.lastRefresh", null);

  }
  
  private lastSelectedTooltipParams = null;
  getParamsFromParent() {
    
    const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId, selectedNodeId:this.selectedNodeId, treeLevelValue:this.treeLevelValue ,
      tooltipCurrencyCode :this.tooltipCurrencyCode , tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate};
    return params;
  }

  private eventsCreated = false;
  private customTooltip: any = null;
  public createTooltip (){
      if(this.customTooltip && this.customTooltip.close)
        this.removeTooltip();
      try {
      const toolTipWidth = 410;
      this.customTooltip = SwtPopUpManager.createPopUp(parent, EnhancedAlertingTooltip, {
      });
      if (window.innerHeight < this.positionY+450)
        this.positionY=120;
      this.customTooltip.setWindowXY(this.positionX+20, this.positionY);
      this.customTooltip.enableResize = false;
      this.customTooltip.width = ''+toolTipWidth;
      this.customTooltip.height = "450";
      this.customTooltip.enableResize = false;
      this.customTooltip.title = "Alert Summary Tooltip";
      this.customTooltip.showControls = true;
      this.customTooltip.showHeader = false;
      this.customTooltip.parentDocument = this;
      this.customTooltip.processBox = this;
      this.customTooltip.display();
      //event for display list button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe((target) => {
              this.lastSelectedTooltipParams = target.noode.data
              ExternalInterface.call("openAlertInstanceSummary", "openAlertInstSummary", this.selectedNodeId, this.treeLevelValue);
              

            });
          }
        }, 0);


        //event for link to specific button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe((target) => {
              this.getScenarioFacility(target.noode.data.scenario_id);
              this.lastSelectedTooltipParams = target.noode.data
              this.hostId = target.hostId;
              this.entityId = this.lastSelectedTooltipParams.ENTITY;
              this.currencyId = this.lastSelectedTooltipParams.CCY;
            });
          }
        }, 0);

        //event for tree item click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().ITEM_CLICK.subscribe((target) => {
              this.selectedNodeId= target.noode.data.id;
              this.treeLevelValue= target.noode.data.treeLevelValue;
              this.customTooltip.getChild().linkToSpecificButton.enabled = false;
              // if (target.noode.data.count == 1 && target.noode.isBranch ==false ) {
              //   this.customTooltip.getChild().linkToSpecificButton.enabled = true;
              // }
            });
          }
        }, 0);
      
      } catch (error) {
          console.log("SwtCommonGrid -> createTooltip -> error", error)
              
      }
  }

  
  getScenarioFacility(scenarioId) {
    this.requestParams = [];
    this.alertingData.cbStart = this.startOfComms.bind(this);
    this.alertingData.cbStop = this.endOfComms.bind(this);
    this.alertingData.cbFault = this.inputDataFault.bind(this);
    this.alertingData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "scenarioSummary.do?";
    // Define method the request to access
    this.actionMethod = 'method=getScenarioFacility';
    this.requestParams['scenarioId'] = scenarioId;
    this.alertingData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.alertingData.cbResult = (event) => {
      this.openGoToScreen(event);
    };
    this.alertingData.send(this.requestParams);
  }

  openGoToScreen(event) {
    if(event && event.ScenarioSummary && event.ScenarioSummary.scenarioFacility){
    var facilityId = event.ScenarioSummary.scenarioFacility;

    const selectedEntity = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['entity_id'] != null?this.lastSelectedTooltipParams['entity_id']:this.tooltipEntityId;
    const selectedcurrency_code = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['currency_code'] != null?this.lastSelectedTooltipParams['currency_code']:this.tooltipCurrencyCode;
    const selectedmatch_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['match_id'] != null?this.lastSelectedTooltipParams['match_id']:null;
    const selectedmovement_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['movement_id'] != null?this.lastSelectedTooltipParams['movement_id']:null
    const selectedsweep_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['sweep_id'] != null?this.lastSelectedTooltipParams['sweep_id']:null;


      ExternalInterface.call("goTo", facilityId, this.hostId, selectedEntity, selectedmatch_id, selectedcurrency_code, selectedmovement_id, selectedsweep_id, "");
    }
  }


  public removeTooltip (){
    if(this.customTooltip != null)
      this.customTooltip.close();
  }


  itemClickFunction(event) {
    if (event.target != null && event.target.field != null && event.target.field == "alerting" && (event.target.data.alerting=="C" || event.target.data.alerting=="Y")) {
      this.tooltipCurrencyCode = event.target.data.ccy;
      this.tooltipEntityId = "All";
      this.tooltipFacilityId = "ENTITY_MONITOR_CCY_ROW";
      this.tooltipSelectedDate = null;
      setTimeout(() => {
        this.createTooltip();
      }, 100);
      }else {
        this.removeTooltip();
      }
  }


  onLoad() {
    this.systemDate = ExternalInterface.call('eval', 'dbDate');
    this.testDate = this.systemDate;
    this.entityMonitorGrid.columnWidthChanged.subscribe((event) => {
      this.columnWidthChange(event);
    });
    /*this.imgShowHideControlBar.toolTip = ExternalInterface.call('getBundle', 'tip', 'label-hideControlBar', 'Hide Control Bar');
    this.imgShowHideButtonBar.toolTip = ExternalInterface.call('getBundle', 'text', 'label-hideButtonBar', 'Hide Button Bar');*/
    this.optionsButton.enabled = false;
    // Assiging the default date from the system parameter
    this.dateFormat = ExternalInterface.call('eval', 'dateFormat');
    //Initialize the context menu
    this.initializeMenus();
    if (this.chkEntityOffset.selected)
      this.startDate.enabled = false;
    this.startDate.formatString = this.dateFormat;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "entityMonitor.do?";
    this.actionMethod = "method=unspecified";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.entityMonitorGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.itemClickFunction(selectedRowData);
      this.onGridCellClick(selectedRowData);
    });

    Observable.fromEvent(document.body, 'click').subscribe(e => {
      this.positionX=e["clientX"];
      this.positionY=e["clientY"];
    });
    ExportEvent.subscribe((type) => {
      this.export(type);
    });
  }

  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this,this.screenName, this.versionNumber, "");
    let addMenuItem: ContextMenuItem = new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }

  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "400";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.display();
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.disableInterface();
    this.startDate.enabled = false;
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.enableInterface();
    this.startDate.enabled = true;
  }

  /**
   * Disable interface, turn off certain UI elements when a request is made to the server
   */
  disableInterface(): void {
    // Setting the visibility of refresh button to false
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
  }

  /**
   * Enable interface, turn on certain UI elements when a request is made to the server
   */
  enableInterface(): void {
    // Setting the visibility of refresh button to true
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
  }

  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   * @param event :FaultEvent
   */
  inputDataFault(event): void {
    //invalidComms=event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.lostConnectionText.visible = true;
    if (this.autoRefresh != null)
    {
      if (!this.autoRefresh.running)
      {
        this.autoRefresh.start();
      }
    }
  }

  inputDataResult(event): void {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.dataExport.enabled=true;
      this.dataBuildingText.visible = false;
      this.lastRefTime.text = this.jsonReader.getScreenAttributes()["lastRefTime"];

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          if (!this.optionsButton.enabled)
            this.optionsButton.enabled = true;
          if (this.chkEntityOffset.selected)
            this.startDate.enabled = false;
          else
            this.startDate.enabled = true;
          this.startDate.showToday = false;
          this.startDate.text = this.jsonReader.getScreenAttributes()["currentdate"];
          this.currGrpCombo.setComboData(this.jsonReader.getSelects());
          this.selectedGroup.text = this.currGrpCombo.selectedValue;
          this.currentFontSize=this.jsonReader.getScreenAttributes()["currfontsize"];
          if (!this.jsonReader.isDataBuilding()) {
            const obj = {columns: this.jsonReader.getColumnData()};
            const objTotal = {columns: this.lastRecievedJSON.entitymonitor.grid.metadataTotal.columns};
            this.entityMonitorGrid.CustomGrid(obj);
            this.totalsGrid.CustomGrid(objTotal);


            for (let i = 0; i < this.entityMonitorGrid.columnDefinitions.length; i++) {
              let column = this.entityMonitorGrid.columnDefinitions[i];
              if (column.field == "alerting") {
                const alertUrl = "./"+ ExternalInterface.call('eval', 'alertOrangeImage');
                const alerCrittUrl = "./"+ ExternalInterface.call('eval', 'alertRedImage');
                if (this.currentFontSize == "Normal"){
                  column['properties'] = {
                    enabled: false,
                    columnName: 'alerting',
                    imageEnabled: alertUrl,
                    imageCritEnabled:alerCrittUrl,
                    imageDisabled: "",
                    _toolTipFlag: true,
                    style: ' display: block; margin-left: auto; margin-right: auto;'

                  };

                }else{
                  column['properties'] = {
                    enabled: false,
                    columnName: 'alerting',
                    imageEnabled: alertUrl,
                    imageCritEnabled:alerCrittUrl,
                    imageDisabled: "",
                    _toolTipFlag: true,
                    style: 'height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;'

                  };
                }
                this.entityMonitorGrid.columnDefinitions[i].editor = null;
                this.entityMonitorGrid.columnDefinitions[i].formatter = AlertingRenderer;
            }
          }
         

            if (this.jsonReader.getRowSize() >= 1)
            {
              this.entityMonitorGrid.gridData = this.jsonReader.getGridData();

              this.entityMonitorGrid.setRowSize = this.jsonReader.getRowSize();
              this.totalsGrid.gridData = this.jsonReader.getTotalsData();
              this.dataExport.enabled=true;
            }
            else {
              this.dataExport.enabled=false;
            }

            
            // Sets the data grid style based on the font size
            if (this.currentFontSize == "Normal")
            {
              this.entityMonitorGrid.styleName="dataGridNormal";
              this.entityMonitorGrid.rowHeight=18;
              this.totalsGrid.styleName="dataGridNormal";
              this.totalsGrid.rowHeight=18;
            }
            else if (this.currentFontSize == "Small")
            {
              this.entityMonitorGrid.styleName="dataGridSmall";
              this.entityMonitorGrid.rowHeight=15;
              this.totalsGrid.styleName="dataGridSmall";
              this.totalsGrid.rowHeight=15;
            }


            this.refreshRate = parseInt(this.jsonReader.getRefreshRate());
            if (this.autoRefresh == null) {

              this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
              this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
            }
            else {
              this.autoRefresh.delay(this.refreshRate * 1000);
            }
            this.prevRecievedJSON = this.lastRecievedJSON;
          } else {
            this.dataBuildingText.visible = true;

          }
        }
        this.systemDate = this.jsonReader.getScreenAttributes()["sysDate"];

      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }
      }
      if (this.autoRefresh != null) {

        if (!this.autoRefresh.running) {

          this.autoRefresh.start();
        }
      }
    }
    
  }

  dataRefresh(event): void {
    this.refreshStatus = "Y";
    //Check on the comboOpen flag, do not want to update if there is a combobox open as this would cause it to close
     if (!this.comboOpen)
     {
       this.updateData('yes');
     }
    this.autoRefresh.stop();
  }

  /**
   * auto refresh initializer method
   * @param refreshStart :string
   *
   **/
  startAutoRefresh(refreshStart:string): void {
    setTimeout(()=> {
      this.updateData('no');
      if (refreshStart == "Y") {
        this.updateData('no');
        if(this.autoRefresh != null)
        this.autoRefresh.start();
      }
    }, 0)

  }

  @HostListener('window:entityMonitor.refresh', ['$event']) 
  onPaymentSuccess(event): void {
      setTimeout(()=> {
        this.updateData('no');
        if (event.detail && event.detail == "Y") {
          this.updateData('no');
          if(this.autoRefresh != null)
          this.autoRefresh.start();
        }
      }, 0)
  }



  onGridCellClick(selectedRowData):void
  {
    try {
      this.selectIndex= this.entityMonitorGrid.selectedIndex;
      if (this.entityMonitorGrid.selectedIndex != -1)
      {
        let fieldName = selectedRowData.target.field;
        let isAggregate : boolean = selectedRowData.target.name == "T";
        let date = (selectedRowData.target.data.slickgrid_rowcontent[fieldName])?selectedRowData.target.data.slickgrid_rowcontent[fieldName].date : "";
        let ccy = (selectedRowData.target.data.ccy)? selectedRowData.target.data.ccy : "";
        let entity = selectedRowData.target.columnGroup;
        let isClickable = (selectedRowData.target.data.slickgrid_rowcontent[fieldName])? selectedRowData.target.data.slickgrid_rowcontent[fieldName].content : "";
        if (isClickable != "" && !isAggregate && fieldName != "ccy" &&  fieldName != "alerting")
        {
          // if click link value not equal to empty
          // if (StringUtil.trim(new XMLList(entityMonitorGrid.dataProvider)[event.cellDTO["row"]].children()[event.cellDTO["col"]]) != "")
          // {
          // if click the currency code link, then convert the total to particular currency
          if (fieldName == "")
          {
            //updateTotalGrid(event.cellDTO["keyColumnValue"]);

          }
          // open the screen based on option radio buttons
          else
          {
            this.clickLink(entity, ccy, date, (this.breakdown.selectedValue).toString());
          }

        }
      }
      else
      {
        this.selectIndex=-1;
        this.updateData("no");
      }
    } catch(e) {
      console.log('errorrrr', e)
    }
    // set the selectIndex

  }
  clickLink(sEntityId:string, sCurrencyCode:string, sColumnDate:string, sActionCode:string):void
  {
    // call the clickLink method in jsp and open the specifies screen

    ExternalInterface.call("clickLink", sEntityId, sCurrencyCode, sColumnDate, sActionCode);
  }
  showHideControlBar(event):void
  {
    if (this.controlBarHideFlag)
    {
      this.swtControlBar.visible = true;
      this.imgShowHideControlBar.styleName= 'minusIcon';
      this.imgShowHideControlBar.toolTip= "Hide Button Bar";
      //this.vboxCanvas.height = this.buttonBarHideFlag ?"86%":"80%" ;
    }
    else
    {
      this.swtControlBar.visible= false;
      this.swtControlBar.includeInLayout = false;
      this.imgShowHideControlBar.styleName= 'plusIcon';
      this.imgShowHideControlBar.toolTip= 'Show Button Bar';
      this.vboxCanvas.height = "100%"; // this.buttonBarHideFlag ?"96%" : "91%";
      this.entityMonitorGrid.resizeGrid()
    }
     this.controlBarHideFlag=!this.controlBarHideFlag;
  }
   showHideButtonBar(event):void
  {
    // Checks the status of the Button Bar
    if (this.buttonBarHideFlag)
    {
      this.swtButtonBar.visible = true;
      this.imgShowHideButtonBar.styleName= 'minusIcon';
      this.imgShowHideButtonBar.toolTip= "Hide Button Bar";
      //this.vboxCanvas.height = this.controlBarHideFlag ?"91%" : "81%";
    }
    else
    {
      this.swtButtonBar.visible= false;
      this.swtButtonBar.includeInLayout = false;
      this.imgShowHideButtonBar.styleName= 'plusIcon';
      this.imgShowHideButtonBar.toolTip= 'Show Button Bar';
      this.vboxCanvas.height = "100%"; //this.controlBarHideFlag ?"96%" : "86%";
      this.entityMonitorGrid.resizeGrid()
    }
     this.buttonBarHideFlag=!this.buttonBarHideFlag;
  }

openedCombo(event:Event):void
  {
    this.comboOpen=true;
    //TODO
    /*if (event.currentTarget is SwtComboBox)
    {
      if ((event.type != "HeaderComboOpen"))
      {
        if((event.target as SwtComboBox).isDropDownOpen)
          (event.target as SwtComboBox).dropDown.height = 215;
      }
    }*/

    if (this.inputData.isBusy())
    {
      this.enableInterface();
      this.inputData.cancel();
      //TODO
      /*if (event.currentTarget is SwtComboBox)
      {
        (event.currentTarget as SwtComboBox).interruptComms=true;
      }
    else if (event.currentTarget is SwtDateField)
      {
        (event.currentTarget as SwtDateField).interruptComms=true;
      }*/
    }
  }

  /**
   * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
   **/
closedCombo(event):void
  {
    this.comboOpen=false;
    //TODO
   /* if ((event.triggerEvent != null) && (event.triggerEvent.type == "mouseDownOutside"))
    {
      if (event.currentTarget is SwtComboBox)
      {
        if ((event.currentTarget as SwtComboBox).interruptComms)
        {
          (event.currentTarget as SwtComboBox).interruptComms=false;
          updateData('no');
        }
      }

    }*/
  }


  changeCombo(event:Event):void
  {
    this.comboChange=true;
    this.updateData('no');
  }

  updateData(autorefresh:string):void
  {
    this.requestParams= [];
    //TODO validate date field before update
    /*var date:Date=startDate.selectedDate;

    var dateAsString:string="";
    // variable to hold the expression pattern
    var pattern:RegExp=null;
    if (date)
    {
      // Formatting the date as per the given format
      if (startDate.formatString == "DD/MM/YYYY")
      {
        dateAsString=date ? (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "/" + (date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) + "/" + date.getFullYear() : null;
        pattern=/^(0[1-9]|[12][0-9]|3[01]|[1-9])\/(0[1-9]|1[012]|[1-9])\/(\d{4}|\d{2})$/
      }
      else
      {
        dateAsString=date ? ((date.getMonth() + 1) < 10 ? "0" + (date.getMonth() + 1) : (date.getMonth() + 1)) + "/" + (date.getDate() < 10 ? "0" + (date.getDate()) : date.getDate()) + "/" + date.getFullYear() : null;
        pattern=/^(0[1-9]|1[012]|[1-9])\/(0[1-9]|[12][0-9]|3[01]|[1-9])\/(\d{4}|\d{2})$/
      }
    }*/
    // set the refresh status
    if (autorefresh == "yes")
    {
      this.refreshStatus="Y";
      this.requestParams["currencyCode"]= this.ccyCurrency;
    }
    else
    {
      this.ccyCurrency="";
      this.refreshStatus="N";
    }
    let currGrp:string= this.currGrpCombo.selectedItem.content;
    this.requestParams["entityMonitor.currentDateAsString"]=this.startDate.text;
    this.requestParams["entityMonitor.currGrp"]=currGrp;
    this.requestParams["systemDate"]=this.systemDate;
    this.requestParams["entityMonitor.entityOffsetTime"] = this.chkEntityOffset.selected;
    this.requestParams["autoRefresh"]=autorefresh;
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.send(this.requestParams);
  }
  export(type:string):void {
    /* // create a instance for columnxml
     var columnxml:XML=new XML();
     // Getting the column data value
     var custom:CustomGrid=new CustomGrid(xmlReader.getColumnData());
     // list to hold the column data
     var xmllist:XMLList=new XMLList();
     // Modified by KaisBS for issue 1054_STL_030
     /!* We work with a copy of lastRecievedXML to eliminate the expand of column header after refreshing due the change of heading in XMLList*!/
     xmllist=xmlReader.getColumnData();
     // change the column heading pattern
     columnxml=xmllist[0] as XML;
     for each (var xml:XML in columnxml["group"])
     {
       for each (var headxml:XML in xml["column"])
       {
         headxml.@heading=xml.@heading + "-" + headxml.@heading;
       }
     }
     if (xmllist.children().length() > 1)
       columnxml.replace(xmllist.children().length() - 1, "");
     // Getting the grid data
     custom.dataProvider=xmlReader.getGridData();
     // Getting the total number of rows in the data grid
     custom.rowCount=xmlReader.getRowSize();*/
    var selects = [];
    // Sets the column name
    selects.push("Ccy=" + this.currGrpCombo.selectedLabel);
    selects.push("Date=" + this.startDate.text);
    this.dataExport.convertData(this.lastRecievedJSON.entitymonitor.grid.metadata.columns, this.entityMonitorGrid, this.totalsGrid.gridData, selects, type, true);

  }
  updateTotalGrid(currencyCode:string):void {
    this.requestParams = [];
    if (this.startDate.selectedDate) {

      this.requestParams["entityMonitor.currentDateAsString"] = this.startDate.text;
      let currGrp:string = this.currGrpCombo.selectedLabel;
      this.requestParams["entityMonitor.currGrp"] = currGrp;
      this.requestParams["systemDate"] = this.systemDate;
      this.requestParams["entityMonitor.entityOffsetTime"] = this.chkEntityOffset.selected;
      this.refreshStatus = "Y";
      this.ccyCurrency = currencyCode;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.requestParams["currencyCode"] = currencyCode;
      this.inputData.send(this.requestParams);
    }
  }
 optionClick():void
  {
    this.refreshStatus="N";
    if (this.autoRefresh != null)
    {
      this.autoRefresh.stop();
      this.autoRefresh = null;//TODO

    }
    ExternalInterface.call("openEntityOptionsWindow");

  }
  closeHandler():void
  {
    ExternalInterface.call("close");
  }
  validateStartDate(dateField){
    try{
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      if(dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase() , true);

        if(!date.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
            this.setFocusDateField(dateField)
          });
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          this.setFocusDateField(dateField)
        });
        return false;
      }
      dateField.selectedDate = date.toDate();
    }catch(error){
      console.log('error in validateDateField', error);
    }

    return true;
  }
  setFocusDateField(startDay) {
    startDay.setFocus();
    startDay.text = this.jsonReader.getScreenAttributes()["currentdate"];;
  }
  validateDate() : void {
    if(this.validateStartDate(this.startDate)){
      this.updateDataFromDate();
    } else {

    }
  }
  entityOffsetTime():void{
    if(this.chkEntityOffset.selected)
      this.startDate.enabled = false;
    else
      this.startDate.enabled = true;
    this.updateData('no');
  }

  updateDataFromDate():void {
    this.requestParams = [];
      if (this.startDate.selectedDate) {
        this.requestParams["entityMonitor.currentDateAsString"] = this.startDate.text;
        var currGrp:string = this.currGrpCombo.selectedItem.content;
        this.requestParams["entityMonitor.currGrp"] = currGrp;
        this.requestParams["entityMonitor.entityOffsetTime"] = this.chkEntityOffset.selected;
        this.updateDateFlag = true;
        this.inputData.send(this.requestParams);
      }

  }
  doHelp() {
  ExternalInterface.call("help");
  }

  columnWidthChange(event): void {
    this.requestParams=[];
    this.widthData.encodeURL = false;
    this.actionMethod = 'method=saveColumnWidth';
    this.actionPath = 'entityMonitor.do?';
    this.requestParams['method']= 'saveColumnWidth';
    let gridColumns = this.entityMonitorGrid.gridObj.getColumns();
    for (let i=0; i< gridColumns.length; i++) {
      if(gridColumns[i].id != "dummy" && gridColumns[i].width != gridColumns[i].previousWidth ) {
        let width = gridColumns[i].width;
        if (width< 20)
        {
          width=20;
        }
        if (width> 200)
        {
          width=200;
        }
        this.requestParams['width'] = width ;
        this.widthData.url = this.baseURL + this.actionPath +this.actionMethod;
        this.widthData.send(this.requestParams, null);
        this.totalsGrid.setRefreshColumnWidths(this.entityMonitorGrid.gridObj.getColumns());
        return;
      }

    }


  }

}
const routes: Routes = [
  { path: '', component: EntityMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [EntityMonitor],
  entryComponents: []
})
export class EntityMonitorModule { }
