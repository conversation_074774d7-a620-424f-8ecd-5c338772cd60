<SwtModule (creationComplete)='onLoad()' width="100%" height="265">
  <VBox width='100%' height='100%' paddingLeft="10" paddingRight="10" paddingTop="10" paddingBottom="10" verticalGap="0">
    <SwtCanvas width='100%' height='200'>
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow>
          <GridItem width="40%">
            <SwtLabel textDictionaryId="label.entityMonitorOptions.usePersonalEntityList"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtCheckBox id="cbUsePeList" #cbUsePeList></SwtCheckBox>
              <SwtButton id="btnEntity" #btnEntity width="70" marginTop="0" (click)="openEntity()"></SwtButton>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel textDictionaryId="label.entityMonitorOptions.hideWeekends"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtCheckBox id="cbHideWeekends" #cbHideWeekends ></SwtCheckBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel textDictionaryId="label.entityMonitorOptions.reportingCcy"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtComboBox dataLabel="ccylist" id="reportingCcy" #reportingCcy (change)="changeCombo($event)" width="130"></SwtComboBox>
            <SwtLabel id="lblcurrencyname" #lblcurrencyname  paddingLeft="10" fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel  textDictionaryId="label.entityMonitorOptions.useCurrencyMultiplier"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtComboBox dataLabel="ccymultiplier" id="ccyMultiplier" #ccyMultiplier (change)="changeCombo($event)" width="130"></SwtComboBox>
            <SwtLabel #lblccymultiplier paddingLeft="10" fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel textDictionaryId="label.entityMonitorOptions.usePersonalCcyList"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtCheckBox id="cbPersonalCcyList" #cbPersonalCcyList></SwtCheckBox>
            <SwtButton id="btnCurrency" #btnCurrency width="70" marginTop="0"  (click)="openCurrency()"></SwtButton>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel textDictionaryId="label.entityMonitorOptions.rate"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtNumericInput #textRefreshRate id="textRefreshRate" width="45"  maxChars="3"></SwtNumericInput>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel textDictionaryId="label.entityMonitorOptions.font" ></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtRadioButtonGroup id="fontSize" #fontSize align="horizontal">
            <SwtRadioItem id="fontNormal" #fontNormal value="N" selected="true" groupName="fontSize"></SwtRadioItem>
            <SwtRadioItem id="fontSmall" #fontSmall value="S" groupName="fontSize"> ></SwtRadioItem>
          </SwtRadioButtonGroup>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <SwtCanvas width="100%" >
      <HBox width="100%">
      <HBox width="100%">
        <SwtButton #btnSave id="btnSave" (click)="updateData()" width="70"></SwtButton>
        <SwtButton #btnCancel id="btnCancel" (click)="closeHandler()" width="70"></SwtButton>
      </HBox>
      <HBox horizontalAlign="right">
        <SwtHelpButton (click)="doHelp()"></SwtHelpButton>
      </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
