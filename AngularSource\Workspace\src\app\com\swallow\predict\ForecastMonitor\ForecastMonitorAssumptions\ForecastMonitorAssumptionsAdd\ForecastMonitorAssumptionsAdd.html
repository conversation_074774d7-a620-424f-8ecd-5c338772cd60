<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox  height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas height="80%" width="100%">
    <Grid height="100%" width="100%">
      <GridRow height="20%" width="100%">
        <GridItem width="16%">
          <SwtLabel textDictionaryId="label.forecastMonitor.entity"></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtComboBox id="cbEntity" #cbEntity
                       dataLabel="entity" width="150"></SwtComboBox>
        </GridItem>
        <GridItem>
          <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal"></SwtLabel>
        </GridItem>
      </GridRow>
      <GridRow height="20%" width="100%">
        <GridItem width="16%">
          <SwtLabel textDictionaryId="label.forecastMonitor.currency"></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtComboBox id="cbCurrency" #cbCurrency
                       dataLabel="currency" width="150"></SwtComboBox>
        </GridItem>
        <GridItem>
          <SwtLabel #selectedCurrency fontWeight="normal"></SwtLabel>
        </GridItem>
      </GridRow>
      <GridRow height="20%" width="100%">
        <GridItem width="16%">
          <SwtLabel textDictionaryId="label.forecastAssumptionAdd.date"></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput enabled="false"
                        id="txtDate" #txtDate
                        textAlign="left"
                        width="140"></SwtTextInput>
        </GridItem>
      </GridRow>
      <GridRow height="20%" width="100%">
        <GridItem width="16%">
          <SwtLabel textDictionaryId="label.forecastAssumptionAdd.amount"></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput id="txtAmount" #txtAmount
                        restrict="0-9.,-bBtTmM"
                        (focusOut)="onAmountChange()"
                        textAlign="right"
                        tooltipDictionaryId="tooltip.forecastAssumptionAdd.amount"
                        maxChars="22"
                        width="210"></SwtTextInput>
        </GridItem>
      </GridRow>
      <GridRow height="20%" width="100%">
        <GridItem width="16%">
          <SwtLabel textDictionaryId="label.forecastAssumptionAdd.assumption"></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput id="txtAssumption" #txtAssumption
                        tooltipDictionaryId="tooltip.forecastAssumptionAdd.assumption"
                        width="400"
                        maxChars="100">
          </SwtTextInput>
        </GridItem>
      </GridRow>
    </Grid>
    </SwtCanvas>
  <SwtCanvas width="100%" height="18%">
    <HBox width="100%" height="100%">
    <SwtButton id="btnSave" #btnSave
               (click)="updateData()"></SwtButton>
    <SwtButton id="btnCancel" #btnCancel
               (click)="closeHandler()"></SwtButton>
    </HBox>
    <HBox horizontalAlign="right">
      <SwtHelpButton (click)="help()"></SwtHelpButton>
    </HBox>
  </SwtCanvas>
  </VBox>
</SwtModule>
