<SwtModule (close)='popupClosed()' (creationComplete)='onLoad()' height='100%' width='420'>
  <VBox  width='100%' height='95%' paddingTop="5" paddingRight="10" paddingLeft="10" paddingBottom="5">
    <SwtCanvas width="100%" height="90%" paddingTop="5" paddingRight="5" paddingLeft="10" paddingBottom="5">
      <VBox width="100%" height="100%" verticalGap="0">
      <HBox height="7%" width="100%">
        <SwtLabel #totalPayLabel text="{{totalPay}}"  styleName="underline" textAlign="right" fontSize="12"></SwtLabel>
        <SwtLabel #paymentsToRelease styleName="underline" fontWeight="normal" fontSize="12"></SwtLabel>
      </HBox>
      <HBox height="7%" width="100%" #waitingPay>
        <spacer width="10"></spacer>
        <SwtLabel text="{{totalWaiting}}" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedWaiting  width="250" ></SwtLabel>
        <SwtCheckBox #waitingCheck selected="true" (change)="changeCheckBoxWaiting()"></SwtCheckBox>
      </HBox>
      <HBox height="7%" width="100%" #stoppedPay>
        <spacer width="10"></spacer>
        <SwtLabel text="{{totalStopped}}"  width="30" textAlign="right" color="red"></SwtLabel>
        <SwtLabel #selectedStopped width="250" color="red"></SwtLabel>
        <SwtCheckBox #stoppedCheck selected="true" (change)="changeCheckBoxStopped()"></SwtCheckBox>
      </HBox>
      <HBox height="7%" width="100%" #blockedPay>
        <spacer width="10"></spacer>
        <SwtLabel text="{{totalBlocked}}"  width="30" textAlign="right"></SwtLabel>
        <SwtLabel  #selectedBlocked  width="250" ></SwtLabel>
        <SwtCheckBox #blockedCheck selected="true" (change)="changeCheckBoxBlocked()"></SwtCheckBox>
      </HBox>

      <HBox height="5%" width="100%" #backValuedPay>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalBackValued}}" fontWeight="bold" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedBValued  width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #backValuedCheck selected="true" (change)="changeCheckBoxBackValued()"></SwtCheckBox>
      </HBox>
      <HBox height="5%" width="100%" #inputCutOffPay>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalInputCutOff}}" fontWeight="bold" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedI width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #inputCutOffCheck selected="true" (change)="changeCheckBoxInputCutOff()"></SwtCheckBox>
      </HBox>
      <HBox height="5%" width="100%" #stoppedCutOffPay>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalStoppedCutOff}}" fontWeight="bold" width="30"  textAlign="right"></SwtLabel>
        <SwtLabel #selectedS width="220"  fontWeight="normal"></SwtLabel>
        <SwtCheckBox #stoppedCutOffCheck selected="true" (change)="changeCheckBoxStoppedCutOff()"></SwtCheckBox>
      </HBox>
      <HBox height="5%" width="100%" #cutOffPassedPay>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalCutOffPassed}}" fontWeight="bold" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedC width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #cutOffPassedCheck selected="true" (change)="changeCheckBoxCutOffPassed()"></SwtCheckBox>
      </HBox>
      <!-------BlockedStopped------------->
      <HBox height="7%" width="100%" #blockedStoppedPay>
        <spacer width="10"></spacer>
        <SwtLabel text="{{totalalreadyStoppedBlocked}}"  width="30" textAlign="right" color="red"></SwtLabel>
        <SwtLabel #selectedBS   width="250" color="red" ></SwtLabel>
        <SwtCheckBox #blockedStoppedCheck selected="true" (change)="changeCheckBoxBlockedStopped()"></SwtCheckBox>
      </HBox>

      <HBox height="5%" width="100%" #backValuedPayS>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalBackValuedS}}" fontWeight="bold" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedBStopped  width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #backValuedCheckS selected="true" (change)="changeCheckBoxBackValuedS()"></SwtCheckBox>
      </HBox>
      <HBox height="5%" width="100%" #inputCutOffPayS>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalInputCutOffS}}" fontWeight="bold" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedIStopped  width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #inputCutOffCheckS selected="true" (change)="changeCheckBoxInputCutOffS()"></SwtCheckBox>
      </HBox>
      <HBox height="5%" width="100%" #stoppedCutOffPayS>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalStoppedCutOffS}}" fontWeight="bold" width="30"  textAlign="right"></SwtLabel>
        <SwtLabel #selectedSStopped  width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #stoppedCutOffCheckS selected="true" (change)="changeCheckBoxStoppedCutOffS()"></SwtCheckBox>
      </HBox>
      <HBox height="5%" width="100%" #cutOffPassedPayS>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalCutOffPassedS}}" fontWeight="bold" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedCStopped  width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #cutOffPassedCheckS selected="true" (change)="changeCheckBoxCutOffPassedS()"></SwtCheckBox>
      </HBox>

      <!-------BlockedPreviouslyStopped------------->

      <HBox height="7%" width="100%" #blockedPrevStoppedPay>
        <spacer width="10"></spacer>
        <SwtLabel text="{{totalBlockedPrevS}}"  width="30" textAlign="right" color="red"></SwtLabel>
        <SwtLabel #selectedBlockedPrevS   width="250" color="red" ></SwtLabel>
        <SwtCheckBox #blockedPrevStoppedCheck selected="true" (change)="changeCheckBoxBlockedPrevStopped()"></SwtCheckBox>
      </HBox>

      <HBox height="5%" width="100%" #backValuedPayPrevS>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalBackValuedPrevS}}" fontWeight="bold" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedBPrevStopped  width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #backValuedCheckPrevS selected="true" (change)="changeCheckBoxBackValuedPrevS()"></SwtCheckBox>
      </HBox>
      <HBox height="5%" width="100%" #inputCutOffPayPrevS>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalInputCutOffPrevS}}" fontWeight="bold" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedIPrevStopped  width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #inputCutOffCheckPrevS selected="true" (change)="changeCheckBoxInputCutOffPrevS()"></SwtCheckBox>
      </HBox>
      <HBox height="5%" width="100%" #stoppedCutOffPayPrevS>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalStoppedCutOffPrevS}}" fontWeight="bold" width="30"  textAlign="right"></SwtLabel>
        <SwtLabel #selectedPrevStopped  width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #stoppedCutOffCheckPrevS selected="true" (change)="changeCheckBoxStoppedCutOffPrevS()"></SwtCheckBox>
      </HBox>
      <HBox height="5%" width="100%" #cutOffPassedPayPrevS>
        <spacer width="25"></spacer>
        <SwtLabel text="{{totalCutOffPassedPrevS}}" fontWeight="bold" width="30" textAlign="right"></SwtLabel>
        <SwtLabel #selectedCPrevStopped  width="220" fontWeight="normal"></SwtLabel>
        <SwtCheckBox #cutOffPassedCheckPrevS selected="true" (change)="changeCheckBoxCutOffPassedPrevS()"></SwtCheckBox>
      </HBox>

      <!------Released and canncelled--------------->
      <HBox height="7%" width="100%" #cancelledPay>
        <spacer width="10"></spacer>
        <SwtLabel text="{{totalCancel}}"  width="30" color="red" textAlign="right"></SwtLabel>
        <SwtLabel #selectedCancelled  width="200" color="red" fontWeight="normal" ></SwtLabel>

      </HBox>

      <HBox height="7%" width="100%" #releasedPay>
        <spacer width="10"></spacer>
        <SwtLabel text="{{totalReleased}}"  width="30" color="red" textAlign="right"></SwtLabel>
        <SwtLabel #selectedReleased  width="200" color="red"  fontWeight="normal"></SwtLabel>
      </HBox>
      </VBox>
    </SwtCanvas>
    <SwtCanvas id="canvasContainer"
               width="100%">
      <HBox>
        <SwtButton #releaseButton
                   (click)="checkBeforeRelease($event)"
                   label="Release"
                   width="70" [buttonMode]="true" enabled="true"></SwtButton>
        <SwtButton buttonMode="true"
                   id="cancelButton"
                   width="70"
                   label="Cancel"
                   #cancelButton
                   (click)="popupClosed();"></SwtButton>
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
