
import {Component, Inject, OnInit, ViewChild, ModuleWithProviders, NgModule, ElementRef} from '@angular/core';
import {
  SwtCanvas,
  SwtCommonGrid,
  CommonService,
  HTTPComms,
  SwtUtil,
  JSONReader,
  SwtLoadingImage,
  SwtTextInput,
  SwtLabel,
  SwtModule,
  SwtButton,
  SwtAlert,
  ExternalInterface,
  SwtToolBoxModule, Alert, SwtPopUpManager
} from "swt-tool-box";
import {RouterModule, Routes} from "@angular/router";
import {CorporateAccountDetails} from '../CorporateAccountDetails/CorporateAccountDetails';

@Component({
  selector: 'app-corporate-account',
  templateUrl: './CorporateAccount.html',
  styleUrls: ['./CorporateAccount.css']
})
export class CorporateAccount extends SwtModule implements OnInit {
  private swtAlert: SwtAlert;
  @ViewChild('lblEntity') lblEntity: SwtLabel;
  @ViewChild('entityId') entityId: SwtTextInput;
  @ViewChild('entityName') entityName: SwtLabel;
  @ViewChild('valueDate') valueDate: SwtTextInput;
  @ViewChild('lblDate') lblDate: SwtLabel;
  @ViewChild('dataBuildingText') dataBuildingText: SwtLabel;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  @ViewChild('corpAccContainer') corpAccContainer: SwtCanvas;
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  private cGrid: SwtCommonGrid;
  private  win: any;

  
  private  jsonReader : JSONReader = new JSONReader();
  /* - START -- Screen Name and Version Number ---- */
  private  screenName :string =ExternalInterface.call('getBundle', 'text', 'label-corporateEntries', 'Corporate Entries');
  private  versionNumber :string = "1.1.0007";
  /* - END -- Screen Name and Version Number ---- */
  private  baseURLMETHOD :string = "";
  private  corpAccName : string = "";

  private  baseURL:string= SwtUtil.getBaseURL();
  private  actionPath:string="";
  private  actionMethod:string="";
  private  requestParams =  [];
  private  lastReceivedJSON;
  private  prevReceivedJSON;
  private  cropNameText : SwtTextInput;
  private  amountText : SwtTextInput;
  private  saveFlag :Boolean = true;
  private  menuAccessIdParent: number = 0;
  //iable added to hold sequnce number for Mantis 1256 by Sami on 28-Sep-2010
  private  corpSeqNo : string = "";
  private dateFormat: string;
  private testDate: string;
  /**
   * Communication Objects
   **/
  private  inputData =new HTTPComms(this.commonService);

  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
  }
  onLoad() {
    this.cGrid = <SwtCommonGrid> this.corpAccContainer.addChild(SwtCommonGrid);
    this.cGrid.onFilterChanged = this.disableButtons.bind(this)
    this.cGrid.onSortChanged = this.disableButtons.bind(this)
    this.dateFormat = ExternalInterface.call('eval', 'dateFormat');
    this.testDate=ExternalInterface.call('eval', 'testDate');
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    //result event
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    }
    //fault event
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    this.menuAccessIdParent = ExternalInterface.call('eval','menuAccessIdParent');
    if(this.menuAccessIdParent == 1){
      this.addButton.enabled = false;
      this.changeButton.enabled = false;
      this.deleteButton.enabled = false;
    }
    //action url
    this.actionPath="corporateAccount.do?";
    //Then declare the action method:
    this.actionMethod="method=display";
    this.actionMethod = this.actionMethod + "&selectedDate=" + ExternalInterface.call('eval','selectedDate')+
      "&selectedEntityId="+ ExternalInterface.call('eval','selectedEntityId')+
      "&selectedEntityName="+ ExternalInterface.call('eval','selectedEntityName');
    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    //Make initial request
    this.inputData.send(this.requestParams);
    this.cGrid.onRowClick = (event) =>{
      this.obtainCell(event);
    }
  }
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  inputDataFault(event): void {
    this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);

  }
 inputDataResult (event) :void {
    //get the received xml
    this.lastReceivedJSON= event;

    this.jsonReader.setInputJSON(this.lastReceivedJSON);
    this.lostConnectionText.visible=false;
    if (this.jsonReader.getRequestReplyStatus())
    {
      if ((this.lastReceivedJSON != this.prevReceivedJSON))
      {
        this.valueDate.text = this.jsonReader.getScreenAttributes()["valueDate"];
        this.entityId.text = this.jsonReader.getScreenAttributes()["selectedEntityId"];
        this.entityName.text = this.jsonReader.getScreenAttributes()["selectedEntityName"];
        /*Condition to check this.jsonReader databuilding*/
        if ( !this.jsonReader.isDataBuilding() ) {
          this.dataBuildingText.visible = false;
          const obj = {columns: this.jsonReader.getColumnData()};
          this.cGrid.CustomGrid(obj);
          this.cGrid.gridData = this.jsonReader.getGridData();
          /* Gets the grid's row length */
          this.cGrid.setRowSize = this.jsonReader.getRowSize();
        } else {
          this.dataBuildingText.visible = true;
        }
      }
    }
  }

  /**This function is used to load the retrieved datas from xml using the this.jsonReader and process it
   * The Column datas are retieved  and are located into the display
   * containes.
   * */
  retrieveAfterSaveInputDataResult (event) :void {
    this.lastReceivedJSON=event;
    this.jsonReader.setInputJSON(this.lastReceivedJSON);
    this.lostConnectionText.visible=false;
    if (this.jsonReader.getRequestReplyStatus())
    {
      if ((this.lastReceivedJSON != this.prevReceivedJSON))
      {
        this.valueDate.text = this.jsonReader.getScreenAttributes()["valueDate"];
        /*Condition to check this.jsonReader databuilding*/
        if ( !this.jsonReader.isDataBuilding() ) {
          this.dataBuildingText.visible = false;
          /* Gets the grid's data */
          this.cGrid.gridData = this.jsonReader.getGridData();
          /* Gets the grid's row length */
          this.cGrid.setRowSize = this.jsonReader.getRowSize();
          this.cGrid.selectedIndex = -1;
          this.changeButton.enabled = false;
          this.deleteButton.enabled = false;

        } else {
          this.dataBuildingText.visible = true;
        }
      }
      //PopUpManager.removePopUp(titleWindow);
    }else{
      if(this.jsonReader.getRequestReplyMessage() == "errors.DataIntegrityViolationExceptioninAdd"){
        this.swtAlert.warning(ExternalInterface.call('getBundle', 'text', 'label-recordExists', 'Record already exists'),ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
      }
    }

  }

  /**
   *Fault event handler for inputData HTTPComms*/
  retrieveAfterSaveInputDataFault(event ) :void {
    this.lostConnectionText.visible = true;

  }
  /**
   * Mouse event to handle check the availability of connection */
  addCropName(event :Event) :void {
    this.win =  SwtPopUpManager.createPopUp(this, CorporateAccountDetails, {
      title: ExternalInterface.call('getBundle', 'text', 'label-addCorporateEntries', 'Add Corporate Entries')

    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '500';
    this.win.height = '170';
    this.win.showControls = true;
    this.win.display();
    this.saveFlag = true;
    //this.openCropAccDetails(ExternalInterface.call('getBundle', 'text', 'label-addCorporateEntries', 'Add Corporate Entries'),"",true);
  }
  updateCropName(event) {
    this.win =  SwtPopUpManager.createPopUp(this, CorporateAccountDetails, {
      title: ExternalInterface.call('getBundle', 'text', 'label-changeCorporateEntries', 'Change Corporate Entries'),
      corpName: this.cGrid.selectedItem.name.content,
      amount: this.cGrid.selectedItem.amount.content

    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '500';
    this.win.height = '190';
    this.win.showControls = true;
    this.win.display();
    this.saveFlag = false;
    this.corpSeqNo = this.cGrid.selectedItem.name.id;
  }
  closeHandler(event) :void {
    ExternalInterface.call("CallBackApp");
    ExternalInterface.call("close");
  }
  /**
   *Mouse event to handle check the availability of connection */
 deleteCropName(event :Event) :void {
    this.swtAlert.question(ExternalInterface.call('getBundle', 'text', 'label-wantToDelete', 'Are you sure you want to delete'),ExternalInterface.call('getBundle', 'text', 'alert-delete', 'Delete'),Alert.OK | Alert.CANCEL,null, this.alertListener.bind(this) );
  }
alertListener(eventObj):void {
    var allowMatch : boolean = false;
    if (eventObj.detail==Alert.OK) {
      if(this.cGrid.selectedIndex >=0){
        //Sets the request params
        this.requestParams = [];
        this.inputData.cbStart= this.startOfComms.bind(this);
        this.inputData.cbStop= this.endOfComms.bind(this);
        //result event
        this.inputData.cbResult= (event) => {
          this.retrieveAfterSaveInputDataResult(event);
        };
        this.inputData.cbFault= this.retrieveAfterSaveInputDataFault.bind(this);
        this.inputData.encodeURL=false;
        //action url
        this.actionPath="corporateAccount.do?";
        //Then declare the action method:
        this.actionMethod="method=deleteCorporateAccountDetails";
        //code added by Sami for Mantis 1256 on 28-Sep-2010 -sequence number added in request parameter
        this.requestParams["selectedDate"]= this.valueDate.text;
        this.requestParams["corpName"]= this.cGrid.selectedItem.name.content;
        this.requestParams["amount"]= this.cGrid.selectedItem.amount.content;
        this.requestParams["selectedEntityId"]= this.entityId.text;
        this.requestParams["selectedEntityName"]= this.entityName.text;
        this.requestParams["corporateSeqNo"]= this.cGrid.selectedItem.name.id;
        //Then apply them to the url member of the HTTPComms object:
        this.inputData.url= this.baseURL +  this.actionPath + this.actionMethod;
        //Make initial request
        this.inputData.send(this.requestParams);
      }
    }
  }
  saveParent(corp: string, amount: string) {
    this.requestParams = [];
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    //result event
    this.inputData.cbResult= (event) => {
      this.retrieveAfterSaveInputDataResult(event);
    }
    //fault event
    this.inputData.cbFault= this.retrieveAfterSaveInputDataFault.bind(this);
    this.inputData.encodeURL=false;
    this.actionPath="corporateAccount.do?";
    this.actionMethod="method=saveCorporateAccountDetails";
    this.requestParams["selectedDate"]=this.valueDate.text;
    this.requestParams["corpName"]= corp;
    this.requestParams["amount"]= amount;
    this.requestParams["selectedEntityId"]= this.entityId.text;
    this.requestParams["selectedEntityName"]= this.entityName.text;
    this.requestParams["editFlag"]=this.saveFlag;
    if(!this.saveFlag)
      this.requestParams["corporateSeqNo"]= this.corpSeqNo;
    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    //Make initial request
    this.inputData.send(this.requestParams);
  }
   obtainCell(e) :void {
    /*/!*Retrieves the column Array of the cell clicked*!/
    var columnArray = e.target.columns;
    /!*Retrieves the key column from which the column data is to be fetched*!/
    var corpAccColumn : String = columnArray[0].dataField;
    /!*Retrieve the column value used to forward the request to GROUP MONITOR or BOOK MONITOR*!/
    corpAccName = e.target.dataProvider[e.rowIndex][corpAccColumn];*/
    if(this.cGrid.selectedIndex >= 0){
      this.changeButton.enabled = true;
      this.deleteButton.enabled = true;
    }else{
      this.changeButton.enabled = false;
      this.deleteButton.enabled = false;
    }
  }
  disableButtons() {
   if(this.cGrid.selectedIndex == -1) {
     this.changeButton.enabled = false;
     this.deleteButton.enabled = false;
   }
  }
}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: CorporateAccount }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CorporateAccount],
  entryComponents: []
})
export class CorporateAccountModule {}
