<SwtModule  #swtModule  (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtFieldSet id="fieldSet1" #fieldSet1 style="height: 21%; width: 100%; color:blue;">
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow width="100%" height="25%">
          <VBox width="91%" height="100%">
            <GridRow width="100%" height="22">
              <GridItem width="150">
                <SwtLabel id="dateSource" #dateSource></SwtLabel>
              </GridItem>
              <GridItem width="140">
                <SwtComboBox id="dateSourceCombo" #dateSourceCombo width="120" (change)="updateDataTypeConfig()"
                dataLabel="dataSourcesList"> </SwtComboBox>
              </GridItem>
              <GridItem width="40">
                <input type="file" style="display: none" #file (change)="readUploadedFile($event)" (click)="onInputClick($event)" 
                accept="{{this.dateSourceCombo.selectedLabel=='Excel file'? '.xlsx' : '.csv'}}"/>
                <SwtImage id="uploadImage1" #uploadImage width="23" (click)="file.click()" styleName="imageStyle"></SwtImage>
              </GridItem>
              <GridItem width="45%">
                <SwtLabel id="fileName" #fileName fontWeight="normal"></SwtLabel>
              </GridItem>
            </GridRow>
            <GridRow width="100%" height="22">
      
              <GridItem width="150">
                <SwtLabel id="headerLabel" #headerLabel></SwtLabel>
              </GridItem>
              <GridItem width="88%">
                <SwtCheckBox id="chkHeader" #chkHeader selected="true" (change)="changeGridDragStatus()"></SwtCheckBox>
              </GridItem>
            </GridRow>
          </VBox>
          <VBox width="9%" height="100%" paddingTop="15">
            <SwtButton [buttonMode]="true" id="resetButton" #resetButton (click)="resetConfig()">
            </SwtButton>
          </VBox>

        </GridRow>
      
        <GridRow width="100%" height="75%" paddingTop="10">
          <VBox width="150" height="100%" paddingTop="22">
            <GridItem width="100%" height="22">
              <SwtLabel id="headerValue" #headerValue fontWeight="normal"></SwtLabel>
            </GridItem>
            <GridItem width="100%" height="25">
              <SwtLabel id="headerType" #headerType fontWeight="normal"></SwtLabel>
            </GridItem>
            <GridItem width="100%" height="25">
              <SwtLabel id="sourceFormat" #sourceFormat fontWeight="normal"></SwtLabel>
            </GridItem>
          </VBox>
          <VBox width="100%" height="100%">
            <SwtCanvas #headersGridContainer id="headersGridContainer" styleName="canvasWithGreyBorder" width="100%"
              height="65" border="false"></SwtCanvas>
            <SwtCanvas #sourceFormatGridContainer id="sourceFormatGridContainer" styleName="canvasWithGreyBorder" width="100%"
              height="36" border="false"></SwtCanvas>
          </VBox>
        </GridRow>
      
      
      </Grid>


    </SwtFieldSet>


    <SwtFieldSet id="fieldSet2" #fieldSet2 style="padding-bottom: 5px; height: 78%; width: 100%;color:blue;">
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow width="100%"  height= "25" paddingTop="2">
          <GridItem width="70%">
            <GridItem width="150">
              <SwtLabel id="importStatusLbl" #importStatusLbl></SwtLabel>
            </GridItem>
            <GridItem width="83%">
              <SwtLabel id="importStatusTxt" #importStatusTxt fontWeight="normal"> </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="30%">

            <GridItem paddingRight="15">
              <GridItem width="10%">
                <SwtLabel id="showLabel" #showLabel></SwtLabel>
              </GridItem>

            </GridItem>
            <GridItem width="90%">
              <SwtRadioButtonGroup #showOptions id="showOptions" (change) ="filterRows()" align="horizontal" width="100%">
                <SwtRadioItem value="A" width="100" groupName="showOptions" selected="true" id="allRows" #allRows>
                </SwtRadioItem>
                <SwtRadioItem value="V" width="100" groupName="showOptions" id="validRows" #validRows></SwtRadioItem>
                <SwtRadioItem value="I" width="100" groupName="showOptions" id="invalidRows" #invalidRows></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
          </GridItem>

        </GridRow>
        <GridRow width="100%" height="100%">
          <SwtCanvas #mvtGridContainer id="mvtGridContainer" (paste)="checkGrid($event)" styleName="canvasWithGreyBorder" width="100%" height="100%"
            border="false"></SwtCanvas>
        </GridRow>

      </Grid>


    </SwtFieldSet>


    <SwtCanvas width="100%" height="35">
      <HBox width="100%" top="1">
        <HBox paddingLeft="5" width="80%">
          <SwtButton [buttonMode]="true" id="loadButton" #loadButton (click)="checkGrid($event)">
          </SwtButton>
        </HBox>
        <HBox width="20%" horizontalAlign="right" paddingRight="10">
          <SwtButton [buttonMode]="true" id="saveButton" #saveButton enabled="false" (click)="checkBeforeSaving()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>

      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>