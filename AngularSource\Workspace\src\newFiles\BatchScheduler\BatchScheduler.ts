import {
  Component,
  OnInit,
  ElementRef,
  ModuleWithProviders,
  NgModule,
  ViewChild,
} from "@angular/core";
import {
  SwtModule,
  CommonService,
  SwtAlert,
  SwtToolBoxModule,
  SwtLabel,
  SwtComboBox,
  SwtCanvas,
  SwtButton,
  SwtLoadingImage,
  JSONReader,
  HTTPComms,
  SwtUtil,
  SwtCommonGrid,
  SwtGroupedCommonGrid,
  ExternalInterface,
  Alert,
  StringUtils,
  HBox,
} from "swt-tool-box";
import { Routes, RouterModule } from "@angular/router";

declare var instanceElement: any;

@Component({
  selector: "app-batch-scheduler",
  templateUrl: "./BatchScheduler.html",
  styleUrls: ["./BatchScheduler.css"],
})
export class BatchScheduler extends SwtModule implements OnInit {
  @ViewChild("scheduledJobTypeLabel") scheduledJobTypeLabel: SwtLabel;
  @ViewChild("selectedJobType") selectedJobType: SwtLabel;
  @ViewChild("lastRefTimeLabel") lastRefTimeLabel: SwtLabel;
  @ViewChild("lastRefTime") lastRefTime: SwtLabel;

  @ViewChild("jobTypeList") jobTypeList: SwtComboBox;

  @ViewChild("dataGridContainer") dataGridContainer: SwtCanvas;

  @ViewChild("refreshButton") refreshButton: SwtButton;
  @ViewChild("executeButton") executeButton: SwtButton;
  @ViewChild("enableButton") enableButton: SwtButton;
  @ViewChild("disableButton") disableButton: SwtButton;
  @ViewChild("addButton") addButton: SwtButton;
  @ViewChild("changeButton") changeButton: SwtButton;
  @ViewChild("removeButton") removeButton: SwtButton;
  @ViewChild("entityButton") entityButton: SwtButton;
  @ViewChild("closeButton") closeButton: SwtButton;
  @ViewChild("printButton") printButton: SwtButton;

  @ViewChild("loadingImage") loadingImage: SwtLoadingImage;

  private actionPath: string;
  private actionMethod: string = "";
  private requestParams: any;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON: any;
  private prevRecievedJSON: any;
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private jobGrid: SwtCommonGrid;

  private lastRefTimeValue: string;
  private menuAccessId: any;
  private scheduledJobType: string;
  private selectedScheduledJobType: string;
  private selectedJobId: string = "";
  private selectedScheduleId: string = "";
  private selectedJobTypeValue: string = "";
  private filterStatus: string;
  private sortStatus: string;
  private sortDescending: string;
  private menuEntityCurrGrpAccess: string;
  private gridScrollTop: string;

  public showJobTypeDropdown: boolean = false;
  public showEntityButton: boolean = false;
  public moduleId = "BatchScheduler";

  constructor(
    private commonService: CommonService,
    private element: ElementRef,
  ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.jobGrid = <SwtCommonGrid>(
      this.dataGridContainer.addChild(SwtCommonGrid)
    );
    this.jobGrid.allowMultipleSelection = false;

    this.scheduledJobTypeLabel.text = SwtUtil.getPredictMessage(
      "batchScheduler.scheduledJobType",
      null,
    );
    this.refreshButton.label = SwtUtil.getPredictMessage(
      "button.Refresh",
      null,
    );
    this.executeButton.label = SwtUtil.getPredictMessage(
      "button.execute",
      null,
    );
    this.enableButton.label = SwtUtil.getPredictMessage("button.enable", null);
    this.disableButton.label = SwtUtil.getPredictMessage(
      "button.disable",
      null,
    );
    this.addButton.label = SwtUtil.getPredictMessage("button.add", null);
    this.changeButton.label = SwtUtil.getPredictMessage("button.change", null);
    this.removeButton.label = SwtUtil.getPredictMessage("button.remove", null);
    this.entityButton.label = SwtUtil.getPredictMessage(
      "button.entityProcess",
      null,
    );
    this.closeButton.label = SwtUtil.getPredictMessage("button.close", null);
    this.lastRefTimeLabel.text = SwtUtil.getPredictMessage(
      "label.lastRefTime2",
      null,
    );

    this.refreshButton.toolTip = SwtUtil.getPredictMessage(
      "tooltip.refreshJobDetail",
      null,
    );
    this.executeButton.toolTip = SwtUtil.getPredictMessage(
      "tooltip.exeJob",
      null,
    );
    this.enableButton.toolTip = SwtUtil.getPredictMessage(
      "tooltip.enable",
      null,
    );
    this.disableButton.toolTip = SwtUtil.getPredictMessage(
      "tooltip.disable",
      null,
    );
    this.addButton.toolTip = SwtUtil.getPredictMessage("tooltip.addJob", null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage(
      "tooltip.changeJob",
      null,
    );
    this.removeButton.toolTip = SwtUtil.getPredictMessage(
      "tooltip.removeJob",
      null,
    );
    this.entityButton.toolTip = SwtUtil.getPredictMessage(
      "tooltip.entityProcess",
      null,
    );
    this.closeButton.toolTip = SwtUtil.getPredictMessage("tooltip.close", null);
  }

  onLoad() {
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
      this.requestParams = [];
      this.menuAccessId = ExternalInterface.call("eval", "menuAccessId");
      this.scheduledJobType = ExternalInterface.call(
        "eval",
        "scheduledJobType",
      );
      this.selectedScheduledJobType = ExternalInterface.call(
        "eval",
        "selectedScheduledJobType",
      );
      this.filterStatus = ExternalInterface.call("eval", "filterStatus");
      this.sortStatus = ExternalInterface.call("eval", "sortStatus");
      this.sortDescending = ExternalInterface.call("eval", "sortDescending");
      this.gridScrollTop = ExternalInterface.call("eval", "gridScrollTop");

      errorLocation = 20;
      this.menuEntityCurrGrpAccess = "0"; //ExternalInterface.call('eval', 'menuEntityCurrGrpAccess');

      if (this.menuAccessId) {
        errorLocation = 30;
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }

      // Set job type dropdown visibility based on scheduledJobType
      if (this.scheduledJobType === "B") {
        this.showJobTypeDropdown = true;
      } else {
        this.showJobTypeDropdown = false;
        if (this.scheduledJobType === "R") {
          this.selectedJobType.text = "Report";
        } else if (this.scheduledJobType === "P") {
          this.selectedJobType.text = "Process";
          this.showEntityButton = true;
        }
      }

      errorLocation = 40;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;

      errorLocation = 50;
      this.actionPath = "scheduler.do?";
      this.actionMethod = "method=displayAngular";
      this.requestParams["menuAccessId"] = this.menuAccessId;
      this.requestParams["scheduledJobType"] = this.scheduledJobType;
      this.requestParams["selectedScheduledJobType"] =
        this.selectedScheduledJobType;
      this.requestParams["selectedFilterStatus"] = this.filterStatus;
      this.requestParams["selectedSortStatus"] = this.sortStatus;
      this.requestParams["selectedSortDescending"] = this.sortDescending;
      this.requestParams["gridScrollTop"] = this.gridScrollTop;

      errorLocation = 60;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      errorLocation = 70;
      this.jobGrid.onRowClick = (event) => {
        this.onSelectTableRow(event);
      };

      errorLocation = 80;
      this.jobGrid.onFilterChanged = this.maintainSortFilterStatus.bind(this);
      this.jobGrid.onSortChanged = this.maintainSortFilterStatus.bind(this);

      // Set refresh interval
      setTimeout(() => this.refreshStatus(), 10000);
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "onLoad",
        errorLocation,
      );
    }
  }

  inputDataResult(event): void {
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
      if (this.inputData.isBusy()) {
        errorLocation = 20;
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        errorLocation = 30;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        errorLocation = 40;

        if (this.jsonReader.getRequestReplyStatus()) {
          if (this.lastRecievedJSON != this.prevRecievedJSON) {
            errorLocation = 50;
            // Enable refresh button
            this.refreshButton.enabled = true;
            this.refreshButton.buttonMode = true;

            // Clear selection
            console.log("Clear selection");
            console.log(this.jobGrid.selectedIndex);
            console.log(this.jobGrid.selectedItem);
            this.jobGrid.selectedIndex = -1;

            errorLocation = 60;
            if (this.showJobTypeDropdown) {
              this.jobTypeList.setComboData(this.jsonReader.getSelects());
              this.jobTypeList.selectedValue = this.selectedScheduledJobType;
            }

            errorLocation = 70;
            // Update last refresh time
            this.lastRefTime.text = this.jsonReader.getSingletons().lastRefTime;

            errorLocation = 80;
            // Initialize the grid
            const obj = {
              columns:
                this.lastRecievedJSON.BatchScheduler.jobGrid.metadata.columns,
            };
            this.jobGrid.CustomGrid(obj);

            errorLocation = 90;
            var gridRows = this.lastRecievedJSON.BatchScheduler.jobGrid.rows;
            if (gridRows && gridRows.size > 0) {
              this.jobGrid.gridData = gridRows;
              this.jobGrid.setRowSize = this.jsonReader.getRowSize();
            } else {
              this.jobGrid.gridData = { size: 0, row: [] };
            }

            errorLocation = 100;
            // Update menu access levels
            this.updateButtonStates();
            console.log("Refresh grid");
            this.jobGrid.refresh();
              console.log("+-------------- selection");
            console.log(this.jobGrid.selectedIndex);
            console.log(this.jobGrid.selectedItem);
            this.prevRecievedJSON = this.lastRecievedJSON;
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(
              this.jsonReader.getRequestReplyMessage() +
                "\n" +
                this.jsonReader.getRequestReplyLocation(),
              "Error",
            );
          }
        }
      }
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "inputDataResult",
        errorLocation,
      );
    }
  }

  updateData(event): void {
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
      this.requestParams = [];
      this.menuAccessId = ExternalInterface.call("eval", "menuAccessId");

      if (this.menuAccessId) {
        errorLocation = 20;
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }

      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;

      errorLocation = 30;
      this.actionPath = "scheduler.do?";
      this.actionMethod = "method=displayAngular";
      this.requestParams["menuAccessId"] = this.menuAccessId;
      this.requestParams["scheduledJobType"] = this.scheduledJobType;

      if (this.showJobTypeDropdown) {
        this.selectedScheduledJobType = this.jobTypeList.selectedValue;
        this.requestParams["selectedScheduledJobType"] =
          this.selectedScheduledJobType;
      }

      this.maintainSortFilterStatus();
      this.requestParams["selectedFilterStatus"] = this.filterStatus;
      this.requestParams["selectedSortStatus"] = this.sortStatus;
      this.requestParams["selectedSortDescending"] = this.sortDescending;
      this.requestParams["gridScrollTop"] =
        document.getElementById("dataGridContainer").scrollTop;
      this.requestParams["selectedjobId"] = this.selectedJobId;
      this.requestParams["selectedScheduleId"] = this.selectedScheduleId;
      this.requestParams["selectedJobType"] = this.selectedJobTypeValue;

      errorLocation = 40;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      errorLocation = 50;
      // Disable refresh button
      this.refreshButton.enabled = false;
      this.refreshButton.buttonMode = false;
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "updateData",
        errorLocation,
      );
    }
  }

  maintainSortFilterStatus(): void {
    try {
      // Since server-side filter and sort are not needed, we'll just use defaults
      this.sortStatus = "0";
      this.sortDescending = "false";
      this.filterStatus = "";
    } catch (error) {
      console.log(error);
    }
  }

  submitExecute(event): void {
    try {
      this.swtAlert.confirm(
        SwtUtil.getPredictMessage("batchScheduler.confirm.execute", null),
        SwtUtil.getPredictMessage("alert_header.confirm", null),
        Alert.YES | Alert.NO,
        null,
        this.doExecute.bind(this),
      );
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "submitExecute",
        0,
      );
    }
  }

  doExecute(): void {
    try {
      this.requestParams = [];
      this.maintainSortFilterStatus();
      this.requestParams["menuAccessId"] = this.menuAccessId;
      this.requestParams["scheduledJobType"] = this.scheduledJobType;

      if (this.showJobTypeDropdown) {
        this.selectedScheduledJobType = this.jobTypeList.selectedValue;
        this.requestParams["selectedScheduledJobType"] =
          this.selectedScheduledJobType;
      }

      this.requestParams["selectedFilterStatus"] = this.filterStatus;
      this.requestParams["selectedSortStatus"] = this.sortStatus;
      this.requestParams["selectedSortDescending"] = this.sortDescending;
      this.requestParams["gridScrollTop"] =
        document.getElementById("dataGridContainer").scrollTop;
      this.requestParams["selectedjobId"] = this.selectedJobId;
      this.requestParams["selectedScheduleId"] = this.selectedScheduleId;
      this.requestParams["selectedJobType"] = this.selectedJobTypeValue;

      this.actionPath = "scheduler.do?";
      this.actionMethod = "method=exec";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      this.refreshButton.enabled = false;
      this.refreshButton.buttonMode = false;
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "doExecute",
        0,
      );
    }
  }

  jobEnable(event): void {
    try {
      this.swtAlert.confirm(
        SwtUtil.getPredictMessage("batchScheduler.confirm.enable", null),
        SwtUtil.getPredictMessage("alert_header.confirm", null),
        Alert.YES | Alert.NO,
        null,
        this.doJobEnable.bind(this),
      );
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "jobEnable",
        0,
      );
    }
  }

  doJobEnable(): void {
    try {
      this.requestParams = [];
      this.maintainSortFilterStatus();
      this.requestParams["menuAccessId"] = this.menuAccessId;
      this.requestParams["scheduledJobType"] = this.scheduledJobType;

      if (this.showJobTypeDropdown) {
        this.selectedScheduledJobType = this.jobTypeList.selectedValue;
        this.requestParams["selectedScheduledJobType"] =
          this.selectedScheduledJobType;
      }

      this.requestParams["enableOrDisable"] = "Enable";
      this.requestParams["method"] = "enableOrDisableJob";
      this.requestParams["selectedFilterStatus"] = this.filterStatus;
      this.requestParams["selectedSortStatus"] = this.sortStatus;
      this.requestParams["selectedSortDescending"] = this.sortDescending;
      this.requestParams["gridScrollTop"] =
        document.getElementById("dataGridContainer").scrollTop;
      this.requestParams["selectedjobId"] = this.selectedJobId;
      this.requestParams["selectedScheduleId"] = this.selectedScheduleId;
      this.requestParams["selectedJobType"] = this.selectedJobTypeValue;

      this.actionPath = "scheduler.do?";
      this.actionMethod = "method=enableOrDisableJob";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      this.refreshButton.enabled = false;
      this.refreshButton.buttonMode = false;
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "doJobEnable",
        0,
      );
    }
  }

  jobDisable(event): void {
    try {
      this.swtAlert.confirm(
        SwtUtil.getPredictMessage("batchScheduler.confirm.disable", null),
        SwtUtil.getPredictMessage("alert_header.confirm", null),
        Alert.YES | Alert.NO,
        null,
        this.doJobDisable.bind(this),
      );
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "jobDisable",
        0,
      );
    }
  }

  doJobDisable(): void {
    try {
      this.requestParams = [];
      this.maintainSortFilterStatus();
      this.requestParams["menuAccessId"] = this.menuAccessId;
      this.requestParams["scheduledJobType"] = this.scheduledJobType;

      if (this.showJobTypeDropdown) {
        this.selectedScheduledJobType = this.jobTypeList.selectedValue;
        this.requestParams["selectedScheduledJobType"] =
          this.selectedScheduledJobType;
      }

      this.requestParams["enableOrDisable"] = "Disable";
      this.requestParams["method"] = "enableOrDisableJob";
      this.requestParams["selectedFilterStatus"] = this.filterStatus;
      this.requestParams["selectedSortStatus"] = this.sortStatus;
      this.requestParams["selectedSortDescending"] = this.sortDescending;
      this.requestParams["gridScrollTop"] =
        document.getElementById("dataGridContainer").scrollTop;
      this.requestParams["selectedjobId"] = this.selectedJobId;
      this.requestParams["selectedScheduleId"] = this.selectedScheduleId;
      this.requestParams["selectedJobType"] = this.selectedJobTypeValue;

      this.actionPath = "scheduler.do?";
      this.actionMethod = "method=enableOrDisableJob";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      this.refreshButton.enabled = false;
      this.refreshButton.buttonMode = false;
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "doJobDisable",
        0,
      );
    }
  }

  changeRecord(): void {
    try {
      this.requestParams = [];
      this.requestParams["changeButtonPressed"] = "Y";
      this.updateData(null);
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "changeRecord",
        0,
      );
    }
  }

  submitDeleteForm(): void {
    try {
      const reportsCount = this.getReportsByTheJob();
      let message = SwtUtil.getPredictMessage(
        "batchScheduler.confirm.removeJob",
        null,
      );

      if (this.selectedJobTypeValue === "R" && reportsCount) {
        message =
          message +
          "\n" +
          reportsCount +
          " " +
          SwtUtil.getPredictMessage(
            "batchScheduler.confirm.removeJobInfo",
            null,
          );
      }

      this.swtAlert.confirm(
        message,
        SwtUtil.getPredictMessage("alert_header.confirm", null),
        Alert.YES | Alert.NO,
        null,
        this.doDelete.bind(this),
      );
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "submitDeleteForm",
        0,
      );
    }
  }

  doDelete(): void {
    try {
      this.requestParams = [];
      this.maintainSortFilterStatus();
      this.requestParams["menuAccessId"] = this.menuAccessId;
      this.requestParams["scheduledJobType"] = this.scheduledJobType;

      if (this.showJobTypeDropdown) {
        this.selectedScheduledJobType = this.jobTypeList.selectedValue;
        this.requestParams["selectedScheduledJobType"] =
          this.selectedScheduledJobType;
      }

      this.requestParams["method"] = "SchedulerDelete";
      this.requestParams["selectedFilterStatus"] = this.filterStatus;
      this.requestParams["selectedSortStatus"] = this.sortStatus;
      this.requestParams["selectedSortDescending"] = this.sortDescending;
      this.requestParams["gridScrollTop"] =
        document.getElementById("dataGridContainer").scrollTop;
      this.requestParams["selectedjobId"] = this.selectedJobId;
      this.requestParams["selectedScheduleId"] = this.selectedScheduleId;
      this.requestParams["selectedJobType"] = this.selectedJobTypeValue;

      this.actionPath = "scheduler.do?";
      this.actionMethod = "method=SchedulerDelete";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      this.refreshButton.enabled = false;
      this.refreshButton.buttonMode = false;
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "doDelete",
        0,
      );
    }
  }

  getReportsByTheJob(): number {
    try {
      if (this.selectedJobId && this.selectedScheduleId) {
        const requestURL =
          this.baseURL +
          "scheduler.do?method=getreportsJobForAjax" +
          "&selectedJobId=" +
          this.selectedJobId +
          "&selectedScheduleId=" +
          this.selectedScheduleId;

        const xhr = new XMLHttpRequest();
        xhr.open("POST", requestURL, false);
        xhr.send();

        if (xhr.responseText) {
          return parseInt(xhr.responseText);
        }
      }
      return 0;
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "getReportsByTheJob",
        0,
      );
      return 0;
    }
  }

  openAddJob(): void {
    try {
      let selectedScheduledJobType = "R"; // Default

      if (this.showJobTypeDropdown && this.jobTypeList) {
        selectedScheduledJobType = this.jobTypeList.selectedValue;
      } else {
        selectedScheduledJobType = this.scheduledJobType;
      }

      // Call external interface method to open the add job window
      ExternalInterface.call(
        "openWindow",
        "scheduler.do?method=add&selectedScheduledJobType=" +
          selectedScheduledJobType +
          "&scheduledJobType=" +
          this.scheduledJobType,
        "",
        "left=50,top=190,width=650,height=545,toolbar=0,scrollbars=yes,status=yes,resizable=yes",
      );
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "openAddJob",
        0,
      );
    }
  }

  openEntityProcess(): void {
    try {
      // Call external interface method to open the entity process window
      ExternalInterface.call(
        "openWindow",
        "entityprocess.do?menuAccessId=" + this.menuAccessId,
        "",
        "left=50,top=190,width=1240,height=533,toolbar=0,status=yes,resizable=yes,scrollbars=yes",
      );
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "openEntityProcess",
        0,
      );
    }
  }

  closeHandler(): void {
    ExternalInterface.call("closeWindow");
  }

  doHelp(): void {
    ExternalInterface.call("doHelp", "batch-scheduler");
  }

  printPage(): void {
    try {
      window.print();
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "printPage",
        0,
      );
    }
  }

  keyDownEventHandler(event): void {
    if (event.key === "Enter") {
      this.printPage();
    }
  }

  refreshStatus(): void {
    try {
      if (
        !this.jobGrid ||
        !this.jobGrid.gridData ||
        this.jobGrid.gridData.size === 0
      ) {
        setTimeout(() => this.refreshStatus(), 10000);
        return;
      }

      // Get application name from external interface
      const appName = ExternalInterface.call("getAppName");
      const requestURL = this.baseURL + "scheduler.do?method=getJobStatus";

      for (let i = 0; i < this.jobGrid.gridData.size; i++) {
        const row = this.jobGrid.gridData.row[i];
        if (!row) continue;

        const oldStatus = row.currentStatus;
        if (oldStatus !== "Disabled") {
          const scheduleId = row.scheduleId;
          const execTime = row.lastExecTimeAsString;

          const xhr = new XMLHttpRequest();
          xhr.open(
            "POST",
            requestURL +
              "&scheduleId=" +
              scheduleId +
              "&LastExecTime=" +
              execTime,
            false,
          );
          xhr.setRequestHeader("Pragma", "no-cache");
          xhr.send();

          const newStatus = xhr.responseText;

          if (newStatus === "@true") {
            this.updateData(null);
            return;
          } else if (oldStatus !== newStatus && newStatus !== "@false") {
            if (
              row.jobTypeFullDescription !== "Manual" ||
              newStatus !== "Pending"
            ) {
              row.currentStatus = newStatus;
              this.updateData(null);
              return;
            }
          }
        }
      }

      setTimeout(() => this.refreshStatus(), 10000);
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "refreshStatus",
        0,
      );
      setTimeout(() => this.refreshStatus(), 10000);
    }
  }

  onSelectTableRow(event): void {
    try {
      console.log("🚀 ~ BatchScheduler ~ onSelectTableRow ~ event:", event);
      const row = event;
      console.log("🚀 ~ BatchScheduler ~ onSelectTableRow ~ row:", row);
      if (!row) return;

      this.selectedJobId = row.jobId.content;
      this.selectedScheduleId = row.scheduleId.content;
      this.selectedJobTypeValue = row.jobTypeProcessOrReport.content;

      // Update button states based on row selection
      this.updateButtonStatesOnSelection(row);
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "onSelectTableRow",
        0,
      );
    }
  }

  updateButtonStatesOnSelection(row): void {
    console.log("🚀 ~ BatchScheduler ~ updateButtonStatesOnSelection ~ row:", row)
    try {
      // Reset all buttons to disabled state
      this.executeButton.enabled = false;
      this.changeButton.enabled = false;
      this.removeButton.enabled = false;
      this.enableButton.enabled = false;
      this.disableButton.enabled = false;

      if (parseInt(this.menuEntityCurrGrpAccess) === 0) {
        if (row.currentStatus.content !== "C") {
          this.changeButton.enabled = true;
          this.removeButton.enabled = true;
        }

        console.log(
          "🚀 ~ BatchScheduler ~ updateButtonStatesOnSelection ~ row.jobTypeFullDescription:",
          row.jobTypeFullDescription,
        );
        if (
          row.jobTypeFullDescription.content === "Manual" &&
          row.currentStatus.content !== "D" &&
          row.currentStatus.content !== "C"
        ) {
          this.executeButton.enabled = true;
        }

        console.log(
          "🚀 ~ BatchScheduler ~ updateButtonStatesOnSelection ~ row.currentStatus:",
          row.currentStatus,
        );
        if (row.currentStatus.content !== "D") {
          if (row.currentStatus.content !== "C") {
            this.disableButton.enabled = true;
          }
        } else {
          this.enableButton.enabled = true;
        }
      }
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "updateButtonStatesOnSelection",
        0,
      );
    }
  }

  updateButtonStates(): void {
    try {
      // Check if add button should be enabled based on menu access
      if (this.menuEntityCurrGrpAccess === "0") {
        this.addButton.enabled = true;
      } else {
        this.addButton.enabled = false;
      }

      // Reset all other buttons to disabled state
      this.executeButton.enabled = false;
      this.changeButton.enabled = false;
      this.removeButton.enabled = false;
      this.enableButton.enabled = false;
      this.disableButton.enabled = false;
    } catch (error) {
      SwtUtil.logError(
        error,
        this.moduleId,
        this.commonService.getQualifiedClassName(this),
        "updateButtonStates",
        0,
      );
    }
  }

  startOfComms(): void {
    this.loadingImage.visible = true;
  }

  endOfComms(): void {
    this.loadingImage.visible = false;
  }

  private inputDataFault(error): void {
    console.error(error);
    this.swtAlert.error("Communication error: " + error, "Error");
  }
}

// Define lazy loading routes
const routes: Routes = [{ path: "", component: BatchScheduler }];
export const routing: ModuleWithProviders<any> = RouterModule.forChild(routes);

// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [BatchScheduler],
  entryComponents: [],
})
export class BatchSchedulerModule {}
