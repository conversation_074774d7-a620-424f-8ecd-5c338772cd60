<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox  width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingRight="5" paddingBottom="5">
    <Grid  minWidth="900" width="100%" height="100" paddingLeft="10" paddingRight="10">
      <GridRow width="100%">
        <GridItem width="240">
          <SwtLabel #scenarioIdLbl></SwtLabel>
        </GridItem>
        <GridItem width="300">
          <SwtTextInput #scenarioIdTxt maxChars="20" width="100%"></SwtTextInput>
        </GridItem>
        <GridItem width="80%">
          <HBox width="100%" horizontalAlign="right">
            <SwtLabel enabled="false" #systemLbl fontWeight="normal"></SwtLabel>
            <SwtCheckBox #systemCheck enabled="false"></SwtCheckBox>
          </HBox>

        </GridItem>
      </GridRow>
      <GridRow width="100%">
        <GridItem width="240">
          <SwtLabel #titleLbl></SwtLabel>
        </GridItem>
        <GridItem width="300">
          <SwtTextInput #titleTxt maxChars="50" width="100%"></SwtTextInput>
        </GridItem>
        <GridItem width="80%">
          <HBox width="100%" horizontalAlign="right">
            <SwtLabel #activeLbl fontWeight="normal"></SwtLabel>
            <SwtCheckBox #activeCheck ></SwtCheckBox>
          </HBox>
        </GridItem>
      </GridRow>
      <GridRow width="100%">
        <GridItem width="240">
          <SwtLabel #desciptionLbl></SwtLabel>
        </GridItem>
        <GridItem width="90%"  horizontalAlign="right">
          <SwtTextArea #descriptionTxt width="100%" maxChars="200" height="40"></SwtTextArea>
        </GridItem>
      </GridRow>
    </Grid>
    <SwtTabNavigator #tabNavigator id="tabNavigatorComponent" paddingBottom="3" minWidth="900"   minHeight="590" width="100%" height="100%"></SwtTabNavigator>
    <SwtCanvas width="100%" height="40"  minWidth="900">
      <HBox width="100%">
        <SwtButton #saveButton (click)="saveHandler()"></SwtButton>
        <SwtButton #cancelButton (click)="cancelHandler()"></SwtButton>
      </HBox>
      <HBox horizontalAlign="right">
        <SwtLoadingImage #loadingImg></SwtLoadingImage>
      </HBox>
    </SwtCanvas>
  </VBox>

</SwtModule>
