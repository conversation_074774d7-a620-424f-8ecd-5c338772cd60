
<SwtModule (creationComplete)="onLoad()" width="100%" height="500" >
  <VBox  height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas   width="100%"
                 height="25%" paddingTop="5">
      <VBox width="100%" height="100%" paddingLeft="10" verticalGap="1">
        <HBox width="100%" height="25%">
          <SwtLabel #lblType  width="60"></SwtLabel>
          <SwtComboBox #cbType
                       width="160"
                       dataLabel="types"
                       (change)="typeChange()"></SwtComboBox>
        </HBox>
        <HBox width="100%" height="25%">
          <SwtLabel #lblEntity width="60"></SwtLabel>
          <SwtComboBox #cbEntity
                       width="160"
                       dataLabel="entity"
                       (change)="typeChange()"></SwtComboBox>
          <SwtLabel #entityNamelbl fontWeight="normal"></SwtLabel>
        </HBox>
        <HBox width="100%" height="25%">
          <SwtLabel #lblId width="60"></SwtLabel>
          <SwtTextInput #txtId
                        width="240"></SwtTextInput>
          <SwtButton #btnSearch (click)="typeChange()" ></SwtButton>
        </HBox>
        <HBox width="100%" height="25%">
          <SwtLabel #lblName width="60"></SwtLabel>
          <SwtTextInput #txtName
                        width="240"></SwtTextInput>
        </HBox>
      </VBox>
    </SwtCanvas>

    <SwtCanvas #findPopUpCanvas
               width="100%"
               height="67%"></SwtCanvas>

    <SwtCanvas  width="100%" height="8%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton #addButton width="70"
                     (click)="addValuesToParent()"></SwtButton>
          <SwtButton #closeButton width="70"
                     id="closeButton"
                     (click)="closeHandler()"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right"  paddingRight="5" top="3">
          <SwtHelpButton id="helpIcon"
                         #helpIcon></SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>

