import { Router } from '@angular/router';
import { Component, ElementRef, OnDestroy, OnInit, ViewChild, NgModule } from '@angular/core';
import moment from "moment";
import { Alert, CommonService, ExternalInterface, focusManager, HBox, HTTPComms, JSONReader, Keyboard, StringUtils, SwtAlert, SwtButton, SwtCheckBox, SwtComboBox, SwtDateField, SwtLabel, SwtLoadingImage, SwtModule, SwtPanel, SwtPopUpManager, SwtRadioButtonGroup, SwtRadioItem, SwtTextArea, SwtTextInput, SwtUtil, TitleWindow, SwtToolBoxModule } from 'swt-tool-box';
import { FourEyesProcess } from '../../../FourEyesProcess/FourEyesProcess';
import { ListValues } from "../../../ListValues/ListValues";
import { ModuleWithProviders } from '@angular/compiler/src/core';
import { RouterModule, Routes } from '@angular/router';

declare var instanceElement: any;
declare function validateCurrencyPlaces(strField, strPat, currCode): any;


declare function validateFormatTime(strField): any;

@Component({
  selector: 'pcstop-rules-add',
  templateUrl: './StopRulesAdd.html',
  styleUrls: ['./StopRulesAdd.css']

})


export class StopRulesAdd extends SwtModule implements OnInit, OnDestroy {
  private swtAlert: SwtAlert;


  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private saveData = new HTTPComms(this.commonService);
  private checkData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];
  public screenName: string = null;
  public helpURL: string = null;
  private message: string = null;
  public title: string = null;
  private groupId: string = null;
  public searchQuery = "";
  public queryToDisplay = "";
  public errorLocation = 0;
  private win: TitleWindow;
  private ruleQuery: string = null;
  public stopRuleId: String = null;

  public ruleToDisplay: string = '';
  public tableToJoin = [];
  public tabAllConditions = [];
  private tableName: string;
  private newQuery: boolean = false;

  private storeRuleTypeComb: string = null;
  private ruleId: string;
  public ruleName: string;
  private stopRuleScreenName: string;

  private moduleId = 'AML';
  public selectedItemsList: string = null;
  public selectedItemsListCcy: string = null;
  public selectedItemsListCountry: string = null;
  public selectedItemsListCounterParty: string = null;
  public selectedItemsListSource: string = null;
  public selectedItemsListMessageType: string = null;
  public selectedItemsListAcctGrp: string = null;
  // private stopPayementsInLabel: string = "STOP all payments in ";
  // private stopPayementsToLabel: string = "STOP all payments to ";
  // private stopPayementsFromLabel: string = "STOP all payments from ";
  // private stopPayementsFromToLabel: string = "STOP all payments from or to ";
  private stopPayementsInLabel: string = "";
  private stopPayementsToLabel: string = "";
  private stopPayementsFromLabel: string = "";
  private stopPayementsFromToLabel: string = "";
  private dateFormat;
  private queryToExecute: String = "";
  private numberOfLinkedPR: number = 0;

  private queryBuilderScreenName = "";

  private initialActiveStatus;
  private maintEventId = null;
  private menuaccess = 2;
  private actionFromParent = null;
  private parentMenuAccess = null;
  private requireAuthorisation = true;
  private  canAmendFacility= false;
  public authOthers = false;
  /***LodingImage*******/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********SwtCombo**************/
  @ViewChild('paymentinCcyComboBox') paymentinCcyComboBox: SwtComboBox;
  @ViewChild('paymentToCountryComboBox') paymentToCountryComboBox: SwtComboBox;
  @ViewChild('paymentToCounterPartyTextInput') paymentToCounterPartyTextInput: SwtTextInput;
  @ViewChild('amountOperatorTextInput') amountOperatorTextInput: SwtTextInput;
  @ViewChild('amountOperatorComboBox') amountOperatorComboBox: SwtComboBox;
  @ViewChild('paymentfromSourceCombobox') paymentfromSourceCombobox: SwtComboBox;
  @ViewChild('paymentfromMessageTypeComboBox') paymentfromMessageTypeComboBox: SwtComboBox;
  @ViewChild('acctGrpComboBox') acctGrpComboBox: SwtComboBox;

  /***********SwtDateField***********/
  @ViewChild('startDatePicker') startDatePicker: SwtDateField;
  @ViewChild('endDatePicker') endDatePicker: SwtDateField;

  /*********SwttextInput*******/
  @ViewChild('ruleNameTextInput') ruleNameTextInput: SwtTextInput;
  @ViewChild('ruleIdTextInput') ruleIdTextInput: SwtTextInput;

  @ViewChild('activatedOnTextInput') activatedOnTextInput: SwtTextInput;
  @ViewChild('activatedByTextInput') activatedByTextInput: SwtTextInput;
  @ViewChild('deactivatedOnTextInput') deactivatedOnTextInput: SwtTextInput;
  @ViewChild('deactivatedByTextInput') deactivatedByTextInput: SwtTextInput;

  /******SwtLabel*************/
  @ViewChild('paymentinCcyLabel') paymentinCcyLabel: SwtLabel;
  @ViewChild('paymentToCountryLabel') paymentToCountryLabel: SwtLabel;
  @ViewChild('paymentToCounterPartyLabel') paymentToCounterPartyLabel: SwtLabel;
  @ViewChild('paymentinAmountLabel') paymentinAmountLabel: SwtLabel;
  @ViewChild('paymentfromSourceLabel') paymentfromSourceLabel: SwtLabel;
  @ViewChild('paymentfromMessageTypeLabel') paymentfromMessageTypeLabel: SwtLabel;
  @ViewChild('startDateLabel') startDateLabel: SwtLabel;
  @ViewChild('endDateLabel') endDateLabel: SwtLabel;
  @ViewChild('acctGrpLabel') acctGrpLabel: SwtLabel;

  /*********SWtButton*************/
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('expressionBuilderButton') expressionBuilderButton: SwtButton;
  @ViewChild('ccyMoreItemsButton') ccyMoreItemsButton: SwtButton;
  @ViewChild('countryMoreItemsButton') countryMoreItemsButton: SwtButton;
  @ViewChild('sourceMoreItemsButton') sourceMoreItemsButton: SwtButton;
  @ViewChild('paymentMoreItemsButton') paymentMoreItemsButton: SwtButton;
  @ViewChild('acctGrpButton') acctGrpButton: SwtButton;

  @ViewChild('acceptButton') acceptButton: SwtButton;
  @ViewChild('rejectButton') rejectButton: SwtButton;
  @ViewChild('amendButton') amendButton: SwtButton;
  
  @ViewChild('cancelAmendButton') cancelAmendButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  /*********Checkbox**********/
  // @ViewChild('paymentinCcyCheckBox') paymentinCcyCheckBox: SwtCheckBox;
  // @ViewChild('paymentToCountryCheckBox') paymentToCountryCheckBox: SwtCheckBox;
  // @ViewChild('paymentToCounterPartyCheckBox') paymentToCounterPartyCheckBox: SwtCheckBox;
  // @ViewChild('paymentfromSourceCheckBox') paymentfromSourceCheckBox: SwtCheckBox;
  // @ViewChild('paymentfromMessageTypeCheckBox') paymentfromMessageTypeCheckBox: SwtCheckBox;

  // @ViewChild('accountGroupCheckBox') accountGroupCheckBox: SwtCheckBox;
  @ViewChild('activeCheckBox') activeCheckBox: SwtCheckBox;
  @ViewChild('stopAllCheckbox') stopAllCheckbox: SwtCheckBox;
  /********RadioButtons*****/
  @ViewChild('radioquickExpression') radioquickExpression: SwtRadioItem;
  @ViewChild('radioAdvancedExpression') radioAdvancedExpression: SwtRadioItem;
  @ViewChild('rulesType') rulesType: SwtRadioButtonGroup;
  @ViewChild('radioLeaveStoped') radioLeaveStoped: SwtRadioItem;
  @ViewChild('radioSetWaiting') radioSetWaiting: SwtRadioItem;
  @ViewChild('rulesActionOnDeactivation') rulesActionOnDeactivation: SwtRadioButtonGroup;
  /**********SwtPanel********/
  @ViewChild('quickExpressionPanel') quickExpressionPanel: SwtPanel;
  @ViewChild('advancedExpressionPanel') advancedExpressionPanel: SwtPanel;
  @ViewChild('activationInfoPanel') activationInfoPanel: SwtPanel;
  @ViewChild('headerPanel') headerPanel: SwtPanel;
  /********TextArea*******/
  @ViewChild('queryText') queryText: SwtTextArea;


  constructor(private commonService: CommonService, private element: ElementRef, private router: Router) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);

  }

  ngOnDestroy(): any {
    instanceElement = null;
  }

  ngOnInit(): void {

    // this.screenName = "add";


    this.acceptButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.label', null);
    this.acceptButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.tooltip', null);

    this.rejectButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.label', null);
    this.rejectButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.tooltip', null);

    
    this.amendButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.amend.label', null);
    this.amendButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.amend.tooltip', null);

    this.cancelAmendButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.cancelAmendButton.toolTip = SwtUtil.getPredictMessage('tooltip.CancelChanges', null);

    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.entityMonitor.close', null);


    let paramsFromParent = [];



    if (window.opener && window.opener.instanceElement) {
      paramsFromParent = window.opener.instanceElement.getParamsFromParent();
        
        this.screenName = paramsFromParent[0].screenName;
        this.stopRuleId = paramsFromParent[0].stopRuleId;
        if(paramsFromParent[0].maintEventId)
          this.maintEventId  = paramsFromParent[0].maintEventId;
        if(paramsFromParent[0].action)
          this.actionFromParent =paramsFromParent[0].action; 
        if(paramsFromParent[0].parentMenuAccess)
          this.parentMenuAccess =paramsFromParent[0].parentMenuAccess; 
        if(paramsFromParent[0].authOthers)
          this.authOthers =paramsFromParent[0].authOthers; 

        if(paramsFromParent[0].amendInFacilityAccess)
          this.canAmendFacility =paramsFromParent[0].amendInFacilityAccess; 


    }else {
      paramsFromParent = [{screenName: 'change', stopRuleId: 'stop_Atef'}];
      if (paramsFromParent) {

        this.screenName = paramsFromParent[0].screenName;
        this.stopRuleId = paramsFromParent[0].stopRuleId;
      }
    }
    

    instanceElement = this;
    this.saveButton.label = "Save";
    this.cancelButton.label = "Cancel";
    this.ccyMoreItemsButton.label = "...";
    this.countryMoreItemsButton.label = "...";
    this.sourceMoreItemsButton.label = "...";
    this.paymentMoreItemsButton.label = "...";
    this.acctGrpButton.label = "...";

    if (this.screenName == "add") {
      this.radioquickExpression.selected = true;
      // this.advancedExpressionPanel.enabled = false;
      this.expressionBuilderButton.enabled = false;
      // this.enableDisableCombo(false);

      //label of combo empty
      this.paymentinCcyLabel.text = ""; //this.stopPayementsInLabel;
      this.paymentToCountryLabel.text = "";// this.stopPayementsToLabel;
      this.paymentToCounterPartyLabel.text = "";// this.stopPayementsToLabel ;
      this.paymentinAmountLabel.text = "";// this.stopPayementsToLabel ;
      this.paymentfromSourceLabel.text = "";// this.stopPayementsFromLabel;
      this.paymentfromMessageTypeLabel.text = "";// this.stopPayementsFromLabel;
      this.acctGrpLabel.text = "";// this.stopPayementsFromToLabel;
      this.enableDisableActivatedFields(false);
    } else {
      this.enableDisableComponent(false);
    }

  }

  cancelAmendEventHandler(){
    this.destoyAllTooltips();
    window.opener.instanceElement.setViewOrAmendSubScreenFromChild('view');
    const currentUrl = this.router.url;
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigateByUrl('/stopRuleAdd');
  });


  }


  destoyAllTooltips(){
    $(".ui-tooltip" ).each(function( index ) {
        $(this).remove();
    });
  }

  amendEventHandler(){
    this.destoyAllTooltips();
    window.opener.instanceElement.setViewOrAmendSubScreenFromChild('change');
    const currentUrl = this.router.url;
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
        
        this.router.navigateByUrl('/stopRuleAdd');
    });

  }

  onLoad(): void {
    try {
      
     if(this.maintEventId && StringUtils.isTrue(this.authOthers) && this.screenName != "change"){
        this.rejectButton.visible = true;
        this.acceptButton.visible = true;
      }

      if(this.maintEventId  && this.screenName == "view"){
        this.amendButton.visible = true;
        this.amendButton.includeInLayout = true;
        this.saveButton.visible = false;
        this.saveButton.includeInLayout = false;
        if(StringUtils.isTrue(this.canAmendFacility)){
          this.amendButton.enabled = true;
          this.rejectButton.visible = true;
        }else {
          this.amendButton.enabled = false;
        }
      }else {
        if(this.maintEventId){
          this.saveButton.visible = true;
          this.saveButton.includeInLayout = true;
          this.cancelAmendButton.visible = true;
          this.cancelAmendButton.includeInLayout = true;

          this.closeButton.visible = true;
          this.closeButton.includeInLayout = true;

          this.cancelButton.visible = false;
          this.cancelButton.includeInLayout = false;

          this.amendButton.visible = false;
          this.amendButton.includeInLayout = false;
        }
      }
      this.requestParams = [];
      this.actionPath = "stopRulesPCM.do?";
      this.requestParams["moduleId"] = this.moduleId;
      if (this.screenName != "add") {
        this.requestParams["stopRuleId"] = this.stopRuleId;
        this.requestParams["screenName"] = this.screenName;
        this.requestParams["isFromMaintenanceEvent"] = this.maintEventId?"true":"false";
        
        this.requestParams["maintEventId"] = this.maintEventId;
        this.actionMethod = "method=viewOrChange";
      } else {
        this.actionMethod = "method=add";
      }
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      // this.inputDataResult(data);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);



      this.saveData.cbStart = this.startOfComms.bind(this);
      this.saveData.cbStop = this.endOfComms.bind(this);
      this.saveData.cbResult = (data) => {
        this.saveDataResult(data);
      };
      // this.inputDataResult(data);
      this.saveData.cbFault = this.inputDataFault.bind(this);
      this.saveData.encodeURL = false;


      this.checkData.cbStart = this.startOfComms.bind(this);
      this.checkData.cbStop = this.endOfComms.bind(this);
      this.checkData.cbResult = (data) => {
        this.checkDataResult(data);
      };
      // this.inputDataResult(data);
      this.checkData.cbFault = this.inputDataFault.bind(this);
      this.checkData.encodeURL = false;

    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "onLoad", this.errorLocation);
    }

  }

  formatDate(date: Date, format: String) {
    if (format && date) {
      if (format.toLowerCase() === "dd/mm/yyyy") {
        return (date.getDate()) + "/" + (date.getMonth() + 1) + "/" + date.getFullYear();
      } else {
        return (date.getMonth() + 1) + "/" + (date.getDate()) + "/" + date.getFullYear();
      }
    } else
      return "";
  }
  /**
   * startOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    // this.loadingImage.setVisible(true);
  }

  /**
   * endOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    // this.loadingImage.setVisible(false);
  }

  /**
   * inputDataResult
   * param event: ResultEvent
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(event): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        /* Get result as xml */
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        /* Condition to check lastRecievedXML not equal to prevRecievedXML */
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          /* Condition to check request reply status is true*/
          if (this.jsonReader.getRequestReplyMessage()) {
            //gets the help url
            this.helpURL = this.jsonReader.getSingletons().helpurl;
            if (!this.jsonReader.isDataBuilding()) {

              this.dateFormat = this.jsonReader.getScreenAttributes()["dateformat"];
              this.requireAuthorisation = this.jsonReader.getScreenAttributes()["requireAuthorisation"];
              this.currencyPattern = this.jsonReader.getScreenAttributes()["currencyPattern"];
              this.menuaccess = this.jsonReader.getScreenAttributes()["menuaccess"];


              this.startDatePicker.formatString = this.dateFormat.toLowerCase();
              this.endDatePicker.formatString = this.dateFormat.toLowerCase();


              this.paymentToCountryComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
              this.amountOperatorComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
              this.paymentinCcyComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
              // this.paymentToCounterPartyComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
              this.paymentfromSourceCombobox.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
              this.paymentfromMessageTypeComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
              this.acctGrpComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
              this.numberOfLinkedPR = parseInt(this.jsonReader.getSingletons().numberOfLinkedPR);

              this.rulesType.selectedValue = "Q";
              this.rulesActionOnDeactivation.selectedValue = "W";


             

              this.ruleNameTextInput.text = this.jsonReader.getSingletons().selectedStopRuleName;
              this.ruleIdTextInput.text = this.jsonReader.getSingletons().selectedStopRuleId;
              
              //Hold the initial status to force 4 eyes if this status was changed
              this.initialActiveStatus = this.jsonReader.getSingletons().isActive == 'Y';

              this.activatedOnTextInput.text = this.jsonReader.getSingletons().activatedOn;
              this.activatedByTextInput.text = this.jsonReader.getSingletons().activatedBy;
              this.deactivatedOnTextInput.text = this.jsonReader.getSingletons().deactivatedOn;
              this.deactivatedByTextInput.text = this.jsonReader.getSingletons().deactivatedBy;


              if (this.screenName !== "add") {
                if(this.maintEventId){
                  if(this.actionFromParent == "I"){
                    this.enableDisableComponent(true);
                    setTimeout(() => {
                      this.changeRadioButton(true);

                      // this.changeCombo(this.paymentinCcyComboBox, this.ccyMoreItemsButton, this.paymentinCcyLabel)
                      // this.changeCombo(this.paymentToCountryComboBox, this.countryMoreItemsButton, this.paymentToCountryLabel)
                      // this.changeCombo(this.paymentfromSourceCombobox, this.sourceMoreItemsButton,this.paymentfromSourceLabel )
                      // this.changeCombo(this.paymentfromMessageTypeComboBox, this.paymentMoreItemsButton, this.paymentfromMessageTypeLabel)
                      if (!this.radioAdvancedExpression.selected) {
                        this.updateQueryTextForStaticRule();
                      }
                    }, 100);
                }
                  else {
                    this.enableDisableComponent(false);
                  }
                  this.enableDisableActivatedFields(false);
                  // ;
                }
                this.activeCheckBox.selected = this.jsonReader.getSingletons().isActive == 'Y';
                if (this.screenName == "change") {
                  if (this.menuaccess == 0) {
                    if (this.numberOfLinkedPR > 0) {
                      if (this.jsonReader.getSingletons().isActive == 'Y') {
                        this.ruleNameTextInput.enabled = true;
                        this.rulesActionOnDeactivation.enabled = true;
                        this.saveButton.enabled = true;
                        this.endDatePicker.enabled = true;

                      } else {
                        if (this.jsonReader.getSingletons().isChangable) {
                          this.ruleNameTextInput.enabled = true;
                          this.rulesActionOnDeactivation.enabled = true;
                          this.saveButton.enabled = true;
                          this.endDatePicker.enabled = true;

                          if (this.jsonReader.getSingletons().minDate) {
                            var minDate = moment(this.jsonReader.getSingletons().minDate, this.dateFormat.toUpperCase(), true);
                            this.endDatePicker.selectableRange = { rangStart: minDate.toDate(), rangeEnd: null };
                          }
                        } else {
                          this.ruleNameTextInput.enabled = false;
                        }
                      }
                    } else {

                      this.rulesActionOnDeactivation.enabled = true;
                      this.saveButton.enabled = true;

                      this.startDatePicker.enabled = true;
                      this.endDatePicker.enabled = true;


                    }

                    if (this.activeCheckBox.selected) {
                      this.activeCheckBox.enabled = true;
                    } else {

                      if (this.numberOfLinkedPR > 0) {
                        this.activeCheckBox.enabled = false;
                      } else {
                        this.activeCheckBox.enabled = true;
                      }
                    }
                  } else {
                    this.ruleNameTextInput.enabled = false;
                  }



                } else {
                  this.ruleNameTextInput.enabled = false;
                }

                if (this.jsonReader.getSingletons().predifinedRules == "A") {
                  this.rulesType.selectedValue = "A";
                  this.radioAdvancedExpression.selected = true;
                } else if (this.jsonReader.getSingletons().predifinedRules == "Q") {
                  this.radioquickExpression.selected = true;
                }

                if (this.jsonReader.getSingletons().stopAll == "Y") {
                  this.stopAllCheckbox.selected = true;
                }

                if (this.jsonReader.getSingletons().actionOnDeactivation == "W") {
                  this.rulesActionOnDeactivation.selectedValue = "W";
                  this.radioSetWaiting.selected = true;
                } else {
                  this.rulesActionOnDeactivation.selectedValue = "S";
                  this.radioLeaveStoped.selected = true;
                }





                if (this.jsonReader.getSingletons().selectedCcy) {
                  // this.paymentinCcyCheckBox.selected = true;
                  this.paymentinCcyComboBox.selectedLabel = this.selectedItemsListCcy = String(this.jsonReader.getSingletons().selectedCcy);
                  this.paymentinCcyLabel.toolTip = this.stopPayementsInLabel + String(this.jsonReader.getSingletons().selectedCcy);
                  if (this.jsonReader.getSingletons().selectedCcy.indexOf(',') > -1) {
                    this.ccyMoreItemsButton.enabled = true;
                    this.paymentinCcyComboBox.selectedIndex = 1;
                    this.paymentinCcyLabel.text = this.stopPayementsInLabel + this.get4FirstItemsFromList(String(this.jsonReader.getSingletons().selectedCcy));
                  }
                }

                if (this.jsonReader.getSingletons().selectedCountry) {
                  // this.paymentToCountryCheckBox.selected = true;
                  this.paymentToCountryComboBox.selectedLabel = this.selectedItemsListCountry = String(this.jsonReader.getSingletons().selectedCountry);
                  if (this.jsonReader.getSingletons().selectedCountry.indexOf(',') > -1) {
                    this.countryMoreItemsButton.enabled = true;
                    this.paymentToCountryComboBox.selectedIndex = 1;
                    this.paymentToCountryLabel.text = this.stopPayementsToLabel + this.get4FirstItemsFromList(String(this.jsonReader.getSingletons().selectedCountry));
                  }
                }

                if (this.jsonReader.getSingletons().selectedSource) {
                  // this.paymentfromSourceCheckBox.selected = true;

                  this.paymentfromSourceCombobox.selectedLabel = this.selectedItemsListSource = String(this.jsonReader.getSingletons().selectedSource);
                  if (this.jsonReader.getSingletons().selectedSource.indexOf(',') > -1) {
                    this.sourceMoreItemsButton.enabled = true;
                    this.paymentfromSourceCombobox.selectedIndex = 1;
                    this.paymentfromSourceLabel.text = this.stopPayementsFromLabel + this.get4FirstItemsFromList(String(this.jsonReader.getSingletons().selectedSource));
                  }
                }

                if (this.jsonReader.getSingletons().selectedMessageType) {
                  // this.paymentfromMessageTypeCheckBox.selected = true;

                  this.paymentfromMessageTypeComboBox.selectedLabel = this.selectedItemsListMessageType = String(this.jsonReader.getSingletons().selectedMessageType);
                  if (this.jsonReader.getSingletons().selectedMessageType.indexOf(',') > -1) {
                    this.paymentMoreItemsButton.enabled = true;
                    this.paymentfromMessageTypeComboBox.selectedIndex = 1;
                    this.paymentfromMessageTypeLabel.text = this.stopPayementsFromLabel + this.get4FirstItemsFromList(String(this.jsonReader.getSingletons().selectedMessageType));
                  }
                }

                if (this.jsonReader.getSingletons().selectedAcctGrp) {
                  // this.accountGroupCheckBox.selected = true;

                  this.acctGrpComboBox.selectedLabel = this.selectedItemsListAcctGrp = String(this.jsonReader.getSingletons().selectedAcctGrp);
                  if (this.jsonReader.getSingletons().selectedAcctGrp.indexOf(',') > -1) {
                    this.acctGrpButton.enabled = true;
                    this.acctGrpComboBox.selectedIndex = 1;
                    this.acctGrpLabel.text = this.stopPayementsFromToLabel + this.get4FirstItemsFromList(String(this.jsonReader.getSingletons().selectedAcctGrp));
                  }
                }

                if (this.jsonReader.getSingletons().selectedCounterParty) {
                  // this.paymentToCounterPartyCheckBox.selected = true;
                  this.paymentToCounterPartyTextInput.text = this.jsonReader.getSingletons().selectedCounterParty;
                  this.paymentToCounterPartyLabel.text = this.stopPayementsToLabel + this.paymentToCounterPartyTextInput.text;
                }

                if (this.jsonReader.getSingletons().selectedAmount) {
                  // this.paymentToCounterPartyCheckBox.selected = true;
                  this.amountOperatorComboBox.selectedValue = String(this.jsonReader.getSingletons().selectedOperator);
                  this.amountOperatorTextInput.text = this.jsonReader.getSingletons().selectedAmount;
                }

                /****this part will be modified getsingeltons of checkbox selected or not***/

                // this.paymentinCcyCheckBox.selected = true;
                // this.paymentToCountryCheckBox.selected = true;
                // this.paymentToCounterPartyCheckBox.selected = true;
                // this.paymentfromSourceCheckBox.selected = true;
                // this.paymentfromMessageTypeCheckBox.selected = true;
                // this.accountGroupCheckBox.selected = true;




                this.startDatePicker.text = String(this.jsonReader.getSingletons().selectedStartDate);
                this.endDatePicker.text = String(this.jsonReader.getSingletons().selectedEndDate);



                this.queryText.text = String(this.jsonReader.getSingletons().queryText);
                this.queryToExecute = String(this.jsonReader.getSingletons().querySQL);

                if(this.maintEventId){
                if(this.jsonReader.getSingletons().actionOnDeactivation_oldValue != this.jsonReader.getSingletons().actionOnDeactivation){
                  this.rulesActionOnDeactivation.toolTipPreviousValue = this.jsonReader.getSingletons().actionOnDeactivation_oldValue;
                }
                if(this.jsonReader.getSingletons().isActive_oldValue != this.jsonReader.getSingletons().isActive){
                  this.activeCheckBox.toolTipPreviousValue = this.jsonReader.getSingletons().isActive_oldValue;
                }
                if(this.jsonReader.getSingletons().selectedEndDate_oldValue != this.jsonReader.getSingletons().selectedEndDate){
                  this.endDatePicker.toolTipPreviousValue = this.jsonReader.getSingletons().selectedEndDate_oldValue;
                }

                if(this.jsonReader.getSingletons().selectedStartDate_oldValue != this.jsonReader.getSingletons().selectedStartDate){
                  this.startDatePicker.toolTipPreviousValue = this.jsonReader.getSingletons().selectedStartDate_oldValue;
                }

                if(this.jsonReader.getSingletons().selectedStopRuleName_oldValue != this.jsonReader.getSingletons().selectedStopRuleName){
                  this.ruleNameTextInput.toolTipPreviousValue = this.jsonReader.getSingletons().selectedStopRuleName_oldValue;
                }
                
                if(this.jsonReader.getSingletons().activatedBy_oldValue != this.jsonReader.getSingletons().activatedBy){
                  this.activatedByTextInput.toolTipPreviousValue = this.jsonReader.getSingletons().activatedBy_oldValue;
                }

                if(this.jsonReader.getSingletons().deactivatedBy_oldValue != this.jsonReader.getSingletons().deactivatedBy){
                  this.activatedByTextInput.toolTipPreviousValue = this.jsonReader.getSingletons().deactivatedBy_oldValue;
                }

                if(this.jsonReader.getSingletons().activatedBy_oldValue != this.jsonReader.getSingletons().activatedBy){
                  this.activatedByTextInput.toolTipPreviousValue = this.jsonReader.getSingletons().activatedBy_oldValue;
                }

                if(this.jsonReader.getSingletons().deactivatedOn_oldValue != this.jsonReader.getSingletons().deactivatedOn){
                  this.deactivatedOnTextInput.toolTipPreviousValue = this.jsonReader.getSingletons().deactivatedOn_oldValue;
                }

                if(this.jsonReader.getSingletons().activatedOn_oldValue != this.jsonReader.getSingletons().activatedOn){
                  this.activatedOnTextInput.toolTipPreviousValue = this.jsonReader.getSingletons().activatedOn_oldValue;
                }
              }


              
                // this.rulesActionOnDeactivation.toolTipPreviousValue = "ateffff";
                // this.startDatePicker.toolTipPreviousValue = "atefff";
                // this.activeCheckBox.toolTipPreviousValue = "zzzzzz";
                // this.ruleNameTextInput.toolTipPreviousValue = "zzzzzz";
                // this.queryText.toolTipPreviousValue = "zzzaaa";

              } else {

                this.activeCheckBox.selected = true;

                // this.paymentinCcyComboBox.text = "";
                // this.paymentToCountryComboBox.text = "";
                // this.paymentToCounterPartyComboBox.text = "";
                // this.paymentfromMessageTypeComboBox.text = "";
                // this.paymentfromSourceCombobox.text = "";
                // this.acctGrpComboBox.text = "";
              }

              if (this.jsonReader.getSingletons().minDate) {
                var minDate = moment(this.jsonReader.getSingletons().minDate, this.dateFormat.toUpperCase(), true);
                if(this.endDatePicker.enabled == true)
                  this.endDatePicker.selectableRange = { rangStart: minDate.toDate(), rangeEnd: null };
                if(this.startDatePicker.enabled == true)
                  this.startDatePicker.selectableRange = { rangStart: minDate.toDate(), rangeEnd: null };
              }

            }
          }
        }
      }
    } catch (error) {
      console.log("error:   ", error);
      SwtUtil.logError(error, this.moduleId, "className", "inputDataResult", this.errorLocation);
    }
  }

  enableDisableComponent(value: boolean): void {
    this.ruleIdTextInput.enabled = value;
    this.rulesType.enabled = value;
    this.rulesActionOnDeactivation.enabled = value;
    this.activeCheckBox.enabled = value;
    this.expressionBuilderButton.enabled = value;
    this.activatedByTextInput.enabled = value;
    this.activatedOnTextInput.enabled = value;
    this.deactivatedByTextInput.enabled = value;
    this.deactivatedOnTextInput.enabled = value;
    this.saveButton.enabled = value;
    this.paymentinCcyComboBox.enabled = value;
    this.paymentToCountryComboBox.enabled = value;
    this.amountOperatorComboBox.enabled = value;
    this.paymentfromMessageTypeComboBox.enabled = value;
    this.paymentfromSourceCombobox.enabled = value;
    this.paymentToCounterPartyTextInput.enabled = value;
    // this.amountOperatorTextInput.enabled = value;
    this.acctGrpComboBox.enabled = value;
    this.startDatePicker.enabled = value;
    this.endDatePicker.enabled = value;
    // this.accountGroupCheckBox.enabled = value;
    // this.paymentinCcyCheckBox.enabled = value;
    // this.paymentToCountryCheckBox.enabled = value;
    // this.paymentToCounterPartyCheckBox.enabled = value;
    // this.paymentfromSourceCheckBox.enabled = value;
    // this.paymentfromMessageTypeCheckBox.enabled = value;
    this.stopAllCheckbox.enabled = value;
  }
  enableDisableActivatedFields(value: boolean): void {
    this.activatedByTextInput.enabled = value;
    this.activatedOnTextInput.enabled = value;
    this.deactivatedByTextInput.enabled = value;
    this.deactivatedOnTextInput.enabled = value;
  }

  enableDisableCombo(value: boolean): void {
    this.paymentinCcyComboBox.enabled = value;
    this.paymentToCountryComboBox.enabled = value;
    this.paymentfromMessageTypeComboBox.enabled = value;
    this.paymentfromSourceCombobox.enabled = value;
    this.paymentToCounterPartyTextInput.enabled = value;
    if (value != true) {
      this.amountOperatorComboBox.enabled = value;
      this.amountOperatorTextInput.enabled = value;
    }
    this.acctGrpComboBox.enabled = value;

  }

  selectCheckBox(checkBox, comboOrTextInput, label, button): void {
    var defaultLabel: string;
    if (label.id == 'paymentinCcyLabel') {
      defaultLabel = this.stopPayementsToLabel;
      this.selectedItemsListCcy = '';
    } else if (label.id == 'paymentToCountryLabel') {
      defaultLabel = this.stopPayementsToLabel;
      this.selectedItemsListCountry = '';
    } else if (label.id == 'paymentToCounterPartyLabel') {
      defaultLabel = this.stopPayementsToLabel;
      this.selectedItemsListCounterParty = '';
    } else if (label.id == 'paymentfromSourceLabel') {
      defaultLabel = this.stopPayementsFromLabel;
      this.selectedItemsListSource = '';
    } else if (label.id == 'paymentfromMessageTypeLabel') {
      defaultLabel = this.stopPayementsFromToLabel;
      this.selectedItemsListMessageType = '';
    } else if (label.id == 'acctGrpLabel') {
      defaultLabel = this.stopPayementsFromToLabel;
      this.selectedItemsListAcctGrp = '';
    }
    if (checkBox.selected) {
      comboOrTextInput.enabled = true;
      label.text = "";
    } else {
      comboOrTextInput.enabled = false;
      comboOrTextInput.text = "";
      label.text = "";
      if (button)
        button.enabled = false;
    }
    this.updateQueryTextForStaticRule();

  }
  /*
    *** Stop All Checkbbox
   */
  stopAllPayments(): void {
    if (this.stopAllCheckbox.selected) {
      this.rulesType.selectedValue = "Q";
      this.radioquickExpression.selected = true;
      this.radioquickExpression.enabled = false;
      this.radioAdvancedExpression.enabled = false;
      this.quickExpressionPanel.enabled = false;
      // this.advancedExpressionPanel.enabled = false;
      this.expressionBuilderButton.enabled = false;
      this.enableDisableCombo(false);

      this.paymentinCcyComboBox.text = "";
      this.paymentToCountryComboBox.text = "";
      this.amountOperatorComboBox.text = "";
      this.paymentToCounterPartyTextInput.text = "";
      this.amountOperatorTextInput.text = "";
      this.paymentfromMessageTypeComboBox.text = "";
      this.paymentfromSourceCombobox.text = "";
      this.acctGrpComboBox.text = "";

      this.paymentinCcyComboBox.selectedIndex = -1;
      this.paymentToCountryComboBox.selectedIndex = -1;
      this.amountOperatorComboBox.selectedIndex = -1;
      this.paymentfromMessageTypeComboBox.selectedIndex = -1;
      this.paymentfromSourceCombobox.selectedIndex = -1;
      this.acctGrpComboBox.selectedIndex = -1;



      this.paymentinCcyLabel.text = "";
      this.paymentToCountryLabel.text = "";
      this.paymentToCounterPartyLabel.text = "";
      this.paymentinAmountLabel.text = "";
      this.paymentfromSourceLabel.text = "";
      this.paymentfromMessageTypeLabel.text = "";
      this.acctGrpLabel.text = "";

      // this.paymentinCcyCheckBox.selected = false;
      // this.paymentToCountryCheckBox.selected = false;
      // this.paymentToCounterPartyCheckBox.selected = false;
      // this.paymentfromSourceCheckBox.selected = false;
      // this.paymentfromMessageTypeCheckBox.selected = false;
      // this.accountGroupCheckBox.selected = false;

      this.acctGrpButton.enabled = false;
      this.paymentMoreItemsButton.enabled = false;
      this.sourceMoreItemsButton.enabled = false;
      this.countryMoreItemsButton.enabled = false;
      this.ccyMoreItemsButton.enabled = false;

      this.queryText.text = "";

      this.selectedItemsListCcy = '';
      this.selectedItemsListCountry = '';
      this.selectedItemsListCounterParty = '';
      this.selectedItemsListMessageType = '';
      this.selectedItemsListSource = '';
      this.selectedItemsListAcctGrp = '';

      this.tabConditionFromQueryBuilder = [];
      this.tableToJoinQueryBuilder = [];
      this.queryToExecute = '';


    } else {
      this.radioquickExpression.enabled = true;
      this.radioAdvancedExpression.enabled = true;
      this.changeRadioButton();
    }



  }



  /**
   * inputDataFault
   * param event:  FaultEvent
   * This is a callback , used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    this.swtAlert.error(event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail);
  }


  saveEventHandler() {

    Alert.okLabel = "OK";
    if (this.ruleIdTextInput.text.length == 0 || this.ruleNameTextInput.text.length == 0) {
      this.swtAlert.warning('Please fill all mandatory fields (marked with *)');
      return;
    }

    if (this.ruleNameTextInput.text.trim().length == 0) {
      this.swtAlert.warning('Rule Name can not be saved as just spaces');
      return;
    }

    if (this.startDatePicker.text) {
      if (!this.validateDateField(this.startDatePicker)) {
        return;
      }
    }
    if (this.endDatePicker.text) {
      if (!this.validateDateField(this.endDatePicker)) {
        return;
      }
    }

    if (!this.checkDates()) {
      this.swtAlert.warning('End Date must be later than Start date');
      return;
    }

    // if(this.paymentToCounterPartyCheckBox.selected) {
    if (!([0, 4, 6, 8, 11].indexOf(this.paymentToCounterPartyTextInput.text.length) > -1)) {
      this.swtAlert.warning('Counterparty must be 4, 6, 8 or 11 alpha numeric characters');
      return;
    }
    // }

    // if(!this.selectedItemsListCcy){
    //   this.swtAlert.warning('Currency checkbox is ticked. You must select one or multiple values.');
    //   return;
    // }
    // if(!this.selectedItemsListCountry){
    //   this.swtAlert.warning('Country checkbox is ticked. You must select one or multiple values.');
    //   return;
    // }

    // if(!this.selectedItemsListMessageType){
    //   this.swtAlert.warning('Message Type checkbox is ticked. You must select one or multiple values.');
    //   return;
    // }
    // if(!this.selectedItemsListSource){
    //   this.swtAlert.warning('Source checkbox is ticked. You must select one or multiple values.');
    //   return;
    // }
    // if(!this.selectedItemsListAcctGrp){
    //   this.swtAlert.warning('Account Group checkbox is ticked. You must select one or multiple values.');
    //   return;
    // }


    if (!this.queryText.text && this.stopAllCheckbox.selected == false) {
      this.swtAlert.warning('Stop rule query cannot be empty!');
      return;
    }


    this.checkIfRecordExist();

  }

  checkIfRecordExist() {
    if (this.screenName == "add") {

      this.actionMethod = "method=checkIfRecordExist";
      this.requestParams["stopRuleId"] = this.ruleIdTextInput.text;
      this.checkData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.checkData.send(this.requestParams);
    } else {
      this.fourEyesCheck();
    }
  }


  fourEyesCheck() {

    // if (this.fourEyesRequired == true && (this.activeCheckBox.selected || (this.initialActiveStatus != this.activeCheckBox.selected))) {
    //   this.win = SwtPopUpManager.createPopUp(this, FourEyesProcess, {
    //     title: SwtUtil.getPredictMessage("label.fourEyes", null),
    //   });

    //   this.win.enableResize = false;
    //   this.win.width = '510';
    //   this.win.height = '215';
    //   this.win.showControls = true;
    //   this.win.isModal = true;
    //   this.win.onClose.subscribe((res) => {
    //     if (this.win.getChild().result) {
    //       if (this.win.getChild().result.login == "SUCCESS") {
    //         this.save();
    //       }
    //     }

    //   });
    //   this.win.display();
    // } else {
      this.save();
    // }

  }
  /**
   * save()
   * Method called on save button clicked
   */
  save(): void {
    try {


      this.requestParams = [];

      if (this.screenName == "change" && "I" != this.actionFromParent) {
        this.actionMethod = "method=update";
        this.requestParams["stopRuleId"] = this.ruleIdTextInput.text;
        this.requestParams["activeFrom"] = this.formatDate(this.startDatePicker.selectedDate, this.dateFormat);
        this.requestParams["activeTo"] = this.formatDate(this.endDatePicker.selectedDate, this.dateFormat);
        this.requestParams["stopRuleName"] = this.ruleNameTextInput.text;
        this.requestParams["actionOnDeactivation"] = this.rulesActionOnDeactivation.selectedValue;
        this.requestParams["isActive"] = this.activeCheckBox.selected;
        this.requestParams["maintEventId"] = this.maintEventId;
        
        this.saveData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.saveData.send(this.requestParams);
        return;

      }
      this.actionMethod = "method=save";

      /* if(!this.queryText.text){
         this.swtAlert.warning('Stop rule query cannot be empty!');
       }*/
      let amount: any = this.amountOperatorTextInput.text;

      if (this.currencyPattern == "currencyPat2") {
        amount = Number(amount.replace(/\./g, '').replace(/,/g, '.'));
      } else if (this.currencyPattern == "currencyPat1") {
        amount = Number(amount.replace(/,/g, ''));
      }



      this.requestParams["stopRuleId"] = this.ruleIdTextInput.text;
      this.requestParams["stopRuleName"] = this.ruleNameTextInput.text;
      this.requestParams["isActive"] = this.activeCheckBox.selected;
      this.requestParams["activeFrom"] = this.formatDate(this.startDatePicker.selectedDate, this.dateFormat);
      this.requestParams["activeTo"] = this.formatDate(this.endDatePicker.selectedDate, this.dateFormat);

      if (this.stopAllCheckbox.selected)
        this.requestParams["stopRuleType"] = 'ALL';
      else
        this.requestParams["stopRuleType"] = this.rulesType.selectedValue;

      this.requestParams["actionOnDeactivation"] = this.rulesActionOnDeactivation.selectedValue;
      this.requestParams["stopAll"] = this.stopAllCheckbox.selected;

      this.requestParams["stopCcy"] = this.selectedItemsListCcy;
      this.requestParams["stopCountries"] = this.selectedItemsListCountry;
      this.requestParams["stopCounterParties"] = this.paymentToCounterPartyTextInput.text;

      if (this.amountOperatorTextInput.text && this.amountOperatorComboBox.selectedValue)
        this.requestParams["amount"] = this.amountOperatorComboBox.selectedValue + " " + amount;
      else
        this.requestParams["amount"] = "";

      this.requestParams["stopSources"] = this.selectedItemsListSource;
      this.requestParams["stopMessageTypes"] = this.selectedItemsListMessageType;
      this.requestParams["stopAccountGroups"] = this.selectedItemsListAcctGrp;
      this.requestParams["queryText"] = this.queryText.text;

      this.requestParams["querySQL"] = this.queryToExecute;
      this.requestParams['ruleConditionsAction'] = 'toSave';
      this.requestParams["maintEventId"] = this.maintEventId;
      
      let ruleConditionArray = [];
      let ruleConditionObject;
      if (this.tabConditionFromQueryBuilder.length !== 0) {
        for (var i = 0; i < this.tabConditionFromQueryBuilder.length; i++) {

          ruleConditionObject = new Object();
          ruleConditionObject.id = new Object();

          ruleConditionObject.id.conditionId = this.tabConditionFromQueryBuilder[i].conditionId;
          ruleConditionObject.fieldName = StringUtils.trim(this.tabConditionFromQueryBuilder[i].columnToStore);
          ruleConditionObject.operatorId = this.tabConditionFromQueryBuilder[i].operation;
          ruleConditionObject.fieldValue = StringUtils.trim(this.tabConditionFromQueryBuilder[i].columnCodeValue);
          ruleConditionObject.localValue = (this.tabConditionFromQueryBuilder[i].localValue !== undefined) ? this.tabConditionFromQueryBuilder[i].localValue : '';
          ruleConditionObject.tableName = this.tabConditionFromQueryBuilder[i].tabName;
          ruleConditionObject.profileField = this.tabConditionFromQueryBuilder[i].profileField;
          ruleConditionObject.profileFieldValue = this.tabConditionFromQueryBuilder[i].enumProfile;


          // if (this.ruleScreenName === 'add') {
          if (i + 1 < this.tabConditionFromQueryBuilder.length) {
            ruleConditionObject.nextCondition = this.tabConditionFromQueryBuilder[i + 1].andOrString;
          }
          // } else {
          //   this.requestParams['ruleConditions[' + i + '].nextCondition'] = this.tabAllConditions[i].andOrString;
          // }

          if (this.tabConditionFromQueryBuilder[i].typeCode === 'DECI') {
            ruleConditionObject.dataType = 'NUM';
          } else {
            ruleConditionObject.dataType = this.tabConditionFromQueryBuilder[i].typeCode;
          }

          ruleConditionArray.push(ruleConditionObject);
        }
      }

      this.requestParams["jsonResult"] = JSON.stringify(ruleConditionArray);

      this.saveData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.saveData.send(this.requestParams);

      // this.close();
    } catch (error) {
      console.log(error);

      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "save", this.errorLocation);
    }

  }

  /**
   * keyDownEventHandler
   * param event: KeyboardEvent
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  keyDownEventHandler(event: KeyboardEvent): void {
    try {
      //Currently focussed property name
      let eventString: string = Object(focusManager.getFocus()).name;
      if ((event.keyCode == Keyboard.ENTER)) {
        if (eventString == "saveButton") {
          this.saveEventHandler();
        } else if (eventString == "cancelButton") {
          this.closeBtn();
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "className", "keyDownEventHandler");
    }
  }


  /**
   * popupClosed
   * Method to close child windows when this screen is closed
   */
  popupClosed(): void {
    if (this.titleWindow) {
      this.close();
    } else {
      window.close();
    }
  }

  /**
   * dispose
   * This is a event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.requestParams = null;
      this.baseURL = null;
      this.actionMethod = null;
      this.actionPath = null;
      // this.close();
      if (this.titleWindow) {
        this.close();
      } else {
        window.close();
      }


    } catch (error) {
      console.log(error, this.moduleId, "className", "dispose");
    }
  }


  /**
   * close
   * param event
   *  called on click of close button to close the window
   */
  closeBtn(): void {
    try {
      this.dispose();
    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "close", this.errorLocation);
    }
  }

  changeCombo(combo, button, label) {
    try {
      if (combo.selectedValue == "<<Multiple Values>>") {
        button.enabled = true;
        label.text = "";

        if (label.id == 'paymentinCcyLabel') {
          this.selectedItemsListCcy = '';
          this.amountOperatorComboBox.selectedIndex = -1;
          this.amountOperatorTextInput.text = "";
          this.amountOperatorComboBox.enabled = false;
          this.amountOperatorTextInput.enabled = false;

        } else if (label.id == 'paymentToCountryLabel') {
          this.selectedItemsListCountry = '';
        } else if (label.id == 'paymentToCounterPartyLabel') {
          this.selectedItemsListCounterParty = '';
        } else if (label.id == 'paymentfromSourceLabel') {
          this.selectedItemsListSource = '';
        } else if (label.id == 'paymentfromMessageTypeLabel') {
          this.selectedItemsListMessageType = '';
        } else if (label.id == 'acctGrpLabel') {
          this.selectedItemsListAcctGrp = '';
        }

      } else {
        var defaultLabel: string;
        var selectedContent: string;
        if(combo.selectedItem){
          selectedContent =  combo.selectedItem.content;
        }else {
          selectedContent = null;
        }

        if (label.id == 'paymentinCcyLabel') {
          defaultLabel = this.stopPayementsToLabel;
          this.selectedItemsListCcy = selectedContent;

          if (combo.selectedIndex > 1) {
            this.amountOperatorComboBox.enabled = true;
            this.amountOperatorTextInput.enabled = true;
            this.amountOperatorComboBox.selectedIndex = 0;
          } else {
            this.amountOperatorComboBox.selectedIndex = -1;
            this.amountOperatorTextInput.text = "";
            this.amountOperatorComboBox.enabled = false;
            this.amountOperatorTextInput.enabled = false;
          }
        } else if (label.id == 'paymentToCountryLabel') {
          defaultLabel = this.stopPayementsToLabel;
          this.selectedItemsListCountry = selectedContent;
        } else if (label.id == 'paymentToCounterPartyLabel') {
          defaultLabel = this.stopPayementsToLabel;
          this.selectedItemsListCounterParty = selectedContent;
        } else if (label.id == 'paymentfromSourceLabel') {
          defaultLabel = this.stopPayementsFromLabel;
          this.selectedItemsListSource = selectedContent;
        } else if (label.id == 'paymentfromMessageTypeLabel') {
          defaultLabel = this.stopPayementsFromToLabel;
          this.selectedItemsListMessageType = selectedContent;
        } else if (label.id == 'acctGrpLabel') {
          defaultLabel = this.stopPayementsFromToLabel;
          this.selectedItemsListAcctGrp = selectedContent;
        }
        button.enabled = false;
        label.text = "";//defaultLabel + combo.selectedLabel;
      }
      //label.fontWeight = 'normal';


    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "changeComboPaymentinCcy", this.errorLocation);
    }


    this.updateQueryTextForStaticRule();

  }


  checkDates() {

    try {
      var startDate: any;
      var endDate: any;
      if (this.startDatePicker.text) {
        startDate = moment(this.startDatePicker.text, this.dateFormat.toUpperCase(), true);
      }
      if (this.endDatePicker.text) {
        endDate = moment(this.endDatePicker.text, this.dateFormat.toUpperCase(), true);
      }

      if (!startDate && endDate) {
        return false;
      }

      if (startDate && endDate && endDate.isBefore(startDate)) {
        return false;
      }

      return true;
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "changeEndDate", this.errorLocation);

    }
  }

  private tabConditionFromQueryBuilder: any = [];
  private tableToJoinQueryBuilder: any = [];
  saveRuleDetails(tabCondition: any, tableToJoin: any, queryToDisplay: string, queryToExecute: string) {
    this.tabConditionFromQueryBuilder = JSON.parse(tabCondition);
    this.tableToJoinQueryBuilder = JSON.parse(tableToJoin);
    this.queryText.text = queryToDisplay;


    this.queryToExecute = queryToExecute;
  }

  enableButtons() {
    this.saveButton.enabled = true;
  }




  get4FirstItemsFromList(list: string) {

    let result = "";
    return list.replace(/,/g, ', ');
    var occurrenceOfComma = (list.match(new RegExp(",", "g")) || []).length;
    if (list && list.indexOf(",") !== -1 && occurrenceOfComma >= 4) {
      result = list.split(",")[0].toString() + ',' + list.split(",")[1].toString() + ',' + list.split(",")[2].toString() + ',' + list.split(",")[3].toString() + ",...";
    } else {
      result = list;
    }

    return result;
  }

  multipleListSelect(comboSelectList, label) {
    var comboList: string;
    var defaultLabel: string;
    var shortSelectedLabel: any;
    try {
      if (label.id == 'paymentinCcyLabel') {
        defaultLabel = this.stopPayementsToLabel;
      } else if (label.id == 'paymentToCountryLabel') {
        defaultLabel = this.stopPayementsToLabel;
      } else if (label.id == 'paymentToCounterPartyLabel') {
        defaultLabel = this.stopPayementsToLabel
      } else if (label.id == 'paymentfromSourceLabel') {
        defaultLabel = this.stopPayementsFromLabel;
      } else if (label.id == 'paymentfromMessageTypeLabel') {
        defaultLabel = this.stopPayementsFromToLabel;
      } else if (label.id == 'acctGrpLabel') {
        defaultLabel = this.stopPayementsFromToLabel;
      }
      if (comboSelectList == "paymentToCountryComboBox") {
        comboList = 'country';
      } else if (comboSelectList == 'paymentinCcyComboBox') {
        comboList = 'currencyList';
      } else if (comboSelectList == 'paymentToCounterPartyComboBox') {
        comboList = 'counterParties'
      } else if (comboSelectList == 'paymentfromSourceCombobox') {
        comboList = 'source';
      } else if (comboSelectList == 'paymentfromMessageTypeComboBox') {
        comboList = 'messageType';
      } else if (comboSelectList == 'acctGrpComboBox') {
        comboList = 'AcctGrpList';
      }


      this.win = SwtPopUpManager.createPopUp(this, ListValues, {
        title:
          comboList,
        operation: "in",
        dataSource: "fromStopRules",
        columnLabel: comboList,
        columnCode: comboList,
        viewOnly: (this.screenName !== "add" && "I" != this.actionFromParent),
      });
      this.win.enableResize = false;
      this.win.id = "listValuesPopup";
      this.win.width = '500';
      this.win.height = '500';
      this.win.showControls = true;
      this.win.isModal = true;
      this.win.onClose.subscribe((res) => {
        if (this.win.getChild().result) {
          if (comboList == 'currencyList') {
            this.selectedItemsList = this.selectedItemsListCcy;
          } else if (comboList == 'country') {
            this.selectedItemsList = this.selectedItemsListCountry;
          } else if (comboList == 'counterParties') {
            this.selectedItemsList = this.selectedItemsListCounterParty;
          } else if (comboList == 'source') {
            this.selectedItemsList = this.selectedItemsListSource;
          } else if (comboList == 'messageType') {
            this.selectedItemsList = this.selectedItemsListMessageType;
          } else if (comboList == 'AcctGrpList') {
            this.selectedItemsList = this.selectedItemsListAcctGrp;
          }
          var occurrenceOfComma = (this.selectedItemsList.match(new RegExp(",", "g")) || []).length;
          /* if (this.selectedItemsList && this.selectedItemsList.indexOf(",") !== -1 && occurrenceOfComma >= 4) {
             label.text = defaultLabel + this.selectedItemsList.split(",")[0].toString() +
              ',' + this.selectedItemsList.split(",")[1].toString() +',' + this.selectedItemsList.split(",")[2].toString() +
              ',' + this.selectedItemsList.split(",")[3].toString() + ",...";
           } else {*/
          label.text = defaultLabel + this.selectedItemsList.replace(/,/g, ', ');
          /*}*/
        }

        this.updateQueryTextForStaticRule();
      });
      this.win.display();


      // SwtPopUpManager.addPopUp(this, ListValues, {
      //   title:
      //   comboList,
      //   operation: "in",
      //   dataSource: "fromStopRules",
      //   columnLabel: comboList,
      //   columnCode: comboList
      // }, true)
      //   .subscribe(() => {
      //     if (comboList == 'currencyList') {
      //       this.selectedItemsList = this.selectedItemsListCcy;
      //     } else if (comboList == 'country') {
      //       this.selectedItemsList = this.selectedItemsListCountry;
      //     } else if (comboList == 'counterParties') {
      //       this.selectedItemsList = this.selectedItemsListCounterParty;
      //     } else if (comboList == 'source') {
      //       this.selectedItemsList = this.selectedItemsListSource;
      //     } else if (comboList == 'messageType') {
      //       this.selectedItemsList = this.selectedItemsListMessageType;
      //     } else if (comboList == 'AcctGrpList') {
      //       this.selectedItemsList = this.selectedItemsListAcctGrp;
      //     }
      //     var occurrenceOfComma = (this.selectedItemsList.match(new RegExp(",", "g")) || []).length;
      //     if (this.selectedItemsList && this.selectedItemsList.indexOf(",") !== -1 && occurrenceOfComma >= 2) {
      //       label.text = defaultLabel + this.selectedItemsList.split(",")[0].toString() + ' ,' + this.selectedItemsList.split(",")[1].toString() + ",...";
      //     } else {
      //       label.text = defaultLabel + this.selectedItemsList;
      //     }
      //   });


      //   subscribe((res) => {
      //     if (this.win.getChild().result) {
      //       this.reserveGrid.appendRow();
      //       this.reserveGrid.dataProvider[this.reserveGrid.dataProvider.length - 1].time = this.win.getChild().result.time;
      //       this.reserveGrid.dataProvider[this.reserveGrid.dataProvider.length - 1].reserve = this.win.getChild().result.reserve;
      //       this.reserveGrid.dataProvider[this.reserveGrid.dataProvider.length - 1].useCL = this.win.getChild().result.useCLCheckBox;
      //       this.reserveGrid.refresh();
      //       this.reserveGrid.selectedIndex = -1;

      //     }
      // });

    } catch (e) {
      console.log('eee', e)
    }

  }


  expressionButtonClick() {

    var typeId: string = '540';
    var errorLocation: number = 0;
    var childTitle: string;
    try {
      // Alert.yesLabel = SwtUtil.getCommonMessages('alert.yes');
      // Alert.noLabel = SwtUtil.getCommonMessages('alert.no');

      if (this.queryText.text !== '' && this.queryText.text !== null) {
        Alert.yesLabel = "Replace";
        Alert.noLabel = "Change";
        var message: string = "Do you want to replace all constraints or only change values for existing constraints?";
        this.swtAlert.confirm(message, "", Alert.YES | Alert.NO | Alert.CANCEL, null, this.clearRuleListener.bind(this));
      } else {
        this.queryBuilderScreenName = "add";
        this.saveButton.enabled = false;

        // var newWindow = window.open("/expressionBuilder", 'expression Builder', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
        // if (window.focus) {
        //   newWindow.focus();
        // }

        ExternalInterface.call('openChildWindow');

      }
    } catch (error) {
      console.log("error = ", error);

      SwtUtil.logError(error, this.moduleId, "className", "expressionButtonClick", this.errorLocation);
    }

  }
  updateQueryText(text, queryToExecute): any {
    this.queryText.text = text;
    this.queryToExecute = queryToExecute;
  }


  getParamsFromParent() {
    // return this.screenName;
    let params = [];
    if (this.queryBuilderScreenName == "add") {
      params = [
        {
          screenName: this.queryBuilderScreenName, queryToDisplay: '', queryToExecute: '',
          tabAllConditions: [], tableToJoin: []
        },
      ];

    } else {

      params = [
        {
          screenName: this.queryBuilderScreenName, queryToDisplay: this.queryText.text, queryToExecute: this.queryToExecute,
          tabAllConditions: JSON.stringify(this.tabConditionFromQueryBuilder), tableToJoin: JSON.stringify(this.tableToJoinQueryBuilder)
        },
      ];
    }

    return params;


  }

  clearRuleListener(event): any {
    try {

      if (event.detail === Alert.YES) {
        this.queryBuilderScreenName = "add";

        // var newWindow = window.open("/expressionBuilder", 'expression Builder', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
        // if (window.focus) {
        //   newWindow.focus();
        // }
        ExternalInterface.call('openChildWindow');


      } if (event.detail === Alert.NO) {
        this.queryBuilderScreenName = "change";
        // var newWindow = window.open("/expressionBuilder", 'expression Builder', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
        // if (window.focus) {
        //   newWindow.focus();
        // }
        ExternalInterface.call('openChildWindow');
      } else {

      }
    } catch (e) {

    }
  }

  /**
   * popupClosedEventHandler
   *
   * @param event :Event
   *
   * Method called when pop up is closed to refresh Grid and Enable Buttons after pop up closed
   *
   */
  private popupClosedEventHandler(event): void {
    //Variable for errorLocation
    var errorLocation: number = 0;
    try {
    } catch (error) {

    }

  }

  validateDateField(dateField) {
    if (dateField.text) {
      var date = moment(dateField.text, this.dateFormat.toUpperCase(), true);

      if (!date.isValid()) {
        this.swtAlert.warning('Date must be in the format ' + this.dateFormat.toUpperCase());
        return false;
      }
    }
    dateField.selectedDate = date.toDate();

    return true;
  }



  changeRadioButton(firstLoad= false): void {
    if(!firstLoad){
      this.selectedItemsListCcy = '';
      this.selectedItemsListCountry = '';
      this.selectedItemsListCounterParty = '';
      this.selectedItemsListMessageType = '';
      this.selectedItemsListSource = '';
      this.selectedItemsListAcctGrp = '';
    }

    if (this.radioAdvancedExpression.selected) {
      this.quickExpressionPanel.enabled = false;
      // this.advancedExpressionPanel.enabled = true;
      this.expressionBuilderButton.enabled = true;

      this.enableDisableCombo(false);

      if(!firstLoad){
        this.paymentinCcyComboBox.text = "";
        this.paymentToCountryComboBox.text = "";
        this.amountOperatorComboBox.text = "";
        this.paymentToCounterPartyTextInput.text = "";
        this.amountOperatorTextInput.text = "";
        this.paymentfromMessageTypeComboBox.text = "";
        this.paymentfromSourceCombobox.text = "";
        this.acctGrpComboBox.text = "";

        this.paymentinCcyComboBox.selectedIndex = -1;
        this.paymentToCountryComboBox.selectedIndex = -1;
        this.amountOperatorComboBox.selectedIndex = -1;
        this.paymentfromMessageTypeComboBox.selectedIndex = -1;
        this.paymentfromSourceCombobox.selectedIndex = -1;
        this.acctGrpComboBox.selectedIndex = -1;

        this.paymentinCcyLabel.text = "";
        this.paymentToCountryLabel.text = "";
        this.paymentToCounterPartyLabel.text = "";
        this.paymentfromSourceLabel.text = "";
        this.paymentfromMessageTypeLabel.text = "";
        this.acctGrpLabel.text = "";
        this.queryText.text = "";
      }

      // this.paymentinCcyCheckBox.selected = false;
      // this.paymentToCountryCheckBox.selected = false;
      // this.paymentToCounterPartyCheckBox.selected = false;
      // this.paymentfromSourceCheckBox.selected = false;
      // this.paymentfromMessageTypeCheckBox.selected = false;
      // this.accountGroupCheckBox.selected = false;

      this.acctGrpButton.enabled = false;
      this.paymentMoreItemsButton.enabled = false;
      this.sourceMoreItemsButton.enabled = false;
      this.countryMoreItemsButton.enabled = false;
      this.ccyMoreItemsButton.enabled = false;
     

    } else {

      this.enableDisableCombo(true);
      this.quickExpressionPanel.enabled = true;
      this.expressionBuilderButton.enabled = false;
      if(!firstLoad){
        
        this.queryText.text = "";
        // this.advancedExpressionPanel.enabled = false;

        this.tabConditionFromQueryBuilder = [];
        this.tableToJoinQueryBuilder = [];
        this.queryToExecute = '';
      }
    }
  }

  saveDataResult(event): void {
    try {
      if (this.saveData.isBusy()) {
        this.saveData.cbStop();
      } else {
        /* Get result as xml */
        this.jsonReader.setInputJSON(event);
        /* Condition to check request reply status is true*/
        if (this.jsonReader.getRequestReplyMessage() == "RECORD_EXIST") {
          this.swtAlert.error("Stop Rule ID already exists");
        } else if (this.jsonReader.getRequestReplyMessage() == "PROCESS_RUNNING") {
          this.swtAlert.error("Stop rule Process is running on the selected Rule , the selected Rule cannot be changed");
        } else if (this.jsonReader.getRequestReplyMessage() == "ERROR_SAVE") {
          this.swtAlert.error("Stop Rule could not be saved successfully.<br>Please contact your System Administrator!");
        } else {

          if(StringUtils.isTrue(this.requireAuthorisation)){
            this.swtAlert.show(SwtUtil.getPredictMessage("maintenanceevent.details.alert.actionneedauthorisation", null), "Warning", Alert.OK, null, this.closeWindow.bind(this));
          }else {
          if(this.maintEventId){
                  if(window.opener && window.opener.opener && window.opener.opener.instanceElement) {
                    window.opener.opener.instanceElement.updateData();
                  }
                  if(window.opener && window.opener.instanceElement) {
                    window.opener.close();
                  }
              
                  window.close();
                }else {
                if (window.opener.instanceElement) {
                  window.opener.instanceElement.refreshGrid();
                  this.closeBtn();
                }
              }
            }

        }
      }
    } catch (error) {
      console.log("error:   ", error);
      SwtUtil.logError(error, this.moduleId, "className", "saveDataResult", this.errorLocation);
    }

  }

  checkDataResult(event): void {
    try {
      if (this.saveData.isBusy()) {
        this.saveData.cbStop();
      } else {
        /* Get result as xml */
        this.jsonReader.setInputJSON(event);
        /* Condition to check request reply status is true*/
        if (this.jsonReader.getRequestReplyMessage() == "RECORD_EXIST") {
          this.swtAlert.error("Stop Rule ID already exists");
        } else if (this.jsonReader.getRequestReplyMessage() == "ERROR_SAVE") {
          this.swtAlert.error("Stop Rule could not be saved successfully.<br>Please contact your System Administrator!");
        } else {
          this.fourEyesCheck();
        }
      }
    } catch (error) {
      console.log("error:   ", error);
      SwtUtil.logError(error, this.moduleId, "className", "saveDataResult", this.errorLocation);
    }

  }


  private currencyPattern;

  validateAmount(): any {
    let currency = null;
    if (this.paymentinCcyComboBox.selectedIndex > 1) {
      currency = this.paymentinCcyComboBox.selectedLabel;
    }
    if (!(validateCurrencyPlaces(this.amountOperatorTextInput, this.currencyPattern, currency))) {
      this.swtAlert.warning('Please enter a valid amount');
      return false;
    }

    this.updateQueryTextForStaticRule();
  }

  updateQueryTextForStaticRule() {
    let simpleRuleQueryText = "";
    if (this.selectedItemsListCcy) {
      if (simpleRuleQueryText.length > 0) {
        simpleRuleQueryText += " And ";
      }
      if (this.selectedItemsListCcy.indexOf(',') > -1)
        simpleRuleQueryText += "[ Currency In (" + this.selectedItemsListCcy + ") ]";
      else
        simpleRuleQueryText += "[ Currency = " + this.selectedItemsListCcy + " ]";
    }
    if (this.selectedItemsListCountry) {
      if (simpleRuleQueryText.length > 0) {
        simpleRuleQueryText += " And ";
      }
      if (this.selectedItemsListCountry.indexOf(',') > -1)
        simpleRuleQueryText += "[ Country In (" + this.selectedItemsListCountry + ") ]";
      else
        simpleRuleQueryText += "[ Country = " + this.selectedItemsListCountry + " ]";
    }
    if (this.paymentToCounterPartyTextInput.text) {
      if (simpleRuleQueryText.length > 0) {
        simpleRuleQueryText += " And ";
      }
      simpleRuleQueryText += "[ Party BIC Like " + this.paymentToCounterPartyTextInput.text + "% ]";
    }
    if (this.amountOperatorTextInput.text && this.amountOperatorComboBox.selectedValue) {
      if (simpleRuleQueryText.length > 0) {
        simpleRuleQueryText += " And ";
      }
      simpleRuleQueryText += "[ Amount " + this.amountOperatorComboBox.selectedValue + " " + this.amountOperatorTextInput.text + " ]";
    }
    if (this.selectedItemsListSource) {
      if (simpleRuleQueryText.length > 0) {
        simpleRuleQueryText += " And ";
      }
      if (('' + this.selectedItemsListSource).indexOf(',') > -1)
        simpleRuleQueryText += "[ Source In (" + this.selectedItemsListSource + ") ]";
      else
        simpleRuleQueryText += "[ Source = " + this.selectedItemsListSource + " ]";
    }

    if (this.selectedItemsListMessageType) {
      if (simpleRuleQueryText.length > 0) {
        simpleRuleQueryText += " And ";
      }
      if (('' + this.selectedItemsListMessageType).indexOf(',') > -1)
        simpleRuleQueryText += "[ MessageType In (" + this.selectedItemsListMessageType + ") ]";
      else
        simpleRuleQueryText += "[ MessageType = " + this.selectedItemsListMessageType + " ]";
    }
    if (this.selectedItemsListAcctGrp) {
      if (simpleRuleQueryText.length > 0) {
        simpleRuleQueryText += " And ";
      }
      if (('' + this.selectedItemsListAcctGrp).indexOf(',') > -1)
        simpleRuleQueryText += "[ AccountGroup In (" + this.selectedItemsListAcctGrp + ") ]";
      else
        simpleRuleQueryText += "[ AccountGroup = " + this.selectedItemsListAcctGrp + " ]";
    }





    this.queryText.text = simpleRuleQueryText;

  }

  doHelp() {
    try {
      ExternalInterface.call("help");
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }

  }

  acceptEventEventHandler(): void {
    // const message = SwtUtil.getPredictMessage('alert.columndelete', null);
    const message =SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoaccept', null);
    this.swtAlert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.acceptStatusHandler.bind(this), null);
}

rejectEventEventHandler(): void {
  // const message = SwtUtil.getPredictMessage('alert.columndelete', null);
  const message =SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoreject', null);
  this.swtAlert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.rejectStatusHandler.bind(this), null);
}

acceptStatusHandler(closeEvent): void {
    if (closeEvent.detail == Alert.YES) {
      if (window.opener && window.opener.instanceElement) {
        this.changeStatusHandler('A');
      }
    }
  }
  rejectStatusHandler(closeEvent): void {
    if (closeEvent.detail == Alert.YES) {
      if (window.opener && window.opener.instanceElement) {
        this.changeStatusHandler('R');
      }
    }
  }




updateMaintenanceEventStatusResult(event):void {
  let errorLocation = 0;
  try {
  // Checks the inputData and stops the communication
  if (this.inputData.isBusy()) {
    this.inputData.cbStop();
  } else {
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    errorLocation = 10;
    //this.dataExport.enabled = true;
    if (this.jsonReader.getRequestReplyStatus()) {
      this.swtAlert.show(SwtUtil.getPredictMessage("maintenanceevent.details.alert.actionperfermored", null), "Warning", Alert.OK, null, this.closeWindow.bind(this));
      
    } else {
      if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }

      }
    }

  } catch (error) {
    // log the error in ERROR LOG
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "cellClickEventHandler", errorLocation);
  }
    
}


closeWindow(event) {
  if(event.detail == Alert.OK) {
    //refresh parent
    if(this.maintEventId){
      if(window.opener && window.opener.opener && window.opener.opener.instanceElement) {
        window.opener.opener.instanceElement.updateData();
      }
      if(window.opener && window.opener.instanceElement) {
        window.opener.close();
      }
  
      window.close();
    }else {
    if (window.opener.instanceElement) {
      window.opener.instanceElement.refreshGrid();
      this.closeBtn();
    }
  }
  }
}




changeStatusHandler(action) {
  let errorLocation = 0;
  try {
    this.actionPath = "maintenanceEvent.do?";
    this.actionMethod = 'method=updateMaintenanceEventStatus';
    errorLocation = 50;
    this.requestParams = [];
    this.requestParams['menuAccessId'] = this.parentMenuAccess;
    errorLocation = 60;
    this.requestParams['maintEventId'] =  this.maintEventId;
    errorLocation = 70;
    this.requestParams['action'] =  action;

    this.inputData.cbResult = (event) => {
      this.updateMaintenanceEventStatusResult(event);
    };
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 100;
    this.inputData.send(this.requestParams);
  } catch (error) {

    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "onLoad", errorLocation);
  }


}


}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: StopRulesAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [StopRulesAdd],
  entryComponents: []
})
export class StopRulesAddModule { }
