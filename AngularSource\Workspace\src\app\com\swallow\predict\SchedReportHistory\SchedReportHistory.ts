import { Component, ElementRef, ModuleWithProviders, NgModule, ViewChild } from '@angular/core';
import {
  SwtModule,
  CommonService,
  SwtAlert,
  SwtCommonGrid,
  JSONReader,
  HTTPComms,
  SwtUtil,
  SwtToolBoxModule,
  SwtButton,
  SwtHelpButton,
  SwtLoadingImage,
  Logger,
  ScreenVersion,
  CommonLogic,
  SwtCanvas,
  SwtComboBox,
  StringUtils,
  ExternalInterface,
  SwtDateField,
  SwtRadioButtonGroup,
  SwtLabel,
  Encryptor,
  DateUtils,
  SwtRadioItem,
  ContextMenuItem, SwtPopUpManager, JSONViewer, CommonUtil
} from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
import moment from "moment";

@Component({
  selector: 'app-sched-report-history',
  templateUrl: './SchedReportHistory.html',
  styleUrls: ['./SchedReportHistory.css']
})
export class SchedReportHistory extends SwtModule {

  @ViewChild('endDateLabel', { read: SwtLabel }) endDateLabel: SwtLabel;
  @ViewChild('selectedAccount', { read: SwtLabel }) selectedAccount: SwtLabel;
  @ViewChild('lblReportType', { read: SwtLabel }) lblReportType: SwtLabel;
  @ViewChild('selectedAttribute', { read: SwtLabel }) selectedAttribute: SwtLabel;
  @ViewChild('startDateLabel') startDateLabel: SwtLabel;
  @ViewChild('dateLabel') dateLabel: SwtLabel;
  // **********************  Buttons ***************************************************/
  @ViewChild('refreshButton', { read: SwtButton }) refreshButton: SwtButton;
  @ViewChild('downloadButton', { read: SwtButton }) downloadButton: SwtButton;
  @ViewChild('resendMailButton', { read: SwtButton }) resendMailButton: SwtButton;
  @ViewChild('viewButton', { read: SwtButton }) viewButton: SwtButton;
  @ViewChild('deleteButton', { read: SwtButton }) deleteButton: SwtButton;
  @ViewChild('closeButton', { read: SwtButton }) closeButton: SwtButton;
  @ViewChild('helpButton', { read: SwtHelpButton }) helpButton: SwtHelpButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  // ************************** Combobox ****************************************************** 
  @ViewChild('reportJobsCombo', { read: SwtComboBox }) reportJobsCombo: SwtComboBox;
  @ViewChild('reportTypesCombo', { read: SwtComboBox }) reportTypesCombo: SwtComboBox;

  @ViewChild('startDate') startDate: SwtDateField;
  @ViewChild('endDate') endDate: SwtDateField;

  @ViewChild('dateSingleOrRange', { read: SwtRadioButtonGroup }) dateSingleOrRange: SwtRadioButtonGroup;
  @ViewChild('singleDate', { read: SwtRadioItem }) singleDate: SwtRadioItem;
  @ViewChild('dateRange', { read: SwtRadioItem }) dateRange: SwtRadioItem;
  @ViewChild('dateItem') dateItem: SwtRadioItem;

  private swtAlert: SwtAlert;
  private logger: Logger = null;
  private dateFormat: string;
  private dateFormatValue: string;
  public moduleId = "Predict";

  /**                                                                                       
			 * Display Objects                                                                        
			 **/
  private schedReportHistGrid: SwtCommonGrid;
  /**
   * Data Objects
   **/
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  // Variable to hold  menuAccessId                                                         
  private menuAccessId: string = null;
  private maintainAnyReportHistAccess = false;
  private fileId: string = null;
  private comboOpen = false;



  /* - START -- Screen Name and Version Number ---- */
  private screenName = 'Scheduled Report History';
  private versionNumber = '1.00.00';
  private releaseDate = '10 September 2019';
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private inputData = new HTTPComms(this.commonService);
  private checkAccess = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];
  private previousSelectedIndex = -1;
  private testDate: string;

  public logic: CommonLogic = new CommonLogic();
  public showJSONPopup: any;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    this.logger = new Logger('SchedReportHistory', commonService.httpclient);
    this.logger.info('method [constructor] - START/END');
    window['Main'] = this;
  }


  onLoad(): void {
    try {
      this.logger.info('method [onLoad] - START ');

      this.schedReportHistGrid = this.canvasGrid.addChild(SwtCommonGrid) as SwtCommonGrid;
      this.schedReportHistGrid.uniqueColumn = "fileId";
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      this.maintainAnyReportHistAccess = (ExternalInterface.call('eval', 'maintainAnyReportHistAccess') == 'true');

      this.dateFormat = ExternalInterface.call('eval', 'dateFormat');
      this.dateFormatValue = ExternalInterface.call('eval', 'dateFormatValue');
      this.testDate = ExternalInterface.call('eval', 'dbDate');
      this.startDate.formatString = this.dateFormatValue;
      this.endDate.formatString = this.dateFormatValue;
      this.schedReportHistGrid.onFilterChanged = this.disableButtons.bind(this);
      this.schedReportHistGrid.onSortChanged = this.disableButtons.bind(this);

      this.schedReportHistGrid.onRowClick = (event) => {
        this.cellLogic();
      };
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "schedReportHist.do?method=";
      this.actionMethod = "displaySchedReportHistList";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

    } catch (error) {
      this.logger.error('method [onLoad] - error ', error);

    }
  }

  /**
          * Part of a callback function to all for control of the loading swf from the HTTPComms Object
 **/
  private endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  /**
         * Part of a callback function to all for control of the loading swf from the HTTPComms Object
  **/
  private startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
 * If a fault occurs with the connection with the server then display the lost connection label
 **/
  private inputDataFault() {
    let message = SwtUtil.getPredictMessage('label.genericException', null);
    this.swtAlert.error(message);
  }

  /***
   * 
   * 
   */
  private inputDataResult(event): void {
    this.logger.info('method [inputDataResult] - START ');
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          if (this.jsonReader.getRequestReplyStatus()) {
            if (!this.jsonReader.isDataBuilding()) {

              // if (this.schedReportHistGrid == undefined) {

              let columnData = event.schedreporthist.grid.metadata;
              // Get datagrid column data 
              this.schedReportHistGrid.moduleId = this.moduleId;
              this.schedReportHistGrid.CustomGrid(columnData);
              this.schedReportHistGrid.gridData = this.jsonReader.getGridData();
              this.schedReportHistGrid.setRowSize = this.jsonReader.getRowSize();
              this.schedReportHistGrid.colWidthURL ( this.baseURL+"schedReportHist.do?&screenName=schedReportHist");
              this.schedReportHistGrid.colOrderURL ( this.baseURL+"schedReportHist.do?&screenName=schedReportHist");
              this.schedReportHistGrid.saveWidths = true;
              this.schedReportHistGrid.saveColumnOrder = true;


              this.reportJobsCombo.setComboData(this.jsonReader.getSelects(), false);
              this.reportTypesCombo.setComboData(this.jsonReader.getSelects(), false);

              this.startDate.text = this.testDate; // new Date(this.testDate, this.dateFormat.toUpperCase());
              this.endDate.text = this.testDate; // new Date(this.testDate);

              this.cellLogic();

              this.endDateLabel.visible =this.endDateLabel.includeInLayout = false;
              this.dateItem.visible = false;


            } else {
              this.swtAlert.error(SwtUtil.getPredictMessage('alert.generic_exception'));
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('method [inputDataResult] - error ', error);
    }
  }


  /**                                                                                                           
			 * This method is called by the HTTPComms when result event occurs.                                           
			 * @param event:ResultEvent                                                                                   
			 * */
  private inputDataRefresh(event): void {
    this.logger.info('method [inputDataRefresh] - START enterrr');
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          if (this.jsonReader.getRequestReplyStatus()) {
            if (!this.jsonReader.isDataBuilding()) {
              if (this.jsonReader.getSingletons().deleteResult != null && this.jsonReader.getSingletons().deleteResult == "SUCCESS") {
                this.swtAlert.show("File deleted succesfully");
              } else if (this.jsonReader.getSingletons().deleteResult != null && this.jsonReader.getSingletons().deleteResult == "NO_ACCESS") {
                this.swtAlert.invalid(SwtUtil.getPredictMessage('alert.schedreporthist.noAccessToFeature', null));
                this.maintainAnyReportHistAccess = false;
              }
              this.reportTypesCombo.setComboData(this.jsonReader.getSelects(), false);
              this.schedReportHistGrid.gridData = this.jsonReader.getGridData();
              this.schedReportHistGrid.setRowSize = this.jsonReader.getRowSize();
              if(this.schedReportHistGrid.selectedIndex === -1) {
                this.disableOrEnableButtons(false);
              } else {
                this.fileId = this.schedReportHistGrid.dataProvider[this.schedReportHistGrid.selectedIndex].fileId;
              }
              // keep the selected index of grid after refresh

              // this.cellLogic();

              // if(this.fileId != null ) {
              //   this.previousSelectedIndex = this.schedReportHistGrid.gridData.findIndex(x=> x.fileId == this.fileId);
              //   if(  this.previousSelectedIndex >=0) {
              //     this.schedReportHistGrid.selectedIndex =  this.previousSelectedIndex;
              //     this.fileId = this.schedReportHistGrid.dataProvider[this.previousSelectedIndex].fileId;
              //   } else {
              //     this.fileId = null;
              //     this.disableOrEnableButtons(false);
              //   }
              // } else {
              //   this.disableOrEnableButtons(false);
              // }

            } else {
              let message = SwtUtil.getPredictMessage('label.genericException', null);
              this.swtAlert.error(message);
            }
          }
          this.prevRecievedJSON = this.lastRecievedJSON;
        }
      }
    } catch (error) {
      this.logger.error('method [inputDataRefresh] - error ', error);
    }
  }

  /**                                                                                                                  
			 * Method used for call back, to refresh the grid data.                                                              
			 **/
  refreshDetails(event): void {
    this.logger.info('method [refreshDetails] - START ');
    let reportJobChanged = false;
    if (event != null && event.target.id == this.reportJobsCombo.id) {
      reportJobChanged = true;
    }

    let reportJobId: string = this.reportJobsCombo.selectedValue;
    let reportTypeId: string = this.reportTypesCombo.selectedValue;

    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);

    this.inputData.cbResult = (data) => {
      this.inputDataRefresh(data);
    };

    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "schedReportHist.do?method=";
    this.actionMethod = "displaySchedReportHistList";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.requestParams = [];
    this.requestParams["reportJobId"] = reportJobId;
    this.requestParams["reportTypeId"] = reportTypeId;
    this.requestParams["reportJobChanged"] = reportJobChanged;
    this.requestParams["fromDate"] = this.startDate.text;
    this.requestParams["toDate"] = this.endDate.text;
    this.requestParams["isDateRange"] = (this.dateSingleOrRange.selectedValue == "D");
    this.inputData.send(this.requestParams);
    this.previousSelectedIndex = this.schedReportHistGrid.selectedIndex;
  }


  /**                                                                                                                  
			 * Method used to open "SchedReportHist" screen to change or View                                                 
			 * an "SchedReportHist"                                                                                         
			 */
  public changeViewSchedReportHist(isChange: boolean): void {
    /* Url to load change SchedReportHist screen */
    this.actionMethod = "displaySchedReportHist";
    this.actionMethod = this.actionMethod + "&fileId=" + this.fileId;
    this.screenName = isChange ? "changeScreen" : "viewScreen";
    this.actionMethod = this.actionMethod + "&screenName=" + this.screenName;
    ExternalInterface.call("openChildWindow", this.actionMethod);
  }


  /**                                                                                                                  
   * Method to pop up delete confirmation to remove an account SchedReportHist                                         
   *
   */
  public deleteSchedReportHist(): void {
    this.swtAlert.confirm(SwtUtil.getPredictMessage('confirm.delete', null),
      SwtUtil.getPredictMessage('alert.deletion.confirm', null),
      SwtAlert.YES | SwtAlert.NO,
      null,
      this.deletionDecision.bind(this),
      null);
  }

  /**                                                                                                                  
			 * Method used to know if the decision to delete (or not) has been made                                              
			 * @param event: CloseEvent                                                                                          
			 */
  private deletionDecision(event): void {
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.inputDataRefresh(data);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionMethod = "deleteSchedReportHist";
    this.requestParams["fileId"] = this.fileId;
    /* Delete account attribute HDR and related records from                                                         
    functional group and account attribute */
    if (event.detail == SwtAlert.YES) {
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    }
  }

  /**                                                                                                                  
         * close the window from the close button                                                                            
         **/
  public closeHandler(): void {
    // call for close window                                                                                          
    ExternalInterface.call("close");
  }

  /**                                                                                                                  
			 * When item click on the datagrd is method will be called                                                           
			 *
			 **/
  private cellLogic(): void {
    this.logger.info('method [cellLogic] - START ');
    let selectedRow = this.schedReportHistGrid.selectedIndex;

    if (selectedRow > -1) {
      this.fileId = this.schedReportHistGrid.selectedItem.fileId.content;
      this.disableOrEnableButtons(true);

    } else {
      this.disableOrEnableButtons(false);
    }
    this.logger.info('method [cellLogic] - END ');
  }

  /**                                                                                                                  
   * Method to disable or enable buttons                                                                               
   * @param isRowSelected:Boolean
   */
  private disableOrEnableButtons(isRowSelected: boolean): void {

    this.logger.info('method [disableOrEnableButtons] - START ');
    let menuAccess: number = parseInt(this.menuAccessId, 10);
    let selectedRow = this.schedReportHistGrid.selectedIndex;

    if (isRowSelected) {
      let fileName: string = this.schedReportHistGrid.selectedItem.fileName.content;
      if (!StringUtils.isEmpty(fileName)) {
        this.resendMailButton.enabled = (menuAccess == 0 && this.maintainAnyReportHistAccess == true);
        this.downloadButton.enabled = (menuAccess < 2);
      } else {
        this.resendMailButton.enabled = false;
        this.downloadButton.enabled = false;
      }
      this.deleteButton.enabled = (menuAccess == 0 && this.maintainAnyReportHistAccess == true);
      this.viewButton.enabled = (menuAccess < 2);
    } else {
      this.deleteButton.enabled = false;
      this.viewButton.enabled = false;
      this.resendMailButton.enabled = false;
      this.downloadButton.enabled = false;
    }

    this.logger.info('method [disableOrEnableButtons] - END ');
  }


  disableButtons() {
    if(this.schedReportHistGrid.selectedIndex == -1) {
      this.disableOrEnableButtons(false);
    } else {
      this.disableOrEnableButtons(true);
    }
  }

  /**
   * This method is used to validate date field values
   * @param event : Event
   * 
   */
  public validateDateFieldValue(event): void {
    if(this.validateDateField(this.startDate) ) {
      if (this.dateSingleOrRange.selectedValue == "D") {
        this.checkDateRangeValueAndUpdateGrid();
      } else {
        this.refreshDetails(null);
      }
    } else {
      console.log('cccc');
    }
   /* if ((event.currentTarget as SwtDateField).selectedDate != null) {
      let value: string = String((event.currentTarget as SwtDateField).selectedDate);
      if (value.length > 0) {
        let isValidDate: Boolean = (this.logic.validateDate(event as FocusEvent, event.currentTarget as SwtDateField, (event.currentTarget as SwtDateField).selectedDate))
        if (!isValidDate) {
          (event.currentTarget as SwtDateField).selectedDate = null;
        } else {
          if (this.dateSingleOrRange.selectedValue == "D") {
            this.checkDateRangeValueAndUpdateGrid();
          } else {
            this.refreshDetails(null);
          }

        }
      }
    }*/
  }
  validateDateField(dateField) {
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('dashboardDetails.alert.dateFormat', null);
      if(dateField.text) {

        date = moment(dateField.text, this.dateFormatValue.toUpperCase() , true);

        if(!date.isValid()) {
          this.swtAlert.warning(alert+ this.dateFormatValue.toUpperCase());
          return false;
        }
      }
      dateField.selectedDate = date.toDate();
    } catch(error) {
    }

    return true;
  }

  /**
         * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
         * @param event:DropdownEvent
         **/
  public closedCombo(event): void {

    this.comboOpen = false;

    // if ((event.triggerEvent != null) && (event.triggerEvent.type == "mouseDownOutside")) {
    // if (event.currentTarget.id is SwtComboBox)
    // {
    //   if ((event.currentTarget as SwtComboBox).interruptComms) {
    //     (event.currentTarget as SwtComboBox).interruptComms = false;
    //   }
    // }
    // else if (event.currentTarget is SwtDateField)
    // {
    //   if ((event.currentTarget as SwtDateField).interruptComms) {
    //     (event.currentTarget as SwtDateField).interruptComms = false;
    //   }
    // }
    // }
  }

  /**
			 * Listener of the effective date radiobuttons when changing the value 
			 **/
  public dateTypeChanged(type: string): void {
    this.logger.info('method [dateTypeChanged] - START ');

    // If the effective date selected is not required, then no need to let the user choosing the effective time
    if (type == "singleDate") {
      this.startDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate, this.dateFormatValue .toUpperCase()));
      this.endDate.selectedDate = null;
      this.endDateLabel.visible = false;
      this.dateItem.visible = false;
      this.dateSingleOrRange.selectedValue = "S";
    } else if (type== "dateRange") {

      /*this.startDate.selectedDate = new Date(this.logic.testDate);
      this.endDate.selectedDate = new Date(this.logic.testDate);*/

      this.startDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate, this.dateFormatValue .toUpperCase()));
      this.endDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate, this.dateFormatValue .toUpperCase()));
      this.endDateLabel.visible = true;
      this.dateItem.visible = true;
      this.dateSingleOrRange.selectedValue = "D";
    }
    this.refreshDetails(event);
    this.logger.info('method [dateTypeChanged] - END ');
  }


  /**
         * This method is used to check the date range value
         * 
         */
  public checkDateRangeValueAndUpdateGrid(): void {
    let isValidRange = true;
    if (this.startDate.selectedDate != null && this.endDate.selectedDate != null) {
      isValidRange =  ExternalInterface.call("comparedates", this.startDate.text, this.endDate.text, this.dateFormat ,'Start Date', 'End Date');
    }

    if (isValidRange) {
      this.refreshDetails(null);
    }
  }

  checkDates() {
    try {
      let startDate: any;
      let endDate: any;
      let isOk= true;
      if (this.startDate.text) {
        startDate = moment(this.startDate.text, this.dateFormatValue.toUpperCase(), true);
      }
      if (this.endDate.text) {
        endDate = moment(this.endDate.text, this.dateFormatValue.toUpperCase(), true);
      }

      if (!startDate && endDate) {
        isOk = false;
      }

      if (startDate && endDate && endDate.isBefore(startDate)) {
        isOk = false;
      }

      return isOk;
    } catch (error) {
      // SwtUtil.logError(error, this.moduleId, "className", "changeEndDate", this.errorLocation);

    }
  }

  /**
   * 
   * @param event : MouseEvent
   */
  public resendMailButton_clickHandler(event: MouseEvent): void {
    this.checkAccess.cbStart = this.startOfComms.bind(this);
    this.checkAccess.cbStop = this.endOfComms.bind(this);
    this.checkAccess.cbResult = (data) => {
      this.resendMailDataResult(data);
    };
    this.checkAccess.cbFault = this.inputDataFault.bind(this);
    this.checkAccess.encodeURL = false;
    this.actionMethod = "checkFullAccessToTheScreen";
    this.checkAccess.url = this.baseURL + this.actionPath + this.actionMethod;
    this.checkAccess.send();
  }


  /**                                                                                                           
			 * This method is called by the HTTPComms when result event occurs.                                           
			 * @param event:ResultEvent                                                                                   
			 * */
  private resendMailDataResult(event): void {
    this.logger.info('method [resendMailDataResult] - START ');
    // Variable Number Error Location
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          if (this.jsonReader.getRequestReplyStatus()) {
            let selectedRow = this.schedReportHistGrid.selectedIndex;
            let scheduleId: string = this.schedReportHistGrid.selectedItem.scheduleId.content;
            let outputLocation: string = this.schedReportHistGrid.selectedItem.outputLocation.content;
            let fileName: string = this.schedReportHistGrid.selectedItem.fileName.content;
            let fileId: string = this.schedReportHistGrid.selectedItem.fileId.content;
            if (!StringUtils.isEmpty(outputLocation) && !StringUtils.isEmpty(fileName)) {
              ExternalInterface.call("openDistListScreen", scheduleId, Encryptor.encode64(outputLocation + "\\" + fileName), fileId);
            }
          } else {
            this.swtAlert.invalid(SwtUtil.getPredictMessage('alert.schedreporthist.noAccessToFeature', null));
            this.maintainAnyReportHistAccess = false;
          }
        }
      }
    } catch (error) {
      this.logger.error('method [resendMailDataResult] - error ', error);
    }
  }

  /**                                                                                                                  
         * Method downloadButton                                         
         * @param event: Event                                                                                          
         */
  public downloadButton_clickHandler(event): void {
    let requestParamForDownlaod = [];
    let selectedRow = this.schedReportHistGrid.selectedIndex;
    if (selectedRow > -1) {
      this.fileId = this.schedReportHistGrid.selectedItem.fileId.content;
      this.checkAccess.cbStart = this.startOfComms.bind(this);
      this.checkAccess.cbStop = this.endOfComms.bind(this);
      this.checkAccess.cbResult = (data) => {
        this.startDownloadAction(data);
      };
      this.checkAccess.cbFault = this.inputDataFault.bind(this);
      this.checkAccess.encodeURL = false;
      this.actionMethod = "checkIfFileExist";
      this.checkAccess.url = this.baseURL + this.actionPath + this.actionMethod;
      requestParamForDownlaod["fileId"] = this.fileId;
      this.checkAccess.send(requestParamForDownlaod);
    }
  }


  /**                                                                                                           
			 * This method is called by the HTTPComms when result event occurs.                                           
			 * @param event:ResultEvent                                                                                   
			 * */
  private startDownloadAction(event): void {
    this.logger.info('method [startDownloadAction] - START ');
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          if (this.jsonReader.getRequestReplyStatus()) {
            let selectedRow = this.schedReportHistGrid.selectedIndex;
            if (selectedRow > -1) {
              this.fileId = this.schedReportHistGrid.selectedItem.fileId.content;
              this.actionMethod = "downloadSchedReportHist";
              ExternalInterface.call("onExport", this.fileId, this.actionMethod);
            }

          } else {
            this.swtAlert.error(SwtUtil.getPredictMessage('alert.mail.fileNoutFound', null));
          }
        }
      }
    } catch (error) {
      this.logger.error('method [startDownloadAction] - error ', error);
    }
  }

  ngOnInit() {

    // Load the Screen information

    this.refreshButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refreshWindow', null);

    this.downloadButton.label = SwtUtil.getPredictMessage('button.schedreporthist.download', null);
    this.downloadButton.toolTip = SwtUtil.getPredictMessage('button.tooltip.schedreporthist.download', null);
    this.resendMailButton.label = SwtUtil.getPredictMessage('button.schedreporthist.resendMail', null);
    this.resendMailButton.toolTip = SwtUtil.getPredictMessage('button.schedreporthist.resendMail', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.details', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('button.tooltip.schedreporthist.details', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('tooltip.deleteSeletedFile', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.singleDate.label = SwtUtil.getPredictMessage('button.schedReportHist.singleDate', null);
    this.singleDate.toolTip = SwtUtil.getPredictMessage('button.tooltip.schedReportHist.singleDate', null);
    this.dateRange.label = SwtUtil.getPredictMessage('button.schedReportHist.dateRange', null);
    this.dateRange.toolTip = SwtUtil.getPredictMessage('button.tooltip.schedReportHist.dateRange', null);
    this.dateLabel.text = "Date";
    this.startDateLabel.text = "From";// SwtUtil.getPredictMessage('button.schedReportHist.stratDate', null);
    this.startDate.toolTip = SwtUtil.getPredictMessage('button.tooltip.schedReportHist.startDate', null);
    this.endDateLabel.text = "To"; // SwtUtil.getPredictMessage('button.schedReportHist.endDate', null);
    this.endDate.toolTip = SwtUtil.getPredictMessage('button.tooltip.schedReportHist.endDate', null);

    this.reportJobsCombo.toolTip = SwtUtil.getPredictMessage('button.tooltip.schedReportHist.reportjob', null);
    this.reportTypesCombo.toolTip = SwtUtil.getPredictMessage('button.tooltip.schedReportHist.reporttype', null);
    this.initializeMenus();

  }
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
    let addMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }
  showGridJSON(): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }

  doHelp() {
    ExternalInterface.call('help');
  }

}




// Define lazy loading routes
const routes: Routes = [
  { path: '', component: SchedReportHistory }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SchedReportHistory],
  entryComponents: []
})
export class SchedReportHistoryModule { }
