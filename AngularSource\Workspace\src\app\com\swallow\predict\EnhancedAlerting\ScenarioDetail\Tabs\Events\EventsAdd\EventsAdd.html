<SwtModule (creationComplete)="onLoad()"  width="100%" height="100%">
  <VBox width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingBottom="5" paddingRight="5">
    <SwtCanvas width="100%" height="95%">
    <VBox width="100%" minWidth="950" height="100%">
    <Grid width="100%" height="125" paddingTop="10">
      <GridRow >
        <GridItem width="140">
          <SwtLabel #scenarioIdLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput #scenarioIdtxt width="200" enabled="false"></SwtTextInput>
        </GridItem>
        <GridItem width="100%">
          <HBox width="100%" horizontalAlign="right">
          <SwtLabel #executeWhenLbl></SwtLabel>
          <SwtComboBox #excecuteCombo dataLabel="" width="350"></SwtComboBox>
          </HBox>
        </GridItem>
      </GridRow>
      <GridRow>
        <GridItem width="140">
          <SwtLabel #eventSeqLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput #eventSeqTxt width="200" enabled="false" textAlign="right"></SwtTextInput>
        </GridItem>
        <GridItem width="100%">
          <HBox width="100%" horizontalAlign="right">
          <SwtLabel #allowRepLbl></SwtLabel>
          <SwtCheckBox #allowRepeatCheck></SwtCheckBox>
          </HBox>
        </GridItem>
      </GridRow>
      <GridRow>
        <GridItem width="140">
          <SwtLabel #eventFacilityLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtComboBox #eventFacilityCombo  (change)="changeEventFacility()"></SwtComboBox>
        </GridItem>
        <GridItem>
          <SwtLabel #selectedEventFacility fontWeight="normal"></SwtLabel>
        </GridItem>
      </GridRow>

      <GridRow>
        <GridItem width="140">
          <SwtLabel #eventFacilityDescLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput #eventFacilityDescTxt width="380"></SwtTextInput>
        </GridItem>
      </GridRow>
    </Grid>
    <SwtCanvas #msgCanvas width="100%" height="90%">
    <Grid #msgGrid width="100%" height="100%" paddingTop="10">
      <GridRow width="100%" height="10px"></GridRow>
      <GridRow width="100%" height="95%">
        <GridItem width="80" paddingLeft="20">
          <SwtLabel #msgComboLbl id="msgComboLbl" text='Format'></SwtLabel>
        </GridItem>
        <GridItem width="350">
          <SwtComboBox #msgCombo id="msgCombo" width="300"  dataLabel="msgFormatList" (change)="changeMsgFormat()"></SwtComboBox>
        </GridItem>
        <GridItem width="30%">
          <SwtLabel #msgLabel id="msgLabel" width="300"></SwtLabel>
        </GridItem>
      </GridRow>
      <GridRow width="100%" height="7%">
        <GridItem width="90%">
        </GridItem>
        <GridItem width="10%">
          <SwtButton #addFormatButton id="addFormatButton" (click)="addMsgFormat()"></SwtButton>
        </GridItem>
      </GridRow>
    </Grid>
  </SwtCanvas> 
    <SwtCanvas #emailCanvas width="100%" height="100%">
    <Grid #emailGrid width="100%" height="100%" paddingTop="10">

      <GridRow width="100%" height="40">
        <GridItem width="105" >
          <SwtLabel #emailFormatLbl id="emailFormatLbl"></SwtLabel>
        </GridItem>
        <GridItem width="350">
          <SwtComboBox #emailFormatCombo id="emailFormatCombo" width="300"  dataLabel="emailTemplatesList" (change)="changeEmailFormat()"></SwtComboBox>
        </GridItem>
        <GridItem>
          <SwtLabel #emailFormatDescLbl id="emailFormatDescLbl" width="300"></SwtLabel>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="40">
        <GridItem width="105">
        </GridItem>
        <GridItem>
          <SwtButton #configureButton id="configureButton" (click)="configureRecipient()"></SwtButton>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="40%" paddingTop="10">
        <SwtLabel #usersLbl id="usersLbl" width="120"></SwtLabel>
        <SwtCanvas #usersGridContainer id="usersGridContainer" styleName="canvasWithGreyBorder" minHeight="80" width="100%" height="100%"
          border="false"></SwtCanvas>
      </GridRow>

      <GridRow width="100%" height="40%" paddingTop="10">
        <SwtLabel #rolesLbl id="rolesLbl" width="120"></SwtLabel>
        <SwtCanvas #rolesGridContainer id="rolesGridContainer" styleName="canvasWithGreyBorder" minHeight="80" width="100%" height="100%"
          border="false"></SwtCanvas>
      </GridRow>

<!--       <GridRow width="100%" height="2%" paddingTop="10">
        <SwtLabel #emailLbl id="emailLbl" width="120"></SwtLabel>
        <SwtCanvas #otherEmailGridContainer id="otherEmailGridContainer" styleName="canvasWithGreyBorder"minHeight="80"  width="100%" height="100%"
          border="false"></SwtCanvas>
      </GridRow> -->

    </Grid>
   </SwtCanvas>   
    <SwtCanvas #subEventCanvas minHeight="120" width="100%" height="40%"></SwtCanvas>
    <Grid #subEventGrid  width="100%" height="150" paddingTop="5">
      <GridRow height="25">
        <GridItem width="140">
          <SwtLabel #parameterIdLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtText #parameterIdTxt></SwtText>
        </GridItem>
      </GridRow>
      <GridRow height="25">
        <GridItem width="140">
          <SwtLabel #desLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtText #descTxt></SwtText>
        </GridItem>
      </GridRow>
      <GridRow height="25">
        <GridItem width="140">
          <SwtLabel #mapFromLbl></SwtLabel>
        </GridItem>
        <GridItem width="400">
          <SwtRadioButtonGroup #mapFrom id="mapFrom"  align="horizontal" width="100%" (change)="changeValueComponent()">
            <SwtRadioItem value="A" width="140" groupName="mapFrom" id="instAttr" #instAttr selected="true">
            </SwtRadioItem>
            <SwtRadioItem value="L" width="70" groupName="mapFrom" id="literal" #literal>
            </SwtRadioItem>
            <SwtRadioItem value="I" width="80" groupName="mapFrom" id="ignore" #ignore>
            </SwtRadioItem>
            <SwtRadioItem value="N" width="60" groupName="mapFrom" id="null" #null>       
            </SwtRadioItem>
          </SwtRadioButtonGroup>
        </GridItem>
      </GridRow>

      
      <GridRow height="40">
        <GridItem width="140">
          <SwtLabel #infoLbl></SwtLabel>
        </GridItem>
        <GridItem width="100%" height="100%">
          <SwtLabel id='infoText' #infoText height='100%' width="100%" fontWeight="normal"></SwtLabel>
        </GridItem>
      </GridRow>

      <GridRow height="25" paddingTop="5">
        <GridItem width="140">
          <SwtLabel #valueLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtComboBox #valueCombo width="200" id="valueCombo" shiftUp="180" enabled="false"></SwtComboBox>
        </GridItem>
        <GridItem>
          <SwtTextInput #valueTxt width="200" id="valueTxt" enabled="false"></SwtTextInput>
        </GridItem>
      </GridRow>

      <GridRow height="30" paddingTop="10">
        <GridItem width="140">

        </GridItem>
        <GridItem>
          <SwtButton #updateButton (click)="updateHandle()"  enabled="false"></SwtButton>
        </GridItem>
      </GridRow>
    </Grid>
      </VBox>
    </SwtCanvas>
    <SwtCanvas width="100%" height="35">
      <HBox width="100%" horizontalGap="5">
        <SwtButton #okButton (click)="saveHandler()"></SwtButton>
        <SwtButton #cancelButton (click)="cancelHandler()"></SwtButton>
        <SwtButton #showXMLButton (click)="showXmlHandler()" enabled="false"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
