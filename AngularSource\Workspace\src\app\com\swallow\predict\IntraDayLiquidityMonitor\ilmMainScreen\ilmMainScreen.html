<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox  width="100%" height="100%" paddingBottom="5" paddingRight="5" paddingTop="5" paddingLeft="5">
    <SwtCanvas minWidth="1000" width="100%" height="35">
      <Grid width="100%" height="100%">
        <GridRow width="100%" height="100%">
          <GridItem width="40%" paddingLeft="5">
          <GridItem width="50%">
            <SwtLabel #entityLabel width="100"></SwtLabel>
            <SwtComboBox #entityCombo dataLabel="entityList" width="120" (change)="updateData('entityCombo');updateILMConf('lastSelectedEntity',null);"></SwtComboBox>
          </GridItem>
          <GridItem width="50%">
            <SwtLabel #ccyLabel width="100"></SwtLabel>
            <SwtComboBox #currencyCombo dataLabel="currencyList" width="120" (change)="updateData('ccyCombo');updateILMConf('lastSelectedCcy',null);"></SwtComboBox>
          </GridItem>
          </GridItem>
          <GridItem width="60%">
            <VBox width="100%">
              <HBox horizontalAlign="right" horizontalGap="20">
                <GridItem>
                <SwtCheckBox class="withoutMargin" #ccyMuliplierCB id="ccyMuliplierCB" paddingTop="1" selected="true"
                             (change)="updateILMConf('ccyMuliplierCB',null);updateValuesUsingCcyMultiplier($event);updateData('refreshButton');">
                </SwtCheckBox>
            
                <SwtLabel #ccyMultiplierLabel fontWeight="normal" paddingTop="1"></SwtLabel>
              </GridItem>
                <GridItem>
                  <SwtCheckBox class="withoutMargin" id="refreshCB" #refreshCB selected="true"
                               (change)="updateILMConf('refreshCB', null)">
                  </SwtCheckBox>
                </GridItem>
                <GridItem>
                  <SwtTextInput id="refreshText" #refreshText width="35" height="20"
                                (change)="refValueChanged = true"
                                editable="true" maxChars="3" restrict="0-9" [enabled]="refreshCB.selected"
                                (focusOut)="updateILMConf('refreshText', null)">
                  </SwtTextInput>
                </GridItem>
                <GridItem>
                  <SwtLabel width="30" text="mins" fontWeight="normal">
                  </SwtLabel>
                </GridItem>
                  <GridItem>
                    <SwtButton buttonMode="true" (click)="updateData('refreshButton')" #refreshButton
                               id="refreshButton">
                    </SwtButton>
                    <SwtLoadingImage #loadingImage></SwtLoadingImage>
                  </GridItem>


              </HBox>
              <HBox horizontalAlign="right">
                <!-- <SwtCheckBox class="withoutMargin" #ccyMuliplierCB id="ccyMuliplierCB" paddingTop="1" selected="true"
                             (change)="updateValuesUsingCcyMultiplier($event);updateILMConf('ccyMuliplierCB',null);">
                </SwtCheckBox>
                <SwtLabel #ccyMultiplierLabel fontWeight="normal" paddingTop="1"></SwtLabel> -->
              </HBox>
            </VBox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <!-- <SwtCanvas width="100%" height="90%"> -->
      <SwtTabNavigator #tabs id="tabs" minWidth="1000" applyOrder="true"  height="100%" showDropDown="true" (onChange)="tabChanged()" borderBottom="false" width="100%">
        <SwtTabPushStrategy width="100%"  height="100%" order="1" #summaryTab id="summaryTab" label="Summary">
        <VBox width="100%" minWidth="1000" height="100%">
            <!-- <VBox width="100%" height="40" verticalGap="0" paddingRight="10"> -->
              <HBox height="40" width="100%"  >
                <HBox horizontalAlign="left" height="40" width="50%"   paddingLeft="20">
                  <SwtLabel textDictionaryId="ilmSummary.label.currentDateSummary" fontWeight="bold" width="200"></SwtLabel>
                </HBox>
                <HBox horizontalAlign="right" height="40" width="50%"  horizontalGap="20" paddingRight="20">
                  <SwtCheckBox id="includeSod" #includeSod
                  label="Include SOD" (change)="updateData($event);updateILMConf('includeSod',null);"></SwtCheckBox>
                  <VBox height="100%" >
                    <SwtCheckBox height="50%" id="includecreditLine" #includecreditLine
                    label="Include Credit Line" (change)="updateData($event);updateILMConf('includecreditLine',null);"></SwtCheckBox>
                    <SwtCheckBox id="includeOpenMvmt" height="50%"  #includeOpenMvmt
                    label="Include Open Movements" (change)="updateData($event);updateILMConf('includeOpenMvmt',null);"></SwtCheckBox>
  
                  </VBox>
                  <VBox height="100%" >
                    <SwtCheckBox id="sumCutOff" #sumCutOff height="50%" 
                    label="Sum by Cut-off" (change)="updateData($event);updateILMConf('sumCutOff',null);"></SwtCheckBox>
                    <SwtCheckBox id="hideNonSumAccount" #hideNonSumAccount height="50%" 
                    label="Hide Non-sum Accounts" (change)="updateData($event);updateILMConf('hideNonSumAccount',null);"></SwtCheckBox>
  
                  </VBox>
               
                 
                </HBox>
              </HBox>

            <!-- </VBox> -->
            <VBox width="100%" height="100%" minWidth="1000" verticalGap="0">
              <SwtCanvas #summaryCanvas  border="false" width="100%" height="100%"></SwtCanvas>
              <SwtCanvas id="totalsContainer" border="false"  width="100%" height="40" #totalsContainer >
              </SwtCanvas>
            </VBox>
          <SwtCanvas width="100%" height="40">
            <HBox width="100%" height="100%">
              <SwtButton #optionButton (click)="optionHandler()"></SwtButton>
              <HBox horizontalAlign="right" width="100%">
                <DataExport #exportContainer id="exportContainerSummary"></DataExport>
                <SwtButton #closeButton (click)="closeHandler()"></SwtButton>
              </HBox>
            </HBox>
          </SwtCanvas>
        </VBox>
        </SwtTabPushStrategy>
      </SwtTabNavigator>

    <!-- </SwtCanvas> -->


  </VBox>
</SwtModule>
