<SwtModule width="100%" height="100%" (creationComplete)="onLoad()">
  <VBox paddingLeft="5" paddingRight="5" paddingTop="5" paddingBottom="5" width="100%" height="100%">
    <SwtFieldSet id="processFieldSet" #processFieldSetss style="height: 100%; width: 100%; color:blue;">
      <VBox paddingLeft="5" paddingRight="5" paddingTop="5" paddingBottom="5" width="100%" height="100%">
        <HBox width="100%">
          <HBox paddingLeft="5" width="90%">
            <SwtLabel id="statusLbl" #statusLbl> Status: </SwtLabel>
            <SwtLabel id="statusDesc" #statusDesc [text]="statusText"></SwtLabel>
          </HBox>
          <HBox width="10%" horizontalAlign="right" paddingLeft="5">
            <SwtLabel id="mvtProcessed" #mvtProcessed [text]="progressText"></SwtLabel>
          </HBox>
        </HBox>

        <!-- Progress Bar -->
        <div class="progress-container" [hidden]="isProcessCompleted">
          <div class="progress-bar" [style.width.%]="progress"></div>
        </div>

        <!-- Processing Steps Log -->
        <SwtTextArea id="summaryTxt" #summaryTxt editable="false" height="100%" width="100%">
        </SwtTextArea>

        <HBox paddingTop="10" width="100%">
          <HBox width="90%">
            <SwtButton [buttonMode]="true" id="cancelButton" #cancelButton [hidden]="isProcessCompleted" (click)="cancel()">
              
            </SwtButton>
            <SwtButton [buttonMode]="true" label="Report" id="generateReportButton" #generateReportButton [hidden]="!isProcessCompleted" (click)="generateReport()">
              
            </SwtButton>
          </HBox>
          <HBox width="10%" horizontalAlign="right" paddingLeft="5">
            <SwtButton [buttonMode]="true" id="closeExistButton" #closeExistButton [hidden]="!isProcessCompleted" (click)="closeAndExit()">
            </SwtButton>
            <SwtButton [buttonMode]="true" id="closeReturnButton" #closeReturnButton [hidden]="!isProcessCompleted" (click)="closeAndReturn()">
            </SwtButton>
          </HBox>
        </HBox>
      </VBox>
    </SwtFieldSet>
  </VBox>
</SwtModule>