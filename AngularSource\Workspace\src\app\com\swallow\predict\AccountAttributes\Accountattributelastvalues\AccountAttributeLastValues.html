<SwtModule (creationComplete)='onLoad()'  width="100%" height="100%">
  <VBox width="100%" height="100%" paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" height="15%">
      <HBox width='100%'  height="100%">
        <Grid width="100%" height="100%" verticalGap="6">
          <GridRow width="100%" height="50%" paddingLeft="10" paddingTop="10">
            <GridItem width="10%" height="100%">
              <SwtLabel styleName="labelBold"  #entityLabel
                       text='Entity'>
              </SwtLabel>
            </GridItem>
            <GridItem width="70%" height="100%">
              <SwtTextInput   #entityIdField id="entityIdField" editable="false">
              </SwtTextInput>
              <SwtLabel  #entityNameLabel id="entityNameLabel" width="100%"  paddingLeft="10" fontWeight="normal" styleName="labelLeft">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow width="100%" height="50%" paddingLeft="10">
            <GridItem width="10%" height="100%">
              <SwtLabel #accountIdLabel  styleName="labelBold"
                       text="Account">
              </SwtLabel>
            </GridItem>
            <GridItem width="70%" height="100%">
              <SwtTextInput #accountIdField id="accountIdField" width="220" editable="false"
                           toolTip="{accountId_field.text}">
              </SwtTextInput>
              <SwtLabel  #accountNameLabel id="accountNameLabel" width="100%" paddingLeft="10" fontWeight="normal" styleName="labelLeft">
              </SwtLabel>
            </GridItem>
          </GridRow>
        </Grid>
      </HBox>
    </SwtCanvas>
    <SwtCanvas id="canvasGrid" #canvasGrid  width="100%" height="78%">
    </SwtCanvas>
    <SwtCanvas width="100%" id="canvasButtons" height="7%" >
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true"
                     id="displayButton"
                     enabled="false"
                     #displayButton
                     (click)="displayButtonClickHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                     id="closeButton"
                     #closeButton
                     (click)="closeHandler()">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="5" paddingTop="5">
          <SwtHelpButton id="helpIcon"
                         [buttonMode]="true"
                         enabled="true"
                         helpFile="groups-of-rules"
                         (click)="doHelp()">
          </SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
