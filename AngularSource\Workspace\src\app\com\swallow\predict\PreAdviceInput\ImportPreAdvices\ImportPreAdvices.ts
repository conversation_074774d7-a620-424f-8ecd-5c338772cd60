import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Alert, CommonService, ExternalInterface, HTTPComms, JSONReader, StringUtils, SwtAlert, SwtButton, SwtCanvas, SwtCheckBox, SwtCommonGrid, SwtFieldSet, SwtLabel, SwtLoadingImage, SwtModule, SwtRadioButtonGroup, SwtRadioItem, SwtToolBoxModule, SwtTotalCommonGrid, SwtUtil, SwtComboBox, SwtImage, CommonUtil } from 'swt-tool-box';
import * as XLSX from 'xlsx'
declare var require: any;
const $ = require('jquery');
declare function checkCurrencyPlaces(strField, strPat, currCode): any;
declare function checkCurrencyPlacesFromNumber(strVal, strPat, currCode): any;
@Component({
  selector: 'app-import-pre-advices',
  templateUrl: './ImportPreAdvices.html',
  styleUrls: ['./ImportPreAdvices.css'],
  encapsulation: ViewEncapsulation.None
})
export class ImportPreAdvices extends SwtModule implements OnInit {
  /***********SwtCanvas***********/
  @ViewChild('headersGridContainer') headersGridContainer: SwtCanvas;
  @ViewChild('sourceFormatGridContainer') sourceFormatGridContainer: SwtCanvas;
  @ViewChild('mvtGridContainer') mvtGridContainer: SwtCanvas;

  /***********SwtLabel***********/
  @ViewChild('dateSource') dateSource: SwtLabel;
  @ViewChild('fileName') fileName: SwtLabel;
  @ViewChild('dateSourceCombo') dateSourceCombo: SwtComboBox;
  @ViewChild('headerLabel') headerLabel: SwtLabel;
  @ViewChild('headerValue') headerValue: SwtLabel;
  @ViewChild('headerType') headerType: SwtLabel;
  @ViewChild('sourceFormat') sourceFormat: SwtLabel;
  @ViewChild('importStatusLbl') importStatusLbl: SwtLabel;
  @ViewChild('importStatusTxt') importStatusTxt: SwtLabel;
  @ViewChild('showLabel') showLabel: SwtLabel;


  /***********SwtCheckBox***********/
  @ViewChild('chkHeader') chkHeader: SwtCheckBox;

  /***********SwtFieldSet***********/
  @ViewChild('fieldSet1') fieldSet1: SwtFieldSet;
  @ViewChild('fieldSet2') fieldSet2: SwtFieldSet;


  /***********SwtRadioButtonGroup***********/
  @ViewChild('showOptions') showOptions: SwtRadioButtonGroup;
  @ViewChild('allRows') allRows: SwtRadioItem;
  @ViewChild('validRows') validRows: SwtRadioItem;
  @ViewChild('invalidRows') invalidRows: SwtRadioItem;

  /***********SwtButton***********/
  @ViewChild('loadButton') loadButton: SwtButton;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('resetButton') resetButton: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('uploadImage') uploadImage: SwtImage;
  /***Module****/
  @ViewChild('swtModule') swtModule: SwtModule;

  private menuAccessId;
  private swtAlert: SwtAlert;
  private ordertData = new HTTPComms(this.commonService);
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;

  /**
   * Variables to hold the grid
   **/
  private headersGrid: SwtCommonGrid; 
  private sourceGrid: SwtTotalCommonGrid;
  private mvtGrid: SwtCommonGrid;

  /***variable to hold excel data ***/
  //private data;
  private named = 'Paste it';
  private importedColumns: string[] = [];
  private defaultColumns: string[] = [];
  // Create table dataSource
  private data = [];
  private excelData = [];
  private editedRow= [];
  private initialdata = [];
  private pastedData = [];
  private gridRows = [];
  private validRow =[];
  private inValidRow =[];

  /***variable to hold module id ***/
  public moduleId = "Predict";

  private clipboadDate = "";
  private callParent = false;
  private headerGridCol;
  private errorMsg = [];
  private finalMsg: string ="";
  private currencyAccess = false;
  private accountAccess = false;
  private metadataHeaderGrid;
  private isSourceFormat=false;
  private isPreviewGrid=false;
  private editableFlag=false;
  private headerGridRows;
  private sourceGridRows;
  private provider: string ="";
  private selectValues;
  private selectedId;
  private selectedItem;
  private sendFlag = true;
  private savedSetting = [];
  private  newOrderColumns = [];
  private columnDefinitionsTempArray = [];
  private columnOrderChanged =false;
  private options = [];
  private rowIndex;
  private dataField;
  private oldComboVal;
  private newComboVal;
  private orderChangedRows= [];
  private clipboardData;
  private pastedText;
  private pasteFlag =false;
  private newHeaderGridData=[];
  private headerChanged=false;
  private updatedHeader=[];
  private resetFlag=false;
  private headerIsSaved;
  private dataSourceType;
  private srcGridChanged=false;
  private stillCheckingFlag=false;
  private fromUpdateFlag = false;
  private uploadedFileName = "";
  private uploadEvent;
  public fileType;
  private excelValFlag=true;
  private importedFileHeaders = [];
  public sourceHeaderProvider = [{ type: "", value: "", selected: 0, content: "" }];
  private ScreenColumns=["Entity_ID", "Ccy", "Account_ID", "Amount", "Value_Date", "Reference", "Sign", "Pred_Status",
  "Book_Code", "Post_Date", "Product_Type", "CounterParty_ID", "Cparty_Text", "Match_Party"];
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }


  ngOnInit() {
    this.headersGrid = <SwtCommonGrid>this.headersGridContainer.addChild(SwtCommonGrid);
    this.sourceGrid = <SwtTotalCommonGrid>this.sourceFormatGridContainer.addChild(SwtTotalCommonGrid);
    this.mvtGrid = <SwtCommonGrid>this.mvtGridContainer.addChild(SwtCommonGrid);
    this.headersGrid.hideHorizontalScrollBar = true;
    this.headersGrid.hideVerticalScrollBar =true;
    this.headersGrid.listenHorizontalScrollEvent = true;
    this.headersGrid.editable=true;
    this.sourceGrid.fireHorizontalScrollEvent = true;
    this.sourceGrid.forceNoAlignRightTotalGrid=true;
    this.sourceGrid.editable = true;
    this.sourceGrid.selectable=false
    this.mvtGrid.editable = true;
    this.dateSource.text = SwtUtil.getPredictMessage('preAdviceInput.dataSource', null);
    this.headerLabel.text = SwtUtil.getPredictMessage('preAdviceInput.headerLabel', null);
    this.headerValue.text = SwtUtil.getPredictMessage('preAdviceInput.headerValue', null);
    this.headerType.text = SwtUtil.getPredictMessage('preAdviceInput.headerType', null);
    this.sourceFormat.text = SwtUtil.getPredictMessage('preAdviceInput.sourceFormat', null);
    this.importStatusLbl.text = SwtUtil.getPredictMessage('preAdviceInput.importStatusLbl', null);
    this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importInitStatus', null);
    this.showLabel.text = SwtUtil.getPredictMessage('preAdviceInput.showLabel', null);

    this.fieldSet1.legendText = SwtUtil.getPredictMessage('preAdviceInput.fieldSetImport1.legendText', null);
    this.fieldSet2.legendText = SwtUtil.getPredictMessage('preAdviceInput.fieldSetImport2.legendText', null);

    this.chkHeader.label = SwtUtil.getPredictMessage('preAdviceInput.chkHeader', null);

    this.allRows.label = SwtUtil.getPredictMessage('preAdviceInput.allRows', null);
    this.validRows.label = SwtUtil.getPredictMessage('preAdviceInput.validRows', null);
    this.invalidRows.label = SwtUtil.getPredictMessage('preAdviceInput.invalidRows', null);
    this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    this.saveButton.toolTip = SwtUtil.getPredictMessage('tooltip.SaveChanges', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.resetButton.label = SwtUtil.getPredictMessage('button.reset', null);
    this.resetButton.toolTip = SwtUtil.getPredictMessage('tooltip.reset', null);
    this.loadButton.label = SwtUtil.getPredictMessage('button.load', null);
    this.loadButton.toolTip = SwtUtil.getPredictMessage('tooltip.load', null);
    this.uploadImage.toolTip= SwtUtil.getPredictMessage('tooltip.chooseFile', null);

  }

  onLoad() {
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "preadviceinput.do?";
    this.actionMethod = 'method=displayDataDef';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['draggable'] = !this.chkHeader.selected;
    this.requestParams['fromMethod'] = "onLoad";
    this.requestParams['dataType'] = this.dateSourceCombo.selectedValue;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    this.mvtGrid.ITEM_CHANGED.subscribe((item) => {
      this.onGridCellClick(item);
    });

    //added to avoid removing mandatory columns by right click on grid header
    this.headersGrid.mandatoryColumns="Entity_ID,Ccy,Account_ID,Amount,Value_Date,Sign,Pred_Status";

    this.headersGrid.columnWidthChanged.subscribe((event) => {
      this.resizeGrids();
    });

    this.headersGrid.columnOrderChanged.subscribe((event) => {
      //if (!this.chkHeader.selected) {
        //check first if mvt grid is empty or not to avoid clearing grid when is not
        if (this.mvtGrid.gridData.length > 0) {
          this.orderChangedRows = this.mvtGrid.gridData;
        }
        this.columnOrderChange(event);
      //}
    });

    this.sourceGrid.ITEM_CHANGED.subscribe((item) => {
      this.confirmValidation(item);
    });
    this.headersGrid.ITEM_CHANGED.subscribe((item) => {
      this.saveColumnsHeaders();
    });

    this.headersGrid.enableDisableCells=(row) => {
      return this.enableDisableRow(row);
    };

// added to avoid not displaying data in the grid after validation (server side issue)
    var timeout;
    document.onmousemove = function (event) {
      clearTimeout(timeout);
      timeout = setTimeout(function () {
        $(document).trigger('onmousemove');

      }, 60);
}

  }


  resizeGrids() {
    try {
      this.sourceGrid.setRefreshColumnWidths(this.headersGrid.gridObj.getColumns());
      this.mvtGrid.setRefreshColumnWidths(this.headersGrid.gridObj.getColumns());

    } catch(e) {
      console.log("resizeGrids", e)
    }

  }

  private enableDisableRow(row: any): boolean {
    if (row.id == "1")
      return false;
    else return true;
  }

  saveColumnsHeaders(){
    this.newHeaderGridData=[];
    this.updatedHeader=[];
    this.headerChanged=true;
    let colValues=this.headersGrid.gridData[0];
    for (let key in colValues) {
      if (typeof colValues[key] == "string") {
      this.updatedHeader.push(colValues[key])
      }
    }
    //this.defaultColumns = this.updatedHeader;
    this.newHeaderGridData=this.headersGrid.gridData;
  }


  confirmValidation(event) {
    this.rowIndex = event.rowIndex;
    this.dataField = event.dataField;
    this.oldComboVal = event.listData.oldValue;
    this.newComboVal = event.listData.newValue;
    //check value if user try to enter it manually
    this.getComboValues();
    if (this.options.includes(event.listData.newValue)) {
      this.srcGridChanged=true;
      //raise alert only when mvt grid is not empty else it is not required
      if (this.mvtGrid.gridData.length > 0) {
        Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
        Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
        var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('preAdviceInput.validateAgain', null)));
        this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.validateRowsAgain.bind(this));
      }
    } else {
      this.sourceGrid.dataProvider[event.rowIndex].slickgrid_rowcontent[event.dataField].content = event.listData.oldValue;
      this.sourceGrid.dataProvider[event.rowIndex][event.dataField] = event.listData.oldValue;
    }
  }

  getComboValues() {
    let select = this.selectValues.select;
    for (let i = 0; i < select.length; i++) {
      for (let j = 0; j < select[i].option.length; j++) {
        if (this.options.indexOf(select[i].option[j].content) === -1) {
          this.options.push(select[i].option[j].content);
        }
      }

    }
  }

  validateRowsAgain(event){
    if (event.detail == Alert.YES) {
    if (this.dateSourceCombo.selectedLabel=="Excel file"){
      this.excelValFlag=false;
      this.pastedText="";
      const wb: XLSX.WorkBook = XLSX.read(this.binarystrGlobal,{  type: 'binary', raw: true,cellNF:true });
      /* selected the first sheet */
      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];
      /* save data */
      if(this.chkHeader.selected){
      this.excelData = XLSX.utils.sheet_to_json(ws, {raw: true});
      }else{
      this.excelData = XLSX.utils.sheet_to_json(ws,{raw: true, header: this.defaultColumns}); 
      }
      this.prepareExcelData(this.excelData,this.importedFileHeaders);
    }else{
    //revalidate all pasted rows and changes will be lost
    this.getAccess(this.pastedData, "save");
    }
    }else{
      this.sourceGrid.gridData[0][this.dataField] = this.oldComboVal;
      this.sourceGrid.refresh();
    }  
  }

  columnOrderChange(event): void {
    this.columnOrderChanged=true;
    this.newOrderColumns = [];
    this.columnDefinitionsTempArray = event;
    for (let i = 0; i < this.columnDefinitionsTempArray.length; i++) {
      if (this.columnDefinitionsTempArray[i].id != "dummy") {
        this.newOrderColumns.push(this.columnDefinitionsTempArray[i].field);
      }
    }
    this.populateSourceGrid(this.newOrderColumns);
    this.updateBottomGridcol(this.newOrderColumns);
    //added to fix issue when dragging columns and mvt grid is not empty
      let size = this.orderChangedRows ? this.orderChangedRows.length : 0;
      let row = this.orderChangedRows ? this.orderChangedRows : [];
      this.mvtGrid.gridData = { size: size, row: row };
      this.defaultColumns = this.newOrderColumns;
  }

  inputDataResult(event): void {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {

          if (!this.jsonReader.isDataBuilding()) {
            //set data source combo options
            this.dateSourceCombo.setComboData(this.jsonReader.getSelects());
            //set showOptions to all/clear clipboardAt
            this.allRows.selected=true;
            this.columnOrderChanged=false;
            this.headerChanged=false;
            this.srcGridChanged=false;
            this.headerIsSaved=this.jsonReader.getSingletons().headerIsSaved;
            let headerStatus=this.jsonReader.getSingletons().headerChecked;
            this.dataSourceType= this.jsonReader.getSingletons().dataSourceType;
            if(this.dataSourceType){
            this.dateSourceCombo.selectedValue= this.dataSourceType;
            }
            if(!this.fromUpdateFlag){
            this.chkHeader.selected=headerStatus ? headerStatus:true;            
            }
            //this.clipboardAt.text = SwtUtil.getPredictMessage('preAdviceInput.clipboardAt', null);
            /****header grid****/
            //to refresh header when clicking on reset button
            this.headersGrid.forceHeaderRefresh = true;
            //to refresh header width when changing headerGrid columns then check/uncheck top checkBox
            this.mvtGrid.forceHeaderRefresh=true;
            const obj = { columns: this.lastRecievedJSON.dataDefinition.HeadersGrid.metadata.columns };
            this.headerGridCol=this.lastRecievedJSON.dataDefinition.HeadersGrid.metadata.columns.column;
            this.headersGrid.CustomGrid(obj);
            this.headerGridRows = this.lastRecievedJSON.dataDefinition.HeadersGrid.rows;
            if (this.headerGridRows.size > 0) {
              this.headersGrid.gridData = this.headerGridRows;
              this.headersGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              this.headersGrid.gridData = { size: 0, row: [] };
            }
            /****source format grid****/
            const obj1 = { columns: this.lastRecievedJSON.dataDefinition.SourceFormatGrid.metadata.columns };
            this.selectValues=this.lastRecievedJSON.dataDefinition.selects;
            this.sourceGrid.gridComboDataProviders(this.selectValues);
            this.sourceGrid.CustomGrid(obj1);
            this.sourceGridRows = this.lastRecievedJSON.dataDefinition.SourceFormatGrid.rows;

            if (this.sourceGridRows.size > 0) {
              this.sourceGrid.gridData = this.sourceGridRows;
              this.sourceGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              this.sourceGrid.gridData = { size: 0, row: [] };
            }
            /****bottom grid****/
            const obj2 = { columns: this.lastRecievedJSON.dataDefinition.DataPreviewGrid.metadata.columns };
            this.mvtGrid.CustomGrid(obj2);
            this.mvtGrid.gridData= { size: 0, row: [] };
            this.validRow = [];
            this.inValidRow = [];
            this.gridRows= [];
            //get default header --> to be used when pasting data without header
            this.getDefaultHeader();
            this.prevRecievedJSON = this.lastRecievedJSON;
            this.resetFlag=false;
          }
        }
        //set button label according to data source
        this.updateDataSource();
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }
      }

    }
   this.fromUpdateFlag=false;
  }

  onGridCellClick(event): void {
    this.editedRow = [];
    if (event.listData.newValue != event.listData.oldValue) {
      let seq=event.listData.new_row.seq_num;
      //added to automatically format input amount when edit it
      if (event.dataField=='Amount'){
      var currencyPattern = this.getPattern(this.sourceGrid.gridData[0].Amount);
      var currency =event.listData.new_row.Ccy;
      let formattedAmount=checkCurrencyPlaces(event.listData.newValue, currencyPattern, currency);
        for (let i = 0; i < this.mvtGrid.dataProvider.length; i++) {
          if (this.mvtGrid.dataProvider[i].seq_num == seq) {
            this.mvtGrid.dataProvider[i].slickgrid_rowcontent[event.dataField].content = formattedAmount;
            this.mvtGrid.dataProvider[i][event.dataField] = formattedAmount;
          }
        }
      this.mvtGrid.refresh();
      }
      const copyData = $.extend(true, {}, event.listData.new_row);
      delete copyData.TooltipMsg;
      delete copyData.slickgrid_rowcontent;
      this.editedRow.push(copyData);
      this.getAccess(this.editedRow, "update");
    }
  }

  pasteDataFromButton() {
    this.data = [];
    this.initialdata = [];
    this.sendFlag = true;
    try{
    // using bracket notation as TypeScript may complain about `clipboard` property
    navigator['clipboard'].readText().then(clipText => {
      let row_data_final = [];
      let row_data = clipText.split('\n');
    
      row_data.forEach(function (value) {
        if (value.trim() != "") {
          row_data_final.push(value);
        }
      });
      //check if column orders changed by dragging columns
      if (this.columnOrderChanged) {
        this.defaultColumns = this.newOrderColumns;
      }
      if(row_data_final.length>0){
      /**  header checkBox checked scenario **/
      if (this.chkHeader.selected) {
        //normal scenario user copied data with header
        row_data_final[0] = row_data_final[0].trimEnd();
        this.importedColumns = row_data_final[0].split('\t');
        if (this.headerChanged || this.headerIsSaved=="true") {
          for (let j = 0; j < this.importedColumns.length; j++) {
            this.importedColumns[j] = this.columnsMapping(this.importedColumns[j]);
          }
        }
        //check if all mandatory columns are imported
        if (this.checkManddatoryColumns()) {
          delete row_data_final[0];
          row_data_final.forEach(row_data_final => {
            let initialrow = {};
            let row = {};

            if(row_data_final.match(/[\u3400-\u9FBF]/)){
              row_data_final = row_data_final.replace(/"[^"]+"/g, function(m) {
                return m.replace(/\t/g, ' ');
              });
              
            }

            this.importedColumns.forEach((a, index) => {
              initialrow[a] = row_data_final.split('\t')[index].trimEnd();
              row[a] = { clickable: false, content: row_data_final.split('\t')[index].trimEnd(), negative: false }
            });
            this.data.push(row);
            this.initialdata.push(initialrow);
          })
          // imported data will be set as default: if  the user decides to paste data without headers
          this.defaultColumns = this.importedColumns;
          this.populateHeaderGrid(this.importedColumns);
          this.populateSourceGrid(this.importedColumns);
          this.updateBottomGridcol(this.importedColumns);
        } else { this.sendFlag = false;
          this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.invalidHeaderAlert'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
        }
      }       
      /**  header checkBox not checked scenario **/
      else {
        //abnormal scenario user copy header labels with data
        if (!this.checkFirstRowValues(row_data_final[0].split('\t'))) {
          this.sendFlag = false;
          this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.extraHeader'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
        }
        // normal sceanrio, user can drag and drop grid columns and
        //should copy data without header labels 
        else {

          row_data_final.forEach(row_data_final => {
            let initialrow = {};
            let row = {};
            this.defaultColumns.forEach((a, index) => {
              if (row_data_final.split('\t')[index] != undefined) {
                initialrow[a] = row_data_final.split('\t')[index].trimEnd();
                row[a] = { clickable: false, content: row_data_final.split('\t')[index].trimEnd(), negative: false }
              } else {
                this.sendFlag = false;
                this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.invalidImportedData'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
              }
            });
            this.data.push(row);
            this.initialdata.push(initialrow);
          })
        }
      }
      this.pastedData= $.extend(true, [], this.initialdata);
      this.populateData();
    }
    });

    } catch{
      this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.importFailed', null), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
    }
  }

  pasteData() {
    this.data = [];
    this.initialdata = [];
    this.sendFlag = true;
      let row_data_final = [];
      try{
      if (this.pastedText.length>50){

      let row_data = this.pastedText.split('\n');
    
      row_data.forEach(function (value) {
        if (value.trim() != "") {
          row_data_final.push(value);
        }
      });
      //check if column orders changed by dragging columns
      if (this.columnOrderChanged) {
        this.defaultColumns = this.newOrderColumns;
      }
      /**  header checkBox checked scenario **/
      if (this.chkHeader.selected) {
        //normal scenario user copied data with header
        row_data_final[0] = row_data_final[0].trimEnd();
        this.importedColumns = row_data_final[0].split('\t');
        if (this.headerChanged || this.headerIsSaved == "true") {
          for (let j = 0; j < this.importedColumns.length; j++) {
            this.importedColumns[j] = this.columnsMapping(this.importedColumns[j]);
          }
        }
        //check if all mandatory columns are imported
        if (this.checkManddatoryColumns()) {
          delete row_data_final[0];
          row_data_final.forEach(row_data_final => {
            let initialrow = {};
            let row = {};
            this.importedColumns.forEach((a, index) => {
              initialrow[a] = row_data_final.split('\t')[index].trimEnd()
              row[a] = { clickable: false, content: row_data_final.split('\t')[index].trimEnd(), negative: false }
            });
            this.data.push(row);
            this.initialdata.push(initialrow);
          })
          // imported data will be set as default: if  the user decides to paste data without headers
          this.defaultColumns = this.importedColumns;
          this.populateHeaderGrid(this.importedColumns);
          this.populateSourceGrid(this.importedColumns);
          this.updateBottomGridcol(this.importedColumns);
        } else { this.sendFlag = false;
          this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.invalidHeaderAlert'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
        }
      }       
      /**  header checkBox not checked scenario **/
      else {
        //abnormal scenario user copy header labels with data
        if (!this.checkFirstRowValues(row_data_final[0].split('\t'))) {
          this.sendFlag = false;
          this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.extraHeader'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
        }
        // normal sceanrio, user can drag and drop grid columns and
        //should copy data without header labels 
        else {
          row_data_final.forEach(row_data_final => {
            let initialrow = {};
            let row = {};
            this.defaultColumns.forEach((a, index) => {
              if (row_data_final.split('\t')[index] != undefined) {
                initialrow[a] = row_data_final.split('\t')[index].trimEnd()
                row[a] = { clickable: false, content: row_data_final.split('\t')[index].trimEnd(), negative: false }
              } else {
                this.sendFlag = false;
                this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.invalidImportedData'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
              }
            });
            this.data.push(row);
            this.initialdata.push(initialrow);
          })
        }
      }
      this.pastedData= $.extend(true, [], this.initialdata);
      this.populateData();
    }
      } catch{
        this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.importFailed', null), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
      }
  }
  changeGridDragStatus() {
    if (this.mvtGrid.gridData.length > 0) {
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('preAdviceInput.unsavedData', null)));
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.confirmChange.bind(this));
    }else{
      this.fromUpdateFlag=true;
      this.updateData();
    }
  }

    
  confirmChange(event){
    if (event.detail == Alert.YES) {
      this.fromUpdateFlag=true;
      this.updateData();
    }else{
      //if no return old checkHeader checkBox status
      this.chkHeader.selected=!this.chkHeader.selected;
    }
  }

  updateData(){
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "preadviceinput.do?";
    this.actionMethod = 'method=displayDataDef';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['draggable'] = this.fromUpdateFlag ? ! this.chkHeader.selected :false;
    this.requestParams['fromMethod'] = "updateData";
    this.requestParams['dataType'] = this.dateSourceCombo.selectedValue;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  updateDataTypeConfig() {
    this.fileName.text= "";
    this.binarystrGlobal="";
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "preadviceinput.do?";
    this.actionMethod = 'method=displayDataDef';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['draggable'] = this.fromUpdateFlag ? !this.chkHeader.selected : false;
    this.requestParams['fromMethod'] = "updateDataTypeConfig";
    this.requestParams['dataType'] = this.dateSourceCombo.selectedValue;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }
  saveSettings() {
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "preadviceinput.do?";
    this.actionMethod = 'method=saveUserSettings';
    this.requestParams['newHeader'] = this.defaultColumns;
    this.requestParams['newSourceHeader'] = JSON.stringify(this.headersGrid.gridData[0]);
    this.requestParams['newValDateFormat'] = this.sourceGrid.gridData[0].Value_Date;
    this.requestParams['newPostDateFormat'] = this.sourceGrid.gridData[0].Post_Date;
    this.requestParams['newAmountFormat'] = this.sourceGrid.gridData[0].Amount;
    this.requestParams['headerChecked'] = this.chkHeader.selected;
    this.requestParams['dataSourceType'] = this.dateSourceCombo.selectedValue;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
  }

  checkManddatoryColumns() {
    var col = ["Entity_ID", "Ccy", "Account_ID", "Amount", "Value_Date", "Sign", "Pred_Status"];
    var valid = [];
    for (let j = 0; j < col.length; j++) {
      if (this.importedColumns.includes(col[j])) {
        valid.push("true");
      } else {
        valid.push("false");
      }
    }
    if (!valid.includes("false")) {
      return true;
    } else {
      return false;
    }
  }


  dragImportedCol(header) {
    var col = [];
    for (let j = 0; j < header.length; j++) {
      if(this.ScreenColumns.includes(header[j])){
      this.setDataProvider(header[j]);
      col.push({
        columnorder: j,
        dataelement: header[j],
        dataprovider:this.provider,
        draggable: false,
        editable:this.editableFlag,
        filterable: this.isPreviewGrid?true:false,
        format: "",
        heading: this.getHeaderName(header[j]),
        maxChars:40,
        sort: this.isPreviewGrid?true:false,
        type: this.isSourceFormat && this.isComboCol(header[j])? "combo":(this.isPreviewGrid && header[j]=="Amount" ? "num" :"str"),
        visible: true,
        visible_default: true,
        width: this.getColumnWidth(header[j])
      })
    }
    }
    this.metadataHeaderGrid = {
      "columns": {
        "column": col
      }
    };
  }

  getHeaderName(header) {
    var headerDesc = null;

    switch (header) {
      case "Entity_ID":
        headerDesc = "Entity";
        break;
      case "Account_ID":
        headerDesc = "Account ID";
        break;
      case "Value_Date":
        headerDesc = "Value Date";
        break;
      case "Pred_Status":
        headerDesc = "Pred Status";
        break;
      case "Book_Code":
        headerDesc = "Book Code";
        break;
      case "Post_Date":
        headerDesc = "Post Date";
        break;
      case "CounterParty_ID":
        headerDesc = "CounterParty ID";
        break;
      case "Cparty_Text":
        headerDesc = "Cparty Text";
        break;
      case "Match_Party":
        headerDesc = "Match Party";
        break;
        case "Sign":
          headerDesc = "Sign";
          break;
        case "Reference":
          headerDesc = "Reference";
          break;
        case "Product_Type":
          headerDesc = "Product Type";
          break;
        case "Amount":
          headerDesc = "Amount";
          break;
        case "Ccy":
          headerDesc = "Ccy";
      default: 
        break;
    }

    return headerDesc;
  }


columnsMapping(importedCol){
  var finalCol = null;

  switch (importedCol) {
    case this.headersGrid.gridData[0].Entity_ID:
      finalCol = "Entity_ID";
      break;
    case this.headersGrid.gridData[0].Account_ID:
      finalCol = "Account_ID";
      break;
    case this.headersGrid.gridData[0].Value_Date:
      finalCol = "Value_Date";
      break;
    case this.headersGrid.gridData[0].Pred_Status:
      finalCol = "Pred_Status";
      break;
    case this.headersGrid.gridData[0].Book_Code:
      finalCol = "Book_Code";
      break;
    case this.headersGrid.gridData[0].Post_Date:
      finalCol = "Post_Date";
      break;
    case this.headersGrid.gridData[0].CounterParty_ID:
      finalCol = "CounterParty_ID";
      break;
    case this.headersGrid.gridData[0].Cparty_Text:
      finalCol = "Cparty_Text";
      break;
    case this.headersGrid.gridData[0].Match_Party:
      finalCol = "Match_Party";
      break;
      case this.headersGrid.gridData[0].Sign:
      finalCol = "Sign";
        break;
      case this.headersGrid.gridData[0].Reference:
      finalCol = "Reference";
        break;
      case this.headersGrid.gridData[0].Product_Type:
      finalCol = "Product_Type";
        break;
      case this.headersGrid.gridData[0].Amount:
      finalCol = "Amount";
        break;
      case this.headersGrid.gridData[0].Ccy:
      finalCol = "Ccy";
    default: 
      break;
  }

  return finalCol;
}  

getColumnWidth(column) {
  var colWidth = null;

  switch (column) {
    case "Entity_ID":
      colWidth = 120;
      break;
    case "Account_ID":
      colWidth = 120;
      break;
    case "Value_Date":
      colWidth = 135;
      break;
    case "Pred_Status":
      colWidth = 120;
      break;
    case "Book_Code":
      colWidth = 120;
      break;
    case "Post_Date":
      colWidth = 120;
      break;
    case "CounterParty_ID":
      colWidth = 150;
      break;
    case "Cparty_Text":
      colWidth = 120;
      break;
    case "Match_Party":
      colWidth = 120;
      break;
    case "Sign":
      colWidth = 80;
      break;
    case "Reference":
      colWidth = 120;
      break;
    case "Product_Type":
      colWidth = 130;
      break;
    case "Amount":
      colWidth = 100;
      break;
    case "Ccy":
      colWidth = 80;
    default:
      break;
  }

  return colWidth;
}

  populateHeaderGrid(col) {
    this.isSourceFormat = false;
    this.isPreviewGrid=false;
    this.editableFlag = true;
    this.dragImportedCol(col);
    this.headersGrid.forceHeaderRefresh=true;
    const obj = { columns: this.metadataHeaderGrid.columns };
    this.headersGrid.CustomGrid(obj);  
    if (this.headerGridRows.size > 0 && !this.headerChanged) { 
      this.headersGrid.gridData = this.headerGridRows;
      this.headersGrid.setRowSize = this.jsonReader.getRowSize();
    }
    else { 
      let array=[];
      for (var i = 0; i < this.newHeaderGridData.length; i++) {
          array.push(this.newHeaderGridData[i].slickgrid_rowcontent);
      } 
      this.headersGrid.gridData = { size: array.length, row: array };

      this.headersGrid.refresh();

    }

  }

/***this method is used to check if the user forget to uncheck My data has header
 checkBox while trying to import data with header labels****/
  checkFirstRowValues(row){
    var values= [this.headersGrid.gridData[0].Entity_ID,this.headersGrid.gridData[0].Ccy,this.headersGrid.gridData[0].Account_ID,
    this.headersGrid.gridData[0].Amount,this.headersGrid.gridData[0].Value_Date,this.headersGrid.gridData[0].Sign,
    this.headersGrid.gridData[0].Pred_Status,this.headersGrid.gridData[0].Reference,this.headersGrid.gridData[0].Book_Code,this.headersGrid.gridData[0].Post_Date,
    this.headersGrid.gridData[0].Product_Type,this.headersGrid.gridData[0].CounterParty_ID,this.headersGrid.gridData[0].Cparty_Text,
    this.headersGrid.gridData[0].Match_Party];

    var valid = [];
    for (let j = 0; j < values.length; j++) {
      if (row.includes(values[j])) {
        valid.push("true");
      } else {
        valid.push("false");
      }
    }
    if (valid.includes("true")) {
      return false;
    } else {
      return true;
    }

  }
  populateSourceGrid(col) {
    this.isSourceFormat=true;
    this.isPreviewGrid=false;
    this.editableFlag=false;
    this.dragImportedCol(col);
    this.sourceGrid.forceHeaderRefresh=true;
    const obj = { columns: this.metadataHeaderGrid.columns };
    this.sourceGrid.gridComboDataProviders(this.selectValues);
    this.sourceGrid.CustomGrid(obj);
    if (this.sourceGridRows.size > 0) {
      this.sourceGrid.gridData = this.sourceGridRows;
      this.sourceGrid.setRowSize = this.jsonReader.getRowSize();
    }else {
      this.sourceGrid.gridData = { size: 0, row: [] };
    }
    //when i changed one of comboBoxes values then i pasted data, new value should be saved when populating source grid*/
    if (this.dataField && this.newComboVal && this.srcGridChanged) {
      this.sourceGrid.gridData[0][this.dataField] = this.newComboVal;
      this.sourceGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content = this.newComboVal;
      this.sourceGrid.dataProvider[this.rowIndex][this.dataField] = this.newComboVal;
      this.sourceGrid.refresh();
    }
    this.resizeGrids();
  }

  updateBottomGridcol(col) {
    this.isSourceFormat=false;
    this.isPreviewGrid=true;
    this.editableFlag=true;
    this.dragImportedCol(col);
    this.mvtGrid.forceHeaderRefresh=true;
    const obj = { columns: this.metadataHeaderGrid.columns };
    this.mvtGrid.CustomGrid(obj);
    //this.mvtGrid.gridData = { size: 0, row: [] };
    this.resizeGrids();
  }

  isComboCol(column) {
    var comboColumns = ['Value_Date', 'Amount', 'Post_Date'];
    if (comboColumns.includes(column))
      return true;
  }

  setDataProvider(column) {
    this.provider = "";
    if (this.isSourceFormat) {
      if (column == "Value_Date") {
        this.provider = "listValDates";
      } else if (column == "Post_Date") {
        this.provider = "listPostDates";
      } else if (column == "Amount") {
        this.provider = "listAmount";
      } else {
        this.provider = "";
      }
    } else {
      this.provider = "";
    }

  }

  getDefaultHeader(){
    this.defaultColumns =[];
    for (let j=0; j<this.headerGridCol.length;j++){
      let col=this.headerGridCol[j].dataelement;
      this.defaultColumns.push(col);
    }
    //this line is added to avoid disabled field after reset(only in case of unchecked checkBox)
     if(this.resetFlag)
     this.updateBottomGridcol(this.defaultColumns);
  }


  checkGrid(event): void { 
    try{ 
    let flag =true;
    //get clipboard data when making ctrl+v
    if (event) {
      this.clipboardData = event.clipboardData;
      this.pastedText = this.clipboardData.getData('text');
      if (this.pastedText && this.pastedText.length<100){
        flag=false;
      }
      this.pasteFlag=true;
    }else{
      this.pastedText="";
      const wb: XLSX.WorkBook = XLSX.read(this.binarystrGlobal,{  type: 'binary', raw: true,cellNF:true });
      /* selected the first sheet */
      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];
      /* save data */
      if(this.chkHeader.selected){
      this.excelData = XLSX.utils.sheet_to_json(ws, {raw: true});
      }else{
      this.excelData = XLSX.utils.sheet_to_json(ws,{raw: true, header: this.defaultColumns}); 
      }
      this.getImportedFileData(ws);
    }
    if (this.mvtGrid.gridData.length > 0 && flag) {
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('preAdviceInput.unsavedData', null)));
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.confirmListener.bind(this));
    } else {
      //this.importStatusTxt.text= SwtUtil.getPredictMessage('preAdviceInput.importInProgress', null);
      this.paste();
    }
    } catch{
      this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.importFailed', null), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
    }
  }


  confirmListener(event) {
    /* Condition to check Ok button is selected */
    if (event.detail == Alert.YES) {
     // this.importStatusTxt.text= SwtUtil.getPredictMessage('preAdviceInput.importInProgress', null);
      this.allRows.selected=true;
      //this.clipboardAt.text = SwtUtil.getPredictMessage('preAdviceInput.importComplete', null);
      this.mvtGrid.gridData= { size: 0, row: [] };
      this.paste();
    }
  }

  paste() {
    try{
    if (this.dateSourceCombo.selectedLabel == "Clipboard" || this.pastedText) {
      if (this.pasteFlag) {
        this.pasteData();
        this.pasteFlag = false;
      } else if (navigator['clipboard']) {
        this.pasteDataFromButton();
      } else {
        this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.ClipboardAlert'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
      }
    } else {
      this.excelValFlag=true;
      this.prepareExcelData(this.excelData,this.importedFileHeaders);
    }
    } catch{
      this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.importFailed', null), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
    }
    
  }

  private populateData(): void {
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
        errorLocation=10;
        if (this.sendFlag) {
        this.saveButton.enabled=true;
        this.saveButton.buttonMode=true;
        /*var today = new Date();
        var date = this.addZero(today.getDate()) + '/' + this.addZero((today.getMonth() + 1)) + '/' + this.addZero(today.getFullYear());
        var time = this.addZero(today.getHours()) + ":" + this.addZero(today.getMinutes()) + ":" + this.addZero(today.getSeconds());
        var dateTime = date + ' ' + time;*/
        errorLocation=20;
        this.getAccess(this.initialdata, "save");
        errorLocation=30;
        //this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importComplete', null);
        /*if(this.dateSourceCombo.selectedLabel=="Clipboard"){
        this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.clipboardAt', null) + ' ' + dateTime;
        }else{
        this.importStatusTxt.text = this.uploadedFileName + ' ' + SwtUtil.getPredictMessage('preAdviceInput.at', null)  + ' ' + dateTime;
        }*/
        }
    }
    catch (error) {
       SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "importData", errorLocation);
    }
  }

  private addZero(i) {
    if (i < 10) {
      i = "0" + i;
    }
    return i;
  }

  private selectInvalidRowColor(dataContext, color ): any {
    // Variable to hold error location
    var errorLocation: number = 0;
    // Color
    var rColor: any;
    try {
      errorLocation = 10;
      if (dataContext.slickgrid_rowcontent.TooltipMsg[0]!="") {
        errorLocation = 20;
        rColor = "#C0C0C0"; //0xDCDCDC not clear 
      }
      else {
        rColor = color;
      }
    }
    catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, this.getQualifiedClassName(this), "selectInvalidRowColor", errorLocation);
    }
    return rColor;
  }

private setTooltipMessage(dataContext): any {
    // Variable to hold error location
    var errorLocation: number = 0;
    var message= "";
    // Color
    var displayedMsg ="";
    try {
      errorLocation = 10;
      message=dataContext.slickgrid_rowcontent.TooltipMsg? dataContext.slickgrid_rowcontent.TooltipMsg[0]: "";
      if (message) {
        message = message.split(" ").join("$#$");
        message = message.split(",").join("&@&");
        message = message.split(";").join("&_&");
        errorLocation = 20;
        displayedMsg = message; //0xDCDCDC not clear 

      }
      else {
        displayedMsg = "";
      }
    }
    catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, this.getQualifiedClassName(this), "setTooltipMessage", errorLocation);
    }
    return displayedMsg;
  }


  clearClipBoard() {
    //clear the clipboard     
    navigator['clipboard'].writeText('').then(function () {
      /* clipboard successfully set */

    }, function () {
      /* clipboard write failed */
    });

  }

public accessSelectIndex :number;
//validate access for all the pasted records
  getAccess(data, operation) {
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.stillCheckingFlag=true;
    this.inputData.cbResult = (event) => {
      this.checkAccess(event,operation);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "preadviceinput.do?";
    this.actionMethod = 'method=checkPreAdviceInputAccess';
    this.requestParams['preAdviceList'] = JSON.stringify(data);
    this.requestParams['ValDateFormat'] = this.sourceGrid.gridData[0].Value_Date;
    this.requestParams['PostDateFormat'] = this.sourceGrid.gridData[0].Post_Date;
    this.requestParams['amountFormat'] =this.sourceGrid.gridData[0].Amount;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    if(JSON.stringify(data).length>0){
    this.inputData.send(this.requestParams);
    }
  }
    b64DecodeUnicode(str) {
    // Going backwards: from bytestream, to percent-encoding, to original string.
    return decodeURIComponent(atob(str).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
}
  checkAccess(event, operation) {
    this.jsonReader.setInputJSON(event);
    if (this.jsonReader.getRequestReplyStatus()) {
      this.saveButton.enabled = true;
      this.saveButton.buttonMode = true;
      this.validRow = [];
      this.inValidRow = [];
      
      var result: string = StringUtils.replaceAll(event.preAdviceInput.updatedArray, {
        '\\(': '=',
        '\\)': '+'
      });

      this.initialdata = JSON.parse(this.b64DecodeUnicode(result));
      this.initialdata.forEach(data => {
        //set row color
        this.mvtGrid.rowColorFunction = (dataContext, color) => {
          return this.selectInvalidRowColor(dataContext, color);
        };

        //set row tooltip in case data is invalid
        this.mvtGrid.customTooltipFunction = (dataContext) => {
          return this.setTooltipMessage(dataContext);
        };
        if (operation == "update") {
          if (data.TooltipMsg[0].length>0) {
            this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.rowStillInvalid'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
          }
        }

      })
      for (var i = 0; i < this.initialdata.length; i++) {
        for (const key in this.initialdata[i]) {
          if (typeof this.initialdata[i][key] == "string") {
            this.initialdata[i][key] = { clickable: false, content: this.initialdata[i][key], negative: false };
          }
        }
        if(operation == "save"){ 
        let seq=i
        this.initialdata[i]["seq_num"]={ clickable: false, content: seq.toString(), negative: false };
        }
      }

      //populate data
      if (operation == "save") {
        this.mvtGrid.gridData = { size: this.initialdata.length, row: this.initialdata };
      }


      // this code is added to avoid gridData issue when user try to edit cell when invalidRows button is selected
      if (operation == "update") {
        let array=[];
        for (var i = 0; i < this.gridRows.length; i++) {
          if (this.gridRows[i].slickgrid_rowcontent.seq_num.content != this.initialdata[0].seq_num.content) {
            array.push(this.gridRows[i].slickgrid_rowcontent);

          }else{
            array.push(this.initialdata[0]);
          }
        } 
        this.mvtGrid.gridData = { size: array.length, row: array };
      }
      this.gridRows = this.mvtGrid.gridData;
      //get valid/invalid rows
      this.mvtGrid.gridData.forEach(data => {

        if (data.slickgrid_rowcontent.TooltipMsg[0] == "") {
          this.validRow.push(data);

        } else {
          this.inValidRow.push(data);
        }

      });
      //to update grid data with the new data
      if (operation == "update"){
      this.filterRows();
      }
      if (this.validRow.length == 0) {
        this.saveButton.enabled = false;
        this.saveButton.buttonMode = false;
      }
    } else {
      // display the error message
      this.swtAlert.error(this.jsonReader.getRequestReplyMessage(), SwtUtil.getPredictMessage('alert_header.error'));
    }
    this.stillCheckingFlag=false;
    if (operation != "update") {
      this.swtAlert.confirm(SwtUtil.getPredictMessage('preAdviceInput.importComplete', null), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeSuccesStatus.bind(this));
    }
  }


  changeFailureStatus() {
    var today = new Date();
    var date = this.addZero(today.getDate()) + '/' + this.addZero((today.getMonth() + 1)) + '/' + this.addZero(today.getFullYear());
    var time = this.addZero(today.getHours()) + ":" + this.addZero(today.getMinutes()) + ":" + this.addZero(today.getSeconds());
    var dateTime = date + ' ' + time;
    if (this.dateSourceCombo.selectedLabel == "Clipboard") {
      this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + ' Clipboard' + ' failed at ' + dateTime;
    } else if (this.dateSourceCombo.selectedLabel == "Excel file") {
      //this.importStatusTxt.text = this.uploadedFileName + ' ' + SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + 'Excel' + ' ' + dateTime;
      this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + ' Excel' + ' failed at ' + dateTime;
    } else {
      //this.importStatusTxt.text = this.uploadedFileName + ' ' + SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + 'Csv' + dateTime;
      this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + ' Csv' + ' failed at ' + dateTime;
    }
    this.clipboadDate = dateTime;
  }


  changeSuccesStatus() {
    var today = new Date();
    var date = this.addZero(today.getDate()) + '/' + this.addZero((today.getMonth() + 1)) + '/' + this.addZero(today.getFullYear());
    var time = this.addZero(today.getHours()) + ":" + this.addZero(today.getMinutes()) + ":" + this.addZero(today.getSeconds());
    var dateTime = date + ' ' + time;
    if (this.dateSourceCombo.selectedLabel == "Clipboard") {
      this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + ' Clipboard' + ' at ' + dateTime;
    } else if (this.dateSourceCombo.selectedLabel == "Excel file") {
      //this.importStatusTxt.text = this.uploadedFileName + ' ' + SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + 'Excel' + ' ' + dateTime;
      this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + ' Excel' + ' at ' + dateTime;
    } else {
      //this.importStatusTxt.text = this.uploadedFileName + ' ' + SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + 'Csv' + dateTime;
      this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importFrom', null) + ' Csv' + ' at ' + dateTime;
    }
    this.clipboadDate = dateTime;
  }

  getPattern(amountFormat) {
    var pattern = null;

    switch (amountFormat) {
      case "999,999.00":
        pattern = "currencyPat1";
        break;
      case "999.999,00":
        pattern = "currencyPat2";
        break;
      default:
        break;
    }

    return pattern;
  }


  saveAll() {
    if(this.stillCheckingFlag){
      setTimeout(() => {
        this.checkBeforeSaving();
        return;
      }, 1000);
    }else{
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.saveSettings();
      ExternalInterface.call("close");
      window.opener.instanceElement.updateData();
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "preadviceinput.do?";
    this.actionMethod = 'method=saveAll';
    this.requestParams['preAdviceList'] = JSON.stringify(this.validRow);
    this.requestParams['newValDateFormat'] = this.sourceGrid.gridData[0].Value_Date;
    this.requestParams['newPostDateFormat'] = this.sourceGrid.gridData[0].Post_Date;
    this.requestParams['newAmountFormat'] = this.getPattern(this.sourceGrid.gridData[0].Amount);
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    if(JSON.stringify(this.validRow).length>0){
    this.inputData.send(this.requestParams);
    }
  }
  }

  checkBeforeSaving(): void {
    if (this.inValidRow.length > 0) {
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('preAdviceInput.invalidRowsAlert', null)));
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.confirmSave.bind(this));
    } else {
      this.saveAll();
    }
  }


  confirmSave(event){
    if (event.detail == Alert.YES) {
      this.saveAll();
    }

  }

  filterRows() {
    if (this.allRows.selected) {
      this.mvtGrid.gridData = { size: this.gridRows.length, row: this.gridRows };
    }
    if (this.validRows.selected) {
      this.mvtGrid.gridData = { size: this.validRow.length, row: this.validRow };
    }
    if (this.invalidRows.selected) {

      this.mvtGrid.gridData = { size: this.inValidRow.length, row: this.inValidRow };
    }
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  closeHandler(): void {
    if (this.mvtGrid.gridData.length > 0) {
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('preAdviceInput.closeAlert', null)));
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.confirmCloseScreen.bind(this));
    } else {
      this.saveSettings();
      ExternalInterface.call("close");
    }

  }


  confirmCloseScreen(event){
    if (event.detail == Alert.YES) {
      this.saveSettings();
      ExternalInterface.call("close");
    }
  }

  resetConfig() {
    this.resetFlag=true;
    Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
    Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
    var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('preAdviceInput.resetAlert', null)));
    this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.deleteConfig.bind(this));

  }

  deleteConfig(event) {
    if (event.detail == Alert.YES) {
      this.requestParams = [];
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = () => {
        this.updateData();
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "preadviceinput.do?";
      this.actionMethod = 'method=deleteUserSettings';
      this.requestParams['sourceDataType'] = this.dateSourceCombo.selectedValue;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    }
  }

  updateDataSource(){
  this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importInitStatus', null);
   if(this.dateSourceCombo.selectedLabel=="Clipboard"){
    this.uploadImage.includeInLayout=false;
    this.uploadImage.source ="";
    this.uploadImage.id ="uploadImage1";
   }else{
    this.uploadImage.includeInLayout=true;
    this.uploadImage.source =this.baseURL + ExternalInterface.call('eval', 'uploadFileImage');
    this.uploadImage.id ="uploadImage";
   }
  }

  onFileChange(event: any) {
    if (this.mvtGrid.gridData.length>0) {
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('preAdviceInput.unsavedData', null)));
      this.uploadEvent=event;
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.confirmUpload.bind(this));
    } else{
      this.readUploadedFile(event);
    }
  }

  //added to avoid 'HTML input file selection event not firing upon selecting the same file' issue
  onInputClick (event){
    event.target.value = ''
  }

  
  confirmUpload(event) {
    /* Condition to check Ok button is selected */
    if (event.detail == Alert.YES) {
      this.allRows.selected=true;
      this.importStatusTxt.text = SwtUtil.getPredictMessage('preAdviceInput.importInitStatus', null);
      this.mvtGrid.gridData= { size: 0, row: [] };
      this.readUploadedFile(this.uploadEvent);
    }
  }
 private binarystrGlobal: string ="";
  readUploadedFile(event){
    /* wire up file reader */
    const target: DataTransfer = <DataTransfer>(event.target);
    this.uploadedFileName= event.target.files[0].name;
    this.fileName.text= this.uploadedFileName;
    if (target.files.length !== 1) {
      throw new Error('Cannot use multiple files');
    }
    const reader: FileReader = new FileReader();
    reader.readAsBinaryString(target.files[0]);
    reader.onload = (e: any) => {
      /* create workbook */
      const binarystr: string = e.target.result;
      this.binarystrGlobal=binarystr;
    };
  }

  //get header rows from excel and populate data in the grid
  getImportedFileData(sheet) {
    this.importedFileHeaders=[];
    var range = XLSX.utils.decode_range(sheet['!ref']);
    var C, R = range.s.r; /* start in the first row */
    /* walk every column in the range */
    for (C = range.s.c; C <= range.e.c; ++C) {
      var cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })] /* find the cell in the first row */
      var hdr = "UNKNOWN " + C; // <-- replace with your desired default 
      if (cell && cell.t) {
        hdr = XLSX.utils.format_cell(cell);
        if(hdr)
        this.importedFileHeaders.push(hdr);
      }
    }
  }

  
  prepareExcelData(data, headers) {
    let map: {[key: string]: string} = {};
    this.data = [];
    this.initialdata = [];
    this.sendFlag = true;
    let amtColName= this.headersGrid.gridData[0].Amount;
    let ccyColName= this.headersGrid.gridData[0].Ccy;
    let valDateColName= this.headersGrid.gridData[0].Value_Date;
    let postDateColName= this.headersGrid.gridData[0].Post_Date;
    let currencyPattern = this.getPattern(this.sourceGrid.gridData[0].Amount);
    let valDatePattern= this.sourceGrid.gridData[0].Value_Date;
    let postDatePattern= this.sourceGrid.gridData[0].Post_Date; 
      let row_data_final = [];
    try{
      //check if column orders changed by dragging columns
      if (this.columnOrderChanged) {
        this.defaultColumns = this.newOrderColumns;
      }
      /**  header checkBox checked scenario **/
      if (this.chkHeader.selected) {
        //normal scenario user copied data with header
        this.importedColumns = headers;
        if (this.headerChanged || this.headerIsSaved == "true") {
          for (let j = 0; j < this.importedColumns.length; j++) {
            map[this.columnsMapping(this.importedColumns[j].replace(",",""))]=this.importedColumns[j];
            this.importedColumns[j] = this.columnsMapping(this.importedColumns[j].replace(",",""));
          }
        }
        //check if all mandatory columns are imported
        if (this.checkManddatoryColumns()) {
           data.forEach(data => {
            let initialrow = {};
            let row = {};
             if(this.dateSourceCombo.selectedLabel=="Excel file"){
             var currency =data[ccyColName];

            if(data[amtColName] && typeof data[amtColName] == 'number'){
             data[amtColName]= checkCurrencyPlacesFromNumber((data[amtColName]).toString(), currencyPattern, currency);
            }

             if(data[valDateColName] && typeof data[valDateColName] == 'number'){
              data[valDateColName]= CommonUtil.formatDate(this.ExcelDateToJSDate(data[valDateColName]),valDatePattern);
             }
             if(data[postDateColName] && typeof data[postDateColName] == 'number'){
              data[postDateColName]= CommonUtil.formatDate(this.ExcelDateToJSDate(data[postDateColName]),postDatePattern);
             }
            }
            for (let i = 0; i < this.importedColumns.length; i++) {
              let index= this.importedColumns[i];
              let indexMap= map[index]?map[index]:index;            
              if(data[indexMap]){
              initialrow[index] = data[indexMap].toString()!=","?data[indexMap].toString():"";
              row[index] = { clickable: false, content: data[indexMap].toString()!=","?data[indexMap].toString():"", negative: false }
              }
            }
            this.data.push(row);
            this.initialdata.push(initialrow);
          })

          // imported data will be set as default: if  the user decides to paste data without headers
          this.defaultColumns = this.importedColumns;
          if(this.excelValFlag){
          this.populateHeaderGrid(this.importedColumns);
          this.populateSourceGrid(this.importedColumns);
          this.updateBottomGridcol(this.importedColumns);
          }
        } else {
          this.sendFlag = false;
          this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.invalidHeaderAlert'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
        }
      }       
      /**  header checkBox not checked scenario **/
      else {
        //abnormal scenario user copy header labels with data
        if (!this.checkFirstRowValues(headers)) {
          this.sendFlag = false;
          this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.extraHeader'), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
        }
        // normal sceanrio, user can drag and drop grid columns and
        //should copy data without header labels 
        else {
          data.forEach(data => {
            let initialrow = {};
            let row = {};
            if(this.dateSourceCombo.selectedLabel=="Excel file"){
            var currency =data[ccyColName];

            if(data[amtColName] && typeof data[amtColName] == 'number'){
            data[amtColName]= checkCurrencyPlacesFromNumber((data[amtColName]).toString(), currencyPattern, currency);
            }

            if(data[valDateColName] && typeof data[valDateColName] == 'number'){
              data[valDateColName]= CommonUtil.formatDate(this.ExcelDateToJSDate(data[valDateColName]),valDatePattern);
             }
             if(data[postDateColName] && typeof data[postDateColName] == 'number'){
              data[postDateColName]= CommonUtil.formatDate(this.ExcelDateToJSDate(data[postDateColName]),postDatePattern);
             } 
          }
            for (let i = 0; i < this.defaultColumns.length; i++) {
              let index= this.defaultColumns[i];
              if(data[index]!=undefined){
              initialrow[index] = data[index].toString()!=","?data[index].toString():""
              } else {
              initialrow[index] = "";
              }
              row[index] = { clickable: false, content: initialrow[index], negative: false }
            }
            this.data.push(row);
            this.initialdata.push(initialrow);
          })

        }
      }
      this.pastedData= $.extend(true, [], this.initialdata);
      this.populateData();
    } catch{
      this.swtAlert.error(SwtUtil.getPredictMessage('preAdviceInput.importFailed', null), SwtUtil.getPredictMessage('alert_header.error'), Alert.OK, null, this.changeFailureStatus.bind(this));
    }
  }


    ExcelDateToJSDate(date) {
    return new Date(Math.round((date - 25569)*86400*1000));
  }


}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ImportPreAdvices }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ImportPreAdvices],
  entryComponents: []
})
export class ImportPreAdvicesModule { }