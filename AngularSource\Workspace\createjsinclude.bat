@echo off
setlocal enabledelayedexpansion

set "jsOutputFile=dist/includeJS.jsp"
set "stylesOutputFile=dist/styles.css"
set "sourceDirectory=./dist"
set "jsFilePrefixes=common polyfills runtime scripts main"
set "stylesFilePrefixes=styles"

echo ^<%%@ page import="org.swallow.util.SwtUtil"%%^> > %jsOutputFile%
echo ^<app-root^>^</app-root^> >> %jsOutputFile%


for %%P in (%jsFilePrefixes%) do (
    for /R %sourceDirectory% %%F in (%%P*.js) do (
        set "fileName=%%~nxF"
        echo ^<script type="text/javascript" src="angularSources/!fileName!"^>^</script^> >> %jsOutputFile%
    )
)

echo. > %stylesOutputFile%


for %%P in (%stylesFilePrefixes%) do (
    for /R %sourceDirectory% %%F in (%%P*.css) do (
        set "fileName=%%~nxF"
        if /I not "!fileName!"=="styles.css" (
            echo @import "!fileName!"; >> %stylesOutputFile%
        )
    )
)
echo Files %jsOutputFile% and %stylesOutputFile% have been created successfully.