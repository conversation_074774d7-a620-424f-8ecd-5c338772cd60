import { Component, OnInit, ViewChild, OnD<PERSON>roy, ElementRef, NgModule } from '@angular/core';
import { SwtLabel, SwtModule, SwtAlert, JSONReader, HTTPComms, SwtUtil, TitleWindow, CommonService, SwtTextInput, Keyboard, SwtButton, focusManager, ExternalInterface, StringUtils, SwtPopUpManager, Alert, SwtRadioItem, SwtRadioButtonGroup, SwtToolBoxModule } from 'swt-tool-box';
declare var instanceElement: any;
import beautify from 'xml-beautifier';
declare var require: any;


var prettyData = require('pretty-data');
import { log } from 'util';
import { CategoryList } from '../PCMBreakdownMonitor/popUp/CategoryList/CategoryList';
import { Routes, RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/compiler/src/core';

@Component({
  selector: 'payment-request-display',
  templateUrl: './PaymentRequestDisplay.html',
  styleUrls: ['./PaymentRequestDisplay.css']
})
export class PaymentRequestDisplay extends SwtModule implements OnInit, OnDestroy {


  @ViewChild('paymentIdTextInput') paymentIdTextInput: SwtTextInput;
  @ViewChild('lastActionLabel') lastActionLabel: SwtLabel;
  @ViewChild('accountidLabel') accountidLabel: SwtLabel;
  @ViewChild('paymentIdLabel') paymentIdLabel: SwtLabel;
  @ViewChild('accountwithinstnameLabel') accountwithinstnameLabel: SwtLabel;
  @ViewChild('accountwithinstaccountLabel') accountwithinstaccountLabel: SwtLabel;
  @ViewChild('accountwithinstbicLabel') accountwithinstbicLabel: SwtLabel;
  @ViewChild('actionLabel') actionLabel: SwtLabel;
  @ViewChild('amountLabel') amountLabel: SwtLabel;
  @ViewChild('boreferenceLabel') boreferenceLabel: SwtLabel;
  @ViewChild('beneficiarycustnameLabel') beneficiarycustnameLabel: SwtLabel;
  @ViewChild('beneficiarycustaccountLabel') beneficiarycustaccountLabel: SwtLabel;
  @ViewChild('beneficiarycustbicLabel') beneficiarycustbicLabel: SwtLabel;
  @ViewChild('beneficiaryinstnameLabel') beneficiaryinstnameLabel: SwtLabel;
  @ViewChild('beneficiaryinstaccountLabel') beneficiaryinstaccountLabel: SwtLabel;
  @ViewChild('beneficiaryinstbicLabel') beneficiaryinstbicLabel: SwtLabel;
  @ViewChild('currencycodeLabel') currencycodeLabel: SwtLabel;
  @ViewChild('delivererscustodianaccountLabel') delivererscustodianaccountLabel: SwtLabel;
  @ViewChild('delivererscustodianbicLabel') delivererscustodianbicLabel: SwtLabel;
  @ViewChild('delivererscustodiannameLabel') delivererscustodiannameLabel: SwtLabel;
  @ViewChild('deliveryagentaccountLabel') deliveryagentaccountLabel: SwtLabel;
  @ViewChild('deliveryagentbicLabel') deliveryagentbicLabel: SwtLabel;
  @ViewChild('deliveryagentnameLabel') deliveryagentnameLabel: SwtLabel;
  @ViewChild('departmentLabel') departmentLabel: SwtLabel;
  @ViewChild('entityidLabel') entityidLabel: SwtLabel;
  @ViewChild('messageLabel') messageLabel: SwtLabel;
  @ViewChild('gpiLabel') gpiLabel: SwtLabel;
  @ViewChild('inputdateLabel') inputdateLabel: SwtLabel;
  @ViewChild('intermediaryaccountLabel') intermediaryaccountLabel: SwtLabel;
  @ViewChild('intermediarybicLabel') intermediarybicLabel: SwtLabel;
  @ViewChild('intermediarynameLabel') intermediarynameLabel: SwtLabel;
  @ViewChild('messagetypeLabel') messagetypeLabel: SwtLabel;
  @ViewChild('orderingcustaccountLabel') orderingcustaccountLabel: SwtLabel;
  @ViewChild('orderingcustbeiLabel') orderingcustbeiLabel: SwtLabel;
  @ViewChild('orderingcustnameLabel') orderingcustnameLabel: SwtLabel;
  @ViewChild('orderinginstaccountLabel') orderinginstaccountLabel: SwtLabel;
  @ViewChild('orderinginstbicLabel') orderinginstbicLabel: SwtLabel;
  @ViewChild('orderinginstnameLabel') orderinginstnameLabel: SwtLabel;
  // @ViewChild('originatingsystemLabel') originatingsystemLabel: SwtLabel;
  @ViewChild('paymenttypeLabel') paymenttypeLabel: SwtLabel;
  @ViewChild('placeofsettlementaccountLabel') placeofsettlementaccountLabel: SwtLabel;
  @ViewChild('placeofsettlementbicLabel') placeofsettlementbicLabel: SwtLabel;
  @ViewChild('placeofsettlementnameLabel') placeofsettlementnameLabel: SwtLabel;
  @ViewChild('receiverbicLabel') receiverbicLabel: SwtLabel;
  @ViewChild('receiverscorresnameLabel') receiverscorresnameLabel: SwtLabel;
  @ViewChild('receiverscorresaccountLabel') receiverscorresaccountLabel: SwtLabel;
  @ViewChild('receiverscorresbicLabel') receiverscorresbicLabel: SwtLabel;
  @ViewChild('paymentreferenceLabel') paymentreferenceLabel: SwtLabel;
  @ViewChild('relatedreferenceLabel') relatedreferenceLabel: SwtLabel;
  @ViewChild('selleraccountLabel') selleraccountLabel: SwtLabel;
  @ViewChild('sellerbicLabel') sellerbicLabel: SwtLabel;
  @ViewChild('sellernameLabel') sellernameLabel: SwtLabel;
  @ViewChild('senderbicLabel') senderbicLabel: SwtLabel;
  @ViewChild('senderscorresnameLabel') senderscorresnameLabel: SwtLabel;
  @ViewChild('senderreceiverinfoLabel') senderreceiverinfoLabel: SwtLabel;
  @ViewChild('senderscorresaccountLabel') senderscorresaccountLabel: SwtLabel;
  @ViewChild('senderscorresbicLabel') senderscorresbicLabel: SwtLabel;
  @ViewChild('sourceidLabel') sourceidLabel: SwtLabel;
  @ViewChild('releasedByLabel') releasedByLabel: SwtLabel;
  @ViewChild('sourceurgencyindicatorLabel') sourceurgencyindicatorLabel: SwtLabel;
  @ViewChild('entitysubidLabel') entitysubidLabel: SwtLabel;
  @ViewChild('thirdreimbsmntaccountLabel') thirdreimbsmntaccountLabel: SwtLabel;
  @ViewChild('thirdreimbsmntbicLabel') thirdreimbsmntbicLabel: SwtLabel;
  @ViewChild('thirdreimbsmntnameLabel') thirdreimbsmntnameLabel: SwtLabel;
  @ViewChild('valuedateLabel') valuedateLabel: SwtLabel;


  @ViewChild('releaseDateLabel') releaseDateLabel: SwtLabel;
  @ViewChild('waitingDateLabel') waitingDateLabel: SwtLabel;
  @ViewChild('reqreleasetimeLabel') reqreleasetimeLabel: SwtLabel;


  @ViewChild('categoryRulelabel') categoryRulelabel: SwtLabel;
  @ViewChild('categoryIDlabel') categoryIDlabel: SwtLabel;
  @ViewChild('accountGroupLabel') accountGroupLabel: SwtLabel;
  @ViewChild('accountGroupNameLabel') accountGroupNameLabel: SwtLabel;
  @ViewChild('cutOffTimeLabel') cutOffTimeLabel: SwtLabel;
  @ViewChild('accounttypeLabel') accounttypeLabel: SwtLabel;
  @ViewChild('urgentLabel') urgentLabel: SwtLabel;
  @ViewChild('foreferenceLabel') foreferenceLabel: SwtLabel;
  @ViewChild('sourcereferenceLabel') sourcereferenceLabel: SwtLabel;
  @ViewChild('stoppedtimeLabel') stoppedtimeLabel: SwtLabel;
  @ViewChild('stoppedUserLabel') stoppedUserLabel: SwtLabel;
  @ViewChild('stoppedReasonLabel') stoppedReasonLabel: SwtLabel;
  @ViewChild('unstoppedtimeLabel') unstoppedtimeLabel: SwtLabel;
  @ViewChild('unstoppedUserLabel') unstoppedUserLabel: SwtLabel;
  @ViewChild('unstoppedReasonLabel') unstoppedReasonLabel: SwtLabel;
  @ViewChild('blockedtimeLabel') blockedtimeLabel: SwtLabel;
  @ViewChild('blockedUserLabel') blockedUserLabel: SwtLabel;
  @ViewChild('blockedReasonLabel') blockedReasonLabel: SwtLabel;
  @ViewChild('cancelledtimeLabel') cancelledtimeLabel: SwtLabel;
  @ViewChild('cancelledUserLabel') cancelledUserLabel: SwtLabel;
  @ViewChild('cancelledReasonLabel') cancelledReasonLabel: SwtLabel;
  @ViewChild('releasedSourceLabel') releasedSourceLabel: SwtLabel;


  @ViewChild('paymentMessageArea') paymentMessageArea: SwtLabel;


  @ViewChild('stoppedLabel') stoppedLabel: SwtLabel;
  @ViewChild('unstoppedLabel') unstoppedLabel: SwtLabel;
  @ViewChild('blockedLabel') blockedLabel: SwtLabel;
  @ViewChild('cancelledLabel') cancelledLabel: SwtLabel;
  @ViewChild('releaseLabel') releaseLabel: SwtLabel;
  @ViewChild('waitingLabel') waitingLabel: SwtLabel;


  @ViewChild('logButton') logButton: SwtButton;
  @ViewChild('messageButton') messageButton: SwtButton;
  @ViewChild('messageOutButton') messageOutButton: SwtButton;
  @ViewChild('stopButton') stopButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;


  @ViewChild('releaseButton') releaseButton: SwtButton;
  @ViewChild('changeCatgButton') changeCatgButton: SwtButton;
  @ViewChild('spreadButton') spreadButton: SwtButton;
  @ViewChild('unStopButton') unStopButton: SwtButton;


    /******RadiButtons**********/
    @ViewChild('timeFrameRadioGroup') timeFrameRadioGroup: SwtRadioButtonGroup ;
    @ViewChild('radioC') radioC: SwtRadioItem ;
    @ViewChild('radioE') radioE: SwtRadioItem ;
    @ViewChild('radioS') radioS: SwtRadioItem ;

  private swtAlert: SwtAlert;


  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();
  public jsonReaderMethod = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  private saveData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];
  public screenName: string = null;
  public helpURL: string = null;
  private message: string = null;
  public title: string = null;
  private groupId: string = null;
  public searchQuery = "";
  public queryToDisplay = "";
  public errorLocation = 0;
  private win: TitleWindow;
  private moduleId = null;
  private paymentRequestId = null;
  private lastActionValue = "";
  private selectedTimeFrame = "E";
  private  menuAccess = 2;
  private location: string;




  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);

  }

  ngOnDestroy(): any {
    instanceElement = null;
  }


  ngOnInit(): void {
    this.enableDisableButtons(false);


    if(window.opener && window.opener.instanceElement ) {
      let params = (window.opener.instanceElement.getParamsFromParent) ? window.opener.instanceElement.getParamsFromParent() : "" ;
      if(params) {
        this.paymentRequestId =  ( params[0].payRequestId) ? params[0].payRequestId : "";
        this.selectedTimeFrame =  ( params[0].timeFrame) ? params[0].timeFrame : "";
      }
    }

    if (this.paymentRequestId) {
      this.paymentIdTextInput.visible = false;
      this.paymentIdTextInput.includeInLayout = false;
      this.paymentIdLabel.text = this.paymentRequestId;
    } else {
      this.paymentIdLabel.visible = false;
    }
    // this.screenName = "add";
    instanceElement = this;

  }


  /**
   * inputDataFault
   * param event:  FaultEvent
   * This is a callback , used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    this.swtAlert.error(event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail);
  }

  onLoad(eventTarget): void {
    try {
      this.enableDisableButtons(false);
      this.unStopButton.enabled = false;
      this.releaseButton.enabled = false;
      this.timeFrameRadioGroup.enabled = false;
      this.changeCatgButton.enabled = false;

      if (this.paymentRequestId) {
        this.requestParams = [];
        this.actionPath = "paymentDisplayPCM.do?";
        this.requestParams["moduleId"] = this.moduleId;
        if (this.screenName != "add") {
          this.requestParams["payReqId"] = this.paymentRequestId;
          this.actionMethod = "method=view";
        }

        if(!this.selectedTimeFrame)
          this.selectedTimeFrame = "E";

        if(eventTarget == "timeFrame") {
          this.selectedTimeFrame = this.timeFrameRadioGroup.selectedValue;
        }else {
          this.timeFrameRadioGroup.selectedValue  = this.selectedTimeFrame;
        }

        this.requestParams["selectedTimeFrame"] = this.selectedTimeFrame;
        this.inputData.cbStart = this.startOfComms.bind(this);
        this.inputData.cbStop = this.endOfComms.bind(this);
        this.inputData.cbResult = (data) => {
          this.inputDataResult(data);
        };
        // this.inputDataResult(data);
        this.inputData.cbFault = this.inputDataFault.bind(this);
        this.inputData.encodeURL = false;
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);

        // this.inputDataResult(data);
        this.saveData.cbFault = this.inputDataFault.bind(this);
        this.saveData.encodeURL = false;
      }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "onLoad", this.errorLocation);
    }

  }


  doShowMessageOutList() {
    // var newWindow = window.open("/messageOutSummary", 'Message Out Summary', 'height=500,width=750,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
    // if (window.focus) {
    //   newWindow.focus();
    // }


    ExternalInterface.call('openChildMessageOut', 'messageout');

  }
  doShowMessageList() {
    // try {
    //   var newWindow = window.open("/messages", 'Message Summary', 'height=500,width=850,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
    //   if (window.focus) {
    //     newWindow.focus();
    //   }
    // } catch (err) {
    //   console.log('err', err);
    // }

    ExternalInterface.call('openChildMessageList', 'message');
  }

  doShowStopRulesList() {
    // var newWindow = window.open("/stopSummary", 'Message Summary', 'height=500,width=700,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
    // if (window.focus) {
    //   newWindow.focus();
    // }

    ExternalInterface.call('openChildStopRulesList', 'stop');
  }
  doShowLogs() {
    // var newWindow = window.open("/payLogs", 'Message Summary', 'height=505,width=800,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
    // if (window.focus) {
    //   newWindow.focus();
    // }

    ExternalInterface.call('openChildLogs', 'logs');
  }


  inputDataResultFromChange(event): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {

        /* Get result as xml */
        this.jsonReaderMethod.setInputJSON(event);
        if (this.jsonReaderMethod.getRequestReplyMessage() == "ERROR") {
            this.swtAlert.error('Error occured during Save, please contact your admininstrator for more');
        } else if (this.jsonReaderMethod.getRequestReplyMessage() == "ERROR_PAYMENT_ENGINE") {
          this.swtAlert.error("Fail when sending response to the payment engine. Please check error logs for more details");
        } else if (this.jsonReaderMethod.getRequestReplyMessage() == "ARCHIVE_LOCATION" || this.jsonReaderMethod.getRequestReplyMessage() == "INCORRECT_PREVIOUS_STATUS") {
          console.log("error:   ", this.jsonReaderMethod.getRequestReplyMessage());
        }else {
            this.onLoad(null);
            if(window.opener && window.opener.instanceElement ) {
              window.opener.instanceElement.paySelectedIndex = null;
              if(window.opener.instanceElement.updataDataWithPagination)
                window.opener.instanceElement.updataDataWithPagination();
            }
          }
      }
    } catch (error) {
      console.log("error:   ", error);
      SwtUtil.logError(error, this.moduleId, "className", "inputDataResultFromChange", this.errorLocation);
    }
  }



  inputDataResult(event): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {

        /* Get result as xml */
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        /* Condition to check lastRecievedXML not equal to prevRecievedXML */
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          /* Condition to check request reply status is true*/
          if (this.jsonReader.getRequestReplyMessage() == "INVALID_ID") {
            this.swtAlert.error('Please enter a valid Id');
            this.enableDisableButtons(false);
          } else {
            //gets the help url
            this.helpURL = this.jsonReader.getSingletons().helpurl;
            if (!this.jsonReader.isDataBuilding()) {
              this.lastActionLabel.text = this.jsonReader.getSingletons().lastAction;
              this.accountidLabel.text = this.jsonReader.getSingletons().accountid;
              this.accountwithinstnameLabel.text = this.jsonReader.getSingletons().accountwithinstname;
              this.accountwithinstaccountLabel.text = this.jsonReader.getSingletons().accountwithinstaccount;
              this.accountwithinstbicLabel.text = this.jsonReader.getSingletons().accountwithinstbic;
              this.actionLabel.text = this.jsonReader.getSingletons().action;
              this.amountLabel.text = this.jsonReader.getSingletons().amount;
              this.boreferenceLabel.text = this.jsonReader.getSingletons().boreference;
              this.beneficiarycustnameLabel.text = this.jsonReader.getSingletons().beneficiarycustname;
              this.beneficiarycustaccountLabel.text = this.jsonReader.getSingletons().beneficiarycustaccount;
              this.beneficiarycustbicLabel.text = this.jsonReader.getSingletons().beneficiarycustbic;
              this.beneficiaryinstnameLabel.text = this.jsonReader.getSingletons().beneficiaryinstname;
              this.beneficiaryinstaccountLabel.text = this.jsonReader.getSingletons().beneficiaryinstaccount;
              this.beneficiaryinstbicLabel.text = this.jsonReader.getSingletons().beneficiaryinstbic;
              this.currencycodeLabel.text = this.jsonReader.getSingletons().currencycode;
              this.delivererscustodianaccountLabel.text = this.jsonReader.getSingletons().delivererscustodianaccount;
              this.delivererscustodianbicLabel.text = this.jsonReader.getSingletons().delivererscustodianbic;
              this.delivererscustodiannameLabel.text = this.jsonReader.getSingletons().delivererscustodianname;
              this.deliveryagentaccountLabel.text = this.jsonReader.getSingletons().deliveryagentaccount;
              this.deliveryagentbicLabel.text = this.jsonReader.getSingletons().deliveryagentbic;
              this.deliveryagentnameLabel.text = this.jsonReader.getSingletons().deliveryagentname;
              this.departmentLabel.text = this.jsonReader.getSingletons().department;
              this.entityidLabel.text = this.jsonReader.getSingletons().entityid;
              this.messageLabel.text = this.jsonReader.getSingletons().creationMsgId;
              this.gpiLabel.text = this.jsonReader.getSingletons().gpi;
              this.inputdateLabel.text = this.jsonReader.getSingletons().inputdate;
              this.intermediaryaccountLabel.text = this.jsonReader.getSingletons().intermediaryaccount;
              this.intermediarybicLabel.text = this.jsonReader.getSingletons().intermediarybic;
              this.intermediarynameLabel.text = this.jsonReader.getSingletons().intermediaryname;
              this.messagetypeLabel.text = this.jsonReader.getSingletons().messagetype;
              this.orderingcustaccountLabel.text = this.jsonReader.getSingletons().orderingcustaccount;
              this.orderingcustbeiLabel.text = this.jsonReader.getSingletons().orderingcustbei;
              this.orderingcustnameLabel.text = this.jsonReader.getSingletons().orderingcustname;
              this.orderinginstaccountLabel.text = this.jsonReader.getSingletons().orderinginstaccount;
              this.orderinginstbicLabel.text = this.jsonReader.getSingletons().orderinginstbic;
              this.orderinginstnameLabel.text = this.jsonReader.getSingletons().orderinginstname;
              // this.originatingsystemLabel.text = this.jsonReader.getSingletons().originatingsystem;
              this.paymenttypeLabel.text = this.jsonReader.getSingletons().paymenttype;
              this.placeofsettlementaccountLabel.text = this.jsonReader.getSingletons().placeofsettlementaccount;
              this.placeofsettlementbicLabel.text = this.jsonReader.getSingletons().placeofsettlementbic;
              this.placeofsettlementnameLabel.text = this.jsonReader.getSingletons().placeofsettlementname;
              this.receiverbicLabel.text = this.jsonReader.getSingletons().receiverbic;
              this.receiverscorresnameLabel.text = this.jsonReader.getSingletons().receiverscorresname;
              this.receiverscorresaccountLabel.text = this.jsonReader.getSingletons().receiverscorresaccount;
              this.receiverscorresbicLabel.text = this.jsonReader.getSingletons().receiverscorresbic;
              this.paymentreferenceLabel.text = this.jsonReader.getSingletons().paymentreference;
              this.relatedreferenceLabel.text = this.jsonReader.getSingletons().relatedreference;
              this.selleraccountLabel.text = this.jsonReader.getSingletons().selleraccount;
              this.sellerbicLabel.text = this.jsonReader.getSingletons().sellerbic;
              this.sellernameLabel.text = this.jsonReader.getSingletons().sellername;
              this.senderbicLabel.text = this.jsonReader.getSingletons().senderbic;
              this.senderscorresnameLabel.text = this.jsonReader.getSingletons().senderscorresname;
              this.senderreceiverinfoLabel.htmlText = this.jsonReader.getSingletons().senderreceiverinfo;
              this.senderscorresaccountLabel.text = this.jsonReader.getSingletons().senderscorresaccount;
              this.senderscorresbicLabel.text = this.jsonReader.getSingletons().senderscorresbic;
              this.sourceidLabel.text = this.jsonReader.getSingletons().sourceid;
              this.releasedByLabel.text = this.jsonReader.getSingletons().releasedBy;
              this.sourceurgencyindicatorLabel.text = this.jsonReader.getSingletons().urgentSpreadable;
              this.entitysubidLabel.text = this.jsonReader.getSingletons().entitysubid;
              this.thirdreimbsmntaccountLabel.text = this.jsonReader.getSingletons().thirdreimbsmntaccount;
              this.thirdreimbsmntbicLabel.text = this.jsonReader.getSingletons().thirdreimbsmntbic;
              this.thirdreimbsmntnameLabel.text = this.jsonReader.getSingletons().thirdreimbsmntname;
              this.valuedateLabel.text = this.jsonReader.getSingletons().valuedate;



              this.categoryRulelabel.text = this.jsonReader.getSingletons().categoryRulelabel;
              this.categoryIDlabel.text = this.jsonReader.getSingletons().categoryIDlabel;
              this.accountGroupLabel.text = this.jsonReader.getSingletons().accountGroupLabel;
              this.accountGroupNameLabel.text = this.jsonReader.getSingletons().accountGroupNameLabel;
              this.cutOffTimeLabel.text = this.jsonReader.getSingletons().cutOffTimeLabel;
              this.accounttypeLabel.text = this.jsonReader.getSingletons().accounttypeLabel;
              this.urgentLabel.text = this.jsonReader.getSingletons().sourceurgencyindicator;
              this.foreferenceLabel.text = this.jsonReader.getSingletons().foreferenceLabel;
              this.sourcereferenceLabel.text = this.jsonReader.getSingletons().sourcereferenceLabel;
              this.stoppedtimeLabel.text = this.jsonReader.getSingletons().stoppedtimeLabel;

              this.releaseDateLabel.text = this.jsonReader.getSingletons().releaseDateLabel;
              this.waitingDateLabel.text = this.jsonReader.getSingletons().waitingDateLabel;
              this.reqreleasetimeLabel.text = this.jsonReader.getSingletons().reqreleasetimeLabel;


              this.stoppedUserLabel.text = this.jsonReader.getSingletons().stoppedUserLabel;
              this.stoppedReasonLabel.text = this.jsonReader.getSingletons().stoppedReasonLabel;
              this.unstoppedtimeLabel.text = this.jsonReader.getSingletons().unstoppedtimeLabel;
              this.unstoppedUserLabel.text = this.jsonReader.getSingletons().unstoppedUserLabel;
              this.unstoppedReasonLabel.text = this.jsonReader.getSingletons().unstoppedReasonLabel;
              this.blockedtimeLabel.text = this.jsonReader.getSingletons().blockedtimeLabel;
              this.blockedUserLabel.text = this.jsonReader.getSingletons().blockedUserLabel;
              this.blockedReasonLabel.text = this.jsonReader.getSingletons().blockedReasonLabel;
              this.cancelledtimeLabel.text = this.jsonReader.getSingletons().cancelledtimeLabel;
              this.cancelledUserLabel.text = this.jsonReader.getSingletons().cancelledUserLabel;
              this.cancelledReasonLabel.text = this.jsonReader.getSingletons().cancelledReasonLabel;
              this.menuAccess = Number(this.jsonReader.getScreenAttributes()["menuaccess"]);
              this.location = this.jsonReader.getSingletons().location;

              if(this.jsonReader.getSingletons().paymentMessage) {

                 var formattedXml = prettyData.pd.xml(StringUtils.decode64(this.jsonReader.getSingletons().paymentMessage));
                 formattedXml = this.htmlEntities(formattedXml);
                
                 this.paymentMessageArea.htmlText = formattedXml;
              }

              this.lastActionValue  = this.jsonReader.getSingletons().lastActionValue;
            
              this.resetTextInputsColor();

              
            if (this.lastActionValue == "N" || this.lastActionValue == "W"  ) {
              this.waitingLabel.color = "green";
              this.waitingDateLabel.color="green";
            } else if (this.lastActionValue == "S") {
              this.stoppedtimeLabel.color ="green";
              this.stoppedUserLabel.color ="green";
              this.stoppedReasonLabel.color ="green";
              this.stoppedLabel.color = "green";

            } else if (this.lastActionValue == "B") {
              this.blockedtimeLabel.color ="green";
              this.blockedUserLabel.color ="green";
              this.blockedReasonLabel.color ="green";
              this.blockedLabel.color = "green";

            } else if (this.lastActionValue == "C") {
              this.cancelledtimeLabel.color ="green";
              this.cancelledUserLabel.color ="green";
              this.cancelledReasonLabel.color ="green";
              this.cancelledLabel.color = "green";

            } else if (this.lastActionValue == "R") {
                this.releasedSourceLabel.color = "green";
                this.releasedByLabel.color="green";
                this.releaseDateLabel.color="green";
                this.releaseLabel.color = "green";
            } 
            if( this.location == "L") {

              if(this.lastActionValue =='S')
                this.unStopButton.enabled =  this.menuAccess == 0;

              /*if(this.lastActionValue =='W')
                this.changeCatgButton.enabled = true;*/


              if (this.lastActionValue == 'B' || this.lastActionValue =='S' || this.lastActionValue =='W') {
              // if(this.jsonReader.getSingletons().isReleasable == "true")
                  this.releaseButton.enabled = this.menuAccess == 0;
              }
            }
              this.enableDisableButtons(true);
            } else {
            }
            this.timeFrameRadioGroup.enabled = true;

          }
        }
      }
    } catch (error) {
      console.log("error:   ", error);
      SwtUtil.logError(error, this.moduleId, "className", "inputDataResult", this.errorLocation);
    }
  }

  htmlEntities(str) {
    return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
    replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
}

  getParamsFromParent() {


    // this.screenName =  paramsFromParent[0].screenName;
    // this.spreadId =  paramsFromParent[0].spreadId;

    let spreadId = this.jsonReader.getSingletons().accountGroupSpreadId;
    let params = [
      { paymentRequestId: this.paymentRequestId, 
        spreadId : spreadId,
        screenName: 'view' },
    ];
    return params;
  }

  /**
   * keyDownOnDateEventHandler
   *
   * @param event:KeyboardEvent
   *
   * This function is used to validate the from and to date when the key is down
   */
  keyDownOnPaymentRequestEventHandler(event) {

    try {
      let eventString = this.paymentIdTextInput.text;

      if (event.keyCode == Keyboard.ENTER) {
        // validate the date.
        this.validatePaymentId(eventString);
      }
    }
    catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "keyDownOnDateEventHandler", this.errorLocation);
    }
  }


  validatePaymentId(paymentId) {

    if (paymentId) {
      this.paymentRequestId = paymentId;
      this.onLoad(null);
    } else {
      this.swtAlert.error('Please enter a valid Id');
    }

  }


  enableDisableButtons(value) {
    this.logButton.enabled = value;
    this.messageButton.enabled = value;
    this.messageOutButton.enabled = value;
    this.stopButton.enabled = value;
    this.spreadButton.enabled = value;

  }

  resetTextInputsColor(): void {
    this.waitingDateLabel.color="#173553";
    this.stoppedtimeLabel.color ="#173553";
    this.stoppedUserLabel.color ="#173553";
    this.stoppedReasonLabel.color ="#173553";

    this.blockedtimeLabel.color ="#173553";
    this.blockedUserLabel.color ="#173553";
    this.blockedReasonLabel.color ="#173553";
    this.cancelledtimeLabel.color ="#173553";
    this.cancelledUserLabel.color ="#173553";
    this.cancelledReasonLabel.color ="#173553";
    this.releaseDateLabel.color="#173553";

    this.stoppedLabel.color = "#173553";
    this.unstoppedLabel.color = "#173553";
    this.blockedLabel.color = "#173553";
    this.cancelledLabel.color = "#173553";
    this.releaseLabel.color = "#173553";
    this.waitingLabel.color = "#173553";

    this.releasedSourceLabel.color = "#173553";
    this.releasedByLabel.color="#173553";

  }


  doReleasePaymentEventHandler(event){
      if (event.detail === Alert.YES) {
      this.actionMethod = 'method=unStopReleasePayment';
      this.actionPath = 'paymentDisplayPCM.do?';
      this.inputData.cbResult = (event) => {
        this.inputDataResultFromChange(event);
      };
      if(this.lastActionValue == 'B' || this.lastActionValue =='S' || this.lastActionValue =='W'){
        this.requestParams['paymentId'] = this.paymentRequestId.toString();
        this.requestParams['previousStatus'] =  this.lastActionValue;
      }
      this.requestParams['paymentAction'] =  "R";
      this.requestParams['location'] = this.location ;

      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    }
  }


  releasePayment(event) {
    try {
      /**if pay request is blocked check if is blocked or blocked and stopped*/
      if(this.blockedReasonLabel.text ||this.lastActionValue =='S') {
        this.checkblockedStoppedPay(this.paymentRequestId);
      } else {
        this.swtAlert.confirm('Do you wish to Release this payment?', '', Alert.YES | Alert.NO, null, this.doReleasePaymentEventHandler.bind(this));
      }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'doDeletePCStopRule', this.errorLocation);
    }
  }
  checkblockedStoppedPay(payReqId) : void {
      this.requestParams=[];
      this.actionMethod = 'method=checkBlockedStoppedPay';
      this.actionPath = 'paymentDisplayPCM.do?';
      this.logicUpdate.cbResult = (event) => {
        this.isBlockedStoppedResult(event);
      };
      this.requestParams['paymentId'] = (payReqId) ? payReqId.toString() : "";
      this.requestParams['location'] = this.location ;
      this.logicUpdate.cbStart = this.startOfComms.bind(this);
      this.logicUpdate.cbStop = this.endOfComms.bind(this);
      this.logicUpdate.encodeURL = false;
      this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
      this.logicUpdate.send(this.requestParams);


  }

  isBlockedStoppedResult(event): void {
    try {
      let message = "";
      let messageDuplicated = "";
      let stopRules = "";
      let jsonSingeltons: string;
      let id: string;
      let isBlockedandStopped =[];
      let stopRule = [];
      let stopRuleTime = [];
      let messageBlockedPay = SwtUtil.getPredictMessage('dashboardDetails.alert.releaseBlockedPay', null);
      let messageLockedPay = SwtUtil.getPredictMessage('dashboardDetails.alert.lockedPay', null);
      let payStopped = SwtUtil.getPredictMessage('dashboardDetails.paymentStopped', null);
      let payWasStopped = SwtUtil.getPredictMessage('dashboardDetails.paymentWasStopped', null);
      let until = SwtUtil.getPredictMessage('dashboardDetails.until', null);
      let wishContinue = SwtUtil.getPredictMessage('dashboardDetails.wishContinue', null);
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = event;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        if (JsonResult.getRequestReplyMessage() !== "ARCHIVE_LOCATION" || JsonResult.getRequestReplyMessage() !==  "INCORRECT_PREVIOUS_STATUS" ) {
          jsonSingeltons = JsonResult.getSingletons().payIdRule.replace('{', '').replace('}', '');
          let arrayOfIdRule = [];
          if (jsonSingeltons) {
            arrayOfIdRule.push(jsonSingeltons.split('#'));
            arrayOfIdRule = arrayOfIdRule[0];
            for (let i = 0; i < arrayOfIdRule.length; i++) {
              isBlockedandStopped.push(arrayOfIdRule[i].split('|')[0]);
              stopRule.push(arrayOfIdRule[i].split('|')[1]);
              stopRuleTime.push(arrayOfIdRule[i].split('|')[2]);
            }
            for (let j = 0; j < isBlockedandStopped.length; j++ ) {
              if(isBlockedandStopped[j] == "Y" && stopRule[j] != undefined) {
                messageDuplicated = messageDuplicated + payStopped + " " + stopRule[j] + "<br/>";
              } else if(isBlockedandStopped[j] == "P" && stopRule[j] != undefined && stopRuleTime[j] != undefined ) {
                messageDuplicated = messageDuplicated + payWasStopped + " " + stopRule[j] + " " + until + " "+ stopRuleTime[j] + "<br/>";
              }
              else if(isBlockedandStopped[j] == "N") {
                message = messageBlockedPay;
                this.swtAlert.confirm(message, "", Alert.YES |  Alert.CANCEL, null, this.doReleasePaymentEventHandler.bind(this));
              } else if(isBlockedandStopped[j] == "L") {
                message = messageLockedPay;
                this.swtAlert.warning(message);
              }
            }
            if(messageDuplicated != '') {
              messageDuplicated = messageDuplicated +  wishContinue;
              if(this.lastActionValue =='S')
                this.swtAlert.confirm(messageDuplicated, "", Alert.YES |  Alert.CANCEL, null, this.doReleasePaymentEventHandler.bind(this));
              else
                this.swtAlert.confirm(messageDuplicated, "", Alert.YES |  Alert.CANCEL, null, this.secondConfirmRelease.bind(this));
            }
          }
        }else{
          console.log("error:   ", this.jsonReaderMethod.getRequestReplyMessage());
        }



      }
    } catch (e) {
      // log the error in ERROR LOG
    }
  }
  secondConfirmRelease(event): void {
    let message: string = null;
    if(event.detail == Alert.YES ) {
      message = "This payment is blocked. Do you wish to release ?";
      this.swtAlert.confirm(message, "", Alert.YES |  Alert.CANCEL, null, this.doReleasePaymentEventHandler.bind(this));
    }
  }
  unStopPayEventHandler(event){
    if (event.detail === Alert.YES) {
      this.actionMethod = 'method=unStopReleasePayment';
      this.actionPath = 'paymentDisplayPCM.do?';
      this.inputData.cbResult = (event) => {
        this.inputDataResultFromChange(event);
      };
      if(this.lastActionValue == 'S'){
        this.requestParams['paymentId'] =  this.paymentRequestId;
        this.requestParams['previousStatus'] =  this.lastActionValue;
      }

      this.requestParams['paymentAction'] =  "U";
      this.requestParams['location'] = this.location ;


      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    }
  }
  unStopPay(event){
      try {
      this.swtAlert.confirm('Do you wish to Unstop this payment?', '', Alert.YES | Alert.NO, null, this.unStopPayEventHandler.bind(this));
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'doDeletePCStopRule', this.errorLocation);
    }
    
  }

  spreadDisplay(event){
    try {
        let spreadId = this.jsonReader.getSingletons().accountGroupSpreadId;
        if(spreadId){
          // var newWindow = window.open("/spreadProfilesAdd", 'Display Spread', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
          // if (window.focus) {
          //   newWindow.focus();
          // }

          ExternalInterface.call('openChildSpreadProfileAdd', 'spreadProfilesView');

        } else {
          this.swtAlert.warning('the selected Account Group is not provided with Spread Profile ID');
       }

    } catch(e) {
      console.log('eeeeee', e)
    }

   // ExternalInterface.call('spreadDisplay');
  }

  changeCategory(event){
    try {
      /*this.win = SwtPopUpManager.createPopUp(this,
        CategoryList,
        {
          title: "Change Payment Category",
          comboDataProvider : this.jsonReader.getSelects()['select'].option,
          selectedCategoryLabel : this.jsonReader.getSingletons().categoryIDlabel

        });
      this.win.width = "600";
      this.win.height = "180";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win.display();*/

    } catch (e) {
      console.log("e",e);
      
    }

  }

  refreshParent(selectedCategory): void  {
    //update the category
    this.requestParams = [];
    this.actionMethod = 'method=updatePaymentCategory';
    this.actionPath = 'dashboardPCM.do?';
    this.inputData.cbResult = (event) => {
      this.inputDataResultFromChange(event);
    };
    this.requestParams['paymentId'] = this.paymentRequestId;
    this.requestParams['categoryId'] = selectedCategory;
    this.requestParams['method'] = 'updateCategory';
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }


  /**
   * startOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    // this.loadingImage.setVisible(true);
  }

  /**
   * endOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    // this.loadingImage.setVisible(false);
  }


  /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(Button)
   */
  keyDownEventHandler(event): void {
    try {
      const eventstring = Object(focusManager.getFocus()).name;
      if ((event.keyCode === Keyboard.ENTER)) {
        if (eventstring === 'addButton') {
        } else if (eventstring === 'viewButton') {
        } else if (eventstring === 'closeButton') {
          this.closeCurrentTab(event);
        } else if (eventstring === 'helpIcon') {
          this.doHelp();
        } else if (eventstring === 'deleteButton') {
          // this.doDeletePCPriorityMaintenance(event);
        } else if (eventstring === 'cancelButton') {
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'keyDownEventHandler', this.errorLocation);
    }
  }



  doHelp() {
    try {
      ExternalInterface.call("help");
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }

  }


  /**
   * closeCurrentTab
   *
   * Function called when close button is called
   *
   * @param event:Event
   */
  closeCurrentTab(event): void {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'ClassName', 'refreshGrid', this.errorLocation);
    }

  }

  /**
  * dispose
  *
  * This is an event handler, used to close the current tab/window
  */
  dispose(): void {
    try {
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      ExternalInterface.call("close");
      if (this.titleWindow) {
        this.close();
      } else {
        window.close();
      }
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'dispose', this.errorLocation);
    }
  }

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: PaymentRequestDisplay }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PaymentRequestDisplay],
  entryComponents: []
})
export class PaymentRequestDisplayModule { }
