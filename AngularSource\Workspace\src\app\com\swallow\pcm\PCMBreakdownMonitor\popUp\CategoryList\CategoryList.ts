import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {CommonService, SwtAlert, SwtComboBox, SwtLabel, SwtModule} from "swt-tool-box";

@Component({
  selector: 'app-category-list',
  templateUrl: './CategoryList.html',
  styleUrls: ['./CategoryList.css']
})
export class CategoryList extends SwtModule implements OnInit {
  public comboDataProvider: any;
  public selectedCategoryLabel: string = null;
  public selectedPayId: string = null;
  @ViewChild('categoryCombo') categoryCombo: SwtComboBox;
  @ViewChild('selectedCategory') selectedCategory: SwtLabel;

  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
  }

  ngOnInit() {
  }
  onLoad(): void  {
    this.categoryCombo.setComboData(this.comboDataProvider, true);
    if(this.selectedCategoryLabel) {
      this.categoryCombo.selectedLabel = this.selectedCategoryLabel;
    }
    this.selectedCategory.text = this.categoryCombo.selectedItem.value;
  }
  changeCombo() : void {
    this.selectedCategory.text = this.categoryCombo.selectedItem.value;

  }
  popupClosed(): void {
    this.close();
  }
  save() : void {
    this.parentDocument.refreshParent(this.categoryCombo.selectedLabel, this.selectedPayId);
    this.close();
  }

}
