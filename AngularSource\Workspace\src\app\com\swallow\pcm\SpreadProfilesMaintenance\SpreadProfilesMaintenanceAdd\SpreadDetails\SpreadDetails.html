<SwtModule (close)='popupClosed()' (creationComplete)='onLoad()' height='100%' width='600' paddingLeft="10">
  <VBox width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width='100%' height="89%">
      <VBox width='100%' height='100%'>
          <Grid width='100%' height='35%'>
            <GridRow>
              <GridItem width="20%">
                <SwtLabel  text="Process Name*" ></SwtLabel>
              </GridItem>
              <GridItem width="80%">
                <SwtTextInput id="processNameInput"
                            #processNameInput
                            toolTip="Process Name"
                            height="22"
                            width="200"
                            maxChars="30"
                            required="true">
                </SwtTextInput>
            </GridItem>
            </GridRow>
            <GridRow>
              <GridItem width="20%">
                <SwtLabel  text="Time*">
                </SwtLabel>
              </GridItem>
              <GridItem width="80%">
                <SwtTextInput id="timeInput"
                              #timeInput
                              toolTip="Time"
                              height="22"
                              width="50"
                              pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"
                              maxChars="5"
                              textAlign="center"
                              (focusOut)="validateTime(timeInput)"
                              required="true">
                </SwtTextInput>
              </GridItem>
            </GridRow>
            <GridRow>
              <GridItem width="20%">
                <SwtLabel  text="Target %*">
                </SwtLabel>
              </GridItem>
              <GridItem width="80%">
                <SwtStepper id="targetInput"
                            #targetInput
                            minimum="1"
                            maximum="100"
                            toolTip="Target"
                            width="24"
                            required="true">
                </SwtStepper>
              </GridItem>
            </GridRow>
            <GridRow>
              <GridItem width="20%">
                <SwtLabel  text="Process Category">
                </SwtLabel>
              </GridItem>
              <GridItem width="80%">
                <SwtComboBox id="processCategoryComboBox"
                             #processCategoryComboBox
                             dataLabel="processCategoryList"
                             width="150"
                             toolTip="Select Process Category"
                             (change)="changeComboProcessCategory()">
                </SwtComboBox>
              </GridItem>
            </GridRow>
            <GridRow>
              <GridItem width="20%">
              </GridItem>
              <GridItem width="80%">
                <SwtLabel id="informationLabel" #informationLabel>
                </SwtLabel>
              </GridItem>
            </GridRow>
        </Grid>
          <HBox height="55%" width="100%">
            <SwtPanel height="100%" width="100%" title="Categories" paddingTop="5">
              <SwtCanvas id="categoryCanvasGrid"
                        #categoryCanvasGrid
                        height="100%"
                        width="100%"
                        border="false">
              </SwtCanvas>
            </SwtPanel>
          </HBox>
          <HBox  height='5%' paddingLeft="15" paddingTop="5">
            <SwtCheckBox #selectAll id="selectAll" selected="false" paddingTop="5" (change)="selectDeselectAll($event)">
            </SwtCheckBox>
            <SwtLabel text="Select All" paddingRight="5">
            </SwtLabel>
          </HBox>
      </VBox>
    </SwtCanvas>
    <SwtCanvas id="canvasContainer" width='100%' height='9%'>
      <HBox width="100%">
        <HBox width="100%" height="100%" style="margin-top: 3px; margin-left: 5px">
          <SwtButton #okButton (click)="save()" (keyDown)="keyDownEventHandler($event)" id="okButton" width="70">
          </SwtButton>
          <SwtButton buttonMode="true" id="cancelButton" width="70" #cancelButton (click)="closeBtn()"
            (keyDown)="keyDownEventHandler($event)"></SwtButton>
        </HBox>
      </HBox>
      <HBox horizontalAlign="right" paddingRight="10" marginTop="5">
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
        <SwtButton [buttonMode]="true" 
                    #printButton 
                    id="printButton" 
                    styleName="printIcon" 
                    (click)="printPage()"
                    (keyDown)="keyDownEventHandler($event)">
        </SwtButton>
        <SwtHelpButton id="helpIcon" 
                        [buttonMode]="true" 
                        enabled="true" 
                        helpFile="spread-profile" 
                        (click)="doHelp()">
        </SwtHelpButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
