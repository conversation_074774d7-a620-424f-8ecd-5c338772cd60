import {Component, ElementRef, ViewChild, OnInit} from '@angular/core';
import {
  SwtAlert,
  SwtUtil,
  SwtTextInput,
  CommonService,
  SwtModule,
  SwtLabel, SwtComboBox, SwtNumericInput, SwtCheckBox, SwtRadioItem, SwtRadioButtonGroup, SwtCanvas, SwtCommonGrid, SwtButton, SwtFieldSet, ExternalInterface, Tab, Alert, StringUtils
} from 'swt-tool-box';
declare  function validateFormatTime(strField): any;
declare  function validateFormatTimeSecond(strField): any;
declare var instanceElement: any;
declare var require: any;
var prettyData = require('pretty-data');
@Component({
  selector: 'app-general',
  templateUrl: './General.html',
  styleUrls: ['./General.css']
})
export class General extends SwtModule implements OnInit{
  @ViewChild('recordInsLbl') recordInsLbl: SwtLabel;
  @ViewChild('categoryLbl') categoryLbl: SwtLabel;
  @ViewChild('selectedCategLbl') selectedCategLbl: SwtLabel;
  @ViewChild('orderLbl') orderLbl: SwtLabel;
  @ViewChild('runLbl') runLbl: SwtLabel;
  @ViewChild('startLbl') startLbl: SwtLabel;
  @ViewChild('endLbl') endLbl: SwtLabel;
  @ViewChild('emailLbl') emailLbl: SwtLabel; 
  @ViewChild('scheduledDesc') scheduledDesc: SwtLabel; 
  @ViewChild('CreateInsDesc') CreateInsDesc: SwtLabel;
  @ViewChild('schedParamsDesc') schedParamsDesc: SwtLabel;
  
  @ViewChild('categoryCombo') categoryCombo: SwtComboBox;
  
  @ViewChild('recordCheck') recordCheck: SwtCheckBox;

  @ViewChild('orderTxt') orderTxt: SwtNumericInput;
  @ViewChild('emailTxt') emailTxt: SwtNumericInput;
  @ViewChild('runTxt') runTxt: SwtTextInput;
  @ViewChild('startTxt') startTxt: SwtTextInput;
  @ViewChild('endTxt') endTxt: SwtTextInput;

  /***********SwtRadioButtonGroup***********/
  @ViewChild('typeOptions') typeOptions: SwtRadioButtonGroup;
  @ViewChild('cyclic') cyclic: SwtRadioItem;
  @ViewChild('scheduled') scheduled: SwtRadioItem;
  @ViewChild('createInst') createInst: SwtRadioItem;

 /***********SwtButton***********/
 @ViewChild('addButton') addButton: SwtButton;
 @ViewChild('changeButton') changeButton: SwtButton;
 @ViewChild('deleteButton') deleteButton: SwtButton;
 @ViewChild('configButton') configButton: SwtButton; 
 @ViewChild('defParams') defParams: SwtButton;

 @ViewChild('schedGridContainer') schedGridContainer: SwtCanvas;

 @ViewChild('fieldSet') fieldSet: SwtFieldSet;
  private swtalert: SwtAlert;
  public schedGrid: SwtCommonGrid;
  public xmlParams;
  public scheduleXml;
  public scheduleGridData=[];
  public generalGridData=[];
  public scheduleParameters=[];
  public oldSchedRows = [];
  public gridDataValues;
  public savedApiParams;
  public operation="";
  public updatedRowIndex:number=-1;
  public apiRequiredCols=[];
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.schedGrid = <SwtCommonGrid>this.schedGridContainer.addChild(SwtCommonGrid);
    this.recordInsLbl.text = SwtUtil.getPredictMessage('scenario.recordInsLbl', null);
    this.categoryLbl.text = SwtUtil.getPredictMessage('scenario.category', null);
    this.orderLbl.text = SwtUtil.getPredictMessage('scenario.displayOrder', null);
    this.runLbl.text = SwtUtil.getPredictMessage('scenario.runEvery', null);
    this.startLbl.text = SwtUtil.getPredictMessage('scenario.startTime', null);
    this.endLbl.text = SwtUtil.getPredictMessage('scenario.endTime', null);
    this.emailLbl.text = SwtUtil.getPredictMessage('scenario.emailWhenDiff', null);
    this.cyclic.label = SwtUtil.getPredictMessage('scenario.cyclic', null);
    this.scheduled.label = SwtUtil.getPredictMessage('scenario.scheduled', null);
    this.createInst.label = SwtUtil.getPredictMessage('scenario.createInst', null);
    this.scheduledDesc.text = SwtUtil.getPredictMessage('scenario.scheduledDesc', null);
    this.CreateInsDesc.text = SwtUtil.getPredictMessage('scenario.CreateInsDesc', null);
    this.addButton.label = SwtUtil.getPredictMessage('scenario.addLabel', null);
    this.changeButton.label = SwtUtil.getPredictMessage('scenario.changeLabel', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('scenario.deleteLabel', null);
    this.configButton.label = SwtUtil.getPredictMessage('scenario.configLabel', null); 
    this.defParams.label = SwtUtil.getPredictMessage('scenario.defParamsLabel', null);
    this.schedParamsDesc.text=SwtUtil.getPredictMessage('scenario.schedParamsDesc', null);
    this.fieldSet.legendText = SwtUtil.getPredictMessage('scenario.fieldSet.legendText', null);

    /***Tooltips***/
    this.recordCheck.toolTip= SwtUtil.getPredictMessage('tooltip.recordCheck', null);
    this.categoryCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectCategory', null);
    this.orderTxt.toolTip = SwtUtil.getPredictMessage('tooltip.scenarioDisplayOrder', null);
    this.runTxt.toolTip = SwtUtil.getPredictMessage('tooltip.scenarioRunEvery', null);
    this.startTxt.toolTip = SwtUtil.getPredictMessage('tooltip.scenarioStartTime', null);
    this.endTxt.toolTip = SwtUtil.getPredictMessage('tooltip.scenarioEndTime', null);
    this.emailTxt.toolTip = SwtUtil.getPredictMessage('tooltip.scenarioEmailWhenDiff', null);
    this.cyclic.toolTip = SwtUtil.getPredictMessage('tooltip.scenarioCyclic', null);
    this.scheduled.toolTip = SwtUtil.getPredictMessage('tooltip.scenarioScheduled', null);
    this.createInst.toolTip = SwtUtil.getPredictMessage('tooltip.scenarioCreateInst', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('toolTip.scenarioAdd', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('toolTip.scenarioChange', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('toolTip.scenarioDelete', null);
    this.configButton.toolTip = SwtUtil.getPredictMessage('toolTip.scenarioConfig', null);
    this.defParams.toolTip = SwtUtil.getPredictMessage('toolTip.scenarioDefParams', null);

  }


  public static ngOnDestroy(): any {
    instanceElement = null;
  }
  onLoad() {
    instanceElement = this;
    this.changeCategoryList();
    this.enableDisableComponents();
    this.schedGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    };
  }

  cellClickEventHandler(event): void {
    this.updatedRowIndex=this.schedGrid.selectedIndex;
    if (this.schedGrid.selectedIndex >= 0 && this.scheduled.selected) {
      this.changeButton.enabled = true;
      this.changeButton.buttonMode = true;
      this.deleteButton.enabled = true;
      this.deleteButton.buttonMode = true;
    } else {
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false;
      this.deleteButton.enabled = false;
      this.deleteButton.buttonMode = false;

    }
  }


  changeCategoryList() {
    this.selectedCategLbl.text = this.categoryCombo.selectedValue;
  }

  validateTime(textInput): any {
    let validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text  && validateFormatTime(textInput) == false) {
      this.swtalert.warning(validTimeMessage, null );
      textInput.text = "";
      return false;
    } else  {
      textInput.text = textInput.text.substring(0,5);
      return true;

    }
  }
  validateTimeOfRun(textInput) {
    let validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00:00";
    }
    if (textInput.text  && validateFormatTimeSecond(textInput) == false) {
      this.swtalert.warning(validTimeMessage, null );
      textInput.text = "";
      return false;
    } else  {
      textInput.text = textInput.text.substring(0,8);
      return true;

    }
  }

  openDefParamScreen(){
    ExternalInterface.call('openDefParamScreen','defineParameters');
  }


  addScheduleDetail(){
    this.operation = "add";
    ExternalInterface.call('openSchedDetailScreen','openSchedule'); 
  }


  changeScheduleDetail(){
    this.operation = "change";
    ExternalInterface.call('openSchedDetailScreen','openSchedule'); 
  }

  openConfigScreen(){
    ExternalInterface.call('openConfigScreen','ConfigureParams');  
  }

  sendGeneralDataToDefParams() {
    let params: any;
      params = {
        "gridData": this.parentDocument.lastRecievedJSON.scenarioDetails.defParamsGrid,
        "scenarioId": ((this.parentDocument.scenarioIdTxt.text ? this.parentDocument.scenarioIdTxt.text : "")),
        "listMapFrom":this.parentDocument.gridComboVal,
        "xmlData":this.xmlParams?this.xmlParams:"",
        "methodName":this.parentDocument.methodName,
        "genaralGridData":this.schedGrid.gridData,
      };
    
    return params;
  }

  sendGeneralDataToSchedule() {
    let params: any;
      params = {
        "gridData": this.parentDocument.lastRecievedJSON.scenarioDetails.scheduleGrid,
        "scenarioId": ((this.parentDocument.scenarioIdTxt.text ? this.parentDocument.scenarioIdTxt.text : "")),
        "xmlData":this.xmlParams?this.xmlParams:"",
        "scheduleGridData":this.scheduleGridData?this.scheduleGridData:[],
        "methodName":this.operation,
        "selectedItem":this.schedGrid.selectedItem?this.schedGrid.selectedItem:"",
        "genaralGridData":this.schedGrid.gridData,
      };
    
    return params;
  }

  sendGeneralDataToConfigScreen() {

    let params: any;
      params = {
        "apiTypeList": this.parentDocument.jsonReader.getSelects()['select'].find(x => x.id == "apiTypeList"),
        "scenarioId": ((this.parentDocument.scenarioIdTxt.text ? this.parentDocument.scenarioIdTxt.text : "")),
        "methodName":this.parentDocument.methodName,
      };
    
    return params;
  }
 public selectedType="C";
  enableDisableComponents() {
    if(this.parentDocument.methodName!="view" && !this.parentDocument.systemCheck.selected){
    if (this.scheduled.selected) {
      this.selectedType="S";
      this.defParams.enabled = true;
      this.defParams.buttonMode = true;
      this.addButton.enabled = true;
      this.addButton.buttonMode = true;
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false;
      this.deleteButton.enabled = false;
      this.deleteButton.buttonMode = false;
      this.configButton.enabled = false;
      this.runTxt.enabled = false;
      this.startTxt.enabled = false;
      this.endTxt.enabled = false;
      this.CreateInsDesc.text=SwtUtil.getPredictMessage('scenario.CreateInsDesc', null);
    } else if (this.createInst.selected) {
      this.selectedType="A";
      this.defParams.enabled = false;
      this.defParams.buttonMode = false;
      this.addButton.enabled = false;
      this.addButton.buttonMode = false;
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false;
      this.deleteButton.enabled = false;
      this.deleteButton.buttonMode = false;
      this.configButton.enabled = true;
      this.runTxt.enabled = false;
      this.startTxt.enabled = false;
      this.endTxt.enabled = false;
      this.CreateInsDesc.text=this.apiRequiredCols.length>0?SwtUtil.getPredictMessage('scenario.CreateInsDescFull', null):SwtUtil.getPredictMessage('scenario.CreateInsDesc', null);
    } else {
      this.selectedType="C";
      this.defParams.enabled = false;
      this.defParams.buttonMode = false;
      this.addButton.enabled = false;
      this.addButton.buttonMode = false;
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false;
      this.deleteButton.enabled = false;
      this.deleteButton.buttonMode = false;
      this.configButton.enabled = false;
      this.startTxt.enabled = true;
      this.endTxt.enabled = true;
      this.runTxt.enabled = true;
      this.CreateInsDesc.text=SwtUtil.getPredictMessage('scenario.CreateInsDesc', null);
    }
  }
  }

  changeComponentStatus(){
  this.schedGrid.selectedIndex=-1;
  this.enableDisableComponents();
  }

  refreshGeneralGrid(){
  if(this.operation=="add"){
    let param=this.generalGridData[0].parameters.content;
    let time=this.generalGridData[0].time.content;
    this.oldSchedRows.push({time: { clickable: false, content:time, negative: false },parameters: { clickable: false, content: param, negative: false }});
    this.schedGrid.gridData={ size: this.oldSchedRows.length, row: this.oldSchedRows };
    this.schedGrid.refresh();
  /*this.parentDocument.fillGeneralTab()  ;
  this.schedGrid.refresh();*/
  }else{
    let param=this.generalGridData[0].parameters.content;
    let time=this.generalGridData[0].time.content;
    this.schedGrid.dataProvider[this.updatedRowIndex].slickgrid_rowcontent.parameters.content = param;
    this.schedGrid.dataProvider[this.updatedRowIndex].parameters = param;
    this.schedGrid.dataProvider[this.updatedRowIndex].slickgrid_rowcontent.time.content = time;
    this.schedGrid.dataProvider[this.updatedRowIndex].time = time;
    this.schedGrid.refresh();
  }
  }

  gridParamsToXml(){
    let array=[];
    let time="";
    let schedId="";
    if (this.schedGrid.gridData.length > 0){
      for (let i = 0; i < this.schedGrid.gridData.length; i++) {
        time=this.schedGrid.gridData[i].time?this.schedGrid.gridData[i].time:"";
        schedId=this.schedGrid.gridData[i].schedulerId?this.schedGrid.gridData[i].schedulerId:"";
        this.scheduleXml="";
        if(this.schedGrid.gridData[i].parameters){
          this.scheduleXml = "<PARAMETERS>" ;
          //this.scheduleXml +="<time>" + this.schedGrid.gridData[i].time + "</time>";
          array=this.schedGrid.gridData[i].parameters.split(";");
          for (let j = 0; j < array.length; j++) {
          this.scheduleXml += "<PARAMETER>" ;
          this.scheduleXml +="<NAME><![CDATA[" + array[j].split("(")[0] + "]]></NAME>";
          this.scheduleXml +="<VALUE><![CDATA[" + array[j].split("(")[1].replace(")","") + "]]></VALUE>";
          this.scheduleXml += "</PARAMETER>" ;
          }
          this.scheduleXml += "</PARAMETERS>" ;
        }
        this.scheduleXml = prettyData.pd.xml(this.scheduleXml);
        this.scheduleParameters.push({schedulerId:schedId, time:time ,xml:this.scheduleXml});

      }
    }
    this.parentDocument.scheduleParams=this.scheduleParameters;
  }

  deleteRow(){
    let deletedRow=this.schedGrid.selectedItem;
    this.schedGrid.removeSelected();
    for( var i = 0; i < this.oldSchedRows.length; i++){    
      if ( this.oldSchedRows[i].parameters.content === deletedRow.parameters.content) {
        this.oldSchedRows.splice(i, 1); 
      } 
  }
    this.schedGrid.refresh();
  }

  changeScenarioCheck(){
    
    if(!this.recordCheck.selected){
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      if((this.schedGrid.gridData && this.schedGrid.gridData.length>0) || (this.apiRequiredCols && this.apiRequiredCols.length>0) || this.xmlParams){
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('scenario.alert.uncheckingRecordInstances1', null)));
      }else{
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('scenario.alert.uncheckingRecordInstances', null)));
      }
      this.swtalert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.confirmListener.bind(this));
    }else {
      this.parentDocument.disableInstAndEvtsTab(this.recordCheck.selected);
      this.cyclic.enabled= true;
      this.cyclic.selected = true;
      this.scheduled.enabled = true;
      this.createInst.enabled = true;
      this.enableDisableComponents();
    }
    
  }


  confirmListener(event) {
    /* Condition to check Ok button is selected */
    if (event.detail == Alert.YES) {
      if(this.schedGrid.gridData && this.schedGrid.gridData.length>0){
        this.schedGrid.gridData = { size: 0, row: [] };
        this.schedGrid.refresh();
      }
      if (this.xmlParams){
        this.xmlParams="";
      }
      if(this.apiRequiredCols.length>0){
        this.apiRequiredCols=[];
        this.CreateInsDesc.text=SwtUtil.getPredictMessage('scenario.CreateInsDesc', null);
      }
      this.parentDocument.refreshGridGuiHighlight();
      this.parentDocument.disableInstAndEvtsTab(this.recordCheck.selected);
      this.scheduled.enabled = false;
      this.createInst.enabled = false;
      this.cyclic.enabled = true;
      //if(this.scheduled.selected){
        this.cyclic.selected = true;
        this.enableDisableComponents();
      //}
    }else{
      this.recordCheck.selected = !this.recordCheck.selected;
    }
  }


}
