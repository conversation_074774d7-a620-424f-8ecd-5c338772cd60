import { Component, OnInit, OnDestroy, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {
  SwtToolBoxModule,
  CommonService,
  SwtModule,
  Logger,
  SwtAlert,
  JSONReader,
  HTTPComms,
  SwtButton,
  ExternalInterface,
  SwtUtil,
  SwtCanvas,
  SwtText,
  DataExportMultiPage,
  SwtLoadingImage,
  SwtLabel,
  ScreenVersion,
  ContextMenuItem,
  SwtPopUpManager,
  JSONViewer,
  SwtComboBox, SwtCheckBox, StringUtils, SwtTabNavigator, Tab, SwtSummary, CustomTree, HDividedEndResizeEvent
} from 'swt-tool-box';


declare var instanceElement: any;


@Component({
  selector: 'app-scenario-summary',
  templateUrl: './ScenarioSummary.html',
  styleUrls: ['./ScenarioSummary.css']
})
export class ScenarioSummary extends SwtModule implements OnInit, OnDestroy {


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Scenario Summary', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }


  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('lblCombo') lblCombo: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('currLabel') currLabel: SwtLabel;
  @ViewChild('zeroLabel') zeroLabel: SwtLabel;
  @ViewChild('alertLabel') alertLabel: SwtLabel;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('currBox') currBox: SwtCheckBox;
  @ViewChild('zeroBox') zeroBox: SwtCheckBox;
  @ViewChild('alertBox') alertBox: SwtCheckBox;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('exportContainer') exportContainer: DataExportMultiPage;
  @ViewChild('lostConnectionText') lostConnectionText: SwtText;
  @ViewChild('lastRefTime') lastRefTime: SwtText;
  @ViewChild('lastRefTimeLabel') lastRefTimeLabel: SwtText;
  @ViewChild('mainGroup') mainGroup: SwtCanvas;
  /*********Tab*********/
  @ViewChild('tabCategoryList') tabCategoryList: SwtTabNavigator;
  @ViewChild('displayContainerPredict') displayContainerPredict: Tab;
  @ViewChild('displayContainerPCM') displayContainerPCM: Tab;
  /*********SwtSummary*********/
  @ViewChild('summary') summary: SwtSummary;



  private swtAlert: SwtAlert;
  private logger: Logger;

  /**
   * Data Objects
   */

  public  jsonReader: JSONReader = new JSONReader();
  public summaryJSONReader: JSONReader = new JSONReader();
  public prevRecievedSummaryJSON;
  public lastRecievedSummaryJSON;
  public prevRecievedJSON;
  public lastRecievedJSON;
  public lastReceivedWidthJSON;

  /**
   * Communication Objects
   */
  private  inputData: HTTPComms = new HTTPComms(this.commonService);
  private  summaryData: HTTPComms = new HTTPComms(this.commonService);
  public sendData: HTTPComms = new HTTPComms(this.commonService);
  private requestParams = [];
  private  invalidComms = "";
  private  baseURL = SwtUtil.getBaseURL();
  private  actionPath = "";
  private  actionMethod = "";
  private currDividerPosition: number;


  /**
   * Refresh Strings
   */
  public  TREE = 'tree';
  public  SCENARIO = 'scenario';

  /**
   * Combo Flags
   */

  private  comboOpen = false;
  private  comboChange = false;

  /**
   * Popup Objects
   */

  private  showJSON: any;
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private showXMLPopup: any;
  /* -- START -- Screen Name and Version Number ---- */
  private  screenName = SwtUtil.getPredictMessage('scenarioSummary.title', null); // "Scenario Summary";
  private  versionNumber = "1.0.0001";
  private  releaseDate = '16 Apr 2020';
  /* -- END -- Screen Name and Version Number ---- */

  private  currentUser = "";
  private  menuAccessId = "";
  private  callerMethod = "";
  private  flashScenarios = "";
  private  popupScenarios = "";
  private  emailScenarios = false;
  private  lastSelectedId = "" ;
  private  firstLoad = true;
  public  tabDataCategory = [];
  /**
   * Summary Tree Objects
   */
  public  scenarioTitle = '';

  private  fisrtTablastSelectedId: string = null;
  private  secondTablastSelectedId: string= null;
  private  firstTabTreeOpenedItems = [];
  private  firstTabTreeClosedItems = [];
  private  secondTabTreeOpenedItems = [];
  private  secondTabTreeClosedItems = [];
  private  previousSelectedTabIndex = -1;

  ngOnInit() {
    instanceElement = this;
    this.summary.summaryGrid.clientSideSort = false;
    this.summary.summaryGrid.clientSideFilter = false;
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.close', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refreshWindow', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.refresh', null);
    this.lblCombo.text = SwtUtil.getPredictMessage('scenarioSummary.Entity', null);
    this.lastRefTimeLabel.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);
    this.entityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.alertLabel.text = SwtUtil.getPredictMessage('scenarioSummary.alertableScen', null);
    this.zeroLabel.text = SwtUtil.getPredictMessage('scenarioSummary.zeroTotals', null);
    this.currLabel.text = SwtUtil.getPredictMessage('scenarioSummary.applyCcy', null);
    this.lostConnectionText.text = SwtUtil.getPredictMessage('screen.connectionError', null);
  }

  ngOnDestroy(): any {
    instanceElement = null;

  }

  /**
   * The functon is used when the page loads initially the generic display screen
   */
  onLoad(): void {
    try {
      this.logger.info('method [onLoad] - START ');

      this.initializeMenus();
      this.callerMethod = ExternalInterface.call('eval', 'callerMethod');
      this.currentUser = ExternalInterface.call('eval', 'currentUser');
      this. menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      this.summary.tree.ITEM_CLICK.subscribe(( item ) => {
        this.summaryTreeEventHandler( item );
      });

      this.summary.summaryGrid.ITEM_CLICK.subscribe((selectedRowData) => {
        this.cellClickEventHandler(selectedRowData);
      });

      this.summary.summaryGrid.columnWidthChanged.subscribe((event) => {
        this.updateWidths(event);
      });

      HDividedEndResizeEvent.subscribe((event) => {
        const widthLeft = ''+this.summary.divBox.widthLeft;
        this.currDividerPosition = Number(widthLeft.substr(0,widthLeft.length-1));
        this.updateWidths(event);
      });

      this.summary.summaryGrid.onFilterChanged = this.doUpdateSortFilter.bind(this);
      this.summary.summaryGrid.onSortChanged = this.doUpdateSortFilter.bind(this);
      this.requestParams = [];
      this.actionPath = 'scenarioSummary.do?';
      this.actionMethod = 'method=summaryScreenInfo';
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.requestParams["callerMethod"] = this.callerMethod;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.logger.info('method [onLoad] - END ');
    } catch (error) {
      this.logger.error('GenericDisplay - method [onLoad] - error ', error);

    }
  }




  /**
   * updateDividerPosition
   * This method is called to save the position of the divider
   **/
  updateDividerPosition(): void {
    let mainWidth: number=this.summary.mainHGroup.width;
    let trWidth: number=this.summary.treeContainer.width;
    this.currDividerPosition =(mainWidth - trWidth);
  }


  /**
   * comboFocusOutHandler
   * @param e:Event
   * This method is called on FocusOut Event occurs on entity Combo Box
   */
  comboFocusOutHandler(e): void {
    if (StringUtils.trim(this.entityCombo.selectedItem.text).length === 0) {
      this.entityCombo.setComboData(this.jsonReader.getSelects());
    }
  }


  updateWidths(event): void {
    let columnWidth=[];
    let cols = this.summary.summaryGrid.gridObj.getColumns().slice(0);
    let columnCount = cols.length;
    for (let i=0; i< columnCount-1; i++) {
      if(cols[i].id == "expand") {
        cols[i].width = 5;
      } else {
        if (cols[i].field != null) {
          columnWidth.push(cols[i].field + "=" + cols[i].width);
        }
      }
    }
    // add divider value
    columnWidth.push("divider=" + this.currDividerPosition);
    this.requestParams=[];
    this.sendData.encodeURL = false;
    this.requestParams["width"]=columnWidth.join(",");
    this.actionPath = 'scenarioSummary.do?';
    this.actionMethod = 'method=saveColumnWidth&';
    this.sendData.cbStart= this.startOfComms.bind(this);
    this.sendData.cbStop= this.endOfComms.bind(this);
    this.sendData.cbResult = ( event) => {
      this.inputDataResultColumnsChange(event);
    };
    this.sendData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.sendData.send(this.requestParams);
  }


  inputDataResultColumnsChange(event): void {
    let index: any;
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastReceivedWidthJSON = event;
      let jsonResponse: JSONReader = new JSONReader();
      jsonResponse.setInputJSON(this.lastReceivedWidthJSON);
      if (jsonResponse.getRequestReplyMessage() !== "Column width saved ok") {
        this.swtAlert.error(SwtUtil.getPredictMessage('error.contactAdmin', null) + '\n' + jsonResponse.getRequestReplyMessage());
      }
    }
  }

  /**
   * cellClickEventHandler
   * @param event:CellEvent
   * This method is called when clicking tree grid cell
   */
  cellClickEventHandler(event): void {
    let fieldName = event.target.field;
    const isClickable = this.summary.setClickable( event.target.data, null,null);
    const value = (event.target.data.slickgrid_rowcontent[fieldName] ) ? event.target.data.slickgrid_rowcontent[fieldName].content : null;
    if (isClickable && value > 0) {
      const entityId: string = event.target.data.slickgrid_rowcontent['entity'].content;
      const currencyId: string = event.target.data.slickgrid_rowcontent['ccy'].content;
      const count: string = event.target.data.slickgrid_rowcontent[fieldName].content;
      const facilityId: string = this.summary.facilityId;
      const facilityName: string = this.summary.facilityName;
      const useGeneric: string = this.summary.useGeneric;
      const scenarioTitle: string = this.summary.scenarioTitle;
      const applyCurrencyThreshold: string = this.currBox.selected === true ? 'Y' : 'N';
      const scenarioId: string = this.summary.selectedscenario;
      ExternalInterface.call('openFacility', scenarioTitle, useGeneric, facilityId, facilityName, scenarioId, entityId, currencyId, applyCurrencyThreshold, count);
    }
  }

  /**
   * loadSummary
   * This function is called to load data relevent to alerts
   */
  loadSummary(): void {
    // Initialize communication objects
    this.summaryData.cbStart = this.startOfComms.bind(this);
    this.summaryData.cbStop = this.endOfComms.bind(this);
    this.summaryData.cbResult = (data) => {
      this.summaryDataResult(data);
    };
    this.summaryData.cbFault = this.inputDataFault.bind(this);
    this.summaryData.encodeURL = false;
    this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
    if (this.firstLoad) {
      this.requestParams["firstLoad"] = 'true';
    } else {
      this.requestParams["firstLoad"] = 'false';
    }
    // set hasfocus
    const hasFocus: string = ExternalInterface.call('eval', 'document.hasFocus()');
    this.requestParams["hasFocus"] = hasFocus;
    this.requestParams["selectedTab"] = this.tabCategoryList.getSelectedTab().id;

    if (this.tabCategoryList.selectedIndex !== -1) {
      this.requestParams["selectedCategory"] = this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName;
    }
    this.actionPath = 'scenarioSummary.do?';
    this.actionMethod = 'method=summaryData';
    this.summaryData.encodeURL=false;
    this.summaryData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.summaryData.send(this.requestParams);
  }



  /**
   * inputDataResult
   * @param event:ResultEvent
   * This method is called when result event occurs.
   */
  inputDataResult(event): void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.lostConnectionText.visible = false;

      if (this.jsonReader.getScreenAttributes().lastRefTime !== '') {
        const lastRef = this.jsonReader.getScreenAttributes()["lastRefTime"];
        this.lastRefTime.text = lastRef.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');
        this.lastRefTime.visible = true;
      }
      if (this.jsonReader.getRequestReplyStatus()) {
        if (this.lastRecievedJSON !== this.prevRecievedJSON) {
          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.selectedEntity.text = this.entityCombo.selectedItem.value;
          this.currBox.selected = this.jsonReader.getScreenAttributes().currencythreshold === 'Y';
          this.zeroBox.selected = Boolean(this.jsonReader.getScreenAttributes().hidezerocounts);
          this.alertBox.selected =Boolean( this.jsonReader.getScreenAttributes().alertablescenarios);
          this.popupScenarios = this.jsonReader.getScreenAttributes().popupScenarios === 'true' ? 'true' : 'false' ;
          this.flashScenarios = this.jsonReader.getScreenAttributes().flashScenarios === 'true' ? 'true' : 'false' ;
          this.emailScenarios = this.jsonReader.getScreenAttributes().emailScenarios === 'true' ;

          if (this.firstLoad) {
            this.tabDataCategory = [];

            if (this.tabCategoryList.getTabChildren().length === 0) {
              if (this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab) {
                for (let i = 0; i < 2; i++) {
                  this.tabDataCategory[i] = this.tabCategoryList.addChild(Tab) as Tab;
                  this.tabDataCategory[i].id = this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].tabId;
                  this.tabDataCategory[i].label = this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].tabName;
                  this.tabDataCategory[i].tabName = this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].tabName;
                  this.tabDataCategory[i].count = this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].count;
                }

              }
            }
          }
          const index = Number(this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.selectedIndex.index);
          this.tabCategoryList.selectedIndex = index;
        }
      } else {
        SwtUtil.getPredictMessage('error.contactAdmin', null);
        this.swtAlert.error(SwtUtil.getPredictMessage('error.contactAdmin', null) + '\n' + this.jsonReader.getRequestReplyMessage() + '\n' + this.jsonReader.getRequestReplyLocation());
      }
      this.prevRecievedJSON = this.lastRecievedJSON;
      this.loadSummary();
    }
    this.previousSelectedTabIndex = this.tabCategoryList.selectedIndex;
  }

  /**
   * summaryDataResult
   * @param event:ResultEvent
   * This method is called when result event occurs.
   */
  summaryDataResult(event) {
    if (this.summaryData.isBusy()) {
      this.summaryData.cbStop();
    } else {
      this.lastRecievedSummaryJSON = (event);
      this.summaryJSONReader.setInputJSON(this.lastRecievedSummaryJSON);
      if (this.summaryJSONReader.getRequestReplyStatus()) {
        if (this.lastRecievedSummaryJSON !== this.prevRecievedSummaryJSON) {
          if (this.summaryJSONReader.getScreenAttributes().scenarioTotalForTab1 !== '') {
            this.tabCategoryList.getChildAt(0).label = this.tabDataCategory[0].tabName + '(' + this.summaryJSONReader.getScreenAttributes().scenarioTotalForTab1 + ')';
          }
          if (this.summaryJSONReader.getScreenAttributes().scenarioTotalForTab2 !== '') {
            this.tabCategoryList.getChildAt(1).label =  this.tabDataCategory[1].tabName + '(' + this.summaryJSONReader.getScreenAttributes().scenarioTotalForTab2 + ')';
          }
          if (this.tabCategoryList.selectedIndex === 0 ) {
            this.lastSelectedId = this.fisrtTablastSelectedId;
            this.summary.treeOpenedItems = this.firstTabTreeOpenedItems.concat();
            this.summary.treeClosedItems = this.firstTabTreeClosedItems.concat();
          } else {
            this.lastSelectedId = this.secondTablastSelectedId;
            this.summary.treeOpenedItems = this.secondTabTreeOpenedItems.concat();
            this.summary.treeClosedItems = this.secondTabTreeClosedItems.concat();
          }

          if (this.summary.treeOpenedItems.length === 0) {
            this.summary.tree.openItems = [];
          }

          if(this.summary.treeClosedItems.length === 0) {
            this.summary.tree.closeItems = [];
          }
          this.summary.setBaseURL(this.baseURL);
          this.summary.setActionPath(this.actionPath);
          this.summary.dataProvider(this.lastRecievedSummaryJSON);

          if (this.firstLoad) {
            this.summary.tree.expandAll(CustomTree.LEVEL_1_STR);
            this.summary.setFirstSubNode();
            this.firstLoad = false;
          } else {
            this.summary.openTreeItems();
            this.summary.tree.selectNodeByAttribute("id", this.lastSelectedId);
          }
          if (this.mainGroup.visible === false) {
            this.mainGroup.visible = true;
          }

        }
      } else {
        this.swtAlert.error(SwtUtil.getPredictMessage('error.contactAdmin', null) + '\n' + this.summaryJSONReader.getRequestReplyMessage() + '\n' + this.summaryJSONReader.getRequestReplyLocation());
      }
      this.prevRecievedSummaryJSON = this.lastRecievedSummaryJSON;
    }
    this.refreshTreeItemRender();

    this.previousSelectedTabIndex = this.tabCategoryList.selectedIndex;
  }


  /**
   * refreshTreeItemRender
   * This method is called to ref+resh tree itemRenders if any update occurs on the summary tree
   */
  refreshTreeItemRender(): void {
    // this.summary.tree.invalidateList();
  }


  /**
   * summaryTreeEventHandler
   * @param event:Event
   * This method is called when Event occurs on the tree tree
   */
  summaryTreeEventHandler(event): void {
    this.summary.summaryGrid.resetFilter();
    if (event.data.isBranch !== true) {
      this.updateData('tree');
    } else {
      this.summary.resetGridData();
    }
  }


  /**
   * filteredGridColumns
   * This function is used to get the filtered grid columns
   */
  private  getfilteredGridColumns(): string {
    try {
      let selectedFilter='';
      let filteredColumnsFields=[];
      if (this.summary.summaryGrid.filteredGridColumns !== '') {
        let gridColumns=this.summary.summaryGrid.getFilterColumns();
        let filteredColumns=this.summary.summaryGrid.filteredGridColumns;
        for (let i=0; i < gridColumns.length; i++) {
          filteredColumnsFields[i] = gridColumns[i].field;
        }
        if(filteredColumns != '') {
          let filterdValues = filteredColumns.split('|');
          for (let i=0; i < filteredColumnsFields.length-1; i++) {
            selectedFilter=selectedFilter+filterdValues[i]+'|';
          }
        } else {
          selectedFilter = this.summary.summaryGrid.getFilteredGridColumns();
        }
      } else {
        selectedFilter = "";
        return selectedFilter;
      }
      return selectedFilter.slice(4,selectedFilter.length);
    } catch (error) {
      // SwtUtil.logError(error, SwtUtil.INPUT_MODULE_ID, this.getQualifiedClassName(this)  , "getfilteredGridColumns", this.errorLocation);
    }
  }




  /**
   * doUpdateSortFilter()
   * @param event:Event
   * Method called when update filter and/or sort.
   **/
  private  doUpdateSortFilter(): void {
    let selectedSort: string = null;
    let selectedFilter: string =null;
    const zeroBalances: string = this.zeroBox.selected.toString();
    const currThreshold: boolean = this.currBox.selected;
    const alertScenarios: string = this.alertBox.selected.toString();

    try {
      this.summary.summaryGrid.selectedItem = null;
      selectedFilter = this.getfilteredGridColumns();
      selectedSort = this.summary.getSortedGridColumn();
      this.requestParams=[];
      this.firstLoad = false;
      this.requestParams["selectedFilter"] = selectedFilter;
      this.requestParams["selectedSort"] = selectedSort;
      this.requestParams["callerMethod"] = this.callerMethod;
      this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
      this.requestParams["userId"] = this.currentUser;
      this.requestParams["menuAccessId"] = this.menuAccessId;
      this.requestParams["zeroBalances"] = zeroBalances;
      this.requestParams["currThreshold"] = currThreshold == true ? "Y": "N";
      this.requestParams["selectedScenario"] = this.summary.getSelectedItemID() ;
      this.requestParams["isScenarioAlertable"] = this.summary.getIsScenarioAlertable();
      this.requestParams["dividerPosition"] = this.summary.getDividerPosition();
      // this.requestParams["selectedSort"] = this.summary.getSortedGridColumn();
      this.requestParams["alertScenarios"] = alertScenarios;
      this.requestParams["flashScenarios"] = this.flashScenarios;
      this.requestParams["popupScenarios"] = this.popupScenarios;
      this.requestParams["emailScenarios"] = this.emailScenarios.toString();
      this.summary.saveTreeOpenState();
      if (this.previousSelectedTabIndex === 0 ) {
        this.firstTabTreeOpenedItems = this.summary.treeOpenedItems.concat();
        this.firstTabTreeClosedItems = this.summary.treeClosedItems.concat();
      } else {
        this.secondTabTreeOpenedItems = this.summary.treeOpenedItems.concat();
        this.secondTabTreeClosedItems = this.summary.treeClosedItems.concat();
      }
      this.loadSummary();

    } catch (error) {

    }
  }


  /**
   * updateData
   * @param refresh:String
   * Update the data, this is called whenever a fresh of the data is required.
   */
  updateData(refresh: string): void {
    const zeroBalances: string = this.zeroBox.selected.toString();
    const currThreshold: boolean = this.currBox.selected;
    const alertScenarios: string = this.alertBox.selected.toString();
    this.requestParams = [];
    this.requestParams["callerMethod"] = this.callerMethod;
    this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
    this.requestParams["userId"] = this.currentUser;
    this.requestParams["menuAccessId"] = this.menuAccessId;
    this.requestParams["zeroBalances"] = zeroBalances;
    this.requestParams["currThreshold"] = currThreshold == true ? "Y": "N";
    this.requestParams["selectedScenario"] = this.summary.getSelectedItemID() ;
    this.requestParams["isScenarioAlertable"] = this.summary.getIsScenarioAlertable();
    this.requestParams["dividerPosition"] = this.summary.getDividerPosition();
    this.requestParams["selectedSort"] = this.summary.getSortedGridColumn();
    this.requestParams["alertScenarios"] = alertScenarios;
    this.requestParams["flashScenarios"] = this.flashScenarios;
    this.requestParams["popupScenarios"] = this.popupScenarios;
    this.requestParams["emailScenarios"] = this.emailScenarios.toString();
    this.requestParams["selectedTab"] = this.tabCategoryList.getSelectedTab().id;
    if (this.tabCategoryList.selectedIndex !== -1) {
      this.requestParams["selectedCategory"] = this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName;
    }
    if(this.summary.tree.selectedItem) {
      if (this.previousSelectedTabIndex === 0 ) {
        this.fisrtTablastSelectedId = this.summary.tree.selectedItem.id;
      } else {
        this.secondTablastSelectedId= this.summary.tree.selectedItem.id;
      }
    } else {
      if (this.previousSelectedTabIndex === 0 ) {
        this.fisrtTablastSelectedId = null;
      } else {
        this.secondTablastSelectedId= null;
      }
    }

    this.summary.tree.selectedIndex = -1 ;
    this.summary.saveTreeOpenState();
    if (this.previousSelectedTabIndex === 0 ) {
      this.firstTabTreeOpenedItems = this.summary.treeOpenedItems.concat();
      this.firstTabTreeClosedItems = this.summary.treeClosedItems.concat();
    } else {
      this.secondTabTreeOpenedItems = this.summary.treeOpenedItems.concat();
      this.secondTabTreeClosedItems = this.summary.treeClosedItems.concat();
    }
    if (refresh === this.TREE) {
      this.loadSummary();
    } else if (refresh === this.SCENARIO) {
      this.inputData.send(this.requestParams);
    }
    this.previousSelectedTabIndex = this.tabCategoryList.selectedIndex;

  }



  /**
   * openedCombo
   * @param event:DropDownEvent
   * When a combobox is open then any requests to the server need to be cancelled
   */
  openedCombo(event): void {
    this.comboOpen = true;
    if (this.inputData.isBusy()) {
      this.inputData.cancel();
      (event.currentTarget as SwtComboBox).interruptComms = true;
    }
  }


  /**
   * closedCombo
   * @param event :DropdownEvent
   * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
   */
  closedCombo(event): void {
    this.comboOpen = false;
    if ((event.triggerEvent !== null) && (event.triggerEvent.type === 'mouseDownOutside')) {
      if ((event.currentTarget as SwtComboBox).interruptComms) {
        (event.currentTarget as SwtComboBox).interruptComms = false;
        this.updateData('scenario');

      }
    }
  }

  /**
   * entityComboChange
   * @param event :Event
   * When there is a change in the in one of the combo's
   */
  entityComboChange(event): void {
    this.comboChange = true;
    this.updateData('scenario');
  }

  /**
   * startOfComms
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.disableInterface();
  }

  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.enableInterface();
  }

  /**
   * inputDataFault
   * @param event:FaultEvent
   * If a fault occurs with the connection with the server then display the lost connection label
   */
  inputDataFault(event): void {
    this.lostConnectionText.visible = true;
    this.invalidComms = event.fault.faultString + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail;
    this.swtAlert.error(this.invalidComms);
  }

  /**
   * disableInterface
   * Disable interface, turn off certain UI elements when a request is made to the server
   */
  disableInterface(): void {
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
    this.currBox.enabled = false;
    this.zeroBox.enabled = false;
    this.alertBox.enabled = false;
    this.summary.disableTree();
    this.summary.tree.removeEventListener('treeItemClick', this.summaryTreeEventHandler, false);
  }
  /**
   * enableInterface
   * Enable interface, turn on certain UI elements when a request is made to the server
   */
  enableInterface(): void {
    // enable refresh button
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
    // enbale checkboxes controls
    this.currBox.enabled = true;
    this.zeroBox.enabled = true;
    this.alertBox.enabled = true;
    this.summary.enableTree();
    // this.summary.tree.addEventListener(CustomTreeEvent.ITEMCLICK, this.summaryTreeEventHandler, false, 0, true);

  }


  /**
   * The function initializes the menus in the right click event on the generic display monitor screen.
   * The links are redirected to their respective pages.
   */
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
    let screenItem: ContextMenuItem = new ContextMenuItem('Show screen details JSON');
    screenItem.MenuItemSelect = this.showXMLSelect.bind(this);
    this.screenVersion.svContextMenu.customItems.push(screenItem);
    let scenarioItem: ContextMenuItem = new ContextMenuItem('Show summary details JSON');
    scenarioItem.MenuItemSelect = this.showSummaryXMLSelect.bind(this);
    this.screenVersion.svContextMenu.customItems.push(scenarioItem);
    this.contextMenu = this.screenVersion.svContextMenu;
  }


  /**
   * This function is used to display the XML for the generic's data screen
   * param event
   */
  showXMLSelect(even): void {
    if (this.lastRecievedJSON !== null) {
      this.showXMLPopup = SwtPopUpManager.createPopUp(this,
        JSONViewer,
        {
          jsonData: this.lastRecievedJSON,
        });
      this.showXMLPopup.width = '700';
      this.showXMLPopup.title = 'Summary Details JSON';
      this.showXMLPopup.height = '350';
      this.showXMLPopup.enableResize = false;
      this.showXMLPopup.showControls = true;
      this.showXMLPopup.display();
    } else {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.scenarioSummary.noData', null), SwtUtil.getPredictMessage('alert.scenarioSummary.noData.title', null));
    }

  }

  /**
   * This function is used to show XML screen
   * @param event:ContextMenuEvent
   */
  showSummaryXMLSelect(event): void {

    if (this.lastRecievedSummaryJSON !== null) {
      this.showJSON = SwtPopUpManager.createPopUp(this,
        JSONViewer,
        {
          jsonData: this.lastRecievedSummaryJSON,
        });
      this.showXMLPopup.width = '700';
      this.showXMLPopup.title = 'Summary Details JSON';
      this.showXMLPopup.height = '350';
      this.showXMLPopup.enableResize = false;
      this.showXMLPopup.showControls = true;
      this.showXMLPopup.display();
    } else {
      this.swtAlert.show( SwtUtil.getPredictMessage('alert.scenarioSummary.noData', null), SwtUtil.getPredictMessage('alert.scenarioSummary.noData.title', null));
    }
  }

  /**
   * close the window from the close button
   * @param e:Event
   */
  closeHandler(e): void {
    ExternalInterface.call('close');
  }
  /**
   * refreshFromJsp
   * Refresh the screen if it is already opened and the user try to show the alerts
   */
  refreshFromJsp(): void {
    this.updateData('scenario');
  }
  /**
   * tabBarLabel
   * @param item:Object
   * Method is used to trim the year in the tab bar label
   */
  tabBarLabelCategory(item): string {
    const label: string = item.label;
    return label;
  }



  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call('help');
    } catch (e) {
      SwtUtil.logError(e, 'Predict', 'ScenarioSummary', 'doHelp', 0);
    }
  }
}


// Define lazy loading routes
const routes: Routes = [
  { path: '', component: ScenarioSummary }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ScenarioSummary],
  entryComponents: []
})
export class ScenarioSummaryModule {  }
