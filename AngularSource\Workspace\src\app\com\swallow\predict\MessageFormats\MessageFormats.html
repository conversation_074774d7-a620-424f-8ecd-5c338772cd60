<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%">
      <HBox width="100%" height="100%" paddingLeft="5" paddingRight="5">
        <VBox width="100%" height="100%" verticalGap="0">
          <HBox width="100%" height="25">
            <SwtLabel id="entityLabel" #entityLabel width="100">
            </SwtLabel>
            <SwtComboBox id="entityCombo" #entityCombo dataLabel="entityList" width="135" (change)="updateData($event)">
            </SwtComboBox>
            <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal" paddingLeft="10">
            </SwtLabel>
          </HBox>
        </VBox>
      </HBox>
    </SwtCanvas>

    <SwtCanvas id="dataGridContainer" #dataGridContainer paddingBottom="5" styleName="canvasWithGreyBorder"
      marginTop="10" border="false" width="100%" height="100%" minHeight="100"></SwtCanvas>

    <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5">
      <HBox width="100%">
        <HBox paddingLeft="5" width="50%">
          <SwtButton #addButton width="70" id="addButton" (click)="doOpenChildWindow('add')"></SwtButton>
          <SwtButton #changeButton width="70" id="changeButton" enabled="false" (click)="doOpenChildWindow('change')">
          </SwtButton>
          <SwtButton #viewButton width="70" id="viewButton" enabled="false" (click)="doOpenChildWindow('view')"></SwtButton>
          <SwtButton #deleteButton width="70" id="deleteButton" enabled="false" (click)="doDeleteAccount()"></SwtButton>
          <SwtButton #closeButton width="70" (click)="closeHandler()" id="closeButton"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" width="50%" paddingTop="5">
          <SwtLabel visible="false" color="red" #dataBuildingText></SwtLabel>
          <SwtLabel visible="false" color="red" #lostConnectionText></SwtLabel>
          <SwtButton [buttonMode]="true" #printButton id="printButton" styleName="printIcon" (click)="printPage()"
            (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtHelpButton id="helpIcon" [buttonMode]="true" enabled="true" helpFile="spread-profile" (click)="doHelp()">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>