<SwtModule (creationComplete)="onLoad()" minWidth="400"  width="100%" height="100%">
  <VBox  width="100%" height="100%" minWidth="400" paddingLeft="5" paddingRight="5" paddingTop="5" paddingBottom="5">
    <SwtCanvas  minWidth="970" id="filtersCanvas" width="100%"  minHeight="120">
      <HBox width="100%" verticalAlign="top">
        <VBox width="75%" verticalGap="5" paddingLeft="5" paddingTop="5">
          <HBox verticalAlign="middle" width="100%">
            <SwtLabel #entityLabel id="entityLabel" width="150"></SwtLabel>
            <SwtComboBox #entityCombo id="entityCombo" width="200" dataLabel="entityList" (change)="onFilterChange(true)"></SwtComboBox>
            <SwtLabel #entityDescLabel id="entityDescLabel" paddingLeft="10" fontWeight="normal" width="350"></SwtLabel>
          </HBox>

          <HBox verticalAlign="middle" width="100%">
            <SwtLabel #messageTypeSearchLabel id="messageTypeSearchLabel" width="150"></SwtLabel>
            <SwtRadioButtonGroup #searchMessageTypeOptions id="searchMessageTypeOptions" (change)="onSearchMessageTypeChange()" width="300" align="horizontal">
              <SwtRadioItem #predefinedMsgTypeRadio value="P" selected="true" groupName="searchMessageTypeOptions"></SwtRadioItem>
              <SwtRadioItem #manualMsgTypeRadio value="M" groupName="searchMessageTypeOptions" paddingLeft="10"></SwtRadioItem>
            </SwtRadioButtonGroup>
          </HBox>

          <HBox verticalAlign="middle" width="100%">
            <SwtLabel #actualMessageTypeLabel id="actualMessageTypeLabel" width="150"></SwtLabel> <VBox>
              <HBox #predefinedMsgTypeBox [visible]="isPredefinedMessageType" [includeInLayout]="isPredefinedMessageType" verticalAlign="middle">
                <SwtComboBox #messageTypeCombo id="messageTypeCombo" width="200" dataLabel="messageTypeList" (change)="onFilterChange()"></SwtComboBox>
              </HBox>
              <HBox #manualMsgTypeBox [visible]="!isPredefinedMessageType" [includeInLayout]="!isPredefinedMessageType" verticalAlign="middle">
                <SwtTextInput #otherMessageTypeInput id="otherMessageTypeInput" width="200" (focusOut)="onFilterChange()" maxLength="30" ></SwtTextInput>
              </HBox>
            </VBox>
          </HBox>

          <HBox verticalAlign="middle" width="100%">
            <SwtLabel #currencyLabel id="currencyLabel" width="150"></SwtLabel>
            <SwtComboBox #currencyCombo id="currencyCombo" width="140" dataLabel="currencyList" (change)="onFilterChange()"></SwtComboBox>
            <SwtLabel #currencyDescLabel id="currencyDescLabel" paddingLeft="10" fontWeight="normal"></SwtLabel>
          </HBox>
        </VBox>

        <HBox #pageBoxTop id="pageBoxTop" width="20%" horizontalAlign="right" paddingRight="5" [visible]="maxPage > 1">
          <SwtCommonGridPagination #numStepperTop></SwtCommonGridPagination>
        </HBox>
      </HBox>
    </SwtCanvas>

    <SwtCanvas id="dataGridContainer" #dataGridContainer styleName="canvasWithGreyBorder" marginTop="10" border="false" width="100%" height="100%" minHeight="300"></SwtCanvas>

    <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5">
      <HBox width="100%" verticalAlign="middle">
        <HBox paddingLeft="5" width="70%" horizontalGap="5">
          <SwtButton #addButton id="addButton" (click)="onAddClick()"></SwtButton>
          <SwtButton #changeButton id="changeButton" (click)="onChangeClick()"></SwtButton>
          <SwtButton #deleteButton id="deleteButton" (click)="onDeleteClick()"></SwtButton>
          <SwtButton #closeButton id="closeButton" (click)="onCloseClick()"></SwtButton>
        </HBox>
        <HBox width="30%" horizontalAlign="right" paddingRight="5" horizontalGap="10">
          <SwtHelpButton #helpButton id="helpButton" (click)="onHelpClick()"></SwtHelpButton>
          <SwtButton #printButton id="printButton" (click)="onPrintClick()" styleName="printIcon"></SwtButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>