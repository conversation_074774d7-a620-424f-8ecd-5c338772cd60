import { <PERSON>mponent, <PERSON>ementRef, OnDestroy, OnInit, ViewChild, ModuleWithProviders, NgModule } from '@angular/core';
import { Alert, CommonService, ExternalInterface, focusManager, HTTPComms, JSONReader, Keyboard, StringUtils, SwtAlert, SwtButton, SwtCanvas, SwtComboBox, SwtCommonGrid, SwtLabel, SwtLoadingImage, SwtModule, SwtPopUpManager, SwtUtil, TitleWindow, SwtToolBoxModule } from 'swt-tool-box';
import { FourEyesProcess } from '../FourEyesProcess/FourEyesProcess';
import { Routes, RouterModule } from '@angular/router';
declare var instanceElement: any;

@Component({
  selector: 'app-spread-profiles-maintenance',
  templateUrl: './SpreadProfilesMaintenance.html',
  styleUrls: ['./SpreadProfilesMaintenance.css']
})
export class SpreadProfilesMaintenance extends SwtModule implements OnInit, OnDestroy {

  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /***********SwtCombo***************/
  @ViewChild('currencyComboBox') currencyComboBox: SwtComboBox;
  /********SwtButton*********************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('csv') csv: SwtButton;
  @ViewChild('excel') excel: SwtButton;
  @ViewChild('pdf') pdf: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  /***********Swtlabel**************/
  @ViewChild('ccyLabel') ccyLabel: SwtLabel;

  private spreadProfilesGrid: SwtCommonGrid;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public jsonReader2: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  public checkAuthData = new HTTPComms(this.commonService);
  public requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  
  /* - START -- Screen Name and Version Number ---- */
  private moduleName = 'Spread Profile Maintenance';
  private versionNumber = '1.00.00';
  private releaseDate = '11 March 2019';
  /* - END -- Screen Name and Version Number ---- */

  private moduleURL: string = null;
  private swtAlert: SwtAlert;
  private menuAccess = 2;
  private programId = '';
  private componentId = false;
  public helpURL: string = null;
  private message: string = null;
  public title: string = null;
  private errorLocation = 0;
  public moduleReportURL: string = null;
  public moduleId = '';
  public searchQuery = '';
  public searchFlag = false;
  public queryToDisplay = '';
  public sQuery = '';
  private win:TitleWindow;
  public screenName;
  // private fourEyesRquired = true;
  private requireAuthorisation = true;
  private facilityId = null;
  private doDeleterecordAction = false;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }



  ngOnInit(): void {
    instanceElement = this;
  }
  disableButtons() {
    if(this.spreadProfilesGrid.selectedIndex ==-1)
     this.disableOrEnableButtons(false)
  }

  onLoad() {
    this.spreadProfilesGrid = <SwtCommonGrid>this.canvasGrid.addChild(SwtCommonGrid);
    this.spreadProfilesGrid.uniqueColumn = "spreadId";
    this.spreadProfilesGrid.onFilterChanged = this.disableButtons.bind(this);
    this.spreadProfilesGrid.onSortChanged = this.disableButtons.bind(this);
    try {
      this.title = SwtUtil.getAMLMessages('pcpriorityMaintenanceScreen.windowtitle.help_screen');
      this.message = SwtUtil.getAMLMessages('pcpriorityMaintenanceScreen.message.help_message');
      this.actionPath = 'spreadProfilesPCM.do?';
      this.actionMethod = 'method=display';
      this.requestParams['moduleId'] = this.moduleId;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      //this.inputDataResult(data);

      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.spreadProfilesGrid.onRowClick = (event) => {
        this.checkIfMaintenanceEventExist(event);
      };
      // Assining properties for controls
      this.addButton.label = "Add";//String(SwtUtil.getCommonMessages('button.add'));
      this.changeButton.label = "Change";//String(SwtUtil.getCommonMessages('button.change'));
      this.viewButton.label = "View";//String(SwtUtil.getCommonMessages('button.view'));
      this.deleteButton.label = "Delete";// String(SwtUtil.getCommonMessages('button.delete'));
      this.closeButton.label = "Close";//String(SwtUtil.getCommonMessages('button.close'));
      this.addButton.setFocus();
    } catch (error) {

    }
  }

  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    try {
      this.loadingImage.setVisible(true);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'startOfComms', this.errorLocation);
    }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    try {
      this.loadingImage.setVisible(false);
      this.spreadProfilesGrid.selectedIndex = -1;
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'endOfComms', this.errorLocation);
    }
  }

  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  public inputDataResult(data): void {
    let header: string;
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {

            if(this.doDeleterecordAction){
              if(StringUtils.isTrue(this.requireAuthorisation))
                this.swtAlert.show("This action needs second user authorisation", "Warning", Alert.OK);
              this.doDeleterecordAction = false;
            }
            
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {
              this.currencyComboBox.setComboData(this.jsonReader.getSelects(), false);
              this.ccyLabel.text = this.currencyComboBox.selectedItem.value;

              this.componentId = this.lastRecievedJSON.screenid;
              this.spreadProfilesGrid.CustomGrid(data.SpreadProfilesMaintenance.grid.metadata);
              if (this.jsonReader.getGridData().size > 0) {
                this.spreadProfilesGrid.gridData = this.jsonReader.getGridData();
                this.spreadProfilesGrid.setRowSize = this.jsonReader.getRowSize();
                this.spreadProfilesGrid.doubleClickEnabled = true;
              } else {
                  this.spreadProfilesGrid.dataProvider = [];
                  this.spreadProfilesGrid.selectedIndex = -1;
              }
              this.menuAccess = this.jsonReader.getScreenAttributes()["menuaccess"];
              // this.fourEyesRquired = this.jsonReader.getScreenAttributes()["fourEyesRequired"];
              this.requireAuthorisation = this.jsonReader.getScreenAttributes()["requireAuthorisation"];
              this.facilityId = this.jsonReader.getScreenAttributes()["faciltiyId"];
              if (this.menuAccess == 0) {
                this.addButton.enabled = true; 
              }
              this.disableComponents();
              // setTimeout(() => {
                // }, 0);
              }
              
              this.spreadProfilesGrid.selectedIndex = -1;
            this.prevRecievedJSON = this.lastRecievedJSON;

          } else {
            if (this.jsonReader.getRequestReplyMessage() ==  "errors.DataIntegrityViolationExceptioninDelete") {
              this.swtAlert.error("Unable to delete, this spread profile is linked to an existing account group");
            } else {
              this.swtAlert.error(SwtUtil.getCommonMessages('alert.generic_exception'));
            }
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error inputDataResult', e);
    }
  }


  checkIfMaintenanceEventExist(event:Event): void {
    try {
      if (this.spreadProfilesGrid.selectedIndices.length === 1 && this.spreadProfilesGrid.selectable) {
     
          let actionMethod = 'method=checkIfMaintenenanceEventExist';
          let actionPath = 'maintenanceEvent.do?';
          this.checkAuthData.cbResult = (data) => {
            this.checkResult(data);
          };
          this.requestParams['recordId']= this.spreadProfilesGrid.dataProvider[this.spreadProfilesGrid.selectedIndex].spreadId;
          this.requestParams['facilityId']= this.facilityId;
          this.checkAuthData.cbFault = this.inputDataFault.bind(this);
          this.checkAuthData.encodeURL = false;
          this.checkAuthData.url =  this.baseURL + actionPath + actionMethod;
          this.checkAuthData.send(this.requestParams);

        }else {
          this.disableOrEnableButtons(false);
      }
    } catch (e) {
      console.log(e);
      // log the error in ERROR LOG
      //SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'ClassName', 'updateData',  this.errorLocation);
    }
  }


  public checkResult(data): void {

    try {
      const JsonResult: JSONReader = new JSONReader();

      if (this.checkAuthData && this.checkAuthData.isBusy()) {
        this.checkAuthData.cbStop();
      } else {
        JsonResult.setInputJSON(data);

          
        if (JsonResult.getRequestReplyMessage() == "RECOD_EXIST") {
          const message = SwtUtil.getPredictMessage('maintenanceEvent.alert.cannotBeAmended', null);
          this.swtAlert.error(message);
          this.disableOrEnableButtons(true,true);
        }  else {
          this.disableOrEnableButtons(true,false);
        }

      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }


  /**
   * inputDataFault
   *
   * @param event:  FaultEvent
   *
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    try {
      this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'inputDataFault', this.errorLocation);
    }
  }


  disableOrEnableButtons(isRowSelected: Boolean, isMaintenenanceEventRelated=false): void {
    if (isRowSelected) {
      this.enableChangeButton(this.menuAccess == 0 && !isMaintenenanceEventRelated);
      this.enableViewButton(this.menuAccess < 2);
      this.enableDeleteButton(this.menuAccess == 0 && !isMaintenenanceEventRelated);
    } else {
      this.enableChangeButton(false);
      this.enableViewButton(false);
      this.enableDeleteButton(false);
    }
  }

  /**
   * enableAddButton
   *
   */
  enableAddButton(value: boolean): void {
    this.addButton.enabled = value;
    this.addButton.buttonMode = value;
  }
  /**
   * enableChangeButton
   *
   */
  enableChangeButton(value): void {
    this.changeButton.enabled = value;
    this.changeButton.buttonMode = value;
  }
  /**
   * enableViewButton
   *
   */
  enableViewButton(value: boolean): void {
    this.viewButton.enabled = value;
    this.viewButton.buttonMode = value;
  }
  /**
   * enableDeleteButton
   *
   */
  enableDeleteButton(value): void {
    this.deleteButton.enabled = value;
    this.deleteButton.buttonMode = value;
  }

  /**
   * doAddSpreadProfilesMaintenance
   *
   * @param event: Event
   *
   * Method to open Add Spread Profile Maintenance details screen
   */
  doAddSpreadProfilesMaintenance(event): void {
    try {
      /*this.win = SwtPopUpManager.createPopUp(this,
        SpreadProfilesMaintenanceAdd,
        {
          title: "Spread Profiles - Add", // childTitle,
          screenName: 'add',
          programId: this.programId,
          moduleId: this.moduleId
        }, true);
      this.win.height = "500";
      this.win.width = "900";
      this.win.id = "spreadProfilesAdd";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win.display();*/
      this.screenName = "add";
      // let newWindow = window.open("/spreadProfilesAdd", 'Spread Profiles Maintenance Add', 'height=500,width=900,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
      // if (window.focus) {
      //   newWindow.focus();
      // }
      
      ExternalInterface.call('openChildWindow', 'spreadProfilesAdd', this.screenName);
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error add', e);
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doAddSpreadProfilesMaintenance', this.errorLocation);
    }
  }

  /**
   * doChangeSpreadProfilesMaintenance
   *
   * @param event: Event
   *
   * Method to open Change group rules details screen
   */
  doChangeSpreadProfilesMaintenance(event): void {
    try {
      this.screenName = "change";
      // let newWindow = window.open("/spreadProfilesAdd", 'Spread Profiles Maintenance Change', 'height=500,width=900,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
      // if (window.focus) {
      //   newWindow.focus();
      // }
      ExternalInterface.call('openChildWindow', 'spreadProfilesAdd', this.screenName);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doChangeSpreadProfilesMaintenance', this.errorLocation);
    }
  }

  /**
   * doViewSpreadProfilesMaintenance
   *
   * @param event: Event
   *
   * Method to open View Spread Profile screen
   */
  doViewSpreadProfilesMaintenance(event): void {
    try {
     /* this.spreadId = this.spreadProfilesGrid.selectedItem.spreadId.content;
      this.spreadName = this.spreadProfilesGrid.selectedItem.spreadName.content;
      this.spreadCurrency = this.spreadProfilesGrid.selectedItem.ccy.content;

      this.win = SwtPopUpManager.createPopUp(this,
        SpreadProfilesMaintenanceAdd,
        {
          title: String(SwtUtil.getAMLMessages('spreadProfilesMaintenanceScreen.infoMsg.viewSpreadProfilesMaintenanceDetails')),
          screenName: 'view',
          spreadId: this.spreadId,
          spreadName: this.spreadName,
          spreadCurrency: this.spreadCurrency
        },
        true);
      this.win.height = "500";
      this.win.width = "900";
      this.win.id = "spreadProfilesAdd";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win.display();*/
      this.screenName = 'view';
      // let newWindow = window.open("/spreadProfilesView", 'Spread Profiles Maintenance View', 'height=500,width=900,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
      // if (window.focus) {
      //   newWindow.focus();
      // }
      ExternalInterface.call('openChildWindow', 'spreadProfilesView', this.screenName);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doViewSpreadProfilesMaintenance', this.errorLocation);
    }
  }

  /**
   * doDeleteSpreadProfilesMaintenance
   *
   * @param event: Event
   *
   * Method to pop up delete confirmation
   *
   */
  doDeleteSpreadProfilesMaintenance(event): void {
    try {
      Alert.yesLabel = "Yes";
      Alert.noLabel = "No";
      const message: string = StringUtils.substitute('Are you sure to delete selected spread profile ID: \n' + this.spreadProfilesGrid.selectedItem.spreadId.content);
      this.swtAlert.confirm(message, SwtUtil.getCommonMessages('alert_header.confirm'), Alert.YES | Alert.NO, null, this.spreadProfileRemoveHandler.bind(this));
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doDeleteSpreadProfilesMaintenance', this.errorLocation);
    }
  }

  spreadProfileRemoveHandler(event) {
    if (event.detail === Alert.YES) {
      // if (this.fourEyesRquired == true) {
      //   this.win = SwtPopUpManager.createPopUp(this, FourEyesProcess, {
      //     title: SwtUtil.getPredictMessage("label.fourEyes", null),
      //   });

      //   this.win.enableResize = false;
      //   this.win.width = '510';
      //   this.win.height = '215';
      //   this.win.showControls = true;
      //   this.win.isModal = true;
      //   this.win.onClose.subscribe((res) => {
      //     if (this.win.getChild().result) {
      //       if (this.win.getChild().result.login == "SUCCESS") {
      //         this.removeSpreadProfile();
      //       }
      //     }

      //   });
      //   this.win.display();
      // } else {
        this.removeSpreadProfile();
    // }
  }
}

  /**
   * removeSpreadProfile
   *
   *
   * Method to remove selected Spread profile
   */
  removeSpreadProfile(): void {
    try {
      this.doDeleterecordAction = true;
      this.requestParams = [];
      this.requestParams["selectedSpreadId"] = this.spreadProfilesGrid.dataProvider[this.spreadProfilesGrid.selectedIndex].spreadId;
      this.actionMethod = 'method=deleteSpreadProfile';
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.spreadProfilesGrid.selectedIndex = -1;

    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'removeSpreadProfile', this.errorLocation);
    }
  }

  /**
   * logicUpdateResult
   *
   * @param event: ResultEvent
   *
   * Method to get result of the group rules
   */
  logicUpdateResult(event): void {
    try {
      const JsonResponse = event;
      const JsonResult: JSONReader = new JSONReader();
      JsonResult.setInputJSON(JsonResponse);
      if (JsonResult.getRequestReplyMessage() == "SUCCESS") {
        this.updateData();
      } else {
        this.swtAlert.error(SwtUtil.getCommonMessages('alert_header.error'));
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'logicUpdateResult', this.errorLocation);
    }
  }

  /**
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  public updateData(): void {
    try {
      this.requestParams = [];
      // Define method the request to access
      this.doDeleterecordAction = false;
      this.actionMethod = 'method=display';
      this.requestParams['programId'] = this.programId;
      this.requestParams['moduleId'] = this.moduleId;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.inputData.send(this.requestParams);
      this.spreadProfilesGrid.selectedIndex = -1;
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'ClassName', 'updateData', this.errorLocation);
    }
  }

  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
     ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }
  }

  /**
   * printPage
   *
   * param event
   *
   * Method to get call the action to get reports
   */
  printPage(): void {
    try {
      this.actionMethod = "type=" + "pdf";
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod = this.actionMethod + "&currentModuleId=" + this.moduleId;
      this.actionMethod = this.actionMethod + "&print=" + "PAGE";
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", this.errorLocation);
    }
  }

  /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(Button)
   */
  keyDownEventHandler(event): void {
    try {
      const eventstring = Object(focusManager.getFocus()).name;
      if ((event.keyCode === Keyboard.ENTER)) {
        if (eventstring === 'addButton') {
          this.doAddSpreadProfilesMaintenance(event);
        } else if (eventstring === 'changeButton') {
          this.doChangeSpreadProfilesMaintenance(event);
        } else if (eventstring === 'viewButton') {
          this.doViewSpreadProfilesMaintenance(event);
        } else if (eventstring === 'closeButton') {
          this.closeCurrentTab(event);
        } else if (eventstring === 'csv') {
          this.report('csv');
        } else if (eventstring === 'excel') {
          this.report('xls');
        } else if (eventstring === 'pdf') {
          this.report('pdf');
        } else if (eventstring === 'helpIcon') {
          this.doHelp();
        } else if (eventstring === 'deleteButton') {
          this.doDeleteSpreadProfilesMaintenance(event);
        } else if (eventstring === 'printButton') {
          this.printPage();
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'keyDownEventHandler', this.errorLocation);
    }
  }

  /**
     * closeCurrentTab
     *
     * Function called when close button is called
     *
     * @param event:Event
     */
  closeCurrentTab(event): void {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'ClassName', 'refreshGrid', this.errorLocation);
    }
  }

  /**
   * dispose
   *
   * This is an event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.spreadProfilesGrid = null;
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this.menuAccess = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      this.searchQuery = '';
      this.searchFlag = false;
      ExternalInterface.call("close");
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'dispose', this.errorLocation);
    }
  }

  /**
  * report
  *
  * @param type: string
  *
  * This is a report icon action handler method
  */
  report(type: string): void {
    let selectedFilter: string = null;
    let selectedSort = '';
    let moduleId: string = null;
    try {
      moduleId = this.moduleId;
      if (this.spreadProfilesGrid.filteredGridColumns !== '') {
        selectedFilter = this.spreadProfilesGrid.getFilteredGridColumns();
      } else {
        selectedFilter = '';
      }
      selectedSort = this.spreadProfilesGrid.getSortedGridColumn();
      //set the action path
      this.actionMethod = 'method=displayReport';
      this.actionMethod = this.actionMethod + '&type=' + type;
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod = this.actionMethod + '&selectedFilter=' + selectedFilter;
      this.actionMethod = this.actionMethod + '&selectedSort=' + selectedSort;
      this.actionMethod = this.actionMethod + '&print=' + 'ALL';
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, moduleId, 'ClassName', 'report', this.errorLocation);
    }
  }

  /**
   * disableComponents()
   *
   * Method called to disable components
   *
   */
  disableComponents(): void {
    try {
      this.disableOrEnableButtons(false);

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'disableComponents', this.errorLocation);
    }
  }

  changeComboCurrency(event): void {
    this.ccyLabel.text = this.currencyComboBox.selectedItem.value;
    this.actionMethod = 'method=display';
    this.doDeleterecordAction = false;
      this.requestParams['currencyCode'] = this.currencyComboBox.selectedLabel;
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
  }

  getParamsFromParent() {
    let spreadId = '';
    if(this.spreadProfilesGrid.selectedIndex>-1) {
      spreadId = this.spreadProfilesGrid.selectedItem.spreadId.content;
    }
    let params = [
      {screenName: this.screenName, spreadId:spreadId},
    ];
    return params;
  }
}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: SpreadProfilesMaintenance }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SpreadProfilesMaintenance],
  entryComponents: []
})
export class SpreadProfilesMaintenanceModule { }
