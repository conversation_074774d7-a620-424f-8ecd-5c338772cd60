import {Component, ElementRef,  OnInit, ViewChild} from '@angular/core';
import {
  CommonService, Encryptor,
  HTTPComms,
  JSONReader,
  SwtAlert, SwtCanvas,
  SwtCommonGrid, SwtLabel, SwtLoadingImage,
  SwtModule,SwtTextInput,
  SwtUtil
} from "swt-tool-box";

@Component({
  selector: 'app-add-sum-window',
  templateUrl: './AddSumWindow.html',
  styleUrls: ['./AddSumWindow.css']
})
export class AddSumWindow extends SwtModule implements OnInit {
  //TODO when restrict works
  private  ASCII_RESTRICT_PATTERN:string="A-Za-z0-9\d_ !\"#$%&'()+,\-.\/:;=?@[\\\]^`{|}~";

  private  inputData:HTTPComms=new HTTPComms(this.commonService);
  private  sendData:HTTPComms=new HTTPComms(this.commonService);
  private  actionMethod:string="";
  private  actionPath:string="";
  private  requestParams = [];
  private  jsonReader: JSONReader = new JSONReader();
  public  lastRecievedJSON;
  private  prevRecievedJSON;
  public  addSumGrid:SwtCommonGrid;
  public  entityId:string = "RABONL2U";
  public  entityName:string = null;
  public  entityNotSaved:string = null;
  public  entitiesList:string = null;
  public  cameFrom:string = null;
  public  updateString:string= "";
  public swtAlert: SwtAlert;
  private  baseURL: string = SwtUtil.getBaseURL();
  private invalidComms = [];
  @ViewChild('sumEntityId') sumEntityId: SwtTextInput;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  @ViewChild('cvSaveOrCancel') cvSaveOrCancel: SwtCanvas;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.addSumGrid = <SwtCommonGrid>this.cvSaveOrCancel.addChild(SwtCommonGrid);
    this.addSumGrid.editable = true;
  }
  onLoad() {
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    this.sendData.cbStart=this.startOfComms.bind(this);
    this.sendData.cbStop=this.endOfComms.bind(this);
    this.sendData.cbResult=(event) => {
      this.sendDataResult(event);
    };
    this.sendData.cbFault=this.sendDataFault.bind(this);
    this.sendData.encodeURL=false;
    //sumEntityId.addEventListener(Event.CHANGE, changeHighLightBorder);
    this.actionPath="entityMonitor.do?";
    this.actionMethod="method=displayAddSumPersonalEntityList";
    if (this.cameFrom == "Change")
    {
      this.sumEntityId.text = this.entityId;
      this.sumEntityId.enabled = false;
      this.requestParams["entityId"] =  this.entityId;
    }
    else
    {
      this.requestParams["entityId"] = "";
      //this.sumEntityId.setStyle("borderColor","red");
    }
    this.sumEntityId.required = true;
    this.inputData.url= this.baseURL +  this.actionPath +  this.actionMethod;
    this.sendData.url=this.baseURL+ this.actionPath + "method=savePersonalSumEntityList";

    this.inputData.send(this.requestParams);

  }
  /**
   * This method is called by the HTTPComms when result event occurs.<br>
   * @param event:ResultEvent
   */
  inputDataResult(event):void
  {
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    } else {

      this.lastRecievedJSON= event;
      if (this.entityNotSaved == "true")
      {
        for ( let i = 0; i < this.lastRecievedJSON.personalentity.grid.rows.size; i++)
        {
          this.lastRecievedJSON.personalentity.grid.rows.row[i].addsum.content = "N";
          this.lastRecievedJSON.personalentity.grid.rows.row[i].addsum.selected = "false";
        }

        let entitiesListArray= this.entitiesList.split(",");
        for (var i:number = 0; i < entitiesListArray.length; i++)
        {
          for ( let j = 0; j < this.lastRecievedJSON.personalentity.grid.rows.size; j++)
          {
            if(this.lastRecievedJSON.personalentity.grid.rows.row[j].entityid.content == entitiesListArray[i]) {
              this.lastRecievedJSON.personalentity.grid.rows.row[j].addsum.content = "Y";
              this.lastRecievedJSON.personalentity.grid.rows.row[j].addsum.selected = "true";
            }
          }

        }
      }
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.lostConnectionText.visible=false;
      if (this.jsonReader.getRequestReplyStatus())
      {
        if ((this.lastRecievedJSON != this.prevRecievedJSON))
        {
          const obj = {columns: this.jsonReader.getColumnData()};
          this.addSumGrid.CustomGrid(obj);
         /* if (addSumGrid == null)
          {
            addSumGrid=new PersonalEntityGrid(this.jsonReader.getColumnData(), parentApplication.baseURL, cancelButton);
            addSumGrid.horizontalScrollPolicy="auto";
            //modified by Med Amine for Mantis 2096
            addSumGrid.verticalScrollPolicy="auto";
            dataGridContainer.addElement(addSumGrid);
          }*/
          this.addSumGrid.gridData=this.jsonReader.getGridData();
          this.addSumGrid.setRowSize=this.jsonReader.getRowSize();
          this.prevRecievedJSON=this.lastRecievedJSON;
        }
      }
    }
  }

  inputDataFault():void
  {
    this.lostConnectionText.visible=true;
  }

  sendDataResult(event):void
  {
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    }
    else {
      this.lastRecievedJSON=event;
      //Parse result xml
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus())
      {
        this.parentDocument.addChangeSumEntityToGrid(this.entityId, this.entityName, this.updateString);
        this.close();
      }
    }
  }

  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  sendDataFault():void
  {
    this.lostConnectionText.visible=true;
  }

  startOfComms():void
  {
    this.loadingImage.setVisible(true);

  }
  endOfComms():void
  {
    this.loadingImage.setVisible(false);
  }

 saveHandle():void
  {
    this.entityId = this.sumEntityId.text;
    if (this.cameFrom != "Change")
    {
      if (this.entityId.length == 0)
      {
        this.swtAlert.error( SwtUtil.getPredictMessage("alert.comboBox.invalidValue", null), 'Error');
        this.sumEntityId.setFocus();
        return;
      }
      if (this.parentDocument.entityExists(this.entityId))
      {
        this.swtAlert.error(SwtUtil.getPredictMessage("alert.personalEntityList.entityExists", null), 'Error');
        return;
      }
    }

    for (var i:number = 0; i < this.addSumGrid.dataProvider.length; i++)
    {
      if (this.addSumGrid.dataProvider[i].addsum == "Y")
      {
        this.updateString+=(this.addSumGrid.dataProvider[i].entityid + ",");

      }
    }

    this.updateString= this.updateString.substr(0, this.updateString.length - 1);
    if (this.updateString.length == 0)
    {
      //ExternalInterface.call('getBundle', 'alert', 'label-sumEntityNotSelected',
      //         'Please, select at least one entity to be summed before saving')
      this.swtAlert.error( SwtUtil.getPredictMessage("alert.personalEntityList.sumEntityNotSelected", null), 'Error');
      return;
    }
    this.entityName = "Aggregation(" + this.updateString + ")";
    this.requestParams = [];
    this.requestParams["entityId"] = Encryptor.encode64(this.entityId);
    this.requestParams["entityName"] = this.entityName;
    this.requestParams["sumEntitiesList"] = this.updateString;
    this.requestParams["action"] = this.cameFrom;
    // send the request
    this.sendData.send(this.requestParams);
  }

  /**
   * close the window from the close button
   **/
  cancelHandle():void
  {
    this.close()
  }

  /**
   *  This function shows an alert message when connection error occurs.<br>
   *
   * @param event:MouseEvent
   */
 connError(event:MouseEvent):void
  {
    this.swtAlert.error("" + this.invalidComms, 'Error');
  }


}
