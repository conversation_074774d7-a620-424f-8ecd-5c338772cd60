import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild } from '@angular/core';
import { Router, RouterModule, Routes } from '@angular/router';
import { CommonService, ExternalInterface, focusManager, HTTPComms, JSONReader, Keyboard, SwtAlert, SwtButton, SwtCommonGrid, SwtLoadingImage, SwtModule, SwtTabNavigator, SwtToolBoxModule, SwtUtil, Tab } from 'swt-tool-box';
import { RolesTab } from './Tab/RolesTab/RolesTab';
import { UsersTab } from './Tab/UsersTab/UsersTab';
declare var require: any;
const $ = require('jquery');
let convert = require('xml-js');
var prettyData = require('pretty-data');
@Component({
  selector: 'app-configure-recipients',
  templateUrl: './ConfigureRecipients.html',
  styleUrls: ['./ConfigureRecipients.css']
})
export class ConfigureRecipients extends SwtModule implements OnInit {

  @ViewChild('configRecipientsNavigator') configRecipientsNavigator: SwtTabNavigator;

  /***LodingImage*******/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********SWtButton*************/
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  /******tabs************/
  /******tabs************/
  @ViewChild('usersTab') usersTab: UsersTab;
  @ViewChild('rolesTab') rolesTab: RolesTab;
  //@ViewChild('otherEmailTab') otherEmailTab: OtherEmailTab;
  /***Module****/
  @ViewChild('swtModule') swtModule: SwtModule;

  @ViewChild('printButton') printButton: SwtButton;
  public jsonReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  private swtalert: SwtAlert;
  private menuAccessId;
  private _invalidComms: string;
  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];
  public moduleId = "Predict";
  public roles: Tab;
  public users: Tab;
  //public otherEmail: Tab;
  public rolesGrid: SwtCommonGrid;
  public usersGrid: SwtCommonGrid;
  //public otherEmailGrid: SwtCommonGrid;


  constructor(private commonService: CommonService, private element: ElementRef, private router: Router) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage('tooltip.cancelbutton', null)
  }


  onLoad() {
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "scenMaintenance.do?";
    this.actionMethod = 'method=displayConfigRecipientsData';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.dynamicCreation();

  }

  
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtalert.show("fault " + this._invalidComms);
  }

  closeHandler(): void {
    ExternalInterface.call("close");
  }

  inputDataResult(event) {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        if (this.lastRecievedJSON != this.prevRecievedJSON) {
          if (!this.jsonReader.isDataBuilding()) {
            this.fillRolesTabDetails();
            this.fillUsersTabDetails();
            //this.fillOtherEmailTabDetails()

            }
          }
        }
      }
    }

  fillRolesTabDetails(){
    this.rolesTab.rolesGrid.CustomGrid(this.lastRecievedJSON.configRecipients.rolesGrid.metadata);
    var gridRows = this.lastRecievedJSON.configRecipients.rolesGrid.rows;
            if (gridRows && gridRows.size > 0) {

              this.rolesTab.rolesGrid.gridData = gridRows;

              this.rolesTab.rolesGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              this.rolesTab.rolesGrid.gridData = { size: 0, row: [] };
            }
            let selectedRolesList = window.opener.instanceElement.selectedRoles;
            if (selectedRolesList) {
            for (let i = 0; i < this.rolesTab.rolesGrid.dataProvider.length; i++) {
              for (let j = 0; j < selectedRolesList.length; j++) {
              if (this.rolesTab.rolesGrid.gridData[i].role == selectedRolesList[j]) {
                this.rolesTab.rolesGrid.dataProvider[i].send ="Y" ;                
             
              }
            }
          }
          this.rolesTab.rolesGrid.refresh();
        }

  }


  fillUsersTabDetails(){
    this.usersTab.usersGrid.CustomGrid(this.lastRecievedJSON.configRecipients.usersGrid.metadata);
    var gridRows = this.lastRecievedJSON.configRecipients.usersGrid.rows;
            if (gridRows && gridRows.size > 0) {

              this.usersTab.usersGrid.gridData = gridRows;

              this.usersTab.usersGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              this.usersTab.usersGrid.gridData = { size: 0, row: [] };
            }

            let selectedUsersList = window.opener.instanceElement.selectedUsers;
            if (selectedUsersList) {
            for (let i = 0; i < this.usersTab.usersGrid.dataProvider.length; i++) {
              for (let j = 0; j < selectedUsersList.length; j++) {
              if (this.usersTab.usersGrid.gridData[i].user == selectedUsersList[j]) {
                this.usersTab.usersGrid.dataProvider[i].send ="Y" ;                
             
              }
            }
          }
          this.usersTab.usersGrid.refresh();
        }

  }

  /*fillOtherEmailTabDetails(){
    this.otherEmailTab.otherEmailGrid.CustomGrid(this.lastRecievedJSON.configRecipients.otherEmailGrid.metadata);
    var gridRows = this.lastRecievedJSON.configRecipients.otherEmailGrid.rows;
            if (gridRows && gridRows.size > 0) {

              this.otherEmailTab.otherEmailGrid.gridData = gridRows;

              this.otherEmailTab.otherEmailGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              this.otherEmailTab.otherEmailGrid.gridData = { size: 0, row: [] };
            }

            
    let selectedEmailsList = window.opener.instanceElement.selectedOtherEmails;
    if (selectedEmailsList) {
    for (let i = 0; i < this.otherEmailTab.otherEmailGrid.dataProvider.length; i++) {
      for (let j = 0; j < selectedEmailsList.length; j++) {
      if (this.otherEmailTab.otherEmailGrid.gridData[i].role == selectedEmailsList[j]) {
        this.otherEmailTab.otherEmailGrid.dataProvider[i].send ="Y" ;                
     
      }
    }
  }
  this.otherEmailTab.otherEmailGrid.refresh();
}

  }*/

  saveHandler(){
    let selectedRoles=[];
    let selectedUsers=[];
    let selectedEmails=[];
    let roleGridData = [];
    for (let i = 0; i < this.rolesTab.rolesGrid.gridData.length; i++) {
      let checked= this.rolesTab.rolesGrid.gridData[i].send;
     

      if(checked=="Y"){
        let roleId = this.rolesTab.rolesGrid.gridData[i].role ? this.rolesTab.rolesGrid.gridData[i].role : "";
        let name = this.rolesTab.rolesGrid.gridData[i].name ? this.rolesTab.rolesGrid.gridData[i].name: "";
        roleGridData.push({ roleId: { clickable: false, content: roleId, negative: false }, name: { clickable: false, content: name, negative: false } });
        selectedRoles.push(this.rolesTab.rolesGrid.gridData[i].role); 
      }
    }
    window.opener.instanceElement.selectedRoles=selectedRoles;
     
   let userGridData = [];
   for (let i = 0; i < this.usersTab.usersGrid.gridData.length; i++) {
    let checked= this.usersTab.usersGrid.gridData[i].send;

    if(checked=="Y"){
    let userId = this.usersTab.usersGrid.gridData[i].user ? this.usersTab.usersGrid.gridData[i].user : "";
    let name = this.usersTab.usersGrid.gridData[i].name ? this.usersTab.usersGrid.gridData[i].name: "";
    userGridData.push({ userId: { clickable: false, content: userId, negative: false }, name: { clickable: false, content: name, negative: false } });
       
    selectedUsers.push(this.usersTab.usersGrid.gridData[i].user); 
 }
}
window.opener.instanceElement.selectedUsers=selectedUsers; 

 /*let otherEmailGridData = [];
 for (let i = 0; i < this.otherEmailTab.otherEmailGrid.gridData.length; i++) {
  let checked= this.otherEmailTab.otherEmailGrid.gridData[i].send;

  if(checked=="Y"){
  let emailAddress = this.otherEmailTab.otherEmailGrid.gridData[i].emailAddress ? this.otherEmailTab.otherEmailGrid.gridData[i].emailAddress : "";
  let description = this.otherEmailTab.otherEmailGrid.gridData[i].description ? this.otherEmailTab.otherEmailGrid.gridData[i].description: "";  
  otherEmailGridData.push({ emailAddress: { clickable: false, content: emailAddress, negative: false }, description: { clickable: false, content: description, negative: false } }); 
  
  selectedEmails.push(this.otherEmailTab.otherEmailGrid.gridData[i].user); 

}
}
window.opener.instanceElement.selectedOtherEmails=selectedEmails;*/ 

window.opener.instanceElement.refreshParent(userGridData, roleGridData);//, otherEmailGridData);
ExternalInterface.call('close'); 
}


  doHelp(): void {
    ExternalInterface.call("help");
  }

  printPage(): void {
    let errorLocation = 0;
    try {
      ExternalInterface.call('printPage');

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", errorLocation);
    }
  }
  
  keyDownEventHandler(event: KeyboardEvent): void {
    try {
      //Currently focussed property name
      let eventString: string = Object(focusManager.getFocus()).name;
      if ((event.keyCode == Keyboard.ENTER)) {
        if (eventString == "saveButton") {
          this.saveHandler();
        } else if (eventString == "cancelButton") {
          ExternalInterface.call("close");
        }
      }
    } catch (error) {
      // log the error in ERROR LOG

    }
  }

  dynamicCreation(): void {
    /**dynamic creation*///
    this.roles = <Tab>this.configRecipientsNavigator.addChild(Tab);
    this.users = <Tab> this.configRecipientsNavigator.addChild(Tab);
    //this.otherEmail = <Tab>this.configRecipientsNavigator.addChild(Tab);

    this.roles.label  = this.roles.id = SwtUtil.getPredictMessage('tab.roles', null);
    this.users.label = this.users.id = SwtUtil.getPredictMessage('tab.users', null);
    //this.otherEmail.label=  this.otherEmail.id = SwtUtil.getPredictMessage('tab.otherEmail', null);

    this.rolesTab = <RolesTab>this.roles.addChild(RolesTab);
    this.usersTab =<UsersTab> this.users.addChild(UsersTab);
    //this.otherEmailTab = <OtherEmailTab>this.otherEmail.addChild(OtherEmailTab);

    this.roles.id ="roles";
    this.users.id = "users";
    //this.otherEmail.id= "otherEmail" ;

    this.usersTab.parentDocument = this.rolesTab.parentDocument = this;//this.otherEmailTab.parentDocument = this;
    
    this.rolesTab.height = "100%";
    this.usersTab.height = "100%";
    //this.otherEmailTab.height = "100%";

  }

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ConfigureRecipients }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ConfigureRecipients],
  entryComponents: []
})
export class ConfigureRecipientsModule {}