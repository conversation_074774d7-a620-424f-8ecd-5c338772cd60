import { Component, ElementRef, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import moment from "moment";
import { Alert, CommonService, ExternalInterface, HTTPComms, JSONReader, StringUtils, SwtAlert, SwtButton, SwtLabel, SwtModule, SwtNumericInput, SwtTextArea, SwtTextInput, SwtUtil } from 'swt-tool-box';
declare function validateFormatTime(strField): any;
declare let instanceElement: any;
@Component({
  selector: 'app-pccut-off-build-query',
  templateUrl: './CutOffBuildQuery.html',
  styleUrls: ['./CutOffBuildQuery.css']
})
export class CutOffBuildQuery extends SwtModule implements OnInit, OnDestroy {


  @ViewChild('testOrderNumericInput') testOrderNumericInput: SwtNumericInput;
  @ViewChild('cutOffTimeInput') cutOffTimeInput: SwtTextInput;
  @ViewChild('queryText') queryText: SwtTextArea;
  @ViewChild('expressionBuilderButton') expressionBuilderButton: SwtButton;
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('logText') logText: SwtTextInput;
  @ViewChild('testOrderLabel') testOrderLabel: SwtLabel;
  @ViewChild('logTextLabel') logTextLabel: SwtLabel;
  @ViewChild('cutOffTimeLabel') cutOffTimeLabel: SwtLabel;
  @ViewChild('cutOffExpressionBuilderLabel') cutOffExpressionBuilderLabel: SwtLabel;

  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  public title: string = null;
  private swtalert: SwtAlert;
  public screenName: string;
  public eodTimeInputFromParent: string;
  public cobTimeInputFromParent: string;
  public kickOffTimeFromParent: string;
  public dataProviderSelectedIndex : any;
  public dataProviderCutOff: any;
  public originalTime: string = null;
  public ruleQuery: string = null;
  public queryBuilderScreenName: string = null;
  public queryToExecute: string = null;
  public tabConditionFromQueryBuilder = [];
  public tabConditionFromDB = [];
  public tableToJoinQueryBuilder = [];
  public  ruleConditionArray = [];
  public ruleConditionObject ;
  public  arrayOfOrder = [];
  public originalOrder: string;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }
  ngOnDestroy(): any {
    instanceElement = null;
  }
  ngOnInit(): void {
    instanceElement = this;
    /***Tooltip****/
    this.testOrderNumericInput.toolTip= SwtUtil.getPredictMessage('cutOff.tooltip.testOrder', null);
    this.logText.toolTip= SwtUtil.getPredictMessage('cutOff.tooltip.logText', null);
    this.cutOffTimeInput.toolTip= SwtUtil.getPredictMessage('cutOff.tooltip.cutOffTime', null);
    this.expressionBuilderButton.toolTip=  SwtUtil.getPredictMessage('cutOff.rule.expression.button.tooltip', null);
    this.testOrderLabel.text = SwtUtil.getPredictMessage('cutOff.label.testOrder', null);
    this.logTextLabel.text = SwtUtil.getPredictMessage('cutOff.label.logText', null);
    this.cutOffTimeLabel.text = SwtUtil.getPredictMessage('cutOff.label.cutOffTime', null);
    this.cutOffTimeInput.toolTip = SwtUtil.getPredictMessage('cutOff.tooltip.cutOffTime', null);
    this.cutOffExpressionBuilderLabel.text = SwtUtil.getPredictMessage('cutOff.label.cutOffExpressionBuilder', null);
    this.expressionBuilderButton.label = SwtUtil.getPredictMessage('cutOff.rule.expression.button.label', null);
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);

  }

    onLoad() {

    for (let i =0; i < this.dataProviderCutOff.length; i++) {
    this.arrayOfOrder.push(this.dataProviderCutOff[i].testOrder);
    }
    this.eodTimeInputFromParent = this.parentDocument.eodBeginTimeInput.text;
    this.cobTimeInputFromParent = this.parentDocument.cobTimeInput.text;
    this.kickOffTimeFromParent = this.parentDocument.kickoffTimeInput.text;
    if(this.screenName == 'add') {
      this.cutOffTimeInput.required = true;
      this.queryText.required = true;
      this.testOrderNumericInput.required = true;
    }
    if (this.screenName != 'view') {
      this.enableDisableComponent(true);
    } else {
      this.enableDisableComponent(false);
    }
    if (this.screenName != "add") {
      this.testOrderNumericInput.text = this.dataProviderSelectedIndex.testOrder;
      if(this.dataProviderSelectedIndex.slickgrid_rowcontent.testOrder.previousValue){
        this.testOrderNumericInput.toolTipPreviousValue=this.dataProviderSelectedIndex.slickgrid_rowcontent.testOrder.previousValue;
      }
      this.originalOrder = this.dataProviderSelectedIndex.testOrder;
      this.cutOffTimeInput.text = this.dataProviderSelectedIndex.cutOffTime;
      if(this.dataProviderSelectedIndex.slickgrid_rowcontent.cutOffTime.previousValue)
        this.cutOffTimeInput.toolTipPreviousValue=this.dataProviderSelectedIndex.slickgrid_rowcontent.cutOffTime.previousValue;

      this.originalTime = this.dataProviderSelectedIndex.cutOffTime;
      this.queryText.text = this.dataProviderSelectedIndex.ruleText;
      if(this.dataProviderSelectedIndex.slickgrid_rowcontent.ruleText.previousValue)
        this.queryText.toolTipPreviousValue=this.dataProviderSelectedIndex.slickgrid_rowcontent.ruleText.previousValue;

      this.logText.text =  this.dataProviderSelectedIndex.logText;
      if(this.dataProviderSelectedIndex.slickgrid_rowcontent.logText.previousValue)
        this.logText.toolTipPreviousValue=this.dataProviderSelectedIndex.slickgrid_rowcontent.logText.previousValue;

      if(this.dataProviderSelectedIndex.ruleCondition) {
        this.tabConditionFromDB = JSON.parse(this.dataProviderSelectedIndex.ruleCondition);
        if (this.tabConditionFromDB.length !== 0) {
          for (let i = 0; i < this.tabConditionFromDB.length; i++) {
            this.ruleConditionObject = new Object();
            this.ruleConditionObject.id =  new Object();

            this.ruleConditionObject.conditionId  = this.tabConditionFromDB[i].id.conditionId;
            this.ruleConditionObject.columnToStore = StringUtils.trim(this.tabConditionFromDB[i].fieldName);
            this.ruleConditionObject.operation = (this.tabConditionFromDB[i].operatorId) ? this.tabConditionFromDB[i].operatorId : '';
            this.ruleConditionObject.columnCodeValue  = StringUtils.trim(this.tabConditionFromDB[i].fieldValue);
            this.ruleConditionObject.localValue = (this.tabConditionFromDB[i].localValue) ? this.tabConditionFromDB[i].localValue : '';
            this.ruleConditionObject.tabName = this.tabConditionFromDB[i].tableName;
            this.ruleConditionObject.profileField = this.tabConditionFromDB[i].profileField;
            this.ruleConditionObject.enumProfile  = this.tabConditionFromDB[i].profileFieldValue;
            this.ruleConditionObject.clause  = this.tabConditionFromDB[i].clause;
            if (i + 1 < this.tabConditionFromDB.length) {
              this.ruleConditionObject.andOrString  = this.tabConditionFromDB[i].nextCondition;
            }

            if (this.tabConditionFromDB[i].typeCode === 'DECI') {
              this.ruleConditionObject.dataType = 'NUM';
            } else {
              this.ruleConditionObject.typeCode  = this.tabConditionFromDB[i].dataType;
            }

            this.tabConditionFromQueryBuilder.push(this.ruleConditionObject);
          }
          this.ruleConditionArray = this.tabConditionFromDB;
        }
        for (let i = 0; i < this.tabConditionFromQueryBuilder.length; i++) {
          this.tableToJoinQueryBuilder.push(this.tabConditionFromQueryBuilder[i].tabName);
        }
      }
      else {
        this.tabConditionFromQueryBuilder = [];
      }
      /*if(1this.dataProviderSelectedIndex.tabToJoin)
      {

      }
        //this.tableToJoinQueryBuilder = JSON.parse(this.dataProviderSelectedIndex.tabToJoin);
      else {
        this.tableToJoinQueryBuilder = [];
      }*/
      this.queryToExecute = this.dataProviderSelectedIndex.ruleQuery;

      
    }
  }



  popupClosed() {
     this.close();
    // SwtPopUpManager.getPopUpById('addCutoFFTitleWindow').close();
  }

  enableDisableComponent(value: boolean): void {
    this.testOrderNumericInput.enabled = value;
    this.cutOffTimeInput.enabled = value;
    this.expressionBuilderButton.enabled = value;
    this.logText.enabled = value;
    this.okButton.enabled = value;
    //this.okButton.enabled = value;

  }



  doAddRule(): void {

    if (this.queryText.text !== '' && this.queryText.text !== null) {

      Alert.yesLabel = SwtUtil.getPredictMessage('button.replace', null);
      Alert.noLabel = SwtUtil.getPredictMessage('button.change', null);
      let message: string = SwtUtil.getPredictMessage('alert.replaceQuery', null);
      this.swtalert.confirm(message, "", Alert.YES | Alert.NO |  Alert.CANCEL, null, this.doChangeRule.bind(this));
    } else {

      this.queryBuilderScreenName = 'add';
      ExternalInterface.call('openExpressionBuilder');
      // let newWindow = window.open("/expressionBuilder", 'expression Builder', 'height=600,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }
    }

  }

  getParamsFromParent() {
    // return this.screenName;
    let params = [];
    if(this.queryBuilderScreenName == "add") {
      params = [
        { screenName: this.queryBuilderScreenName, queryToDisplay: '', queryToExecute: '',
          tabAllConditions: [], tableToJoin: [] },
      ];

    }else {

      params = [
        { screenName: this.queryBuilderScreenName, queryToDisplay: this.queryText.text, queryToExecute: this.queryToExecute,
          tabAllConditions: JSON.stringify(this.tabConditionFromQueryBuilder), tableToJoin: JSON.stringify(this.tableToJoinQueryBuilder) },
      ];
    }

    return params;


  }

  doChangeRule(event): void {
    if (event.detail == Alert.YES) {
      this.queryBuilderScreenName = 'add';
      ExternalInterface.call('openExpressionBuilder');
      // let newWindow = window.open("/expressionBuilder", 'expression Builder', 'height=600,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }
    } else if(event.detail == Alert.NO) {
      this.queryBuilderScreenName = 'change';
     ExternalInterface.call('openExpressionBuilder');
    //  let newWindow = window.open("/expressionBuilder", 'expression Builder', 'height=600,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
    //   if (window.focus) {
    //     newWindow.focus();
    //   }
    } else {

    }
  }

  saveRuleDetails(tabCondition:any, tableToJoin:any, queryToDisplay:string,  queryToExecute:string) {
     this.ruleConditionArray = [];
    this.ruleConditionObject;
    this.tabConditionFromQueryBuilder = JSON.parse(tabCondition);

    if (this.tabConditionFromQueryBuilder.length !== 0) {
      for (let i = 0; i < this.tabConditionFromQueryBuilder.length; i++) {

        this.ruleConditionObject = {};
        this.ruleConditionObject.id =  {};

        this.ruleConditionObject.id.conditionId = this.tabConditionFromQueryBuilder[i].conditionId;
        this.ruleConditionObject.fieldName = StringUtils.trim(this.tabConditionFromQueryBuilder[i].columnToStore);
        this.ruleConditionObject.operatorId = (this.tabConditionFromQueryBuilder[i].operation) ? this.tabConditionFromQueryBuilder[i].operation : '';
        this.ruleConditionObject.fieldValue = StringUtils.trim(this.tabConditionFromQueryBuilder[i].columnCodeValue);
        this.ruleConditionObject.localValue = (this.tabConditionFromQueryBuilder[i].localValue) ? this.tabConditionFromQueryBuilder[i].localValue : '';
        this.ruleConditionObject.tableName = this.tabConditionFromQueryBuilder[i].tabName;
        this.ruleConditionObject.profileField = this.tabConditionFromQueryBuilder[i].profileField;
        this.ruleConditionObject.profileFieldValue = this.tabConditionFromQueryBuilder[i].enumProfile;
        this.ruleConditionObject.clause = this.tabConditionFromQueryBuilder[i].clause;

        // if (this.ruleScreenName === 'add') {
        if (i + 1 < this.tabConditionFromQueryBuilder.length) {
          this.ruleConditionObject.nextCondition = this.tabConditionFromQueryBuilder[i + 1].andOrString;
        }
        // } else {
        //   this.requestParams['ruleConditions[' + i + '].nextCondition'] = this.tabAllConditions[i].andOrString;
        // }

        if (this.tabConditionFromQueryBuilder[i].typeCode === 'DECI') {
          this.ruleConditionObject.dataType = 'NUM';
        } else {
          this.ruleConditionObject.dataType = this.tabConditionFromQueryBuilder[i].typeCode;
        }

        this.ruleConditionArray.push(this.ruleConditionObject);
      }
    }


    this.tableToJoinQueryBuilder = JSON.parse(tableToJoin);
    this.queryText.text = queryToDisplay;
    this.queryToExecute = queryToExecute;
  }

  validateTime(textInput): any {
    let bornInfTime;
    let cobAsTime;
    let cutOffAsTime;
    let validTIme = SwtUtil.getPredictMessage('alert.validTime', null);
    let cutOffTimeRange = SwtUtil.getPredictMessage('alert.cutOffRangeTime', null);
    let kickSupCutOff = SwtUtil.getPredictMessage('alert.cutOffSupKickOff', null);
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text && validateFormatTime(textInput) == false) {
      this.swtalert.warning(validTIme, null);
      //textInput.text = "";
      return false;
    }  else  {
      textInput.text = textInput.text.substring(0,5);
      if (this.eodTimeInputFromParent)
        bornInfTime = moment(this.eodTimeInputFromParent, 'HH:mm');
      else if(!this.eodTimeInputFromParent && this.kickOffTimeFromParent) {
        bornInfTime = moment(this.kickOffTimeFromParent, 'HH:mm');
      } else {
        bornInfTime = moment("00:00", 'HH:mm');

      }

      if (this.cobTimeInputFromParent)
        cobAsTime = moment(this.cobTimeInputFromParent, 'HH:mm');
      if (this.cutOffTimeInput.text)
        cutOffAsTime = moment(this.cutOffTimeInput.text, 'HH:mm');

      if (cobAsTime !== undefined && cutOffAsTime !== undefined && (cobAsTime.isBefore(cutOffAsTime) || this.cobTimeInputFromParent == this.cutOffTimeInput.text) ) {
          this.swtalert.warning(cutOffTimeRange);
         return false;
      } else if (bornInfTime !== undefined && cutOffAsTime !== undefined && (cutOffAsTime.isBefore(bornInfTime)) ){
        if(this.eodTimeInputFromParent) {
          this.swtalert.warning(cutOffTimeRange);
        } else {
          this.swtalert.warning(kickSupCutOff);
        }
        return false;
      } else {
        return true;
      }
    }

  }
  save(): void {
    let testOrderUnique = SwtUtil.getPredictMessage('cutOff.alert.testOrder', null);
    let mondatoryField = SwtUtil.getPredictMessage('cutOff.alert.testOrder', null);
    if(this.testOrderNumericInput.text && this.arrayOfOrder.indexOf(this.testOrderNumericInput.text.toString()) != -1 && this.testOrderNumericInput.text.toString() != this.originalOrder) {
      this.swtalert.warning(testOrderUnique);
    }
    else if (!this.validateTime(this.cutOffTimeInput)) {

    }
    else if(!this.testOrderNumericInput.text || !this.queryText.text || !this.cutOffTimeInput.text ){
      this.swtalert.warning(mondatoryField);
    }
    else if(this.validateTime(this.cutOffTimeInput) && this.testOrderNumericInput.text && this.queryText.text) {
      this.parentDocument.refreshParent((this.testOrderNumericInput.text) ? this.testOrderNumericInput.text : "",
        this.cutOffTimeInput.text, this.queryText.text, this.queryToExecute, (this.logText.text) ? this.logText.text : "" ,
        JSON.stringify(this.ruleConditionArray), JSON.stringify(this.tableToJoinQueryBuilder));
      if(this.titleWindow){
        this.close();
      }else {
        window.close();
      }
    }

  }
}
