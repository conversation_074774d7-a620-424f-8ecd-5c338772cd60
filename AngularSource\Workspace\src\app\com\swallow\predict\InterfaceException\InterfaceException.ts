import { Component, <PERSON>ementRef, OnDestroy, OnInit, ViewChild, ModuleWithProviders, NgModule } from '@angular/core';
import {
  Alert,
  CommonService,
  ExternalInterface,
  focusManager,
  Keyboard,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCommonGrid,
  SwtLabel,
  SwtLoadingImage,
  SwtModule,
  SwtPopUpManager,
  SwtUtil,
  SwtToolBoxModule,
  ContextMenuItem,
  JSONViewer,
  JSONReader,
  HTTPComms,
  ScreenVersion,
  SwtComboBox,
  SwtTextArea,
  Logger,
  HBox, SwtCommonGridPagination
} from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
declare let instanceElement: any;
declare var require: any;
var prettyData = require('pretty-data');

@Component({
  selector: 'app-interface-exception',
  templateUrl: './InterfaceException.html',
  styleUrls: ['./InterfaceException.css']
})
export class InterfaceException extends SwtModule implements OnInit, OnDestroy {

  @ViewChild('gridContainer') gridContainer: SwtCanvas;
  @ViewChild('messageDetails') messageDetails: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /********SwtButton*********************/
  @ViewChild('closeButton') closeButton: SwtButton;
  // @ViewChild('reprocessButton') reprocessButton: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  /***********Swtlabel**************/
  @ViewChild('messageTypelbl') messageTypelbl: SwtLabel;
  @ViewChild('messageTypeValue') messageTypeValue: SwtLabel;
  @ViewChild('messageStatuslbl') messageStatuslbl: SwtLabel;
  @ViewChild('messageStatusValue') messageStatusValue: SwtLabel;
  @ViewChild('pageBox') pageBox: HBox;
  @ViewChild('pagination') pagination: SwtCommonGridPagination;
  @ViewChild('messageForm') messageForm: SwtTextArea;

  private swtAlert: SwtAlert;
  private logger: Logger;
  public moduleId = 'Predict';

  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private messageRPC = new HTTPComms(this.commonService);
  private rowsRPC: HTTPComms = new HTTPComms(this.commonService);
  private reprocessRPC: HTTPComms = new HTTPComms(this.commonService);
  private deleteRPC: HTTPComms = new HTTPComms(this.commonService);
  private colWidth: HTTPComms = new HTTPComms(this.commonService);

  public requestParams = [];
  private actionMethod = '';
  private actionPath = '';

  private inputExceptionsGrid: SwtCommonGrid;
  private  params: Object = [];
  private  dictionary: any;
  private  currentPage=1;
  private  maxPage=1;
  private  totalAvailableMessages: number;
  private  extraPages=0;
  private  selectedMessageSeqId="";
  private  seqIdArray = null;
  private  messageType: string;
  private  descending="false";
  private  sortingColumn: string;
  private  startDate: string;
  private  endDate: string;
  private  messagesPerPage: string;
  private  status: string;


  private  baseURL = SwtUtil.getBaseURL();
  private  gridData= null;
  private  prevRawData= null;
  private  columnData = null;
  private  isFiltered=false;
  private  filteredColumns=[];
  private  filteredColumn="";
  private  filteredValue="";
  private  selectedFilteredColumns =[];
  private  requested=false;
  private  sendRequest=true;
  private fromPCM = null;
  private  versionNumber="1.0.0001";
  private  screenName="Interface Exceptions";
  private releaseDate = '17 March 2020';
  public screenVersion  = new ScreenVersion(this.commonService);

  private showJSONPopup: any;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Interface Monitor Screen', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }



  ngOnInit(): void {
    instanceElement = this;
    this.inputExceptionsGrid = this.gridContainer.addChild(SwtCommonGrid) as SwtCommonGrid;
    this.inputExceptionsGrid.lockedColumnCount = 1;

    this.inputExceptionsGrid.paginationComponent = this.pagination;
    this.inputExceptionsGrid.onPaginationChanged = this.paginationChanged.bind(this);
    this.inputExceptionsGrid.onSortChanged = this.globalSort.bind(this);
    this.inputExceptionsGrid.columnWidthChanged.subscribe((event) => {
      this.updateWidths(event);
    });
    this.messageTypelbl.text="Interface:";
    this.messageStatuslbl.text="Message Status:";
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
  }



  /**
   * This function is called when the creationComplete function is done.
   */
  onLoad(): void {
    this.createAboutMenu();
    this.inputExceptionsGrid.onRowClick = (selectedRowData) => {
      this.obtainCell(selectedRowData);
    };
    // this.addEventListener(ListEvent.ITEM_CLICK, this.obtainCell, true);
    // this.addEventListener(DataGridEvent.HEADER_RELEASE, this.globalSort, true);
    this.fromPCM = ExternalInterface.call('eval', 'fromPCM');
    this.dictionary = this.getUrlParams();
    this.messageType = this.dictionary["type"];
    this.totalAvailableMessages = parseInt(this.dictionary["m"], 10);
    let statusVerbose: string;
    switch (parseInt(this.dictionary["status"],10)) {
      case 1:
        statusVerbose="FILTERED";  // If status is '1' set it as 'FILTERED'
        break;
      case 2:
        statusVerbose="BAD";// If status is '2' set it as 'BAD'
        break;
    }
    this.startDate= this.dictionary["fromDate"];
    this.endDate=this.dictionary["toDate"];
    this.currentPage=this.dictionary["p"];
    this.messagesPerPage = this.dictionary["n"];
    this.status = this.dictionary["status"];
    let totalPages: number = this.totalAvailableMessages / 100;
    this.extraPages = this.totalAvailableMessages % 100;
    if (this.extraPages > 0) {
      totalPages = Math.ceil(totalPages);
    }
    this.maxPage = totalPages;
    if (totalPages > 1) {
      this.pageBox.visible=true;
      this.pagination.maximum= Number(this.maxPage);
      this.pagination.value = Number(this.currentPage);
    } else {
      this.pageBox.visible=false;
    }
    this.messageTypelbl.text="Interface:"; // SwtUtil.getPredictMessage('label.interfaceExceptions.messageType', null);
    this.messageTypeValue.text= this.messageType;
    this.messageStatuslbl.text="Message Status:" ;
    this. messageStatusValue.text = statusVerbose;
    this.params= { fromPCM: this.fromPCM, fromDate: this.startDate, toDate: this.endDate, status: this.status, type: this.messageType, p: this.currentPage, n: this.messagesPerPage, m: this.totalAvailableMessages.toString()};

    this.actionPath = 'interfaceexceptions.do?';
    this.actionMethod = 'method=messagesData';
    this.rowsRPC.cbStart = this.startLoader.bind(this);
    this.rowsRPC.cbStop = this.stopLoader.bind(this);
    this.rowsRPC.cbResult = (data) => {
      this.rowsRPCResult(data);
    };
    this.rowsRPC.cbFault = this.inputDataFault.bind(this);
    this.rowsRPC.encodeURL = false;
    this.rowsRPC.url = this.baseURL + this.actionPath + this.actionMethod;


    if (this.sendRequest) {
      this.rowsRPC.send(this.params);
    }
  }

  /**
   * This function is used to create the About menu
   */
   createAboutMenu(): void {
    this.screenVersion.loadScreenVersion(this, 'Interface Exception Screen', this.versionNumber, this.releaseDate);
    let addMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }

  /** This function is used to display the JSON
   *    for data showed in grid
   */
  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    // this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "400";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.display();
  }

  /**
   * This method is used to get the rows for the RPC result
   *
   * @param event:ResultEvent
   */
   rowsRPCResult(event): void {
    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    if (this.jsonReader.getRequestReplyStatus()) {
      if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
        this.selectedMessageSeqId="";
        this.columnData = this.jsonReader.getColumnData();
        this.gridData = this.jsonReader.getGridData();
        this.totalAvailableMessages= Number(event.interfaceexceptions.grid.metadata.datapage.records_total) ;
        const obj = {columns: this.jsonReader.getColumnData()};
        this.inputExceptionsGrid.CustomGrid(obj);
        if (this.jsonReader.getGridData().size > 0) {
          // this.inputExceptionsGrid.id="main";
          this.inputExceptionsGrid.dataProvider = null;
          this.inputExceptionsGrid.gridData = this.jsonReader.getGridData();
          this.inputExceptionsGrid.setRowSize = this.jsonReader.getRowSize();
          this.inputExceptionsGrid.selectable=true;
          this.inputExceptionsGrid.allowMultipleSelection=true;
        }  else {
          this.inputExceptionsGrid.dataProvider = [];
          this.inputExceptionsGrid.selectedIndex = -1;
        }
        this.prevRecievedJSON = this.lastRecievedJSON;
      }
    } else {
      this.swtAlert.warning(this.jsonReader.getRequestReplyMessage(),  'Error');
    }
  }


  /**
   * This function is used to update the column width for the data grid.<br>
   *
   * @param event: DataGridEvent
   */
  updateWidths(event): void {
    if (event.currentTarget.columns[event.columnIndex].width < 115) {
      event.currentTarget.columns[event.columnIndex].width=115;
    }
    this.updateColumnWidths();
  }


  /**
   * This function acts as a helper method to set the column width.<br>
   */
   updateColumnWidths(): void {
    let columnWidth= [];
    let cols = this.inputExceptionsGrid.columnDefinitions;
    for (let i =0; i < this.inputExceptionsGrid.columnDefinitions.length-1; i++) {
      if (cols[i].field != null) {
        columnWidth.push(cols[i].field + "=" + cols[i].width);
      }
    }
    if ( this.colWidth.isBusy()) {
      this.colWidth.cancel();
    }
    this.actionPath = 'interfaceexceptions.do?';
    this.actionMethod = 'method=deleteRequest&amp;';
    this.colWidth.cbStart = this.startLoader.bind(this);
    this.colWidth.cbStop = this.stopLoader.bind(this);
    this.colWidth.cbResult = (data) => {
      this.reprocessRPCResult(data);
    };
    this.colWidth.cbFault = this.inputDataFault.bind(this);
    this.colWidth.encodeURL = false;
    this.colWidth.url = this.baseURL + this.actionPath + this.actionMethod;
    let colParams = new Object();

    colParams["width"]= columnWidth.join(",");
    colParams["fromPCM"]= this.fromPCM;
    if ( this.sendRequest) {
      this.colWidth.send(colParams);
    }
  }


  /**
   * This function is used to check the grid column for legal dragging.<br>
   *
   * @param event:IndexChangedEvent
   */
 checkLegalDrag(event): void {

    if (event.newIndex < 1) {
      let colArr=[];
      let newCol=[];
      colArr=event.currentTarget.columns;
      for (let i=0; i < colArr.length; i++) {
        if (i != event.newIndex) {
          newCol.push(colArr[i]);
        }
        if (i == event.oldIndex) {
          newCol.push(colArr[event.newIndex]);
        }
      }
      // Gets the current target
      event.currentTarget.columns=newCol;
    }
  }

  /**
   * This function is called if the filter creteria is clicked.<br>
   *
   * @param e:ListEvent
   */
 filterCall(e): void {
    let values = [];
    if ((e.target as SwtComboBox).selectedLabel != "--ALL--") {
      this.filteredValue=(e.target as SwtComboBox).selectedLabel;
      this.filteredColumn = this.inputExceptionsGrid.filteredGridColumns;
      values[ this.filteredColumn]= this.filteredValue;
      this.filteredColumns.push(values);
      this.selectedFilteredColumns[ this.filteredColumn]=true;
      this.isFiltered=true;
    } else {
      this.filteredColumn=this.inputExceptionsGrid.filteredGridColumns ; // ((e.target as SwtComboBox).parent as ColumnHeaderRenderer).columnData;
      this.filteredValue="";
      this.selectedFilteredColumns[ this.filteredColumn]=false;
      for (let i=0; i <  this.filteredColumns.length; i++) {
        // Checks the filteredColumns array value
        if ( this.filteredColumns[i].hasOwnProperty( this.filteredColumn)) {
          this. filteredColumns.splice(i, 1);
          break;
        }
      }
      if ( this.filteredColumns.length < 1) {
        this.isFiltered=false;
      }
    }
    this.inputExceptionsGrid.dataProvider=  this.filterData( this.gridData.children());
  //  this.inputExceptionsGrid.refreshHeaders( this.selectedFilteredColumns);
  }

  /**
   * This function is used to show data in the datagrid based on the filter value.<br>
   *
   * @param item:Object
   * @return Boolean
   */
  filtering(item): boolean {
    return item[this.filteredColumn] == this.filteredValue;
  }


  /**
   * This function is used to get the filter data based on the filtered value.<br>
   *
   * @param rawData:XMLList
   * @return XMLList
   */
  filterData(rawData)  {

    // let rtn: XMLList=rawData;
    // let XMLColl: XMLListCollection=new XMLListCollection(rtn);
    // let k: any;
    // for (let i: int=0; i < this.filteredColumns.length; i++) {
    //   for (k in this.filteredColumns[i]) {
    //     XMLColl=new XMLListCollection(rtn);
    //     this.filteredColumn =(k as string);
    //     this.filteredValue=( this.filteredColumns[i])[k];
    //     XMLColl.filterFunction=  this.filtering;
    //     XMLColl.refresh();
    //     rtn=XMLColl.copy();
    //   }
    // }
    // return rtn;
  }


  htmlEntities(str) {
    try {
      return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
      replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
    } catch (e) {
      console.log('error', e, str);
    }
  }

  /**
   * Thins function is used to get the remote procedure call result message.<br>
   *
   * @param event:ResultEvent
   */
  messageRPCResult(event): void {
    this.messageForm.text = "";
    let message: string;
    message = String(event.interfaceexceptions.message);
    message = message.split("$#$").join("\n");
    message = message.split("&@&").join("&nbsp;");
    let messageAsXML  = prettyData.pd.xml(message.split('&nbsp;').join(' '));
    messageAsXML = this.htmlEntities(messageAsXML);
    this.messageForm.htmlText = messageAsXML;

  }

  /**
   * This function is used to Close the Interface Exceptions screen
   */
  close(): void {
    ExternalInterface.call("close");
  }


  public resizing(event) {
    console.log('event :', event);
  }


  /**
   * This function is used to get the URL parameters
   */
 getUrlParams(): any {
    let urlParams= {};
    let fullUrl: string = ExternalInterface.call("document_location_href");
    let paramStr: string = fullUrl.split('?')[1];
    if(paramStr) {
      paramStr = paramStr.split('#')[0];
      let arr = paramStr.split('&');
      for (let i = 0; i < arr.length; i++) {
        let a = arr[i].split('=');
        let paramName = a[0];
        let paramValue = typeof (a[1]) === 'undefined' ? true : a[1];
        if (paramName.match(/\[(\d+)?\]$/)) {
          let key = paramName.replace(/\[(\d+)?\]/, '');
          if (!urlParams[key]) {
            urlParams[key] = [];
          }
          if (paramName.match(/\[\d+\]$/)) {
            let index = /\[(\d+)\]/.exec(paramName)[1];
            urlParams[key][index] = paramValue;
          } else {
            urlParams[key].push(paramValue);
          }
        } else {
          if (!urlParams[paramName]) {
            urlParams[paramName] = paramValue;
          } else if (urlParams[paramName] && typeof urlParams[paramName] === 'string') {
            urlParams[paramName] = [urlParams[paramName]];
            urlParams[paramName].push(paramValue);
          } else {
            urlParams[paramName].push(paramValue);
          }
        }

      }
    }
    // variable.decode(paramStr);
    // for (let k in variable) {
    //   urlParams[k]=variable[k];
    // }
    return urlParams;
  }


  /**
   * This function is used to refresh the screen.
   */
   setRefresh(): void {
    ExternalInterface.call("refreshParent");
  }


  /**
   * This function is used to obtain the Cell
   *
   * @param e:ListEvent
   */
  obtainCell(e): void {
    let keyColumnValue= "";
    if (keyColumnValue != ("No Messages")) {
      this.seqIdArray = this.inputExceptionsGrid.selectedItems;
      this.actionPath = 'interfaceexceptions.do?';
      this.actionMethod = 'method=messageData';
      this.messageRPC.cbStart = this.startLoader.bind(this);
      this.messageRPC.cbStop = this.stopLoader.bind(this);
      this.messageRPC.cbResult = (data) => {
        this.messageRPCResult(data);
      };
      this.messageRPC.cbFault = this.inputDataFault.bind(this);
      this.messageRPC.encodeURL = false;
      this.messageRPC.url = this.baseURL + this.actionPath + this.actionMethod;

      if (this.seqIdArray.length > 0) {
        if (this.seqIdArray.length == 1) {
          let messageParams =[];
          messageParams['seqid'] = this.seqIdArray[0].msgid.content;
          messageParams['fromPCM'] = this.fromPCM;
          // messageParams = {seqid: (this.seqIdArray[0].msgid.content)};
          if (this.sendRequest) {
            this.messageRPC.send(messageParams);
          }
        } else {
          let cadenaNueva: string;
          // let cadena = "Multiple Messages Selected: \n";
          let cadena =  "Multiple Messages Selected: "  ;
          for(let k =0; k < this.seqIdArray.length; k++) {
            cadena += "\n Message ID: " + String(this.seqIdArray[k].msgid.content);
          }
          this.messageForm.htmlText = cadena.replace("\n", "<br />");

        }
      } else {
        this.messageForm.text="";
      }
    } else {
      this.messageForm.text="No Messages";

    }
  }



  /**
   * Method to handle the Enter key press for fetching the details for the given page
   * If the entered value exceeds the maxPage it will restored to the last page number
   *
   * @param event:KeyboardEvent
   */
  keyDownPager(event): void {

    if (event.charCode == Keyboard.ENTER) {
      this.currentPage= this.pagination.value;
      this.dictionary=this.getUrlParams();
      this.params = {fromPCM: this.fromPCM, fromDate:  this.startDate, toDate:  this.endDate, status:  this.status, type:  this.messageType, p:  this.currentPage, n:  this.messagesPerPage, m:  this.totalAvailableMessages, desc:  this.descending, order:  this.sortingColumn};
      this.requested=true;
      if ( this.sendRequest) {
        this.rowsRPC.send( this.params);
      }

    }
  }

  paginationChanged(event) {
    this.next();
  }

  /**
   * Method to handle the up/down key click in the numeric stepper for pagination
   * Page number will not incremented beyond maxPage and decremented less than 1
   */
  next(): void {
    this.currentPage = this.pagination.value;
    this.dictionary=this.getUrlParams();
    this.params= {fromPCM:this.fromPCM, fromDate: this.startDate, toDate: this.endDate, status: this.status, type: this.messageType, p: this.currentPage, n: this.messagesPerPage, m: this.totalAvailableMessages, desc: this.descending, order: this.sortingColumn};
    this.requested=true;
    if (this.sendRequest) {
      this.rowsRPC.send(this.params);
    }
  }


  getParamsFromParent() {
    let spreadId = '';
    if(this.inputExceptionsGrid.selectedIndex>-1) {
      spreadId = this.inputExceptionsGrid.selectedItem.spreadId.content;
    }
    let params = [
      {screenName: this.screenName, spreadId},
    ];
    return params;
  }

  /**
   * This function is used to reprocess the seleceted message(s) in Interface Exceptions screen
   */
  reprocessMessages(): void {
    let reproParams=[];
    this.actionPath = 'interfaceexceptions.do?';
    this.actionMethod = 'method=reprocessRequest&amp;';
    this.reprocessRPC.cbStart = this.startLoader.bind(this);
    this.reprocessRPC.cbStop = this.stopLoader.bind(this);
    this.reprocessRPC.cbResult = (data) => {
      this.reprocessRPCResult(data);
    };
    this.reprocessRPC.cbFault = this.inputDataFault.bind(this);
    this.reprocessRPC.encodeURL = false;
    this.reprocessRPC.url = this.baseURL + this.actionPath + this.actionMethod;
    if ( this.seqIdArray.length != 0) {
      for(let k =0; k < this.seqIdArray.length; k++) {
        reproParams.push( this.seqIdArray[k].msgid.content);
      }
      if ( this.sendRequest) {
        this.reprocessRPC.arraySend(reproParams, "seqid", false);
      }
    } else {
      this.swtAlert.show( SwtUtil.getPredictMessage('label_noMessag', null),  'Error');
    }
  }


  /**
   * This function is used to sort all the values in the Interface Exceptions screen
   *
   * @param e:DataGridEvent
   */
 globalSort(e): void {
    if (this.sendRequest) {
      e.preventDefault();
      // let col= this.inputExceptionsGrid.columnDefinitions;
      // this.sortingColumn = col[e.columnIndex].field;
      // if (this.prevColumnSort["PrevCol"] != undefined) {
      //   col[this.prevColumnSort["PrevCol"]].setStyle("backgroundColor", undefined);
      // }
      for(let i = 0; i< this.inputExceptionsGrid.sorters.length; i++) {

        this.sortingColumn = this.inputExceptionsGrid.sorters[i].columnId;
        this.descending = this.inputExceptionsGrid.sorters[i].direction ? "true": "false";
      }
      // if (this.sortingColumn == this.prevColumnSort["ColumnName"]) {
      //   if (this.descending == "true") {
      //     this.descending="false";
      //   } else {
      //     this.descending="true";
      //   }
      // } else {
      //   this.descending="false";
      // }
      // this.prevColumnSort["ColumnName"]=this.sortingColumn;
      // this.prevColumnSort["PrevCol"]=e.columnIndex;
      this.params= {fromPCM:this.fromPCM, fromDate: this.startDate, toDate: this.endDate, status: this.status, type: this.messageType, p: this.currentPage, n: this.messagesPerPage, m: this.totalAvailableMessages.toString(), desc: this.descending, order: this.sortingColumn};
      this.requested=true;
      this.rowsRPC.send(this.params);
    }
  }

  /**
   * This function is used to reprocess the RPC client
   *
   * @param event:ResultEvent
   */
  reprocessRPCResult(event): void {
    const JsonResponse = event;
    const JsonResult: JSONReader = new JSONReader();
    JsonResult.setInputJSON(JsonResponse);
    this.messageForm.text="Update Response\n";
    this.messageForm.text+="--------------------\n\n";
    this.messageForm.text += JsonResult.getRequestReplyMessage();
    this.setRefresh();
    this.totalAvailableMessages-= this.seqIdArray.length;
    let totalPages: number=this.totalAvailableMessages / 50;
    this.extraPages=this.totalAvailableMessages % 50;
    if (this.extraPages > 0) {
      totalPages++;
    }
    // this.maxPageLabel.text=" of " + totalPages;
    if (totalPages < this.maxPage) {
      this.maxPage=totalPages;
      this.pagination.maximum =   this.maxPage;
    }
    if (this.currentPage > this.maxPage) {
      this.currentPage--;
      this. pagination.value =   this.currentPage;
    }
    // Sets the parameters
    this.params = {
      fromPCM: this.fromPCM,
      fromDate: this.startDate,
      toDate: this.endDate,
      status: this.status,
      type: this.messageType,
      p: this.currentPage,
      n: this.messagesPerPage,
      m: this.totalAvailableMessages.toString(),
      desc: this.descending,
      order: this.sortingColumn};
    this.requested=true;

    if (this.sendRequest) {
      this.rowsRPC.send(this.params);
    }
  }

  /**
   * This function is used to open the delete alert
   */
   openDeleteAlert(): void {
    this.swtAlert.show(
      SwtUtil.getPredictMessage('alert.interfaceExceptions.sure', null), // text
      "Delete Messages", Alert.OK | Alert.CANCEL,
      this, // parent
      this.alertListener.bind(this), // close handler
      null, Alert.CANCEL); // icon and default button
  }

  /**
   * This function is used to listen to the alert
   *
   * @param eventObj CloseEvent
   */
  alertListener(eventObj): void {
    if (eventObj.detail == Alert.OK) {
      this.deleteMessages();
    }
  }

  /**
   * This function is used to delete the messages from the Interface Exceptions screen
   */
  deleteMessages(): void {
    let delParams= [];
    this.actionPath = 'interfaceexceptions.do?';
    this.actionMethod = 'method=deleteRequest&amp;';
    this.deleteRPC.cbStart = this.startLoader.bind(this);
    this.deleteRPC.cbStop = this.stopLoader.bind(this);
    this.deleteRPC.cbResult = (data) => {
      this.reprocessRPCResult(data);
    };
    this.deleteRPC.cbFault = this.inputDataFault.bind(this);
    this.deleteRPC.encodeURL = false;
    this.deleteRPC.url = this.baseURL + this.actionPath + this.actionMethod;
    if (this.seqIdArray.length != 0) {
      for (let k =0 ;k<this.seqIdArray.length; k++) {
        delParams.push(this.seqIdArray[k].msgid.content);
      }

      if (this.sendRequest) {
        this.deleteRPC.arraySend(delParams, "seqid", false);
      }
    } else {
      this.swtAlert.show(SwtUtil.getPredictMessage('alert.interfaceExceptions.noMessage', null), SwtUtil.getPredictMessage('label-error', null));
    }
  }

  /**
   * This function is used to update the result
   *
   * @param event:ResultEvent
   */
   updatesResult(event): void {
    const JsonResponse = event;
    const JsonResult: JSONReader = new JSONReader();
    JsonResult.setInputJSON(JsonResponse);
    if (!JsonResult.getRequestReplyMessage()) {
      this.swtAlert.show("" + JsonResult.getRequestReplyMessage(), SwtUtil.getPredictMessage('alert-error', null));
    }


  }

  /**
   *  This function is used to start the loader
   */
   startLoader(): void {
    this.loadingImage.visible=true;
    this.pagination.enabled=false;
    this.sendRequest=false;
  }

  /**
   * This function is used to stop the loader
   */
   stopLoader(): void {
    if (!this.rowsRPC.isBusy() && !this.messageRPC.isBusy() && !this.reprocessRPC.isBusy() && !this.deleteRPC.isBusy() && !this.colWidth.isBusy()) {
      this.loadingImage.visible=false;
      this.pagination.enabled=true;
      this.sendRequest=true;
    }
  }

  /**
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   *
   * @param event:  KeyboardEvent
   */
   keyDownEventHandler(event): void {
    let eventString: string = Object(focusManager.getFocus()).id;
    if ((event.keyCode == Keyboard.ENTER)) {
      if (eventString == "reprocessButton") {
        this.reprocessMessages();
      } else if (eventString == "closeButton") {
        close();
      }
    }
  }

  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   *
   * @param event:FaultEvent
   **/
  inputDataFault(event): void {
    this.sendRequest=true;
    this.pagination.enabled=true;
  }

  /**
   * This function is used to display the total number of records as a tooltip on the scroll bar (both horizontal and vertical).<br>
   *
   * @param direction - Decides to show the tooltip right or left
   * @param position - Decides whether horizontal or vertical scroll bar
   *
   * @return String - Tool tip value
   */
  howVScrollToolTip(direction: string, position: number): string {
    let toolTipValue ="";
    toolTipValue="No of Records:" + this.totalAvailableMessages;
    return toolTipValue;
  }




}




// Define lazy loading routes
const routes: Routes = [
  { path: '', component: InterfaceException }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [InterfaceException],
  entryComponents: []
})
export class InterfaceExceptionModule { }
