<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox  width='100%' height='100%' paddingLeft="5" paddingTop="5" paddingRight="5" verticalGap="0">
    <SwtCanvas #headerContainer id="headerContainer" minWidth="1200" width="100%" height="56">
    <HBox id="filterContainer" #filterContainer
           width="100%">
     <HBox width="30%">
       <SwtLabel text="Entity"></SwtLabel>
       <SwtComboBox dataLabel="entity"
                    id="entityCombo"
                    #entityCombo
                    enabled="false" width="135"></SwtComboBox>
       <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal" ></SwtLabel>

     </HBox>
      <HBox width="18%" #paginationData visible="false">
        <SwtCommonGridPagination #numStepper></SwtCommonGridPagination>
      </HBox>
      <HBox width="55%">
        <VBox width="100%" verticalGap="0">
          <HBox width="100%" horizontalAlign="right" height="20">
            <SwtCheckBox #currencyThreshold
                         id="currencyThreshold"
                         label="Apply Currency Threshold"
                         (change)="currencyThresholdChange($event)"></SwtCheckBox>
          </HBox>
          <HBox width="100%" horizontalAlign="right" id="filterArea" #filterArea horizontalGap="2">
          <SwtLabel text="Current Filter" paddingTop="1"></SwtLabel>
          <SwtComboBox id="filterComboMSD" #filterComboMSD
                       prompt="Ad Hoc"
                       width="332" (change)="changeFilter()"
                       dataLabel="filterList"> </SwtComboBox>
          <SwtButton id="saveFilterImage"  #saveFilterImage marginTop="3"
                     (click)="saveFilter($event)"
                     enabled="false"
                     styleName="fileSaveIcon"
          toolTip="Save filter"></SwtButton>
          <SwtButton id="deleteFilterImage" marginTop="3" marginLeft="8"
                     #deleteFilterImage
                     styleName="fileDeleteIcon"
                     toolTip="Delete filter"
                     enabled="false"
                     (click)="deleteFilterClickHandler($event)"></SwtButton>
          </HBox>
        </VBox>
      </HBox>
    </HBox>
    </SwtCanvas>
    <SwtCanvas id="dataGridContainer" #dataGridContainer minWidth="1200" width="100%" height="82%"></SwtCanvas>
    <SwtCanvas id="dataGridContainer2" #dataGridContainer2 minWidth="1200" width="100%" height="30%" visible="false" includeInLayout="false"></SwtCanvas>
    <HBox horizontalAlign="right" height="11">
      <SwtButton #imgShowHideButtonBar id="imgShowHideButtonBar" (click)="showHideButtonBar()" styleName="plusIcon"></SwtButton>
    </HBox>
    <SwtCanvas id="totalsPanel"  #totalsPanel minWidth="1200" width="100%" height="5%" marginBottom="0" visible="false" includeInLayout="false">
      <HBox width="100%" height="100%">
      <HBox width="100%" height="100%">
        <SwtLabel text="Total of selected:" fontWeight="normal"></SwtLabel>
        <SwtLabel #totalSelectedValue fontWeight="normal"></SwtLabel>
      </HBox>
      <HBox horizontalAlign="right">
     <SwtLabel text="Total in this page:" fontWeight="normal"></SwtLabel>
        <SwtLabel #totalInPageValue fontWeight="normal"></SwtLabel>
        <SwtLabel paddingLeft="15" text="Total over all pages:" fontWeight="normal"></SwtLabel>
        <SwtLabel #totalOverPagesValue paddingLeft="15" fontWeight="normal"></SwtLabel>
      </HBox>
      </HBox>
    </SwtCanvas>
    <!--<spacer width="100%" height="1%" id="spacerForGrid" #spacerForGrid></spacer>-->
    <SwtCanvas width="100%" height="40" minWidth="1200">
      <HBox width="100%">
        <HBox id="buttonBox" #buttonBox width="100%">
        </HBox>
      <HBox horizontalAlign="right" horizontalGap="3">
<VBox height="100%" verticalGap="0" marginRight="5">
        <SwtLabel  text="Difference: " fontWeight="normal" width="70"></SwtLabel>
        <SwtLabel #diffText fontWeight="normal" marginTop="-15"></SwtLabel>
</VBox>
        <VBox height="100%" verticalGap="0">
        <SwtLabel #lastRefTimeLabel text="Last Refresh:" fontWeight="normal"></SwtLabel>
          <SwtLabel #lastRefTime fontWeight="normal" marginTop="-15"></SwtLabel>
        </VBox>
        <DataExportMultiPage #exportContainer id="exportContainer" >
        </DataExportMultiPage>
        <SwtHelpButton id="helpIcon"
                       #helpIcon
                       (click)="doHelp()">
        </SwtHelpButton>
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
      </HBox>

      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
