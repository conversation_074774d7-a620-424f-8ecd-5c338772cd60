<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><fmt:message key="sweepcancelQ.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<SCRIPT language="JAVASCRIPT">
var dateFormat = '${sessionScope.CDM.dateFormat}';
var totalCount = '${totalCount}';
var currentFilter = "${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var currPage = '${requestScope.currentPage}';
var maxPage = '${requestScope.maxPage}';
var filterValues = new Array();
var currentFilterValues = currentFilter.split("|");
var sortingValues = currentSort.split("|");
var sortedValues = new Array();
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1];
var lastRefTime = "${requestScope.lastRefTime}";
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var requestURL = new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>";
var idy = requestURL.indexOf('/' + appName + '/');
requestURL = requestURL.substring(0, idy + 1);

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;
 var menuAccessId = "${requestScope.menuAccessId}";
 var queueName = '${requestScope.sweepQueue.queueName}';

 function buildViewSweepDisplayURL(selectedSweepID, selectedEntityID) {
	    var param = 'sweepsearch.do?method=view&sweepId=';
	    param += selectedSweepID;
	    param += '&entityid=';
	    param += selectedEntityID;

		openWindow(param,'sweepsearchlistWindow','left=50,top=190,width=796,height=745,toolbar=0, resizable=yes, scrollbars=yes','true');

}

function openSearch(methodName, entityId, acctType, currency, queueName){
 // variable declared the menu access
 var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Sweep Search");
 // variable declared the Menu Name
 var menuName = new String('<fmt:message key="sweepSearch.title.window"/>');
 //variable declared the smart predict
 var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
 menuName = menuName.substr(0,smrtPredPos-3);
 if (menuAccessIdOfChildWindow == 2) {
  alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');

 } else {
  var param='sweepsearch.do?method='+methodName+'&entityCode='+entityId;
  param +='&actType='+acctType;
  param +='&status='+queueName;
  param +='&currency='+currency;
  param +='&menuAccessId='+menuAccessIdOfChildWindow;

  /*Start:Code Modified for Mantis 1577 on 19-01-2011:The purpose Alignment width && height */
  openWindow(param,'SweepSearchWindow','width=911,height=488,toolbar=0, status=yes,resizable=yes, scrollbars=yes','true');
  /*End:Code Modified for Mantis 1577 on 19-01-2011:The purpose Alignment width && height */
 }
}

function openSweepQueueDetail(selectedSwpId, entityId, queueName)
{
 openWindow('sweepdetail.do?method=displayQueue&swpid='+selectedSwpId+'&entid='+entityId+'&qname='+queueName,'SweepDtlWindow','width=1021,height=635,toolbar=0, status=yes,resizable=yes, scrollbars=yes','true');
}

 function accountAccessConfirm(entityCr, entityDr, accountIdCr, accountIdDr) {
	    var oXMLHTTP = new XMLHttpRequest();
	    var sURL = requestURL + appName + "/sweepcancelqueue.do?method=acctAccessConfirm";
	    sURL = sURL + "&accountIdCr=" + accountIdCr;
	    sURL = sURL + "&entityIdCr=" + entityCr;
        sURL = sURL + "&accountIdDr=" + accountIdDr;
        sURL = sURL + "&entityIdDr=" + entityDr;
	    sURL = sURL + "&status=Sweeping";
	    oXMLHTTP.open("POST", sURL, false);
	    oXMLHTTP.send();
	    var str = oXMLHTTP.responseText;
	    return str;
	}
 
</script>
</head>

<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "sweepQueueCancel";


 </script>
<%@ include file="/angularscripts.jsp"%>
</body>

</html>