import {Component, ElementRef, HostL<PERSON>ener, NgModule, ViewChild} from '@angular/core';
import {
  HTTPComms,
  SwtButton,
  JSONReader,
  SwtAlert,
  SwtUtil,
  SwtTextInput,
  CommonService,
  SwtModule,
  Keyboard,
  focusManager,
  SwtTextArea,
  SwtCanvas,
  SwtLabel,
  SwtNumericInput, SwtComboBox, Alert, StringUtils, ExternalInterface, SwtPanel, SwtToolBoxModule
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";


declare var instanceElement: any;

@Component({
  selector: 'app-pccategory-rule-add',
  templateUrl: './CategoryRuleAdd.html',
  styleUrls: ['./CategoryRuleAdd.css']
})
export class CategoryRuleAdd extends  SwtModule {


  @HostListener('window:unload', ['$event'])
  unloadHandler(event) {
    window.opener.instanceElement.enableButtons();
  }

  private swtAlert: SwtAlert;

  /**
   * Data Objects
   **/
  public jsonReader= new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  private  inputData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private  actionMethod= "";
  private  actionPath= "";
  private  requestParams = [];

  public screenName = null;
  public helpURL=null;
  public  title=null;
  public  categoryRuleId = null;
  public  ruleId = null;
  public errorLocation=0;
  public listOrder;
  public maxOrder;
  public  ruleConditionArray = [];
  public ruleConditionObject ;
  public dataProviderSelectedIndex : any;
  public ruleQuery: string = null;
  public tabConditionFromQueryBuilder = [];
  public tabConditionFromDB = [];
  public queryToExecute: string = null;
  public queryBuilderScreenName = "";
  public tableToJoinQueryBuilder = [];

  private ordinal;
  private reOrderCR= false;
  /* - START -- Screen Name and Version number ---- */
  private moduleName = 'PC Category Rule Details';
  private versionNumber = '1.00.00';
  private releaseDate = '20 Feb 2019';
  private moduleId='PC';

  /**
   * Logic Objects
   **/
  private sourceComboChange = false;
  private entityComboChange = false;
  private ccyComboChange = false;

  /*********SwtComboBox*********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('sourceCombo') sourceCombo: SwtComboBox;
  /************SwtTextInput********/
  @ViewChild('categoryRuleNameTxtInput') categoryRuleNameTxtInput: SwtTextInput;
  /************SwtNumericInput********/
  @ViewChild('orderNumInput') orderNumInput: SwtNumericInput;
  /************SwtLabel********/
  @ViewChild('categoryRuleNameLabel') categoryRuleNameLabel: SwtLabel;
  @ViewChild('orderLabel') orderLabel: SwtLabel;
  @ViewChild('sourceLabel') sourceLabel: SwtLabel;
  @ViewChild('selectedSource') selectedSource: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('incTargLabel') incTargLabel: SwtLabel;
  @ViewChild('useliqLabel') useliqLabel: SwtLabel;
  @ViewChild('incLiqLabel') incLiqLabel: SwtLabel;

  /***LodingImage*******/
  //@ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********SWtButton*************/
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('ruleBuilderButton') ruleBuilderButton: SwtButton;

  /*********SwtCheckBox*************/
  // @ViewChild('actCheckbox') actCheckbox: SwtCheckBox;

  /*********SwtTextArea*************/
  @ViewChild('queryText') queryText: SwtTextArea;
  /*********SwtCanvas*************/
  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  /*********SwtPanel*************/
  @ViewChild('panelRuleExpression') panelRuleExpression: SwtPanel;
  @ViewChild('canvas1') canvas1: SwtCanvas;
  @ViewChild('canvasContainer') canvasContainer: SwtCanvas;


  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }


  ngOnInit(): void {
    let paramsFromParent = [];

    if(window.opener.instanceElement) {
      paramsFromParent =  window.opener.instanceElement.getParamsFromParent();
      if(paramsFromParent) {
        this.screenName =  paramsFromParent[0].screenName;
        this.categoryRuleId =  paramsFromParent[0].categoryRuleId;
        this.ruleId= paramsFromParent[0].ruleId;
        this.maxOrder = paramsFromParent[0].maxOrder;
       this.listOrder = paramsFromParent[0].listOrder;
        this.dataProviderSelectedIndex = paramsFromParent[0].dataProviderSelectedIndex;
      }
    }
    instanceElement = this;
    this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.categoryRuleNameLabel.text = SwtUtil.getPredictMessage('categoryRuleDetails.label.name', null);
    this.categoryRuleNameTxtInput.toolTip = SwtUtil.getPredictMessage('categoryRuleDetails.tooltip.name', null);
    this.orderLabel.text = SwtUtil.getPredictMessage('categoryRuleDetails.label.order', null);
    this.orderNumInput.toolTip = SwtUtil.getPredictMessage('categoryRuleDetails.tooltip.order', null);
    this.sourceLabel.text = SwtUtil.getPredictMessage('categoryRuleDetails.label.source', null);
    this.sourceCombo.toolTip = SwtUtil.getPredictMessage('categoryRuleDetails.tooltip.source', null);
    this.entityLabel.text = SwtUtil.getPredictMessage('categoryRuleDetails.label.entity', null);
    this.entityCombo.toolTip = SwtUtil.getPredictMessage('categoryRuleDetails.tooltip.entity', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('categoryRuleDetails.label.ccy', null);
    this.ccyCombo.toolTip = SwtUtil.getPredictMessage('categoryRuleDetails.tooltip.ccy', null);
    this.ruleBuilderButton.label = SwtUtil.getPredictMessage('categoryRuleDetails.rule.expression.button.label', null);
    this.panelRuleExpression.title = SwtUtil.getPredictMessage('categoryRuleDetails.rule.expression.title', null);


    this.categoryRuleNameTxtInput.enabled = true;
    if(this.screenName ==="add"){
      this. saveButton.visible = true;
      this.ccyCombo.enabled = true;
      this.sourceCombo.enabled = true;
      this.entityCombo.enabled = true;
      this.ruleBuilderButton.enabled = true;
      this.categoryRuleNameTxtInput.setFocus();
      this.orderNumInput.enabled=true;
      this.orderNumInput.text = Number(this.maxOrder);
    }else if(this.screenName === "view"){
      this.categoryRuleNameTxtInput.enabled = false;
      this. saveButton.visible = false;
      this.ccyCombo.enabled = false;
      this.sourceCombo.enabled = false;
      this.entityCombo.enabled = false;
      this.ruleBuilderButton.enabled = false;
      this.orderNumInput.enabled=false;
    }else if (this.screenName === "change") {
      this. saveButton.visible = true;
      this.ccyCombo.enabled = true;
      this.sourceCombo.enabled = true;
      this.entityCombo.enabled = true;
      this.orderNumInput.enabled=true;
      this.ruleBuilderButton.enabled = true;
    }

  }

  /**
   * onLoad
   * Initializer (multilingual, fetch list details )
   */
  onLoad():void {
    try{

      this.requestParams = [];
      this.actionPath='categoryRulesPCM.do?';
      //Checks the screen is add and calls the add action
      if(this.screenName != "add"){
        this.actionMethod='method=view';
      } else{
        this.actionMethod='method=add';
      }
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);

      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      this.inputData.cbFault=this.inputDataFault.bind(this);
      this.inputData.encodeURL=false;
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

    } catch(error){
      SwtUtil.logError(error, this.moduleId, "CategoryRuleAdd", "onLoad", this.errorLocation);
    }
  }
  /**
   * startOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms():void {
    this.disableComponents(false);
  }

  /**
   * endOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms():void {
    this.disableComponents(true);
  }

  /**
   * inputDataResult
   * param event: ResultEvent
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(event): void {
    try{
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        /* Get result as xml */
        this.lastRecievedJSON =event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        /* Condition to check lastRecievedXML not equal to prevRecievedXML */
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          /* Condition to check request reply status is true*/
          if (this.jsonReader.getRequestReplyStatus()) {
            if (!this.jsonReader.isDataBuilding()) {
              this.ccyCombo.setComboData(this.jsonReader.getSelects(), false);
              this.sourceCombo.setComboData(this.jsonReader.getSelects(), false);
              this.entityCombo.setComboData(this.jsonReader.getSelects(), false);

              if(this.screenName != "add"){
                this.categoryRuleNameTxtInput.text =  this.dataProviderSelectedIndex.categoryRuleName;
                this.queryText.text =  this.dataProviderSelectedIndex.ruleText;
                this.ordinal = Number(this.dataProviderSelectedIndex.ordinal);
                this.orderNumInput.text = this.ordinal;

                if(typeof(this.dataProviderSelectedIndex.toSource)== "string"){
                  this.sourceCombo.selectedLabel=this.dataProviderSelectedIndex.toSource;
                  this.selectedSource.text= this.sourceCombo.selectedItem.value;

                }
                if(typeof(this.dataProviderSelectedIndex.toCcy)== "string"){
                  this.ccyCombo.selectedLabel = this.dataProviderSelectedIndex.toCcy;
                  this.selectedCcy.text= this.ccyCombo.selectedItem.value;
                }
                if(typeof(this.dataProviderSelectedIndex.toEntity)== "string"){
                  this.entityCombo.selectedLabel = this.dataProviderSelectedIndex.toEntity;
                  this.selectedEntity.text= this.entityCombo.selectedItem.value;
                }

                if(this.dataProviderSelectedIndex.ruleConditions) {
                  this.tabConditionFromDB = JSON.parse(this.dataProviderSelectedIndex.ruleConditions);
                  if (this.tabConditionFromDB.length !== 0) {
                    for (let i = 0; i < this.tabConditionFromDB.length; i++) {
                      this.ruleConditionObject = new Object();
                      this.ruleConditionObject.id =  new Object();
                      this.ruleConditionObject.conditionId  = this.tabConditionFromDB[i].id.conditionId;
                      this.ruleConditionObject.columnToStore = StringUtils.trim(this.tabConditionFromDB[i].fieldName);
                      this.ruleConditionObject.operation = (this.tabConditionFromDB[i].operatorId) ? this.tabConditionFromDB[i].operatorId : '';
                      this.ruleConditionObject.columnCodeValue  = StringUtils.trim(this.tabConditionFromDB[i].fieldValue);
                      this.ruleConditionObject.localValue = (this.tabConditionFromDB[i].localValue) ? this.tabConditionFromDB[i].localValue : '';
                      this.ruleConditionObject.tabName = this.tabConditionFromDB[i].tableName;
                      this.ruleConditionObject.profileField = this.tabConditionFromDB[i].profileField;
                      this.ruleConditionObject.enumProfile  = this.tabConditionFromDB[i].profileFieldValue;
                      if (i + 1 < this.tabConditionFromDB.length) {
                        this.ruleConditionObject.andOrString  = this.tabConditionFromDB[i].nextCondition;
                      }

                      if (this.tabConditionFromDB[i].typeCode === 'DECI') {
                        this.ruleConditionObject.dataType = 'NUM';
                      } else {
                        this.ruleConditionObject.typeCode  = this.tabConditionFromDB[i].dataType;
                      }

                      this.tabConditionFromQueryBuilder.push(this.ruleConditionObject);
                    }
                    this.ruleConditionArray = this.tabConditionFromDB
                  }
                  for (let i = 0; i < this.tabConditionFromQueryBuilder.length; i++) {
                    this.tableToJoinQueryBuilder.push(this.tabConditionFromQueryBuilder[i].tabName);
                  }
                } else {
                  this.tabConditionFromQueryBuilder = [];
                }
               this.queryToExecute = this.dataProviderSelectedIndex.ruleQuery;
              }
            }
          }else{
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage());
          }
        }
      }
    } catch(error){
      console.log(error, this.moduleId, "CategoryRuleAdd", "inputDataResult", this.errorLocation);
    }
  }

  /**
   * inputDataFault
   * param event:  FaultEvent
   * This is a callback , used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event):void {
    let message = SwtUtil.getPredictMessage('label.genericException', null);
    this.swtAlert.error(message);
  }



  /**
   * save()
   * Method called on save button clicked
   */
  save():void {
    let toSource:string = "";
    let toCcy:string = "";
    let toEntity:string = "";
    try {
      if(this.queryText.text.length==0 || this.categoryRuleNameTxtInput.text.length==0 || !(this.orderNumInput.text)  || this.orderNumInput.text == ""){
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.mandatoryField', null));
        return;
      }
      if(this.categoryRuleNameTxtInput.text.trim().length == 0) {
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.CategoryRuleNameCannotEmpty', null));
        return;
      }

      if(this.sourceCombo.selectedIndex !== -1){
        toSource = this.sourceCombo.selectedItem.content;
      }
      if(this.ccyCombo.selectedIndex !== -1){
        toCcy = this.ccyCombo.selectedItem.content;
      }

      if(this.entityCombo.selectedIndex !== -1){
        toEntity = this.entityCombo.selectedItem.content;
      }
      window.opener.instanceElement.refreshParent(this.reOrderCR, this.ordinal, this.categoryRuleId,(this.categoryRuleNameTxtInput.text)? this.categoryRuleNameTxtInput.text: "",(this.orderNumInput.text) ? this.orderNumInput.text : "",
        toSource, toCcy, toEntity,this.ruleId,  this.queryText.text, this.queryToExecute,
        JSON.stringify(this.ruleConditionArray), JSON.stringify(this.tableToJoinQueryBuilder));
        this.popupClosed();
    } catch (error) {
      console.log(error, this.moduleId, "CategoryRuleAdd", "save");
      // log the error in ERROR LOG
      //SwtUtil.logError(error, this.moduleId, "CategoryRuleAdd", "save", this.errorLocation);
    }

  }


  /**
   * This method is used to handle the entity change.
   * This loads the entity with the default currency.
   */
  entityChangeCombo(){
    this.selectedEntity.text= this.entityCombo.selectedItem.value;
    this.entityComboChange=true;
  }

  /**
   * This method is used to handle the entity change.
   * This loads the entity with the default currency.
   */
  ccyChangeCombo(){
    this.selectedCcy.text= this.ccyCombo.selectedItem.value;
    this.ccyComboChange=true;
  }


  /**
   * This method is used to handle the entity change.
   * This loads the entity with the default currency.
   */
  sourceChangeCombo(){
    this.selectedSource.text= this.sourceCombo.selectedItem.value;
    this.sourceComboChange=true;
  }

  /**
   * disableComponents()
   * Method called to disable components
   *
   */
  disableComponents(value): void {
    try {

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryRuleAdd', 'disableComponents', this.errorLocation);
    }
  }

  /**
   * keyDownEventHandler
   * param event
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  keyDownEventHandler(event):void {
    try {
      //Currently focussed property name
      let eventString:string=Object(focusManager.getFocus()).name;
      if ((event.keyCode == Keyboard.ENTER)) {
        if (eventString == "ruleBuilderButton") {
          this.doAddRule(event);
        } else if (eventString == "saveButton") {
          this.save();
        } else if (eventString == "cancelButton") {
          this.popupClosed();
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryRuleAdd", "keyDownEventHandler");
    }
  }


  /**
   * changeEventHandler
   * param event:
   */
  changeEventHandler(event):void {
    try {

      let eventString:string=Object(focusManager.getFocus()).name;
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryRuleAdd", "changeEventHandler");
    }
  }

  /**
   * focusOutOrdinalInput
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  focusOutOrdinalInput():void {
    try {
      if(this.ordinal !== Number(this.orderNumInput.text)){
        if(Number(this.orderNumInput.text) <= 0) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.orderHigherThanZero', null));
          return;
        }
        Alert.yesLabel = 'Yes';
        Alert.noLabel = 'No';
        const message: string = SwtUtil.getPredictMessage('alert.CategoryRuleReorder', null);
        if(this.screenName =="add"){
          if(!(Number(this.orderNumInput.text) >= this.maxOrder )){
            if( this.listOrder.indexOf(Number(this.orderNumInput.text)) !== -1){
              this.swtAlert.confirm(message, 'Alert', Alert.YES | Alert.NO, null, this.ordinalAlertListener.bind(this));
            }
          }
        }else {// change
          if( this.listOrder.indexOf(this.orderNumInput.text) !== -1){
            this.swtAlert.confirm(message, 'Alert', Alert.YES | Alert.NO, null, this.ordinalAlertListener.bind(this));
          }
        }
      }

    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryRuleAdd", "focusOutOrdinalInput");
    }
  }

  /**
   * ordinalAlertListener
   * @param event:
   *
   * Method to Alert
   */
  ordinalAlertListener(event):void {
    try {
      if (event.detail === Alert.YES) {
        this.reOrderCR= true;
      }else{
        this.reOrderCR= false;
        if(this.screenName=="add"){
          this.orderNumInput.text=this.maxOrder;
        }else{
          let order= String(this.dataProviderSelectedIndex.ordinal);
          if(order == ""){
            this.orderNumInput.text = this.maxOrder;
          }else{
            this.orderNumInput.text=this.dataProviderSelectedIndex.ordinal;
          }
        }

      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryRuleAdd", "ordinalAlertListener");
    }
  }

  /**
   * doAddRule
   * @param event:
   */
  doAddRule(event):void {
    try {
      if (this.queryText.text !== '' && this.queryText.text !== null) {
        Alert.yesLabel = "Replace";
        Alert.noLabel = "Change";
        let message ="Do you want to replace all constraints or only change values for existing constraints?";
        this.swtAlert.confirm(message, "", Alert.YES | Alert.NO | Alert.CANCEL, null, this.clearRuleListener.bind(this));
      } else {
        this.queryBuilderScreenName = "add";
        this.saveButton.enabled = false;
        // let newWindow = window.open("/expressionBuilder", 'expression Builder', 'height=550,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
        // if (window.focus) {
        //   newWindow.focus();
        // }

        ExternalInterface.call('openChildWindow');
      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryRuleAdd", "doAddRule");
    }
  }

  clearRuleListener(event): any {
    try {
      if (event.detail === Alert.YES) {
        this.queryBuilderScreenName = "add";
        this.saveButton.enabled = false;
        // let newWindow = window.open("/expressionBuilder", 'expression Builder', 'height=550,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
        // if (window.focus) {
        //   newWindow.focus();
        // }
        ExternalInterface.call('openChildWindow');
      } else if(event.detail === Alert.NO){
        this.queryBuilderScreenName = "change";
        this.saveButton.enabled = false;
        // let newWindow = window.open("/expressionBuilder", 'expression Builder', 'height=550,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
        // if (window.focus) {
        //   newWindow.focus();
        // }
        ExternalInterface.call('openChildWindow');
      }else {

      }
    } catch (error) {
      console.log(error, this.moduleId, "CategoryRuleAdd", "clearRuleListener");
    }
  }

  getParamsFromParent() {
    let params = [];
    if(this.queryBuilderScreenName == "add") {
      params = [
        { screenName: this.queryBuilderScreenName, queryToDisplay: '', queryToExecute: '',
          tabAllConditions: [], tableToJoin: [] }];
    }else {
      params = [
        { screenName: this.queryBuilderScreenName, queryToDisplay: this.queryText.text, queryToExecute: this.queryToExecute,
          tabAllConditions: JSON.stringify(this.tabConditionFromQueryBuilder), tableToJoin: JSON.stringify(this.tableToJoinQueryBuilder) }
      ];
    }
    return params;
  }


  saveRuleDetails(tabCondition:any, tableToJoin:any, queryToDisplay:string,  queryToExecute:string) {
    try{
      this.ruleConditionArray = [];
      this.ruleConditionObject;
      this.tabConditionFromQueryBuilder = JSON.parse(tabCondition);
      if (this.tabConditionFromQueryBuilder.length !== 0) {
        for (let i = 0; i < this.tabConditionFromQueryBuilder.length; i++) {

          this.ruleConditionObject = new Object();
          this.ruleConditionObject.id =  new Object();

          this.ruleConditionObject.id.conditionId = this.tabConditionFromQueryBuilder[i].conditionId;
          this.ruleConditionObject.fieldName = StringUtils.trim(this.tabConditionFromQueryBuilder[i].columnToStore);
          this.ruleConditionObject.operatorId = (this.tabConditionFromQueryBuilder[i].operation) ? this.tabConditionFromQueryBuilder[i].operation : '';
          this.ruleConditionObject.fieldValue = StringUtils.trim(this.tabConditionFromQueryBuilder[i].columnCodeValue);
          this.ruleConditionObject.localValue = (this.tabConditionFromQueryBuilder[i].localValue) ? this.tabConditionFromQueryBuilder[i].localValue : '';
          this.ruleConditionObject.tableName = this.tabConditionFromQueryBuilder[i].tabName;
          this.ruleConditionObject.profileField = this.tabConditionFromQueryBuilder[i].profileField;
          this.ruleConditionObject.profileFieldValue = this.tabConditionFromQueryBuilder[i].enumProfile;

          if (i + 1 < this.tabConditionFromQueryBuilder.length) {
            this.ruleConditionObject.nextCondition = this.tabConditionFromQueryBuilder[i + 1].andOrString;
          }

          if (this.tabConditionFromQueryBuilder[i].typeCode === 'DECI') {
            this.ruleConditionObject.dataType = 'NUM';
          } else {
            this.ruleConditionObject.dataType = this.tabConditionFromQueryBuilder[i].typeCode;
          }

          this.ruleConditionArray.push(this.ruleConditionObject);
        }
      }
      this.tableToJoinQueryBuilder = JSON.parse(tableToJoin);
      this.queryText.text = queryToDisplay;
      this.queryToExecute = queryToExecute;
    }catch(error){
      console.log(error, this.moduleId, "CategoryRuleAdd", "saveRuleDetails");
    }

  }

  enableButtons() {
    this.saveButton.enabled = true;
  }
  /**
   * popupClosed
   * Method to close child windows when this screen is closed
   */
  popupClosed():void {
    this.dispose();
  }

  /**
   * dispose
   * This is a event handler, used to close the current tab/window
   */
  dispose():void {
    try {
      this.requestParams=null;
      this.baseURL=null;
      this.actionMethod=null;
      this.actionPath=null;
      if(window.opener.instanceElement) {
        window.opener.instanceElement.refreshGrid();
      }
      if(this.titleWindow){
        this.close();
      }else {
        window.close();
      }

    } catch (error) {
      console.log(error, this.moduleId, "CategoryRuleAdd", "dispose");
    }
  }

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: CategoryRuleAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CategoryRuleAdd],
  entryComponents: []
})
export class CategoryRuleAddModule {}
