<SwtModule (creationComplete)='onLoad()' width="100%" height="100%" paddingTop="10" paddingBottom="5">
  <VBox width='100%' height='100%' paddingLeft="10" paddingRight="10">
    <SwtCanvas width="100%" height="9%">
        <HBox height="28" paddingLeft="10" marginTop="8">
            <SwtLabel text="Currency" width="115"></SwtLabel>
            <SwtComboBox id="currencyComboBox"
                          #currencyComboBox
                          width="100"
                          dataLabel="currencyList"
                          toolTip="Currency Tooltip"
                          (change)="changeComboCurrency($event)">
            </SwtComboBox>
             <SwtLabel #ccyLabel fontWeight="normal"  text=" " paddingLeft="10"></SwtLabel>
          </HBox>
    </SwtCanvas>
    <SwtCanvas id="canvasGrid" 
                #canvasGrid 
                width="100%" 
                height="79%">
    </SwtCanvas>
    <SwtCanvas width="100%" height="7%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%" >
          <SwtButton [buttonMode]="true" 
                      id="addButton" 
                      #addButton 
                      enabled="false"
                      (click)="doAddSpreadProfilesMaintenance($event)"
                      (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true" 
                      id="changeButton" 
                      #changeButton
                      enabled="false"
                      (click)="doChangeSpreadProfilesMaintenance($event)" 
                      (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true" 
                      id="viewButton" 
                      #viewButton 
                      (click)="doViewSpreadProfilesMaintenance($event)"
                      (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true" 
                      id="deleteButton" 
                      #deleteButton
                      enabled="false"
                      (click)="doDeleteSpreadProfilesMaintenance($event)" 
                      (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true" 
                      id="closeButton" 
                      #closeButton 
                      (click)="closeCurrentTab($event)"
                      (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
          <SwtHelpButton id="helpIcon" 
                          [buttonMode]="true" 
                          enabled="true" 
                          helpFile="spread-profile" 
                          (click)="doHelp()">
          </SwtHelpButton>
        </HBox>
	  </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
