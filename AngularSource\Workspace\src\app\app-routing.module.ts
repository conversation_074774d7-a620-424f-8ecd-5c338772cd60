import { ModuleWithProviders, NgModule } from '@angular/core';
import { RouterModule, Routes, ROUTES } from '@angular/router';
import {MovementMatchSummaryDisplay} from './com/swallow/predict/MovementMatchSummaryDisplay/MovementMatchSummaryDisplay';


const routes: Routes = [

  {
    path: '',
    loadChildren: './com/swallow/predict/JsAngularBridge/JsAngularBridge#JsAngularBridgeModule',
    data: { title: 'jsAngularBridge' }
},
  {
    path: 'MessageDetails',
    loadChildren: './com/swallow/predict/InputExceptionMonitor/MessageDetails/MessageDetails#MessageDetailsModule',
    data: { title: 'Input Exception' }
  },
  {
    path: 'InputException',
    loadChildren: './com/swallow/predict/InputExceptionMonitor/InputException#InputExceptionModule',
    data: { title: 'Input Exception' }
  },
  {
    path: 'AttributeUsage',
    loadChildren: './com/swallow/predict/AccountAttributes/AttributeUsage/AttributeUsage#AttributeUsageModule',
    data: { title: 'Usage Attribute' }
  },

  {
    path: 'AttributeUsageAdd',
    loadChildren: './com/swallow/predict/AccountAttributes/AttributeUsage/AttributeUsageAdd/AttributeUsageAdd#AttributeUsageAddModule',
    data: { title: 'Usage Attribute Add' }
  },

  {
    path: 'AttributeDefinition',
    loadChildren: './com/swallow/predict/AccountAttributes/AttributeDefinition/AttributeDefinition#AttributeDefinitionModule',
    data: { title: 'Define Attribute' }
  },
  {
    path: 'AttributeDefinitionAdd',
    loadChildren: './com/swallow/predict/AccountAttributes/AttributeDefinition/AttributeDefinitionAdd/AttributeDefinitionAdd#AttributeDefinitionAddModule',
    data: { title: 'Define Attribute Add' }
  },

  {
    path: 'AccountAttributeLastValues',
    loadChildren: './com/swallow/predict/AccountAttributes/Accountattributelastvalues/AccountAttributeLastValues#AccountAttributeLastValuesModule',
    data: { title: 'Account Attribute Last Values' }
  },

  {
    path: 'AccountAttributeMaintenance',
    loadChildren: './com/swallow/predict/AccountAttributes/AccountAttributeMaintenance/AccountAttributeMaintenance#AccountAttributeMaintenanceModule',
    data: { title: 'Account Attribute maintenance' }
  },
  {
    path: 'AccountAttributeMaintenanceAdd',
    loadChildren: './com/swallow/predict/AccountAttributes/AccountAttributeMaintenance/AccountAttributeMaintenanceAdd/AccountAttributeMaintenanceAdd#AccountAttributeMaintenanceAddModule',
    data: { title: 'Account Attribute maintenance Add' }
  },
  {
    path: 'currency',
    loadChildren: './com/swallow/pcm/CurrencyMaintenance/CurrencyMaintenance#CurrencyMaintenanceModule',
    data: { title: 'currency maintenance' }
  },
  {
    path: 'currencyMaintenanceAdd',
    loadChildren: './com/swallow/pcm/CurrencyMaintenance/CurrencyMaintenanceAdd/CurrencyMaintenanceAdd#CurrencyMaintenanceAddModule',
    data: { title: 'currency maintenance' }
  },
  {
    path: 'category',
    loadChildren: './com/swallow/pcm/CategoryMaintenance/CategoryMaintenance#CategoryMaintenanceModule',
    data: { title: 'category Rule maintenance' }
  },
  {
    path: 'categoryAdd',
    loadChildren: './com/swallow/pcm/CategoryMaintenance/CategoryMaintenanceAdd/CategoryMaintenanceAdd#CategoryMaintenanceAddModule',
    data: { title: 'category maintenance Add' }
  },
  {
    path: 'categoryRuleAdd',
    loadChildren: './com/swallow/pcm/CategoryMaintenance/CategoryMaintenanceAdd/CategoryRuleAdd/CategoryRuleAdd#CategoryRuleAddModule',
    data: { title: 'category Rule Add' }
  },
  { path: 'expressionBuilder',
    loadChildren: './com/swallow/pcm/RulesDefinitionAddRule/RulesDefinitionAddRule#ExpressionBuilderModule',
    pathMatch: 'full'
  },
  {
    path: 'spreadProfiles',
    loadChildren: './com/swallow/pcm/SpreadProfilesMaintenance/SpreadProfilesMaintenance#SpreadProfilesMaintenanceModule',
    data: { title: 'Spread Profiles Maintenance' }
  },
  {
    path: 'spreadProfilesAdd',
    loadChildren: './com/swallow/pcm/SpreadProfilesMaintenance/SpreadProfilesMaintenanceAdd/SpreadProfilesMaintenanceAdd#SpreadProfilesMaintenanceAddModule',
    data: { title: 'Spread Profiles Maintenance Add' }
  },
  {
    path: 'spreadDetails',
    loadChildren: './com/swallow/pcm/SpreadProfilesMaintenance/SpreadProfilesMaintenanceAdd/SpreadDetails/SpreadDetails#SpreadDetailsModule',
    data: { title: 'Spread Process Point Add' }
  },
  {
    path: 'AccountsGroup',
    loadChildren: "./com/swallow/pcm/AccountGroupsMaintenance/AccountGroups/AccountGroups#AccountGroupsModule",
    data: { title: 'Accounts Group' }
  },
  { path: 'AccountGroupDetail',
    loadChildren: "./com/swallow/pcm/AccountGroupsMaintenance/AccountGroupsAdd/AccountGroupsAdd#AccountGroupsAddModule",
    pathMatch: 'full'
  },
  { path: 'DashboardDetails',
    loadChildren: "./com/swallow/pcm/PCMBreakdownMonitor/PCMBreakdownMonitor#PCMBreakdownMonitorModule",
    pathMatch: 'full'
  },
  { path: 'PaymentDisplay',
    loadChildren: "./com/swallow/pcm/PaymentRequestDisplay/PaymentRequestDisplay#PaymentRequestDisplayModule",
    pathMatch: 'full'
  },
  {
    path: 'PartySearch',
    loadChildren: './com/swallow/pcm/PartySearch/PartySearch#PartySearchModule',
    data: { title: 'Party Search' }
  },
  {
    path: 'PCMMonitor',
    loadChildren: "./com/swallow/pcm/PCMMonitor/PCMMonitor#PCMMonitorModule",
    data: { title: 'PCM Monitor' }
  },
  {
    path: 'stopRules',
    loadChildren: './com/swallow/pcm/StopRules/StopRules#StopRulesModule',
    data: { title: 'stop Rules' }
  },
  {
    path: 'stopRuleAdd',
    loadChildren: './com/swallow/pcm/StopRules/details/StopRulesAdd/StopRulesAdd#StopRulesAddModule',
    data: { title: 'stop Rules Add' }
  },
  {
    path: 'pcmReport',
    loadChildren: './com/swallow/pcm/report/Report#PCReportModule',
    data: { title: 'PCM Report' }
  },
  {
    path: 'paymentSearch',
    loadChildren: './com/swallow/pcm/PaymentRequestSearch/PaymentRequestSearch#PaymentRequestSearchModule',
    data: { title: 'Payment Request Search' }
  },
  {
    path: 'paymentArchiveSearch',
    loadChildren: './com/swallow/pcm/PaymentArchiveSearch/PaymentArchiveSearch#PaymentArchiveSearchModule',
    data: { title: 'Payment Archive Search' }
  },
  {
    path: 'paymentLogs',
    loadChildren: './com/swallow/pcm/PaymentRequestLog/PaymentRequestLog#PaymentRequestLogModule',
    data: { title: 'Payment Logs' }
  },
  {
    path: 'paymentStopRules',
    loadChildren: './com/swallow/pcm/PaymentRequestStopRulesSummary/PaymentRequestStopRulesSummary#PaymentRequestStopRulesSummaryModule',
    data: { title: 'Payment Stop Rules Summary' }
  },
  {
    path: 'paymentMessages',
    loadChildren: './com/swallow/pcm/PaymentRequestMessageSummary/PaymentRequestMessageSummary#PaymentRequestMessageSummaryModule',
    data: { title: 'Payment Message Summary' }
  },
  {
    path: 'paymentResponses',
    loadChildren: './com/swallow/pcm/PaymentRequestResponsesSummary/PaymentRequestResponsesSummary#PaymentRequestResponsesSummaryModule',
    data: { title: 'Payment Responses Summary' }
  },
  {
    path: 'accountMonitor',
    loadChildren: './com/swallow/predict/AccountMonitor/AccountMonitor#AccountMonitorModule',
    data: { title: 'Account Monitor' }
  },
  {
    path: 'accountspecificsweepformat',
    loadChildren: './com/swallow/predict/AccountSpecificSweepFormat/accountspecificsweepformat#AccountSpecificSweepFormatModule',
    data: { title: 'Account Specific Sweep Format' }
  }
  ,
  {
    path: 'accountspecificsweepformatadd',
    loadChildren: './com/swallow/predict/AccountSpecificSweepFormatAdd/accountspecificsweepformatadd#AccountSpecificSweepFormatAddModule',
    data: { title: 'Account Specific Sweep Format Add' }
  },
  {
    path: 'schedReportHistory',
    loadChildren: './com/swallow/predict/SchedReportHistory/SchedReportHistory#SchedReportHistoryModule',
    data: { title: 'Scheduled Report History' }
  },
  {
    path: 'schedReportHistAdd',
    loadChildren: './com/swallow/predict/SchedReportHistAdd/SchedReportHistAdd#SchedReportHistAddModule',
    data: { title: 'Scheduled Report History Details' }
  },


  {
    path: 'ForecastMonitor',
    loadChildren: './com/swallow/predict/ForecastMonitor/ForecastMonitor#ForecastMonitorModule',
    data: { title: 'Forecast Monitor' }
  },
  {
    path: 'ForecastMonitorOptions',
    loadChildren: './com/swallow/predict/ForecastMonitor/ForecastMonitorOptions/ForecastMonitorOptions#ForecastMonitorOptionsModule',
    data: { title: 'Forecast Monitor Options' }
  },
  {
    path: 'TemplateOptions',
    loadChildren: './com/swallow/predict/ForecastMonitor/TemplateOptions/TemplateOptions#TemplateOptionsModule',
    data: { title: 'Template Options' }
  },
  {
    path: 'forecastMonitorTemplate',
    loadChildren: './com/swallow/predict/ForecastMonitor/ForecastMonitorTemplate/ForecastMonitorTemplate#ForecastMonitorTemplateModule',
    data: { title: 'Forecast Monitor Template' }
  },
  {
    path: 'forecastMonitorTemplateMain',
    loadChildren: './com/swallow/predict/ForecastMonitor/ForecastMonitorTemplateMain/ForecastMonitorTemplateMain#ForecastMonitorTemplateMainModule',
    data: { title: 'Forecast Monitor Template Main' }
  },
  {
    path: 'forecastMonitorTemplateDetail',
    loadChildren: './com/swallow/predict/ForecastMonitor/ForecastMonitorTemplateDetail/ForecastMonitorTemplateDetail#ForecastMonitorTemplateDetailModule',
    data: { title: 'Forecast Monitor Template Detail' }
  },
  {
    path: 'copyForecastTemplate',
    loadChildren: './com/swallow/predict/ForecastMonitor/CopyForecastTemplate/CopyForecastTemplate#CopyForecastTemplateModule',
    data: { title: 'Copy Forecast Template' }
  },
  {
    path: 'findPopUp',
    loadChildren: './com/swallow/predict/ForecastMonitor/FindPopUp/FindPopUp#FindPopUpModule',
    data: { title: 'Find Pop Up' }
  },
  {
    path: 'subTotalPopUp',
    loadChildren: './com/swallow/predict/ForecastMonitor/SubTotalPopUp/SubTotalPopUp#SubTotalPopUpModule',
    data: { title: 'Sub Total Pop Up' }
  },
  {
    path: 'interfaceMonitor',
    loadChildren: './com/swallow/predict/InterfaceMonitor/InterfaceMonitor#InterfaceMonitorModule',
    data: { title: 'Interface Monitor' }
  },
  {
    path: 'genericDisplay',
    loadChildren: './com/swallow/predict/EnhancedAlerting/genericDisplay/GenericDisplay#GenericDisplayModule',
    data: { title: 'Generic Display' }
  },
  {
    path: 'scenarioSummary',
    loadChildren: './com/swallow/predict/EnhancedAlerting/ScenarioSummary/ScenarioSummary#ScenarioSummaryModule',
    data: { title: 'Scenario Summary' }
  },
  {
    path: 'ilmCurrencyCalculation',
    loadChildren: './com/swallow/predict/IlmCurrencyCalculation/IlmCurrencyCalculation#IlmCurrencyCalculationModule',
    data: { title: 'ILm Currency Calculation' }
  },

  //Predict Screens

  {
    path: 'currencyMonitor',
    loadChildren: './com/swallow/predict/CurrencyMonitor/CurrencyMonitor#CurrencyMonitorModule',
    data: { title: 'Currency Monitor' }
  },
  {
    path: 'bookGroupMonitor',
    loadChildren: './com/swallow/predict/BookGroupMonitor/BookGroupMonitor#BookGroupMonitorModule',
    data: { title: 'Book Group Monitor ' }
  },
  {
    path: 'corporateAccount',
    loadChildren: './com/swallow/predict/CentralBankMonitor/CorporateAccount/CorporateAccount#CorporateAccountModule',
    data: { title: 'Corporate Account' }
  },
  {
    path: 'ilmAccountGroupDetail',
    loadChildren: './com/swallow/predict/IntraDayLiquidityMonitor/IlmAccountGroupDetails/IlmAccountGroupDetails#IlmAccountGroupDetailslModule',
    data: {title: 'ILm Account Monitor'}
  },
  {
    path: 'centralBankMonitor',
    loadChildren: './com/swallow/predict/CentralBankMonitor/CentralBankMonitor/CentralBankMonitor#CentralBankMonitorModule',
    data: { title: 'Central Bank Monitor' }
  },
  {
    path: 'msd',
    loadChildren: './com/swallow/predict/MovementSummaryDisplay/MovementSummaryDisplay#MovementSummaryDisplayModule',
    data: { title: 'Movement Summary Display' }
  },
  {
    path: 'entityMonitor',
    loadChildren: './com/swallow/predict/EntityMonitor/EntityMonitor#EntityMonitorModule',
    data: { title: 'Entity Monitor' }
  },
  {
    path: 'entityMonitorOptions',
    loadChildren: './com/swallow/predict/EntityMonitor/EntityMonitorOptions/EntityMonitorOptions#EntityMonitorOptionsModule',
    data: { title: 'Entity Monitor' }
  },
  {
    path: 'personalEntityList',
    loadChildren: './com/swallow/predict/EntityMonitor/PersonalEntityList/PersonalEntityList#PersonalEntityListModule',
    data: { title: 'Personal Entity List' }
  },
  {
    path: 'personalCcy',
    loadChildren: './com/swallow/predict/EntityMonitor/PersonalCurrency/PersonalCurrency#PersonalCurrencyModule',
    data: { title: 'Personal Currency' }
  },

  {
    path: 'MovementMatchSummaryDisplay',
    loadChildren: './com/swallow/predict/MovementMatchSummaryDisplay/MovementMatchSummaryDisplay#MovementMatchSummaryDisplayModule',
    data: { title: 'Movement Match Summary Display' }
  },
  {
    path: 'InterfaceException',
    loadChildren: './com/swallow/predict/InterfaceException/InterfaceException#InterfaceExceptionModule',
    data: { title: 'Input Exception' }
  },
  {
    path: 'connectionPool',
    loadChildren: './com/swallow/predict/ConnectionPoolStats/ConnectionPoolStats#ConnectionPoolStatsModule',
    data: { title: 'Connection Pool Stats' }
  },
  {
    path: 'connectionPoolDetails',
    loadChildren: './com/swallow/predict/ConnectionPoolStats/ConnectionPoolDetails/ConnectionPoolDetails#ConnectionPoolDetailsModule',
    data: { title: 'Connection Pool Stats' }
  },
  {
    path: 'workFlowMonitor',
    loadChildren: './com/swallow/predict/WorkFlowMonitor/WorkFlowMonitor#WorkFlowMonitorModule',
    data: { title: 'Work Flow Monitor' }
  },
  {
    path: 'inputConfig',
    loadChildren: './com/swallow/predict/InputConfiguration/InputConfiguration#InputConfigurationModule',
    data: { title: 'Input Configuration' }
  },
  {
    path: 'jsAngularBridge',
    loadChildren: './com/swallow/predict/JsAngularBridge/JsAngularBridge#JsAngularBridgeModule',
    data: { title: 'jsAngularBridge' }
  },
  {
    path: 'ilmSaveProfilePopup',
    // component: IntraDayLiquidityMonitorModule,
    loadChildren: './com/swallow/predict/IntraDayLiquidityMonitor/intradayliquidity/controls/saveProfilePopup/saveProfilePopup#SaveProfilePopupModule'
  },
  {
    path: 'plainMessage',
    // component: IntraDayLiquidityMonitorModule,
    loadChildren: './com/swallow/predict/InputExceptionMonitor/PlainMessage/PlainMessage#PlainMessageModule'
  },
  {
    path: 'ForecastAssumption',
    loadChildren: './com/swallow/predict/ForecastMonitor/ForecastMonitorAssumptions/ForecastMonitorAssumptions#ForecastMonitorAssumptionsModule'
  },


    {
    path: 'forecastAssumptionAdd',
    loadChildren: './com/swallow/predict/ForecastMonitor/ForecastMonitorAssumptions/ForecastMonitorAssumptionsAdd/ForecastMonitorAssumptionsAdd#ForecastMonitorAssumptionsAddModule'
  },
  {
    path: 'ILMThroughPutRatioMonitor',
    // component: IntraDayLiquidityMonitorModule,
   loadChildren: './com/swallow/predict/IntraDayLiquidityMonitor/ILMThroughPutRatioMonitor/ILMThroughPutRatioMonitor#ILMThroughPutRatioMonitorModule'
  },
  {
    path: 'ILMThroughPutBreakDown',
    loadChildren: './com/swallow/predict/IntraDayLiquidityMonitor/ILMThroughPutRatioMonitor/ILMThroughPutRatioBreakdown/ILMThroughPutRatioBreakdown#ILMThroughPutRatioBreakdownModule'
  },
  {
    path: 'scenarioDetail',
    loadChildren: './com/swallow/predict/EnhancedAlerting/ScenarioDetail/ScenarioDetail#ScenarioDetailModule'
  },
  {
    path: 'guiHighlightSub',
    loadChildren: './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/GuiHighlight/GuiHighlightAdd/GuiHighlightAdd#GuiHighlightAddModule'
  },
  {
    path: 'eventsSub',
    loadChildren: './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/Events/EventsAdd/EventsAdd#EventsAddModule'
  },
  {
    path: 'defParam',
    loadChildren: './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/General/DefineParams/DefineParams#DefineParamsModule'
  },
  {
    path: 'scheduleDetails',
    loadChildren: './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/General/ScheduleDetails/ScheduleDetails#ScheduleDetailsModule'
  },
  {
    path: 'configScreen',
    loadChildren: './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/General/ConfigureParams/ConfigureParams#ConfigureParamsModule'
  },
  // {
  //   path: 'ilm',
  //   loadChildren: './com/swallow/predict/IntraDayLiquidityMonitor/ilm/ilm.component#IlmComponentModule'
  // },
  {
    path: 'ilmOptions',
    loadChildren: './com/swallow/predict/IntraDayLiquidityMonitor/options/options#OptionsModule'
  },
  {
    path: 'PreAdviceInput',
    loadChildren: './com/swallow/predict/PreAdviceInput/PreAdviceInput#PreAdviceInputModule',
    data: { title: 'Pre Advice Input' }
  },
  {
    path: 'ImportPreAdvices',
    loadChildren: './com/swallow/predict/PreAdviceInput/ImportPreAdvices/ImportPreAdvices#ImportPreAdvicesModule',
    data: { title: 'Import Pre Advice' }
  },
  {
  path: 'AcctCcyPeriodMaint',
  loadChildren: './com/swallow/predict/AcctCcyPeriodMaint/AcctCcyPeriodMaint#AcctCcyPeriodMaintModule',
  data: { title: 'Account Currency Maintenance Period' }
  },
  {
    path: 'AcctCcyPeriodMaintAdd',
    loadChildren: './com/swallow/predict/AcctCcyPeriodMaint/AcctCcyPeriodMaintAdd/AcctCcyPeriodMaintAdd#AcctCcyPeriodMaintAddModule',
    data: { title: 'Account Currency Maintenance Period Add' }
  },
  {
    path: 'AcctCcyPeriodMaintLog',
    loadChildren: './com/swallow/predict/AcctCcyPeriodMaint/AcctCcyPeriodMaintLog/AcctCcyPeriodMaintLog#AcctCcyPeriodMaintLogModule',
    data: { title: 'Account Currency Maintenance Period Log' }
  },
  {
    path: 'AcctCcyPeriodMaintViewLog',
    loadChildren: './com/swallow/predict/AcctCcyPeriodMaint/AcctCcyPeriodMaintLog/AcctCcyPeriodMaintViewLog/AcctCcyPeriodMaintViewLog#AcctCcyPeriodMaintViewLogModule',
    data: { title: 'Account Currency Maintenance Period Log' }
  },
  {
    path: 'AlertInstanceSummary',
    loadChildren: './com/swallow/predict/AlertInstanceSummary/AlertInstanceSummary#AlertInstanceSummaryModule',
    data: { title: 'Alert Instance Summary' }
  },
  {
    path: 'AlertInstanceDisplay',
    loadChildren: './com/swallow/predict/AlertInstanceSummary/AlertInstanceDisplay/AlertInstanceDisplay#AlertInstanceDisplayModule',
    data: { title: 'Alert Instance Display' }
  },
  {
    path: 'AlertInstMsgDisplay',
    loadChildren: './com/swallow/predict/AlertInstanceSummary/AlertInstanceDisplay/AlertInstMsgDisplay/AlertInstMsgDisplay#AlertInstMsgDisplayModule',
    data: { title: 'Alert Instance Messages Display' }
  },
  {
    path: 'EnhancedAlertJsp',
    loadChildren: './com/swallow/predict/EnhancedAlertJsp/EnhancedAlertJsp#EnhancedAlertJspModule',
    data: { title: '' }
  },
  {
    path: 'ILMMainScreen',
    loadChildren: './com/swallow/predict/IntraDayLiquidityMonitor/ilmMainScreen/ilmMainScreen#ILMMainScreenModule'
  },
  // {
  //   path: 'newAccountMonitor',
  //   loadChildren: './com/swallow/predict/newAccountMonitor/newAccountMonitor#NewAccountMonitorModule'
  // },
  {
    path: 'addColsForSearch',
    loadChildren: './com/swallow/predict/AddColsForSearch/AddColsForSearch#AddColsForSearchModule',
    data: { title: 'Additinal Columns For Search' }
  },
  

  {
    path: 'LogonScreen',
    loadChildren: './com/swallow/predict/LogonScreen/LogonScreen#LogonScreenModule'
  },

  {
    path: 'AccountScheduleSweepDetails',
    loadChildren: './com/swallow/predict/AccountScheduleSweepDetails/AccountScheduleSweepDetails#AccountScheduleSweepDetailsModule'
  }, 
  {
    path: 'AcctSweepBalGrp',
    loadChildren: './com/swallow/predict/AcctSweepBalGrp/AcctSweepBalGrp#AcctSweepBalGrpModule'
  },
  {
    path: 'CashRsrvBalManagmnt',
    loadChildren: './com/swallow/predict/CashRsvrBalManagmnt/CashRsvrBalManagmnt#CashRsvrBalManagmntModule'
  },
  {
    path: 'AcctMaintViewLog',
    loadChildren: './com/swallow/predict/MaintenanceLogView/MaintenanceLogView#MaintenanceLogViewModule'
  },
  {
    path: 'AcctMaintViewLog',
    loadChildren: './com/swallow/predict/MaintenanceLogView/MaintenanceLogView#MaintenanceLogViewModule'
  },

  {
    path: 'MaintenanceEventDetails',
    loadChildren: './com/swallow/predict/MaintenanceEventDetails/MaintenanceEventDetails#MaintenanceEventDetailsModule'
},
{
  path: 'MaintenanceEvent',
  loadChildren: './com/swallow/predict/MaintenanceEvent/MaintenanceEvent#MaintenanceEventModule'
},
   {
     path: 'AccountMaintenance',
    loadChildren: './com/swallow/predict/AccountMaintenance/AccountMaintenance#AccountMaintenanceModule'
   },
{
    path: 'msdAdditionalColumns',
    loadChildren: './com/swallow/predict/MovementSummaryDisplay/AdditionalColumns/AdditionalColumns#AdditionalColumnsModule'
  },
  {
    path: 'saveMsdProfilePopup',
    loadChildren: './com/swallow/predict/MovementSummaryDisplay/SaveMsdProfilePopup/SaveMsdProfilePopup#SaveMsdProfilePopupModule'
  },
  {
    path: 'roleBasedControl',
    loadChildren: './com/swallow/predict/RoleBasedControl/RoleBasedControl#RoleBasedControlModule'
  },
  {
    path: 'sweepQueue',
    loadChildren: './com/swallow/predict/SweepQueue/SweepQueue#SweepQueueModule'
  },
  {
    path: 'sweepQueueCancel',
    loadChildren: './com/swallow/predict/SweepQueueCancel/SweepQueueCancel#SweepQueueCancelModule'
  },
  {
    path: 'SweepSearchList',
    loadChildren: './com/swallow/predict/SweepSearchList/SweepSearchList#SweepSearchListModule',
    data: { title: 'Sweep Search List' }
  },
  {
    path: 'ManualSweep',
    loadChildren: './com/swallow/predict/ManualSweep/ManualSweep#ManualSweepModule',
    data: { title: 'ManualSweep ' }
  },
  {
  path: 'MessageFormats',
  loadChildren: './com/swallow/predict/MessageFormats/MessageFormats#MessageFormatsModule',
  data: { title: 'Sweep Message Format Maintenance' }, 
},
{
  path: 'BalMaintenance',
  loadChildren: './com/swallow/predict/BalMaintenance/BalMaintenance#BalMaintenanceModule',
  data: { title: 'Start of Day Balance Maintenance' }
},
{
  path: 'ErrorLog',
  loadChildren: './com/swallow/predict/ErrorLog/ErrorLog#ErrorLogModule',
  data: { title: 'Error Log' }
}, 
{
  path: 'MaintenanceLog',
  loadChildren: './com/swallow/predict/MaintenanceLog/MaintenanceLog#MaintenanceLogModule',
  data: { title: 'MaintenanceLog' }
}, 
{
  path: 'BatchScheduler',
  loadChildren: './com/swallow/predict/BatchScheduler/BatchScheduler#BatchSchedulerModule',
  data: { title: 'BatchScheduler' }
}, 
{
  path: 'SystemLog',
  loadChildren: './com/swallow/predict/SystemLog/SystemLog#SystemLogModule',
  data: { title: 'System Log' }
}, 
{
  path: 'CriticalMvtUpdateMaint',
  loadChildren: './com/swallow/predict/CriticalMvtUpdateMaintenance/CriticalMvtUpdateMaintenance#CriticalMvtUpdateMaintenanceModule',
  data: { title: 'Critical Movement Update Maintenance' }
}, 
{
  path: 'CriticalMvtUpdateDetail',
  loadChildren: './com/swallow/predict/CriticalMvtUpdateMaintenance/CriticalMvtUpdateDetail/CriticalMvtUpdateDetail#CriticalMvtUpdateDetailModule',
  data: { title: 'Critical Movement Update Detail' }
},
{
  path: 'EmailTemplateMaintenance',
  loadChildren: './com/swallow/predict/EmailTemplateMaintenance/EmailTemplateMaintenance#EmailTemplateMaintenanceModule',
  data: { title: 'Email Templat eMaintenance' }
},{
  path: 'EmailTemplateMaintenanceDetails',
  loadChildren: './com/swallow/predict/EmailTemplateMaintenance/EmailTemplateMaintenanceAdd/EmailTemplateMaintenanceAdd#EmailTemplateMaintenanceAddModule',
  data: { title: 'Email Template Maintenance Detail' }
},
{
  path: 'ConfigRecipients',
  loadChildren: './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/Events/EventsAdd/ConfigureRecipients/ConfigureRecipients#ConfigureRecipientsModule',
  data: { title: 'Configure Recipients' }
}, 
{
  path: 'AuditLog',
  loadChildren: './com/swallow/predict/AuditLog/AuditLog#AuditLogModule',
  data: { title: 'Audit Log' }
}, 
{
  path: 'AuditLogView',
  loadChildren: './com/swallow/predict/AuditLog/AuditLogView/AuditLogView#AuditLogViewModule',
  data: { title: 'Audit Log View' }
}, 
{
  path: 'MyUserAuditLog',
  loadChildren: './com/swallow/predict/MyUserAuditLog/MyUserAuditLog#MyUserAuditLogModule',
  data: { title: 'Audit Log' }
},
{
  path: 'MultipleMvtActions', 
  loadChildren: './com/swallow/predict/MultipleMvtActions/MultipleMvtActions#MultipleMvtActionsModule',
  data: { title: 'Multiple Movement Action' }
},

{
  path: 'PartyMaintenance', 
  loadChildren: './com/swallow/predict/PartyMaintenance/PartyMaintenance#PartyMaintenanceModule',
  data: { title: 'Party Maintenance' }
}
,
  {
    path: 'CorrespondentAcctMaintenance',
    loadChildren: './com/swallow/predict/CorrespondentAcctMaintenance/CorrespondentAcctMaintenance#CorrespondentAcctMaintenanceModule',
    data: { title: 'Correspondent Account Maintenance' }
  }
];
export const routing = RouterModule.forRoot(routes);

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
  providers: [{
    provide: "routes",
    useValue: routes
  }]
})
export class AppRoutingRoutingModule {
  static forRoot(): ModuleWithProviders {
    return {
      ngModule: AppRoutingRoutingModule,
      providers: [
        // provider for Angular CLI to analyze
        { provide: ROUTES, useValue: routes, multi: true },
        { provide: "routes", useValue: routes },
      ],
    };
  }
}