import { HttpClient } from "@angular/common/http";
import { Component, ElementRef, OnInit, ViewChild, NgModule } from '@angular/core';
import { PaginationChangedArgs } from 'angular-slickgrid';
import { CommonService, HBox, HTTPComms, JSONReader, Logger, SwtAlert, SwtButton, SwtCanvas, SwtCommonGrid, SwtCommonGridPagination, SwtLoadingImage, SwtModule, SwtPopUpManager, SwtTextInput, SwtUtil, SwtComboBox, SwtLabel, SwtDateField, SwtRadioButtonGroup, SwtCheckBox, ExternalInterface, SwtToolBoxModule } from "swt-tool-box";
import moment from "moment";
import { ModuleWithProviders } from '@angular/compiler/src/core';
import { RouterModule, Routes } from '@angular/router';

@Component({
  selector: 'app-pcm-report',
  templateUrl: './Report.html',
  styleUrls: ['./Report.css']
})
export class Report extends SwtModule implements OnInit {
  @ViewChild('reportTypeCombo') reportTypeCombo: SwtComboBox;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('acctGrpCombo') acctGrpCombo: SwtComboBox;
  @ViewChild('sourceCombo') sourceCombo: SwtComboBox;

  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedAcctGrp') selectedAcctGrp: SwtLabel;
  @ViewChild('selectedSource') selectedSource: SwtLabel;
  @ViewChild('toLabel') toLabel: SwtLabel;

  @ViewChild('frmDateChooser') frmDateChooser: SwtDateField;
  @ViewChild('toDateChooser') toDateChooser: SwtDateField;

  @ViewChild('dateGroup') dateGroup: SwtRadioButtonGroup;
  @ViewChild('exportTypeGroup') exportTypeGroup: SwtRadioButtonGroup;

  @ViewChild('useCcyMultiplier') useCcyMultiplier: SwtCheckBox;

  rows: any;
  _selectedRowData: any;
  listValues: SwtCommonGrid;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  //**********************  TextInput ***************************************************/ 
  @ViewChild('partyIdInput') partyIdInput: SwtTextInput;
  @ViewChild('partyNameInput') partyNameInput: SwtTextInput;
  //**********************  Buttons ****************************************************/ 
  @ViewChild('reportButton', { read: SwtButton }) reportButton: SwtButton;
  @ViewChild('cancelButton', { read: SwtButton }) cancelButton: SwtButton;
  @ViewChild('closeButton', { read: SwtButton }) closeButton: SwtButton;
  /*********HBox*****************/
  @ViewChild('filterHbox') filterHbox: HBox;
  gridJSONList: any;
  gridJSONListValue: any;
  // get code of the selected column in search screen
  private columnCode: string = null;
  // get the current operation used in search screen
  private operation: string = null;
  // get label of the selected column in search screen
  private columnLabel: string = null;
  //To hold the help message
  private message: string = null;
  //To hold the dataSource
  private dataSource: string = null;
  /** 
* Private parameters 
**/
  private logger: Logger;
  private programId: string = "404";  //set program
  private componentId: string = null; //hold componentId
  private menuAccess: string = "0";//To store menu access
  private alertMsg: string;
  private alertMsgToShow: string;
  /**
   * Data Objects
   **/
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  private errorLocation = 0;
  public moduleReportURL: string = null;
  public moduleId = '';
  public viewOnly: boolean = false;
  //Variable to store the last page no
  public lastNumber: Number = 0;
  // flag variable to display the first record if page number is changed
  private dateFormat;
  private singleOrRange;
  private firstDateOfPreviousMonth;
  private lastDateOfPreviousMonth;
  private yesterday;

  dataProviderTemp: any;
  constructor(private element: ElementRef, httpClient: HttpClient, private commonService: CommonService) {
    super(element, commonService);
    this.logger = new Logger("ListValues", httpClient);
    this.swtAlert = new SwtAlert(commonService);
  }


  ngOnInit(): void {

    //this.toDateChooser.visible = false;
    if (this.viewOnly) {
      this.reportButton.enabled = false;
    }
  }
  onLoad() {
    try {
      this.toDateChooser.visible = false;
      this.toLabel.visible = false;
      this.message = SwtUtil.getAMLMessages('pcpriorityMaintenanceScreen.message.help_message');
      this.actionPath = 'reportPCM.do?';
      this.actionMethod = 'method=reportDisplay';
      this.requestParams['moduleId'] = this.moduleId;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      //this.inputDataResult(data);

      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      // Assining properties for controls
      this.reportButton.label = "Report";//String(SwtUtil.getCommonMessages('button.add'));
      this.cancelButton.label = "Cancel";//String(SwtUtil.getCommonMessages('button.search'));
      this.closeButton.label = "Close";//String(SwtUtil.getCommonMessages('button.close'));
    } catch (error) {

    }
  }


  /**
  * inputDataResult
  *
  * @param data: ResultEvent
  *
  * This is a callback method, to handle result event
  *
  */
  public inputDataResult(data): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {
            this.dateFormat = this.jsonReader.getSingletons().dateformat;
            this.singleOrRange = this.jsonReader.getSingletons().singleOrRange;
            this.firstDateOfPreviousMonth = this.jsonReader.getSingletons().firstDateOfPreviousMonth;
            this.lastDateOfPreviousMonth = this.jsonReader.getSingletons().lastDateOfPreviousMonth;
            this.yesterday = this.jsonReader.getSingletons().fromDate;
            this.useCcyMultiplier.selected = this.jsonReader.getSingletons().useCcyMultiplier == 'Y';
            this.frmDateChooser.formatString = this.dateFormat.toLowerCase();
            this.toDateChooser.formatString = this.dateFormat.toLowerCase();
            if (this.singleOrRange == 'S') {
              this.frmDateChooser.text = this.yesterday;
              this.toDateChooser.visible = false;
              this.toLabel.visible = false;
            } else {
              this.frmDateChooser.text = this.firstDateOfPreviousMonth;
              this.toDateChooser.text = this.lastDateOfPreviousMonth;
              this.toDateChooser.visible = true;
              this.toLabel.visible = true;
            }
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {

              this.componentId = this.lastRecievedJSON.screenid;
              this.menuAccess = this.jsonReader.getScreenAttributes()["menuaccess"];
              this.fillComboData();
            }

            this.prevRecievedJSON = this.lastRecievedJSON;

          } else {
            this.swtAlert.error(SwtUtil.getCommonMessages('alert.generic_exception'));
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error inputDataResult', e);
    }
  }

  fillComboData(): void {

    try {
      this.reportTypeCombo.setComboData(this.jsonReader.getSelects(), false);
      this.ccyCombo.setComboData(this.jsonReader.getSelects(), false);
      this.acctGrpCombo.setComboData(this.jsonReader.getSelects(), false);
      this.sourceCombo.setComboData(this.jsonReader.getSelects(), false);
      this.entityCombo.setComboData(this.jsonReader.getSelects(), false);
      /*********Labels********/
      this.selectedAcctGrp.text = this.acctGrpCombo.selectedItem.value;
      this.selectedCcy.text = this.ccyCombo.selectedItem.value;
      this.selectedEntity.text = this.entityCombo.selectedItem.value;
      this.selectedSource.text = this.sourceCombo.selectedItem.value;

    } catch (err) {
      console.log('err', err);
    }
  }

  disableOrEnableButtons(isRowSelected: Boolean): void {
    if (isRowSelected) {
      this.reportButton.enabled = true;
    } else {
      this.reportButton.enabled = false;
    }
  }

  /**
   * inputDataFault
   *
   * @param event:  FaultEvent
   *
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    try {
      this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'inputDataFault', this.errorLocation);
    }
  }

  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
     ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }
  }

  /**
   * close
   *
   * @param  event:Event
   *
   * Method to close this module
   */
  cancelHandler(event): void {
    //Variable for errorLocation
    var errorLocation = 0;
    try {
      ExternalInterface.call("close");
    }
    catch (error) {
      this.logger.error("method [cancelHandler] - error : ", error, "- errorLocation :", errorLocation);

      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this) + ".ts", "cancelHandler", errorLocation);
    }
  }

  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    try {
      this.loadingImage.setVisible(true);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'startOfComms', this.errorLocation);
    }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    try {
      this.loadingImage.setVisible(false);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'endOfComms', this.errorLocation);
    }
  }

  doReport(event): void {
    try {
      if (!this.checkDates() && this.dateGroup.selectedValue == 'D') {
        this.swtAlert.warning('End Date must be later than Start date');
        return;
      }
      ExternalInterface.call('report', this.exportTypeGroup.selectedValue, this.reportTypeCombo.selectedItem.value, this.entityCombo.selectedItem.content,
        this.ccyCombo.selectedItem.content, this.acctGrpCombo.selectedItem.content, this.sourceCombo.selectedItem.content,
        this.frmDateChooser.text, this.toDateChooser.text, this.useCcyMultiplier.selected ? 'Y' : 'N', this.dateGroup.selectedValue, 
        this.selectedAcctGrp.text, this.selectedCcy.text, this.selectedEntity.text, this.selectedSource.text);
      /*this.reportButton.enabled = false;
      this.cancelButton.enabled = true;*/
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "PCM Report", "doReport", this.errorLocation);
    }
  }

  cancelReport(event): void {
    /*ExternalInterface.call('cancelPCMExport');
    this.reportButton.enabled = true;
    this.cancelButton.enabled = false;*/
  }

  changeCombo(event): void {
    try {
      this.selectedAcctGrp.text = this.acctGrpCombo.selectedItem.value;
      this.selectedCcy.text = this.ccyCombo.selectedItem.value;
      this.selectedEntity.text = this.entityCombo.selectedItem.value;
      this.selectedSource.text = this.sourceCombo.selectedItem.value;
      if (event.target.id == this.ccyCombo.id) {
        this.requestParams = [];
        this.requestParams['reportType'] = this.reportTypeCombo.selectedItem.content;
        this.requestParams['entity'] = this.entityCombo.selectedItem.content;
        this.requestParams['currencyCode'] = this.ccyCombo.selectedItem.content;
        this.requestParams['accountGroup'] = this.acctGrpCombo.selectedItem.content;
        this.requestParams['source'] = this.sourceCombo.selectedItem.content;
        this.requestParams['useCcyMultiplier'] = this.useCcyMultiplier.selected ? 'Y' : 'N';
        this.requestParams['singleOrRange'] = this.dateGroup.selectedValue;
        this.requestParams['selectedFromDate'] = this.frmDateChooser.text;
        this.requestParams['selectedToDate'] = this.toDateChooser.text;
        this.requestParams['isCurrencyChanged'] = true;

        this.inputData.send(this.requestParams);
      }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "onLoad", this.errorLocation);
    }

  }

  onDateChange(): void {
    if (this.dateGroup.selectedValue == 'S') {
      this.frmDateChooser.text = this.yesterday;
      this.toDateChooser.visible = false;
      this.toLabel.visible = false;
    } else {
      this.frmDateChooser.text = this.firstDateOfPreviousMonth;
      this.toDateChooser.text = this.lastDateOfPreviousMonth;
      this.toDateChooser.visible = true;
      this.toLabel.visible = true;
    }
  }

  checkDates() {

    try {
      var startDate: any;
      var endDate: any;
      if (this.frmDateChooser.text) {
        startDate = moment(this.frmDateChooser.text, this.dateFormat.toUpperCase(), true);
      }
      if (this.toDateChooser.text) {
        endDate = moment(this.toDateChooser.text, this.dateFormat.toUpperCase(), true);
      }
      
      if (!startDate && endDate) {
        return false;
      }
      
      if (startDate && endDate && endDate.isBefore(startDate)) {
        return false;
      }

      return true;
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "checkDates", this.errorLocation);

    }
  }
}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: Report }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [Report],
  entryComponents: []
})
export class PCReportModule { }