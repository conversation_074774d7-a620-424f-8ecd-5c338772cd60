<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingRight="5">
    <HBox width="100%" height="30">
      <HBox width="270" horizontalGap="5" paddingRight="10">
        <SwtLabel id="profileIdLbl" #profileIdLbl></SwtLabel>
        <SwtComboBox id="profileCombo" #profileCombo width="200" 
        (change)="changeProfile($event)" dataLabel="profileList"> </SwtComboBox>
      </HBox>
      <HBox horizontalGap="5" paddingTop="3">
        <SwtButton id="saveProfileImage" style="z-index: 4;" #saveProfileImage (click)="addProfile($event)"
          styleName="fileSaveIcon" enabled="false">
        </SwtButton>
        <SwtButton id="revertProfileImage" style="z-index: 4;" #revertProfileImage (click)="revertProfileClickHandler($event)"
        styleName="fileRevertIcon" enabled="false">
       </SwtButton>
        <SwtButton id="deleteProfileImage" #deleteProfileImage style="z-index: 4;" (click)="deleteProfileClickHandler($event)"
          styleName="fileDeleteIcon" enabled="false"></SwtButton>

      </HBox>
      <!--<HBox width="50%" horizontalGap="5" horizontalAlign="right" paddingRight="10">
          <SwtLabel id="generalProfileLbl" #generalProfileLbl></SwtLabel>
          <SwtCheckBox id="generalProfilecheck" #generalProfilecheck selected="true"></SwtCheckBox>
      </HBox>-->
    </HBox>
    <!--<VBox #gridContainer id="gridContainer" height="100%" width="100%">
      <HBox width="100%" paddingBottom="5">
        <HBox height="2%" width="50%">
        </HBox>
        <HBox height="2%" width="50%" horizontalAlign="right" paddingRight="15">
          <SwtButton buttonMode="true" #addColsButton id="addColsButton" label="+"
          enabled="true" (click)="addColumns()" width="30"></SwtButton>
          <SwtButton  buttonMode="true" #removeColsButton id="removeColsButton" label="-"
          enabled="false" (click)="removeColumns()" width="30">
          </SwtButton>

        </HBox>
      </HBox>

      <HBox width="100%" height="98%">-->
        <SwtCanvas #colCanvas id="colCanvas" width="100%" height="90%" minHeight="100" minWidth="350"></SwtCanvas>
     <!-- </HBox>
    </VBox>-->
    <SwtCanvas width="100%" height="35" paddingTop="5" minWidth="300">
      <HBox width="100%" horizontalGap="5" paddingLeft="10">
        <SwtButton #addButton id="addButton" enabled="true" (click)="addHandler()"></SwtButton>
        <SwtButton #deleteButton id="deleteButton" enabled="false" (click)="deleteHandler()"></SwtButton>
      </HBox>
      <HBox width="100%" horizontalGap="5" horizontalAlign="right" paddingRight="10">
       <!--- <SwtButton #saveButton id="saveButton" enabled="true" (click)="save()"></SwtButton>
        <SwtButton #saveAsButton id="saveAsButton" enabled="true" (click)="saveNew()"></SwtButton>-->
        <SwtButton #closeButton id="closeButton" enabled="true" (click)="closeAndUpdate()"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>