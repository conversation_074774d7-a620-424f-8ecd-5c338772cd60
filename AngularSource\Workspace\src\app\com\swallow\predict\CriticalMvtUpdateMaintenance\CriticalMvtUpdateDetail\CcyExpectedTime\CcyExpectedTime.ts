import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { SwtModule, Logger, SwtAlert, CommonService, SwtUtil, SwtLabel, SwtTextInput, SwtComboBox, ExternalInterface, SwtButton } from 'swt-tool-box';
declare  function validateFormatTime(strField): any;
@Component({
  selector: 'app-ccy-expected-time',
  templateUrl: './CcyExpectedTime.html',
  styleUrls: ['./CcyExpectedTime.css']
})
export class CcyExpectedTime extends SwtModule implements OnInit {

  @ViewChild('ccyLbl') ccyLbl: SwtLabel;
  @ViewChild('expectedTimeLbl') expectedTimeLbl: SwtLabel;
  @ViewChild('expTimeTxt') expTimeTxt: SwtTextInput;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  /***********SwtButton***********/
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  
  private logger: Logger;
  private swtAlert: SwtAlert;
  public ccyComboData;
  public gridData= [];
  public selectedRow;
  public selectedIndex;
  public action;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Currency Default Expected Time Popup', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.ccyLbl.text= SwtUtil.getPredictMessage('criticalMvtUpdate.currencyId', null);
    this.expectedTimeLbl.text= SwtUtil.getPredictMessage('criticalMvtUpdate.expectedTime', null);
    this.ccyCombo.toolTip= SwtUtil.getPredictMessage('criticalMvtUpdate.tooltip.currencyId', null);
    this.expTimeTxt.toolTip= SwtUtil.getPredictMessage('criticalMvtUpdate.tooltip.expectedTime', null);
    this.okButton.label= SwtUtil.getPredictMessage('button.ok', null);
    this.okButton.toolTip= SwtUtil.getPredictMessage('button.ok', null);
    this.cancelButton.label= SwtUtil.getPredictMessage('button.cancel', null);
    this.cancelButton.toolTip= SwtUtil.getPredictMessage('tooltip.cancelbutton', null);
  }

  onLoad(){
  this.ccyCombo.setComboData(this.ccyComboData);
  if(this.action=='change'){
  this.ccyCombo.enabled=false;
  this.ccyCombo.selectedLabel= this.selectedRow.ccy.content;
  this.expTimeTxt.text= this.selectedRow.defaultExpectedTime.content;
  }
  }


  validateTime(textInput): any {
 
    let validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text  && validateFormatTime(textInput) == false) {
      this.swtAlert.warning(validTimeMessage, null );
      textInput.text = "";
      return false;
    }

  }

  okHandler() {
    if (!this.expTimeTxt.text) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.criticalPaymentType.emptyExpectedTime', null), null);
    } else {
      let ccy = this.ccyCombo.selectedLabel;
      let time = this.expTimeTxt.text;
      
      let ccyList = [];
      if (this.action == "add") {
        for (let i = 0; i < this.gridData.length; i++) {
          if (this.gridData[i].ccy) {
            ccyList.push(this.gridData[i].ccy);
          }
        }
        if (ccyList.includes(ccy)) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.criticalPaymentType.ccyAlreadyExists', null), null);
          return;
        } else {
         // this.gridData.push({ ccy: { clickable: false, content: ccy, negative: false }, defaultExpectedTime: { clickable: false, content: time, negative: false } });
         let item: any;
         item = {
           'ccy' : {'content':ccy},
           'defaultExpectedTime': {'content': time}
         };
         this.parentDocument.ccyGrid.appendRow(item, false,false);
        }
      } else {
        this.parentDocument.ccyGrid.dataProvider[this.selectedIndex].ccy = ccy;
        this.parentDocument.ccyGrid.dataProvider[this.selectedIndex].slickgrid_rowcontent.ccy.content = ccy;
        this.parentDocument.ccyGrid.dataProvider[this.selectedIndex].defaultExpectedTime = time;
        this.parentDocument.ccyGrid.dataProvider[this.selectedIndex].slickgrid_rowcontent.defaultExpectedTime.content = time;
      }
      this.parentDocument.ccyGrid.refresh();
      this.close();
    }
  }

  closeHandler(){
    this.close();
  }

}
