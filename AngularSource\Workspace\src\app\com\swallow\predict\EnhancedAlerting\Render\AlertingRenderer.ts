import { Column, Formatter } from "angular-slickgrid/public_api";


export const AlertingRenderer: Formatter = (row: number, cell: number, value: any, columnDef: any, dataContext: any) => {

    let datagrid = columnDef.params.grid;
    let dataRow = dataContext['slickgrid_rowcontent'];
    let dataIndex = dataRow['id'];
    let columnName = columnDef.field;

    let backgroundColor = columnDef.params.rowColorFunction(datagrid, dataIndex, 'transparent');
    let enabledFlag = columnDef.params.enableDisableCells(dataRow, columnName);
    let showHideCells = columnDef.params.showHideCells(dataRow, columnName);

    let type = columnDef['columnType'] ? String(columnDef['columnType']) : null;
    let imageSource = null;
    let style = columnDef.properties ? columnDef.properties.style : '';
    let buttonMode: boolean = columnDef.properties ? columnDef.properties._buttonMode : false;
    let toolTipFlag: boolean = columnDef.properties ? columnDef.properties._toolTipFlag : false;
    let toolTip = '';

    let useHandCursor = false;
    if (showHideCells) {
        if (columnName == 'alerting' && dataRow.alerting && dataRow.alerting.content == "C") {
            imageSource = columnDef.properties ? columnDef.properties.imageCritEnabled : null;
        }
        else if (columnName == 'alerting' && dataRow.alerting && dataRow.alerting.content == "Y") {
            imageSource = columnDef.properties ? columnDef.properties.imageEnabled : null;
        }
        if (buttonMode) {
            useHandCursor = true;
        }
    }
    if (imageSource != null) {
        return `<img src="${imageSource}" class="alertIcon"  style="height:18px;width:18px; ${style} ${useHandCursor ? 'cursor: pointer;' : ''}" title="${toolTip}">
        </img>`;
    }
    return ``;

};
