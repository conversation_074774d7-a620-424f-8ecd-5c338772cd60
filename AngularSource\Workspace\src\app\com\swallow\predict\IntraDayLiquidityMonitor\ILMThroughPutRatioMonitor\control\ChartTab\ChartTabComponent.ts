import { Component, OnInit, NgModule, ModuleWithProviders, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { SwtToolBoxModule, SwtModule, CommonService, TabChange, TabSelectEvent } from 'swt-tool-box';
import { RouterModule, Routes } from '@angular/router';
import ResizeObserver from 'resize-observer-polyfill';

import * as Highcharts from 'highcharts';
import { Subscription } from 'rxjs';
import { unsubscribeAllObservables } from 'angular-slickgrid';
import { ChartData } from './ChartData';
import moment from "moment";
declare var require: any;
let Boost = require('highcharts/modules/boost');
let noData = require('highcharts/modules/no-data-to-display');
let More = require('highcharts/highcharts-more');
const Exporting = require("highcharts/modules/exporting");
Exporting(Highcharts);


Boost(Highcharts);
noData(Highcharts);
More(Highcharts);
noData(Highcharts);

@Component({
  selector: 'ChartTabComponent',
  templateUrl: './ChartTabComponent.html',
  styleUrls: ['./ChartTabComponent.css']
})
export class ChartTabComponent extends SwtModule implements OnInit, OnDestroy {

  @ViewChild("containerHighChart") containerHighChart: ElementRef;
  private subscriptions: Subscription[] = [];
  threshold1LabelTime: string;
  threshold2LabelTime: string;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
  }

  public timeData: string;
  public percentData: string;
  public timeDataAsArray: string[];
  public percentDataAsArray: number[];
  public percentDataOrignal: number[];
  public chart: Highcharts.Chart = null;
  private options;
  private threshold1TimeIndex;
  private threshold2TimeIndex;
  private threshold1Time;
  private threshold2Time;
  private threshold1Percentage;
  private threshold2Percentage;
  private startTime;
  private endTime;
  //Threshold state can 0 => (not yet) , 1  => (successfully passed) or 2  => not successfuly passed
  private threshold1State = 0;
  private threshold2State = 0;
  private colorArray = ['grey', 'green', 'red']

  ngOnInit() {

    const that = this;

    this.options = {
      chart: {
        animation: false,
        type: "spline",
        events: {

          load: function () {

            var point = this.series[1].points[0],
              {
                plotX: x,
                plotY: y
              } = point;

            point.path = this.renderer.path()
              .add(point.series.group)
              .attr({
                stroke: point.color,
                'stroke-width': 2,
                d: `M 0 ${y} L ${x} ${y} L ${x} ${this.plotHeight}`
              });

            point.Label = this.renderer.label(
              this.series[1].points[0].x + "%",
              250,
              point.plotY - 20,
              'rect', null, null, true
            )
              .attr({
                // align: 'right',
              })
              .add();


            var point2 = this.series[1].points[1],
              {
                plotX: x,
                plotY: y
              } = point2;

            point2.path = this.renderer.path()
              .add(point2.series.group)
              .attr({
                stroke: point2.color,
                'stroke-width': 2,
                d: `M 0 ${y} L ${x} ${y} L ${x} ${this.plotHeight}`
              });
            if (this.series[0].points[that.threshold1TimeIndex]) {

              this.series[0].points[that.threshold1TimeIndex].update({
                marker: {
                  enabled: true,
                  fillColor: point.color,
                }
              });
            }
            if (this.series[0].points[that.threshold2TimeIndex]) {
              this.series[0].points[that.threshold2TimeIndex].update({
                marker: {
                  enabled: true,
                  fillColor: point2.color,
                }
              });
            }

          },
          redraw: function () {



            var point = this.series[1].points[0],
              {
                plotX: x,
                plotY: y
              } = point,

              path = point.path;

            if (path) {
              path.attr({
                stroke: point.color,
                d: `M 0 ${y} L ${x} ${y} L ${x} ${this.plotHeight}`
              })
            }

            try {
              if (point.Label) {
              point.Label.destroy();
              }
            } catch (error) {

            }

            if (this.series[1].points[0].y > 0 && that.threshold1TimeIndex > -1) {

            point.Label = this.renderer.label(
              this.series[1].points[0].y + "%",
              250,
              point.plotY - 20,
              'rect', null, null, true
            )
              .attr({
                align: 'right',
              }).css({
                color: point.color,
                fontSize: '16px',
                fontWeight: 'bold',
                fontStyle: 'normal',
              })
              .add();

            }

            var point2 = this.series[1].points[1],
              {
                plotX: x,
                plotY: y
              } = point2,

              path2 = point2.path;


            if (path2) {
              path2.attr({
                stroke: point2.color,
                d: `M 0 ${y} L ${x} ${y} L ${x} ${this.plotHeight}`
              })
            }
            try {
            if (point2.Label2)
              point2.Label2.destroy();
            } catch (error) {

            }
            if (this.series[1].points[0].y > 0 && that.threshold2TimeIndex > -1) {
            point2.Label2 = this.renderer.label(
              this.series[1].points[1].y + "%",
              250,
              point2.plotY - 20,
              'rect', null, null, true
            )
              .attr({
                align: 'right',
              }).css({
                color: point2.color,
                fontSize: '16px',
                fontWeight: 'bold',
                fontStyle: 'normal',
              })
              .add();
          }
            
            if(this.yAxis[0].max < 100) {
              this.yAxis[0].update({max : 100});
            }else if(this.yAxis[0].dataMax > 100 && this.yAxis[0].max <= 100) {
              this.yAxis[0].update({max : null});
            }
          }
        }
      },
      title: {
        text: ''
      },
      credits: {
        enabled: false
      },
      legend: {
        // enabled : false
      },
      tooltip: {
        valueSuffix: " °%",
        borderColor: function () {
          return null;
        }, 
          formatter: function () {
              if (this.x == that.threshold1Time) {
                const yValue = that.percentDataOrignal[that.threshold1TimeIndex];
                  return '<span style="font-size: 1.3em;color: ' + that.colorArray[that.threshold1State] + '">T1 : ' + that.threshold1LabelTime + '  /  ' + that.threshold1Percentage + '%</span><br><span style="font-size: 1.3em;color: ' + that.colorArray[that.threshold1State] + '">' + yValue + '% Outflows Settled </span>';

              } else if (this.x == that.threshold2Time) {
                const yValue = that.percentDataOrignal[that.threshold2TimeIndex];
                  return '<span style="font-size: 1.3em;color: ' + that.colorArray[that.threshold2State] + '">T2 : ' + that.threshold2LabelTime + '  /  ' + that.threshold2Percentage + '%</span><br><span style="font-size: 1.3em;color: ' + that.colorArray[that.threshold2State] + '">' + yValue+ '% Outflows Settled </span>';

              } else {
              return this.series.name +': <b>' + this.y + '% at '+this.x+'<br/>';
              }
      },
      },
      xAxis: {
        categories: this.timeDataAsArray,
        max: this.timeDataAsArray.indexOf(this.endTime) > -1 ? this.timeDataAsArray.indexOf(this.endTime) : null,
        min: this.timeDataAsArray.indexOf(this.startTime) > -1 ? this.timeDataAsArray.indexOf(this.startTime) : null,
      }, yAxis: {
        title: {
          text: "%"
        },
      },
      series: [
        {
          name: 'Throughput Ratio',
          data: this.percentDataAsArray,
          color: 'blue',
          marker: {
            enabled: false
          },
        }, {
          name: 'Thresholds',
          visible: true,
          type: 'scatter',
          // data: [[this.threshold1Time, 35], [this.threshold2Time, 83]]
          data: [{
            x: this.threshold1TimeIndex,
            y: this.threshold1Percentage,
            color: this.colorArray[this.threshold1State]
          }, {
            x: this.threshold2TimeIndex,
            y: this.threshold2Percentage,
            color: this.colorArray[this.threshold2State]
          }],
          tooltip: {
            pointFormatter: function() {
              return this.series.name +' '+ (this.index+1)+': <b>' + this.y + '% at '+that.timeDataAsArray[this.x]+'<br/>';
            }
          }
        }
      ]
    }
    this.chart = Highcharts.chart(this.containerHighChart.nativeElement, this.options);


  }

  setChartsData(data: ChartData) {
    try {


      this.threshold1Time = data.threshold1Time;
      this.threshold2Time = data.threshold2Time;

      this.threshold1LabelTime = data.threshold1Time;
      this.threshold2LabelTime = data.threshold2Time;

      this.threshold1Percentage = data.threshold1Percentage;
      this.threshold2Percentage = data.threshold2Percentage;
      this.timeData = data.timeData;
      this.percentData = data.percentData;

      this.timeDataAsArray = this.timeData.split(",");

      this.percentDataAsArray =  this.percentData.split(",").map(Number);
      this.percentDataOrignal = this.percentData.split(",").map(Number);
      this.threshold1TimeIndex = this.timeDataAsArray.indexOf(this.threshold1Time);
      this.threshold2TimeIndex = this.timeDataAsArray.indexOf(this.threshold2Time);
      this.checkIfThresholdsExist();

      this.threshold1State = data.threshold1State;
      this.threshold2State = data.threshold2State;
      

      this.startTime = data.startTime;
      this.endTime = data.endTime;

      if("00:00" == this.startTime){
        this.startTime = "00:15";
      }

      // const time1 = this.timeDataAsArray.indexOf(this.endTime) > -1 ? this.timeDataAsArray.indexOf(this.endTime) : null;
      // const time2 = this.timeDataAsArray.indexOf(this.startTime) > -1 ? this.timeDataAsArray.indexOf(this.startTime) : null;
        

    } catch (error) {

    }
  }

  updateChart(data: ChartData) {

    this.threshold1Time = data.threshold1Time;
    this.threshold2Time = data.threshold2Time;

    this.threshold1LabelTime = data.threshold1Time;
    this.threshold2LabelTime = data.threshold2Time;

    this.threshold1Percentage = data.threshold1Percentage;
    this.threshold2Percentage = data.threshold2Percentage;
    this.timeData = data.timeData;
    this.percentData = data.percentData;

    this.timeDataAsArray = this.timeData.split(",");

    this.percentDataAsArray =  this.percentData.split(",").map(Number);
    this.percentDataOrignal =  this.percentData.split(",").map(Number);

    this.threshold1TimeIndex = this.timeDataAsArray.indexOf(this.threshold1Time);
    this.threshold2TimeIndex = this.timeDataAsArray.indexOf(this.threshold2Time);
    this.checkIfThresholdsExist();
    this.threshold1State = data.threshold1State;
    this.threshold2State = data.threshold2State;

    this.startTime = data.startTime;
    this.endTime = data.endTime;

    if("00:00" == this.startTime){
      this.startTime = "00:15";
    }
    

    this.chart.series[0].setData(this.percentDataAsArray);
    this.chart.series[1].setData([{
      x: this.threshold1TimeIndex,
      y: this.threshold1Percentage,
      color: this.colorArray[this.threshold1State]
    }, {
      x: this.threshold2TimeIndex,
      y: this.threshold2Percentage,
      color: this.colorArray[this.threshold2State]
    }]);
    
    this.chart.xAxis[0].update({
      max: this.timeDataAsArray.indexOf(this.endTime) > -1 ? this.timeDataAsArray.indexOf(this.endTime) : null,
      min: this.timeDataAsArray.indexOf(this.startTime) > -1 ? this.timeDataAsArray.indexOf(this.startTime) : null,
    });


    if (this.chart.series[0].points[this.threshold1TimeIndex]) {

      this.chart.series[0].points[this.threshold1TimeIndex].update({
        marker: {
          enabled: true,
          fillColor: this.colorArray[this.threshold1State],
        }
    });
    }
    if (this.chart.series[0].points[this.threshold2TimeIndex]) {
      this.chart.series[0].points[this.threshold2TimeIndex].update({
        marker: {
          enabled: true,
          fillColor: this.colorArray[this.threshold2State],
        }
      });
    }
    

  }
  
  checkIfThresholdsExist(){
    if(this.threshold1TimeIndex == -1 && this.threshold1Time && this.timeDataAsArray.length > 0){
      const firstTime = this.timeDataAsArray[0]
      const lastTime = this.timeDataAsArray[this.timeDataAsArray.length-1]
      if(!moment(this.threshold1Time, "hh:mm").isAfter(moment(lastTime, "hh:mm")) && !moment(this.threshold1Time, "hh:mm").isBefore(moment(firstTime, "hh:mm"))){
          for (let index = 0; index < this.timeDataAsArray.length; index++) {
            const element = this.timeDataAsArray[index];
            if(moment(element, "hh:mm").isAfter(moment(this.threshold1Time, "hh:mm"))){
              this.threshold1Time = element;
              this.threshold1TimeIndex = index;
              break;
            }
            
          }
      }
     
    }
    if(this.threshold2TimeIndex == -1 && this.threshold2Time && this.timeDataAsArray.length > 0){
      const firstTime = this.timeDataAsArray[0]
      const lastTime = this.timeDataAsArray[this.timeDataAsArray.length-1]
      if(!moment(this.threshold2Time, "hh:mm").isAfter(moment(lastTime, "hh:mm")) && !moment(this.threshold2Time, "hh:mm").isBefore(moment(firstTime, "hh:mm"))){
          for (let index = 0; index < this.timeDataAsArray.length; index++) {
            const element = this.timeDataAsArray[index];
            if(moment(this.threshold2Time, "hh:mm").isBefore(moment(element, "hh:mm"))){
              this.threshold2Time = element;
              this.threshold2TimeIndex = index;
              break;
            }
            
          }
      }
     
    }
  }
  
  ngAfterViewInit() {
    const ro = new ResizeObserver((entries, observer) => {
      if (this.isVisible(this.element.nativeElement)) {
        this.chart.reflow();
        this.chart.update({
          plotOptions: {
            series: {
              states: {
                hover: {
                  enabled: false
                },
                inactive: {
                  enabled: false
                }
              }
            }
          },
        })
      }
    });
    ro.observe(this.element.nativeElement);


  }

  isVisible(e) {
    return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
  }


  public ngOnDestroy(): void {
    this.subscriptions = unsubscribeAllObservables(this.subscriptions);
  }

}

