<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingTop="5">
    <SwtCanvas width='100%'>
    <Grid width="100%" paddingLeft="5">


      <GridRow width="100%" height="25">
        <!-- <GridItem width="65%"> -->
          <GridItem width="120">
            <SwtLabel id="status" text="Status" #status></SwtLabel>
          </GridItem>
          <GridItem >
              <HBox   verticalGap="0">
  
                <HBox width="100%" height="30" >
                  <SwtCheckBox  #pendingBox  id="pendingBox" selected="true" value ="Y"
                  ></SwtCheckBox>
                  <SwtLabel text="Pending" width="130" fontWeight="bold" #pendingLabel></SwtLabel>
                </HBox>
                <HBox width="100%" height="30" >
                  <SwtCheckBox   #acceptedBox  id="acceptedBox" selected="false"   value ="N"
                  >
                </SwtCheckBox>
                <SwtLabel  text="Accepted" width="130" #acceptedLabel  fontWeight="bold"></SwtLabel>
                </HBox>
                <HBox width="100%" height="30" >
                  <SwtCheckBox #rejectedBox  id="rejectedBox" selected="false" value ="N"
                   >
                </SwtCheckBox>
                <SwtLabel  text="Rejected" width="130" #rejectedLabel  fontWeight="bold"></SwtLabel>
                </HBox>
              </HBox>
            </GridItem>
        <!-- </GridItem> -->
      </GridRow>


      <SwtRadioButtonGroup #showGrp id="showGrp" align="vertical">
      </SwtRadioButtonGroup>
      <GridRow width="100%" height="25">
        <GridItem width="120">
          <SwtLabel id="show" text="Date Selection" #show></SwtLabel>
        </GridItem>
        <GridItem horizontalAlign="right">
              <SwtRadioItem value="A" groupName="showGrp" (change)="showGrp.selectedValue='A'" id="allRadio" #allRadio></SwtRadioItem>
              <SwtLabel  paddingLeft="7" text="All" width="164" #allDatesLabel  fontWeight="bold"></SwtLabel>
              <!-- <spacer width="150"></spacer> -->
          </GridItem>
      </GridRow>
      <GridRow width="100%" height="25">
        <spacer width="120"></spacer>
        <GridItem>
          <SwtRadioItem value="D" groupName="showGrp" (change)="showGrp.selectedValue='D';" id="forDateRadio" #forDateRadio></SwtRadioItem>
          <!-- <spacer width="30"></spacer> -->
          <SwtLabel  paddingLeft="7" text="Date Range" width="148" #dateRangeLabel  fontWeight="bold"></SwtLabel>
           
        </GridItem>
        <GridItem>
          <GridItem width="50">
            <SwtLabel id="fromDateLabel" text="From" #fromDateLabel></SwtLabel>
          </GridItem>
            <GridItem width="116">
              <SwtDateField id="fromDateField" #fromDateField enabled="{{forDateRadio.selected}}" (change)="validateDateField(fromDateField)"
              width="70"></SwtDateField>
            </GridItem>
            <GridItem width="30">
              <SwtLabel id="toDateLabel" text="To" #toDateLabel></SwtLabel>
            </GridItem>
            <GridItem width="150">
              <SwtDateField id="toDateField" #toDateField enabled="{{forDateRadio.selected}}" (change)="validateDateField(toDateField)"
              width="70"></SwtDateField>
            </GridItem>
            
        </GridItem>
      </GridRow>


      <GridRow width="100%" height="25">
        <GridItem width="65%">
          <GridItem width="300">
            <GridItem width="120">
              <SwtLabel id="faciltiyLabel" #faciltiyLabel text="Facility"></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="facilityCombo" #facilityCombo width="300" dataLabel="facilityList" >
              </SwtComboBox>
            </GridItem>
          </GridItem>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="40">
        <GridItem width="65%">
          <GridItem width="300">
            <GridItem width="120">
              <SwtLabel id="user" #user text="User selection"></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="userCombo" #userCombo width="200" dataLabel="userList" >
              </SwtComboBox>
            </GridItem>
          </GridItem>
          <GridItem paddingLeft="300">
            <SwtLabel id="userDesc" #userDesc fontWeight="normal"></SwtLabel>
            <SwtButton [buttonMode]="true" (click)="updateData()" id="searchButton" label="Search" #searchButton >
            </SwtButton> 
          </GridItem>
        </GridItem>
      </GridRow>
    </Grid>
  </SwtCanvas>
    <GridRow width="100%" height="80%" paddingBottom="10">
      <SwtCanvas #maintEventGridContainer id="maintEventGridContainer" styleName="canvasWithGreyBorder" width="100%"
        height="100%" border="false"></SwtCanvas>
    </GridRow>
   
    <SwtCanvas width="100%" height="35">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <!-- <SwtButton [buttonMode]="true" id="displayButton" label="Display" #displayButton (click)="displayClickHandler()">
          </SwtButton> -->
           <SwtButton [buttonMode]="true" id="displayButton" label="Display" #displayButton (click)="displayClickHandler()" enabled="false">
          </SwtButton> 
        </HBox>
        <HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtButton [buttonMode]="true" id="closeButton" label="Close" #closeButton (click)="closeHandler()">
          </SwtButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
    
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>