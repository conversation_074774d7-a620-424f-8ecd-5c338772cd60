import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {CommonService, SwtModule, SwtTextArea} from "swt-tool-box";
declare var require: any;
var prettyData = require('pretty-data');
@Component({
  selector: 'app-show-xml',
  templateUrl: './ShowXML.html',
  styleUrls: ['./ShowXML.css']
})
export class ShowXML extends SwtModule implements OnInit {

  @ViewChild('messageForm') messageForm: SwtTextArea;
  public xmlData: string;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);

  }

  onLoad() {
    var messageAsXML = prettyData.pd.xml(this.xmlData);
    messageAsXML = this.htmlEntities(messageAsXML);
    this.messageForm.text = messageAsXML.replace(/\r/g, '');

  }

  htmlEntities(str) {
    try {
      return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
      replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
    } catch (e) {
      console.log('error', e, str)
    }
  }


}


