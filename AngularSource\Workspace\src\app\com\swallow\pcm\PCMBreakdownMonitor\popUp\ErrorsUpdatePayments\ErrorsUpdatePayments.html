<SwtModule (close)='popupClosed()' (creationComplete)='onLoad()' height='100%' width='1000'>
  <VBox  width='90%' height='100%' paddingLeft="5" paddingTop="5" paddingBottom="5" paddingRight="5">

    <SwtCanvas id="gridCanvas" #gridCanvas
               width="100%" height="90%">  </SwtCanvas>
    <SwtCanvas width="100%">
      <HBox>
        <SwtButton #releaseAllButton
                   (click)="checkBeforeReleaseUnstop($event)"
                   id="releaseAllButton"
                   visible="true"
                   width="80" [buttonMode]="false" enabled="true"></SwtButton>
        <SwtButton buttonMode="true"
                   id="releasePayButton"
                   #releasePayButton
                   width="70"
                   visible="true"
                   enabled="false"
                   (click)="checkBeforeReleaseUnstop($event)"> </SwtButton>
          <SwtButton buttonMode="true"
                   id="UnStopButton"
                   #UnStopButton
                   width="70"
                   visible="false"
                   includeInLayout="false"
                   enabled="false"
                   (click)="checkBeforeReleaseUnstop($event)"> </SwtButton>
        <SwtButton buttonMode="true"
                   id="cancelButton"
                   #cancelButton
                   width="70"
                   enabled="true"
                   (click)="popupClosed()"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
