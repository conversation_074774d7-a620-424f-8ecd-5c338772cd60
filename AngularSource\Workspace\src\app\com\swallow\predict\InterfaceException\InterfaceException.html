<SwtModule (creationComplete)='onLoad()' width="100%" height="700">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas width="100%" height="5%">
      <HBox width="100%">
        <SwtLabel id="messageTypelbl" #messageTypelbl  fontWeight="bold" text="Interface:"></SwtLabel>
        <SwtLabel  id="messageTypeValue" #messageTypeValue  fontWeight="normal" text=""></SwtLabel>
        <SwtLabel  id="messageStatuslbl" #messageStatuslbl  fontWeight="bold" text="Message Status:"></SwtLabel>
        <SwtLabel id="messageStatusValue" #messageStatusValue  fontWeight="normal"  ></SwtLabel>
      </HBox>
      <HBox #pageBox horizontalAlign="right" visible="false">
        <SwtCommonGridPagination #pagination></SwtCommonGridPagination>
      </HBox>
    </SwtCanvas>
    <VBox width="100%" height="90%">
      <VDividedBox height="100%" width="100%" (resize)="resizing($event)" id="vSplit" >
        <SwtCanvas  #gridContainer id="gridContainer" width="100%" height="50%"  class="top" ></SwtCanvas>
        <SwtCanvas  id="messageDetails" #messageDetails width="100%" height="50%" class="bottom">
          <SwtTextArea  #messageForm id="messageForm"  editable="false"  width="100%"  height="90%" >
          </SwtTextArea>
        </SwtCanvas>
      </VDividedBox>
    </VBox>
    <SwtCanvas width="100%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%" marginTop="6">
          <!--<SwtButton [buttonMode]="true"-->
                     <!--label = "Rep"-->
                     <!--id="reprocessButton"-->
                     <!--#reprocessButton-->
                     <!--(click)="reprocessMessages()">-->
          <!--</SwtButton>-->
          <SwtButton [buttonMode]="true"
                     label = "Close"
                      id="closeButton" 
                      #closeButton 
                      (click)="close()">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">
          <HBox horizontalAlign="right" paddingRight="10" marginTop="2">
            <SwtHelpButton
              id="helpIcon">
            </SwtHelpButton>
            <SwtLoadingImage #loadingImage id="loadingImage"> </SwtLoadingImage>
          </HBox>
        </HBox>
	  </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
