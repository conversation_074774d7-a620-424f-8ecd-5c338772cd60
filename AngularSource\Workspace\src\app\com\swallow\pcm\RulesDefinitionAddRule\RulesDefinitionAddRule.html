
<SwtModule title="{{title}}" (creationComplete)="loadSearchDefaults();init()" width="100%" height="100%">

  <VBox styleName="vboxOuter"
        width="100%" height="100%">
    <SwtCanvas width="100%" height="90%">
      <VBox width="100%" height="100%">
      <SwtPanel  height="55%" styleName="panelInsideFormLayout"
                title="Search Criteria"
                width="100%">
        <VBox width="100%" height="100%">
        <VBox width="100%" height="95%">
          <HBox id="filterBox"
                horizontalAlign="left" width="100%" height="15%" paddingTop="5">
            <SwtLabel id="labelFilter"
                      width="50"
                      text="Filter"></SwtLabel>
            <SwtTextInput #filterText
                          id="filterText"
                          styleName="textbox"
                          enabled="false"
                          width="400"
                          (change)="filtringGrid(filterText.text)"></SwtTextInput>
          </HBox>
          <HBox width="100%" height="95%" horizontalGap="0" >
            <SwtCanvas #customGrid
                       id="customGrid"
                       width="50%"
                       height="95%">
            </SwtCanvas>
            <spacer width="5%"> </spacer>
           <VBox width="10%" height="90%" verticalAlign="middle" horizontalAlign="center">
            <SwtButton  id="changeCritButton"
                       #changeCritButton
                       [buttonMode]="false"
                       enabled="false"
                       width="33"
                       marginTop="50"
                       styleName="editIcon"
                       visible="false"
                       (click)="changeCriteria()"
                       (keyDown)="keyDownEventHandler($event)"></SwtButton>
          </VBox>
            <spacer width="5%"> </spacer>
            <SwtCanvas id="CanvOperator" width="40%" height="90%" paddingRight="5">
              <VBox width="100%" height="100%">
                <HBox horizontalAlign="left" width="500" height="35">
                  <SwtButton #equalButton
                             id="equalButton"
                             enabled="false"
                             width="60"
                             label="="
                             (click)="operation=equalButton.label;operationToDisplay=equalButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <SwtButton #differentButton
                             id="differentButton"
                             enabled="false"
                             width="60"
                             label="<>"
                             (click)="operation=differentButton.label;operationToDisplay=differentButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <SwtButton #inButton
                             id="inButton"
                             enabled="false"
                             width="60"
                             label="In"
                             (click)="operation='in';operationToDisplay=inButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <spacer id="space1" width="30"></spacer>
                  <SwtButton #leftParentheseButton
                             id="leftParentheseButton"
                             label="("
                             width="60"
                             (click)="doOpenCloseParentheses()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                </HBox>
                <HBox horizontalAlign="left"  width="500" height="35">
                  <SwtButton #higherButton
                             id="higherButton"
                             enabled="false"
                             width="60"
                             label=">"
                             (click)="operation=higherButton.label;operationToDisplay=higherButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <SwtButton #higherequalButton
                             id="higherequalButton"
                             enabled="false"
                             label=">="
                             width="60"
                             (click)="operation=higherequalButton.label;operationToDisplay=higherequalButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <SwtButton #notinButton
                             id="notinButton"
                             enabled="false"
                             width="60"
                             label="Not In"
                             (click)="operation='not in';operationToDisplay=notinButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <spacer width="30"></spacer>
                  <SwtButton #rightParentheseButton
                             id="rightParentheseButton"
                             label=")"
                             width="60"
                             enabled="false"
                             (click)="doOpenCloseParentheses()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                </HBox>
                <HBox  horizontalAlign="left" width="500" height="35">
                  <SwtButton #lowerButton
                             id="lowerButton"
                             label="<"
                             enabled="false"
                             width="60"
                             (click)="operation=lowerButton.label;operationToDisplay=lowerButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <SwtButton #lowerequalButton
                             id="lowerequalButton"
                             label="<="
                             enabled="false"
                             width="60"
                             (click)="operation=lowerequalButton.label;operationToDisplay=lowerequalButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <SwtButton #betweenButton
                             id="betweenButton"
                             label="Between"
                             enabled="false"
                             width="60"
                             (click)="operation='between';operationToDisplay=betweenButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <spacer width="30"></spacer>
                  <SwtButton #andButton
                             id="andButton"
                             label="And"
                             enabled="false"
                             width="60"
                             (click)="andOrString='and';andOrStringToDisplay=andButton.label;operationToDisplay=andButton.label;addOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                </HBox>
                <HBox horizontalAlign="left" width="500" height="35">
                  <SwtButton #likeButton
                             id="likeButton"
                             label="Like"
                             width="60"
                             enabled="false"
                             (click)="operation='like';operationToDisplay=likeButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <SwtButton #notLikeButton
                             id="notLikeButton"
                             label="Not Like"
                             width="60"
                             enabled="false"
                             (click)="operation='Not Like';operationToDisplay=notLikeButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <SwtButton #inListButton
                             id="inListButton"
                             label="In List"
                             enabled="false"
                             width="60"
                             (click)="operation='in list';operationToDisplay=inListButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                  <spacer width="30"></spacer>
                  <SwtButton #orButton
                             id="orButton"
                             label="Or"
                             enabled="false"
                             width="60"
                             (click)="andOrString='or';andOrStringToDisplay=orButton.label;operationToDisplay=orButton.label;addOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                </HBox>
                <HBox horizontalAlign="left" width="500" height="35">
                  <SwtButton id="containsButton"
                             #containsButton
                             label="Contains"
                             enabled="false"
                             width="70"
                             (click)="operation='exists';operationToDisplay=containsButton.label;doOperation()"
                             (keyDown)="keyDownEventHandler($event)"></SwtButton>
                </HBox>
              </VBox>
            </SwtCanvas>
          </HBox>
        </VBox>
        <VBox width="100%" height="10%">
          <SwtLabel id="labelQuery" #labelQuery
                    width="100%" height="100%"></SwtLabel>
        </VBox>
        </VBox>
      </SwtPanel>
      <SwtPanel  height="20%" styleName="panelInsideFormLayout"
                title="Values"
                width="100%">
        <HBox paddingTop="8"
              height="100%"
              width="100%">
          <VBox width="35%" height="100%" >
            <HBox width="100%">
          <SwtLabel #propertiesLabel
                    id="propertiesLabel"
                    styleName="label"
                    paddingTop="20"></SwtLabel>
          <SwtLabel #operator
                    id="operator"
                    height="20"
                    styleName="label"
                    paddingLeft="15"
                    paddingRight="15"
                    text="{{operationToDisplay}}"></SwtLabel>
            </HBox>
          </VBox>
          <VBox #vBoxContainer id="vBoxContainer" width="60%">
            <HBox #hBoxContainer id="hBoxContainer" height="50%"></HBox>
            <HBox #hBoxContainer2 id="hBoxContainer2" height="50%">
            </HBox>
          </VBox>

          <VBox horizontalAlign="right" width="10%" height="100%" paddingBottom="5">
            <SwtButton #addButton
                       id="addButton"
                       enabled="false"
                       width="60"
                       label="Add"
                       (click)="chekcFields()"marginBottom="2"></SwtButton>
            <SwtButton #undoButton
                       id="undoButton"
                       enabled="false"
                       label="Undo"
                       width="60"
                       (click)="previous()"
                       (keyDown)="keyDownEventHandler($event)"  marginBottom="2"></SwtButton>
            <SwtButton #changeButton
                       id="changeButton"
                       width="60"
                       label="Change"
                       visible="false"
                       enabled="false"
                       (click)="chekcFields()"
                       (keyDown)="keyDownEventHandler($event)"></SwtButton>
          </VBox>

        </HBox>
      </SwtPanel>
      <SwtPanel height="23%" width="100%" styleName="panelInsideFormLayout"
                title="Rule to add">
        <VBox  paddingBottom="5"
               paddingTop="10"
               paddingRight="10"
               height="100%"
               width="100%">
          <SwtTextArea #queryText
                       id="queryText"
                       editable="false"
                       text="{{queryToDisplay}}"
                       height="100%"
                       toolTip="Expression Rule"></SwtTextArea>
        </VBox>
      </SwtPanel>
      </VBox>
    </SwtCanvas>
    <SwtCanvas id="buttonCanvas">
        <HBox width="100%">
      <HBox    #buttonHbox width="100%"
               paddingLeft="5"
               id="buttonHbox">
        <SwtButton  #okButton
                    id="okButton"
                    width="70"
                    toolTip="Ok"
                    label="Ok"
                    enabled="false"
                    (click)="executeQuery($event)"
                    (keyDown)="keyDownEventHandler($event)"></SwtButton>
        <SwtButton #resetButton
                   id="resetButton"
                   label="Reset"
                   enabled="false"
                   width="70"
                   (click)="reset($event); filterText.text = ''"
                   toolTip=""
                   (keyDown)="keyDownEventHandler($event)"></SwtButton>
        <SwtButton  #cancelButton
                    id="cancelButton"
                    toolTip="Cancel"
                    label="Cancel"
                    width="70"
                    (click)="closePopUp()"
                    (keyDown)="keyDownEventHandler($event)"></SwtButton>
      </HBox>
      <HBox horizontalAlign="right" paddingRight="10">
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
        <SwtHelpButton #helpIcon
                       id="helpIcon"
                       enabled="true"
                       (click)="doHelp($event)"
                       toolTip="Help"></SwtHelpButton>
      </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>

