<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingTop="5">
    <Grid width="100%" height="94%" paddingLeft="5">
      <GridRow width="100%" height="28">
        <GridItem width="300">
          <GridItem width="120">
            <SwtLabel id="templateIdLabel" #templateIdLabel></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtTextInput #templateIdTxtInput id="templateIdTxtInput" editable="true" width="200" textAlign="left">
            </SwtTextInput>
          </GridItem>
        </GridItem>
      </GridRow>
      <GridRow width="100%" height="28">
        <GridItem width="300">
          <GridItem width="120">
            <SwtLabel id="descriptionLabel" #descriptionLabel></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtTextInput #descriptionTxtInput id="descriptionTxtInput" editable="true" width="200" textAlign="left">
            </SwtTextInput>
          </GridItem>
        </GridItem>
      </GridRow>
      <GridRow width="100%" height="28">
        <GridItem width="100%" height="100%">
          <GridItem width="120">
            <SwtLabel id="subjectContentLabel" #subjectContentLabel></SwtLabel>
          </GridItem>
          <GridItem width="100%" height="100%">
            <textarea #subjectContent id="subjectContent"></textarea>
          </GridItem>
        </GridItem>
      </GridRow>
      <GridRow  width="100%" paddingTop="5" height="100%">
        <GridItem width="100%" height="100%">
          <GridItem width="120">
            <SwtLabel id="bodyContentLabel" #bodyContentLabel></SwtLabel>
          </GridItem>
          <GridItem width="100%" height="100%">
            <textarea #emailContent id="emailContent"></textarea>
          </GridItem>
        </GridItem>
      </GridRow>
      <GridRow width="100%" paddingTop="2" height="28">
        <GridItem width="300">
          <GridItem width="120">
            <SwtLabel id="keywordsComboLabel" #keywordsComboLabel></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtComboBox id="keywordsCombo" #keywordsCombo width="300" dataLabel="keywords" shiftUp="100" enabled="true"
              prompt="Select Specific Keyword to add" (change)="changeKeywords()"> </SwtComboBox>
          </GridItem>
        </GridItem>
      </GridRow>
    </Grid>


    <SwtCanvas width="100%" height="35">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="saveButton" #saveButton (click)="saveHandler()">
          </SwtButton>

          <SwtButton [buttonMode]="true" id="cancelButton" #cancelButton (click)=" popupClosed()">
          </SwtButton>
        </HBox>
        <HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtButton [buttonMode]="true" label="Preview" id="previewEmailTemplateButton" #previewEmailTemplateButton (click)="showPreviewDialog()">
          </SwtButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>

  <div id="previewDialog" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <SwtFieldSet legendText="Enter Field Values" id="fieldSet1" #fieldSet1 style="height: 100%; width: 100%; color:blue;">
            <div id="fieldInputs" style="padding-top: 20px;" ></div>
          </SwtFieldSet>
            <SwtCanvas width="100%" style="padding-top: 10px;" height="35">
            <SwtButton [buttonMode]="true" label="Generate" id="previewEmail" #previewEmail (click)="generatePreview()">  </SwtButton>
            <SwtButton [buttonMode]="true" style="padding-left:10px" label="Close" id="closePopup2" #closePopup2 (click)="closePopup('previewDialog')">
            </SwtButton>
          </SwtCanvas>
        </div>
    </div>

    <div id="previewModal" class="modal">
        <div class="modal-content2">
            <span class="close">&times;</span>
            <SwtFieldSet legendText="Preview" id="fieldSet2" #fieldSet1 style="max-height: 80vh;height: 100%; width: 100%; color:blue;">
            <div style="margin-top: 15px; margin-bottom: 5px; max-width: 1200px;
            max-height: 75vh; 
            overflow-y: auto; "><div id="aaazz" ><strong>Subject:</strong> <div id="previewSubject" style="color:black;" ></div></div>
            <strong>Body:</strong><div id="previewContent" style="color:black;" ></div>
          </div>
          </SwtFieldSet>
            <SwtCanvas width="100%" style="margin-top: 10px;" height="35">
            <SwtButton [buttonMode]="true" label="Close" id="closePopup2" #closePopup2 (click)="closePopup('previewModal')">
            </SwtButton>
          </SwtCanvas>
        </div>
    </div>


</SwtModule>