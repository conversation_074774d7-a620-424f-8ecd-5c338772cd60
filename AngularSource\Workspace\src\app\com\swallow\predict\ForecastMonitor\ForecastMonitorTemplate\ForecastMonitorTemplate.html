
<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox  height="100%" width="100%" paddingLeft="10" paddingRight="10" paddingBottom="10" paddingTop="10">
    <SwtCanvas  id="forecastMonitorTemplateCanvas"
                #forecastMonitorTemplateCanvas
                width="100%"
                height="88%"></SwtCanvas>
    <SwtCanvas  width="100%" height="9%" >
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true"
                     id="addButton"
                     #addButton
                     width="70"
                     (click)="addTemplate()"></SwtButton>
          <SwtButton #changeButton
                     id="changeButton"
                     width="70"
                     enabled="false"
                     (click)="changeTemplate()"></SwtButton>
          <SwtButton #deleteButton
                     id="deleteButton"
                     width="70"
                     enabled="false"
                     (click)="deleteTemplate()"></SwtButton>
          <SwtButton #closeButton
                     id="closeButton"
                     width="70"
                     (click)="closeHandler()"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" top="3">
            <DataExport  #dataExport id="dataExport"></DataExport>
          <SwtHelpButton id="helpIcon"
                           #helpIcon (click)="doHelp()"></SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>

