
<SwtModule (creationComplete)="onLoad()" width="100%" height="100%" >
  <VBox  height="100%" width="100%" paddingLeft="10" paddingRight="10" paddingBottom="10" paddingTop="10">
     <SwtCanvas  id="accountGroupsCanvas"
                  #accountGroupsCanvas
                  width="100%"
                  height="93%"></SwtCanvas>
     <SwtCanvas  width="100%" height="6%">
       <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true"
                     id="addButton"
                     #addButton
                     (click)="doAddAccountGroups()"></SwtButton>
          <SwtButton #changeButton
                     id="changeButton"
                     [buttonMode]="true"
                     (click)="doChangeAccountGroups()"></SwtButton>
          <SwtButton #deleteButton
                     id="deleteButton"
                     [buttonMode]="true"
                     (click)="doDeleteAccountGroups()"></SwtButton>
          <SwtButton #viewButton
                     id="viewButton"
                     [buttonMode]="true"
                     (click)="doDispalyAccountGroups()"></SwtButton>
          <SwtButton #closeButton
                     id="closeButton"
                     [buttonMode]="true"
                     (click)="closeAccountGroups()"></SwtButton>
        </HBox>
       <HBox horizontalAlign="right"  paddingRight="5">
         <SwtHelpButton (click)="doHelp()"></SwtHelpButton>
         <SwtLoadingImage #loadingImage></SwtLoadingImage>
       </HBox>
       </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>
