import { CommonModule } from '@angular/common';
import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild, Input, Output, ViewEncapsulation } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtUtil, CommonService, SwtAlert, Alert, JSONReader, HTTPComms, SwtDateField, SwtButton, SwtLabel, SwtCanvas, SwtCommonGrid, ExternalInterface, SwtLoadingImage, Logger, StringUtils, VDividedBox } from "swt-tool-box";
import moment from 'moment';
declare var instanceElement: any;
import { DiffEditorModel, MonacoEditorModule } from 'ngx-monaco-editor';
@Component({
  selector: 'app-maint-event-log-',
  templateUrl: './MaintenanceEventDetails.html',
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./MaintenanceEventDetails.css']
})
export class MaintenanceEventDetails implements OnInit {

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  
  /***********SwtCanvas***********/
  @ViewChild('logGridContainer') logGridContainer: SwtCanvas;

  /***********SwtDateField***********/
  @ViewChild('fromDateField') fromDateField: SwtDateField;
  @ViewChild('toDateField') toDateField: SwtDateField;

  /***********SwtButton***********/
  @ViewChild('viewInFacilityButton') viewInFacilityButton: SwtButton;
  // @ViewChild('amendInFacilityButton') amendInFacilityButton: SwtButton;
  @ViewChild('acceptButton') acceptButton: SwtButton;
  @ViewChild('rejectButton') rejectButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  /***********SwtLabel***********/
  @ViewChild('fromDateLabel') fromDateLabel: SwtLabel;
  @ViewChild('viewLogGridContainer') viewLogGridContainer: SwtCanvas;

  @ViewChild('facilityValue') facilityValue: SwtLabel;
  @ViewChild('statusValue') statusValue: SwtLabel;
  @ViewChild('recordValue') recordValue: SwtLabel;
  @ViewChild('requestUserIdValue') requestUserIdValue: SwtLabel;
  @ViewChild('requestDateValue') requestDateValue: SwtLabel;
  @ViewChild('typeValue') typeValue: SwtLabel;
  @ViewChild('authtUserIdValue') authtUserIdValue: SwtLabel;
  @ViewChild('authDateValue') authDateValue: SwtLabel;

  @ViewChild('maintEventIdValue') maintEventIdValue: SwtLabel;

  @ViewChild('vDivider') vDivider: VDividedBox;
  @ViewChild('editorContainer') editorContainer!: ElementRef;


  @ViewChild('oldValLbl') oldValLbl: SwtLabel;
  @ViewChild('newValLbl') newValLbl: SwtLabel;
  @ViewChild('fullDetailsLbl') fullDetailsLbl: SwtLabel;


  private swtAlert: SwtAlert;
  private logger: Logger = null;
  private menuAccessId;
  private maintFacilityId;
  private status;
  private requestUser;
  private screenName;
  private parameters;
   /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public inputDataDetails = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  private logGrid: SwtCommonGrid;
  private currencyPattern: string;
  private dateFormat: string;
  // private fromDate: string;
  // private toDate: string;
  private selectedtRow;
  // private action: string;
  private maintEventId: string;
  private actionValue:string;
  private viewOrAmendSubScreen:string;
  // private ccyCode: string;

  private viewLogGrid;
  authOthers: any;
  fullAccessToScreen:any;
  currentUserId: any;

  private amendInFacilityAccess = false;



  private fromValue;
  private toValue;
  text1 = '';
  text2 = '';
  isCompared = false;

  @Output()
  selectedLang = 'plaintext';
  @Output()
  selectedTheme = 'vs';

  @Input()
  languages = [
    'bat',
    'c',
    'coffeescript',
    'cpp',
    'csharp',
    'csp',
    'css',
    'dockerfile',
    'fsharp',
    'go',
    'handlebars',
    'html',
    'ini',
    'java',
    'javascript',
    'json',
    'less',
    'lua',
    'markdown',
    'msdax',
    'mysql',
    'objective-c',
    'pgsql',
    'php',
    'plaintext',
    'postiats',
    'powershell',
    'pug',
    'python',
    'r',
    'razor',
    'redis',
    'redshift',
    'ruby',
    'rust',
    'sb',
    'scss',
    'sol',
    'sql',
    'st',
    'swift',
    'typescript',
    'vb',
    'xml',
    'yaml'
  ];

  @Input()
  themes = [
    {
      value: 'vs',
      name: 'Visual Studio'
    },
    {
      value: 'vs-dark',
      name: 'Visual Studio Dark'
    },
    {
      value: 'hc-black',
      name: 'High Contrast Dark'
    }
  ];

  // input
  inputOptions = {
    theme: 'vs',
    language: 'plaintext',
    minimap: {
      enabled: false
    },
    scrollbar: {
      // Subtle shadows to the left & top. Defaults to true.
      useShadows: false,
      // Render vertical arrows. Defaults to false.
      verticalHasArrows: false,
      // Render horizontal arrows. Defaults to false.
      horizontalHasArrows: false,
      // Render vertical scrollbar.
      // Accepted values: 'auto', 'visible', 'hidden'.
      // Defaults to 'auto'
      vertical: 'hidden',
      // Render horizontal scrollbar.
      // Accepted values: 'auto', 'visible', 'hidden'.
      // Defaults to 'auto'
      horizontal: 'hidden'
    }
  };
  // compare, output
  diffOptions = {
    theme: 'vs',
    language: 'plaintext',
    readOnly: true,
    renderSideBySide: true
  };
  originalModel: DiffEditorModel = {
    code: '',
    language: 'plaintext'
  };

  modifiedModel: DiffEditorModel = {
    code: '',
    language: 'plaintext'
  };

  editorHeight: number = 0;

  // ngAfterViewInit(): void {
  //   // Get the height of the parent container
  //   const parentHeight = this.editorContainer.nativeElement.offsetParent.offsetHeight;
  //   // Set the height of the editor to match the parent height
  //   this.editorHeight = parentHeight;
  //   // Set the height of the editor to match the container height
  // }

  
  constructor(private commonService: CommonService, private element: ElementRef) {
    //super(element, commonService);
    this.logger = new Logger('Account Currency Period maintenance', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    try {

      this.acceptButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.label', null);
      this.acceptButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.tooltip', null);
  
      this.rejectButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.label', null);
      this.rejectButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.tooltip', null);

      // this.amendInFacilityButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.amendwinfacility.label', null);
      // this.amendInFacilityButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.amendwinfacility.tooltip', null);
      
      this.viewInFacilityButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.viewinfacility.label', null);
      this.viewInFacilityButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.viewinfacility.tooltip', null);

      
    instanceElement = this;

     if(window.opener && window.opener.instanceElement) {
      // let params = ['change', 'aaadddd'];//window.opener.instanceElement.getParams();
      let params = window.opener.instanceElement.getParams();//window.opener.instanceElement.getParams();
      if (params !=undefined ) {
        this.maintEventId = params.maintEventId;
        this.menuAccessId = params.menuAccessId;
        this.maintFacilityId = params.maintFacilityId;
        this.status = params.status;
        this.requestUser = params.requestUser;
      }
    }else {
      this.maintEventId = '70';
      this.menuAccessId = '0';
      this.maintFacilityId = 'ACCT_GRP';
      this.status = 'P';
    }


    this.logGrid = <SwtCommonGrid>this.logGridContainer.addChild(SwtCommonGrid);
    this.viewLogGrid=<SwtCommonGrid>this.viewLogGridContainer.addChild(SwtCommonGrid);
    this.fromDateLabel.text= SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.from', null); 
    // this.refreshButton.label = SwtUtil.getPredictMessage('button.refresh', null);
    // this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refresh', null);
    // this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    // this.viewButton.toolTip = SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.view', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.oldValLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.oldVal', null);
    this.newValLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.newVal', null);
    this.fullDetailsLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.fullDetails', null);
    
  } catch (error) {
      console.log("🚀 ~ file: MaintenanceEventDetails.ts:274 ~ MaintenanceEventDetails ~ ngOnInit ~ error:", error)
      
  }
  
  }

  
  onLoad(){
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.requestParams = [];
    // this.action= ''//window.opener.instanceElement.screenName;
    errorLocation = 10;
    // this.maintEventId= window.opener.instanceElement.getParams()//window.opener.instanceElement.maintEventId;
    errorLocation = 20;
    // this.menuAccessId = '0'//ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId != undefined) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    
    errorLocation = 30;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 40;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "maintenanceEvent.do?";
    this.actionMethod = 'method=displayLog';
    errorLocation = 50;
    this.requestParams['menuAccessId'] = this.menuAccessId;
    errorLocation = 60;
    this.requestParams['maintEventId'] =  this.maintEventId;
    errorLocation = 70;
    // this.requestParams['action'] =  this.action;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 100;
    this.inputData.send(this.requestParams);

      this.logGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };
      this.viewLogGrid.onRowClick = (event) => {
        errorLocation = 120;
        this.cellClickEventHandlerDetailsLogs(event);
      };


      this.vDivider.DIVIDER_DRAG_COMPLETE.subscribe((event) => {
        window.dispatchEvent(new Event('resize'));
      });

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "onLoad", errorLocation);
    }
  }
  cellClickEventHandlerDetailsLogs(event) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      if (this.viewLogGrid.selectedIndex >= 0) {
        errorLocation = 10;
        this.fromValue = this.viewLogGrid.selectedItem.changedFrom.content;
        errorLocation = 20;
        this.toValue = this.viewLogGrid.selectedItem.changedTo.content;
        errorLocation = 30;
        this.onCompare();
      } else {
        errorLocation = 40;
        this.fromValue = "";
        this.toValue = "";
        this.onCompare();
      }

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'MaintenanceLogView.ts', "cellClickEventHandler", errorLocation);
    }
  }

  onCompare() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.originalModel = Object.assign({}, this.originalModel, {
        code: this.fromValue
      });
      errorLocation = 10;
      this.modifiedModel = Object.assign({}, this.originalModel, {
        code: this.toValue
      });
      errorLocation = 20;
      this.isCompared = true;
      window.scrollTo(0, 0); // scroll the window to top
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onCompare] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'MaintenanceLogView.ts', "onCompare", errorLocation);
    }
  }

  /**
     * deleteHandler()
     *
     * This method is used to delete the selected record
     */
  acceptButtonHandler(): void {
    // const message = SwtUtil.getPredictMessage('alert.columndelete', null);
    const message = SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoaccept', null);
    this.swtAlert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.acceptStatusHandler.bind(this), null);
}

rejectButtonHandler(): void {
  // const message = SwtUtil.getPredictMessage('alert.columndelete', null);
  const message = SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoreject', null);
  this.swtAlert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.rejectStatusHandler.bind(this), null);
}

acceptStatusHandler(closeEvent): void {
    if (closeEvent.detail == Alert.YES) {
      this.changeStatusHandler('A');
    }
  }
  rejectStatusHandler(closeEvent): void {
    if (closeEvent.detail == Alert.YES) {
      this.changeStatusHandler('R');
    }
  }
  changeStatusHandler(action) {
    let errorLocation = 0;
    try {
      this.actionPath = "maintenanceEvent.do?";
      this.actionMethod = 'method=updateMaintenanceEventStatus';
      errorLocation = 50;
      this.requestParams = [];
      this.requestParams['menuAccessId'] = this.menuAccessId;
      errorLocation = 60;
      this.requestParams['maintEventId'] =  this.maintEventId;
      errorLocation = 70;
      this.requestParams['action'] =  action;

      this.inputData.cbResult = (event) => {
        this.updateMaintenanceEventStatusResult(event);
      };
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 100;
      this.inputData.send(this.requestParams);
    } catch (error) {

      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "onLoad", errorLocation);
    }


  }
  getSubGridDetails(){
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.requestParams = [];
    // this.action= ''//window.opener.instanceElement.screenName;
    errorLocation = 10;
    // this.maintEventId= '31'//window.opener.instanceElement.maintEventId;
    errorLocation = 20;
    // this.menuAccessId = '0'//ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    errorLocation = 30;
    this.inputDataDetails.cbStart = this.startOfComms.bind(this);
    this.inputDataDetails.cbStop = this.endOfComms.bind(this);
    this.inputDataDetails.cbResult = (event) => {
      this.inputDataDetailsResult(event);
    };
    errorLocation = 40;
    this.inputDataDetails.cbFault = this.inputDataFault.bind(this);
    this.inputDataDetails.encodeURL = false;
    this.actionPath = "maintenanceEvent.do?";
    this.actionMethod = 'method=displayViewLog';
    errorLocation = 50;
    this.requestParams['menuAccessId'] = this.menuAccessId;
    errorLocation = 60;
    this.requestParams['maintEventId'] =  this.maintEventId;

    const reference =  this.selectedtRow.reference.content;
    const date =  this.selectedtRow.date.content;
    const time =  this.selectedtRow.time.content;
    const userId =  this.selectedtRow.user.content;
    const tableName =  this.selectedtRow.tableName.content;
    const ipAddress=  this.selectedtRow.ipAddress.content;
    const action=  this.selectedtRow.action.content;

    this.requestParams['reference'] =  reference;
    this.requestParams['date'] =  date;
    this.requestParams['time'] =  time;
    this.requestParams['userId'] =  userId;
    this.requestParams['tableName'] =  tableName;
    this.requestParams['ipAddress'] =  ipAddress;
    this.requestParams['action'] =  action;
    errorLocation = 70;
    // this.requestParams['action'] =  this.action;
    this.inputDataDetails.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 100;
    this.inputDataDetails.send(this.requestParams);

      // this.logGrid.onRowClick = (event) => {
      //   this.cellClickDetailsEventHandler(event);
      // };
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "onLoad", errorLocation);
    }
  }

  /** This method is called when selecting a row from bottom grid**/

  cellClickEventHandler(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // this.logGrid.refresh();
      errorLocation = 10;
      if (this.logGrid.selectedIndex >= 0) {
        errorLocation = 20;
        // this.viewButton.enabled = true;
        // this.viewButton.buttonMode = true;

        this.selectedtRow = this.logGrid.selectedItem;
        this.getSubGridDetails();
      } else {
        errorLocation = 30;
        // this.viewButton.enabled = false;
        // this.viewButton.buttonMode = false;

      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "cellClickEventHandler", errorLocation);
    }
  }


  inputDataDetailsResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      errorLocation = 10;
      //this.dataExport.enabled = true;

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {

        if (!this.jsonReader.isDataBuilding()) {
          const obj = { columns: this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodViewLogGrid.metadata.columns };
          errorLocation = 20;
          this.viewLogGrid.CustomGrid(obj);
          errorLocation = 30;
          var gridRows = this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodViewLogGrid.rows;
          errorLocation = 40;
          if (gridRows.size > 0) {
            this.viewLogGrid.gridData = gridRows;
            this.viewLogGrid.setRowSize = this.jsonReader.getRowSize();
            errorLocation = 50;
            this.viewLogGrid.refresh();
            //this.dataExport.enabled = true;
          }
          else {
            this.viewLogGrid.gridData = { size: 0, row: [] };
            //this.dataExport.enabled = false;
          }
          this.prevRecievedJSON = this.lastRecievedJSON;
        }
      }
    } else {
      if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }

        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "inputDataResult", errorLocation);
    }
  }

  updateMaintenanceEventStatusResult(event):void {
    let errorLocation = 0;
    try {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      errorLocation = 10;
      //this.dataExport.enabled = true;
      if (this.jsonReader.getRequestReplyStatus()) {
        // this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-profileSaved', 'The profile was successfully saved'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));

        this.swtAlert.show(SwtUtil.getPredictMessage("maintenanceevent.details.alert.actionperfermored", null), "Warning", Alert.OK, null, this.closeWindow.bind(this));
        
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }

        }
      }

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "cellClickEventHandler", errorLocation);
    }
      
  }


  closeWindow(event) {
    if(event.detail == Alert.OK) {
      //refresh parent
      if(window.opener && window.opener.instanceElement) {
        window.opener.instanceElement.updateData();
      }

      window.close();
    }
  }


  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      errorLocation = 10;
      //this.dataExport.enabled = true;

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          this.logGrid.selectedIndex=-1;
          //this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
          this.dateFormat = this.jsonReader.getSingletons().dateFormat;

          this.facilityValue.text = this.jsonReader.getSingletons().maintFacilityId;
          this.statusValue.text = this.jsonReader.getSingletons().status;
          this.recordValue.text = this.jsonReader.getSingletons().recordId;
          this.requestUserIdValue.text = this.jsonReader.getSingletons().requestUser;
          this.requestDateValue.text = this.jsonReader.getSingletons().requestDate;
          this.typeValue.text = this.jsonReader.getSingletons().action;
          this.actionValue = this.jsonReader.getSingletons().actionValue;
          this.authtUserIdValue.text = this.jsonReader.getSingletons().authUser;
          this.authDateValue.text = this.jsonReader.getSingletons().authDate;
          this.authOthers = this.jsonReader.getSingletons().authOthers;
          this.fullAccessToScreen = this.jsonReader.getSingletons().fullAccessToScreen;
          
          this.currentUserId = this.jsonReader.getSingletons().currentUserId;
          this.currentUserId = this.jsonReader.getSingletons().currentUserId;
          this.maintEventIdValue.text = this.maintEventId;
          
          
          if("P"=== this.status){
            if(StringUtils.isTrue(this.authOthers) && this.requestUser != this.currentUserId){
              this.acceptButton.enabled = this.menuAccessId == 0;
              this.rejectButton.enabled = this.menuAccessId == 0;
            }
            if(StringUtils.isTrue(this.authOthers) || this.requestUser == this.currentUserId){
            //   if("D" != this.actionValue)
            //     this.amendInFacilityButton.enabled = this.menuAccessId == 0;
            if(StringUtils.isTrue(this.fullAccessToScreen)){
                this.amendInFacilityAccess = true;
                this.rejectButton.enabled = this.menuAccessId == 0;
              }
            }
          }
          
          this.viewInFacilityButton.enabled = this.menuAccessId == 0;
          
          errorLocation = 20;
          // this.fromDate=this.jsonReader.getSingletons().fromDate;
          // this.toDate=this.jsonReader.getSingletons().toDate;
          errorLocation = 30;
          // this.fromDateField.formatString = this.dateFormat.toLowerCase();
          // this.fromDateField.text = this.fromDate;
          errorLocation = 40;
          // this.toDateField.formatString = this.dateFormat.toLowerCase();
          // this.toDateField.text = this.toDate;
          errorLocation = 50;
          if (!this.jsonReader.isDataBuilding()) {
            const obj = { columns: this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodLogGrid.metadata.columns };
            errorLocation = 60;
            this.logGrid.CustomGrid(obj);
            errorLocation = 70;
            var gridRows = this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodLogGrid.rows;
            errorLocation = 80;
            if (gridRows.size > 0) {
              this.logGrid.gridData = gridRows;
              errorLocation = 90;
              this.logGrid.setRowSize = this.jsonReader.getRowSize();
              this.logGrid.refresh();
              setTimeout(() => {
                this.logGrid.selectedIndex = 0;
                this.cellClickEventHandler(null);
              }, 100);
              //this.dataExport.enabled = true;
            }
            else {
              this.logGrid.gridData = { size: 0, row: [] };
              //this.dataExport.enabled = false;
            }
            this.prevRecievedJSON = this.lastRecievedJSON;
          }
        }
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }

        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "cellClickEventHandler", errorLocation);
    }
  }

  validateDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      errorLocation = 10;
      if (dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase(), true);
        errorLocation = 20;
        if (!date.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
            errorLocation = 30;
            this.setFocusDateField(dateField)
          });
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          errorLocation = 40;
          this.setFocusDateField(dateField)
        });
        return false;
      }
      dateField.selectedDate = date.toDate();
      errorLocation = 50;  
      this.updateData();   
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "validateDateField", errorLocation);
    }

    return true;
  }

  setFocusDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      dateField.setFocus();
      //dateField.text = this.jsonReader.getSingletons().displayedDate;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "validateDateField", errorLocation);
    }
  }

  updateData(){
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    errorLocation = 10;
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    errorLocation = 20;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 30;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "accountPeriod.do?";
    this.actionMethod = 'method=displayLog';
    errorLocation = 40;
    this.requestParams['menuAccessId'] = this.menuAccessId;
    // this.requestParams['fromDate'] = this.fromDateField.text;
    // this.requestParams['toDate'] =  this.toDateField.text;
    this.requestParams['maintEventId'] =  this.maintEventId;
    // this.requestParams['action'] =  this.action;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 50;
    this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [updateData] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "updateData", errorLocation);
    }
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  setViewOrAmendSubScreenFromChild(screenName){
    this.viewOrAmendSubScreen = screenName;
  }
  getParams(){
    const canAcceptOrReject = StringUtils.isTrue(this.authOthers) && this.requestUser != this.currentUserId;
    if("ACCT_GRP"===this.maintFacilityId){

      return [this.viewOrAmendSubScreen, this.recordValue.text, this.maintEventId , this.actionValue,this.menuAccessId, ''+canAcceptOrReject,''+this.amendInFacilityAccess] ;
    }
    if("STOP_RULE"===this.maintFacilityId){
      return [{"screenName":this.viewOrAmendSubScreen, "stopRuleId":this.recordValue.text, 'maintEventId':this.maintEventId, 'action':this.actionValue,'parentMenuAccess':this.menuAccessId, 'authOthers':''+canAcceptOrReject,"amendInFacilityAccess": ''+this.amendInFacilityAccess}] ;

      
    }
    if("SPREAD_PROFILES"===this.maintFacilityId){
      return [{"screenName":this.viewOrAmendSubScreen, "spreadId":this.recordValue.text, 'maintEventId':this.maintEventId,'action':this.actionValue,'parentMenuAccess':this.menuAccessId, 'authOthers':''+canAcceptOrReject,"amendInFacilityAccess":''+this.amendInFacilityAccess}] ;
    }

    if("CRIT_PAYMENT_TYPE"===this.maintFacilityId){
      return [this.viewOrAmendSubScreen, this.recordValue.text, "", this.maintEventId, this.actionValue, this.menuAccessId, ''+canAcceptOrReject,''+this.amendInFacilityAccess] ;
    }
    
  }

  getParamsFromParent(){
    return this.getParams();
  }

  viewHandler(action) {
    let params = [];
    //Variable for errorLocation
    let errorLocation = 0;
    
    try {
      this.viewOrAmendSubScreen = action;
      if("ACCT_GRP"===this.maintFacilityId){
        // let params = [action, this.jsonReader.getSingletons().recordId]

        // var newWindow = window.open("/AccountGroupDetail", 'Acccount Group Details', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no' );
        // if (window.focus) {
        //   newWindow.focus();
        // }
        ExternalInterface.call('buildFacilityURL', 'AccountGroupDetail',action);


      }
      if("STOP_RULE"===this.maintFacilityId){
        // let params = [action, this.jsonReader.getSingletons().recordId]

        // var newWindow = window.open("/stopRuleAdd", 'StopRule Add Group Details', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no' );
        // if (window.focus) {
        //   newWindow.focus();
        // }

        ExternalInterface.call('buildFacilityURL', 'stopRuleAdd',action);

      }

      if("SPREAD_PROFILES"===this.maintFacilityId){
        // let params = [action, this.jsonReader.getSingletons().recordId]

        // var newWindow = window.open("/spreadProfilesAdd", 'Spread Profile Details', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no' );
        // if (window.focus) {
        //   newWindow.focus();
        // }

        ExternalInterface.call('buildFacilityURL', 'spreadProfilesAdd',action);
      }

      if("CRIT_PAYMENT_TYPE"===this.maintFacilityId){

        ExternalInterface.call('buildFacilityURL', 'criticalMvtUpdateDetail',action);
      }


      // params.push({
      //   reference: this.selectedtRow.reference.content,
      //   action: this.selectedtRow.action.content
      // });
      errorLocation = 10;
      // ExternalInterface.call('openViewLogScreen', 'displayViewLogScreen', JSON.stringify(params));
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [viewHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "viewHandler", errorLocation);
    }
  }

  popupClosed(): void {
    window.close();
  } 

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: MaintenanceEventDetails}
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule,MonacoEditorModule,CommonModule],
  declarations: [MaintenanceEventDetails],
  entryComponents: []
})
export class MaintenanceEventDetailsModule {
 }