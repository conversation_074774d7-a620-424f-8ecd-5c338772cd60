<SwtModule (creationComplete)='onLoad()' paddingLeft="10" paddingTop="10" height='620' title='{{title}}' width='100%'>
  <VBox  height='100%' >
    <SwtCanvas width='98%'  height="90%"  >
      <HBox width='100%' height="185"  >
        <VBox width='100%'>
          <HBox width='100%' height="30">
            <SwtLabel textDictionaryId="connectionPool.module" width="160" height="19" #categoryRuleNameLabel>
            </SwtLabel>
            <SwtTextInput enabled="false" id="module" #module height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width='100%' height='30'>
            <SwtLabel textDictionaryId="connectionPool.connectionId"  width="160" height="19">
            </SwtLabel>
            <SwtTextInput enabled="false" id="connectionId" #connectionId height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" height='30'>
            <SwtLabel textDictionaryId="connectionPool.status" width="160" height="19">
            </SwtLabel>
            <SwtTextInput enabled="false" id="status" #status height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" height='30'>
            <SwtLabel textDictionaryId="connectionPool.lastActionTime"  width="160" height="19" >
            </SwtLabel>
            <SwtTextInput enabled="false" id="lastActionTime" #lastActionTime height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" height='30'>
            <SwtLabel textDictionaryId="connectionPool.duration"  width="160" height="19">
            </SwtLabel>
            <SwtTextInput enabled="false" id="duration" #duration height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" height='30'>
            <SwtLabel  textDictionaryId="connectionPool.sqlStatus"  width="160" height="19" >
            </SwtLabel>
            <SwtTextInput enabled="false" id="sqlStatus" #sqlStatus height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" height='30'>
            <SwtLabel textDictionaryId="connectionPool.sid"  width="160" height="19" >
            </SwtLabel>
            <SwtTextInput enabled="false" id="sid" #sid height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" height='110'>
            <SwtLabel textDictionaryId="connectionPool.sqlStatement"  width="160" height="19" >
            </SwtLabel>
            <SwtTextArea enabled="false" id='sqlStatement' #sqlStatement [editable]='false' height="90%" required="true" width="550">
            </SwtTextArea>
          </HBox>
          <HBox width="100%" height='30'>
            <SwtLabel textDictionaryId="connectionPool.sqlId" width="160" height="19" >
            </SwtLabel>
            <SwtTextInput enabled="false" id="sqlId" #sqlId height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" height='30'>
            <SwtLabel textDictionaryId="connectionPool.sqlExecStartTime" width="160" height="19" >
            </SwtLabel>
            <SwtTextInput enabled="false" id="sqlExecStart" #sqlExecStart height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" height='30'>
            <SwtLabel  textDictionaryId="connectionPool.audsid"  width="160" height="19">
            </SwtLabel>
            <SwtTextInput enabled="false" id="audsid" #audsid height="22" width="270"
              editable="false">
            </SwtTextInput>
          </HBox>
          <HBox width="100%" height='110'>
            <SwtLabel textDictionaryId="connectionPool.stackTrace"  width="160" height="19" >
            </SwtLabel>
            <SwtTextArea  enabled="false" id='stackTrace' #stackTrace [editable]='false' height="90%" required="true" width="550">
            </SwtTextArea>
          </HBox>
        </VBox>
      </HBox>
    </SwtCanvas>
    <SwtCanvas id="canvasContainer" width='98%' height='7%'>
      <HBox>
        <SwtButton #killButton (click)="doKillConnectionEventHandler($event)" (keyDown)="keyDownEventHandler($event)" id="killButton"></SwtButton>
        <SwtButton buttonMode="true" id="cancelButton" #cancelButton (click)="popupClosed()"
          (keyDown)="keyDownEventHandler($event)"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
