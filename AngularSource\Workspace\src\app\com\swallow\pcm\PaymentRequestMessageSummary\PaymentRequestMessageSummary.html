<SwtModule (creationComplete)='onLoad()'  width="100%" height="100%">
    <VBox width="100%" height="100%" paddingRight="5" paddingBottom="5" paddingTop="5" paddingLeft="5">
      <SwtCanvas id="canvasGrid" #canvasGrid  width="100%" height="90%">
      </SwtCanvas>
      <SwtCanvas width="100%" id="canvasButtons" height="40" >
        <HBox width="100%">
          <HBox paddingLeft="5" width="100%">
            <SwtButton [buttonMode]="true"
                       id="viewButton"
                       #viewButton
                       (click)="doViewMessage($event)"
                       (keyDown)="keyDownEventHandler($event)">
            </SwtButton>
            <SwtButton [buttonMode]="true"
                       id="closeButton"
                       #closeButton
                       (click)="closeCurrentTab($event)"
                       (keyDown)="keyDownEventHandler($event)">
            </SwtButton>
          </HBox>
          <HBox horizontalAlign="right" paddingRight="5">
            <SwtLoadingImage #loadingImage></SwtLoadingImage>
            <SwtHelpButton id="helpIcon"
                           [buttonMode]="true"
                           enabled="true"
                           helpFile="groups-of-rules"
                           (click)="doHelp()">
            </SwtHelpButton>
          </HBox>
        </HBox>
      </SwtCanvas>
    </VBox>
  </SwtModule>
