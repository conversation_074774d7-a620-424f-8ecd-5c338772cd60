
import { Compo<PERSON>, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON>, ModuleWithProviders, NgModule } from '@angular/core';
import {
	HTTPComms, SwtButton,
	SwtCommonGrid,
	SwtHelpButton,
	<PERSON>gger,
	JSONReader,
	Swt<PERSON>lert,
	SwtUtil,
	SwtLoadingImage,
	CommonService,
	SwtModule,
	SwtDateField,
	SwtCanvas,
	Keyboard,
	ExternalInterface,
	SwtToolBoxModule,
} from "swt-tool-box";
import { Routes, RouterModule } from '@angular/router';

@Component({
	selector: 'app-paymentRequestLog',
	templateUrl: './PaymentRequestLog.html'
})
export class PaymentRequestLog extends SwtModule {
	private logger: Logger;
	private baseURL = SwtUtil.getBaseURL();
	selectedRowData: any = null;
	// START -- Screen Name and Version Number
	private moduleName: string = "Payment request Log";
	private versionNumber: string = "1.00.00";
	private releaseDate: string = "22 May 2019";
	// END -- Screen Name and Version Number

	private errorLocation = 0;
	private requestParams = [];
	private invalidComms = null;
	private currDate = null;
	private lastFromDate = null;
	private countFlag = false;
	private actionMethod = "";
  public paymentRequestId="";
	private fromDate = null;
	private toDate = null;
	//Menu access right. 0-Full access, 1 - View access and 2 - No access
	private menuAccess = 0;
	/**
		* Data Objects
	 **/
	private jsonReader: JSONReader = new JSONReader();
	private lastRecievedJSON;
	private prevRecievedJSON;
	private swtAlert: SwtAlert;
	private actionPath: string = null;
	private helpURL= null;
	private sysdateformat: string = null;
	private screenId: string = null;
	private lastToDate: string = null;
	private dataArray = [];
	public params = [];
  public moduleId = 'PCM';

	private inputData = new HTTPComms(this.commonService);
	private viewInputData = new HTTPComms(this.commonService);

	//**********************  Data Grid ***************************************************/ 
	@ViewChild('customGrid', { read: SwtCanvas }) customGrid: SwtCanvas;
	@ViewChild('paymentRequestLogGrid', { read: SwtCommonGrid }) paymentRequestLogGrid: SwtCommonGrid;

	//**********************  datePicker ***************************************************/ 
	// @ViewChild('frmDateChooser', { read: SwtDateField }) frmDateChooser: SwtDateField;
	// @ViewChild('toDateChooser', { read: SwtDateField }) toDateChooser: SwtDateField;

	//**********************  Buttons ***************************************************/ 
	// @ViewChild('refreshButton', { read: SwtButton }) refreshButton: SwtButton;
	@ViewChild('closeButton', { read: SwtButton }) closeButton: SwtButton;
	// @ViewChild('csv', { read: SwtButton }) csv: SwtButton;
	// @ViewChild('excel', { read: SwtButton }) excel: SwtButton;
	// @ViewChild('pdf', { read: SwtButton }) pdf: SwtButton;
	@ViewChild('helpIcon', { read: SwtHelpButton }) helpIcon: SwtHelpButton;
	// @ViewChild('loadingImage', { read: SwtLoadingImage }) loadingImage: SwtLoadingImage;

	constructor(private commonService: CommonService, private element: ElementRef) {
		super(element, commonService);
		this.logger = new Logger('Payment request Log', this.commonService.httpclient);
		this.swtAlert = new SwtAlert(this.commonService);
	}


  ngOnInit(): void {

		let paramsFromParent = [];

    if(window.opener && window.opener.instanceElement) {
      paramsFromParent =  window.opener.instanceElement.getParamsFromParent();
      if(paramsFromParent) {
        this.paymentRequestId =  paramsFromParent[0].paymentRequestId;
      }
    }

  }


  onLoad(){
		try {
			this.actionPath = "paymentDisplayPCM.do?";
			this.closeButton.enabled = true;
			this.requestParams["payReqId"] = this.paymentRequestId;
			this.doRefresh();

			this.requestParams = [];
			this.inputData.cbStart = this.startOfComms.bind(this);
			this.inputData.cbStop = this.endOfComms.bind(this);
			this.inputData.cbResult = (data) => {
				this.inputDataResult(data);

			};
		
			this.inputData.cbFault = this.inputDataFault.bind(this);
			this.inputData.encodeURL = false;
			this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
			
			this.inputData.send(this.requestParams);
			this.logger.info("method [initData] - END");

		}
		catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, 'PaymentRequestLog', 'onLoad');
		}

	}

	/**
	 * inputDataResult
	 *
	 * This function is used to load the retrieved datas from xml using the xmlReader and process it
	 * The Column datas are retieved and rendered using DataGrid and are located into the display
	 * containers.
	 *
	 */

	inputDataResult(event): void {
		let jsonList: any = null;
		let header = null;
		let pattern: RegExp = null;
		let rowsize;
		try {
			if (this.inputData.isBusy()) {
				this.inputData.cbStop();
			}
			else {
				this.lastRecievedJSON = event;
				this.jsonReader.setInputJSON(this.lastRecievedJSON);
				
				if (!JSONReader.compareJSON(this.lastRecievedJSON, this.prevRecievedJSON)) {
					if (this.jsonReader.getRequestReplyStatus()) {
						this.helpURL = this.jsonReader.getSingletons().helpurl;
						this.sysdateformat = this.jsonReader.getSingletons().dateformat;
						this.screenId = this.lastRecievedJSON.screenid;

						this.currDate = this.jsonReader.getSingletons().todate;
						//this.paymentRequestId = this.jsonReader.getSingletons().payReq?this.jsonReader.getSingletons().payReq:"";
						this.paymentRequestId = this.jsonReader.getSingletons().payReq;
						
						if (!this.jsonReader.isDataBuilding()) {
							rowsize = this.jsonReader.getRowSize();
							//Gets the columns data
							jsonList = this.jsonReader.getColumnData();
              for (let i = 0; i < jsonList.column.length; i++) {
                header = SwtUtil.getAMLMessages(jsonList.column[i].heading);
                jsonList.column[i].heading = header;
              }
              const obj = {columns: jsonList};
              if (this.paymentRequestLogGrid === null || this.paymentRequestLogGrid === undefined) {
                this.paymentRequestLogGrid.componentID = this.jsonReader.getSingletons().screenid;
                this.paymentRequestLogGrid.CustomGrid(obj);
              }
              this.paymentRequestLogGrid.doubleClickEnabled = true;
              this.paymentRequestLogGrid.CustomGrid(obj);
              if (this.jsonReader.getGridData().size > 0) {
                this.paymentRequestLogGrid.dataProvider = null;
                this.paymentRequestLogGrid.gridData = this.jsonReader.getGridData();
                this.paymentRequestLogGrid.setRowSize = this.jsonReader.getRowSize();
                this.paymentRequestLogGrid.doubleClickEnabled = true;
              } else {
                this.paymentRequestLogGrid.dataProvider = null;
                this.paymentRequestLogGrid.selectedIndex = -1;
              }



						//Assins the current xml to previous
						this.prevRecievedJSON = this.lastRecievedJSON;
					}
					else {
						//Send message if any error occurs
						this.swtAlert.error('generic exception');

					}
				}
			}
		}
		} catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, 'PaymentRequestLog', 'inputDataResult');
		}
	}
	

	/**
	 * inputDataFault
	 *
	 * If a fault occurs with the connection with the server then display the lost connection label
	 */

	inputDataFault(event): void {
		this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
		this.swtAlert.error(this.invalidComms);

	}
	  /**
			* startOfComms
			* Part of a callback function to all for control of the loading swf from the HTTPComms Object
			*/
	  startOfComms(): void {
	  }

	/**
	 * Part of a callback function to all for control of the loading swf from the HTTPComms Object
	 */
	endOfComms(): void {
	}


	/**
* validateDate
* @param requestType:String
*
* This function is used to validate the from and to date
*/



	/**
* popupClosedEventHandler
* @param event :Event
* Method called when pop up is closed to refresh Grid and Enable Buttons after pop up closed
*/
	 popupClosedEventHandler(event): void {
		try {
			this.selectedRowData = null;
		}
		catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, 'PaymentRequestLog', 'popupClosedEventHandler');
		}
	}



	/**
	 * refreshGrid
	 *
	 *
	 */
	refreshGrid(event): void {
		let jsonList: any = null;
		let header: String = null;
		let rowsize;
		try {
			if (this.inputData.isBusy()) {
				this.inputData.cbStop();
			}
			else {
				this.lastRecievedJSON = event;
				this.jsonReader.setInputJSON(this.lastRecievedJSON);
				//Check whether last recieved xml and cureent are same or not
				if (!JSONReader.compareJSON(this.lastRecievedJSON, this.prevRecievedJSON)) {
					if (this.jsonReader.getRequestReplyStatus()) {
						this.helpURL = this.jsonReader.getSingletons().helpurl;
						
						if (!this.jsonReader.isDataBuilding()) {
							rowsize = this.jsonReader.getRowSize();
							jsonList = this.jsonReader.getColumnData();
							//Sets the header names
							for (let ii = 0; ii < jsonList.column.length; ii++) {
								header = SwtUtil.getSystemMessages(jsonList.column[ii].heading);
								jsonList.column[ii].heading = header;
							}
						}
						if (this.jsonReader.getGridData().size > 0) {
							//Setes the grid data
							this.paymentRequestLogGrid.gridData = this.jsonReader.getGridData();
						} else {
							this.paymentRequestLogGrid.gridData = null;
						}
					
						this.prevRecievedJSON = this.lastRecievedJSON;
					}
					else {
						this.swtAlert.error('generic_exception');

					}
				}
			}
		}
		catch (error) {
			this.logger.error("paymentrequestlog Screen", error);
		}
	}

	/**
	   * keyDownEventHandler
	   * @param event: KeyboardEvent
	   */
	keyDownEventHandler(event): void {
		let eventString: String = null;
		try {
			eventString = event.target.parentElement.id;
			if ((event.keyCode == Keyboard.ENTER)) {
				if (eventString == "refreshButton") {
					this.doRefresh();
				} else if (eventString == "helpIcon") {
					this.doHelp();
				} else if (eventString == "closeButton") {
					this.closeCurrentTab();
				}
			}
		}
		catch (error) {
			this.logger.error("paymentrequestlog Screen", error);
		}
	}





	/**
 * refreshDataGridForDateChange
 * This function is used to initiate the request to the server for from or to date change
 */
	refreshDataGridForDateChange(): void {
	/*	let selectedFilter: string = null;
		let selectedSort: string = null;*/
		try {
			/*selectedFilter = this.getfilteredGridColumns();
			selectedSort = this.paymentRequestLogGrid.sortedGridColumn;*/
			this.requestParams = [];

			//Adding request params
			this.requestParams["payReqId"] = this.paymentRequestId;

			this.actionPath = "paymentDisplayPCM.do?";
			this.actionMethod = "method=logsDisplay";
			this.inputData.cbStart = this.startOfComms.bind(this);
			this.inputData.cbStop = this.endOfComms.bind(this);
			this.inputData.cbResult = (data) => {
				this.refreshGrid(data);
			};
			//to handle fault event
			this.inputData.cbFault = this.inputDataFault.bind(this);
			this.inputData.encodeURL = false;
			this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
			this.inputData.send(this.requestParams);

		}
		catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, 'PaymentRequestLog', 'refreshDataGridForDateChange');
		}
	}

	    /**
		   * doRefresh
		   * This method refreshes the screen when filtered or sorted
		   * or clicked the refresh button
		   */
	 doRefresh(): void {
		let selectedFilter = null;
		let selectedSort = null;
		try {

			// Initialise the requestParams
			this.requestParams = [];
			// this.requestParams["fromdate"] = this.frmDateChooser.text;
			// this.requestParams["todate"] = this.toDateChooser.text;
			this.requestParams["payReqId"] = this.paymentRequestId;
			this.actionMethod = "method=logsDisplay";

			this.inputData.cbStart = this.startOfComms.bind(this);
			this.inputData.cbStop = this.endOfComms.bind(this);
			this.inputData.cbResult = (data) => {
				this.refreshGrid(data);
			};
			this.inputData.cbFault = this.inputDataFault.bind(this);
			this.inputData.encodeURL = false;
			this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
			this.inputData.send(this.requestParams);
		}
		catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, 'PaymentRequestLog', 'doRefresh');
		}
	}



	
	closeCurrentTab() {
		try {
      //nullify
      this.params = [];
      this.dataArray = [];
      this.viewInputData = null;
      this.paymentRequestLogGrid = null;
      this.requestParams = [];
      this.inputData = null;
      this.jsonReader = null;
      this.menuAccess = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      ExternalInterface.call("close");
		} catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, 'PaymentRequestLog', 'closeCurrentTab');
		}

	}


	doHelp() {
		try {
      ExternalInterface.call("help");
		} catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, 'PaymentRequestLog', 'doHelp');
		}
	}

}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: PaymentRequestLog }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PaymentRequestLog],
  entryComponents: []
})
export class PaymentRequestLogModule { }
