<div #divRestOfspace>
  <SwtModule height="100%" width="100%">
    <VDividedBox maxHeightBottom="255" height="100%" liveDrag="true" extendedDividedBox="true" id="gridDivider" #gridDivider dividersAnimation="S"
      width="100%">
      <SwtCanvas class="top" width="100%" height="75%" border="false">
        <HDividedBox id="treeDivider" maxWidthLeft="250" extendedDividedBox="true"  liveDrag="true" #treeDivider height="100%" width="100%">
          <SwtCanvas class="left" id="treeBox" width="18%" height="100%" borderStyle="inset">
            <Grid width='100%' height="100%">
              <GridRow height="5%">
                <GridItem width='100%'>
                  <SwtCheckBox id="ShowActualDSOnly" #ShowActualDSOnly width="100%"
                    (change)="changeDatasetsInTree($event)" selected="false"></SwtCheckBox>
                </GridItem>
              </GridRow>
              <GridRow height="5%">
                <GridItem width='100%'>
                  <HBox width="100%" horizontalAlign="right" paddingRight="5">
                    <LinkButton id="ExpandCollapse" #ExpandCollapse buttonMode="true" (click)="expandOrCollapse($event)"
                      label="Expand All">
                    </LinkButton>
                  </HBox>
                </GridItem>
              </GridRow>
              <GridRow height="90%" >
                <GridItem height="100%" width='100%'>
                  <ILMTreeIndeterminate #ilmTree id="ilmTree" width="100%" height="100%" hideIcons="true"
                    addCheckbox="true" indeterminateCheckbox="true"></ILMTreeIndeterminate>
                </GridItem>
              </GridRow>
            </Grid>
          </SwtCanvas>
          <VBox class="right" id="chartsContainer" #chartsContainer width="82%" height="100%" minHeight="5">
            <HDividedBox maxWidthRight="300" liveDrag="true" id="legendsDivider" #legendsDivider class="legendsDivider" width="100%" height="100%"
              minHeight="5" extendedDividedBox="true" minWidth="870" dividersAnimation="E" styleName="ExtendedDivider">
              <SwtCanvas class="left" width="80%" height="100%" minHeight="5" borderStyle="inset">

                <SwtCanvas border="false" id="borderContainerChart"  width="100%" height="100%" minHeight="5">
                  <VBox width="100%" height="100%" minHeight="5">
                    <Grid width='100%' height="100%">
                      <GridRow  >
                        <GridItem width='100%'>
                            <SwtLabel height="14" id="labelForCombined" #labelForCombined visible="false" fontWeight="bold"
                              includeInLayout="false" paddingLeft="10">
                            </SwtLabel>
                        </GridItem>
                      </GridRow>
                      <GridRow #topChartContainer height="37">
                        <GridItem  height="100%"  width='100%'>
                          <HBox width="280" paddingTop="5">
                            <SwtLabel text="Interval" fontWeight="normal"></SwtLabel>
                            <!-- <charts:AccessibleTimeRange id="timeRange" width="100%" height="22" tabIndex="5" ></charts:AccessibleTimeRange> -->
                            <!-- <SwtSlider width="30%"></SwtSlider> -->
                            <SwtAdvSlider id="timeRange" #timeRange width="100%"></SwtAdvSlider>
                          </HBox>


                          <HBox height="35"  width='100%' horizontalAlign="right">
                            <VBox height="100%"  width='200'>
                              <SwtCheckBox  height="50%" id="sumByCutOff" #sumByCutOff label="Sum to Cut-off"
                              class="withoutMargin" (change)="updateData($event)"></SwtCheckBox>
                              <SwtCheckBox  height="50%" id="includeOpenMvnts" #includeOpenMvnts class="withoutMargin"
                              label="Include Open Movements" (change)="updateData($event)"></SwtCheckBox>
                            </VBox>
                            <VBox height="100%"  width='150'>
                              <SwtCheckBox height="50%" id="alignScaleCB" #alignScaleCB class="withoutMargin"
                              label="Include SOD" (change)="alignScale($event)"></SwtCheckBox>
                              <SwtCheckBox height="50%" id="sourceLiquidityTop" #sourceLiquidityTop class="withoutMargin"
                              label="Sources of Liquidity" (change)="sourceLiquidity_clickHandler($event)">
                            </SwtCheckBox>
                            </VBox>


                          </HBox>

                            <!-- <VBox height="100%"  width='100%'>
                              <HBox height="50%" horizontalAlign="right">
                                <SwtCheckBox id="sumByCutOff" #sumByCutOff label="Sum to Cut-off"
                                             class="withoutMargin" (change)="updateData($event)"></SwtCheckBox>
                               
                              </HBox>
                              <HBox height="50%" horizontalAlign="right">


                             
                              </HBox>

                            </VBox> -->
                        </GridItem>
                      </GridRow>
                      <GridRow >
                        <GridItem>
                          <HBox wrapContent='true' #chartValuesContainer id='chartValuesContainer' width="100%" >
                            <!-- <controls:ILMSeriesLiveValue id="timeDynamic" hasCercle="false" ></controls:ILMSeriesLiveValue> -->
                            <ILMSeriesLiveValue isTimeLiveItem='true' id="timeDynamicValue" #timeDynamicValue>
                            </ILMSeriesLiveValue>
                          </HBox>
                        </GridItem>
                      </GridRow>
                      <GridRow height="89%" >
                        <GridItem height="100%" width='100%'>
                          <SwtCanvas border="false" id="lineChartContainer" width="100%" height="101%" >

                            <!-- <controls:ILMLineChart id="linechart" visible="false" width="100%" height="100%" minHeight="5"
                      accumLabel="{accumLabel}" accumulatedDCLegend="{AccumulatedDCLegend}" assetsLegend="{assetsLegend}"
                      axisTitleStyleName="axisTitles" balanceLabel="{balanceLabel}" balancesLegend="{balanceLegend}"
                      chartValuesContainer="{chartValuesContainer}" showDataTips="true" timeDynamicValue="{timeDynamic}" ></controls:ILMLineChart> -->
                            <ILMLineChart id="linechart" #linechart width="100%" height="100%"></ILMLineChart>
                          </SwtCanvas>
                        </GridItem>
                      </GridRow>
                      <GridRow width="100%" height="17">
                        <GridItem width='100%'>
                          <HBox width="100%" horizontalAlign="center" >
                          <SwtLabel #timeLabel fontSize="10" fontWeight="bold"></SwtLabel>
                          <SwtLabel id="entityTimeFrameLabel" #entityTimeFrameLabel (click)="onChangeEntityTimeframe($event)" buttonMode="true" color="blue"
                                    fontWeight="bold" paddingLeft="-7"></SwtLabel>
                          <SwtLabel fontSize="10" fontWeight="bold" paddingLeft="-5" text=")"></SwtLabel>
                          </HBox>
                        </GridItem>
                      </GridRow>
                      <!--<HBox width="100%" height="1%">
                  </HBox>-->
                    </Grid>
                  </VBox>
                </SwtCanvas>
              </SwtCanvas>
              <SwtCanvas class="right" id="legendsBox" #legendsBox width="20%" height="100%" borderStyle="inset">
                <VDividedBox  maxHeightBottom="255" minHeightBottom="35" liveDrag="true" width="100%" height="100%">

                  <SwtCanvas class="top" style="border: 1px solid gray; box-shadow: 1px 1px #888888" border="false"
                    width="100%" height="60%">
                    <VBox id="legendsContainer" showScrollBar='true' width="100%" height="100%" paddingBottom="5"
                      paddingLeft="2" paddingTop="20">
                      <SwtLabel id="balanceLabel" #balanceLabel fontWeight="normal"></SwtLabel>
                      <CheckBoxLegend id="balanceLegend" #balanceLegend (change)="legendItemChanged($event)"
                        paddingTop="5"></CheckBoxLegend>
                      <SwtLabel id="accumLabel" #accumLabel fontWeight="normal"></SwtLabel>
                      <CheckBoxLegend #AccumulatedDCLegend id="AccumulatedDCLegend" (change)="legendItemChanged($event)"
                        paddingTop="5"></CheckBoxLegend>
                      <SwtLabel id="thresholdsLegendLabel" #thresholdsLegendLabel fontWeight="normal"></SwtLabel>
                      <VBox id="thresholdsLegends" #thresholdsLegends paddingLeft="5">
                      </VBox>

                    </VBox>
                  </SwtCanvas>
                  <SwtCanvas width="100%" style="border: 1px solid gray; box-shadow: 1px 1px #888888" border="false"
                    class="bottom" height="40%"  minHeight="35">
                    <VBox id="AssetLiquidityContainer" showScrollBar='true' width="100%" height="100%" paddingLeft="2">

                      <VBox width="100%" paddingLeft="5" verticalGap="0">
                        <SwtLabel id="sourcesLabel" height="17" #sourcesLabel fontWeight="normal" class="no-margin">
                        </SwtLabel>
                        <SwtLabel id="groupsLegendLabel" height="17" #groupsLegendLabel fontWeight="normal" class="no-margin">
                        </SwtLabel>
                        <SwtComboBox id="groupCombo" #groupCombo width="200" (change)="getSourceLiquidity(true)"
                          dataLabel="group">
                        </SwtComboBox>
                        <SwtLabel id="scenarioLegendLabel" height="17" #scenarioLegendLabel fontWeight="normal" class="no-margin">
                        </SwtLabel>
                        <SwtComboBox id="scenarioCombo" #scenarioCombo width="200" (change)="getSourceLiquidity(false)"
                          dataLabel="scenario">
                        </SwtComboBox>
                        <VBox width="100%" verticalGap="2">
                          <AssetsLegend #assetsLegend id="assetsLegend" (change)="legendItemChanged($event)"
                            paddingTop="5"></AssetsLegend>
                        </VBox>
                      </VBox>
                    </VBox>

                  </SwtCanvas>
                </VDividedBox>
              </SwtCanvas>
            </HDividedBox>
          </VBox>
        </HDividedBox>



      </SwtCanvas>
      <SwtCanvas class="bottom" id="bottomGridContainer" width="100%" height="25%">
        <SwtCanvas border="false" width="100%" height="100%">
          <HBox id="gridContainer" width="100%" height="100%">
            <SwtTabNavigator id="tabNavigator" #tabNavigator width="94%" height="100%"
              (onChange)="initScenarioTab($event)" paddingBottom="5" paddingLeft="5">
              <SwtTabPushStrategy #groupsTab id="groupsTab">
                <HBox width="100%" height="100%" paddingRight="8">
                  <VBox id="grpgrid" width="100%" height="100%">
                    <SwtCanvas border="false" id="groupGridContainer" #groupGridContainer width="100%" height="100%">
                    </SwtCanvas>
                  </VBox>
                </HBox>
              </SwtTabPushStrategy>

              <SwtTabPushStrategy #scenarioTab id="scenarioTab" width="100%" height="100%">
                <HBox width="100%" height="100%" paddingRight="10">
                  <VBox width="100%" height="100%">
                    <SwtCanvas border="false" id="scenarioGridContainer" #scenarioGridContainer width="100%"
                      height="100%">
                    </SwtCanvas>
                  </VBox>
                </HBox>
              </SwtTabPushStrategy>
              <SwtTabPushStrategy #balancesTab id="balancesTab" width="100%" height="100%">
                <HBox width="100%" height="100%" paddingRight="10">
                  <VBox width="100%" height="100%">
                    <SwtCanvas border="false" id="balanceGridContainer" #balanceGridContainer width="100%"
                      height="100%">
                    </SwtCanvas>
                  </VBox>
                </HBox>
              </SwtTabPushStrategy>
            </SwtTabNavigator>
            <VBox id="maintainGrp" width="10%" height="100%" horizontalAlign="center">
              <SwtButton id="maintainButton" #maintainButton buttonMode="true"
                (click)="maintainButton_clickHandler($event)" width="70" marginBottom="6"></SwtButton>
              <SwtButton id="styleBtn" #styleBtn (click)="setGrpScnStyle()" width="70" marginBottom="6">
              </SwtButton>
              <SwtButton id="closeButton" #closeButton buttonMode="true" (click)="closeHandler($event)" width="70"
                marginBottom="6">
              </SwtButton>
              <DataExport #exportContainer id="exportContainer_{{id}}"></DataExport>
            </VBox>
          </HBox>
        </SwtCanvas>

      </SwtCanvas>
    </VDividedBox>


    <SwtLabel #htmlText id="htmlText" includeInLayout="false">
    </SwtLabel>
  </SwtModule>
</div>
