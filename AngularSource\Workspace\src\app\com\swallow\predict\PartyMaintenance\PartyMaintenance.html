<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%" paddingLeft="5" paddingRight="5" paddingTop="5" paddingBottom="5">
    <SwtCanvas id="partySearchCanvas" width="100%" minHeight="90">
      <HBox width="100%" verticalAlign="middle">
        <VBox width="75%" verticalGap="5" paddingLeft="5" paddingTop="5">
          <HBox verticalAlign="middle" width="100%">
            <SwtLabel #entityLabel id="entityLabel" width="80"></SwtLabel>
            <SwtComboBox #entityCombo id="entityCombo" width="200" dataLabel="entityList" (change)="onEntityChange()"></SwtComboBox>
            <SwtLabel #entityDescLabel id="entityDescLabel" paddingLeft="10" fontWeight="normal"></SwtLabel>
          </HBox>
          <HBox verticalAlign="middle" width="100%">
            <SwtLabel #partyIdLabel id="partyIdLabel" width="80"></SwtLabel>
            <SwtTextInput #partyIdInput id="partyIdInput" width="200" (change)="onSearchInputChange()" maxLength="12"></SwtTextInput>
          </HBox>
          <HBox verticalAlign="middle" width="100%">
            <SwtLabel #partyNameLabel id="partyNameLabel" width="80"></SwtLabel>
            <SwtTextInput #partyNameInput id="partyNameInput" width="400" (change)="onSearchInputChange()" maxLength="60"></SwtTextInput>
            <SwtButton #searchButton id="searchButton" (click)="onSearchClick()" marginLeft="10"></SwtButton>
            <SwtButton #resetButton id="resetButton" (click)="onResetClick()" marginLeft="5"></SwtButton> <!-- Reset Button Added -->
          </HBox>
        </VBox>
        <HBox #pageBoxTop id="pageBoxTop" width="25%" horizontalAlign="right" paddingRight="5" visible="false">
          <SwtCommonGridPagination #numStepperTop></SwtCommonGridPagination>
        </HBox>
      </HBox>
    </SwtCanvas>

    <SwtCanvas id="dataGridContainer" #dataGridContainer styleName="canvasWithGreyBorder" marginTop="10" border="false" width="100%" height="100%" minHeight="300"></SwtCanvas>

    <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5">
      <HBox width="100%" verticalAlign="middle">
        <HBox paddingLeft="5" width="70%" horizontalGap="5">
          <SwtButton #addButton id="addButton" (click)="onAddClick()"></SwtButton>
          <SwtButton #changeButton id="changeButton" (click)="onChangeClick()"></SwtButton>
          <SwtButton #deleteButton id="deleteButton" (click)="onDeleteClick()"></SwtButton>
          <SwtButton #aliasButton id="aliasButton" (click)="onAliasClick()"></SwtButton>
          <SwtButton #closeButton id="closeButton" (click)="onCloseClick()"></SwtButton>
        </HBox>
        <HBox width="30%" horizontalAlign="right" paddingRight="5" horizontalGap="10">
          <SwtHelpButton #helpButton id="helpButton" (click)="onHelpClick()"></SwtHelpButton>
           <SwtButton [buttonMode]="true" #printButton id="printButton" styleName="printIcon"   (click)="printPage()">
            </SwtButton><SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>