import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService, ExternalInterface,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas, SwtCommonGrid,
  SwtLoadingImage, SwtModule, SwtUtil, SwtToolBoxModule, SwtLabel, SwtTextInput,
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";

@Component({
  selector: 'app-sub-total-pop-up',
  templateUrl: './SubTotalPopUp.html',
  styleUrls: ['./SubTotalPopUp.css']
})
export class SubTotalPopUp extends SwtModule{

  @ViewChild('lblId') lblId: SwtLabel;
  @ViewChild('lblName') lblName: SwtLabel;
  @ViewChild('btnSearch') btnSearch: SwtButton;
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('txtId') txtId: SwtTextInput;
  @ViewChild('txtName') txtName: SwtTextInput;
  @ViewChild('forecastMonitorTemplateCanvas') forecastMonitorTemplateCanvas: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;


  private  swtAlert: SwtAlert;
  private forecastMonitorTemplateGrid: SwtCommonGrid;
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public inputData = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;

  private templateId:string=null;
  private templateName:string=null;
  private ordnialPos:string=null;
  private userId:string=null;
  private gridJSONList: any;
  private idValue:string=null;
  private nameValue:string=null;
  private selectedIds = [];
  private selectedNames= [];
  private selectedOrdinalPos = [];
  private selectedColumnNo = [];
  private columnId:string=null;
  private description:string=null;
  private shortName: string=null;
  private pressedbtn:string=null;
  
  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  ngOnInit(): void {
    this.lblId.text = SwtUtil.getPredictMessage('label.findoraddpopup.id', null);
    this.txtId.toolTip = SwtUtil.getPredictMessage('tooltip.findoraddpopup.id', null);
    this.btnSearch.label = SwtUtil.getPredictMessage('button.search', null);
    this.btnSearch.toolTip = SwtUtil.getPredictMessage('tooltip.findoraddpopup.Search', null);
    this.lblName.text =SwtUtil.getPredictMessage('label.findoraddpopup.name', null);
    this.txtName.toolTip = SwtUtil.getPredictMessage('tooltip.findoraddpopup.name', null);
    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
  }

  onLoad() {
    this.forecastMonitorTemplateGrid = <SwtCommonGrid> this.forecastMonitorTemplateCanvas.addChild(SwtCommonGrid);
    this.forecastMonitorTemplateGrid.allowMultipleSelection=true;
    this.forecastMonitorTemplateGrid.onFilterChanged = this.disableAddBtn.bind(this);
    this.forecastMonitorTemplateGrid.onSortChanged = this.disableAddBtn.bind(this);
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);

    this.templateId=ExternalInterface.call('eval', 'templateId');
    this.templateName=ExternalInterface.call('eval', 'templateName');
    this.ordnialPos=ExternalInterface.call('eval', 'ordinalPos');
    this.userId=ExternalInterface.call('eval', 'userId');
    this.columnId=ExternalInterface.call('eval', 'columnId');
    this.description=ExternalInterface.call('eval', 'description');
    this.shortName=ExternalInterface.call('eval', 'shortName');
    this.pressedbtn=ExternalInterface.call('eval', 'pressedbutton');
    //result event 
    this.inputData.cbResult= (event)=> {
      this.inputDataResult(event);
    };
    //fault event
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    //action url	
    this.actionPath="forecastMonitorTemplate.do?method=";
    //Then declare the action method:					
    this.actionMethod="addPopUpForSubtotal&loadFlex=true";


    this.actionMethod+="&templateId=" + this.templateId;
    this.actionMethod+="&templateName=" + this.templateName;
    this.actionMethod+="&userId=" + this.userId;
    this.actionMethod+="&ordinalPos=" + this.ordnialPos;
    this.actionMethod+="&columnId=" + this.columnId;
    this.actionMethod+="&shortName=" + this.shortName;
    this.actionMethod+="&description=" + this.description;
    this.actionMethod+="&pressedbutton=" + this.pressedbtn;

    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    this.forecastMonitorTemplateGrid.onRowClick = () =>{
      this.cellLogic();
    }

  }
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  inputDataFault(): void {
    this.swtAlert.error('Generic exception error');

  }
  inputDataResult(event):void {
    try {
      this.lastRecievedJSON= event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        if (( this.lastRecievedJSON != this.prevRecievedJSON)) {
          //this.gridJSONList=this.jsonReader.getGridData();

          if (!this.jsonReader.isDataBuilding()) {


            const obj = {columns: this.jsonReader.getColumnData()};
            this.forecastMonitorTemplateGrid.CustomGrid(obj);
            if (this.jsonReader.getGridData().row  ) {
              this.forecastMonitorTemplateGrid.gridData = this.jsonReader.getGridData();
              this.forecastMonitorTemplateGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              this.forecastMonitorTemplateGrid.gridData = {row: [], size: 0};
              this.forecastMonitorTemplateGrid.setRowSize = 0;
            }

          }
          this.prevRecievedJSON= this.lastRecievedJSON;
        }
        else {
          this.addButton.enabled=(this.forecastMonitorTemplateGrid.selectedIndex > -1);

        }
      }
      else
      {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error", Alert.OK);
      }
    } catch (e) {
      console.log('"eerrr', e)
    }

  }
  disableAddBtn() {
    if(this.forecastMonitorTemplateGrid.selectedIndex == -1)
      this.addButton.enabled = false;
  }
 cellLogic():void
  {
    if (this.forecastMonitorTemplateGrid.selectedIndex >= 0)
    {
      this.idValue= this.forecastMonitorTemplateGrid.selectedItem.id.content;
      this.nameValue= this.forecastMonitorTemplateGrid.selectedItem.name.content;
      this.addButton.enabled=true;
      this.selectedIds= [];
      this.selectedNames= [];
      this.selectedOrdinalPos= [];
      this.selectedColumnNo= [];
      for (var selMvmtCounter=0; selMvmtCounter < this.forecastMonitorTemplateGrid.selectedIndices.length; selMvmtCounter++) {
        this.selectedIds.push(this.forecastMonitorTemplateGrid.selectedItems[selMvmtCounter].id.content);
        this.selectedNames.push(this.forecastMonitorTemplateGrid.selectedItems[selMvmtCounter].name.content);
        this.selectedOrdinalPos.push(this.forecastMonitorTemplateGrid.selectedItems[selMvmtCounter].ordinalpos.content);
        this.selectedColumnNo.push(this.forecastMonitorTemplateGrid.selectedItems[selMvmtCounter].columnno.content);

      }

    }

  }
  addValuesToParent():void
  {
    this.requestParams = [];

    this.requestParams["shortName"]= this.shortName;
    this.requestParams["description"]= this.description;
    this.requestParams["columnId"]= this.columnId;
    this.requestParams["selectedIds"]= this.selectedIds.toString();
    this.requestParams["selectedColumnNo"]= this.selectedColumnNo.toString();
    this.requestParams["selectedOrdinalPos"]= this.selectedOrdinalPos.toString();
    this.requestParams["selectedNames"]= this.selectedNames.toString();
    this.requestParams["fromFlex"]= true;
    this.inputData.cbResult= () => {
      this.setColSource();
    };
    this.actionPath="forecastMonitorTemplate.do?method=";
    this.actionMethod="addSubTotalColumnSources";
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

  }
 searchRecords():void
  {
    this.txtId.enabled=true;
    this.txtName.enabled=true;
    this.btnSearch.enabled=true;
    this.forecastMonitorTemplateGrid.enabled=true;
    this.requestParams["fieldId"]=this.txtId.text;
    this.requestParams["fieldName"]= this.txtName.text;
    this.inputData.send(this.requestParams);
  }
  setColSource():void
  {
    if (window.opener && window.opener.instanceElement) {
      window.opener.instanceElement.refreshSubDetail();
      window.close();
    }
   // ExternalInterface.call("refreshParent");
  }
  closeHandler():void
  {
    ExternalInterface.call("close");
  }


}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: SubTotalPopUp }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SubTotalPopUp],
  entryComponents: []
})
export class SubTotalPopUpModule {}
