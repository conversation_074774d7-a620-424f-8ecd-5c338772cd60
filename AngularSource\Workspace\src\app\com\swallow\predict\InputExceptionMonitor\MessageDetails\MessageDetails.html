<SwtModule (creationComplete)='onLoad()' width="100%" height="100%" >
  <VBox width='100%' height='100%' paddingLeft="10" paddingRight="10" paddingTop="10" paddingBottom="10">
    <SwtCanvas width="100%" height="5%">
      <Grid width="100%" height="100%">
        <GridRow width="100%" height="100%">
          <GridItem width="80%">
          <GridItem>
            <SwtLabel id="messageTypelbl" #messageTypelbl textDictionaryId="inputexceptions.label.message_type" ></SwtLabel>
          </GridItem>
          <GridItem paddingLeft="10">
            <SwtLabel id="messageTypeValue" #messageTypeValue  fontWeight="normal" visible="true"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtComboBox id="messageTypeCombo"
                         #messageTypeCombo
                         (change)="changeCombo($event)"
                         dataLabel="messageTypeList" visible="false"></SwtComboBox>
          </GridItem>
          <GridItem paddingLeft="30">
            <SwtLabel id="messageStatuslbl" styleName="inputDate" #messageStatuslbl  textDictionaryId="inputexceptions.label.message_status" ></SwtLabel>
          </GridItem>
          <GridItem paddingLeft="10">
            <SwtLabel id="messageStatusValue" #messageStatusValue fontWeight="normal"></SwtLabel>
          </GridItem>
          </GridItem>
          <GridItem>
            <HBox #pageBox horizontalAlign="right" visible="false">
              <SwtCommonGridPagination #pagination></SwtCommonGridPagination>
            </HBox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <SwtCanvas width="100%" height="90%">
    <VDividedBox height="100%" width="100%">
          <SwtCanvas class="top" 
                  id="canvasGrid" 
                  #canvasGrid 
                  width="100%" 
                  height="50%">
          </SwtCanvas>
            <SwtCanvas width="100%" height="50%" class="bottom">
              <VBox width="100%" height="100%">
              <HBox id="autoFormatXMLContainer" #autoFormatXMLContainer horizontalAlign="right" height="25" paddingTop="0" visible="false">
                <SwtCheckBox id="autoFormatXMLCombo" #autoFormatXMLCombo (change)="autoFormatXMLMessage($event)" selected="true"
                              ></SwtCheckBox>
              </HBox>
              <SwtTextArea width="100%" height="90%" id="messageForm" #messageForm editable="false"></SwtTextArea>
              </VBox>
            </SwtCanvas>
      </VDividedBox>
    </SwtCanvas>
    <SwtCanvas width="100%" height="5%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true"
                      id="reprocessButton"
                      width="80"
                      #reprocessButton
                      (keyDown)="keyDownEventHandler($event)"
                      (click)="reprocessMessages()">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                      id="suppressButton"
                      #suppressButton
                      (keyDown)="keyDownEventHandler($event)"
                      (click)="suppressOrRejectMessages('suppress')">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                      id="rejectButton"
                      #rejectButton
                      (keyDown)="keyDownEventHandler($event)"
                      (click)="suppressOrRejectMessages('reject')">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                      id="closeButton" 
                      #closeButton 
                      (click)="closeHandler($event)"
                      (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">
          <HBox horizontalAlign="right" paddingRight="10" marginTop="2">
            <SwtLoadingImage #loadingImage id="loadingImage"> </SwtLoadingImage>
          </HBox>
        </HBox>
	  </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
