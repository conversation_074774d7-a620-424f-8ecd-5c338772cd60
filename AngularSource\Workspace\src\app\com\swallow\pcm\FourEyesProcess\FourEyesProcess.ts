import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { SwtAlert, CommonService, SwtModule, SwtUtil, HTTPComms, JSONReader, SwtButton, focusManager, Keyboard, ExternalInterface, SwtTextInput, Encryptor } from 'swt-tool-box';


declare var require: any;
 const $ = require('jquery');

@Component({
  selector: 'four-eyes-process',
  templateUrl: './FourEyesProcess.html',
  styleUrls: ['./FourEyesProcess.css']
})
export class FourEyesProcess extends SwtModule   implements OnInit {

  @ViewChild('loginButton') loginButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('passwordTextInput') passwordTextInput: SwtTextInput;
  @ViewChild('userNameTextInput') userNameTextInput: SwtTextInput;
  
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  public requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';

  /* - START -- Screen Name and Version Number ---- */
  private moduleName = 'Stop Rules Maintenance';
  private versionNumber = '1.00.00';
  private releaseDate = '04 March 2019';
  /* - END -- Screen Name and Version Number ---- */

  // to open a pop up
  private moduleURL: string = null;
  private swtAlert: SwtAlert;
  private menuAccess = '';
  private programId = '';
  public helpURL: string = null;
  public title: string = null;
  private errorLocation = 0;
  public moduleReportURL: string = null;
  public moduleId = '';

  private menuaccess = 2;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnDestroy(): any {
  }


  ngOnInit(): void {


  }
  
  onLoad() {
    try {
      // Assining properties for controls
      this.loginButton.label = "Validate";//String(SwtUtil.getCommonMessages('button.add'));
      this.cancelButton.label = "Cancel";//String(SwtUtil.getCommonMessages('button.add'));
      this.loginButton.setFocus();

      $(document).keydown(function(event) {
        if (event.ctrlKey==true && (event.which == '118' || event.which == '86')) {
            event.preventDefault();
         }
    });
    
    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'ClassName', 'onLoad');
    }

  }
  toHex(str) {
      var hex = '';
      for(var i=0;i<str.length;i++) {
        hex += ''+str.charCodeAt(i).toString(16);
      }
      return hex;
    }
   

  login(){
       //this.swtAlert.warning('Login');
      // this.moduleId = '';
      this.requestParams = [];
      let sessionId = "pe9gxq7qqd2q1oymx1uzenazk";
      let encryPass = Encryptor.encryptPredict(sessionId,this.userNameTextInput.text, this.passwordTextInput.text);
      this.actionMethod = 'method=login4Eyes';
      this.actionPath = 'fourEyes.do?';
      this.requestParams["clientSession"] = sessionId;
      this.requestParams["userId"] = this.userNameTextInput.text;
      this.requestParams["encPassword"] = encryPass;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      // this.inputDataResult(data);

      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
       this.inputData.send(this.requestParams);
  }

  /**
   * inputDataFault
   *
   * @param event:  FaultEvent
   *
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    try {
      this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'inputDataFault', this.errorLocation);
    }
  }
  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  public inputDataResult(data): void {
    let jsonList = null;
    let header: string;
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyMessage() == "SUCCESS") {
            
            this.result = {
              "login": "SUCCESS",
            };
            this.dispose();
          } else if (this.jsonReader.getRequestReplyMessage() == "ERROR_LOGIN") {
            this.swtAlert.error("Invalid login");
          } else  if (this.jsonReader.getRequestReplyMessage() == "ROLE_ACCESS") {
            this.swtAlert.error("Your role does not provide access to this feature");
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error inputDataResult', e);
    }
  }


  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    try {
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'startOfComms', this.errorLocation);
    }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    try {
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'endOfComms', this.errorLocation);
    }
  }





  /**
   * enableViewButton
   *
   */
  enableLoginButton(value: boolean): void {
    this.loginButton.enabled = value;
    this.loginButton.buttonMode = value;
  }

   /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(Button)
   */
  keyDownEventHandler(event): void {
    try {
      const eventstring = Object(focusManager.getFocus()).name;
      if ((event.keyCode === Keyboard.ENTER)) {
        if (eventstring === 'addButton') {
         // this.doAddPCPriorityMaintenance(event);
        } else if (eventstring === 'loginButton') {
          //this.doViewPCPriorityMaintenance(event);
        } else if (eventstring === 'cancelButton') {
          this.closeCurrentTab(event);
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'ClassName', 'keyDownEventHandler',   this.errorLocation);
    }
  }

  




    /**
   * closeCurrentTab
   *
   * Function called when close button is called
   *
   * @param event:Event
   */
  closeCurrentTab(event): void {
    try {
      this.result = {
        "login": "FAILED",
      };
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'ClassName', 'refreshGrid', this.errorLocation);
    }
  }

   /**
   * dispose
   *
   * This is an event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this.menuAccess = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      if(this.titleWindow){
        this.close();
      }else {
        window.close();
      }
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'dispose', this.errorLocation);
    }
  }





  doHelp() {
    try {

    } catch (error) {
        SwtUtil.logError(error, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }
    
  }


}




