import {CommonService, SwtButton, SwtModule, SwtTextInput} from 'swt-tool-box';
import {Component, ElementRef, ViewChild} from '@angular/core';


@Component({
    selector: 'app-rate-pop-up',
    templateUrl: './RatePopUp.html',
    styleUrls: ['./RatePopUp.css']
})

export class RatePopUp extends SwtModule {

    @ViewChild("okButton") public okButton: SwtButton;
    @ViewChild("cancelButton") public cancelButton: SwtButton;
    @ViewChild("refresh")  public refresh: SwtTextInput;

    refreshText: string;

    constructor(private element: ElementRef, private commonService: CommonService) {
        super(element, commonService);
    }

  onLoad() {
    this.refresh.text=this.refreshText +"";
  }

  closePopup() {
      try {
        if(this.titleWindow) {
          this.close();
        } else {
          window.close();
        }
      } catch (error) {
        console.log(error, "RatePopUp", "closePopup");
      }
    }

    okHandler() {
        try {
          this.parentDocument.saveRefreshRate(this.refresh.text);
          this.closePopup();
        } catch(error) {
            console.error('error :',error);
        }
    }
}
