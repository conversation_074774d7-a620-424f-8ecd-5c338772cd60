/* @(#)SchedulerAction.java 1.0 27/01/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.web;

import java.io.File;
import java.io.StringReader;
import java.io.StringWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.thoughtworks.xstream.io.xml.StaxDriver;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;








import org.swallow.batchScheduler.ILMProcess;
import org.swallow.batchScheduler.SwtJobInfo;
import org.swallow.batchScheduler.SwtJobScheduler;
import org.swallow.cluster.ZkUtils;
import org.swallow.control.dao.InterfaceInterruptDAO;
import org.swallow.control.model.Job;
import org.swallow.control.model.JobStatus;
import org.swallow.control.model.ScheduledReportParams;
import org.swallow.control.model.ScheduledReportType;
import org.swallow.control.model.Scheduler;
import org.swallow.control.service.SchedulerManager;
import org.swallow.control.service.UserMaintenanceManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SystemExceptionHandler;
import org.swallow.maintenance.dao.hibernate.SysParamsDAOHibernate;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.reports.model.SchedReportHist;
import org.swallow.reports.service.SchedReportHistManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.service.ILMAnalysisMonitorManager;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;

import com.thoughtworks.xstream.XStream;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * SchedulerAction.java
 *
 * This class is used to display the Scheduler details,add New Job, update
 * Previous job,Delete Existing Job.
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

@Scope("prototype")
@Controller
@RequestMapping(value = {"/scheduler", "/scheduler.do"})
public class SchedulerAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/control/addjob");
		viewMap.put("jobDisplay", "jsp/control/Jobmaintenance");
		viewMap.put("fail", "error");
		viewMap.put("showXML", "jsp/control/showXML");
		viewMap.put("displaySchedule", "jsp/control/addjob");
		viewMap.put("distList", "jsp/control/schedulerdistributionlist");
		viewMap.put("success", "jsp/control/BatchScheduler");
		viewMap.put("accessList", "jsp/control/scheduleraccesslist");
		viewMap.put("Schedulerview", "jsp/control/addjob");
		viewMap.put("inputJobEnable", "redirect:/inputProcess.go?screenName=inputJobEnableOrDisable");
//	@Result(name = "inputJobEnable", type="redirect", location = "/inputProcess.go?screenName=inputJobEnableOrDisable"),

	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}


	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();
	private Scheduler scheduler;
	public Scheduler getScheduler() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		// First check if there's a scheduler attribute in the request
		Scheduler schedulerFromAttr = (Scheduler) request.getAttribute("scheduler");
		if (schedulerFromAttr != null) {
			return schedulerFromAttr;
		}
		// If not, fall back to creating from request parameters
		scheduler = RequestObjectMapper.getObjectFromRequest(Scheduler.class, request,"scheduler");
		return scheduler;
	}

	public void setScheduler(Scheduler scheduler) {
		this.scheduler = scheduler;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("scheduler", scheduler);
	}
	private ScheduledReportParams scheduledReportParams;
	public ScheduledReportParams getScheduledReportParams() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		// First check if there's a scheduledReportParams attribute in the request
		ScheduledReportParams paramsFromAttr = (ScheduledReportParams) request.getAttribute("scheduledReportParams");
		if (paramsFromAttr != null) {
			return paramsFromAttr;
		}
		// If not, fall back to creating from request parameters
		scheduledReportParams = RequestObjectMapper.getObjectFromRequest(ScheduledReportParams.class, request,"scheduledReportParams");
		return scheduledReportParams;
	}

	public void setScheduledReportParams(ScheduledReportParams scheduledReportParams) {
		this.scheduledReportParams = scheduledReportParams;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("scheduledReportParams", scheduledReportParams);
	}
	private Job job;
	public Job getJob() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		// First check if there's a job attribute in the request
		Job jobFromAttr = (Job) request.getAttribute("job");
		if (jobFromAttr != null) {
			return jobFromAttr;
		}
		// If not, fall back to creating from request parameters
		job = RequestObjectMapper.getObjectFromRequest(Job.class, request,"job");
		return job;
	}

	public void setJob(Job job) {
		this.job = job;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("job", job);
	}
	private JobStatus jobStatus;
	public JobStatus getterJobStatus() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		if (jobStatus == null) {
			jobStatus = new JobStatus();
		}
		return jobStatus;
	}
	public void setJobStatus(JobStatus jobStatus) {
		this.jobStatus = jobStatus;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("jobStatus", jobStatus);
	}
	private String selectedjobId;
	public String getSelectedjobId() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		selectedjobId = request.getParameter("selectedjobId");
		return selectedjobId;
	}

	public void setSelectedjobId(String selectedjobId) {
		this.selectedjobId = selectedjobId;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("selectedjobId", selectedjobId);
	}

	private final Log log = LogFactory.getLog(SchedulerAction.class);

	/* Creating and Setting instance of Role Manager */
	@Autowired
	private SchedulerManager schedulerMgr = null;
	public void setSchedulerManager(SchedulerManager schedulerMgr) {
		this.schedulerMgr = schedulerMgr;
	}

	/**
	 * @param date
	 * @return
	 */
	public static String formatTime(Date date) {
		SimpleDateFormat sdf = null;

		if (date != null) {
			sdf = new SimpleDateFormat("HH:mm");
		} else {
			return "";
		}

		return sdf.format(date);
	}

	/**
	 * @param request
	 * @throws Exception
	 */
	private void putSystemDateInRequest(HttpServletRequest request)
			throws SwtException {
		log.debug("Entering ' putSystemDateInRequest' method");

		Date date = new Date();
		String systemDate = SwtUtil.formatDate(date, SwtUtil
				.getCurrentSystemFormats(request.getSession())
				.getDateFormatValue());
		String systemTime = formatTime(date);
		request.setAttribute("systemDate", systemDate);
		request.setAttribute("systemTime", systemTime);
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "add":
				return add();
			case "terminate":
				return terminate();
			case "exec":
				return exec();
			case "displaySchedule":
				return displaySchedule();
			case "SchedulerDelete":
				return SchedulerDelete();
			case "Scheduleradd":
				return Scheduleradd();
			case "schedulerChange":
				return schedulerChange();
			case "jobDisplay":
				return jobDisplay();
			case "Schedulerview":
				return Schedulerview();
			case "enableOrDisableJob":
				return enableOrDisableJob();
			case "getJobStatus":
				return getJobStatus();
			case "displayAccessList":
				return displayAccessList();
			case "displayDistList":
				return displayDistList();
			case "checkIfConfigParamsIsCorrect":
				return checkIfConfigParamsIsCorrect();
			case "getScreenDetails":
				return getScreenDetails();
			case "getreportsJobForAjax":
				return getreportsJobForAjax();
			case "showXMLWindow":
				return showXMLWindow();
		}


		return unspecified();
	}


	/**
	 * Control first goes to this method of Action Class whenever no particular
	 * value is given to "methodName" variable This calls the dispaly() method
	 */
	public String unspecified()
			throws SwtException {
		try {
			log.debug("Entering ' Scheduler unspecified' method");

			return display();
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SchedulerAction.'unspecified' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					swtexp, "unspecified", SchedulerAction.class), request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("SwtException Catch in SchedulerAction.'unspecified' method : "
							+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "unspecified", SchedulerAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method is used to display the scheduler details.
	 *
	 * @return
	 * @throws Exception
	 */
	public String display()
			throws SwtException {
		/* Local variable declaration */
		String hostId = null;
		String entityId = null;
		String changeButtonPressed = null;
		String filterStatus = null;
		String sortStatus = null;
		String sortDescending = null;
		String selectedScheduledJobType = null;
		String selectedScheduleReports = null;
		String gridScrollTop = null;
		ArrayList jobTypeList = null;

		try {
			HttpServletRequest request =  SwtUtil.getCurrentRequest();
			log.debug(this.getClass().getName() + " - [display] - " + "Entry");
			selectedScheduleReports = request.getParameter("selectedScheduleReports");
			selectedScheduledJobType = request.getParameter("selectedScheduledJobType");
			if (SwtUtil.isEmptyOrNull(selectedScheduledJobType)) {
				if (SwtConstants.JOB_TYPE_BOTH.equals(request.getParameter("scheduledJobType"))) {
					selectedScheduledJobType = SwtConstants.JOB_TYPE_PROCESS;
				} else {
					selectedScheduledJobType = request.getParameter("scheduledJobType");
				}
			}
			jobTypeList = new ArrayList();
			hostId = SwtUtil.getCurrentHostId();
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			Collection jobDetails = schedulerMgr.getJobStatusDetails(hostId,
					SwtUtil.getCurrentSystemFormats(request.getSession()),
					entityId, selectedScheduledJobType);
			boolean isJobRunning = false;

			/*
			 * In this method we use a parameter called 'changeButtonPressed'
			 * which is being passed to it through frontend. On pressing of
			 * change button either of the following two can take place 1) --
			 * screen refreshes and if the selected job is not running a
			 * change-job window opens 2) -- screen refreshes and if the
			 * selected job is running a msg is flashed to user that 'job cannot
			 * be changed' for the first case this method directs control to
			 * BatchScheduler.jsp for the second case this method refreshes the
			 * parent jsp and set another parameter 'isSelectedJobRunning' to N.
			 * When control goes back to BatchScheduler.jsp there the value of
			 * 'isSelectedJobRunning' is checked if 'isSelectedJobRunning' = 'Y' --
			 * flash an alert msg if 'isSelectedJobRunning'= 'N' -- open a child
			 * window for changing job.
			 */
			changeButtonPressed = request.getParameter("changeButtonPressed");

			String selectedjobId = request.getParameter("selectedjobId");
			String selectedScheduleId = request.getParameter("selectedScheduleId");
			request.setAttribute("jobIdSelected", selectedjobId);
			request.setAttribute("selectedScheduleId", selectedScheduleId);

			filterStatus = request.getParameter("selectedFilterStatus");
			sortStatus = request.getParameter("selectedSortStatus");
			sortDescending = request.getParameter("selectedSortDescending");
			gridScrollTop =request.getParameter("gridScrollTop");
			request.setAttribute("filterStatus", filterStatus);
			request.setAttribute("sortStatus", sortStatus);
			request.setAttribute("sortDescending", sortDescending);
			request.setAttribute("gridScrollTop", gridScrollTop);
			if ((changeButtonPressed != null)
					&& changeButtonPressed.equals("Y")) {
				if (jobDetails != null) {
					Iterator itr = jobDetails.iterator();

					while (itr.hasNext()) {
						JobStatus js = (JobStatus) (itr.next());

						if (js.getScheduleId().toString().equals(selectedScheduleId)) {
							String currentStatus = js.getCurrentStatus();
							if ((currentStatus != null)
									&& currentStatus
									.equals(SwtConstants.JOB_STATUS_RUNNING)) {
								isJobRunning = true;
							}

							break;
						}


					}
				}

				if (isJobRunning) {
					request.setAttribute("isSelectedJobRunning", "Y");
				} else {
					request.setAttribute("isSelectedJobRunning", "N");
				}
			}
			// Change the job name if it is report type
			if (jobDetails != null) {
				Iterator itr = jobDetails.iterator();
				while (itr.hasNext()) {
					JobStatus js = (JobStatus) (itr.next());
					if (SwtConstants.JOB_TYPE_REPORT.equals(js.getJobTypeProcessOrReport())) {
						js.getJob().setJobDescription(js.getJob().getJobDescription() + " - " + js.getReportName());
					}
				}
			}
			// Retrieve available job type list
			jobTypeList = getJobTypeList(request, false);
			request.setAttribute("jobTypeList", jobTypeList);
			request.setAttribute("lastRefTime", SwtUtil
					.getLastRefreshServerTimeOnGMTOffset(request));
			request.setAttribute("scheduledJobType", request.getParameter("scheduledJobType"));
			request.setAttribute("selectedJobType", request.getParameter("selectedJobType"));
			request.setAttribute("selectedScheduledJobType", selectedScheduledJobType);
			request.setAttribute("selectedScheduleReports", selectedScheduleReports);
			request.setAttribute("jobDetails", jobDetails);
			log.debug(this.getClass().getName() + " - [display()] - " + "Exit");
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);

			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SchedulerAction.'display' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					swtexp, "display", SchedulerAction.class), request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("SwtException Catch in SchedulerAction.'display' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display", SchedulerAction.class), request, "");
			return getView("fail");
		} finally {
			/* null the objects created already. */
			hostId = null;
			entityId = null;
			changeButtonPressed = null;
			filterStatus = null;
			sortStatus = null;
			sortDescending = null;
		}
	}

	public String add()
			throws SwtException {

		ArrayList jobTypeList = null;
		Iterator itr;
		String jobType = null;
		String selectedJobType = null;
		String selectedJobId = null;
		Collection jobNameList;
		Collection reportTypeList;
		ArrayList<ScheduledReportType> reportTypeResult;
		Scheduler scheduler = null;
		ScheduledReportParams scheduledReportParams = null;
		Job jobForm = null;
// To remove: 		DynaValidatorForm dyForm;
		String sourceScreen = null;
		String scheduledJobType = null;
		String schedReportTypeParams = null;
		String roleId = null;
		String entityId = null;
		try {
			log.debug("SchedulerAction.java " + " Add Method  ");
			HttpServletRequest request =  SwtUtil.getCurrentRequest();
			scheduledJobType = request.getParameter("scheduledJobType");
			sourceScreen = request.getParameter("sourceScreen");
			selectedJobType = request.getParameter("selectedJobType");
			selectedJobId = request.getParameter("selectedJobDescription");

			jobTypeList = new ArrayList();
			reportTypeList = new ArrayList();
			String hostId = SwtUtil.getCurrentHostId();

			String jobId = ((request.getParameter("selectedjobId") != null) && (request
					.getParameter("selectedjobId").trim().length() > 0)) ? request
					.getParameter("selectedjobId").trim()
					: null;

			String screenType = "";

			if (jobId != null) {
				Job job = schedulerMgr.getJobDetail(hostId, jobId);

				if ((job != null)
						&& ((job.getSystemJob() != null) && job.getSystemJob()
						.equalsIgnoreCase("Y"))) {
					screenType = "predict";
					request.setAttribute("screenType", "predict");
				} else {
					screenType = "input";
					request.setAttribute("screenType", "input");
				}
			} else {
				screenType = "predict";
				request.setAttribute("screenType", "predict");
			}

			// Retrieve available job type list
			jobTypeList = getJobTypeList(request, false);
			request.setAttribute("jobTypeList", jobTypeList);

			jobNameList = schedulerMgr.getJobNameList(hostId, selectedJobType);
			request.setAttribute("jobNameList", jobNameList);

			if (SwtUtil.isEmptyOrNull(selectedJobId)) {
				String selectedJobName ="";
				Iterator iter = jobNameList.iterator();
				Object obj = new Object();
				while( iter.hasNext()) {
					obj = iter.next();
					selectedJobName = ((LabelValueBean) obj).getLabel();
					selectedJobId = ((LabelValueBean) obj).getValue();
					break;
				}

			}

			// Retrieve report type list and put in request
			reportTypeList =  getReportTypeList(request, selectedJobId);
			request.setAttribute("reportTypeList", reportTypeList);

			schedReportTypeParams = schedulerMgr.getScheduledReportTypeConfig(selectedJobId);
			request.setAttribute("schedReportTypeParams", schedReportTypeParams);

			// Get role list
			putRoleListInReq(request, hostId);
			// Put output file list in request based on the selected report type
			putOutputFileListInRequest(request);
			/* Puts the list of entities into request object */
			putEntityListInReq(request);

			request.setAttribute("methodName", "add");
			request.setAttribute("scheduledJobType", scheduledJobType);

// To remove: 			dyForm = (DynaValidatorForm) form;

			scheduler = (Scheduler) getScheduler();
			scheduledReportParams = (ScheduledReportParams) getScheduledReportParams();
			// get the entity id if the entity id is not in the form bean then use the user's default
			if ((entityId == null)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				scheduledReportParams.setRunDateEntity(entityId);
			}
			jobForm = (Job) getJob();

			// Initialize Job type with CYCLIC
			scheduler.setJobType("O");
			scheduler.setJobStatus(SwtConstants.ADDJOB_ENABLE);
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			scheduledReportParams.setExecutionRole(roleId);
			scheduledReportParams.setMapDateMethod(SwtConstants.MAP_DATE_SYSTEM);
			if (SwtConstants.JOB_TYPE_REPORT.equals(scheduledJobType) || SwtUtil.getMessage("addJob.combo.report", request).equals(jobType) || selectedJobType.equals("R")) {
				jobForm.setJobType("R");
			}else {
				jobForm.setJobType("P");
			}
			putSystemDateInRequest(request);
			log.debug("Exiting 'add' Method");
			setJob(jobForm);
			setScheduler(scheduler);
			setScheduledReportParams(scheduledReportParams);
			return getView("add");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error("SwtException Catch in SchedulerAction.'add' method : "
					+ swtexp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					swtexp, "add", SchedulerAction.class), request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log.error("SwtException Catch in SchedulerAction.'add' method : "
					+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "add", SchedulerAction.class), request, "");
			return getView("fail");
		}
	}

	private Collection getReportTypeList(HttpServletRequest request, String selectedJobId) {
		ArrayList<ScheduledReportType> reportTypeResult;
		Collection reportTypeList = null;
		try {
			reportTypeList = new ArrayList();
			reportTypeResult = schedulerMgr.getReportTypes(SwtUtil.getCurrentHostId(), selectedJobId);
			for (int i = 0; i < reportTypeResult.size(); i++) {
				LabelValueBean bean = new LabelValueBean(reportTypeResult.get(i).getReportName(),
						reportTypeResult.get(i).getReportTypeId());
				reportTypeList.add(bean);
			}
		} catch (SwtException e) {
			log
					.error("SwtException Catch in SchedulerAction.'getReportTypeList' method : "
							+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "getReportTypeList", SchedulerAction.class), request, "");
		}
		return reportTypeList;
	}

	private ArrayList getJobTypeList(HttpServletRequest request, boolean showAllValue) {
		ArrayList jobTypeList = null;
		Iterator itr;
		try {
			jobTypeList = new ArrayList();
			if (showAllValue) {
				jobTypeList.add(new LabelValueBean("All", "B"));
			}
			itr = (schedulerMgr.getJobTypeList(SwtUtil.getCurrentHostId())).iterator();
			while (itr.hasNext()) {
				String jobType = (String) (itr.next());
				if ((SwtConstants.JOB_TYPE_PROCESS).equals(jobType)) {
					jobTypeList.add(new LabelValueBean(SwtUtil.getMessage("addJob.combo.process", request), jobType));
				} else if ((SwtConstants.JOB_TYPE_REPORT).equals(jobType)) {
					jobTypeList.add(new LabelValueBean(SwtUtil.getMessage("addJob.combo.report", request), jobType));
				}
			}
		} catch (SwtException e) {
			log
					.error("SwtException Catch in SchedulerAction.'getJobTypeList' method : "
							+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "getJobTypeList", SchedulerAction.class), request, "");
		}
		return jobTypeList;

	}

	public String terminate()
			throws SwtException {
		String selectedScheduledJobType = null;
		ArrayList jobTypeList = null;
		try {
			HttpServletRequest request =  SwtUtil.getCurrentRequest();
			log.debug("entering 'terminate' method");
			selectedScheduledJobType = request.getParameter("selectedScheduledJobType");
			if (SwtUtil.isEmptyOrNull(selectedScheduledJobType)) {
				selectedScheduledJobType = request.getParameter("scheduledJobType");
			}
			jobTypeList = new ArrayList();
			String currentStatus = null;
			String term = "false";


// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			JobStatus jobStatus = (JobStatus) getterJobStatus();
			String jobId = request.getParameter("selectedjobId");
			String selectedScheduleId = request.getParameter("selectedScheduleId");
			String hostId = SwtUtil.getCurrentHostId();
			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			Collection jobDetails = schedulerMgr.getJobStatusDetails(hostId,
					SwtUtil.getCurrentSystemFormats(request.getSession()),
					entityId, selectedScheduledJobType);
			Iterator itr = jobDetails.iterator();

			while (itr.hasNext()) {
				jobStatus = (JobStatus) itr.next();

				if (jobStatus.getScheduleId().toString().equalsIgnoreCase(selectedScheduleId)) {
					currentStatus = jobStatus.getCurrentStatus();
				}
			}

			if (currentStatus.equalsIgnoreCase("R")) {
				jobStatus.setScheduleId(Integer.parseInt(selectedScheduleId));
				jobStatus.setJobId(jobId);
				jobStatus.setHostId(hostId);

				SwtJobInfo swtjobinfo = new SwtJobInfo();
				swtjobinfo.setHostId(hostId);
				swtjobinfo.setJobId(jobId);
				swtjobinfo.setSchedulerId(Integer.parseInt(selectedScheduleId));

				SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
				swtJobScheduler.killJob(swtjobinfo, "T");
				ZkUtils.setSerializableProperty(ZkUtils.PROPERTY_KILL_SCHEDULER+"---"+ZkUtils.instanceUuid+"---"+selectedScheduleId,swtjobinfo);
				request.setAttribute("term", "true");

				log.debug("Exiting 'terminate' Method");
			}

			request.setAttribute("methodName", "terminate");
			request.setAttribute("scheduledJobType", request.getParameter("scheduledJobType"));
			request.setAttribute("selectedScheduledJobType", selectedScheduledJobType);
			return display();
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SchedulerAction.'terminate' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					swtexp, "terminate", SchedulerAction.class), request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("SwtException Catch in SchedulerAction.'terminate' method : "
							+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "terminate", SchedulerAction.class), request, "");
			return getView("fail");
		}
	}

	public String exec()
			throws SwtException {

		String selectedScheduledJobType = null;
		String selectedJobType = null;
// To remove: 		DynaValidatorForm dyForm;
		JobStatus jobStatus;
		String selectedScheduleId = null;
		String currentStatus = null;
		String jobId = null;
		String schedulerId = null;
		String hostId = null;
		String fromInput = null;

		try {
			log.debug("entering 'exec' method");
			HttpServletRequest request =  SwtUtil.getCurrentRequest();
			selectedScheduledJobType = request.getParameter("selectedScheduledJobType");
			selectedJobType = request.getParameter("selectedJobType");

// To remove: 			dyForm = (DynaValidatorForm) form;
			jobStatus = (JobStatus) getterJobStatus();
			selectedScheduleId = request.getParameter("selectedScheduleId");

			jobId = request.getParameter("selectedjobId");
			schedulerId = request.getParameter("selectedScheduleId");

			hostId = SwtUtil.getCurrentHostId();
			fromInput = request.getParameter("fromInput");

			if ((fromInput != null)
					&& fromInput.trim().equalsIgnoreCase("true")) {
				return display();
			}

			boolean systemJobFlag = isSystemJobFlag(hostId, jobId);

			if (!systemJobFlag) {
				request.setAttribute("action", "execute");
				request.setAttribute("selectedJobId", jobId);
				request.setAttribute("userId", SwtUtil.getCurrentUserId(request
						.getSession()));

				return getView("inputJobEnable");
			}

			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			Collection jobDetails = schedulerMgr.getJobStatusDetails(hostId,
					SwtUtil.getCurrentSystemFormats(request.getSession()),
					entityId, selectedJobType);
			Iterator itr = jobDetails.iterator();

			while (itr.hasNext()) {
				jobStatus = (JobStatus) itr.next();

				if (jobStatus.getScheduleId().toString().equalsIgnoreCase(selectedScheduleId)) {
					currentStatus = jobStatus.getCurrentStatus();
				}
			}

			if ((currentStatus == null) || currentStatus.equalsIgnoreCase("P")
					|| currentStatus.equals("")) {
				// Calling Quartz Batch Scheduler Method Starts Here
				SwtJobInfo swtjobinfo = new SwtJobInfo();
				swtjobinfo.setHostId(hostId);
				swtjobinfo.setJobId(jobId);
				swtjobinfo.setSchedulerId(Integer.parseInt(schedulerId));

				swtjobinfo.setUserId(SwtUtil.getCurrentUserId(request
						.getSession()));

				SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
				swtJobScheduler.runImmediate(swtjobinfo);
				ZkUtils.setSerializableProperty(ZkUtils.PROPERTY_RUN_NOW_SCHEDULER+"---"+ZkUtils.instanceUuid+"---"+schedulerId,swtjobinfo);
				// Calling Quartz Batch Scheduler Method Ends Here
				request.setAttribute("exec", "true");

				try {
					long numMillisecondsToSleep = 500; // 500 mili seconds
					Thread.sleep(numMillisecondsToSleep);
				} catch (InterruptedException e) {
					log.error(this.getClass().getName()
							+ " - Exception Catched in [exec] method : - "
							+ e.getMessage());

				}
			} else {
				request.setAttribute("exec", "false");
			}

			jobStatus.setScheduleId(Integer.parseInt(selectedScheduleId));
			jobStatus.setJobId(jobId);
			jobStatus.setHostId(hostId);
			request.setAttribute("methodName", "exec");
			request.setAttribute("selectedScheduledJobType", selectedScheduledJobType);
			request.setAttribute("selectedJobType", selectedJobType);
			request.setAttribute("scheduledJobType", request.getParameter("scheduledJobType"));
			return display();
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in SchedulerAction.'exec' method : "
					+ swtexp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					swtexp, "exec", SchedulerAction.class), request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error("SwtException Catch in SchedulerAction.'exec' method : "
					+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "exec", SchedulerAction.class), request, "");
			return getView("fail");
		}
	}

	public String displaySchedule() throws SwtException {

		ArrayList jobTypeList = null;
		Collection jobNameList = null;
		Collection reportTypeList = null;
		String selectedJobType = null;
		ArrayList<ScheduledReportType> reportTypeResult = null;
		Job job = null;
		ScheduledReportParams scheduledReportParams = null;
		Scheduler scheduler = null;
// To remove: 		DynaValidatorForm dyForm;
		String jobId = null;
		String scheduleId = null;
		String schedReportTypeParams = null;
		HttpServletRequest request =  SwtUtil.getCurrentRequest();
		try {
			log.debug("Entering 'displaySchedule' Method");
			reportTypeList = new ArrayList();
			selectedJobType = request.getParameter("selectedJobType");
// To remove: 			dyForm = (DynaValidatorForm) form;
			scheduler = (Scheduler) getScheduler();
			jobId = (String) getSelectedjobId();
			scheduleId = request.getParameter("selectedScheduleId");
			if (SwtUtil.isEmptyOrNull(scheduleId)) {
				scheduleId = scheduler.getScheduleId().toString();
			}
			/* Puts the list of entities into request object */
			putEntityListInReq(request);
			String hostId = SwtUtil.getCurrentHostId();
			Collection schedulerDetails = schedulerMgr.getSchedulerDetails(
					Integer.parseInt(scheduleId), SwtUtil.getCurrentSystemFormats(request
							.getSession()));
			Iterator itr = schedulerDetails.iterator();
			String Hrs = null;

			while (itr.hasNext()) {
				Scheduler sch = (Scheduler) itr.next();
				if (sch.getJobId().equals(jobId)) {
					if (sch.getDurationHours() != null) {
						if (sch.getDurationHours().intValue() < 10) {
							sch.setDurationHoursasString("0"
									+ sch.getDurationHours().toString());
						} else {
							sch.setDurationHoursasString(sch.getDurationHours()
									.toString());
						}
					}

					if (sch.getDurationMins() != null) {
						if (sch.getDurationMins().intValue() < 10) {
							sch.setDurationMinsasString("0"
									+ sch.getDurationMins().toString());
						} else {
							sch.setDurationMinsasString(sch.getDurationMins()
									.toString());
						}
					}

					if (sch.getDurationSecs() != null) {
						if (sch.getDurationSecs().intValue() < 10) {
							sch.setDurationSecsasString("0"
									+ sch.getDurationSecs().toString());
						} else {
							sch.setDurationSecsasString(sch.getDurationSecs()
									.toString());
						}
					}
				}
			}

			request.setAttribute("schedulerDetails", schedulerDetails);
			scheduler = null;

			if (schedulerDetails.size() > 0) {
				itr = schedulerDetails.iterator();

				while (itr.hasNext()) {
					scheduler = (Scheduler) itr.next();
				}

				setScheduler(scheduler);
			} else {
				setScheduler(new Scheduler());
			}
			String[] returnMsg = new String[1];
			if (SwtUtil.getMessage("addJob.combo.report", request).equals(selectedJobType) || SwtConstants.JOB_TYPE_REPORT.equals(selectedJobType)) {
				scheduledReportParams = scheduler.getScheduledReportParams();
				setScheduledReportParams(scheduledReportParams);
				request.setAttribute("selectedUsers", scheduledReportParams.getAccessListUsers());
				request.setAttribute("selectedRoles", scheduledReportParams.getAccessListRoles());
				request.setAttribute("selectedEmailRoles", scheduledReportParams.getEmailDistListRoles());
				request.setAttribute("selectedEmailUsers", scheduledReportParams.getEmailDistListUsers());
				request.setAttribute("parametersStatusValue", configParametersIsCorrect(scheduledReportParams.getReportConfig(), scheduledReportParams.getExecutionRole(), request, returnMsg) ? "configCorrect" : "configIncorrect");
				request.setAttribute("configIncorrectCause", returnMsg[0]);
			}
			job = scheduler.getJob();
			if (SwtConstants.JOB_TYPE_PROCESS.equals(job.getJobType())) {
				job.setJobType(SwtUtil.getMessage("addJob.combo.process", request));
			} else if (SwtConstants.JOB_TYPE_REPORT.equals(job.getJobType())) {
				job.setJobType(SwtUtil.getMessage("addJob.combo.report", request));
			}
			setJob(job);
			selectedJobType = scheduler.getJob().getJobType();

			jobNameList = schedulerMgr.getJobNameList(hostId, selectedJobType);
			request.setAttribute("jobNameList", jobNameList);

			// Retrieve available job type list
			jobTypeList = getJobTypeList(request, false);
			request.setAttribute("jobTypeList", jobTypeList);

			if (SwtUtil.isEmptyOrNull(jobId)) {
				jobId = ((LabelValueBean) jobNameList.iterator().next()).getValue();
			}

			schedReportTypeParams = schedulerMgr.getScheduledReportTypeConfig(jobId);
			request.setAttribute("schedReportTypeParams", schedReportTypeParams);


			// Retrieve report type list and put in request
			reportTypeList =  getReportTypeList(request, jobId);
			request.setAttribute("reportTypeList", reportTypeList);

			// Get role list
			putRoleListInReq(request, hostId);

			// Put output file list in request based on the selected report type
			putOutputFileListInRequest(request);

			String name = (String) request.getAttribute("methodName");

			if ((name == null) || name.equalsIgnoreCase("displaySchedule")) {
				request.setAttribute("methodName", "change");
				request.setAttribute("screenFieldsStatus", "");
			}

			request.setAttribute("selectedjobId", jobId);
			request.setAttribute("selectedScheduleId", scheduleId);
			putSystemDateInRequest(request);
			log.debug("Exiting 'displaySchedule' Method");

			return getView("add");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("SwtException Catch in SchedulerAction.'displaySchedule' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							swtexp, "displaySchedule", SchedulerAction.class), request,
					"");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error("SwtException Catch in SchedulerAction.'displaySchedule' method : "
							+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displaySchedule", SchedulerAction.class), request, "");
			return getView("fail");
		}
	}

	public String SchedulerDelete() throws SwtException {

		String fromInput = null;
// To remove: 		DynaValidatorForm dyForm;
		String scheduleId = null;
		String hostId = null;
		String jobId = null;
		Scheduler scheduler;
		JobStatus jobStatus = null;
		String currentStatus = "";
		String selectedJobType = null;
		SystemInfo systemInfo;
		ScheduledReportParams scheduledReportParams = null;
		String selectedScheduledJobType = null;
		Collection<SchedReportHist> result = null;
		SchedReportHist schedReportHist = null;
		SchedReportHistManager schedReportHistManager = null;
		HttpServletRequest request =  SwtUtil.getCurrentRequest();
		try {
			fromInput = request.getParameter("fromInput");
			scheduleId = request.getParameter("selectedScheduleId");
			selectedScheduledJobType = request.getParameter("selectedScheduledJobType");
			selectedJobType = request.getParameter("selectedJobType");
			hostId = SwtUtil.getCurrentHostId();

// To remove: 			dyForm = (DynaValidatorForm) form;
			scheduledReportParams = (ScheduledReportParams) getScheduledReportParams();
			jobId = (String) getSelectedjobId();
			scheduler = (Scheduler) getScheduler();
			scheduler.setScheduleId(Integer.parseInt(scheduleId));
			scheduler.setHostId(hostId);
			scheduler.setJobId(jobId);

			systemInfo = new SystemInfo();
			if (SwtConstants.JOB_TYPE_REPORT.equals(selectedJobType)) {
				scheduledReportParams.setHostId(hostId);
				scheduledReportParams.setScheduleId(Integer.parseInt(scheduleId));
				scheduledReportParams.setJobId(jobId);
				scheduler.setScheduledReportParams(scheduledReportParams);
			}

			if ((fromInput != null)
					&& fromInput.trim().equalsIgnoreCase("true")) {
				return display();
			}

			boolean systemJobFlag = isSystemJobFlag(hostId, jobId);

			if (!systemJobFlag) {
				request.setAttribute("action", "remove");
				request.setAttribute("selectedJobId", jobId);
				request.setAttribute("userId", SwtUtil.getCurrentUserId(request
						.getSession()));

				return getView("inputJobEnable");
			}

			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			Collection jobDetails = schedulerMgr.getJobStatusDetails(hostId,
					SwtUtil.getCurrentSystemFormats(request.getSession()),
					entityId, selectedJobType);
			Iterator itr = jobDetails.iterator();

			while (itr.hasNext()) {
				jobStatus = (JobStatus) itr.next();

				if (jobStatus.getScheduleId().toString().equalsIgnoreCase(scheduleId)) {
					currentStatus = jobStatus.getCurrentStatus();
				}
			}

			if ((currentStatus == null)
					|| currentStatus.equalsIgnoreCase("P")
					|| currentStatus
					.equalsIgnoreCase(SwtConstants.ADDJOB_DISABLE)
					|| currentStatus.equalsIgnoreCase("")) {


				schedReportHistManager = (SchedReportHistManager) SwtUtil.getBean("schedReportHistManager");
				//delete reports of the job too
				result = schedReportHistManager.getSchedReportHistListJob(scheduleId);

				if (result != null) {
					Iterator itrr = result.iterator();
					while (itrr.hasNext()) {
						log.debug("Entering into the iterator loop for delete SchedReportHist");
						schedReportHist = (SchedReportHist) (itrr.next());
						String pathfile=schedReportHist.getOutputLocation()+"/"+schedReportHist.getFileName();
						File file = new File(pathfile);
						if (file.exists()) {
							file.delete();
						}
						schedReportHistManager.deleteSchedReportHist(schedReportHist);
					}
				}

				schedulerMgr.deleteSchedulerDetail(scheduler, systemInfo,
						SwtUtil.getCurrentSystemFormats(request.getSession()));

				SwtJobInfo swtjobinfo = new SwtJobInfo();
				swtjobinfo.setHostId(hostId);
				swtjobinfo.setJobId(jobId);
				swtjobinfo.setSchedulerId(Integer.parseInt(scheduleId));

				SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
				swtJobScheduler.killJob(swtjobinfo, "D");
				SwtUtil.removeJobFromList(scheduleId);
				ZkUtils.setSerializableProperty(ZkUtils.PROPERTY_DELETE_SCHEDULER+"---"+ZkUtils.instanceUuid+"---"+scheduler.getScheduleId(),swtjobinfo);

				if (jobId.equals(SwtConstants.INTERFACE_NOTIFICATION_JOBID) || jobId.equals(SwtConstants.PCM_INTERFACE_NOTIFICATION_JOBID)) {
					schedulerMgr.deleteNotifications(jobId);
				}
			} else {
				request.setAttribute("delete", "false");
			}

			request.setAttribute("methodName", "SchedulerDelete");
			request.setAttribute("selectedScheduledJobType", selectedScheduledJobType);
			request.setAttribute("selectedJobType", selectedJobType);
			request.setAttribute("scheduledJobType", request.getParameter("scheduledJobType"));
			log.debug("exiting 'SchedulerDelete' method=======>");
			return display();
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SchedulerAction.'displaySchedule' method : "
							+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "true");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return jobDisplay();
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Exception Catch in SchedulerAction.'displaySchedule' method : "
					+ e.getMessage());

			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "SchedulerDelete",
							SchedulerAction.class), request, ""));
			return getView("fail");
		}

	}

	/**
	 * This is used to add the scheduler
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String Scheduleradd()
			throws SwtException {

		// Declared to hold errors
		ActionErrors errors = null;
// To remove: 		// declaration for DynaValidatorForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Declared to hold the Schedulerobject
		Scheduler scheduler = null;
		Job job = null;
		ScheduledReportParams scheduledReportParams = null;
		// Declared to hold the jobId
		String jobId = null;
		// Declared to hold the hostId
		String hostId = null;
		// Declared to hold the fromInput
		String fromInput = null;
		// Declared to hold systemJobFlag
		boolean systemJobFlag = false;
		String jobType = null;
		HashMap<String, String> schedulerConfig = null;
		String schedulerConfigXML = null;
		String selectedUsers = null;
		String selectedRoles = null;
		String selectedEmailUsers = null;
		String selectedEmailRoles = null;
		String runDateWorkdays = "N";
		HttpServletRequest request =  SwtUtil.getCurrentRequest();
		try {
			log.debug(this.getClass().getName()
					+ "- [Scheduleradd] - Entering ");
			// get Dyna validator Form
// To remove: 			dyForm = (DynaValidatorForm) form;
			job = (Job) getJob();
			jobType = job.getJobType();
			// get scheduler object from dynaform
			scheduler = (Scheduler) getScheduler();
			scheduledReportParams = (ScheduledReportParams) getScheduledReportParams();
			errors = new ActionErrors();
			fromInput = request.getParameter("fromInput");
			schedulerConfigXML = request.getParameter("schedulerConfigXML");
			/* Condition to check the request value */
			if ((fromInput != null)
					&& fromInput.trim().equalsIgnoreCase("true")) {
				request.setAttribute("methodName", "displaySchedule");
				request.setAttribute("parentFormRefresh", "yes");

				return displaySchedule();
			}
			/* Read host id from swtutil file */
			hostId = SwtUtil.getCurrentHostId();
			jobId = (String) getSelectedjobId();
			/* Getting & setting host id using bean class */
			scheduler.setHostId(hostId);
			/* Getting & setting job id using bean class */
			scheduler.setJobId(jobId);
			/* Condition to check hours,seconds and minutes value */
			if (!SwtUtil.isEmptyOrNull(scheduler.getDurationHoursasString())) {
				scheduler.setDurationHours(new Integer(scheduler
						.getDurationHoursasString()));
			}

			if (!SwtUtil.isEmptyOrNull(scheduler.getDurationSecsasString())) {
				scheduler.setDurationSecs(new Integer(scheduler
						.getDurationSecsasString()));
			}

			if (!SwtUtil.isEmptyOrNull(scheduler.getDurationMinsasString())) {
				scheduler.setDurationMins(new Integer(scheduler
						.getDurationMinsasString()));
			}
			/* Setting start and end date using bean class */
			scheduler.setStartingDate(new Date());
			scheduler.setEndingDate(new Date());
			/* Checking system job flag value based on host id and job id */
			systemJobFlag = isSystemJobFlag(hostId, jobId);
			// Retrieve the value of runDateWorkdays from the request
			runDateWorkdays = request.getParameter("scheduledReportParams.runDateWorkdays");
			// Check if the value is not "Y"
			if (runDateWorkdays == null || !runDateWorkdays.equals("Y")) {
				runDateWorkdays = "N"; // Set to "N" if not "Y"
			}
			// Set the value in the scheduledReportParams object
			scheduledReportParams.setRunDateWorkdays(runDateWorkdays);

			scheduler.setUpdateUserNeedUpdated(systemJobFlag);
			if (SwtConstants.JOB_TYPE_REPORT.equals(jobType) || SwtUtil.getMessage("addJob.combo.report", request).equals(jobType)) {
				selectedUsers = request.getParameter("selectedUsers");
				selectedRoles = request.getParameter("selectedRoles");
				selectedEmailUsers = request.getParameter("selectedEmailUsers");
				selectedEmailRoles = request.getParameter("selectedEmailRoles");
				scheduledReportParams.setAccessListUsers(selectedUsers);
				scheduledReportParams.setAccessListRoles(selectedRoles);
				scheduledReportParams.setEmailDistListUsers(selectedEmailUsers);
				scheduledReportParams.setEmailDistListRoles(selectedEmailRoles);
				scheduledReportParams.setReportConfig(schedulerConfigXML);
				scheduler.setScheduledReportParams(scheduledReportParams);
				request.setAttribute("selectedUsers", selectedUsers);
				request.setAttribute("selectedRoles", selectedRoles);
				request.setAttribute("selectedEmailUsers", selectedEmailUsers);
				request.setAttribute("selectedEmailRoles", selectedEmailRoles);
				request.setAttribute("schedulerConfigXML", schedulerConfigXML);
				String[] returnMsg = new String[1];
				request.setAttribute("parametersStatusValue", configParametersIsCorrect(scheduledReportParams.getReportConfig(), scheduledReportParams.getExecutionRole(), request, returnMsg) ? "configCorrect" : "configIncorrect");
				request.setAttribute("configIncorrectCause", returnMsg[0]);

			}
			/* Save the scheduler details by calling manager classs */
			schedulerMgr.saveSchedulerDetail(scheduler, SwtUtil
					.getCurrentSystemFormats(request.getSession()));

			request.setAttribute("scheduledJobType", request.getParameter("scheduledJobType"));
			if (systemJobFlag) {
				request.setAttribute("parentFormRefresh", "yes");
				request.setAttribute("methodName", "add");
				request.setAttribute("selectedjobId", "0");
				request.setAttribute("jobTypeList", new ArrayList());
				request.setAttribute("reportTypeList", new ArrayList());
				request.setAttribute("outputFileList", new ArrayList());
				request.setAttribute("roleList", new ArrayList());
				setJob(job);
				setScheduler(scheduler);
				setScheduledReportParams(scheduledReportParams);
				return displaySchedule();
			} else {
				request.setAttribute("action", "schedule");
				request.setAttribute("selectedJobId", jobId);
				request.setAttribute("userId", SwtUtil.getCurrentUserId(request
						.getSession()));

				return getView("inputJobEnable");
			}

		} catch (SwtException swtexp) {
			if(!SwtErrorHandler.JOB_SAME_LOCATION_ERROR.equals(swtexp.getErrorCode()) && !SwtErrorHandler.JOB_SAME_NAME_ERROR.equals(swtexp.getErrorCode())) {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [Scheduleradd] method : - "
						+ swtexp.getMessage());
			}
			request.setAttribute("methodName", "add");
			request.setAttribute("parentFormRefresh", "no");
			request.setAttribute("screenFieldsStatus", "");
			request.setAttribute("selectedjobId", jobId);
			if (scheduler != null) {
				setScheduler(scheduler);
			}

			ArrayList jobTypeList = null;
			Collection jobNameList = null;
			Collection reportTypeList = new ArrayList();
			String selectedJobId = null;
			selectedJobId = jobId;
			ArrayList<ScheduledReportType> reportTypeResult = null;
			String schedReportTypeParams = null;

			// Retrieve available job type list
			jobTypeList = getJobTypeList(request, false);
			request.setAttribute("jobTypeList", jobTypeList);

			// Retrieve available job name
			if (SwtUtil.isEmptyOrNull(jobType) && jobTypeList != null) {
				jobType = ((LabelValueBean) jobTypeList.get(0)).getValue();
			}
			if (SwtUtil.getMessage("addJob.combo.report", request).equals(job.getJobType())) {
				jobType = SwtConstants.JOB_TYPE_REPORT;
			}
			jobNameList = schedulerMgr.getJobNameList(hostId, jobType);
			request.setAttribute("jobNameList", jobNameList);

			if (SwtUtil.isEmptyOrNull(selectedJobId)) {
				selectedJobId = ((LabelValueBean) jobNameList.iterator().next()).getValue();
			}
			// Retrieve report type list and put in request
			reportTypeList =  getReportTypeList(request, selectedJobId);
			request.setAttribute("reportTypeList", reportTypeList);

			String selectedJobType = request.getParameter("scheduledJobType");
			if (SwtUtil.getMessage("addJob.combo.report", request).equals(selectedJobType) || SwtConstants.JOB_TYPE_REPORT.equals(selectedJobType)) {
				request.setAttribute("scheduledJobType", "Report");
			}else {
				request.setAttribute("scheduledJobType", "P");
			}
			request.setAttribute("reportTypePreviousSelectedIndex", request.getParameter("reportTypePreviousSelectedIndex"));

			// Get role list
			putRoleListInReq(request, hostId);

			schedReportTypeParams = schedulerMgr.getScheduledReportTypeConfig(selectedJobId);
			request.setAttribute("schedReportTypeParams", schedReportTypeParams);

			// Put output file list in request based on the selected report type
			putOutputFileListInRequest(request);
			putSystemDateInRequest(request);
			if (swtexp.getErrorCode().equals("errors.DataIntegrityViolationExceptioninAdd")
					|| swtexp.getErrorCode().equals("errors.CouldNotSaveJobReportWithSameNameExceptioninAdd")
					|| swtexp.getErrorCode().equals("errors.CouldNotSaveJobReportWithSameLocationPrefixExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			setJob(job);
			setScheduler(scheduler);
			setScheduledReportParams(scheduledReportParams);
			return getView("add");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [Scheduleradd] method : - "
					+ e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "Scheduleradd",
							SchedulerAction.class), request, ""));
			return getView("fail");
		} finally {
			// nullify
			scheduler = null;
			jobId = null;
			hostId = null;
			fromInput = null;
			log
					.debug(this.getClass().getName()
							+ "- [Scheduleradd] - Exiting ");

		}
	}

	public String schedulerChange() throws SwtException {

		ActionErrors errors = null;
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm) form;
		Scheduler scheduler = null;
		ScheduledReportParams scheduledReportParams = null;
		Integer scheduleId;
		String jobId = "";
		Job job = null;
		String selectedUsers = null;
		String selectedRoles = null;
		String selectedEmailUsers = null;
		String selectedEmailRoles = null;
		String hostId = null;
		String jobType = null;
		String schedulerConfigXML = null;
		String runDateWorkdays = "N";
		boolean runAfterChange = true;
		HttpServletRequest request =  SwtUtil.getCurrentRequest();
		try {
			String Hrs = null;
			String Min = null;
			String Sec = null;
			log.debug("entering 'schedulerChange' method");
			hostId = SwtUtil.getCurrentHostId();
			job = (Job) getJob();
			scheduler = (Scheduler) getScheduler();
			jobId = (String) getSelectedjobId();
			scheduleId = scheduler.getScheduleId();
			scheduler.setHostId(hostId);
			scheduler.setJobId(jobId);

			jobType = SwtConstants.JOB_TYPE_PROCESS;
			if (SwtUtil.getMessage("addJob.combo.report", request).equals(job.getJobType())) {
				jobType = SwtConstants.JOB_TYPE_REPORT;
				scheduledReportParams = (ScheduledReportParams) getScheduledReportParams();
				selectedUsers = request.getParameter("selectedUsers");
				selectedRoles = request.getParameter("selectedRoles");
				selectedEmailUsers = request.getParameter("selectedEmailUsers");
				selectedEmailRoles = request.getParameter("selectedEmailRoles");
				schedulerConfigXML = request.getParameter("schedulerConfigXML");
				runDateWorkdays = request.getParameter("runDateWorkdays");
				// Retrieve the value of runDateWorkdays from the request
				runDateWorkdays = request.getParameter("scheduledReportParams.runDateWorkdays");
				// Check if the value is not "Y"
				if (runDateWorkdays == null || !runDateWorkdays.equals("Y")) {
					runDateWorkdays = "N"; // Set to "N" if not "Y"
				}
				// Set the value in the scheduledReportParams object
				scheduledReportParams.setRunDateWorkdays(runDateWorkdays);
				scheduledReportParams.setAccessListUsers(selectedUsers);
				scheduledReportParams.setAccessListRoles(selectedRoles);
				scheduledReportParams.setEmailDistListUsers(selectedEmailUsers);
				scheduledReportParams.setEmailDistListRoles(selectedEmailRoles);
				scheduledReportParams.setHostId(hostId);
				scheduledReportParams.setJobId(jobId);
				scheduledReportParams.setScheduleId(scheduleId);
				scheduledReportParams.setReportConfig(schedulerConfigXML);
				scheduledReportParams.setRunDateWorkdays(runDateWorkdays);
				scheduler.setScheduledReportParams(scheduledReportParams);
				String[] returnMsg = new String[1];
				request.setAttribute("parametersStatusValue", configParametersIsCorrect(schedulerConfigXML, scheduledReportParams.getExecutionRole(), request, returnMsg) ? "configCorrect" : "configIncorrect");
				request.setAttribute("selectedUsers", selectedUsers);
				request.setAttribute("selectedRoles", selectedRoles);
				request.setAttribute("selectedEmailUsers", selectedEmailUsers);
				request.setAttribute("selectedEmailRoles", selectedEmailRoles);
				request.setAttribute("schedulerConfigXML", schedulerConfigXML);
			}
			runAfterChange = "true".equalsIgnoreCase(request.getParameter("changeAndRunNow"));
			scheduler.setRunAfterChange(runAfterChange);
			String fromInput = request.getParameter("fromInput");
			String selectedJobType = request.getParameter("scheduledJobType");
			if (SwtUtil.getMessage("addJob.combo.report", request).equals(selectedJobType) || SwtConstants.JOB_TYPE_REPORT.equals(selectedJobType)) {
				request.setAttribute("scheduledJobType", "R");
			}else {
				request.setAttribute("scheduledJobType", "P");
			}

			if ((fromInput != null)
					&& fromInput.trim().equalsIgnoreCase("true")) {
				request.setAttribute("methodName", "displaySchedule");
				request.setAttribute("parentFormRefresh", "yes");

				return displaySchedule();
			}

			SystemInfo systemInfo = new SystemInfo();

			/*
			 * Setting the original starting and ending date in the scheduler
			 * object
			 */
			Collection schedulerDetails = schedulerMgr.getSchedulerDetails(
					scheduleId, SwtUtil.getCurrentSystemFormats(request
							.getSession()));

			Scheduler schedulerRec = null;

			if (schedulerDetails != null) {
				Iterator itr = schedulerDetails.iterator();

				while (itr.hasNext()) {
					log.debug("Entering into the iterator loop");
					schedulerRec = (Scheduler) (itr.next());

					break;
				}
			}

			if (schedulerRec != null) {
				scheduler.setStartingDate(schedulerRec.getStartingDate());
				scheduler.setStartingTime(schedulerRec.getStartingTime());
				scheduler.setEndingDate(schedulerRec.getEndingDate());
				scheduler.setEndingTime(schedulerRec.getEndingTime());
			}

			Hrs = scheduler.getDurationHoursasString();
			Min = scheduler.getDurationMinsasString();
			Sec = scheduler.getDurationSecsasString();

			if (!SwtUtil.isEmptyOrNull(Hrs)) {
				scheduler.setDurationHours(new Integer(scheduler
						.getDurationHoursasString()));
			}


			if (!SwtUtil.isEmptyOrNull(Min)) {
				scheduler.setDurationMins(new Integer(scheduler
						.getDurationMinsasString()));
			}

			if (!SwtUtil.isEmptyOrNull(Sec)) {
				scheduler.setDurationSecs(new Integer(scheduler
						.getDurationSecsasString()));
			}
			boolean systemJobFlag = isSystemJobFlag(hostId, jobId);

			schedulerMgr.updateSchedulerDetail(scheduler, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()),
					systemJobFlag);

			if (!systemJobFlag) {
				request.setAttribute("action", "change");
				request.setAttribute("selectedJobId", jobId);
				request.setAttribute("userId", SwtUtil.getCurrentUserId(request
						.getSession()));

				return getView("inputJobEnable");
			} else {
				request.setAttribute("screenFieldsStatus", "true");
				request.setAttribute("methodName", "change");
				log.debug("exiting 'schedulerChange' method");
				request.setAttribute("parentFormRefresh", "yes");
				setJob(job);
				setScheduler(scheduler);
				setScheduledReportParams(scheduledReportParams);
				return displaySchedule();
			}

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			if(!SwtErrorHandler.JOB_SAME_LOCATION_ERROR.equals(swtexp.getErrorCode()) && !SwtErrorHandler.JOB_SAME_NAME_ERROR.equals(swtexp.getErrorCode())) {
				log.error("SwtException Catch in SchedulerAction.'schedulerChange' method : " + swtexp.getMessage());
			}
			ArrayList jobTypeList = null;
			Collection jobNameList = null;
			Collection reportTypeList = new ArrayList();
			String selectedJobId = null;
			String schedReportTypeParams = null;

			// Retrieve available job type list
			jobTypeList = getJobTypeList(request, false);
			request.setAttribute("jobTypeList", jobTypeList);

			// Retrieve available job name
			if (SwtUtil.isEmptyOrNull(jobType) && jobTypeList != null) {
				jobType = ((LabelValueBean) jobTypeList.get(0)).getValue();
			}
			jobNameList = schedulerMgr.getJobNameList(hostId, jobType);
			request.setAttribute("jobNameList", jobNameList);

			if (SwtUtil.isEmptyOrNull(selectedJobId)) {
				selectedJobId = ((LabelValueBean) jobNameList.iterator().next()).getValue();
			}

			// Retrieve report type list and put in request
			reportTypeList =  getReportTypeList(request, selectedJobId);
			request.setAttribute("reportTypeList", reportTypeList);

			// Get role list
			putRoleListInReq(request, hostId);

			// Put output file list in request based on the selected report type
			putOutputFileListInRequest(request);
			/* Puts the list of entities into request object */
			putEntityListInReq(request);

			schedReportTypeParams = schedulerMgr.getScheduledReportTypeConfig(selectedJobId);
			request.setAttribute("schedReportTypeParams", schedReportTypeParams);

			if (swtexp.getErrorCode().equals(
					"errors.CouldNotSaveJobReportWithSameNameExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);
			}

			request.setAttribute("parentFormRefresh", "no");
			request.setAttribute("methodName", "change");
			request.setAttribute("screenFieldsStatus", "");
			request.setAttribute("selectedjobId", jobId);
			setJob(job);
			setScheduler(scheduler);
			setScheduledReportParams(scheduledReportParams);
			putSystemDateInRequest(request);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("add");
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error("Exception Catch in SchedulerAction.'schedulerChange' method : "
							+ e.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [schedulerChange] method : - "
					+ e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "schedulerChange",
							SchedulerAction.class), request, ""));

			return getView("fail");
		}
	}

	public String jobDisplay()
			throws SwtException {
		try {
			log.debug("entering 'jobDisplay' method");
			HttpServletRequest request =  SwtUtil.getCurrentRequest();
			String hostId = SwtUtil.getCurrentHostId();
			Collection jobNameList = schedulerMgr.getJobNames(hostId);
			request.setAttribute("jobNameList", jobNameList);
			log.debug("Exiting 'jobDisplay' Method");

			return getView("jobDisplay");
		} catch (SwtException e) {
			log
					.error("swtException Catch in SchedulerAction.'jobDisplay' method : "
							+ e.getMessage());

			SystemExceptionHandler.logError(e);
			SwtUtil.logException(e, request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("Exception Catch in SchedulerAction.'jobDisplay' method : "
							+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "jobDisplay", SchedulerAction.class), request, "");
			return getView("fail");
		}
	}

	public String Schedulerview()
			throws SwtException {
		Integer scheduleId;
		HttpServletRequest request =  SwtUtil.getCurrentRequest();
		try {
			log.debug("entering 'Schedulerview' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			Scheduler scheduler = (Scheduler) getScheduler();
			scheduleId = scheduler.getScheduleId();
			Job job = (Job) getJob();
			String jobId = (String) getSelectedjobId();
			String hostId = SwtUtil.getCurrentHostId();
			Collection schedulerDetails = schedulerMgr.getSchedulerDetails(
					scheduleId, SwtUtil.getCurrentSystemFormats(request
							.getSession()));
			Iterator itr = schedulerDetails.iterator();
			String Hrs = null;

			while (itr.hasNext()) {
				Scheduler sch = (Scheduler) itr.next();

				if (sch.getJobId().equals(jobId)) {
					if (sch.getDurationHours() != null) {
						if (sch.getDurationHours().intValue() < 10) {
							sch.setDurationHoursasString("0"
									+ sch.getDurationHours().toString());
						} else {
							sch.setDurationHoursasString(sch.getDurationHours()
									.toString());
						}
					}

					if (sch.getDurationMins() != null) {
						if (sch.getDurationMins().intValue() < 10) {
							sch.setDurationMinsasString("0"
									+ sch.getDurationMins().toString());
						} else {
							sch.setDurationMinsasString(sch.getDurationMins()
									.toString());
						}
					}

					if (sch.getDurationSecs() != null) {
						if (sch.getDurationSecs().intValue() < 10) {
							sch.setDurationSecsasString("0"
									+ sch.getDurationSecs().toString());
						} else {
							sch.setDurationSecsasString(sch.getDurationSecs()
									.toString());
						}
					}
				}
			}

			request.setAttribute("schedulerDetails", schedulerDetails);
			scheduler = null;

			if (schedulerDetails.size() > 0) {
				itr = schedulerDetails.iterator();

				while (itr.hasNext()) {
					scheduler = (Scheduler) itr.next();
				}

				setScheduler(scheduler);
			} else {
				setScheduler(new Scheduler());
				request.setAttribute("jobNameList", new ArrayList());
			}

			request.setAttribute("methodName", "view");
			request.setAttribute("screenFieldsStatus", "true");

			log.debug("Exiting 'Schedulerview' Method");

			return getView("Schedulerview");
		} catch (SwtException e) {
			log
					.error("swtException Catch in SchedulerAction.'Schedulerview' method : "
							+ e.getMessage());

			SwtUtil.logException(e, request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("Exception Catch in SchedulerAction.'Schedulerview' method : "
							+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "Schedulerview", SchedulerAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method allows user to directly enable or disable a job from the main
	 * batch scheduler screen without going to change-job screen.
	 *
	 * @return
	 * @throws Exception
	 */
	public String enableOrDisableJob() throws SwtException {

		String fromInput = null;
		InterfaceInterruptDAO interfaceInterruptDAO;
		String jobId = null;
		String hostId = null;
		String enableOrDisableFlag = null;
		Integer scheduleId;
		SystemInfo systemInfo;
		Scheduler schedulerRec = null;
		HttpServletRequest request =  SwtUtil.getCurrentRequest();
		try {
			log.debug("entering 'enableOrDisableJob' method");
			fromInput = request.getParameter("fromInput");
			// removing the Notification scheduler.
			if ((fromInput != null) && fromInput.trim().equalsIgnoreCase("true")) {
				request.setAttribute("refreshPage", "true");

				return display();
			}

			systemInfo = new SystemInfo();
			jobId = request.getParameter("selectedjobId");
			scheduleId = Integer.parseInt(request.getParameter("selectedScheduleId"));
			hostId = SwtUtil.getCurrentHostId();
			enableOrDisableFlag = request
					.getParameter("enableOrDisable");
			Collection schedulerDetails = schedulerMgr.getSchedulerDetails(
					scheduleId, SwtUtil.getCurrentSystemFormats(request
							.getSession()));


			if (schedulerDetails != null) {
				Iterator itr = schedulerDetails.iterator();

				while (itr.hasNext()) {
					schedulerRec = (Scheduler) (itr.next());

					break;
				}
			}

			if (schedulerRec != null) {
				if (enableOrDisableFlag.equals("Enable")) {

					schedulerRec.setJobStatus(SwtConstants.ADDJOB_ENABLE);
				} else if (enableOrDisableFlag.equals("Disable")) {
					if(jobId.equals(ILMProcess.ILM_JOB_ID) ){
						ILMProcess.cancelILMJobs();
					} else if(jobId.equals(SwtConstants.INTERFACE_NOTIFICATION_JOBID)){
						// If the selected job is for interface notification, then
						// delete and the interface notifications
						interfaceInterruptDAO = (InterfaceInterruptDAO) SwtUtil
								.getBean("interfaceInterruptDAO");
						interfaceInterruptDAO
								.deleteNotificationDisable(SwtConstants.INTERFACE_NOTIFICATION_TYPE, false);
						interfaceInterruptDAO
								.deleteNotificationDisable(SwtConstants.MESSAGE_NOTIFICATION_TYPE, false);
					} else if(jobId.equals(SwtConstants.PCM_INTERFACE_NOTIFICATION_JOBID)){
						// If the selected job is for interface notification, then
						// delete and the interface notifications
						interfaceInterruptDAO = (InterfaceInterruptDAO) SwtUtil
								.getBean("interfaceInterruptDAOPCM");
						interfaceInterruptDAO
								.deleteNotificationDisable(SwtConstants.INTERFACE_NOTIFICATION_TYPE, true);
						interfaceInterruptDAO
								.deleteNotificationDisable(SwtConstants.MESSAGE_NOTIFICATION_TYPE, true);
					}
					schedulerRec.setJobStatus("D");
				}
			}

			boolean systemJobFlag = isSystemJobFlag(hostId, jobId);
			schedulerMgr.updateSchedulerDetail(schedulerRec, systemInfo,
					SwtUtil.getCurrentSystemFormats(request.getSession()),
					systemJobFlag);

			if (systemJobFlag) {
				return display();
			} else {
				JobStatus jobStatus = schedulerMgr.getJobStatus(scheduleId);

				if (enableOrDisableFlag.equals("Enable")) {
					jobStatus.setJobStatus("P");
					schedulerMgr.updateJobStatus(jobStatus);
				} else if (enableOrDisableFlag.equals("Disable")) {

					jobStatus.setJobStatus("D");
					schedulerMgr.updateJobStatus(jobStatus);
				}

				request.setAttribute("action", enableOrDisableFlag);
				request.setAttribute("selectedJobId", jobId);
				request.setAttribute("userId", SwtUtil.getCurrentUserId(request
						.getSession()));

				return getView("inputJobEnable");
			}

		} catch (SwtException swtexp) {
			log
					.error("swtException Catch in SchedulerAction.'enableOrDisableJob' method : "
							+ swtexp.getMessage());
			request.setAttribute("selectedjobId", jobId);
			putSystemDateInRequest(request);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return display();
		} catch (Exception e) {
			log
					.error("Exception Catch in SchedulerAction.'enableOrDisableJob' method : "
							+ e.getMessage());

			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "enableOrDisableJob",
							SchedulerAction.class), request, ""));
			return display();
		}
	}

	/*
	 * Added for Refreshing the batch scheduler window , is job status or next
	 * time changed in backend
	 */
	public String getJobStatus() throws SwtException {
		log.debug("entering 'getJobStatus' method ");
		Integer scheduleId = null;
		Date lastExecTimeFromRequest;
		String jobStatus = "0"; // Corrected spelling
		String hostId = SwtUtil.getCurrentHostId();
		HttpServletRequest request =  SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String jobId = request.getParameter("jobId");
		if (!SwtUtil.isEmptyOrNull(request.getParameter("scheduleId"))) {
			scheduleId = Integer.parseInt(request.getParameter("scheduleId"));
		}
		String lastExecTimeFromRequestAsString = request.getParameter("LastExecTime");
		JobStatus status = schedulerMgr.getJobStatus(scheduleId);
		if ((hostId != null) && (scheduleId != null) && (lastExecTimeFromRequestAsString != null)) {
			if (lastExecTimeFromRequestAsString.trim().length() > 10) {
				SimpleDateFormat formatter = new SimpleDateFormat(SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss");
				try {
					lastExecTimeFromRequest = formatter.parse(lastExecTimeFromRequestAsString);
				} catch (ParseException e) {
					log.error("Error parsing date", e); // Handle the exception
					lastExecTimeFromRequest = null;
				}

				if ((status != null) && (status.getLastExecTime() != null)) {
					Date lastExecTime = status.getLastExecTime();
					try {
						lastExecTime = formatter.parse(formatter.format(lastExecTime));
					} catch (ParseException e) {
						log.error("Error parsing date", e); // Handle the exception
					}

					try {
						if (lastExecTimeFromRequest != null && (lastExecTimeFromRequest.after(lastExecTime) || lastExecTimeFromRequest.equals(lastExecTime))) {
							response.getWriter().print("@true");
							return null;
						}
					} catch (Exception exp) {
						exp.printStackTrace(); // Log the exception or handle it as needed
					}
				}
			}

			if ((status != null) && (status.getCurrentStatus() != null)) {
				jobStatus = status.getCurrentStatus();
				String entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				Collection coll = (Collection) CacheManager.getInstance().getMiscParams("CURRENTSTATUS", entityId);

				Iterator itr1 = coll.iterator();

				while (itr1.hasNext()) {
					MiscParams mp = (MiscParams) (itr1.next());
					String temp = mp.getId().getKey2();

					if (jobStatus.equals(mp.getId().getKey2())) {
						jobStatus = mp.getParValue();
					}
				}
			} else {
				jobStatus = "@false";
			}
		}
		try {
			response.getWriter().print(jobStatus);
		} catch (Exception exp) {
			exp.printStackTrace(); // Handle the IOException
		}
		log.debug("exiting 'getJobStatus' method");

		return null;
	}

	private boolean isSystemJobFlag(String hostId, String jobId)
			throws SwtException {
		boolean systemJobFlag = true;
		log.debug("Entering in the 'isSystemJobFlag' method with hostid = "
				+ hostId + ",jobid " + jobId);

		if (jobId != null) {
			Job tempjob = schedulerMgr.getJobDetail(hostId, jobId);

			if (!((tempjob != null) && (tempjob.getSystemJob() != null) && tempjob
					.getSystemJob().equalsIgnoreCase("Y"))) {
				systemJobFlag = false;
			}
		}

		log.debug("Returning from 'isSystemJobFlag' method with result -->"
				+ systemJobFlag);

		return systemJobFlag;
	}

	/**
	 * @param request
	 * @throws SwtException
	 */
	private void putRoleListInReq(HttpServletRequest request, String hostId) throws SwtException {
		UserMaintenanceManager usermaintenanceManager=(UserMaintenanceManager)(SwtUtil.getBean("usermaintenanceManager"));
		//getting  roleId and roleName from Role table
		Collection roleList= usermaintenanceManager.getRoleList(hostId);
		request.setAttribute("roleList", roleList);
	}

	/**
	 * @param request
	 * @throws SwtException
	 */
	private void putUserListInReq(HttpServletRequest request, String hostId, String userList) throws SwtException {
		Collection collUser = schedulerMgr.getUserList(hostId, userList);
		request.setAttribute("usersList", collUser);
	}

	/**
	 * This method is used to display the access list.
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String displayAccessList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String hostId = null;

		try {
			log.debug(this.getClass().getName() + " - [displayAccessList] - "
					+ "Entry");
			hostId = SwtUtil.getCurrentHostId();

			Collection collUser = schedulerMgr.getRoleList(hostId, null);
			request.setAttribute("roleList", collUser);
			putUserListInReq(request, hostId, null);

			log.debug(this.getClass().getName() + " - [displayAccessList] - "
					+ "Exit");
			return getView("accessList");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAccessList] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance()
					.handleException(exp, "displayAccessList",
							ScenMaintenanceAction.class), request, "");
			return getView("fail");

		}
	}
	/**
	 * This method is used to display the distribution list.
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String displayDistList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String hostId = null;
		String selectedUsers = null;
		String selectedRoles = null;
		String fromSchedReportHist = null;
		String scheduleId = null;
		String filePath = null;
		String fileId = null;
		Collection collRoles;

		try {
			log.debug(this.getClass().getName() + " - [displayDistList] - " + "Entry");
			hostId = SwtUtil.getCurrentHostId();
			selectedUsers = request.getParameter("selectedUsers");
			selectedRoles = request.getParameter("selectedRoles");
			fromSchedReportHist = request.getParameter("fromSchedReportHist");
			scheduleId = request.getParameter("scheduleId");
			filePath = request.getParameter("filePath");
			fileId = request.getParameter("fileId");

			if(!SwtUtil.isEmptyOrNull(fromSchedReportHist) && "true".equals(fromSchedReportHist)) {
				if (!SwtUtil.isEmptyOrNull(scheduleId)) {
					Collection schedulerDetails = schedulerMgr.getSchedulerDetails(
							Integer.parseInt(scheduleId), SwtUtil.getCurrentSystemFormats(request
									.getSession()));

					Scheduler schedulerRec = null;
					if (schedulerDetails != null) {
						Iterator itr = schedulerDetails.iterator();

						while (itr.hasNext()) {
							schedulerRec = (Scheduler) (itr.next());

							break;
						}
					}
					// Check if schedulerRec and its parameters are not null
					if (schedulerRec != null && schedulerRec.getScheduledReportParams() != null) {
						selectedRoles = schedulerRec.getScheduledReportParams().getEmailDistListRoles();
						selectedUsers = schedulerRec.getScheduledReportParams().getEmailDistListUsers();
					} else {
						// Log a warning if schedulerRec or its Scheduled Report Params are null
						log.warn("schedulerRec or its Scheduled Report Params are null. Unable to retrieve Email Distribution List Roles or Users.");
					}
				}
			}

			if (!SwtUtil.isEmptyOrNull(selectedRoles)) {
				collRoles = schedulerMgr.getRoleList(hostId, selectedRoles);
			} else {
				collRoles = new ArrayList();
			}
			request.setAttribute("roleList", collRoles);
			request.setAttribute("fromSchedReportHist", fromSchedReportHist);
			request.setAttribute("filePath", filePath);
			request.setAttribute("fileId", fileId);
			if (!SwtUtil.isEmptyOrNull(selectedUsers)) {
				putUserListInReq(request, hostId, selectedUsers);
			} else {
				request.setAttribute("usersList", new ArrayList());
			}

			log.debug(this.getClass().getName() + " - [displayDistList] - " + "Exit");
			return getView("distList");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayDistList] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance()
					.handleException(exp, "displayAccessList",
							ScenMaintenanceAction.class), request, "");
			return getView("fail");

		}
	}
	public boolean configParametersIsCorrect(String reportConfig, String roleId, HttpServletRequest request, String[] returnMsg) {

		String entityId = null;
		String currencyId = null;
		String ilmScenarioId = null;
		String ilmGroupId = null;
		XStream xStream;
		HashMap<String, String> schedulerConfigMap;
		ILMAnalysisMonitorManager ilmAnalysisMonitorManager;
		String isAllEntityAvailable = null;
		String hostId = null;
		int access = 0;
		try {
			returnMsg[0] = "";
			hostId = SwtUtil.getCurrentHostId();
			ilmAnalysisMonitorManager = (ILMAnalysisMonitorManager)(SwtUtil.getBean("ilmAnalysisMonitorManager"));
			xStream = new XStream(new StaxDriver());
			xStream.registerConverter(new SwtUtil.MapEntryConverter());
			xStream.alias("schedconfig", Map.class);
			xStream.setClassLoader(Thread.currentThread().getContextClassLoader());
			schedulerConfigMap = (HashMap<String, String>) xStream.fromXML(SwtUtil.decode64(reportConfig));
			if("200".equals(schedulerConfigMap.get("jobid"))) {

				entityId = schedulerConfigMap.get("entityid");
				currencyId = schedulerConfigMap.get("currencycode");

				access = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(roleId, entityId,
						currencyId);
				if (access == 2) {
					returnMsg[0] = SwtUtil.getMessage("addjob.label.noAccessinEntityCurrency", request).replace("{0}", entityId).replace("{1}", currencyId);
				}


				return (access != 2);
			} else if("300".equals(schedulerConfigMap.get("jobid"))) {

				entityId = schedulerConfigMap.get("entityid");
				Collection entityAccess = SwtUtil.getSwtMaintenanceCache()
						.getFullEntityAccessCollection(roleId);
				Iterator itr = entityAccess.iterator();
				while (itr.hasNext()) {
					EntityUserAccess entityUserAccess = (EntityUserAccess) itr.next();
					if (entityUserAccess.getEntityId().equals(entityId)) {
						access = entityUserAccess.getAccess();
						break;
					}
				}
				if (access == 2) {
					returnMsg[0] = SwtUtil.getMessage("addjob.label.noAccessinEntity", request).replace("{0}", entityId);
				}


				return (access != 2);
			}else if("400".equals(schedulerConfigMap.get("jobid"))) {

				entityId = schedulerConfigMap.get("entityid");
				currencyId = schedulerConfigMap.get("currencycode");

				if ("All".equals(currencyId)) {
					Collection entityAccess = SwtUtil.getSwtMaintenanceCache()
							.getFullEntityAccessCollection(roleId);
					Iterator itr = entityAccess.iterator();
					while (itr.hasNext()) {
						EntityUserAccess entityUserAccess = (EntityUserAccess) itr.next();
						if (entityUserAccess.getEntityId().equals(entityId)) {
							access = entityUserAccess.getAccess();
							break;
						}
					}
					if (access == 2) {
						returnMsg[0] = SwtUtil.getMessage("addjob.label.noAccessinEntity", request).replace("{0}", entityId);
					}
				} else {
					access = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(roleId, entityId,
							currencyId);
					if (access == 2) {
						returnMsg[0] = SwtUtil.getMessage("addjob.label.noAccessinEntityCurrency", request).replace("{0}", entityId).replace("{1}", currencyId);
					}
				}


				return (access != 2);
			}else if("500".equals(schedulerConfigMap.get("jobid"))) {

				entityId = schedulerConfigMap.get("entityid");
				currencyId = schedulerConfigMap.get("currencycode");

				access = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(roleId, entityId,
						currencyId);
				if (access == 2) {
					returnMsg[0] = SwtUtil.getMessage("addjob.label.noAccessinEntityCurrency", request).replace("{0}", entityId).replace("{1}", currencyId);
				}

				return (access != 2);
			}else {

				entityId = schedulerConfigMap.get("entityid");
				currencyId = schedulerConfigMap.get("currencycode");
				ilmScenarioId = schedulerConfigMap.get("scenarioid");
				ilmGroupId = schedulerConfigMap.get("ilmgroup");

				if ("All".equals(entityId)) {

					isAllEntityAvailable = ilmAnalysisMonitorManager.isAllEntityAvailable(hostId, currencyId, roleId);

					if("y".equalsIgnoreCase(isAllEntityAvailable)) {
						access = 0;
					}else {
						access = 2;
						returnMsg[0] = SwtUtil.getMessage("addjob.label.noAccessinAllEntity", request);
					}
				} else {
					if ("All".equals(currencyId)) {
						Collection entityAccess = SwtUtil.getSwtMaintenanceCache()
								.getFullEntityAccessCollection(roleId);
						Iterator itr = entityAccess.iterator();
						while (itr.hasNext()) {
							EntityUserAccess entityUserAccess = (EntityUserAccess) itr.next();
							if (entityUserAccess.getEntityId().equals(entityId)) {
								access = entityUserAccess.getAccess();
								break;
							}
						}
						if (access == 2) {
							returnMsg[0] = SwtUtil.getMessage("addjob.label.noAccessinEntity", request).replace("{0}", entityId);
						}
					} else {
						access = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(roleId, entityId,
								currencyId);
						if (access == 2) {
							returnMsg[0] = SwtUtil.getMessage("addjob.label.noAccessinEntityCurrency", request).replace("{0}", entityId).replace("{1}", currencyId);
						}
					}
				}

				// Check if the ilm group already exists in the DB and not deleted
				if (!SwtUtil.isEmptyOrNull(ilmGroupId) && !"All".equals(ilmGroupId) && ilmAnalysisMonitorManager.getILMGroupDetails(ilmGroupId) == null) {
					access = 2;
					returnMsg[0] += SwtUtil.getMessage("addjob.label.noAccessinILMGroup", request).replace("{0}", ilmGroupId);
				}
				// Check if the ilm scenario already exists in the DB and not deleted
				if (!("Standard".equals(ilmScenarioId)) && ilmAnalysisMonitorManager.getILMScenarioDetails(ilmScenarioId) == null) {
					access = 2;
					returnMsg[0] += SwtUtil.getMessage("addjob.label.noAccessinILMScenario", request).replace("{0}", ilmScenarioId);
				}
				return (access != 2);
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [configParametersIsCorrect] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "configParametersIsCorrect", SchedulerAction.class), request, "");
			returnMsg[0] = "ERROR";
			return false;
		}
	}

	/**
	 * Check if config parameters for the report is successfully set and return error message if it is incorrect
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkIfConfigParamsIsCorrect() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String reportConfig = null;
		String executionRole = null;
		String[] returnMsg = new String[1];
		boolean returnValue;
		String resultAsString = null;
		try{
			log.debug(this.getClass().getName() + "- [checkIfConfigParamsIsCorrect] - Enter");
			reportConfig= request.getParameter("reportConfig");
			executionRole= request.getParameter("executionRole");
			returnValue = configParametersIsCorrect(reportConfig, executionRole, request, returnMsg);
			// Set the screenDetails to response
			resultAsString = returnMsg[0];
			if(!SwtUtil.isEmptyOrNull(resultAsString)) {
				resultAsString = resultAsString.replaceAll("\\\\n", "\n");
			}
			response.getWriter().print(returnValue + SwtConstants.SEPARATOR_FACILITIES + resultAsString);
			log.debug(this.getClass().getName() + " - [checkIfConfigParamsIsCorrect] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkIfConfigParamsIsCorrect] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "checkIfConfigParamsIsCorrect", SchedulerAction.class), request, "");
			return getView("fail");
		}
	}

	public void putOutputFileListInRequest(HttpServletRequest request) {
		Collection<LabelValueBean> outputFileList = new ArrayList<LabelValueBean>();
		// Put output file type list
		outputFileList.add(new LabelValueBean(SwtConstants.OUTPUT_FILE_TYPE_PDF, SwtConstants.OUTPUT_FILE_TYPE_PDF));
		outputFileList.add(new LabelValueBean(SwtConstants.OUTPUT_FILE_TYPE_XLS, SwtConstants.OUTPUT_FILE_TYPE_XLS));
		outputFileList.add(new LabelValueBean(SwtConstants.OUTPUT_FILE_TYPE_CSV, SwtConstants.OUTPUT_FILE_TYPE_CSV));
		outputFileList.add(new LabelValueBean(SwtConstants.OUTPUT_FILE_TYPE_TXT, SwtConstants.OUTPUT_FILE_TYPE_TXT));
		request.setAttribute("outputFileList", outputFileList);
	}

	/**
	 * Get the screen details (the program name, height and width) for a defined menu item Id
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getScreenDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String menuItemId = "";
		String screenDetails = "";
		try{
			log.debug(this.getClass().getName() + "- [getScreenDetails] - Enter");
			menuItemId = request.getParameter("menuItemId");
			// Get a string like: 'program name','height','width'
			screenDetails = schedulerMgr.getScreenDetails(menuItemId);
			// Set the screenDetails to response
			response.getWriter().print(screenDetails);
			log.debug(this.getClass().getName() + " - [getScreenDetails] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScreenDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getScreenDetails", SchedulerAction.class), request, "");
			return getView("fail");
		}
	}


	public String getreportsJobForAjax() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String selectedScheduleId = null;
		int result = 0;
		Collection<SchedReportHist> schedulerDetails = null;

		SchedReportHistManager schedReportHistManager = null;
		try {
			log.debug("Entering 'getreportsJobForAjax' method ");
			selectedScheduleId = request.getParameter("selectedScheduleId");
			schedReportHistManager = (SchedReportHistManager) SwtUtil.getBean("schedReportHistManager");
			schedulerDetails =schedReportHistManager.getSchedReportHistListJob(selectedScheduleId);
			if(schedulerDetails!=null) {
				result= schedulerDetails.size();
			}

			// write the result into response
			response.getWriter().print(result);
			log.debug("exiting 'getreportsJobForAjax' method");
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error("Exception Catch in batchScheduler.'getreportsJobForAjax' method : "
					+ exp.getMessage());

		}
		return null;
	}

	/**
	 * This method is used to display the show XML window for the report configuration parameters.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 * @throws Exception
	 */
	public String showXMLWindow() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String schedulerConfigXML = null;
		log.debug(this.getClass().getName() + " - [displayAccessList] - "
				+ "Entry");
		schedulerConfigXML =  SwtUtil.decode64(request.getParameter("schedulerConfigXML"));
		request.setAttribute("schedulerConfigXML", SwtUtil.encode64(getPrettyXML(schedulerConfigXML)));
		log.debug(this.getClass().getName() + " - [displayAccessList] - "
				+ "Exit");
		return getView("showXML");
	}

	/**
	 * get pretty view of the xml with removing date format tag and making dates in iso format
	 * @param xml
	 * @return
	 * @throws SwtException
	 */
	public static String getPrettyXML(String xml) throws SwtException{
		String dateForamtAsString = null;
		// Dates are provided by SI on ISO format
		String fromDateAsString = null;
		String toDateAsString = null;
		try {
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			dbf.setValidating(false);
			DocumentBuilder db = dbf.newDocumentBuilder();
			Document doc = db.parse(new InputSource(new StringReader(xml)));
			if(doc.getElementsByTagName("dateformat").getLength() >0) {
				dateForamtAsString = ((Element)doc.getElementsByTagName("dateformat").item(0)).getTextContent();
				Element dateFormatElement = (Element)doc.getElementsByTagName("dateformat").item(0);
				dateFormatElement.setTextContent("YYYY-MM-DD");

				if(doc.getElementsByTagName("fromdateasstring").getLength() >0) {
					Element fromdateasstring = (Element)doc.getElementsByTagName("fromdateasstring").item(0);
					fromDateAsString = fromdateasstring.getTextContent();
					if(!SwtUtil.isEmptyOrNull(fromDateAsString) && !SysParamsDAOHibernate.keywordQuery.containsKey(fromDateAsString) ) {
						fromDateAsString  = SwtUtil.formatDate(SwtUtil.parseDate(fromDateAsString, dateForamtAsString), "yyyy-MM-dd");
						fromdateasstring.setTextContent(fromDateAsString);
					}
				}

				if(doc.getElementsByTagName("todateasstring").getLength() >0) {
					Element todateasstring = (Element)doc.getElementsByTagName("todateasstring").item(0);
					toDateAsString = todateasstring.getTextContent();
					if(!SwtUtil.isEmptyOrNull(toDateAsString) && !SysParamsDAOHibernate.keywordQuery.containsKey(toDateAsString)) {
						toDateAsString  = SwtUtil.formatDate(SwtUtil.parseDate(toDateAsString, dateForamtAsString), "yyyy-MM-dd");
						todateasstring.setTextContent(toDateAsString);
					}
				}
			}

			Transformer transformer = TransformerFactory.newInstance().newTransformer();
			transformer.setOutputProperty(OutputKeys.INDENT, "yes");
			transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
			transformer.setOutputProperty("omit-xml-declaration", "yes");
			//initialize StreamResult with File object to save to file
			StreamResult result = new StreamResult(new StringWriter());
			DOMSource source = new DOMSource(doc);
			transformer.transform(source, result);
			return result.getWriter().toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return xml;
	}


	/**
	 * @param request
	 * @throws SwtException
	 */
	private Collection putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		Collection coll = SwtUtil.getUserEntityAccessList(UserThreadLocalHolder
				.getUserSession());
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll,
				UserThreadLocalHolder.getUserSession());
		ArrayList list = (ArrayList) coll;

		request.setAttribute("entityList", list);
		return list;
	}
}