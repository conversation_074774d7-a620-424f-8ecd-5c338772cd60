import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PaginationChangedArgs } from 'angular-slickgrid';
import moment from 'moment';
import { CommonService, DataExportMultiPage, EnhancedAlertingTooltip, ExportEvent, ExternalInterface, HBox, HTTPComms, JSONReader, JSONViewer, Logger, ScreenVersion, SwtAlert, SwtButton, SwtCanvas, SwtComboBox, SwtCommonGrid, SwtCommonGridPagination, SwtDateField, SwtLabel, SwtLoadingImage, SwtModule, SwtPopUpManager, SwtToolBoxModule, SwtUtil } from 'swt-tool-box';
import { AlertingRenderer } from '../EnhancedAlerting/Render/AlertingRenderer';
declare var instanceElement: any;

@Component({
  selector: 'app-bal-maintenance',
  templateUrl: './BalMaintenance.html',
  styleUrls: ['./BalMaintenance.css']
})
export class BalMaintenance extends SwtModule implements OnInit {

  /***********SwtLabel***********/
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('balanceTypeLabel') balanceTypeLabel: SwtLabel;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('dateLabel') dateLabel: SwtLabel;

  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedCurrency') selectedCurrency: SwtLabel;

  /***********SwtComboBox***********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('balanceTypeCombo') balanceTypeCombo: SwtComboBox;
  @ViewChild('currencyCombo') currencyCombo: SwtComboBox;

  /***********SwtCanvas***********/
  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
  @ViewChild('numstepper') numstepper: SwtCommonGridPagination;
  @ViewChild('pageBox') pageBox: HBox;

  /***********SwtButton***********/
  @ViewChild("viewButton") viewButton: SwtButton;
  @ViewChild("changeButton") changeButton: SwtButton;
  @ViewChild("reasonButton") reasonButton: SwtButton;
  @ViewChild("logButton") logButton: SwtButton;
  @ViewChild("closeButton") closeButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;

  /***********SwtDateField***********/
  @ViewChild('forDateField') forDateField: SwtDateField;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('exportContainer') exportContainer: DataExportMultiPage;
  //Variable to store the last page no
  public lastNumber: Number = 0;
  private entityId: string = null;
  private actionPath;
  private actionMethod: string = "";
  private requestParams;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private mainGrid: SwtCommonGrid;
  private defaultEntity;
  private selectedBalType;
  private selectedCurr;
  private defaultAcctType;
  private menuAccessId;
  private dateFormat: string;
  private displayedDate;
  private _invalidComms: string;
  public moduleId = "Predict";
  public currencyPattern ="";

  private errorLocation = 0;
  public screenVersion = new ScreenVersion(this.commonService);
  public lastReceivedJSON;
  private showJsonPopup = null;
  private logger: Logger = null;
  private currentFontSize: string = "";
  private hostId: string = null;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }

  ngOnInit() {
    instanceElement = this;
    this.mainGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.mainGrid.lockedColumnCount = 1;
    this.mainGrid.lockedColumnCount = 2;
    this.mainGrid.allowMultipleSelection = true;
    this.entityLabel.text = SwtUtil.getPredictMessage('entity.id', null);
    this.balanceTypeLabel.text = SwtUtil.getPredictMessage('balanceType', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('balance.currency', null);
    this.dateLabel.text = SwtUtil.getPredictMessage('date', null);

    this.entityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.balanceTypeCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectBalType', null);
    this.currencyCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectCurr', null);
    this.dateLabel.toolTip = SwtUtil.getPredictMessage('tooltip.selectBalDate', null);

    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('tooltip.viewSelBal', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('tooltip.changeSelBal', null);
    this.logButton.label = SwtUtil.getPredictMessage('button.log', null);
    this.logButton.toolTip = SwtUtil.getPredictMessage('tooltip.viewBalanceLog', null);
    this.reasonButton.label = SwtUtil.getPredictMessage('button.editreason', null);
    this.reasonButton.toolTip = SwtUtil.getPredictMessage('tooltip.editReason', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    this.hostId = ExternalInterface.call('eval', 'hostId');
    ExternalInterface.call('eval', 'menuAccessId');
    this.mainGrid.clientSideSort = false;
    this.mainGrid.clientSideFilter = false;

    // Set up column order and width change events
    this.mainGrid.columnWidthChanged.subscribe((event) => {
      this.columnWidthChange(event);
    });
    this.mainGrid.columnOrderChanged.subscribe((event) => {
      this.columnOrderChange(event);
    });

    this.mainGrid.onPaginationChanged = (event) => {
      this.paginationChanged(event);
    };
    this.exportContainer.enabled=false;
  }

  onLoad() {
    this.requestParams = [];

    this.loadingImage.setVisible(false);

    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    // set version number
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };

    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "balMaintenance.do?";
    this.actionMethod = 'method=displayListBalanceTypeAngular';
    this.requestParams['forDate'] = this.forDateField.text;

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    this.mainGrid.onFilterChanged = this.updateData.bind(this);
    this.mainGrid.onSortChanged = this.updateData.bind(this);
    
    this.mainGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      setTimeout(() => {
        this.itemClickFunction(selectedRowData)
      }, 100); ;
    });
    this.mainGrid.onRowClick = () => {
      this.cellClickEventHandler();
    };

    ExportEvent.subscribe((exportEvent) => {
      this.export(exportEvent.type, exportEvent.noOfPages);
      });

  }




  tooltipEntityId = null;
  tooltipCurrencyCode = null;
  tooltipSelectedAccount = null;
  tooltipFacilityId = null;
  tooltipSelectedDate = null;
  tooltipOtherParams = [];
  private positionX:number;
  private positionY:number;
  private selectedNodeId = null;
  private treeLevelValue = null;
  private alertingData = new HTTPComms(this.commonService);


  imageClickFunction(event) {
    const alerting = this.mainGrid.selectedItem && this.mainGrid.selectedItem.alerting;
  
    if (alerting === "Y" || alerting === "C") {
      this.tooltipSelectedAccount =
        this.mainGrid.selectedItem && this.mainGrid.selectedItem.accountId
        ? this.mainGrid.selectedItem.accountId.content
        : "";
      this.tooltipSelectedDate = this.forDateField.text;
      this.tooltipCurrencyCode = this.currencyCombo.selectedLabel;
      this.tooltipEntityId = this.entityCombo.selectedLabel;
      this.tooltipFacilityId = "BALANCE_MAINTENANCE_ACCOUNT_ROW";
      this.tooltipOtherParams = [];
      this.createTooltip(event);
    }
  }
 
   private lastSelectedTooltipParams = null;
   getParamsFromParent() {
    const params = {
      sqlParams: this.lastSelectedTooltipParams,
      facilityId: this.tooltipFacilityId,
      selectedNodeId: this.selectedNodeId,
      treeLevelValue: this.treeLevelValue,
      tooltipCurrencyCode: this.tooltipCurrencyCode,
      tooltipEntityId: this.tooltipEntityId,
      tooltipSelectedDate: this.tooltipSelectedDate,
      tooltipSelectedAccount: this.tooltipSelectedAccount,
      tooltipOtherParams: this.tooltipOtherParams,
    };
    return params;
  }
 
   private eventsCreated = false;
   private customTooltip: any = null;
   public createTooltip (event){
     if(this.customTooltip && this.customTooltip.close)
       this.removeTooltip();
     try {
       const toolTipWidth = 430;
       this.customTooltip = SwtPopUpManager.createPopUp(parent, EnhancedAlertingTooltip, {
       });
       this.customTooltip.enableResize = false;
       if(event != null ){
         this.positionX=event["clientX"];
         this.positionY=event["clientY"];
       }
       this.customTooltip.width = ''+toolTipWidth;
       this.customTooltip.height = "450";
       this.customTooltip.enableResize = false;
       this.customTooltip.title = "Alert Summary Tooltip";
       this.customTooltip.showControls = true;
       if (window.innerHeight < this.positionY+450)
         this.positionY=200;
       this.customTooltip.setWindowXY(this.positionX+20, this.positionY);
       this.customTooltip.showHeader = true;
       this.customTooltip.parentDocument = this;
       this.customTooltip.processBox = this;
       this.customTooltip.display();
       //event for display list button click
       setTimeout(() => {
 
          if (!this.eventsCreated) {
           this.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe((target) => {
             this.lastSelectedTooltipParams = target.noode.data;
              ExternalInterface.call("openAlertInstanceSummary", "openAlertInstSummary", this.selectedNodeId, this.treeLevelValue);
             /*var newWindow = window.open('/AlertInstanceSummary', 'Stop Rule Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
             if (window.focus) {
               newWindow.focus();
             }*/
           });
         }
       }, 0);
 
       //event for link to specific button click
       setTimeout(() => {
 
         if (!this.eventsCreated) {
           this.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe((target) => {
             this.getScenarioFacility(target.noode.data.scenario_id);
             this.lastSelectedTooltipParams = target.noode.data;
             this.hostId = target.hostId;
           });
         }
       }, 0);
 
       //event for tree item click
       setTimeout(() => {
 
         if (!this.eventsCreated) {
           this.customTooltip.getChild().ITEM_CLICK.subscribe((target) => {
             this.selectedNodeId= target.noode.data.id;
             this.treeLevelValue= target.noode.data.treeLevelValue;
             this.customTooltip.getChild().linkToSpecificButton.enabled = false;
             if(this.tooltipFacilityId == 'BALANCE_MAINTENANCE_ACCOUNT_ROW'){
               if (target.noode.data.count == 1 && target.noode.isBranch ==false ) {
                 this.customTooltip.getChild().linkToSpecificButton.enabled = true;
               }
             } 
           });
         }
       }, 0);
 
     } catch (error) {
       console.log("SwtCommonGrid -> createTooltip -> error", error)
 
     }
   }
 
   getScenarioFacility(scenarioId) {
     this.requestParams = [];
     this.alertingData.cbStart = this.startOfComms.bind(this);
     this.alertingData.cbStop = this.endOfComms.bind(this);
     this.alertingData.cbFault = this.inputDataFault.bind(this);
     this.alertingData.encodeURL = false;
     // Define the action to send the request
     this.actionPath = "scenarioSummary.do?";
     // Define method the request to access
     this.actionMethod = 'method=getScenarioFacility';
     this.requestParams['scenarioId'] = scenarioId;
     this.alertingData.url = this.baseURL + this.actionPath + this.actionMethod;
     this.alertingData.cbResult = (event) => {
       this.openGoToScreen(event);
     };
     this.alertingData.send(this.requestParams);
   }
 
   openGoToScreen(event) {
    if (event && event.ScenarioSummary && event.ScenarioSummary.scenarioFacility) {
      const facilityId = event.ScenarioSummary.scenarioFacility;
  
      const selectedEntity =
        (this.lastSelectedTooltipParams && this.lastSelectedTooltipParams.entity_id) || this.tooltipEntityId;
      const selectedCurrencyCode =
        (this.lastSelectedTooltipParams && this.lastSelectedTooltipParams.currency_code) || this.tooltipCurrencyCode;
      const selectedMatchId =
        (this.lastSelectedTooltipParams && this.lastSelectedTooltipParams.match_id) || null;
      const selectedMovementId =
        (this.lastSelectedTooltipParams && this.lastSelectedTooltipParams.movement_id) || null;
      const selectedSweepId =
        (this.lastSelectedTooltipParams && this.lastSelectedTooltipParams.sweep_id) || null;
  
      ExternalInterface.call(
        "goTo",
        facilityId,
        this.hostId,
        selectedEntity,
        selectedMatchId,
        selectedCurrencyCode,
        selectedMovementId,
        selectedSweepId,
        ""
      );
    }
  }
 
   public removeTooltip (){
     if(this.customTooltip != null)
       this.customTooltip.close();
   }
 
   itemClickFunction(event) {
    if (
      event.selectedCellTarget != null &&
      event.selectedCellTarget.field != null &&
      event.selectedCellTarget.field === "alerting" &&
      (event.selectedCellTarget.data.alerting === "C" || event.selectedCellTarget.data.alerting === "Y")
    ) {
      this.tooltipCurrencyCode = event.selectedCellTarget.data.balCurrencyCode;
      this.tooltipEntityId = this.entityCombo.selectedLabel;
      this.tooltipSelectedAccount = event.selectedCellTarget.data.accountId;
      this.tooltipFacilityId = "BALANCE_MAINTENANCE_ACCOUNT_ROW";
      this.tooltipSelectedDate = this.forDateField.text;
      this.tooltipOtherParams = [];
  
      setTimeout(() => {
        this.createTooltip(null);
      }, 100);
    } else {
      this.removeTooltip();
    }
  }
 

  /**
     * This function is invoked when export button is clicked,
     * used to export the grid data.
     *
     * @param reportType: String - Report type CSV/Excel/PDF
  **/
  export(reportType: string, noOfPages : any): void {
      // Get the FilteredGridColumn
      let selectedFilter: string = this.buildFilterString();
      // Get the SortedGridColumn
      let selectedSort: string = this.buildSortString();
    // Call the javascript function 'exportData' with required parameters
    ExternalInterface.call('exportData', reportType, noOfPages, this.numstepper.value, this.entityCombo.selectedLabel, this.currencyCombo.selectedLabel, this.forDateField.text, this.balanceTypeCombo.selectedValue, selectedFilter, selectedSort);
  }

  cellClickEventHandler(): void {
    const selIdx = this.mainGrid.selectedIndex;
    if (selIdx >= 0) {
      const item = this.mainGrid.selectedItem;
      const accountId       = item.accountId.content;
      const userName        = item.user.content   || "1";
      const forecastSOD     = item.forecastSODAsString.content.trim();
      const externalSOD     = item.externalSODAsString.content.trim();
      const entityId        = this.entityCombo.selectedLabel;
      const selectedFilter  = this.buildFilterString();
      const selectedSort    = this.buildSortString();

      
  
      // 1) Always enable View & Log when a row is selected
      this.viewButton.enabled  = true; this.viewButton.buttonMode  = true;
      this.logButton.enabled   = true; this.logButton.buttonMode   = true;
  
      // 2) Check currency access via synchronous XHR (as in JSP)
      let access = 0;
      try {
        const url = `${this.baseURL}currency.do?method=checkCurrencyAccess`
                  + `&entityId=${encodeURIComponent(entityId)}`
                  + `&accountId=${encodeURIComponent(accountId)}`;
        const xhr = new XMLHttpRequest();
        xhr.open("POST", url, false);
        xhr.send();
        access = Number(xhr.responseText);
      } catch (e) {
        console.error("currency access check failed", e);
        access = 1;
      }
  
      // 3) Change button only if menuAccessId==0 AND currency access==0
      const canChange = this.menuAccessId === 0 && access === 0;
      this.changeButton.enabled   = canChange;
      this.changeButton.buttonMode = canChange;
  
      // 4) Edit-Reason only if SOD length>2 AND menuAccessId==0 AND access==0
      const canEditReason = (forecastSOD.length > 2 || externalSOD.length > 2)
                          && this.menuAccessId === 0
                          && access === 0;
      this.reasonButton.enabled   = canEditReason;
      this.reasonButton.buttonMode = canEditReason;
  
      // 5) Close remains what it was (you already wire up closeHandler)
      //    No special disable on select.
  
    } else {
      console.warn("No row selected, disabling buttons");
      // nothing selected: disable everything except Close
      this.viewButton.enabled    =
      this.changeButton.enabled  =
      this.logButton.enabled     =
      this.reasonButton.enabled  = false;
      this.viewButton.buttonMode    =
      this.changeButton.buttonMode  =
      this.logButton.buttonMode     =
      this.reasonButton.buttonMode  = false;
    }
  }
  



  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    let maxPage;
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          errorLocation = 40;
          //Gets the current page from xml
          this.numstepper.value = Number(event.balMaintenanceDetailsList.grid.paging.currentpage);
          //Gets the maximum no of pages value
          maxPage = event.balMaintenanceDetailsList.grid.paging.maxpage;
          //Sets the Numeric stepper maximum value
          this.numstepper.maximum = Number(maxPage);
          this.mainGrid.paginationComponent = this.numstepper;
          //to make the pagination invisible if maxPage >1
          if(maxPage > 1) {
            this.pageBox.visible = true;
            this.numstepper.minimum=1;
            this.numstepper.maximum=maxPage;
          } else {
            this.pageBox.visible = false;
          }
          /*Enabling export container*/
          if (this.jsonReader.getRowSize() != 0) {
            this.exportContainer.enabled = true;
          }
          else {
            this.exportContainer.enabled = false;
          }
          this.exportContainer.maxPages = maxPage;
          this.exportContainer.totalPages = maxPage;
          this.exportContainer.currentPage = Number(event.balMaintenanceDetailsList.grid.paging.currentpage);
          this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
          this.dateFormat = this.jsonReader.getSingletons().dateFormat;
          errorLocation = 50;
          this.forDateField.formatString = this.dateFormat.toLowerCase();
          this.displayedDate = this.jsonReader.getSingletons().displayedDate;
          this.forDateField.text = this.displayedDate;
          errorLocation = 60;
          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.balanceTypeCombo.setComboData(this.jsonReader.getSelects(), true);
          this.currencyCombo.setComboData(this.jsonReader.getSelects());

          this.defaultAcctType = this.jsonReader.getSingletons().accounttype;

          this.defaultEntity = this.jsonReader.getSingletons().defaultEntity;
          if (this.defaultEntity != undefined) {
            this.entityCombo.selectedLabel = this.defaultEntity;
          }
          this.selectedBalType = this.jsonReader.getSingletons().selectedBalType;
          if (this.selectedBalType != undefined) {
            this.balanceTypeCombo.selectedValue = this.selectedBalType;
          }
          this.selectedCurr = this.jsonReader.getSingletons().selectedCurrencyCode;
          if (this.selectedCurr != undefined) {
            this.currencyCombo.selectedLabel = this.selectedCurr;
          }
          this.selectedEntity.text = this.entityCombo.selectedValue;
          this.selectedCurrency.text = this.currencyCombo.selectedValue;
          this.currentFontSize=  this.jsonReader.getScreenAttributes()["currfontsize"];
          if (!this.jsonReader.isDataBuilding()) {
            const obj = { columns: this.lastRecievedJSON.balMaintenanceDetailsList.grid.metadata.columns };
            //Balance Maintenance grid
            this.mainGrid.CustomGrid(obj);
            var gridRows = this.lastRecievedJSON.balMaintenanceDetailsList.grid.rows;
            if (gridRows.size > 0) {
              this.mainGrid.gridData = gridRows;
              this.mainGrid.setRowSize = this.jsonReader.getSingletons().totalCount;

              // Set up column order and width saving
              this.mainGrid.colWidthURL(this.baseURL + "balMaintenance.do?");
              this.mainGrid.colOrderURL(this.baseURL + "balMaintenance.do?");
              this.mainGrid.entityID = this.entityCombo.selectedLabel;
              this.mainGrid.saveWidths = true;
              this.mainGrid.saveColumnOrder = true;

              for (let i = 0; i < this.mainGrid.columnDefinitions.length; i++) {
                let column = this.mainGrid.columnDefinitions[i];
                if (column.field == "alerting") {
                  const alertUrl = "./" + ExternalInterface.call('eval', 'alertOrangeImage');
                  const alerCrittUrl = "./" + ExternalInterface.call('eval', 'alertRedImage');
                  if (this.currentFontSize == "Normal") {
                    column['properties'] = {
                      enabled: false,
                      columnName: 'alerting',
                      imageEnabled: alertUrl,
                      imageCritEnabled: alerCrittUrl,
                      imageDisabled: "",
                      _toolTipFlag: true,
                      style: ' display: block; margin-left: auto; margin-right: auto;'

                    };

                  } else {
                    column['properties'] = {
                      enabled: false,
                      columnName: 'alerting',
                      imageEnabled: alertUrl,
                      imageCritEnabled: alerCrittUrl,
                      imageDisabled: "",
                      _toolTipFlag: true,
                      style: 'height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;'

                    };
                  }
                  this.mainGrid.columnDefinitions[i].editor = null;
                  this.mainGrid.columnDefinitions[i].formatter = AlertingRenderer;
                }
              }

            }
            else {
              this.mainGrid.gridData = { size: 0, row: [] };
            }
            this.cellClickEventHandler();
            this.prevRecievedJSON = this.lastRecievedJSON;

          }
        }
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }
      }
    }
  }



STATIC_COLUMN_INDEX: Record<string, number> = {
  alerting:                    0,
  accountid:                   1,
  name:                        2,
  forecastsodasstring:         3,
  forecastsodtypeasstring:     4,
  externalsodasstring:         5,
  externalsodtypeasstring:     6,
  reasoncode:                  7,
  reasondesc:                  8,
  usernotes:                   9,
  user:                       10,
  inputdateasstring:          11
};

/**
 * Build the legacy “columnIndex|asc” string no matter how the user has dragged things around.
 */
private buildSortString(): string {
  // get the lowercase sorted column id, or empty string
  const id = (this.mainGrid.sortedGridColumnId || '').toLowerCase();
  if (!id) {
    return '';
  }

  // look up its original static index
  const idx = this.STATIC_COLUMN_INDEX[id];
  if (idx === undefined) {
    console.warn('Unmapped sortedGridColumnId:', id);
    return '';
  }
  
  // normalize sortDirection and test for ASC
  const dir = this.mainGrid.sortDirection || '';
  const asc = dir.toString().toUpperCase() === 'TRUE';

  return idx + '|' + asc;
}


private buildFilterString(): string {
  // 1) Gather all static indices
  const idxValues: number[] = [];
  for (const key in this.STATIC_COLUMN_INDEX) {
    if (this.STATIC_COLUMN_INDEX.hasOwnProperty(key)) {
      idxValues.push(this.STATIC_COLUMN_INDEX[key]);
    }
  }

  // 2) Find the maximum index
  const maxIdx = Math.max.apply(Math, idxValues);

  // 3) Initialize everything to "All"
  const filterArr: string[] = [];
  for (let i = 0; i <= maxIdx; i++) {
    filterArr[i] = 'All';
  }

  // 4) Pull in filters
  const filters = this.mainGrid.filters ? this.mainGrid.filters : [];
  for (let i = 0; i < filters.length; i++) {
    const f = filters[i];
    const idRaw = f.columnId;
    const id = idRaw ? idRaw.toLowerCase() : '';
    const idx = this.STATIC_COLUMN_INDEX[id];

    if (idx !== undefined
        && f.operator === 'EQ'
        && f.searchTerms
        && f.searchTerms.length > 0) {
      let val = f.searchTerms[0];

      // If this is a numeric SOD column, strip formatting:
      if (id === 'forecastsodasstring' || id === 'externalsodasstring') {
        // currencyPat2: thousand=”.”, decimal=“,”
        if (this.currencyPattern === 'currencyPat2') {
          // remove dots, swap comma→dot, then parse
          val = Number(
            val.replace(/\./g, '').replace(/,/g, '.')
          ).toString();
        }
        // currencyPat1: thousand=”,”, decimal=“.”
        else if (this.currencyPattern === 'currencyPat1') {
          // remove commas, then parse
          val = Number(
            val.replace(/,/g, '')
          ).toString();
        }
        // otherwise leave val as-is
      }

      filterArr[idx] = val;
    }
  }

  return filterArr.join('|');
}
/** Utility to show the “please fill date” alert */
private showFillDateAlert() {
  const msg = SwtUtil.getPredictMessage('alert.pleaseFillAllMandatoryFields', null);
  this.swtAlert.error(msg);
}

/** Ensure date is non-empty and valid, else return false */
private ensureDateValid(): boolean {
  // if empty, fill with systemDate
  if (!this.forDateField.text) {
    this.forDateField.text = this.displayedDate; // from lastRecievedJSON
  }
  // now validate
  return this.validateDateField(this.forDateField);
}

/** Called when Balance-Type changes */
onBalanceTypeChange(): void {
  // JSP enableCurrency logic
  const bt = this.balanceTypeCombo.selectedValue;
  if (bt === 'U' || bt === 'C' || bt === 'A') {
    this.currencyCombo.enabled = true;
  } else {
    this.currencyCombo.selectedLabel = 'All';
    this.currencyCombo.enabled = false;
  }

  // now send
  this.updateData('false');
}

/** Called when Currency changes */
onCurrencyChange(): void {
  setTimeout(() => {
    this.updateData('false');
  }, 100); 
}

/** Called when Entity changes */
onEntityChange(): void {

  // clear sort/filter/page
  try {
    this.mainGrid.clearSortFilter();  
  } catch (error) {
  }
  this.numstepper.value = 1;

  this.updateData('true');
}
updateData(isEntityChanged: string): void {
  let errorLocation = 0;
  try {
    errorLocation = 10;
    // Build the core payload
    const selectedFilter = this.buildFilterString();
    const selectedSort   = this.buildSortString();
    let currentPage    = 1;
    const entityId       = this.entityCombo.selectedLabel;
    const balType        = this.balanceTypeCombo.selectedValue;
    const currCode       = this.currencyCombo.selectedLabel;
    const forDate        = this.forDateField.text;
    
    // Common comms setup
    this.inputData.cbStart = this.startOfComms.bind(this);
    errorLocation = 20;
    this.inputData.cbStop  = this.endOfComms.bind(this);
    this.inputData.cbResult = (evt) => this.inputDataResult(evt);
    errorLocation = 30;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    errorLocation = 40;
    this.inputData.encodeURL = false;
    if (this.numstepper.value > 0) {
      currentPage =Number(this.numstepper.value.toString());
    }

    // Build params
    this.requestParams = {
      menuAccessId: this.menuAccessId,
      currentPage: isEntityChanged === 'true' ? '1' : currentPage,
      selectedSort: isEntityChanged === 'true' ? '' : selectedSort,
      selectedFilter: isEntityChanged === 'true' ? '' : selectedFilter,
      isEntityChanged : isEntityChanged,
      entityId,
      selectedBalType: balType,
      selectedCurrencyCode: currCode,
      selectedDisplayedDate: forDate,
      parentScreen: ''
    };

    // Fire
    this.actionPath   = 'balMaintenance.do?';
    this.actionMethod = 'method=displayListBalanceTypeAngular';
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 50;
    this.mainGrid.selectedIndex = -1;
    this.inputData.send(this.requestParams);
  } catch (err) {
    console.error('Error in updateData:', err);
    SwtUtil.logError(err, this.moduleId, this.commonService.getQualifiedClassName(this),
                     'updateData', errorLocation);
  }
}



  /**
   * This method is called when column width changes
   * @param event The column width change event
   */
  columnWidthChange(event): void {
    // The grid component handles saving the column widths automatically
    // when saveWidths is set to true and colWidthURL is set
  }

  /**
   * This method is called when column order changes
   * @param event The column order change event
   */
  columnOrderChange(event): void {
    // The grid component handles saving the column order automatically
    // when saveColumnOrder is set to true and colOrderURL is set
  }

  closeHandler() {
    ExternalInterface.call("close");
  }

  doBuildChangeBalMaintURL(methodeName) {
    // Initialize variables with provided values
    // Get the FilteredGridColumn
    let selectedFilter: string = this.buildFilterString();
    // Get the SortedGridColumn
    let selectedSort: string = this.buildSortString();
    var entityCode = this.jsonReader.getSingletons().defaultEntity;
    var selectedBalTypeId = this.mainGrid.selectedItem.accountId.content;
    var selectedName = this.mainGrid.selectedItem.name.content;
    var currentPage = this.numstepper.value.toString();
    var currentSort = selectedSort;
    var currentFilter = selectedFilter;
    var userName = this.mainGrid.selectedItem.user.content;
    var selectedBalType = this.balanceTypeCombo.selectedValue;
    var selectedDate = this.mainGrid.selectedItem.inputDateAsString.content;
    var selectedCurrency = this.currencyCombo.selectedLabel;
    var selectedCurrencyCode = this.mainGrid.selectedItem.balCurrencyCode.content;
    var EntityName = this.selectedEntity.text;
    var balanceSource = this.mainGrid.selectedItem.forecastSODTypeAsString.content;
    var reasonDesc = this.mainGrid.selectedItem.reasonDesc.content;
    var suppliedExternalBalanceSod = this.mainGrid.selectedItem.externalSODAsString.content;
    var forecastSOD = this.mainGrid.selectedItem.forecastSODAsString.content;
    var forecastSODType = this.mainGrid.selectedItem.forecastSODTypeAsString.content;
    var externalSOD = this.mainGrid.selectedItem.externalSODAsString.content

    // Call the ExternalInterface method with the parameters
    ExternalInterface.call("buildChangeBalMaintURL", methodeName,
      entityCode,
      selectedBalTypeId,
      selectedName,
      currentPage,
      currentSort,
      currentFilter,
      userName,
      selectedBalType,
      selectedDate,
      selectedCurrency,
      selectedCurrencyCode,
      EntityName,
      balanceSource,
      reasonDesc,
      suppliedExternalBalanceSod,
      forecastSOD,
      forecastSODType,
      externalSOD
    );
  }

  doBuildViewBalLogURL() {
    // Initialize variables with provided values
    var entityCode = this.jsonReader.getSingletons().defaultEntity;
    var selectedBalTypeId = this.mainGrid.selectedItem.accountId.content;
    var selectedBalTypeName = this.mainGrid.selectedItem.name.content;
    var selectedBalType = this.balanceTypeCombo.selectedValue;
    var selectedDate = this.mainGrid.selectedItem.inputDateAsString.content;
    var selectedCurrency = this.mainGrid.selectedItem.balCurrencyCode.content;
    // Call the ExternalInterface method with the parameters
    ExternalInterface.call("buildViewBalLogURL", entityCode, selectedBalTypeId, selectedBalType, selectedCurrency, selectedDate, selectedBalTypeName
    );
  }


  doBuildEditReason() {
    // Initialize variables with provided values
    // Get the FilteredGridColumn
    let selectedFilter: string = this.buildFilterString();
    // Get the SortedGridColumn
    let selectedSort: string = this.buildSortString();
    var entityCode = this.jsonReader.getSingletons().defaultEntity;
    var selectedBalTypeId = this.mainGrid.selectedItem.accountId.content;
    var selectedName = this.mainGrid.selectedItem.name.content;
    var currentPage = this.numstepper.value.toString();
    var currentSort = selectedSort;
    var currentFilter = selectedFilter;
    var maxPage = this.lastRecievedJSON.balMaintenanceDetailsList.grid.paging.maxpage;
    var userName = this.mainGrid.selectedItem.user.content;
    // Check if userName is null or undefined and set it to "1" if it is
    userName = userName != null ? userName : "1";
    var selectedBalType = this.balanceTypeCombo.selectedValue;
    var selectedDate = this.mainGrid.selectedItem.inputDateAsString.content;
    var selectedCurrency = this.mainGrid.selectedItem.balCurrencyCode.content;;
    var reasonCode = this.mainGrid.selectedItem.reasonCode.content;
    var selectedSodBalance = this.mainGrid.selectedItem.forecastSODAsString.content + this.mainGrid.selectedItem.externalSODAsString.content;
    var access = "0";
    // Call the ExternalInterface method with the parameters
    ExternalInterface.call("buildeditreason", entityCode, selectedBalTypeId, selectedDate, selectedName, selectedBalType, currentPage,
      selectedCurrency, currentSort, currentFilter, maxPage, userName, reasonCode, selectedSodBalance, access);
  }


  setFocusDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      dateField.setFocus();
      errorLocation = 10;
      dateField.text = this.jsonReader.getSingletons().displayedDate;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [setFocusDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "setFocusDateField", errorLocation);
    }
  }

  validateDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      errorLocation = 10;
      if (dateField.text) {
        errorLocation = 20;
        date = moment(dateField.text, this.dateFormat.toUpperCase(), true);
        errorLocation = 30;
        if (!date.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
            errorLocation = 40;
            this.setFocusDateField(dateField)
          });
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          errorLocation = 50;
          this.setFocusDateField(dateField)
        });
        return false;
      }
      errorLocation = 60;
      dateField.selectedDate = date.toDate();
      this.updateData('false');
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'BalMaintenance.ts', "validateDateField", errorLocation);
    }

    return true;
  }

  doHelp(): void {
    ExternalInterface.call("help");
  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  paginationChanged(event: PaginationChangedArgs) {
    this.numstepper.processing = true;
    this.doRefreshPage();
  }

  private doRefreshPage(): void {
    //To hold selected sort value
    var selectedSort: string = null;

    //To hold current page
    var currentPage: string = null;

    try {
      if (this.numstepper.value > 0) {
        if (((this.numstepper.value <= this.numstepper.maximum) && (this.numstepper.value != this.lastNumber) && (this.numstepper.value != 0))) {

          // Get the FilteredGridColumn
          let selectedFilter: string = this.buildFilterString();
          // Get the SortedGridColumn
          let selectedSort: string = this.buildSortString();
          //Get the current page value
          currentPage = (this.numstepper.value).toString();

          this.entityId = this.entityCombo.selectedLabel;
          // Initialising the request params
          this.requestParams = {};

          // Adding request params
          this.requestParams['selectedSort'] = selectedSort;
          this.requestParams['selectedFilter'] = selectedFilter;
          this.requestParams["currentPage"] = currentPage;
          this.requestParams["entityId"] = this.entityCombo.selectedLabel;
          this.requestParams["selectedCurrencyCode"] = this.currencyCombo.selectedLabel;
          this.requestParams['selectedBalType'] = this.balanceTypeCombo.selectedValue;
          this.requestParams['selectedDisplayedDate'] = this.forDateField.text;
          this.actionPath = 'balMaintenance.do?';
          this.actionMethod = 'method=displayListBalanceTypeAngular';
          //set http url
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;

          //send request to server
          this.inputData.send(this.requestParams);
        }
      }
      this.logger.info("method [doRefreshPage] - END");
    }
    catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'inputDataFault', 0);
    }

  }

  /**
 * enablePrintButton
 *
 */
  enablePrintButton(value): void {
    this.printButton.enabled = value;
    this.printButton.buttonMode = value;
  }

  /**
  * printPage
  *
  * param event
  *
  * Method to get call the action to get printPage
  */
  printPage(): void {
    let errorLocation = 0;
    try {
      ExternalInterface.call('printPage');

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", errorLocation);
    }
  }

  keyDownEventHandler(event) {

  }

  /** This function is used to display the XML for the monitor selected in the monitor combo
      */
  showJSONSelect(event): void {

    this.showJsonPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastReceivedJSON,
      });
    this.showJsonPopup.width = "700";
    this.showJsonPopup.title = "Last Received JSON";
    this.showJsonPopup.height = "500";
    this.showJsonPopup.enableResize = false;
    this.showJsonPopup.showControls = true;
    this.showJsonPopup.display();
  }
}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: BalMaintenance }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [BalMaintenance],
  entryComponents: []
})
export class BalMaintenanceModule { }