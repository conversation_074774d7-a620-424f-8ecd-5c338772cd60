<SwtModule (creationComplete)="onLoad()" width="100%" height="100%" paddingTop="10" paddingBottom="10">

  <VBox #allContainer id="allContainer" width="100%" height="100%" minWidth="800" styleName="vgroupAllContainer"
    paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas #movementSummary id="movementSummary" width="100%" height="100%">
      <VBox #Container id="Container" width="100%" height="100%" styleName="vgroupContainer">
        <SwtCanvas width="100%" height="40">
          <HBox #filterContainer id="filterContainer" x="0" y="17" width="100%" height="24"
            styleName="hgroupFilterContainer">
            <HBox height="100%" styleName="hgroupGrid" width="100%">
              <!-- <VBox> -->
              <HBox width="100%" height="100%">
                <!-- <HBox width="100%" height="100%"></HBox> -->
                <HBox width="100%" height="100%">
                  <SwtLabel paddingLeft="15" paddingTop="6" fontWeight="bold" text="Scenario ID"></SwtLabel>
                  <SwtLabel #scenarioIdAndTitle paddingLeft="15" paddingTop="6" id="scenarioIdAndTitle" fontWeight="normal"></SwtLabel>
                </HBox>
                <!-- <HBox width="100%" height="100%">
                  </HBox> -->
                <HBox width="100%" height="100%">
                  <SwtLabel #selectedEntity id="selectedEntity" paddingTop="6" styleName="labelLeft"></SwtLabel>
                </HBox>
              </HBox>
              <!-- </VBox> -->
            </HBox>
            <HBox styleName="hgroupCenterData">
              <HBox #paginationData id="paginationData" horizontalAlign="right" width="280">
                <SwtLabel #page id="page" horizontalAlign="button"></SwtLabel>
                <SwtCommonGridPagination #numStepper id="numStepper"></SwtCommonGridPagination>
                <!-- <SwtLabel #pageLeft id="pageLeft"></SwtLabel> -->
              </HBox>
            </HBox>
            <HBox></HBox>
          </HBox>
        </SwtCanvas>
        <SwtCanvas #dataGridContainer id="dataGridContainer"  width="100%" height="85%">
        </SwtCanvas>
        <SwtCanvas width="100%" height="35">
          <HBox id="controlContainer" width="100%" height="100%" styleName="hgroupFilterContainer">
            <HBox #buttonBox id="buttonBox" paddingLeft="8" width="100%">
              <SwtButton #refreshButton id="refreshButton" (click)="dataRefresh($event)"
                visible="false"></SwtButton>
              <SwtButton #closeButton id="closeButton" (click)="closeHandler($event)"
                visible="false"></SwtButton>
              <SwtButton #goToButton id="goToButton" 
                  styleName="flexButton" (click)="goTo($event)"
                visible="false"></SwtButton>
            </HBox>
            <HBox horizontalAlign="right" width="400">
              <SwtText #lastRefTimeLabel id="lastRefTimeLabel" visible="true" height="25" paddingTop="0" fontWeight="bold"></SwtText>
              <SwtText #lastRefTime id="lastRefTime" visible="true" height="25" paddingTop="0"
                styleName="textLastRefTime">
              </SwtText>
              <!-- <spacer width="5"></spacer> -->
              <!-- <HBox paddingTop="1" horizontalAlign="right"> -->
              <!-- <SwtDataExportMultiPage id="exportContainer" closePopupWindow="closePopup"
                  exportCancelFunction="exportCancel" exportFunction="export"></SwtDataExportMultiPage> -->

              <DataExportMultiPage right="45" tabIndex="6" #exportContainer id="exportContainer" verticalCenter="-1">
              </DataExportMultiPage>
              <SwtHelpButton #helpButton id="helpButton" right="5" tabIndex="20" buttonMode="true" enabled="true"
                styleName="helpIcon" (click)="doHelp()"></SwtHelpButton>
              <SwtLoadingImage #loadingImage> </SwtLoadingImage>
              <!-- </HBox> -->
            </HBox>
          </HBox>
        </SwtCanvas>
      </VBox>
    </SwtCanvas>
  </VBox>

</SwtModule>
