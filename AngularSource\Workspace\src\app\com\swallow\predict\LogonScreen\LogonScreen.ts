import { <PERSON><PERSON><PERSON>, StringUtils, Logger } from 'swt-tool-box';
import {Component, ElementRef, ModuleWithProviders, NgModule, ViewChild} from '@angular/core';
import {
  CommonService,
  ExternalInterface,
  focusManager,
  HTTPComms,
  JSONReader,
  Keyboard,
  SwtAlert,
  SwtButton,
  SwtComboBox,
  SwtLabel,
  SwtModule,
  SwtTextInput,
  SwtUtil, SwtNumericInput, SwtRadioButtonGroup, SwtRadioItem, SwtToolBoxModule, SwtLoadingImage, Alert
} from 'swt-tool-box';
import {RouterModule, Routes} from '@angular/router';

@Component({
  selector: 'app-logon-screen',
  templateUrl: './LogonScreen.html',
  styleUrls: ['./LogonScreen.css']
})
export class LogonScreen  extends SwtModule {

  private swtAlert: SwtAlert;
 /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];
  private invalidComms = "";
  private moduleId = "PREDICT";
  private callerMethod = null;
  private  errorLocation = 0;
  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();

  @ViewChild('comboAuthMethod') comboAuthMethod: SwtComboBox;
  @ViewChild('lblAuthMethod') lblAuthMethod: SwtLabel;
  @ViewChild('messageLabel') messageLabel: SwtLabel;
  @ViewChild('unauthorisedLabel') unauthorisedLabel: SwtLabel;
  @ViewChild('reloginLabel') reloginLabel: SwtLabel;
  
  
  @ViewChild('authMethodHBox') authMethodHBox: HBox;
  @ViewChild('logoutHBox') logoutHBox: HBox;
  @ViewChild('reloginBox') reloginBox: HBox;
  
  private allowInternalAuthentication: any;
  private isLogoutRedirect: any;
  private fromComboBox: boolean = false;
  userStillConnected: any;
  private logger: Logger = null;


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Logon Screen', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
    this.logger.info("method [constructor] - START/END ");
  }

  ngOnInit() {
    this.authMethodHBox.visible = false;
    this.authMethodHBox.includeInLayout = false;
    this.messageLabel.text = "";
    // this.comboAuthMethod.visible = false;
    // this.lblAuthMethod.visible = false;
    // allowInternalAuthentication
  }
  preLoginScreen
  onLoad(){
    // this.comboAuthMethod.dataProvider = [{"content":"External (Single-sign-on)","value":"External","selected":0,"type":""},{"content":"User/Password","value":"Internal","selected":0,"type":""}];
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.callerMethod = ExternalInterface.call('eval', 'callerMethod');
      errorLocation = 10;
      this.isLogoutRedirect = ExternalInterface.call('eval', 'isLogoutRedirect');
      errorLocation = 20;
      this.requestParams = [];
      this.requestParams["callerMethod"] = this.callerMethod;
      this.requestParams["isLogoutRedirect"] = this.isLogoutRedirect;

      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.errorLocation = 30;
        this.inputDataResult(data);
      };
      this.errorLocation = 40;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = 'logon.do?';
      this.actionMethod = "method=preLoginScreenData";

      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 50;
      this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'LogonScreen.ts', "onLoad", errorLocation);
    }
  }

  /**
   * inputDataFault
   * param event:  FaultEvent
   * This is a callback , used to handle fault event.
   * Shows fault message in alert window.
   */
   inputDataFault(event): void {
    this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.error(this.invalidComms);
  }

   /**
   * inputDataResult
   * param data: ResultEvent
   * This is a callback method, to handle result event
   *
   */
    inputDataResult(data): void {
      //Variable for errorLocation
      let errorLocation = 0;
      try {
        if (this.inputData.isBusy()) {
          this.inputData.cbStop();
        } else {
          this.jsonReader.setInputJSON(data);
          errorLocation = 10;
            if (this.jsonReader.getRequestReplyStatus()) {
              errorLocation = 20;
              this.comboAuthMethod.setComboData(this.jsonReader.getSelects(),true );
              errorLocation = 30;
              this.allowInternalAuthentication = this.jsonReader.getSingletons().allowInternalAuthentication;
              errorLocation = 40;
              this.isLogoutRedirect = this.jsonReader.getSingletons().isLogoutRedirect;
              errorLocation = 50;
              this.userStillConnected = this.jsonReader.getSingletons().userStillConnected;
              errorLocation = 60;
              if(StringUtils.isTrue(this.userStillConnected)){
                errorLocation = 70;
                this.messageLabel.text = "Your session is still active, Login Button to proceed";
              }else  if(StringUtils.isTrue(this.isLogoutRedirect)){
              errorLocation = 80;
                if(!StringUtils.isTrue(this.allowInternalAuthentication)){
                  this.messageLabel.height = '50px'
                  this.messageLabel.text = "You have been successfully logged out";
                  this.reloginBox.visible = true;
                  this.reloginBox.includeInLayout = true;
                  this.reloginBox.paddingTop = "0" ;
                  
                }else{
                  this.messageLabel.text = "You have been successfully logged out";
                  this.fromComboBox = true;
                  this.authMethodHBox.visible = true;
                  this.authMethodHBox.includeInLayout = true;
                  this.authMethodHBox.paddingTop = "0" ;
                }
              }
              else if(!StringUtils.isTrue(this.allowInternalAuthentication)){
                errorLocation = 90;
                this.messageLabel.text = "Please click on Login Button to proceed";
              }else {
                errorLocation = 100;
                this.fromComboBox = true;
                this.authMethodHBox.visible = true;
                this.authMethodHBox.includeInLayout = true;
                this.logoutHBox.visible = false;
                this.logoutHBox.includeInLayout = false;
              }
            } else {

              this.swtAlert.error( SwtUtil.getPredictMessage('label.errorContactSystemAdmin', null) + '\n'+ this.jsonReader.getRequestReplyMessage(), SwtUtil.getPredictMessage('screen.error', null));
            }
        }
      } catch (error) {
        // log the error in ERROR LOG
        this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
        SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'LogonScreen.ts', "inputDataResult", errorLocation);
      }
    }

    /**
     * This method is executed when clicking on login button, 2 options is possible if SmartAuthenficator is enabled :
     *  1- if inernal login is enabled then 2 options is poosible using external or internal(user and pass)
     *  2- if internal login is disabled then the user will not have the choice and will have a text they shows please click on the login button
     */
    loginHandler(){
      //Variable for errorLocation
      let errorLocation = 0;
      try {
        if(this.fromComboBox){
          errorLocation = 10;
          if(this.comboAuthMethod.selectedValue == "internal"){
            errorLocation = 20;
            ExternalInterface.call('doRedirectToLoginPage');
          }else {
            errorLocation = 30;
            ExternalInterface.call('doRedirectToMFAPage');
          }
        }else{
          if(StringUtils.isTrue(this.userStillConnected)){
            errorLocation = 40;
            ExternalInterface.call('doRedirectToLoginPage');
          }else{
            errorLocation = 50;
            ExternalInterface.call('doRedirectToMFAPage');
          }

        }
      } catch (error) {
        // log the error in ERROR LOG
        this.logger.error('method [loginHandler] - error: ', error, 'errorLocation: ', errorLocation);
        SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'LogonScreen.ts', "loginHandler", errorLocation);
      }

    }


    /*
    * keyDownEventHandler
    * param event
    * This is a key event listener, used to perform the operation
    * when hit the enter key based on the currently focused property(button)
    */
   keyDownEventHandler(event):void {
     //Variable for errorLocation
     let errorLocation = 0;
     try {
       //Currently focussed property name
       let eventString:string=Object(focusManager.getFocus()).name;
       errorLocation = 10;
       if ((event.keyCode == Keyboard.ENTER)) {
        errorLocation = 20;
         if (eventString == "loginButton") {
          errorLocation = 30;
           this.loginHandler();
         } else if (eventString == "cancelButton"){
          errorLocation = 40;
          window.close();
         }
       }
     } catch (error) {
       // log the error in ERROR LOG
       this.logger.error('method [keyDownEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
       SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'LogonScreen.ts', "keyDownEventHandler", errorLocation);
     }
   }
 
  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
   startOfComms(): void {
     //Variable for errorLocation
     let errorLocation = 0;
     try {
      // this.loadingImage.setVisible(true);
     } catch (error) {
       // log the error in ERROR LOG
       this.logger.error('method [startOfComms] - error: ', error, 'errorLocation: ', errorLocation);
       SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'LogonScreen.ts', "startOfComms", errorLocation);
     }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [endOfComms] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'LogonScreen.ts', "endOfComms", errorLocation);
    }
  }

}
// Define lazy loading routes
const routes: Routes = [
  { path: '', component: LogonScreen }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [LogonScreen],
  entryComponents: []
})
export class LogonScreenModule {}