<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="appContainer" width="100%" height="100%" paddingBottom="20" paddingLeft="20" paddingTop="20" paddingRight="20">
    <SwtCanvas id="swtControlBar" height="75%" width="100%">
        <Grid paddingLeft="15" paddingTop="10" width="100%" height="100%" >
          <GridRow width="100%" height="100%">
            <GridItem width="15%" paddingTop="4" height="100%">
              <SwtLabel #entityLabel styleName="labelBold" paddingRight="10"></SwtLabel>
            </GridItem>
            <GridItem width="21%" height="100%">
              <SwtComboBox id="cbEntity" #cbEntity
                           dataLabel="entity"
                           width="135"
                           (change)="changeCombo($event)"></SwtComboBox>

            </GridItem>
            <GridItem width="21%" height="100%">
              <SwtLabel id="selectedEntity" #selectedEntity width="239" fontWeight="normal"
                        styleName="labelLeft"></SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow width="100%" height="100%">
            <GridItem width="15%" paddingTop="2" height="100%">
              <SwtLabel #currencyLabel styleName="labelBold" paddingRight="10"></SwtLabel>
            </GridItem>
            <GridItem width="21%" height="100%">
              <SwtComboBox id="cbCurrency"
                           #cbCurrency
                           width="135"
                           dataLabel="currency"
                           (change)="changeCurrency($event)"></SwtComboBox>

            </GridItem>
            <GridItem width="21%" height="100%">
              <SwtLabel id="selectedCurrency" #selectedCurrency width="239" styleName="labelLeft" fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow width="100%" height="100%">
            <GridItem width="15%" height="100%">
              <SwtLabel #templateLabel styleName="labelBold" paddingRight="10"></SwtLabel>
            </GridItem>
            <GridItem width="21%" height="100%">
              <SwtComboBox id="cbTemplate"
                           #cbTemplate
                           width="135"
                           dataLabel="template"
                           (change)="changeComboTemplate($event)" shiftUp="40"></SwtComboBox>

            </GridItem>
            <GridItem width="31%" height="100%">
              <SwtLabel id="selectedTemplate"  #selectedTemplate width="239" styleName="labelLeft" fontWeight="normal"></SwtLabel>
            </GridItem>
            <GridItem width="11%"  height="100%">
              <SwtButton id="btnAdd" #btnAdd
                        buttonMode="true"
                         (click)="addTemplate()"></SwtButton>
            </GridItem>
            <GridItem width="10%"  height="100%">
              <SwtButton id="btnChange" #btnChange
                         buttonMode="true"
                         (click)="changeTemplate()"></SwtButton>
            </GridItem>
          </GridRow>
        </Grid>
    </SwtCanvas>
    <SwtCanvas id="cvSaveOptions" width="100%" height="20%" marginTop="10">
      <HBox width="100%">
        <HBox width="90%">
          <SwtButton id="btnSave" #btnSave
                     (click)="updateData('no')"
                     buttonMode="true"></SwtButton>
          <SwtButton id="btnCancel" #btnCancel
                     (click)="closeHandler($event)"
                     buttonMode="true"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" width="5%">
          <SwtHelpButton id="helpIcon"
                         #helpIcon (click)="doHelp($event)">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
