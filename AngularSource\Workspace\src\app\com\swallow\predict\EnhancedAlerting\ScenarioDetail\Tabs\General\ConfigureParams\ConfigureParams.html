<SwtModule (creationComplete)="onLoad()"  width="100%" height="100%">
  <VBox width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingBottom="5" paddingRight="5">
      <!--<VBox width="100%" height="100%">-->
    <Grid width="100%" height="20%" paddingTop="10">
      <GridRow  paddingLeft="23">
        <GridItem width="120">
          <SwtLabel #scenarioIdLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput #scenarioIdtxt width="200" enabled="false"></SwtTextInput>
        </GridItem>
      </GridRow>
      <GridRow  paddingLeft="23">
        <GridItem width="120">
          <SwtLabel #apiTypeLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtComboBox #apiTypeCombo id="apiTypeCombo" width="200"></SwtComboBox>
        </GridItem>
      </GridRow>
    </Grid>

    <SwtFieldSet id="fieldSet" #fieldSet style="height: 70%; width: 100%; color:blue; padding-right:5px; padding-bottom: 10px; padding-left: 5px;">
    <HBox width="100%" height="100%">
      <HBox width=50% height="100%">
        <Grid width="100%" height="100%" paddingTop="10" paddingLeft="10">
          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="hostCheck" #hostCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #hostLbl></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="entityCheck" #entityCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #entityLbl></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="ccyCheck" #ccyCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #ccyLbl></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="accountCheck" #accountCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #accountLbl></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="valDateCheck" #valDateCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #valDateLbl></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="amountCheck" #amountCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #amountLbl></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="signCheck" #signCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #signLbl></SwtLabel>
            </GridItem>
          </GridRow>

        </Grid>
      </HBox>
       <HBox width="50%"  height="100%" horizontalAlign="right">
        <Grid paddingTop="10" paddingLeft="10">
          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="mvtCheck" #mvtCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #mvtLbl></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="matchCheck" #matchCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #matchLbl></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="sweepCheck" #sweepCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #sweepLbl></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow paddingBottom="40">
            <GridItem width="30">
              <SwtCheckBox id="payCheck" #payCheck selected="false" ></SwtCheckBox>
            </GridItem>
            <GridItem width="250">
              <SwtLabel #payLbl></SwtLabel>
            </GridItem>
          </GridRow>

          
          <GridRow>
            <GridItem width="30">
              <SwtCheckBox id="allCheck" #allCheck selected="false"  (click)="checkAll()" ></SwtCheckBox>
            </GridItem>
            <GridItem width="300">
              <SwtLabel #allLbl></SwtLabel>
            </GridItem>
          </GridRow>

        </Grid>
      </HBox>
    </HBox>
   
    
    </SwtFieldSet>

    <SwtCanvas width="100%" height="10%" paddingTop="8">
      <HBox width="100%" horizontalGap="5" paddingLeft="10">
        <SwtButton #okButton enabled="true" (click)="saveMandatoryAttributes()" ></SwtButton>
        <SwtButton #cancelButton enabled="true" (click)="cancelHandler()" ></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
