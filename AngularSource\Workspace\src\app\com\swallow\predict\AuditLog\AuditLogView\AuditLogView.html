<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">

    <SwtCanvas id="dataGridContainer" #dataGridContainer paddingBottom="5" styleName="canvasWithGreyBorder"
      marginTop="10" border="false" width="100%" height="100%"></SwtCanvas>

      <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5">
        <HBox width="100%">
          <HBox paddingLeft="5" paddingTop="2" width="100%" >
            <SwtButton #closeButton width="70" (click)="closeHandler()" id="closeButton"></SwtButton>
          </HBox>      
            <HBox horizontalAlign="right" paddingRight="10" paddingTop="2" >
              <SwtHelpButton id="helpIcon" #helpIcon (click)="doHelp()"></SwtHelpButton>
              <SwtButton [buttonMode]="true" #printButton id="printButton" styleName="printIcon"
                (click)="printPage()">
              </SwtButton>
              <SwtLoadingImage #loadingImage></SwtLoadingImage>
            </HBox>
          </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>