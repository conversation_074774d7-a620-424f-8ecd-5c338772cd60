import { StringUtils } from 'swt-tool-box';
import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService,
  ExternalInterface,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtCanvas,
  SwtCommonGrid,
  SwtModule,
  SwtUtil,
  SwtToolBoxModule,
  SwtRadioItem,
  SwtRadioButtonGroup,
  SwtLabel,
  SwtComboBox,
  SwtDateField,
  SwtButton,
  HBox,
  CommonUtil,
  Timer,
  ContextMenuItem,
  SwtPopUpManager,
  JSONViewer,
  ScreenVersion,
  GridRow,
  SwtLoadingImage,
  Logger
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";
import moment from "moment";

@Component({
  selector: 'app-ilm-currency-calculation',
  templateUrl: './IlmCurrencyCalculation.html',
  styleUrls: ['./IlmCurrencyCalculation.css']
})
export class IlmCurrencyCalculation extends SwtModule {
  public jsonReader: JSONReader = new JSONReader();
  public lastReceivedJSON;
  public prevReceivedJSON;
  private  currencyCalculationGrid: SwtCommonGrid;

  /**
   * Communication Objects
   **/
  private inputData =  new HTTPComms(this.commonService);
  private baseURL: string= SwtUtil.getBaseURL();
  private actionMethod: string=null;
  private actionPath: string=null;
  private requestParams = [];
  private invalidComms: string = null;
  private closeWindow=false;
  private entityId: string;
  private currencyCode: string;
  private selectedStartDate: string;
  private selectedEndDate: string;
  private processOption: string;
  private singleOrRange: string;
  private dateFormat: string;
  private dateFormatValue: string;
  private refreshRate = 5;
  private sequenceNumber: string;
  private isSingleDate: boolean;
  private allProcess: boolean ;
  private ccyEntityAccess: string = null;
  private ccyIsEmpty: string = null;
  private swtAlert: SwtAlert;
  private errorLocation = 0;
  private moduleId ="Predict";
  private isCalculationLaunched = false;
  private menuAccessId  = 0;
  private menuAccess  = "";
  private status  = "";
  private autoRefresh: Timer = null;
  // Variable that holds the version number for this mxml
  private versionNumber = "1";
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private  screenName = "ILM Calculation Launcher";
  private versionDate = "04/11/2019";
  private canceled = false;
  private logger: Logger;
  private fromDateAsString;
  private toDateAsString;
  private leftThresholdDay;
  private rightThresholdDay;
  private selecteDefaultDate;
  private allIsAllowed;
  /**
   * Popup Objects
   **/
  private showJsonPopup = null;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('labelStatus') labelStatus: SwtLabel;
  @ViewChild('gridCanvas') gridCanvas: SwtCanvas;
  /*********Combobox*********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;

  /*********SwtLabel*********/
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('toDateLabel') toDateLabel: SwtLabel;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('calculateGroupLabel') calculateGroupLabel: SwtLabel;
  @ViewChild('dateGroupLabel') dateGroupLabel: SwtLabel;

  /*********SwtRadioButtonGroup*************/
  @ViewChild('calculateGroup') calculateGroup: SwtRadioButtonGroup;
  @ViewChild('dateGroup') dateGroup: SwtRadioButtonGroup;

  /*********SwtRadioItem*************/
  @ViewChild('screens') screens: SwtRadioItem;
  @ViewChild('screenAndReports') screenAndReports: SwtRadioItem;
  @ViewChild('single') single: SwtRadioItem;
  @ViewChild('range') range: SwtRadioItem;
  /*********SwtDateField*********/
  @ViewChild('startDate') startDate: SwtDateField;
  @ViewChild('toDate') toDate: SwtDateField;

  /*********SwtButton*********/
  @ViewChild('processButton') processButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  /*********HBox*********/
  @ViewChild('toHbox') toHbox: HBox;
  @ViewChild('gridRowEntity') gridRowEntity: GridRow;
  @ViewChild('gridRowCurrency') gridRowCurrency: GridRow;
  @ViewChild('gridRowCalculateGroup') gridRowCalculateGroup: GridRow;
  @ViewChild('gridRowDateGroup') gridRowDateGroup: GridRow;
  @ViewChild('gridRowDates') gridRowDates: GridRow;

  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.logger = new Logger('Interface Monitor Screen', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }

  ngOnInit() {
    this.currencyCalculationGrid = this.gridCanvas.addChild(SwtCommonGrid) as SwtCommonGrid;
    this.entityLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.entity', null);
    this.entityCombo.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.entity', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('label.accountattribute.currency', null);
    this.ccyCombo.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.currency', null);
    this.dateGroupLabel.text = "Date";// SwtUtil.getPredictMessage('reports.process', null);
    this.screens.label = "ILM data for screens"; // SwtUtil.getPredictMessage('label.ilmdataandreportscreens', null);
    this.screenAndReports.label = SwtUtil.getPredictMessage('label.ilmdataandreportscreens', null);
    this.calculateGroupLabel.text = "Calculate";// SwtUtil.getPredictMessage('label.ilmdataandreportscreens', null);
    this.calculateGroup.toolTip = SwtUtil.getPredictMessage('tooltip.ilmdatascreenreports', null);
    this.range.label = SwtUtil.getPredictMessage('ilmReport.dateRange', null);
    this.single.label = SwtUtil.getPredictMessage('ilmReport.singleDay', null);
    this.processButton.label = SwtUtil.getPredictMessage('reports.process', null);
    this.processButton.toolTip = SwtUtil.getPredictMessage('tooltip.processButton', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('tooltip.cancel', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.toDateLabel.text ="To"  ;// SwtUtil.getPredictMessage('button.cancel', null);
    this.menuAccess = ExternalInterface.call('eval', 'menuAccessId');
    this.ccyEntityAccess = ExternalInterface.call("eval","ccyEntityAccess");
    this.ccyIsEmpty = ExternalInterface.call("eval","ccyIsEmpty");
    if (this.ccyIsEmpty == 'true') {
      this.processButton.enabled = false;
    } else {
      if (this.menuAccess  == "0") {
          this.processButton.enabled = this.ccyEntityAccess == "0";
      } else {
        this.processButton.enabled = false;
      }
    }
    this.cancelButton.enabled = false;
  }
 
  /**
   * Upon completion of loading into the flash player this method is called
   **/
  onLoad(): void {
    this.loadingImage.setVisible(false);

    if(this.menuAccess) {
      if(this.menuAccess !== "") {
        this.menuAccessId = Number(this.menuAccess);
      }
    }
    // set version number
    this.initializeMenus();
    this.entityId = ExternalInterface.call("eval","entityId");
    this.currencyCode = ExternalInterface.call("eval","currencyCode");
    this.selectedStartDate = ExternalInterface.call("eval","selectedStartDate");
    this.selectedEndDate = ExternalInterface.call("eval","selectedEndDate");
    this.processOption = ExternalInterface.call("eval","processOption");
    this.singleOrRange = ExternalInterface.call("eval","singleOrRange");
    this.allProcess = ExternalInterface.call("eval","allProcess");
    this.isSingleDate = ExternalInterface.call("eval","isSingleDate");
    this.dateFormat = ExternalInterface.call("eval","dateFormat");
    this.dateFormatValue = ExternalInterface.call("eval","dateFormatValue");
    this.toHbox.visible = !this.isSingleDate;

    // result event
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    // action url	
    this.actionPath="intraDayLiquidity.do?method=";
    // Then declare the action method:					
    this.actionMethod="listCurrencyProcessStatus";
    this.requestParams =[];
    this.requestParams["entityId"] = this.entityId;
    this.requestParams["currencyCode"] = this.currencyCode;
    this.requestParams["selectedStartDate"] = this.selectedStartDate;
    this.requestParams["selectedEndDate"] = this.selectedEndDate;
    this.requestParams["processOption"] = this.processOption;
    this.requestParams["singleOrRange"] = this.singleOrRange;
    this.requestParams["sequenceNumber"]= this.sequenceNumber;
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    // Make initial request
    this.inputData.send(this.requestParams);

    this.currencyCalculationGrid.rowColorFunction = ( dataContext, dataIndex, color, dataField ) => {
      return this.drawRowBackground( dataContext, dataIndex, color , dataField);
    };
    

  }

  drawRowBackground( dataContext, dataIndex, color,dataField ): string {

    let rColor: string;
    try {
      let colorFlag: string;
    if(dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.hasViewAccessOnly.content){
      if(StringUtils.isTrue(dataContext.slickgrid_rowcontent.hasViewAccessOnly.content)){
        rColor = "#DDDDDD";
      }
      
    }
      
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }
  /**
   * The function initializes the menus in the right click event on the Entity Monitor screen.
   * The links are redirected to their respective pages.
   */
  private  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.versionDate);
    let addMenuItem = new ContextMenuItem("Show JSON");
    // add the listener to addMenuItem
    addMenuItem.MenuItemSelect = this.showJSONSelect.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }


  /** This function is used to display the XML for the monitor selected in the monitor combo
   */
  showJSONSelect(event): void {

    this.showJsonPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastReceivedJSON,
      });
    this.showJsonPopup.width = "700";
    this.showJsonPopup.title = "Last Received JSON";
    this.showJsonPopup.height = "500";
    this.showJsonPopup.enableResize = false;
    this.showJsonPopup.showControls = true;
    this.showJsonPopup.display();
  }

  /**
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   * */
  inputDataRefresh(event): void {
    this.lastReceivedJSON= event;
    this.jsonReader.setInputJSON(this.lastReceivedJSON);
    if (this.jsonReader.getRequestReplyStatus()) {
      ExternalInterface.call("checkProcessingResult",""+this.jsonReader.getprossesInfoStatus(),""+this.jsonReader.getprossesInfoRunning());
      if ((this.lastReceivedJSON != this.prevReceivedJSON)) {
        this.currencyCalculationGrid.gridData = this.jsonReader.getGridData();
        this.currencyCalculationGrid.setRowSize = this.jsonReader.getRowSize();
        this.currencyCalculationGrid.selectedIndex = -1;
        this.prevReceivedJSON = this.lastReceivedJSON;

        this.allIsAllowed=this.jsonReader.getSingletons().allIsAllowed;
        if(!StringUtils.isTrue(this.allIsAllowed) && !this.alertShown){
          this.swtAlert.warning("You don't have full access on at least one currency.  The Process button will act only on currencies where you have Full Access" );
         }
         this.alertShown = true;

      }
    } else {
      this.swtAlert.warning(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error", Alert.OK, this, this.errorHandler.bind(this));
    }
  }
  private alertShown=true;

  /**
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   * */
  inputDataResult(event): void {
    // get the received xml
    this.lastReceivedJSON= event;
    this.jsonReader.setInputJSON(this.lastReceivedJSON);
    if (this.jsonReader.getRequestReplyStatus()) {
      if ((this.lastReceivedJSON != this.prevReceivedJSON)) {
        if (!this.jsonReader.isDataBuilding()) {
          this.ccyCombo.setComboData( this.jsonReader.getSelects(), false);
          this.entityCombo.setComboData( this.jsonReader.getSelects(), false);
          this.selectedCcy.text = this.ccyCombo.selectedItem.value;
          this.selectedEntity.text = this.entityCombo.selectedItem.value;
          this.selectedStartDate = this.jsonReader.getSingletons().selectedStartDate;
          this.leftThresholdDay= this.jsonReader.getSingletons().firstThresholdDay;
          this.rightThresholdDay= this.jsonReader.getSingletons().lastThresholdDay;
          this.selecteDefaultDate=this.jsonReader.getSingletons().selecteDefaultDate;
          this.allIsAllowed=this.jsonReader.getSingletons().allIsAllowed;
          if(!StringUtils.isTrue(this.allIsAllowed) && !this.alertShown){
            this.swtAlert.warning("You don't have full access on at least one currency.  The Process button will act only on currencies where you have Full Access" );
           }
           this.alertShown = true;
          this.startDate.formatString =  this.dateFormatValue ;
          this.startDate.selectedDate = new Date(CommonUtil.parseDate(this.selectedStartDate, this.dateFormatValue .toUpperCase()));
          this.fromDateAsString = this.jsonReader.getSingletons().fromDateAsString;//ExternalInterface.call("eval","fromDateAsString");
          this.toDateAsString = this.jsonReader.getSingletons().toDateAsString;//ExternalInterface.call("eval","toDateAsString");
          let minDate = new Date(CommonUtil.parseDate(this.fromDateAsString, this.dateFormatValue .toUpperCase()));
          let maxDate = new Date(CommonUtil.parseDate(this.toDateAsString, this.dateFormatValue .toUpperCase()));
          this.startDate.selectableRange = { rangStart: minDate, rangeEnd: maxDate };
          this.toDate.selectableRange =  { rangStart: minDate, rangeEnd: maxDate };
          if( this.singleOrRange !== "S") {
            this.selectedEndDate = this.jsonReader.getSingletons().selectedEndDate;
            this.toDate.formatString =  this.dateFormatValue ;
            this.toDate.selectedDate = new Date(CommonUtil.parseDate(this.selectedEndDate, this.dateFormatValue .toUpperCase()));
          }

          /*****grid display***********/
          const obj  = {columns: this.jsonReader.getColumnData()};
          this.currencyCalculationGrid.doubleClickEnabled  = true;
          this.currencyCalculationGrid.CustomGrid(obj);
          if (this.jsonReader.getGridData()) {
            if (this.jsonReader.getGridData().size > 0) {
              this.currencyCalculationGrid.gridData=this.jsonReader.getGridData();
              this.currencyCalculationGrid.setRowSize=this.jsonReader.getRowSize();
            } else {
              this.currencyCalculationGrid.dataProvider = null;
              this.currencyCalculationGrid.selectedIndex = -1;
            }
          } else {
            this.currencyCalculationGrid.dataProvider = null;
            this.currencyCalculationGrid.selectedIndex = -1;
          }
          /**AutoRefresh******************/
          if (this.autoRefresh == null) {
            this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
            this.autoRefresh.addEventListener("timer", this.refreshdetails.bind(this));
          } else {
            this.autoRefresh.delay(this.refreshRate * 1000);
          }
        }
        this.prevReceivedJSON=this.lastReceivedJSON;
      }
    } else {
      if (this.lastReceivedJSON.hasOwnProperty("requestthis.reply")) {
        if (this.lastReceivedJSON.requestthis.reply.closewindow == "true") {
          this.closeWindow=true;
        }
      }
      this.swtAlert.warning(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error", Alert.OK, this);
    }
    if (this.autoRefresh != null) {
      if (!this.autoRefresh.running) {
        this.autoRefresh.start();
      }
    }
  }

  setScreenStatusView(status, seqNumber) {
    if(status == "RUNNING") {
      //if(this.sequenceNumber == seqNumber) {
        // Display calculation in progress text
       this.labelStatus.fontWeight="bold";
       this.labelStatus.color ="red";
       this.labelStatus.text = SwtUtil.getPredictMessage('label.ilmccyprocess.calculinprogress', null);
       this.loadingImage.setVisible(true);
       this.cancelButton.enabled = true;
       this.disableEnableComponents(false);
       this.processButton.enabled = false;
       this.cancelButton.enabled = true;
       this.isCalculationLaunched = true;
    /*  } else {
        if (this.menuAccess === "0") {
          this.processButton.enabled = false;
        }
        this.cancelButton.enabled = false;

      }*/
    } else {
      if(status == "CANCEL" && this.isCalculationLaunched) {
       this.labelStatus.text = SwtUtil.getPredictMessage('label.ilmccyprocess.calculationcancelled', null);
      } else if(status == "LAUNCH_FAILED") {
        this.labelStatus.text = SwtUtil.getPredictMessage('label.ilmccyprocess.calculationlaunchfailed', null);
      } else if(status == "SUCCESS" && this.isCalculationLaunched) {
        this.labelStatus.text = SwtUtil.getPredictMessage('label.ilmcalculationsuccess', null);
      } else if(status == "FAIL" && this.isCalculationLaunched) {
        this.labelStatus.text = SwtUtil.getPredictMessage('label.ilmcalculationfailed', null);
      }

      this.loadingImage.setVisible(false);
      this.disableEnableComponents(true);
      let result: string = ExternalInterface.call("setScreenStatusView",this.entityId,this.currencyCode);
      if (this.menuAccessId == 0) {
          this.processButton.enabled = result == "0";
      }
      this.cancelButton.enabled = false;
      // CANCELED status from DB should be token into account
      if(this.status != "CANCELED") {
        this.labelStatus.color = "";
        this.labelStatus.fontWeight="normal";
        this.isCalculationLaunched = false;
      } else if(this.isCalculationLaunched == true) {
        this.labelStatus.fontWeight="normal";
        this.labelStatus.color = "";
        this.labelStatus.text=  SwtUtil.getPredictMessage('label.ilmccyprocess.calculationcancelled', null);
        this.isCalculationLaunched = false;
      }
    }
  }

  /**
   * disableComponents()
   *
   * Method called to disable components
   *
   */
  disableEnableComponents(value: boolean): void {
    try {
      this.gridRowEntity.enabled = value;
      this.gridRowCurrency.enabled = value;
      this.gridRowCalculateGroup.enabled = value;
      this.calculateGroup.enabled = value;
      this.gridRowDateGroup.enabled = value;
      this.dateGroup.enabled = value;
      this.gridRowDates.enabled = value;

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'IlmCurrencyCalculation', 'disableComponents', this.errorLocation);
    }
  }


  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   * @param event:FaultEvent
   **/
 inputDataFault(event): void {
   this.invalidComms= event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
   this.swtAlert.error("fault "+ this.invalidComms);
  }


  validateField(dateField): boolean {
    try {
      let date;
      if(dateField.text && (dateField.text).length == 10 ) {
        date = moment(dateField.text, this.dateFormatValue.toUpperCase() , true);
        if(!date.isValid()) {
          this.swtAlert.warning("Please enter a valid Date" );
          return false;
        }
      } else {
        this.swtAlert.warning("Please enter a valid Date" );
        return false;
      }
      dateField.selectedDate = date.toDate();
      return true;
    } catch (error) {
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 'IlmCurrencyCalculation', ' validateDateField', this.errorLocation);
    }

    return true;
  }

  validateDateField(dateField, fieldName) {
    try {
      let isValid = false;
      if (this.dateFormat == 'datePat2') {
        isValid = this.validateField(dateField);
      } else if (this.dateFormat == 'datePat1') {
        isValid = this.validateField(dateField);
      }
      if (!isValid) {
        dateField.selectedDate = null;
      }

      return isValid;
    } catch(error) {
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 'ILM CALCULATION', ' validateDateField', this.errorLocation);
    }

  }


  validateReportDates() {
    let resultComapreDate = false;
    if(this.isSingleDate) {
      if(this.startDate.text == "" ) {
        SwtUtil.getPredictMessage('alert.enterValidDate', null);
        this.swtAlert.error( SwtUtil.getPredictMessage('alert.enterValidDate', null));
      } else {
        this.toDate.selectedDate = null;
        return this.validateDateField(this.startDate,"fromDateAsString");
      }
    } else {
      if(this.startDate.text == "") {
        this.swtAlert.error(SwtUtil.getPredictMessage('alert.enterValidFromDate', null));
      } else if (this.toDate.text == "") {
        this.swtAlert.error(SwtUtil.getPredictMessage('alert.enterValidToDate', null));
      } else {
        resultComapreDate = ExternalInterface.call("comparedates", this.startDate.text, this.toDate.text, this.dateFormat ,'Start Date', 'End Date');
      }
      return resultComapreDate;
    }
  }


  launchCalculationProcess() {
    this.isCalculationLaunched = false;
    this.canceled = false;
    let validReportDates: boolean;
    validReportDates = this.validateReportDates();
    let results = [];
    results = this.testThresholdDates();
    this.entityId = this.entityCombo.selectedItem.content;
    this.currencyCode = this.ccyCombo.selectedItem.content;
    this.selectedStartDate = this.startDate.text;
    this.selectedEndDate = this.toDate.text;
    this.processOption = this.allProcess ? "A" : "M";
    this.singleOrRange = this.isSingleDate ? "S" : "R";
    ExternalInterface.call("launchCalculation", this.entityId,  this.currencyCode, this.selectedStartDate, this.selectedEndDate,this.processOption,this.isSingleDate,this.singleOrRange,validReportDates,results );
  }


  testThresholdDates() {
    //let leftThresholdDay = ExternalInterface.call("eval","firstThresholdDay");
    //let rightThresholdDay = ExternalInterface.call("eval","lastThresholdDay");
    let res1 = true;
    let res2 = false;
    let resArray = new Array(3);
    if (this.leftThresholdDay != "" && this.leftThresholdDay != this.rightThresholdDay) {
      if (this.stringToDate(this.leftThresholdDay,this.dateFormatValue,"/").getTime() != this.stringToDate(this.startDate.text,this.dateFormatValue,"/").getTime()) {
        res1 = ExternalInterface.call("compareTwoDates", this.leftThresholdDay, this.startDate.text, this.dateFormat ,'00:00', '00:00');
      } else {
        res1 = false;
      }
    }
    if(this.isSingleDate) {
      if (this.stringToDate(this.rightThresholdDay,this.dateFormatValue,"/").getTime()!= this.stringToDate(this.startDate.text,this.dateFormatValue,"/").getTime()) {
        res2 = ExternalInterface.call("compareTwoDates", this.startDate.text, this.rightThresholdDay, this.dateFormat ,'00:00', '00:00');
      }
    } else {
      if (this.stringToDate(this.rightThresholdDay,this.dateFormatValue,"/").getTime()!= this.stringToDate(this.toDate.text,this.dateFormatValue,"/").getTime()) {
        res2 = ExternalInterface.call("compareTwoDates", this.toDate.text, this.rightThresholdDay, this.dateFormat ,'00:00', '00:00');
      }
    }

    resArray[0] = res1;
    resArray[1]=res2;
    resArray[2] = res1 && res2;
    return resArray;
}


  stringToDate(date,format, delimiter) {
    let formatLowerCase = format.toLowerCase();
    let formatItems = formatLowerCase.split(delimiter);
    let dateItems = date.split(delimiter);
    let monthIndex =formatItems.indexOf("mm");
    let dayIndex = formatItems.indexOf("dd");
    let yearIndex = formatItems.indexOf("yyyy");
    let month = parseInt(dateItems[monthIndex],10);
    month-=1;
    return new Date(dateItems[yearIndex],month,dateItems[dayIndex]);
  }


   resetOffendingDate(results) {
    if (!results[0]) {
      this.startDate.text = this.fromDateAsString;//ExternalInterface.call("eval", "fromDateAsString");
    } else if (!results[1] && this.isSingleDate) {
      this.startDate.text = this.toDateAsString;//ExternalInterface.call("eval", "toDateAsString");
    } else {
      this.toDate.text =this.toDateAsString//ExternalInterface.call("eval", "toDateAsString");
    }

    this.refreshGrid();
  }


  refreshGrid() {
    this.alertShown = false;
    this.entityId = this.entityCombo.selectedItem.content;
    this.currencyCode = this.ccyCombo.selectedItem.content;
    this.selectedStartDate = this.startDate.text;
    this.selectedEndDate = this.toDate.text;
    this.processOption = this.allProcess ? "A" : "M";
    this.singleOrRange = this.isSingleDate ? "S" : "R";
    if (this.validateReportDates()) {
      this.refreshdetails(null);
    }
  }

  updateData() {
    this.actionPath="intraDayLiquidity.do?method=";
    this.actionMethod="listCurrencyProcessStatus";
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    this.entityId = this.entityCombo.selectedItem.content;
    this.selectedStartDate = this.startDate.text;
    this.selectedEndDate = this.toDate.text;
    this.processOption = this.allProcess ? "A" : "M";
    this.singleOrRange = this.isSingleDate ? "S" : "R";
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    this.requestParams =[];
    this.requestParams["entityId"] = this.entityId;
    this.requestParams["currencyCode"] = this.currencyCode;
    this.requestParams["selectedStartDate"] = this.selectedStartDate;
    this.requestParams["selectedEndDate"] = this.selectedEndDate;
    this.requestParams["processOption"] = this.processOption;
    this.requestParams["singleOrRange"] = this.singleOrRange;
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }


  cancelCalculation() {
    this.canceled = true;
    this.entityId = this.entityCombo.selectedItem.content;
    this.currencyCode = this.ccyCombo.selectedItem.content;
    this.selectedStartDate = this.startDate.text;
    this.selectedEndDate = this.toDate.text;
    this.processOption = this.allProcess ? "A" : "M";
    this.singleOrRange = this.isSingleDate ? "S" : "R";
    ExternalInterface.call("cancelCalculation", this.entityId,  this.currencyCode, this.selectedStartDate, this.selectedEndDate,this.processOption,this.singleOrRange,this.singleOrRange );

  }

 checkValidationDates(startDate: string,endDate: string,singleOrRange: string): boolean {
    if("R" == singleOrRange) {
      if((startDate == null || startDate == "" || startDate.length < 10) || (endDate == null || endDate == "" || endDate.length < 10)) {
        return false;
      }

      if(ExternalInterface.call("compareTwoDates", this.selectedStartDate, this.selectedEndDate, this.dateFormat ) == false) {
        return false;
      }
    } else {
      if(startDate == null || startDate == "" || startDate.length < 10) {
        return false;
      }
    }
    return true;
  }


  /**
   * This method is used to handle the entity change.
   * This loads the entity with the default currency.
   */
  entityComboChange(): void {
    this.labelStatus.color = "";
    this.labelStatus.text = "";
    this.selectedEntity.text = this.entityCombo.selectedItem.value;
    this.entityId =  this.entityCombo.selectedItem.content;
    this.currencyCode = "";
    this.alertShown = false;
    this.updateData();

  }


  /**
   * This method is used to handle the entity change.
   * This loads the entity with the default currency.
   */
  currencyComboChange(): void {
    this.selectedCcy.text = this.ccyCombo.selectedItem.value;
    this.refreshGrid();
  }



  refreshdetails(evt): void {
      this.entityId  = this.entityCombo.selectedItem.content;
      this.currencyCode =  this.ccyCombo.selectedItem.content;
      this.selectedStartDate = this.startDate.text;
      this.selectedEndDate  = this.toDate.text;
      this.processOption = this.allProcess ? "A" : "M";
      this.singleOrRange  = this.isSingleDate ? "S" : "R";
      this.sequenceNumber = ExternalInterface.call("eval", "sequenceNumber");

      if (!this.checkValidationDates(this.selectedStartDate,this.selectedEndDate,this.singleOrRange)) {
       // this.swtAlert.warning("Please enter a valid Date" );
        return;
      }
      this.inputData.cbResult= (event) => {
        this.inputDataRefresh(event);
      };
      this.inputData.cbFault= this.inputDataFault.bind(this);
      this.inputData.encodeURL=false;
      this.actionPath="intraDayLiquidity.do?method=";
      this.actionMethod="listCurrencyProcessStatus";

      this.selectedCcy.text = this.ccyCombo.selectedItem.value;
      this.selectedEntity.text = this.entityCombo.selectedItem.value;
      this.requestParams =[];
      this.requestParams["entityId"] = this.entityId;
      this.requestParams["currencyCode"] = this.currencyCode;
      this.requestParams["selectedStartDate"] = this.selectedStartDate;
      this.requestParams["selectedEndDate"] = this.selectedEndDate;
      this.requestParams["processOption"] = this.processOption;
      this.requestParams["singleOrRange"] = this.singleOrRange;
      this.requestParams["sequenceNumber"]= this.sequenceNumber;

      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

  }


  changeProcessOption(): void {
    this.labelStatus.color = "";
    this.labelStatus.text = "";
    this.allProcess = this.calculateGroup.selectedValue != "M";
    this.refreshGrid();
  }

  changeDateGroup(): void {
    this.labelStatus.color = "";
    this.labelStatus.text = "";
    if(this.dateGroup.selectedValue =='S') {
     // let selecteDefaultDate: string = ExternalInterface.call("eval","selecteDefaultDate");
      this.startDate.formatString =  this.dateFormatValue ;
      this.startDate.selectedDate = new Date(CommonUtil.parseDate(this.selecteDefaultDate, this.dateFormatValue .toUpperCase()));
      this.isSingleDate = true;
      this.toHbox.visible = false;
    } else {
      //let fromDateAsString: string = ExternalInterface.call("eval","fromDateAsString");
      //let toDateAsString: string = ExternalInterface.call("eval","toDateAsString");
      this.startDate.formatString =  this.dateFormatValue ;
      this.startDate.selectedDate = new Date(CommonUtil.parseDate(this.fromDateAsString, this.dateFormatValue .toUpperCase()));
      this.toDate.formatString =  this.dateFormatValue ;
      this.toDate.selectedDate = new Date(CommonUtil.parseDate(this.toDateAsString, this.dateFormatValue .toUpperCase()));
      this.isSingleDate = false;
      this.toHbox.visible = true;
    }
    this.refreshGrid();
  }


  ChangeDate(elem,fieldName) {
    this.labelStatus.color = "";
    this.labelStatus.text = "";
    if(this.testValidateDateField(elem,fieldName)) {
      this.refreshGrid();
    }
  }

  testValidateDateField(elem,fieldName) {
    let isValidDate = this.validateDateField(elem,fieldName,);
    if (isValidDate) {
      let results = this.testThresholdDates();
      if(!results[2]) {
        this.swtAlert.warning(SwtUtil.getPredictMessage('warn.outsideRange', null));
        this.resetOffendingDate(results);
        isValidDate = false;
      }
    }
    return isValidDate;
  }

  /**
   * doHelp
   *
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call('help');
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'IlmCurrencyCalculation', 'doHelp',   this.errorLocation);
    }
  }


  /**
   * Listener for Error alert message and
   * perform action for when click ok button
   * @param event:CloseEvent
   * */
  errorHandler(event): void {
    // check event click is ok button
    if (event.detail == Alert.OK) {
      if (this.closeWindow) {
        // closeHandler();
        this.closeWindow=false;
      }
    }
  }

}
// Define lazy loading routes
const routes: Routes = [
  { path: '', component: IlmCurrencyCalculation }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [IlmCurrencyCalculation],
  entryComponents: []
})
export class IlmCurrencyCalculationModule {}
