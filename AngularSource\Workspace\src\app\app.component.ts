import { Component, OnInit } from '@angular/core';
import { RouterOutlet, Router, ActivatedRoute } from '@angular/router';
import { log } from 'util';
import { ExternalInterface } from 'swt-tool-box';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.css'],
})

export class AppComponent implements OnInit {


  constructor(private router: Router, private route: ActivatedRoute) {
  }


  ngOnInit(): void {
    try {
      let screenUrl = ExternalInterface.call('eval', 'screenRoute');
      this.router.navigate([screenUrl]);
    } catch (e) {
      //console.log("e", e);

    }
  }

  getAnimationData(outlet: RouterOutlet) {
    return outlet && outlet.activatedRouteData;
  }
}
