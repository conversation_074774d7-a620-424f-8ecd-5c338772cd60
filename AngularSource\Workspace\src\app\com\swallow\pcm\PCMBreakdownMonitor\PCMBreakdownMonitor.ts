import {Component, ElementRef, NgModule, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import moment from "moment";
import 'rxjs/add/observable/interval';
import {
  Alert,
  CommonService,
  CommonUtil, ExportEvent,
  ExternalInterface,
  HBox,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCheckBox,
  SwtComboBox,
  SwtCommonGrid,
  SwtCommonGridPagination, SwtDataExport,
  SwtDateField,
  SwtLabel,
  SwtLoadingImage,
  SwtModule,
  SwtPopUpManager,
  SwtRadioButtonGroup,
  SwtRadioItem,
  SwtTabNavigator,
  SwtTextInput,
  SwtToolBoxModule,
  SwtUtil,
  Tab,
  Timer
} from 'swt-tool-box';
import { ListValues } from "../ListValues/ListValues";
import { CategoryList } from "./popUp/CategoryList/CategoryList";
import { ConfirmRelease } from './popUp/ConfirmRelease/ConfirmRelease';
import { ErrorsUpdatePayments } from "./popUp/ErrorsUpdatePayments/ErrorsUpdatePayments";
import { ExportPages } from "./popUp/ExportPages/ExportPages";
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";


declare var instanceElement: any;
@Component({
  selector: 'app-pcdashboard-details',
  templateUrl: './PCMBreakdownMonitor.html',
  styleUrls: ['./PCMBreakdownMonitor.css']
})
export class PCMBreakdownMonitor extends  SwtModule implements OnInit {
  /*********SwtCanvas*********/
  @ViewChild('gridCanvas') gridCanvas: SwtCanvas;
  @ViewChild('totalCanvas') totalCanvas: SwtCanvas;
  @ViewChild('globalCanvas') globalCanvas: SwtCanvas;
  /*********Combobox*********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('statusCombo') statusCombo: SwtComboBox;
  @ViewChild('accountCombo') accountCombo: SwtComboBox;
  @ViewChild('blokedCombo') blokedCombo: SwtComboBox;
  @ViewChild('acctGrpCombo') acctGrpCombo: SwtComboBox;
  /*********SwtLabel*********/
  @ViewChild('applyCurrencyLabel') applyCurrencyLabel: SwtLabel;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('acagLabel') acagLabel: SwtLabel;
  @ViewChild('accountLabel') accountLabel: SwtLabel;
  @ViewChild('statusLabel') statusLabel: SwtLabel;
  @ViewChild('dateLabel') dateLabel: SwtLabel;
  @ViewChild('startDate') startDate: SwtDateField;
  @ViewChild('inputSince') inputSince: SwtDateField;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedAccount') selectedAccount: SwtLabel;
  @ViewChild('selectedAcctGrp') selectedAcctGrp: SwtLabel;
  @ViewChild('blokedLabel') blokedLabel: SwtLabel;
  @ViewChild('inputSinceLabel') inputSinceLabel: SwtLabel;
  @ViewChild('applyAbsoluteLabel') applyAbsoluteLabel: SwtLabel;
  @ViewChild('timeFrameLabel') timeFrameLabel: SwtLabel;
  @ViewChild('entityRadio') entityRadio: SwtLabel;
  @ViewChild('currencyRadio') currencyRadio: SwtLabel;
  @ViewChild('systemRadio') systemRadio: SwtLabel;
  @ViewChild('sodLabel') sodLabel: SwtLabel;
  @ViewChild('confirmedLabel') confirmedLabel: SwtLabel;
  @ViewChild('creditLabel') creditLabel: SwtLabel;
  @ViewChild('releasedPayLabel') releasedPayLabel: SwtLabel;
  @ViewChild('otherPaymentsLabel') otherPaymentsLabel: SwtLabel;
  @ViewChild('excludeCLlabel') excludeCLlabel: SwtLabel;
  @ViewChild('includeCLlabel') includeCLlabel: SwtLabel;
  @ViewChild('reservelabel') reservelabel: SwtLabel;
  /*********SwtTextInput*********/
  @ViewChild('sodText') sodText: SwtTextInput;
  @ViewChild('confirmedText') confirmedText: SwtTextInput;
  @ViewChild('creditText') creditText: SwtTextInput;
  @ViewChild('releasedPayText') releasedPayText: SwtTextInput;
  @ViewChild('otherPaymentsText') otherPaymentsText: SwtTextInput;
  @ViewChild('excludeCLText') excludeCLText: SwtTextInput;
  @ViewChild('includeCLText') includeCLText: SwtTextInput;
  @ViewChild('reserveText') reserveText: SwtTextInput;

  /***Checkbox**********/
  @ViewChild('applyCurrencyCheck') applyCurrencyCheck: SwtCheckBox;
  @ViewChild('applyAbsoluteCheck') applyAbsoluteCheck: SwtCheckBox;

  /*********SwtButton*********/
  @ViewChild('releaseButton') releaseButton: SwtButton;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('displayButton') displayButton: SwtButton;
  @ViewChild('changeCatgButton') changeCatgButton: SwtButton;
  @ViewChild('spreadButton') spreadButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('upButton') upButton: SwtButton;
  @ViewChild('downButton') downButton: SwtButton;
  @ViewChild('settingButton') settingButton: SwtButton;
  @ViewChild('rejectButton') rejectButton: SwtButton;
  @ViewChild('unStopButton') unStopButton: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  @ViewChild('printIcon') printIcon: SwtButton;
  @ViewChild('highValueButton') highValueButton: SwtButton;
  @ViewChild('trimButton') trimButton: SwtButton;
  @ViewChild('entityMoreItemsButton') entityMoreItemsButton: SwtButton;
  /*******tabNavigator*******/
  @ViewChild('tabs') tabs: SwtTabNavigator;
  /*************HBox************************/
  @ViewChild('blokedHbox') blokedHbox: HBox;
  @ViewChild('inputSinceHbox') inputSinceHbox: HBox;
  @ViewChild('hboxTextInput') hboxTextInput: HBox;
  /*********************  commonGridPa********/
  @ViewChild('numstepper') numstepper: SwtCommonGridPagination ;
  /******RadiButtons**********/
  @ViewChild('timeFrameRadioGroup') timeFrameRadioGroup: SwtRadioButtonGroup ;
  @ViewChild('radioC') radioC: SwtRadioItem ;
  @ViewChild('radioE') radioE: SwtRadioItem ;
  @ViewChild('radioS') radioS: SwtRadioItem ;
  @ViewChild('dataExport') dataExport: SwtDataExport ;



  private commonGrid: SwtCommonGrid;
  private inputData = new HTTPComms(this.commonService);
  private widthData = new HTTPComms(this.commonService);
  private ordertData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  private actionPath;
  private actionMethod;
  private baseURL = SwtUtil.getBaseURL();
  private requestParams;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  private invalidComms:string = null;
  private errorLocation = 0;
  private comboOpen = false;
  private comboChange =  false;
  //public currentDate: string = null;
  public firstLoad: boolean = true;

  public moduleId = 'PC';
  public status: string = null;
  public ccyFromPrevScreen: string= null;
  public acctGrpFromPrevScreen: string = null;
  public acctFromPrevScreen: string = null;
  public account:string = null;
  public entityFromPrevScreen:string = null;
  public listEntitiesFromPrevScreen:string = null;
  public valueDateFromPrevScreen:string = null;
  public initialFilterFromPrevScreen:string = null;
  public refFilterFromPrevScreen:string = null;
  public archiveFromPrevScreen:string = "";
  public spreadOnlyFromPrevScreen:string = "N";
  public ccyThresholdFromPrevScreen:string = "N";
  public ccyMultiplierFromPrevScreen:string = "N";
  public timeFrameFromPrevScreen:string = "E";
  public inputSinceFromPrevScreen:string = "";
  public bVFromPrevScreen: boolean = false;
  public currencyCode:string = null;
  /**Dynamic tabs***/
  public currentDateTab: Tab;
  public currentDateTabPlusOne: Tab;
  public currentDateTabPlusTwo: Tab;
  public currentDateTabPlusThree: Tab;
  public currentDateTabPlusFour: Tab;
  public currentDateTabPlusFive: Tab;
  public currentDateTabPlusSix: Tab;
  public currentDateTabPlusSeven: Tab;
  public selectedDateTab: Tab;
  public maxPage: number = 0;
  public lastNumber: number = 0;
  public win: any;
  public isDisplayClicked: boolean = false;
  public isSpreadClicked : boolean = false;
  public fromSummary: boolean = false;
  public arrayOftabs = [];
  public sysDateFrmSession: string;
  private dateFormat : string;
  private formatIso : string = "yyyy-mm-dd";
  private formatIsoTime : string = "yyyy-mm-dd hh24:mi:ss";
  public dateFormatUpper : string;
  private applyAbsLabel : string;
  public spreadId: string = null;
  public selectedFilter: string = null;
  public selectedSort : string = null;
  public pageSize: number = 1;
  public screenName: string = "";
  public previousStatus: string = "";
  public statusChanged: boolean = false;
  public isDateChanged : boolean = false;
  public autoRefresh: Timer = null;
  public refreshRate: number;
  private interval = null;
  public refusedStatus = [];
  public totalSelectedPayments : number ;
  public count=2;
  public isReleaseButtonClicked: boolean = false;

  public userFilter : string = null;
  public order : string = null;
  public ascDesc: string = null;
  public selectedItemsListEntity: string = null;
  public selectedItemsList: string = null;
  public isBlockedStopped: string = null;
  private arrayStatusPayments = [];
  private blockedPaymentsId = [];
  public paySelectedIndex: number;
  private  columnsNewWidths : string ="";
  private  columnsNewOrders : string ="";
  private columnDefinitionsTempArray = [];
  public indexSelectedPay: number;
  public resetStepper: boolean = false;
  public previousCcy: string;
  public previousAcctGrp: string;
  public menuAccessId: number = 2;
  private entityChanged = false;

  //private activatedRoute:ActivatedRoute,
  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  ngOnInit(): void {
    instanceElement = this;
    this.commonGrid = <SwtCommonGrid>this.gridCanvas.addChild(SwtCommonGrid);
    this.commonGrid.paginationComponent = this.numstepper;
    this.commonGrid.onPaginationChanged = this.updataDataWithPagination.bind(this);
    this.commonGrid.onFilterChanged = this.updateData.bind(this);
    this.commonGrid.onSortChanged = this.updateData.bind(this);
    this.commonGrid.columnWidthChanged.subscribe((event) => {
      this.columnWidthChange(event);
    });
    this.commonGrid.columnOrderChanged.subscribe((event) => {
      this.columnOrderChange(event);
    });
    this.commonGrid.clientSideFilter = false;
    this.commonGrid.clientSideSort = false;
    this.commonGrid.allowMultipleSelection = true;
    /**Text and tooltip*********/
    this.currencyLabel.text = SwtUtil.getPredictMessage('dashboardDetails.currency.label', null);
    this.ccyCombo.toolTip = SwtUtil.getPredictMessage('dashboardDetails.currency.tooltip', null);
    this.entityLabel.text = SwtUtil.getPredictMessage('dashboardDetails.entity.label', null);
    this.entityCombo.toolTip = SwtUtil.getPredictMessage('dashboardDetails.entity.tooltip', null);
    this.acagLabel.text = SwtUtil.getPredictMessage('dashboardDetails.accountGroup.label', null);
    this.acctGrpCombo.toolTip = SwtUtil.getPredictMessage('dashboardDetails.accountGroup.tooltip', null);
    this.accountLabel.text = SwtUtil.getPredictMessage('dashboardDetails.account.label', null);
    this.accountCombo.toolTip = SwtUtil.getPredictMessage('dashboardDetails.account.tooltip', null);
    this.statusLabel.text = SwtUtil.getPredictMessage('dashboardDetails.status.label', null);
    this.statusCombo.toolTip = SwtUtil.getPredictMessage('dashboardDetails.status.tooltip', null);
    this.blokedLabel.text = SwtUtil.getPredictMessage('dashboardDetails.blocked.label', null);
    this.inputSinceLabel.text = SwtUtil.getPredictMessage('dashboardDetails.inputSince.label', null);
    this.inputSince.toolTip = SwtUtil.getPredictMessage('dashboardDetails.inputSince.tooltip', null);
    this.blokedCombo.toolTip = SwtUtil.getPredictMessage('dashboardDetails.blocked.tooltip', null);
    this.dateLabel.text = SwtUtil.getPredictMessage('dashboardDetails.date.label', null);
    this.startDate.toolTip = SwtUtil.getPredictMessage('dashboardDetails.date.tooltip', null) + '' + this.dateFormatUpper;
    this.applyCurrencyLabel.text = SwtUtil.getPredictMessage('dashboardDetails.currencyThreshold.label', null);
    this.applyAbsoluteLabel.text = SwtUtil.getPredictMessage('dashboardDetails.currencyMultiplier.label', null);
    this.timeFrameLabel.text = SwtUtil.getPredictMessage('dashboardDetails.timeFrame.label', null);
    this.entityRadio.text = SwtUtil.getPredictMessage('dashboardDetails.radioEntity.label', null);
    this.currencyRadio.text = SwtUtil.getPredictMessage('dashboardDetails.radioCurrency.label', null);
    this.systemRadio.text = SwtUtil.getPredictMessage('dashboardDetails.radiosystem.label', null);
    this.sodLabel.text = SwtUtil.getPredictMessage('dashboardDetails.startDayBalance.label', null);
    this.confirmedLabel.text = SwtUtil.getPredictMessage('dashboardDetails.confirmedCredits.label', null);
    this.creditLabel.text = SwtUtil.getPredictMessage('dashboardDetails.creditLine.label', null);
    this.releasedPayLabel.text = SwtUtil.getPredictMessage('dashboardDetails.releasedPayments.label', null);
    this.otherPaymentsLabel.text = SwtUtil.getPredictMessage('dashboardDetails.otherPayments.label', null);
    this.excludeCLlabel.text = SwtUtil.getPredictMessage('dashboardDetails.availableLiquidityEx.label', null);
    this.includeCLlabel.text = SwtUtil.getPredictMessage('dashboardDetails.availableLiquidityInc.label', null);
    this.reservelabel.text = SwtUtil.getPredictMessage('dashboardDetails.reserve.label', null);
    this.displayButton.label = SwtUtil.getPredictMessage('dashboardDetails.buttonDisplay.label', null);
    this.displayButton.toolTip = SwtUtil.getPredictMessage('dashboardDetails.buttonDisplay.tooltip', null);
    this.releaseButton.label = SwtUtil.getPredictMessage('dashboardDetails.buttonRelease.label', null);
    this.releaseButton.toolTip = SwtUtil.getPredictMessage('dashboardDetails.buttonRelease.tooltip', null);
    this.spreadButton.label = SwtUtil.getPredictMessage('dashboardDetails.spreadDisplay.label', null);
    this.spreadButton.toolTip = SwtUtil.getPredictMessage('dashboardDetails.spreadDisplay.tooltip', null);
    this.unStopButton.label = SwtUtil.getPredictMessage('dashboardDetails.unStopButton.label', null);
    this.unStopButton.toolTip = SwtUtil.getPredictMessage('dashboardDetails.unStopButton.tooltip', null);
    this.changeCatgButton.label = SwtUtil.getPredictMessage('dashboardDetails.changeCatButton.label', null);
    this.changeCatgButton.toolTip = SwtUtil.getPredictMessage('dashboardDetails.changeCatButton.tooltip', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refresh', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.printIcon.toolTip = SwtUtil.getPredictMessage('tooltip.print', null);
    ExportEvent.subscribe((type) => {
      this.report(type)
    });

// get params from parent window
    if(window.opener) {
      if(window.opener.instanceElement ) {
        let params = (window.opener.instanceElement.getParamsFromParent) ?  window.opener.instanceElement.getParamsFromParent() : "";
        if(params) {
          this.screenName= ( params[0].screenName) ? params[0].screenName : "grid";
          this.status= ( params[0].status) ? params[0].status : "W";
          this.ccyFromPrevScreen = (params[0].currencyCode) ? params[0].currencyCode : "All";
          this.acctGrpFromPrevScreen = (params[0].accountGroup) ?  params[0].accountGroup : "All";
          this.acctFromPrevScreen = (params[0].account) ? params[0].account : "All";
          this.entityFromPrevScreen = (params[0].entity) ? params[0].entity : "All";
          this.listEntitiesFromPrevScreen = (params[0].listEntity) ? params[0].listEntity : "";
          this.valueDateFromPrevScreen = (params[0].valueDate) ? params[0].valueDate : "";
          this.initialFilterFromPrevScreen = (params[0].initialFilter) ? params[0].initialFilter : "";
          this.refFilterFromPrevScreen = (params[0].refFilter) ? params[0].refFilter : "";
          this.archiveFromPrevScreen = (params[0].archive) ? params[0].archive : "";
          this.spreadOnlyFromPrevScreen = (params[0].spreadOnly) ? params[0].spreadOnly : "N";
          this.ccyThresholdFromPrevScreen = (params[0].ccyThreshold) ? params[0].ccyThreshold : "N";
          this.ccyMultiplierFromPrevScreen = (params[0].ccyMultiplier) ? params[0].ccyMultiplier : "N";
          this.timeFrameFromPrevScreen = (params[0].timeFrame) ? params[0].timeFrame : "E";
          this.inputSinceFromPrevScreen = (params[0].inputSince) ? params[0].inputSince : "";
          this.bVFromPrevScreen = (params[0].isBackValueClicked) ? params[0].isBackValueClicked : false;


        }

      }
    }
    this.previousStatus = this.status;
    this.previousCcy = this.ccyFromPrevScreen;
    this.previousAcctGrp = this.acctGrpFromPrevScreen;


  }
  ngOnDestroy(): any {
    instanceElement = null;
  }

  onLoad() {

    this.applyAbsLabel = this.applyAbsoluteLabel.text;
    this.applyCurrencyCheck.selected = (this.ccyThresholdFromPrevScreen == "Y");
    this.applyAbsoluteCheck.selected = (this.ccyMultiplierFromPrevScreen == "Y");
    if(this.timeFrameFromPrevScreen == "C") {
      this.radioC.selected = true;
    }else if(this.timeFrameFromPrevScreen == "S") {
      this.radioS.selected = true;
    } else {
      this.radioE.selected = true
    }

    if(this.listEntitiesFromPrevScreen != "") {
      //this.entityFromPrevScreen = this.listEntitiesFromPrevScreen;
      this.selectedEntity.text = this.listEntitiesFromPrevScreen;
      this.selectedItemsListEntity = this.listEntitiesFromPrevScreen;
    }

    this.requestParams = [];
    if(this.bVFromPrevScreen ) {
      this.inputSinceHbox.visible = true;
      this.inputSince.text= this.inputSinceFromPrevScreen;
      this.valueDateFromPrevScreen = "";
      this.requestParams["blockedReason"] = "BV"
    }
    //this.enableDisableButtonsByStatus();
    this.actionMethod = 'method=getDashboardDetails';
    this.actionPath = 'dashboardPCM.do?';
    this.requestParams['method']= 'getDashboardDetails';
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    if(this.screenName != "") {
      this.requestParams['currencyCode'] = this.ccyFromPrevScreen;
      this.requestParams['accountGroup'] = this.acctGrpFromPrevScreen;
      this.requestParams['account'] = this.acctFromPrevScreen;
      this.requestParams['entity'] = this.entityFromPrevScreen;
      this.requestParams['status'] = this.status;
      this.requestParams['date'] = this.valueDateFromPrevScreen;
    } else {
      this.requestParams['currencyCode'] = 'All';
      this.requestParams['accountGroup'] = 'All';
      this.requestParams['account'] = 'All';
      this.requestParams['entity'] = 'All';
      this.requestParams['status'] = "W";
     // this.requestParams['date'] = '10/01/2008';
    }
    if(this.screenName == "search") {
      this.requestParams['initialFilter'] = this.initialFilterFromPrevScreen;
      this.requestParams['refFilter'] = this.refFilterFromPrevScreen;
      this.requestParams['archive'] = this.archiveFromPrevScreen;
    }
    this.requestParams['applyCurrencyThreshold'] = this.ccyThresholdFromPrevScreen;
    this.requestParams['spreadOnly'] = this.spreadOnlyFromPrevScreen;
    this.requestParams['absoluteAmount'] = this.ccyMultiplierFromPrevScreen;
    this.requestParams['fromScreen'] = this.screenName;
    this.requestParams['timeFrame'] = this.timeFrameFromPrevScreen;
    this.requestParams['inputSince'] = this.inputSinceFromPrevScreen;
    this.inputData.cbFault = this.inputDataFault;
    this.inputData.encodeURL = false;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.commonGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    };

  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.dataExport.enabled = false;
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.dataExport.enabled = true;
  }

  inputDataResult(event) {
    let jsonList = null;
    let header: string;
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    }
    else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
        if (this.jsonReader.getRequestReplyStatus()) {
          if (!this.jsonReader.isDataBuilding()) {
            this.menuAccessId = this.jsonReader.getScreenAttributes()["menuaccess"];
            this.sysDateFrmSession = this.jsonReader.getSingletons().sysDateFrmSession;
            if(this.jsonReader.getSingletons().multiplier) {
              this.applyAbsoluteLabel.text = this.applyAbsLabel + " "+ this.jsonReader.getSingletons().multiplier;
            } else {
              this.applyAbsoluteLabel.text = this.applyAbsLabel;
            }

            this.dateFormat = this.jsonReader.getScreenAttributes()["dateformat"];
            this.dateFormatUpper = this.dateFormat.toUpperCase();
            this.startDate.toolTip = SwtUtil.getPredictMessage('dashboardDetails.date.tooltip', null) + '' + this.dateFormatUpper;
            this.startDate.formatString = this.dateFormat.toLowerCase();
            this.fillComboData();
            this.handleFieldsDisplay();
            /*reset order when changing status*/
            if(this.previousStatus != this.statusCombo.selectedItem.value ) {
              this.columnsNewOrders = "";
              this.columnsNewWidths = "";
            }
            /*****tabs display***********/
            if (this.tabs.getTabChildren().length ==0  ||  this.entityChanged || (["R", "S", "C", "W", "All"].indexOf(this.previousStatus) > -1 &&  this.statusCombo.selectedItem.value == "B") || (["R", "S", "C", "W", "All"].indexOf(this.statusCombo.selectedItem.value) > -1  &&  this.previousStatus == "B" )) {
              if(this.tabs.getTabChildren().length > 0) {
                for(let i =0; i < this.arrayOftabs.length; i++) {
                  this.tabs.removeChild(this.arrayOftabs[i] );
                }
              }

              this.entityChanged = false;
              this.currentDateTab = <Tab> this.tabs.addChild(Tab);
              this.currentDateTabPlusOne = <Tab> this.tabs.addChild(Tab);
              this.currentDateTabPlusTwo = <Tab> this.tabs.addChild(Tab);
              this.currentDateTabPlusThree = <Tab> this.tabs.addChild(Tab);
              this.currentDateTabPlusFour = <Tab> this.tabs.addChild(Tab);
              this.currentDateTabPlusFive = <Tab> this.tabs.addChild(Tab);
              this.currentDateTabPlusSix = <Tab> this.tabs.addChild(Tab);
              this.currentDateTabPlusSeven = <Tab> this.tabs.addChild(Tab);
              this.selectedDateTab = <Tab> this.tabs.addChild(Tab);
              this.arrayOftabs = [this.currentDateTab, this.currentDateTabPlusOne, this.currentDateTabPlusTwo, this.currentDateTabPlusThree,
                this.currentDateTabPlusFour, this.currentDateTabPlusFive, this.currentDateTabPlusSix, this.currentDateTabPlusSeven, this.selectedDateTab];

              for (let i = 0; i < this.lastRecievedJSON.DashboardLevel2.tabs.row.length; i++) {
                this.arrayOftabs[i].label = this.lastRecievedJSON.DashboardLevel2.tabs.row[i].dateLabel;
                this.arrayOftabs[i].businessday = this.lastRecievedJSON.DashboardLevel2.tabs.row[i].businessday;
                this.arrayOftabs[i].year = this.lastRecievedJSON.DashboardLevel2.tabs.row[i].content.substr(0,4);
                if(this.arrayOftabs[i].businessday ==false){
                  this.arrayOftabs[i].setTabHeaderStyle("color","darkgray");
                } else
                  this.arrayOftabs[i].setTabHeaderStyle("color","black");
              }
              this.arrayOftabs[8].label = SwtUtil.getPredictMessage('dashboard.selected.label', null);
            }
            setTimeout(() => {
              this.handleDate();
              if(!this.jsonReader.getSingletons().valueDate) {
                this.startDate.text = "";
                this.tabs.selectedIndex = 8;
              }
            }, 0);


            this.previousStatus = this.statusCombo.selectedItem.value;
            this.previousCcy = this.ccyCombo.selectedItem.value;
            this.previousAcctGrp = this.acctGrpCombo.selectedItem.value;

            /*****grid display***********/
            const obj = {columns: this.jsonReader.getColumnData()};
            this.commonGrid.CustomGrid(obj);
            if (this.jsonReader.getGridData()) {
              if (this.jsonReader.getGridData().size) {
                this.commonGrid.gridData = this.jsonReader.getGridData();
                 this.commonGrid.setRowSize = this.jsonReader.getRowSize();

              } else {
                this.commonGrid.dataProvider = [];
                //this.commonGrid.setRowSize = 0;
               // this.commonGrid.gridData= { row: {}, size: 0};
              }
            }else {
              this.commonGrid.dataProvider = [];
            }
            if(this.paySelectedIndex) {
              this.indexSelectedPay= this.commonGrid.gridData.findIndex(x=> x.payreq_id == this.paySelectedIndex);
              if(this.indexSelectedPay >=0) {
                this.commonGrid.selectedIndex = this.indexSelectedPay;
              }else {
                this.commonGrid.selectedIndex = -1;
                this.disableButtons()
              }
            } else {
              this.commonGrid.selectedIndex = -1;
            }

            if(this.jsonReader.getRowSize() > 0) {
              this.pageSize =Number(this.jsonReader.getSingletons().pageSize);
              this.maxPage= this.jsonReader.getRowSize()/this.pageSize;
              if(this.maxPage < 1) {
                this.maxPage = 1;
              } else {
                this.maxPage= Math.ceil(this.maxPage);
              }
              this.numstepper.processing = false;
              this.numstepper.maximum=Number(this.maxPage);
              if (Number(this.maxPage) > 0)
              {
                this.numstepper.minimum=1;
              }
              else
              {
                this.numstepper.minimum=0;
              }
            } else {
              this.dataExport.enabled= false;
              this.numstepper.value=0;
              this.numstepper.maximum = 0;
            }
            /**AutoRefresh******************/
            this.refreshRate =  this.jsonReader.getSingletons().refresh; //60
            if(this.refreshRate == 0) {
              this.refreshRate = 60;
            }
            let timeLeft = this.refreshRate;
            clearInterval(this.interval);
            this.interval = setInterval(() => {
              this.updataDataWithPagination();
            }, timeLeft*1000)
          }
        }else{
          this.swtAlert.error(SwtUtil.getCommonMessages('alert.generic_exception'));
        }
      }
    }
  }

  fillComboData(): void {
    this.ccyCombo.setComboData( this.jsonReader.getSelects(), false);
      this.entityCombo.setComboData( this.jsonReader.getSelects(), false);
      this.acctGrpCombo.setComboData(this.jsonReader.getSelects(), false);
      this.accountCombo.setComboData(this.jsonReader.getSelects(), false);
      this.statusCombo.setComboData(this.jsonReader.getSelects(), true);
      this.blokedCombo.setComboData(this.jsonReader.getSelects(), true);

      /*********Labels********/
      //this.statusCombo.selectedLabel = this.jsonReader.getSingletons().status[0].toUpperCase() + this.jsonReader.getSingletons().status.slice(1);
      //this.selectedEntity.text = this.entityCombo.selectedItem.value;
      this.selectedCcy.text = this.ccyCombo.selectedItem.value;
      this.selectedAcctGrp.text =  this.acctGrpCombo.selectedItem.value;
      this.selectedAccount.text = this.accountCombo.selectedItem.value;
      this.startDate.text= this.jsonReader.getSingletons().valueDate;
      if(this.inputSinceHbox.visible ) {
        this.inputSince.formatString = this.dateFormat.toLowerCase();
        this.inputSince.text= this.jsonReader.getSingletons().inputSince;
        this.blokedCombo.selectedValue = "BV";
      }


    if(this.jsonReader.getSingletons().selectedEntity.indexOf(',') != -1){
      this.entityMoreItemsButton.enabled = true;
      this.selectedEntity.text = this.get2FirstItemsFromList(this.jsonReader.getSingletons().selectedEntity);
      //this.selectedEntity.toolTip = this.jsonReader.getSingletons().selectedEntity;
    }else{
      this.entityMoreItemsButton.enabled = false;
      this.selectedEntity.text = this.entityCombo.selectedItem.value;
    }





  }
  /**
   * filteredGridColumns
   *
   *
   * This function is used to get the filtered grid columns
   */
  private  getfilteredGridColumns():string
  {

    var selectedFilter: string = "";
    var filteredColumnsFields=[];
    var gridColumns=this.commonGrid.getFilterColumns();
    var filteredColumns=this.commonGrid.filteredGridColumns;
    try
    {
      // Iterate for gridColumns
      for (var i=0; i < gridColumns.length; i++)
      {
        filteredColumnsFields[i] = gridColumns[i].field;
      }
      if(filteredColumns != ''){
        let filterdValues = filteredColumns.split('|');
        for (var i=0; i < filteredColumnsFields.length; i++)
        {
    if(filterdValues[i] != "") {
      if(filterdValues[i] != "All" && filterdValues[i] != undefined) {
        var underscoreExist: string = filteredColumnsFields[i][filteredColumnsFields[i].length -1] ;
        if (underscoreExist == "_" ) {
          filteredColumnsFields[i] = filteredColumnsFields[i].slice(0, -1);
        }
          if( filteredColumnsFields[i] == "value_date" || filteredColumnsFields[i] == "release_date" || filteredColumnsFields[i] == "input_date" || filteredColumnsFields[i] == "stop_date" || filteredColumnsFields[i] == "cancel_date") {
          //to be reviewed when columntype is date time
          selectedFilter= selectedFilter  +  filteredColumnsFields[i] + "="  + "TO_DATE ('"+filterdValues[i]  +"' , '"+this.formatIsoTime+ "')" + " and " ;
        }
        else if( filteredColumnsFields[i] == "required_release_time"  || filteredColumnsFields[i] == "cutoff_time") {
          selectedFilter= selectedFilter  + "to_char(" +  filteredColumnsFields[i] + ", 'hh24:mi')="   +"'"+ filterdValues[i].substring(11,16) + "'"+ " and " ;
        }
        else
        selectedFilter= selectedFilter + filteredColumnsFields[i] + "=" +"'"+ filterdValues[i]+ "'" +" and ";
      }
    }
    /*else {
      selectedFilter= selectedFilter + filteredColumnsFields[i] + " is null" +" and ";

    }*/
        }
      }

      selectedFilter = selectedFilter.substring(0, selectedFilter.length - 5);
      return selectedFilter;
    }
    catch (error)
    {
      console.log('error', error)
    }
  }
  convertStringDateToIso(str: string): string {
    let date = moment(str, this.dateFormat.toUpperCase() , true).utcOffset('+0100').toDate();

     return CommonUtil.formatDate(date, "YYYY-MM-DD") ;
  }
handleFieldsDisplay(): void {
    if (this.statusCombo.selectedItem &&  this.ccyCombo.selectedItem){
    if(this.statusCombo.selectedItem.value == 'W' && this.startDate.text == this.sysDateFrmSession  && this.ccyCombo.selectedItem.value != "All") {
      this.hboxTextInput.visible = true;
      this.sodText.text = (this.jsonReader.getSingletons().sodBalance != "") ? this.jsonReader.getSingletons().sodBalance : '';
      this.confirmedText.text = (this.jsonReader.getSingletons().confirmedCredit) ? this.jsonReader.getSingletons().confirmedCredit : '';
      this.creditText.text = (this.jsonReader.getSingletons().creditLine) ? this.jsonReader.getSingletons().creditLine : ' ';
      this.releasedPayText.text = (this.jsonReader.getSingletons().releasePayment) ? this.jsonReader.getSingletons().releasePayment : '';
      this.otherPaymentsText.text = (this.jsonReader.getSingletons().otherPayments) ? this.jsonReader.getSingletons().otherPayments : '';
      this.includeCLText.text = (this.jsonReader.getSingletons().incCreditLine) ? this.jsonReader.getSingletons().incCreditLine : '' ;
      this.excludeCLText.text = (this.jsonReader.getSingletons().exCreditLine) ? this.jsonReader.getSingletons().exCreditLine :  '';
      this.reserveText.text = (this.jsonReader.getSingletons().reserveBalanced) ?  this.jsonReader.getSingletons().reserveBalanced: '';
    } else {
      this.hboxTextInput.visible = false;
    }
    if(this.statusCombo.selectedItem.value == 'B') {
      this.blokedHbox.visible= true;
      if(this.blokedCombo.selectedItem.value =="BV")
        this.inputSinceHbox.visible = true;
      else
        this.inputSinceHbox.visible = false
    }else{
      this.blokedHbox.visible= false;
      this.inputSinceHbox.visible = false
    }
  }

}
validateDate(dateField) : void {
  if(this.validateDateField(dateField)){
    this.updateData(null);
  } else {

  }
}
  handleDate() :void {
    let exist = false;
    let firstDateInBlocked = this.arrayOftabs[0].label + "/"+ this.arrayOftabs[0].year;
    let tempDate= moment(this.sysDateFrmSession, this.dateFormat.toUpperCase());
    let selectedDate = moment(this.startDate.text, this.dateFormat.toUpperCase());
    if(selectedDate) {
      for (let index = 0; index < 8; index++) {
        if(this.statusCombo.selectedValue == "B") {
          tempDate= moment(firstDateInBlocked, this.dateFormat.toUpperCase()).add(index, 'days');
        } else {
          tempDate= moment(this.sysDateFrmSession, this.dateFormat.toUpperCase()).add(index, 'days');
        }

        if(selectedDate.diff(tempDate) == 0){
          exist = true;
          this.tabs.selectedIndex = index;
          break;
        }
      }
    }
    if(!exist){
      this.tabs.selectedIndex = 8;
    }
  }
      validateDateField(dateField){
        try{
          let date;
          let alert = SwtUtil.getPredictMessage('dashboardDetails.alert.dateFormat', null);
          if(dateField.text) {

            date = moment(dateField.text, this.dateFormat.toUpperCase() , true);

            if(!date.isValid()) {
              this.swtAlert.warning(alert+ this.dateFormat.toUpperCase());
              return false;
            }
          }
          dateField.selectedDate = date.toDate();
        }catch(error){
          SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 'PCDashboard', ' validateDateField', this.errorLocation);
        }

        return true;
      }

  /**
   * cellClickEventHandler
   * @param event: Event
   * This method is used to maintain the button status when a row is clicked
   */
  cellClickEventHandler(event):void {
    try {
      if(this.commonGrid.selectedIndex >=0) {
        this.enableDisableButtonsByStatus();
        if (this.commonGrid.selectedIndices.length == 1) {
          //get the pay id to reselect it after refresh
          this.paySelectedIndex = this.commonGrid.dataProvider[this.commonGrid.selectedIndex].payreq_id;
          this.spreadButton.enabled = true;
          this.displayButton.enabled = true;
          this.printIcon.enabled = true;

        } else if (this.commonGrid.selectedIndices.length > 1) {
          //stop timer when selecting multiple pay
          clearInterval(this.interval);
          this.paySelectedIndex = null;
          this.spreadButton.enabled = false;
          this.displayButton.enabled = false;
          this.changeCatgButton.enabled = false;
          this.printIcon.enabled = false;
        }
       } else {
        this.paySelectedIndex = null;
        this.spreadButton.enabled = false;
        this.displayButton.enabled = false;
        this.changeCatgButton.enabled = false;
        this.printIcon.enabled = false;
        this.releaseButton.enabled = false;
        this.unStopButton.enabled = false;
      }


    } catch (error)
    {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'cellClickEventHandler', this.errorLocation);
    }
  }

  columnWidthChange(event): void {
    this.columnsNewWidths = "";
    let gridColumns = this.commonGrid.gridObj.getColumns();
    for (let i=0; i< gridColumns.length; i++) {
      if(gridColumns[i].id != "dummy")
      this.columnsNewWidths = this.columnsNewWidths + gridColumns[i].field + "=" + gridColumns[i].width + ',';
    }
    this.columnsNewWidths = this.columnsNewWidths.substring(0, this.columnsNewWidths.length - 1);
    this.requestParams=[];
    this.widthData.encodeURL = false;
    this.actionMethod = 'method=saveColumnWidthBreakDownScreen';
    this.actionPath = 'dashboardPCM.do?';
    this.requestParams['method']= 'saveColumnWidthBreakDownScreen';
    this.requestParams['width'] = this.columnsNewWidths;
    if(this.entityCombo.selectedLabel == "<Multiple values>" && this.selectedItemsListEntity != null) {
      this.requestParams['entityid'] = this.selectedItemsListEntity;
    } else {
      this.requestParams['entityid'] = this.entityCombo.selectedLabel;
    }
    this.requestParams['status'] = this.statusCombo.selectedValue;
    this.widthData.cbStart= this.startOfComms.bind(this);
    this.widthData.cbStop= this.endOfComms.bind(this);
    this.widthData.cbResult = (event) => {
      this.inputDataResultColumnsChange(event);
    };
    this.widthData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.widthData.send(this.requestParams, null);
  }
  columnOrderChange(event): void {
    this.columnsNewOrders = "";
    this.columnDefinitionsTempArray = event;
    for (let i=0; i< this.columnDefinitionsTempArray.length; i++) {

      if(this.columnDefinitionsTempArray[i].id != "dummy")
        this.columnsNewOrders = this.columnsNewOrders + this.columnDefinitionsTempArray[i].field + "=" + this.columnDefinitionsTempArray[i].width + ',';
    }
    this.columnsNewOrders = this.columnsNewOrders.substring(0, this.columnsNewOrders.length - 1);
    this.requestParams=[];
    this.ordertData.encodeURL = false;
    this.actionMethod = 'method=saveColumnOrderBreakDownScreen';
    this.actionPath = 'dashboardPCM.do?';
    this.requestParams['method']= 'saveColumnOrderBreakDownScreen';
    this.requestParams['order'] = this.columnsNewOrders;
    if(this.entityCombo.selectedLabel == "<Multiple values>" && this.selectedItemsListEntity != null) {
      this.requestParams['entityid'] = this.selectedItemsListEntity;
    } else {
      this.requestParams['entityid'] = this.entityCombo.selectedLabel;
    }
    this.requestParams['status'] = this.statusCombo.selectedValue;
    this.ordertData.cbStart= this.startOfComms.bind(this);
    this.ordertData.cbStop= this.endOfComms.bind(this);
    this.ordertData.cbResult = (event) => {
      this.inputDataResultColumnsChange(event);
    };
    this.ordertData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.ordertData.send(this.requestParams, null);
  }

  inputDataResultColumnsChange(event) : void {
    let index: any;
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyMessage() == "Column width saved ok") {

      } else {
        this.swtAlert.error(SwtUtil.getCommonMessages('alert.generic_exception'));
      }
    }
  }
  /**
   * Update the data, this is called whenever a fresh of the data is required.
   * This could be called from either a change in a combobox selection of from the timer
   **/
  updateData(str): void {
    //reset variable of getting id of selected payment
    if(str != "keepSelected") {
      this.paySelectedIndex = null;
    } else {
      if(this.commonGrid.selectedIndices.length == 1) {
        this.paySelectedIndex = this.commonGrid.dataProvider[this.commonGrid.selectedIndex].payreq_id;
      }
    }
    this.userFilter = this.getfilteredGridColumns();
    for(let i = 0; i< this.commonGrid.sorters.length; i++) {
      var underscoreExist: string = this.commonGrid.sorters[i].columnId[this.commonGrid.sorters[i].columnId.length -1] ;
      if (underscoreExist == "_") {
        this.commonGrid.sorters[i].columnId = this.commonGrid.sorters[i].columnId.slice(0, -1);
      }
      this.order = this.commonGrid.sorters[i].columnId;
      this.ascDesc = this.commonGrid.sorters[i].direction ? "Asc": "Desc";
    }

    this.requestParams=[];
    this.inputData.encodeURL = false;
    this.actionMethod = 'method=getDashboardDetails';
    this.actionPath = 'dashboardPCM.do?';
    this.requestParams['method']= 'getDashboardDetails';
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      //when going to the next page and then channgin combo I need to reset the numStepper this line is to be reviewd
      this.numstepper.value = 1;
      this.inputDataResult(event);
      this.disableButtons();
    };
    this.requestParams['applyCurrencyThreshold'] = (this.applyCurrencyCheck.selected) ? 'Y' : 'N';
    this.requestParams['absoluteAmount'] = (this.applyAbsoluteCheck.selected) ? 'Y' : 'N';
    this.requestParams['spreadOnly'] = this.spreadOnlyFromPrevScreen;
    this.requestParams['currencyCode'] =  this.ccyCombo.selectedLabel;
    this.requestParams['accountGroup'] = this.acctGrpCombo.selectedLabel;
    this.requestParams['account'] =  this.accountCombo.selectedLabel;
    if(this.entityCombo.selectedLabel == "<Multiple values>" && this.selectedItemsListEntity != null) {
      this.requestParams['entity'] = this.selectedItemsListEntity;
    } else {
      this.requestParams['entity'] = this.entityCombo.selectedLabel;
    }

    if(this.entityCombo.selectedLabel == "<Multiple values>" && this.selectedItemsListEntity != null) {
      this.requestParams['accountEntity'] = this.selectedItemsListEntity;
    } else {
      this.requestParams['accountEntity'] = this.getEntityId();
    }
    this.requestParams['status'] = this.statusCombo.selectedItem.value;
    this.requestParams['date'] = this.startDate.text;
    if(this.statusCombo.selectedItem.value == "B") {
      this.requestParams["blockedReason"] = this.blokedCombo.selectedItem.value;
    }
    this.requestParams['fromScreen'] = this.screenName;
    this.requestParams['userFilter'] = this.userFilter;
    this.requestParams['order'] = this.order;
    this.requestParams['ascDesc'] = this.ascDesc;
    if(this.screenName == "search") {
      this.requestParams['initialFilter'] = this.initialFilterFromPrevScreen;
      this.requestParams['refFilter'] = this.refFilterFromPrevScreen;
      this.requestParams['archive'] = this.archiveFromPrevScreen;
    }
    this.requestParams['timeFrame'] = this.getTimeFrame();
    this.requestParams['inputSince'] = (this.inputSinceHbox.visible ) ? this.inputSince.text : "";
    this.inputData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.inputData.send(this.requestParams, null);
    this.isDateChanged = false;

  }
  updataDataWithPagination(): void {
    // Instantiating the Array object that holds the request parameters
    var rowBegin : number;
    var rowEnd: number;
    rowBegin = Number(this.numstepper.value -1)* this.pageSize + ((this.numstepper.value == 1) ? 0 :1) ;
    rowEnd = Number(this.numstepper.value)* this.pageSize;
    this.userFilter = this.getfilteredGridColumns();
    for(let i = 0; i< this.commonGrid.sorters.length; i++) {
      var underscoreExist: string = this.commonGrid.sorters[i].columnId[this.commonGrid.sorters[i].columnId.length -1] ;
      if (underscoreExist == "_") {
        this.commonGrid.sorters[i].columnId = this.commonGrid.sorters[i].columnId.slice(0, -1);
      }
      this.order = this.commonGrid.sorters[i].columnId;
      this.ascDesc = this.commonGrid.sorters[i].direction ? "Asc": "Desc";
    }

    this.requestParams=[];
    this.inputData.encodeURL = false;
    this.actionMethod = 'method=getDashboardDetails';
    this.actionPath = 'dashboardPCM.do?';
    this.requestParams['method']= 'getDashboardDetails';
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
      this.disableButtons();
    };
    this.requestParams['applyCurrencyThreshold'] = (this.applyCurrencyCheck.selected) ? 'Y' : 'N';
    this.requestParams['absoluteAmount'] = (this.applyAbsoluteCheck.selected) ? 'Y' : 'N';
    this.requestParams['spreadOnly'] = this.spreadOnlyFromPrevScreen;
    this.requestParams['currencyCode'] =  this.ccyCombo.selectedLabel;
    this.requestParams['accountGroup'] = this.acctGrpCombo.selectedLabel;
    this.requestParams['account'] =  this.accountCombo.selectedLabel;
    if(this.entityCombo.selectedLabel == "<Multiple values>" && this.selectedItemsListEntity != null) {
      this.requestParams['entity'] = this.selectedItemsListEntity;
    } else {
      this.requestParams['entity'] = this.entityCombo.selectedLabel;
    }

    if(this.entityCombo.selectedLabel == "<Multiple values>" && this.selectedItemsListEntity != null) {
      this.requestParams['accountEntity'] = this.selectedItemsListEntity;
    } else {
      this.requestParams['accountEntity'] = this.getEntityId();
    }
    this.requestParams['status'] = this.statusCombo.selectedItem.value;
    this.requestParams['date'] = this.startDate.text;
    if(this.statusCombo.selectedItem.value == "B") {
      this.requestParams["blockedReason"] = this.blokedCombo.selectedItem.value;
    }
    this.requestParams['fromScreen'] = this.screenName;
    this.requestParams['userFilter'] = this.userFilter;
    this.requestParams['order'] = this.order;
    this.requestParams['ascDesc'] = this.ascDesc;
    if( rowBegin < 0) {
      this.requestParams['rowBegin'] = null;
      this.requestParams['rowEnd'] = null;
    } else {
      this.requestParams['rowBegin'] = rowBegin;
      this.requestParams['rowEnd'] = rowEnd;
    }
    if(this.screenName == "search") {
      this.requestParams['initialFilter'] = this.initialFilterFromPrevScreen;
      this.requestParams['refFilter'] = this.refFilterFromPrevScreen;
      this.requestParams['archive'] = this.archiveFromPrevScreen;
    }
    this.requestParams['timeFrame'] = this.getTimeFrame();
    this.requestParams['inputSince'] = (this.inputSinceHbox.visible ) ? this.inputSince.text : "";
    this.inputData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.inputData.send(this.requestParams, null);
    this.isDateChanged = false;

  }





  /**
   * changeCombo
   * This function is called when there is a change in one of the combo's
   **/
  changeCombo():void {
    if(this.ccyCombo.selectedItem){
    if(this.previousCcy != this.ccyCombo.selectedItem.value ) {
      this.acctGrpCombo.selectedLabel = 'All';
      this.accountCombo.selectedLabel = 'All';
    }
    }
    if(this.previousAcctGrp != this.acctGrpCombo.selectedItem.value) {
      this.accountCombo.selectedLabel = 'All';
    }
    this.selectedCcy.text = this.ccyCombo.selectedItem ? this.ccyCombo.selectedItem.value : "";
    this.selectedAcctGrp.text = this.acctGrpCombo.selectedItem.value;
    this.selectedAccount.text = this.accountCombo.selectedItem.value;
    this.selectedItemsList = (this.entityCombo.selectedValue == "<<Multiple Values>>") ? this.selectedEntity.text : this.entityCombo.selectedLabel;
    this.handleFieldsDisplay();
    /*if(this.previousStatus != this.statusCombo.selectedItem.value) {
      /!*clear filter*!/
      if(this.commonGrid.filteredGridColumns != "") {
        this.commonGrid.filteredGridColumns = ""
        this.commonGrid.clearSortFilter();
      }

    }*/
    this.updateData(null);
  }
  get2FirstItemsFromList(list) {
    let result = "";
    let occurrenceOfComma = (list.match(new RegExp(",", "g")) || []).length;
    if (list && list.indexOf(",") !== -1 && occurrenceOfComma >= 2) {
      result = list.split(",")[0].toString() + ' ,' + list.split(",")[1].toString() + ",...";
    } else {
      result = list;
    }
    return result;
  }

  changeComboEntity(combo, button, label) {
    try {
      if (combo.selectedValue == "<<Multiple Values>>") {
        button.enabled = true;
        this.selectedItemsListEntity = '';
        label.text = "";
      }
      else {
        button.enabled = false;
        label.text = combo.selectedValue;
        this.selectedItemsList = combo.selectedLabel;
        this.selectedItemsListEntity =this.entityCombo.selectedItem ? this.entityCombo.selectedItem.content: "";
        this.updateData(null);
        this.entityChanged = true;
      }

    } catch (e) {
      console.log(e, this.moduleId, 'PCDashboard', 'changeCombo');
    }

  }

  multipleListSelect(comboSelectList, label) {
    try {
      this.win =  SwtPopUpManager.createPopUp(this, ListValues, {
        title: "Entity",
        operation: "in",
        dataSource: "fromDashboard",
        columnLabel: "entityList",
        columnCode: "entityList",
        viewOnly : false,
      });
      this.win.enableResize = false;
      this.win.id = "listValuesPopup";
      this.win.width = '500';
      this.win.height = '500';
      this.win.showControls = true;
      this.win.isModal = true;
      this.win.onClose.subscribe((res) => {
        if (this.win.getChild().result) {
          this.selectedItemsList = this.selectedItemsListEntity;
          let occurrenceOfComma = (this.selectedItemsList.match(new RegExp(",", "g")) || []).length;
          if (this.selectedItemsList && this.selectedItemsList.indexOf(",") !== -1 && occurrenceOfComma >= 2) {
            label.text = this.selectedItemsList.split(",")[0].toString() + ' ,' + this.selectedItemsList.split(",")[1].toString() + ",...";
          } else {
            label.text = this.selectedItemsList;
          }
          this.entityChanged = true;
          this.updateData(null);
        }
      });
      this.win.display();
    } catch (e) {
      console.log(e, this.moduleId, "Dashboard", "multipleListSelect");
    }

  }

  previouStatus(): void {
    if (this.count%2 == 0) {
      this.previousStatus = this.statusCombo.selectedItem.value;
    }
    this.count ++
  }
  previouCcy(): void {
      this.previousCcy =  this.ccyCombo.selectedItem.value;

  }
  previouAcctGrp(): void {
      this.previousAcctGrp =  this.acctGrpCombo.selectedItem.value;

  }
  closeHandler() {
   window.close();
  }


  displayPayment(event) {
    this.isDisplayClicked = true;
     /*var newWindow = window.open("/PaymentDisplay", 'Display Payment', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
          if (window.focus) {
            newWindow.focus();
          }*/
   ExternalInterface.call('paymentDisplay');

  }
  getParamsFromParent(): any {
    let params = [];
    if(this.isSpreadClicked) {


       params = [{screenName: 'view', spreadId: this.spreadId}];
      this.isSpreadClicked = false;
    }
    else if(this.isDisplayClicked) {
       params = [{payRequestId: this.commonGrid.selectedItem.payreq_id.content, timeFrame: this.getTimeFrame()}];
    }
    return params;
  }
  checkBeforeUnstop(event) {
    if(this.archiveFromPrevScreen == "") {
    this.isReleaseButtonClicked = false;
    this.requestParams=[];
    this.actionMethod = 'method=checkStopProcess';
    this.actionPath = 'dashboardPCM.do?';
    this.logicUpdate.cbResult = (event) => {
      this.isUnStopProcessRunning(event);
    };
    this.requestParams['method'] = 'checkStopProcess';
    this.logicUpdate.cbStart = this.startOfComms.bind(this);
    this.logicUpdate.cbStop = this.endOfComms.bind(this);
    this.logicUpdate.encodeURL = false;
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);
    } else {
      //do not do anything
    }
  }
  isUnStopProcessRunning(resultEvent): void {
    let result: string = null;
    let message =  SwtUtil.getPredictMessage("dashboardDetails.stopProcessRun", null);
    Alert.okLabel = SwtUtil.getPredictMessage("button.retry", null);
    Alert.cancelLabel =  SwtUtil.getPredictMessage("button.cancel", null);
    try {
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = resultEvent;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        result = JsonResult.getSingletons().isStopProcRun;
        if(result == "N") {
          this.unStopPay(event)
        } else {
          this.swtAlert.confirm(message, "", Alert.OK |  Alert.CANCEL, null, this.reCheckBeforeUnstop.bind(this));
        }

      }
      Alert.okLabel = SwtUtil.getPredictMessage("button.ok", null);
    }
    catch(e) {
    }
  }
  reCheckBeforeUnstop(event) {
    if(event.detail == Alert.OK ) {
      clearInterval(this.interval);
      this.checkBeforeUnstop(event)
    } else {
      this.autoRefreshAfterStop();
    }
  }
  checkBeforeRelease(event) {
    if(this.archiveFromPrevScreen == "") {
    this.isReleaseButtonClicked = true;
    this.requestParams=[];
    this.actionMethod = 'method=checkStopProcess';
    this.actionPath = 'dashboardPCM.do?';
    this.logicUpdate.cbResult = (event) => {
      this.isStopProcessRunning(event);
    };
    this.requestParams['method'] = 'checkStopProcess';
    this.logicUpdate.cbStart = this.startOfComms.bind(this);
    this.logicUpdate.cbStop = this.endOfComms.bind(this);
    this.logicUpdate.encodeURL = false;
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);
    } else {
      //Do not do anything
    }
  }
  isStopProcessRunning(resultEvent): void {
    let result: string = null;
    let message =  SwtUtil.getPredictMessage("dashboardDetails.stopProcessRun", null);
    Alert.okLabel = SwtUtil.getPredictMessage("button.retry", null);
    Alert.cancelLabel =  SwtUtil.getPredictMessage("button.cancel", null);
    try {
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = resultEvent;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        result = JsonResult.getSingletons().isStopProcRun;
        if(result == "N") {
          this.releasePayment(event)
        } else {
          this.swtAlert.confirm(message, "", Alert.OK |  Alert.CANCEL, null, this.reCheckBeforeRelease.bind(this));
        }

      }
      Alert.okLabel = SwtUtil.getPredictMessage("button.ok", null);
    }
    catch(e) {
    }
  }
  reCheckBeforeRelease(event) {
    if(event.detail == Alert.OK ) {
      clearInterval(this.interval);
      this.checkBeforeRelease(event)
    } else {
      this.autoRefreshAfterStop();
    }
  }
  releasePayment(event) {
    let message: string;
    clearInterval(this.interval);

    if(this.commonGrid.selectedItems.length ==1) {
      if(this.commonGrid.selectedItem.status.content == "B" ||this.commonGrid.selectedItem.status.content == "S" /*&& this.commonGrid.selectedItem.is_stopped.content == "Stopped"*/) {
        this.blockedStoppedRelease(this.commonGrid.selectedItem.payreq_id.content, false);
      }
     /* else if(this.commonGrid.selectedItem.status.content == "B" && this.commonGrid.selectedItem.block_reason_code.content == "Input as Back Valued") {
        this.blockedStoppedRelease(this.commonGrid.selectedItem.payreq_id.content, false);
      }*/
      else {
        message = SwtUtil.getPredictMessage('dashboardDetails.alert.releaseSelectedPay', null);
        this.swtAlert.confirm(message, "", Alert.YES |  Alert.CANCEL, null, this.confirmRelease.bind(this));
      }
    }  else {
      this.confirmReleaseMultiplePayments(event);
    }



  }
  blockedStoppedRelease(payReqId, fromMultipleSelect) : void {
    this.requestParams=[];
    this.actionMethod = 'method=checkBlockedStoppedPay';
    this.actionPath = 'dashboardPCM.do?';
    this.logicUpdate.cbResult = (event) => {
      if(fromMultipleSelect) {
        this.isBlockedStoppedResultFromMultipleSelection(event);
      } else
      this.isBlockedStoppedResult(event);
    };
    this.requestParams['paymentId'] = (payReqId) ? payReqId.toString() : "";
    this.requestParams['method'] = 'checkBlockedStoppedPay';
    this.logicUpdate.cbStart = this.startOfComms.bind(this);
    this.logicUpdate.cbStop = this.endOfComms.bind(this);
    this.logicUpdate.encodeURL = false;
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);
  }
  secondConfirmRelease(event): void {
    let message: string = null;
    if(event.detail == Alert.YES ) {
      message = SwtUtil.getPredictMessage('dashboardDetails.alert.releaseBlockedPay', null);
      this.swtAlert.confirm(message, "", Alert.YES |  Alert.CANCEL, null, this.confirmRelease.bind(this));
    } else {
      this.autoRefreshAfterStop();
    }
  }

  confirmRelease(event): void{
    try {
      if (event.detail == Alert.YES) {
        this.requestParams = [];
        /*if(event.detail == Alert.YES ) {*/
        let arrayofPaymentId = [];
        let arrayOfStatus = [];
        this.isReleaseButtonClicked = true;
        this.totalSelectedPayments = this.commonGrid.selectedItems.length;
        for (let i = 0; i < this.totalSelectedPayments; i++) {
          if (this.commonGrid.selectedItems[i].status.content == 'S' || this.commonGrid.selectedItems[i].status.content == 'W' || this.commonGrid.selectedItems[i].status.content == 'B') {
            arrayOfStatus.push(this.commonGrid.selectedItems[i].status.content);
            arrayofPaymentId.push(this.commonGrid.selectedItems[i].payreq_id.content);
          }
        }
        if (this.commonGrid.selectedItems.length == 1) {
          this.actionMethod = 'method=unStopReleasePayment';
          this.actionPath = 'dashboardPCM.do?';
          this.logicUpdate.cbResult = (event) => {
            this.logicUpdateResult(event);
          };
          this.requestParams['paymentId'] = arrayofPaymentId.toString();
          this.requestParams['previousStatus'] = arrayOfStatus.toString();
          this.requestParams['paymentAction'] = "R";
          this.requestParams['method'] = 'unStopReleasePayment';
          this.logicUpdate.cbStart = this.startOfComms.bind(this);
          this.logicUpdate.cbStop = this.endOfComms.bind(this);
          this.logicUpdate.encodeURL = false;
          this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
          this.logicUpdate.send(this.requestParams);
        }
      } else {
        this.autoRefreshAfterStop();
      }
    }catch(e) {
      console.log('error', e);
    }

  }
  confirmReleaseMultiplePayments(event): void{
    try {
      this.requestParams=[];
      this.blockedPaymentsId = [];
        this.totalSelectedPayments = this.commonGrid.selectedItems.length;
        for (let i =0; i <this.totalSelectedPayments; i++) {
          if(this.commonGrid.selectedItems[i].status.content == "B" ) {
            this.blockedPaymentsId.push(this.commonGrid.selectedItems[i].payreq_id.content);
          }
        }
        this.blockedStoppedRelease(this.blockedPaymentsId, true);

    } catch(e) {
      console.log('error', e);
    }

  }

  unStopPay(event){
    let message: string = "";
    if(this.commonGrid.selectedItems.length == 1) {
      message = SwtUtil.getPredictMessage('dashboardDetails.alert.unstopPay', null);
    } else {
      message = SwtUtil.getPredictMessage('dashboardDetails.alert.unstopPays', null);
    }

    this.swtAlert.confirm(message, "", Alert.YES |  Alert.CANCEL, null, this.confirmUnstop.bind(this));

  }
  confirmUnstop(event) {
    if(event.detail == Alert.YES ) {
      clearInterval(this.interval);
      let arrayOfStatus = [];
      let arrayofPaymentId = [];
      for (let i =0; i <this.commonGrid.selectedItems.length; i++) {
        if(this.commonGrid.selectedItems[i].status.content =='S' ) {
          arrayOfStatus.push(this.commonGrid.selectedItems[i].status.content);
          arrayofPaymentId.push(this.commonGrid.selectedItems[i].payreq_id.content);
        }
      }
      this.isReleaseButtonClicked = false;
      this.actionMethod = 'method=unStopReleasePayment';
      this.actionPath = 'dashboardPCM.do?';
      this.logicUpdate.cbResult = (event) => {
        //this.updateData();
        this.logicUpdateResult(event);
      };
      this.requestParams['paymentId'] =  arrayofPaymentId.toString();
      this.requestParams['previousStatus'] =  arrayOfStatus.toString();
      this.requestParams['paymentAction'] =  "U";
      this.requestParams['method'] = 'unStopReleasePayment';
      this.logicUpdate.cbStart = this.startOfComms.bind(this);
      this.logicUpdate.cbStop = this.endOfComms.bind(this);
      this.logicUpdate.encodeURL = false;
      this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
      this.logicUpdate.send(this.requestParams);
    } else {
      this.autoRefreshAfterStop();
    }
  }
  autoRefreshAfterStop() {
    let timeLeft = this.refreshRate;
    clearInterval(this.interval);
    this.interval = setInterval(() => {
      this.updataDataWithPagination();
    }, timeLeft*1000)
  }
  logicUpdateResult(event): void {
    try {
      let messageErrorEngine = SwtUtil.getPredictMessage('dashboardDetails.alert.inputEngineError', null);
      let genericError = SwtUtil.getPredictMessage('dashboardDetails.alert.error', null);
      let titleErrors = SwtUtil.getPredictMessage('dashboardDetails.title.errorPay', null);
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = event;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);

       /* if(this.refusedStatus.length > 0) {
          this.popUpRefusedReleasedPay();
        }*/
        if (JsonResult.getRequestReplyMessage() == "ERROR_PAYMENT_ENGINE") {
          this.swtAlert.error(messageErrorEngine);
        }else if (JsonResult.getRequestReplyMessage() == "GENERIC_ERROR") {
          this.swtAlert.error(genericError);
        } else {
          if(event.ErrorsOfUpdate.grid && JsonResult.getGridData().size > 0 ) {
            this.win = SwtPopUpManager.createPopUp(this,
              ErrorsUpdatePayments,
              {
                title: titleErrors,
                rowsData : JsonResult.getGridData(),
                columnsData: JsonResult.getColumnData(),
                isReleaseAction: this.isReleaseButtonClicked

              });
            this.win.width = "1010";
            this.win.height = "400";
            this.win.enableResize = false;
            this.win.showControls = true;
            this.win.display();
          }
          this.updateData(null);


        }
      }
    } catch (e) {
      // log the error in ERROR LOG
    }
  }
  isBlockedStoppedResult(event): void {
    let message = "";
    let messageDuplicated = "";
    let stopRules = "";
    let jsonSingeltons: string;
    let id: string;
    let isBlockedandStopped =[];
    let stopRule = [];
    let stopRuleTime = [];
    let messageBlockedPay = SwtUtil.getPredictMessage('dashboardDetails.alert.releaseBlockedPay', null);
    let messageLockedPay = SwtUtil.getPredictMessage('dashboardDetails.alert.lockedPay', null);
    let payStopped = SwtUtil.getPredictMessage('dashboardDetails.paymentStopped', null);
    let payWasStopped = SwtUtil.getPredictMessage('dashboardDetails.paymentWasStopped', null);
    let until = SwtUtil.getPredictMessage('dashboardDetails.until', null);
    let wishContinue = SwtUtil.getPredictMessage('dashboardDetails.wishContinue', null);
    try {
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = event;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        jsonSingeltons = JsonResult.getSingletons().payIdRule.replace('{', '').replace('}', '');
        let arrayOfIdRule = [];
        if (jsonSingeltons) {
          arrayOfIdRule.push(jsonSingeltons.split('#'));
          arrayOfIdRule = arrayOfIdRule[0];
          for (let i = 0; i < arrayOfIdRule.length; i++) {
            isBlockedandStopped.push(arrayOfIdRule[i].split('|')[0]);
            stopRule.push(arrayOfIdRule[i].split('|')[1]);
            stopRuleTime.push(arrayOfIdRule[i].split('|')[2]);
          }
          for (let j = 0; j < isBlockedandStopped.length; j++ ) {
            if(isBlockedandStopped[j] == "Y" && stopRule[j] != undefined) {
              messageDuplicated = messageDuplicated + payStopped + " " + stopRule[j] + "<br/>";
          } else if(isBlockedandStopped[j] == "P" && stopRule[j] != undefined && stopRuleTime[j] != undefined ) {
              messageDuplicated = messageDuplicated + payWasStopped + " " + stopRule[j] + " " + until + " "+ stopRuleTime[j] + "<br/>";
            }
            else if(isBlockedandStopped[j] == "N") {
              message = messageBlockedPay;
              this.swtAlert.confirm(message, "", Alert.YES |  Alert.CANCEL, null, this.confirmRelease.bind(this));
            } else if(isBlockedandStopped[j] == "L") {
             message = messageLockedPay;
              this.swtAlert.warning(message);
            }
          }
          if(messageDuplicated != '') {
            messageDuplicated = messageDuplicated +  wishContinue;
            if(this.commonGrid.selectedItem.status.content == 'S'){
              this.swtAlert.confirm(messageDuplicated, "", Alert.YES |  Alert.CANCEL, null, this.confirmRelease.bind(this));
            }else {
              this.swtAlert.confirm(messageDuplicated, "", Alert.YES |  Alert.CANCEL, null, this.secondConfirmRelease.bind(this));
            }
          }
        }

        }
    } catch (e) {
      // log the error in ERROR LOG
    }
  }

  isBlockedStoppedResultFromMultipleSelection(event): void {
    let jsonSingeltons : string;
    let isBlockedArray = [];
    let idArray = [];
    let j =0;
    let titleReleaseConf = SwtUtil.getPredictMessage('dashboardDetails.title.releaseConfirmation', null);
    this.arrayStatusPayments = [];
    try {
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = event;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        jsonSingeltons = JsonResult.getSingletons().payIdRule.replace('{', '').replace('}', '');
        let arrayOfPayments = [];
        if (jsonSingeltons) {
          arrayOfPayments.push(jsonSingeltons.split(';'));
          arrayOfPayments = arrayOfPayments[0];
          //arrayOfPayments = ["N", "Y|stop,P|stop2", "P|stop"];
        for (let i = 0; i < arrayOfPayments.length; i++) {
          isBlockedArray.push(arrayOfPayments[i].substr(0,1));
        }
      }
        for (let i =0; i <this.commonGrid.selectedItems.length; i++) {
          if(this.commonGrid.selectedItems[i].status.content == "B" ) {
            if(isBlockedArray[j] == "Y") {
              this.arrayStatusPayments.push({id:this.commonGrid.selectedItems[i].payreq_id.content, status: this.commonGrid.selectedItems[i].status.content, blockReason:  (this.commonGrid.selectedItems[i].block_reason_code) ? this.commonGrid.selectedItems[i].block_reason_code.content : "" , alreadyStopped :  "Stopped" } );
            } else if(isBlockedArray[j] == "P") {
              this.arrayStatusPayments.push({id:this.commonGrid.selectedItems[i].payreq_id.content, status: this.commonGrid.selectedItems[i].status.content, blockReason:  (this.commonGrid.selectedItems[i].block_reason_code) ? this.commonGrid.selectedItems[i].block_reason_code.content : "" , alreadyStopped : "PrevStopped"} );
            } else if(isBlockedArray[j] == "N") {
              this.arrayStatusPayments.push({id:this.commonGrid.selectedItems[i].payreq_id.content, status: this.commonGrid.selectedItems[i].status.content, blockReason:  (this.commonGrid.selectedItems[i].block_reason_code) ? this.commonGrid.selectedItems[i].block_reason_code.content : "" , alreadyStopped : ""} );
            }
             j++;
          } else {
            this.arrayStatusPayments.push({id:this.commonGrid.selectedItems[i].payreq_id.content, status: this.commonGrid.selectedItems[i].status.content, blockReason:  "" , alreadyStopped : "" } )
          }
        }
        this.win = SwtPopUpManager.createPopUp(this,
          ConfirmRelease,
          {
            title: titleReleaseConf,
            statusPayArray: this.arrayStatusPayments

          });
        this.win.isModal = true;
        this.win.width = "430";
        this.win.height = "460";
        this.win.enableResize = false;
        this.win.showControl;
        this.win.display();
      }

    } catch (e) {
      console.log('error ', e);
      // log the error in ERROR LOG
    }
  }
  spreadDisplay(event){
    try {
      let messageNoSpread = SwtUtil.getPredictMessage('dashboardDetails.alert.noSpread', null);
      let messageNoAccount = SwtUtil.getPredictMessage('dashboardDetails.alert.noAccount', null);
      this.isSpreadClicked = true;
      if(this.commonGrid.selectedItem.acc_grp_id.content) {
      this.actionMethod = 'method=getSpreadId';
      this.actionPath = 'dashboardPCM.do?';
      this.inputData.cbResult = (event) => {
        this.jsonReader.setInputJSON(event);
        this.spreadId = this.jsonReader.getSingletons().spreadId;
        if(this.spreadId) {
         /* var newWindow = window.open("/spreadProfilesAdd", 'Display Spread', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
          if (window.focus) {
            newWindow.focus();
          }*/
          ExternalInterface.call('spreadDisplay');
        } else {
            this.swtAlert.warning(messageNoSpread, null, Alert.OK);
        }
      };
      this.requestParams['accountGroupId'] = this.commonGrid.selectedItem.acc_grp_id.content;
      this.requestParams['method'] = 'getSpreadId';
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      } else {
        this.swtAlert.warning(messageNoAccount);
      }

    } catch(e) {
      console.log('error', e)
    }

  }

  changeCategory(event){
    try {
      //enable when demanding
      /*this.win = SwtPopUpManager.createPopUp(this,
        CategoryList,
        {
          title: "Change Payment Category",
          comboDataProvider : this.jsonReader.getSelects()['select'].find(x => x.id === 'categoryList').option,
          selectedCategoryLabel : this.commonGrid.selectedItem.category_id.content,
          selectedPayId: this.commonGrid.selectedItem.payreq_id.content

        });
      this.win.width = "620";
      this.win.height = "180";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win.display();*/

    } catch (e) {
    }

  }
  refreshParent(selectedCategory, selectedPayId): void  {
    //update the category
    this.requestParams = [];
    this.actionMethod = 'method=updatePaymentCategory';
    this.actionPath = 'dashboardPCM.do?';
    this.inputData.cbResult = (event) => {
      this.updateData(null);
    };
    this.requestParams['paymentId'] = selectedPayId;
    this.requestParams['categoryId'] = selectedCategory;
    this.requestParams['method'] = 'updateCategory';
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }




  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  public inputDataFault( event ):void {
    //this.invalidComms = event.fault.faultString + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail;
    this.swtAlert.error( SwtUtil.getPredictMessage('dashboardDetails.genericError', null) );
  }

  /**
   * doHelp
   * Function is called when 'Help' button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call('help');
    } catch (e) {

    }
  }


  /**
   * print
   * This is a report icon action handler method
   */
  print(): void {
    ExternalInterface.call("printPage");
  }

  /**
   * popupClosed
   * param event
   * Method to close child windows when this screen is closed
   */
  popupClosed(event):void {
    this.close();
  }

  enableDisableButtonsByStatus():void {
    if(this.archiveFromPrevScreen) {
      this.releaseButton.enabled = false;
      this.unStopButton.enabled = false;
      this.changeCatgButton.enabled = false;
    } else {
      if (this.menuAccessId == 0) {
      for(let i =0 ;i < this.commonGrid.selectedItems.length; i++ ) {

        if(this.commonGrid.selectedItems.length == 1) {
          if (this.commonGrid.selectedItem.status.content == "S" || this.statusCombo.selectedItem.value == "S") {
            this.unStopButton.enabled = true;
          } else {
            this.unStopButton.enabled = false;
          }
          } else {
          if (this.commonGrid.selectedItems[i].status.content == "S" || this.statusCombo.selectedItem.value == "S") {
            this.unStopButton.enabled = true;
            break;

          } else {
            this.unStopButton.enabled = false;
          }
        }
      }
    for(let i =0 ;i < this.commonGrid.selectedItems.length; i++ ) {
      if(this.commonGrid.selectedItems.length == 1) {
        if(this.commonGrid.selectedItem.status.content =='R' ||this.commonGrid.selectedItem.status.content =='C') {
          this.releaseButton.enabled = false;
        } else {
          this.releaseButton.enabled = true;
        }
      } else {
        if(this.commonGrid.selectedItems[i].status.content =='R' ||this.commonGrid.selectedItems[i].status.content =='C') {
          this.releaseButton.enabled = false;
        } else {
          this.releaseButton.enabled = true;
          break;
        }
      }
   }
      } else {
        this.releaseButton.enabled = false;
        this.unStopButton.enabled = false;
      }
      this.changeCatgButton.enabled = false;
    }
  }

  /**
   *change tabs event handler
   *
   */
  tabIndexchangeHandler(): void {
    try {
      this.statusChanged = false;
      this.isDateChanged = true;
      let todayDate;
      let firstDateInBlocked = this.arrayOftabs[0].label + "/"+ this.arrayOftabs[0].year;
      if(this.statusCombo.selectedValue == "B") {
        todayDate  = new Date(CommonUtil.parseDate(firstDateInBlocked, this.dateFormat.toUpperCase()));
      } else {
        todayDate  = new Date(CommonUtil.parseDate(this.sysDateFrmSession, this.dateFormat.toUpperCase()));
      }
      todayDate.setHours(12, 0, 0);
      if(this.tabs.selectedLabel != 'Selected') {
       // this.sysDateFrmSession = this.tabs.selectedLabel + year;
        this.startDate.selectedDate = new Date(todayDate.setDate(todayDate.getDate() + this.tabs.selectedIndex));
      }
      /*clear filter*/
      //this.commonGrid.clearSortFilter();
      this.updateData(null);

    } catch(e) {
      console.log('error', e)
    }

  }
  enableDisableTextInput(value:boolean): void {
    this.sodText.enabled = value;
    this.confirmedText.enabled = value;
    this.creditText.enabled = value;
    this.releasedPayText.enabled = value;
    this.otherPaymentsText.enabled = value;
    this.excludeCLText.enabled = value;
    this.includeCLText.enabled = value;
    this.reserveText.enabled = value;
  }
  disableButtons(): void {
    if(this.paySelectedIndex == null || this.indexSelectedPay < 0) {
    this.spreadButton.enabled = false;
    this.displayButton.enabled = false;
    this.changeCatgButton.enabled = false;
    this.printIcon.enabled = false;
    this.releaseButton.enabled = false;
    this.unStopButton.enabled = false;
    }
  }
  /**
   * report
   *
   * @param type: String
   *
   * This is a report icon action handler method
   */
  report(type: string):void {
    let entity;
    let blockedReason = "";
    let initialFilter = "";
    let refFilter = "";
    let archive = "";
    let ccyThreshold;
    let absoluteAmount;
    let ccyCode;
    let accountGroup;
    let account;
    let status;
    let fromScreen ;
    let userFilter;
    let order;
    let ascDesc;
    let rowBegin : number;
    let rowEnd: number;
    let pagesToExport = "";
    let titlePage = SwtUtil.getPredictMessage('exportPages.title', null);
    try {
      ccyThreshold = (this.applyCurrencyCheck.selected) ? 'Y' : 'N';
      absoluteAmount = (this.applyAbsoluteCheck.selected) ? 'Y' : 'N';
      ccyCode =  this.ccyCombo.selectedLabel;
      accountGroup = this.acctGrpCombo.selectedLabel;
      account =  this.accountCombo.selectedLabel!="All"?this.accountCombo.selectedLabel.split("(")[0]:this.accountCombo.selectedLabel;
      if(this.entityCombo.selectedLabel == "<Multiple values>" && this.selectedItemsListEntity != null) {
        entity = this.selectedItemsListEntity;
      } else {
        entity = this.entityCombo.selectedLabel;
      }

      if(this.entityCombo.selectedLabel == "<Multiple values>" && this.selectedItemsListEntity != null) {
        entity = this.selectedItemsListEntity;
    } else {
        entity = this.getEntityId();
    }
      rowBegin = Number(this.numstepper.value -1)* this.pageSize + ((this.numstepper.value == 1) ? 0 :1) ;
      rowEnd = Number(this.numstepper.value)* this.pageSize;
      status  = this.statusCombo.selectedItem.value;
      if(this.statusCombo.selectedItem.value == "B") {
        blockedReason = this.blokedCombo.selectedItem.value;
      }
      fromScreen = this.screenName;
      userFilter = this.userFilter;
      order = this.order;
      ascDesc = this.ascDesc;
      if(this.screenName == "search") {
        initialFilter = this.initialFilterFromPrevScreen;
        refFilter = this.refFilterFromPrevScreen;
        archive = this.archiveFromPrevScreen;
      } else {
        initialFilter = "";
        refFilter = "";
        archive = "";
      }
      this.win =  SwtPopUpManager.createPopUp(this, ExportPages, {

      });
      this.win.enableResize = false;
      this.win.title = titlePage;
      this.win.width = '220';
      this.win.height = '180';
      this.win.showControls = true;
      this.win.isModal = true;
      this.win.onClose.subscribe((res) => {
        if(this.win.getChild().result) {
        if (this.win.getChild().result == "current") {
          pagesToExport = "current";
        } else  {
          pagesToExport = "all";

        }
        ExternalInterface.call("report", entity, this.selectedEntity.text, type, this.dateFormat, this.startDate.text,
          ccyCode, accountGroup, account, status, ccyThreshold, absoluteAmount, blockedReason, this.spreadOnlyFromPrevScreen, fromScreen,
          userFilter, order, ascDesc, initialFilter, refFilter, archive, rowBegin, rowEnd, this.sodText.text, this.confirmedText.text, this.creditText.text, this.releasedPayText.text, this.otherPaymentsText.text, this.excludeCLText.text, this.includeCLText.text, this.reserveText.text, pagesToExport, this.getTimeFrame(), this.inputSince.text);
        }
      });
      this.win.display();
      } catch (error) {
      console.log("error", error);
      SwtUtil.logError(error,   this.moduleId, 'Dashboard', 'report',   this.errorLocation);
    }
  }
  getTimeFrame(): string {
    if(this.radioC.selected) {
      return 'C';
    }else if(this.radioE.selected) {
      return 'E';
    } else {
      return 'S'
    }
  }

  getEntityId() {
    let entity = null;
    if (this.entityCombo.selectedLabel == "All" && this.accountCombo.selectedLabel && this.accountCombo.selectedLabel!="All") {
      const regex = /\(([^)]+)\)/;
      const match = this.accountCombo.selectedLabel.match(regex);

      if (match) {
        entity = match[1];
      }
    }else{
      entity= this.entityCombo.selectedLabel;

    }
    return entity;
  }

  
}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: PCMBreakdownMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PCMBreakdownMonitor],
  entryComponents: []
})
export class PCMBreakdownMonitorModule {}
