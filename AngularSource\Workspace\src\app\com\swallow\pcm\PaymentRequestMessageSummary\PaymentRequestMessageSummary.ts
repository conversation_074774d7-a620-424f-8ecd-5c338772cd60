import {
  <PERSON>mponent, ElementRef,
  ViewChild,
  OnInit,
  OnDestroy,
  NgModule,
} from '@angular/core';
import {
  HTTPComms,
  SwtButton,
  SwtCommonGrid,
  JSONReader,
  SwtAlert,
  SwtUtil,
  SwtLoadingImage,
  CommonService,
  SwtCanvas,
  SwtPopUpManager,
  SwtPanel,
  Keyboard,
  focusManager,
  Alert, ExternalInterface, SwtModule, SwtLabel, SwtToolBoxModule
} from 'swt-tool-box';
import { MessageDetails } from '../MessageDetails/MessageDetails';
import { Routes, RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/compiler/src/core';
@Component({
  selector: 'payment-request-message-summary',
  templateUrl: './PaymentRequestMessageSummary.html',
  styleUrls: ['./PaymentRequestMessageSummary.css']
})
export class PaymentRequestMessageSummary  extends  SwtModule implements OnInit, OnDestroy{

  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********Combobox*********/
  /*********SwtLabel*********/
  /********SwtButton*********************/
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;


  private messageGrid: SwtCommonGrid;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  private win;

  /* - START -- Screen Name and Version Number ---- */
  private moduleName = 'Currency Maintenance';
  private versionNumber = '1.00.00';
  private releaseDate = '20 February 2019';
  /* - END -- Screen Name and Version Number ---- */

  // to open a pop up
  private child: SwtPanel;
  private swtAlert: SwtAlert;
  private  menuAccess = 2;
  public  helpURL: string = null;
  private  message: string = null;
  public  title: string = null;
  private errorLocation = 0;
  public moduleId = '';
  private paymentRequestId  = null;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
  }


  ngOnDestroy(): any {
  }


  ngOnInit(): void {

    let paramsFromParent = [];

    if(window.opener && window.opener.instanceElement) {
      paramsFromParent =  window.opener.instanceElement.getParamsFromParent();
      if(paramsFromParent) {
        this.paymentRequestId =  paramsFromParent[0].paymentRequestId;
      }
    }

  }


  onLoad() {

    this.messageGrid = <SwtCommonGrid> this.canvasGrid.addChild(SwtCommonGrid);
    try {
      this.actionMethod = 'method=getMessageList';
      this.actionPath = 'paymentDisplayPCM.do?';
      this.requestParams["payReqId"] = this.paymentRequestId;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      //this.inputDataResult(data);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.messageGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };
      this.messageGrid.onRowDoubleClick = (event) => {
        this.doViewMessage(event);
      };
      // Assining properties for controls
      this.viewButton.label = "View";
      this.closeButton.label = "Close";
    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'ClassName', 'onLoad');
    }

  }

  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(data): void {
    let jsonList = null;
    let header: string;
    try{
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {
            this.helpURL = this.jsonReader.getSingletons().helpurl;
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {
              this.menuAccess = Number(this.jsonReader.getScreenAttributes()["menuaccess"]);
              this.disableOrEnableButtons(false);
              jsonList = this.jsonReader.getColumnData();
              for (let i = 0; i < jsonList.column.length; i++) {
                header = SwtUtil.getAMLMessages(jsonList.column[i].heading);
                jsonList.column[i].heading = header;
              }
              const obj = {columns: jsonList};
              if (this.messageGrid === null || this.messageGrid === undefined) {
                this.messageGrid.componentID = this.jsonReader.getSingletons().screenid;
                this.messageGrid.CustomGrid(obj);
              }
              this.messageGrid.doubleClickEnabled = true;
              this.messageGrid.CustomGrid(obj);
              if (this.jsonReader.getGridData().size > 0) {
                this.messageGrid.dataProvider = null;
                this.messageGrid.gridData = this.jsonReader.getGridData();
                this.messageGrid.setRowSize = this.jsonReader.getRowSize();
                this.messageGrid.doubleClickEnabled = true;
              } else {
                this.messageGrid.dataProvider = null;
                this.messageGrid.selectedIndex = -1;
              }
            }
            this.prevRecievedJSON = this.lastRecievedJSON;

          } else {
              this.swtAlert.error(this.jsonReader.getRequestReplyMessage());
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'inputDataResult', this.errorLocation);
    }
  }


  disableOrEnableButtons(isRowSelected:Boolean):void {
    if(isRowSelected){
      this.enableViewButton(this.menuAccess < 2);
    }else{
      this.enableViewButton(false);
    }
  }


  /**
   * enableViewButton
   *
   */
  enableViewButton(value: boolean): void {
    this.viewButton.enabled = value;
    this.viewButton.buttonMode = value;
  }


  /**
   * doViewCurrency
   *
   * @param event: Event
   *
   * Method to open View Currency  details screen
   */
  doViewMessage(event): void {
    try {
      this.win = SwtPopUpManager.createPopUp(this,
        MessageDetails,
        {
          title: "Message Details",
          screenName: 'view',
          messageId:this.messageGrid.selectedItem.messageId.content,
          messageBody:this.messageGrid.selectedItem.messageBody.content,
      });
      this.win.width = "700";
      this.win.height = "390";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win.display();

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'doViewMessage', this.errorLocation);
    }
  }

  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    try {
      this.loadingImage.setVisible(true);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'startOfComms', this.errorLocation);
    }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    try {
      this.loadingImage.setVisible(false);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'endOfComms', this.errorLocation);
    }
  }

  /**
   * inputDataFault
   *
   * @param event:  FaultEvent
   *
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    try {
      this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'inputDataFault', this.errorLocation);
    }
  }


  /**
   * cellClickEventHandler
   *
   * @param event: Event
   *
   * This method is used to manumberain the button status when a row is clicked
   */
  cellClickEventHandler(event): void {
    try {
      if (this.messageGrid.selectedIndex >= 0 && this.messageGrid.selectable) {
        this.disableOrEnableButtons(true);
        event.stopPropagation();
      }else{
        this.disableOrEnableButtons(false);
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'cellClickEventHandler', this.errorLocation);
    }
  }

  /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(Button)
   */
  keyDownEventHandler(event): void {
    try {
      const eventstring = Object(focusManager.getFocus()).name;
      if ((event.keyCode === Keyboard.ENTER)) {
        if (eventstring === 'closeButton') {
          this.closeCurrentTab(event);
        } else if (eventstring === 'helpIcon') {
          this.doHelp();
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'ClassName', 'keyDownEventHandler',   this.errorLocation);
    }
  }


  doHelp() {
    try {
      ExternalInterface.call("help");
    } catch (error) {
        SwtUtil.logError(error, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }
    
  }


  /**
   * closeCurrentTab
   *
   * Function called when close button is called
   *
   * @param event:Event
   */
  closeCurrentTab(event): void {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'ClassName', 'refreshGrid', this.errorLocation);
    }

  }

   /**
   * dispose
   *
   * This is an event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      ExternalInterface.call("close");
      if(this.titleWindow){
        this.close();
      }else {
        window.close();
      }
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'dispose', this.errorLocation);
    }
  }
  


}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: PaymentRequestMessageSummary }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PaymentRequestMessageSummary],
  entryComponents: []
})
export class PaymentRequestMessageSummaryModule { }
