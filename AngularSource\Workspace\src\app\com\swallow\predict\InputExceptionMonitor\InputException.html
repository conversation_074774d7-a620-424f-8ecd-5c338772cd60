<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">

    <SwtCanvas width="100%" height="5%">
      <Grid width="100%" height="5%">
        <GridRow width="100%" height="100%">
          <GridItem width="115">
            <SwtLabel textDictionaryId="inputException.inputDate"></SwtLabel>
          </GridItem>
          <GridItem width="40">
            <SwtLabel textDictionaryId="label.from"  fontWeight="normal"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtDateField #startDate id="startDate" width="70"  toolTip="Enter from date"
                          restrict="0-9/"
                          (change)="validateStartDate($event,'change')">
            </SwtDateField>
          </GridItem>
          <GridItem width="40"  marginLeft="40">
            <SwtLabel textDictionaryId="text.showdays" fontWeight="normal"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtNumericInput #showDays id="showDays" width="35" maxChars="3" tooltipDictionaryId="tooltip.showdays"
                             (focusOut)="validateShowDaysValue($event)" (keypress)="keyDownInNumberOfDays($event)">
            </SwtNumericInput>
          </GridItem>
          <GridItem width="115" marginLeft="10">
            <SwtLabel id="daysLabel" #daysLabel  textDictionaryId="text.day" fontWeight="normal" ></SwtLabel>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <SwtCanvas id="canvasGrid" 
                #canvasGrid 
                width="100%" 
                height="90%">
    </SwtCanvas>
    <SwtCanvas width="100%" height="5%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true"
                      id="refreshButton"
                      #refreshButton
                      enabled="true"
                      (click)="updateDatafromRefresh()">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                      id="rateButton"
                      #rateButton
                      enabled="true"
                      (click)="rateHandler()">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                      id="closeButton" 
                      #closeButton 
                      (click)="cLoseHandler()"
                      (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">
          <HBox width="100%" horizontalAlign="right" paddingRight="10">
            <SwtLabel visible="false" textDictionaryId="screen.buildInProgress"  color="red" #dataBuildingText id="dataBuildingText"
              right="155" height="16" ></SwtLabel>
          </HBox>
          <HBox horizontalAlign="right" paddingRight="10">

            <SwtLabel visible="false" textDictionaryId="screen.connectionError"  color="red" #lostConnectionText id="lostConnectionText" >
            </SwtLabel>
            <SwtLabel textDictionaryId="screen.lastRefresh" fontWeight="normal"  ></SwtLabel>
            <SwtLabel id="lastRefTime" #lastRefTime styleName="labelLeftRefTime" fontWeight="normal">
            </SwtLabel>
            
          </HBox>
          <HBox horizontalAlign="right" paddingRight="10" >
            <div>
              <DataExport #dataExport id="dataExport"></DataExport>
            </div>
            <SwtHelpButton (click)="doHelp()">
            </SwtHelpButton>
            <SwtLoadingImage #loadingImage id="loadingImage"> </SwtLoadingImage>
          </HBox>
        </HBox>
	  </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
