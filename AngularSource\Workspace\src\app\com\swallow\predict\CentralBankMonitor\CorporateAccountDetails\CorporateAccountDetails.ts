import {Component, Inject, OnInit, ViewChild, ModuleWithProviders, NgModule, ElementRef} from '@angular/core';
import {
  SwtCanvas,
  SwtCommonGrid,
  CommonService,
  HTTPComms,
  SwtUtil,
  JSONReader,
  SwtLoadingImage,
  SwtTextInput,
  SwtLabel,
  SwtModule,
  SwtButton,
  SwtAlert,
  ExternalInterface,
  SwtToolBoxModule, Alert, StringUtils
} from "swt-tool-box";
import {RouterModule, Routes} from "@angular/router";

@Component({
  selector: 'app-corporate-account-details',
  templateUrl: './CorporateAccountDetails.html',
  styleUrls: ['./CorporateAccountDetails.css']
})
export class CorporateAccountDetails extends SwtModule implements OnInit {
  private swtAlert: SwtAlert;
  public  amount: string;
  public corpName: string;
  @ViewChild('cropNameLabel') cropNameLabel: SwtLabel;
  @ViewChild('amountLabel') amountLabel: SwtLabel;
  @ViewChild('corpNameText') corpNameText: SwtTextInput;
  @ViewChild('amountText') amountText: SwtTextInput;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  onLoad() {
    this.corpNameText.required = true;
    this.amountText.required = true;
    if(this.amount) {
      this.amountText.text = this.amount
    }
    if(this.corpName) {
      this.corpNameText.text = this.corpName
    }
  }
  OnAmountChange(event):void{

    var amount:string;
    //get the currency limit
    amount= this.amountText.text;
    var amtSign : Boolean = false;
    if(amount.indexOf("-") >= 0){
      amtSign = true;
      amount = amount.substr(1,amount.length);
    }
    //call the formatCurrency
    var changedAmt: string=ExternalInterface.call("formatCurrency", amount);
    /*var buttonId:String = Object(focusManager.getFocus()).id;
    if(buttonId == null ||buttonId == "closeButton"
      ||buttonId == "changeButton" ||buttonId == "addButton"){
    }
    else*/
      if (changedAmt == "invalid" || changedAmt.length <=0 )
    {
      this.swtAlert.warning(ExternalInterface.call('getBundle', 'text', 'label-validAmount', 'Please enter a valid amount'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'), Alert.OK, null,(data) =>{
        this.setFocusField(data,this.amountText)
      });
      return;
    }
    else
    {
      //set the amount
      if(amtSign){
        this.amountText.text= "-" + changedAmt;
      }else{
        this.amountText.text=changedAmt;
      }
    }
  }
  setFocusField(event, textAmount: SwtTextInput) {
    if(event.detail == Alert.OK) {
      textAmount.setFocusAndSelect();
    }

  }
  closeCorpAcc() {
    this.close()
  }
  saveCorpAcc() {
    if(StringUtils.trim(this.corpNameText.text.toString()).length <= 0 || StringUtils.trim(this.amountText.text).length <= 0)
    {
      this.swtAlert.warning(ExternalInterface.call('getBundle', 'text', 'label-fillMondatoryFields', 'Please fill all Mandatory Fields(*)'),ExternalInterface.call('getBundle', 'text', 'alert-mandatory', 'Mandatory'),Alert.OK, this.mandatoryAlertListener.bind(this), null, Alert.OK);
    } else {
    this.parentDocument.saveParent(this.corpNameText.text, this.amountText.text );
    this.close();
    }
  }
  mandatoryAlertListener(eventObj):void {

    if(this.corpNameText.text.length <= 0)
    {
      this.corpNameText.setFocus();
    }
    else if(this.amountText.text.length <= 0)
    {
      this.amountText.setFocus();
    }
  }
}
