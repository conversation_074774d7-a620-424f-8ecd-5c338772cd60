import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, SwtAlert, CommonService, JSONReader, HTTPComms, SwtUtil, ExternalInterface, SwtLabel, SwtCanvas, SwtButton, SwtCommonGrid, ExportEvent, SwtDataExport, SwtLoadingImage, SwtDateField, SwtCommonGridPagination, Logger, HBox } from 'swt-tool-box';
import moment from 'moment';
import { PaginationChangedArgs } from 'angular-slickgrid';
declare var instanceElement: any;

@Component({
  selector: 'app-error-log',
  templateUrl: './SystemLog.html',
  styleUrls: ['./SystemLog.css']
})
export class SystemLog extends SwtModule implements OnInit {


  /***********SwtLabel***********/
  @ViewChild('startDateLabel') startDateLabel: SwtLabel;
  @ViewChild('endDateLabel') endDateLabel: SwtLabel;
  @ViewChild('lastRefTimeLabel') lastRefTimeLabel: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;

  /***********SwtCanvas***********/
  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;

  /***********SwtButton***********/
  @ViewChild('refreshButton', { read: SwtButton }) refreshButton: SwtButton;
  @ViewChild("closeButton") closeButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  /***********SwtDateField***********/
  @ViewChild('fromDateChooser') fromDateChooser: SwtDateField;
  @ViewChild('toDateChooser') toDateChooser: SwtDateField;

  /***********SwtDateField and Pagination***********/
  // @ViewChild('dataExport') dataExport: SwtDataExport;
  @ViewChild('numstepper') numstepper: SwtCommonGridPagination;
  @ViewChild('pageBox') pageBox: HBox;

  private actionPath;
  private actionMethod: string = "";
  private requestParams;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private mainGrid: SwtCommonGrid;
  private menuAccessId;
  private _invalidComms: string;
  public moduleId = "Predict";
  private dateFormat: string;
  private displayedDate;
  private logger: Logger = null;
  private totalCount;
  public errorLocation = 0;
  //Variable to store the last page no
  public lastNumber: Number = 0;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.mainGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.mainGrid.allowMultipleSelection = true;

    this.closeButton.label = SwtUtil.getPredictMessage('sweep.close', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refreshWindow', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.lastRefTimeLabel.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);
    this.startDateLabel.text =  SwtUtil.getPredictMessage('auditLog.from', null) +"*" ;
    this.endDateLabel.text =SwtUtil.getPredictMessage('auditLog.to', null) +"*" ;
    this.startDateLabel.toolTip =  SwtUtil.getPredictMessage('tooltip.fromDate', null);
    this.endDateLabel.toolTip =SwtUtil.getPredictMessage('tooltip.toDate', null);
    this.mainGrid.clientSideSort = false;
    this.mainGrid.clientSideFilter = false;
    this.mainGrid.lockedColumnCount = 1 ;
    this.mainGrid.onPaginationChanged = (event) => {
      this.paginationChanged(event);
    };

  }

  onLoad() {
    let selectedFilter: string = null;
    let selectedSort : string = null;
    this.requestParams = [];
    this.totalCount = ExternalInterface.call('eval', 'totalCount');
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };

    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "systemlog.do?";
    this.actionMethod = 'method=displayAngular';
    this.requestParams['selectedSort'] = selectedSort;
    this.requestParams['selectedFilter'] = selectedFilter;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    this.mainGrid.onFilterChanged = this.updateData.bind(this);
    this.mainGrid.onSortChanged = this.updateData.bind(this);

    ExportEvent.subscribe((type) => {
      this.report(type);
    });
  }




  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    let maxPage;
    try {
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);

        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            this.mainGrid.selectable = false;
            errorLocation = 10;
            //Gets the current page from xml
            this.numstepper.value = Number(event.systemLogList.grid.paging.currentpage);
            //Gets the maximum no of pages value
            maxPage = event.systemLogList.grid.paging.maxpage;
            //Sets the Numeric stepper maximum value
            this.numstepper.maximum = Number(maxPage);
            this.mainGrid.paginationComponent = this.numstepper;
            //to make the pagination invisible if maxPage >1
            if(maxPage > 1) {
              this.pageBox.visible = true;
              this.numstepper.minimum=1;
              this.numstepper.maximum=maxPage;
            } else {
              this.pageBox.visible = false;
            }
            errorLocation = 20;
            this.dateFormat = this.jsonReader.getSingletons().dateFormat;
            this.displayedDate = this.jsonReader.getSingletons().displayedDate;
            this.fromDateChooser.formatString = this.dateFormat.toLowerCase();
            this.fromDateChooser.text = this.jsonReader.getSingletons().fromDate.content;
            errorLocation = 30;
            this.displayedDate = this.jsonReader.getSingletons().displayedDate;
            this.toDateChooser.formatString = this.dateFormat.toLowerCase();
            this.toDateChooser.text = this.jsonReader.getSingletons().toDate.content;

            this.lastRefTime.text = this.jsonReader.getSingletons().lastRefTime.content;

            if (!this.jsonReader.isDataBuilding()) {
              console.log("(this.jsonReader.getGridData().row      ", (this.jsonReader.getGridData().row));

              const obj = { columns: this.lastRecievedJSON.systemLogList.grid.metadata.columns };
              this.createColumnFilter(obj);
              //SystemLog grid
              this.mainGrid.CustomGrid(obj);
              this.mainGrid.refreshFilters();
              var gridRows = this.lastRecievedJSON.systemLogList.grid.rows;
              if (gridRows.size > 0) {
                this.mainGrid.gridData = gridRows;
                this.mainGrid.setRowSize = this.jsonReader.getSingletons().totalCount;
              }
              else {
                this.mainGrid.gridData = { size: 0, row: [] };
              }
              this.prevRecievedJSON = this.lastRecievedJSON;
            }
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }
        }
      }

    } catch (error) {
      // log the error in SYSTEM LOG
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'SystemLog.ts', "inputDataResult", errorLocation);
    }
  }


  paginationChanged(event: PaginationChangedArgs) {
    this.numstepper.processing = true;
    this.doPaginationChanged();
  }
  /*change data grid*/
  doPaginationChanged(): void {
    this.requestParams = [];
    var maxPage = this.lastRecievedJSON.systemLogList.grid.paging.maxpage;
    //To hold current page
    var currentPage: string = null;
    // Get the FilteredGridColumn
    let selectedFilter: string = this.mainGrid.getFilteredGridColumns();
    // Get the SortedGridColumn
    let selectedSort: string = this.mainGrid.getSortedGridColumn();
    try {
      if (this.numstepper.value > 0) {
        if (((this.numstepper.value <= this.numstepper.maximum) && (this.numstepper.value != this.lastNumber) && (this.numstepper.value != 0))) {
         
          //Get the current page value
          currentPage = (this.numstepper.value).toString();

          this.inputData.cbStart = this.startOfComms.bind(this);
          this.inputData.cbStop = this.endOfComms.bind(this);
          this.inputData.cbResult = (event) => {
            this.inputDataResult(event);
          };
          this.inputData.cbFault = this.inputDataFault.bind(this);
          this.inputData.encodeURL = false;
          this.actionPath = "systemlog.do?";
          this.actionMethod = 'method=displayAngular';
          this.requestParams["currentPage"] = currentPage;
          this.requestParams['selectedFromDateChooser'] = this.fromDateChooser.text;
          this.requestParams['selectedToDateChooser'] = this.toDateChooser.text;
          this.requestParams["maxPage"] = maxPage;
          this.requestParams['selectedSort'] = selectedSort;
          this.requestParams['selectedFilter'] = selectedFilter;
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
          this.inputData.send(this.requestParams);
        }
      }
      this.logger.info("method [doPaginationChanged] - END");
    }
    catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'SystemLog', 0);
    }

  }


  /*change data grid*/
  updateData(): void {
    this.requestParams = [];
    // Get the FilteredGridColumn
    let selectedFilter: string = this.mainGrid.getFilteredGridColumns();
    // Get the SortedGridColumn
    let selectedSort: string = this.mainGrid.getSortedGridColumn();
     
    var maxPage = this.lastRecievedJSON.systemLogList.grid.paging.maxpage;
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "systemlog.do?";
    this.actionMethod = 'method=displayAngular';
    this.requestParams["currentPage"] = 1;
    this.requestParams['selectedFromDateChooser'] = this.fromDateChooser.text;
    this.requestParams['selectedToDateChooser'] = this.toDateChooser.text;
    this.requestParams["maxPage"] = maxPage;
    this.requestParams['selectedSort'] = selectedSort;
    this.requestParams['selectedFilter'] = selectedFilter;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

  }


  setFocusDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      dateField.setFocus();
      errorLocation = 10;
      // dateField.text = this.jsonReader.getSingletons().displayedDate;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [setFocusDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'ErrorLocation.ts', "setFocusDateField", errorLocation);
    }
  }

  validateDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      errorLocation = 10;
      if (dateField.text) {
        errorLocation = 20;
        date = moment(dateField.text, this.dateFormat.toUpperCase(), true);
        errorLocation = 30;
        if (!date.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
            errorLocation = 40;
            this.setFocusDateField(dateField)
          });
          return false;
        }
        if (!this.checkDates()) {
          this.swtAlert.warning('End Date must be later than Start date');
          return;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          errorLocation = 50;
          this.setFocusDateField(dateField)
        });
        return false;
      }
      errorLocation = 60;
      dateField.selectedDate = date.toDate();
      this.updateData();
    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'SystemLog.ts', "validateDateField", errorLocation);
    }

    return true;
  }

  closeHandler() {
    ExternalInterface.call("close");
  }

  doHelp(): void {
    ExternalInterface.call("help");
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
    // this.dataExport.enabled = false;
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
    // this.dataExport.enabled = true;
  }

  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  /**
 * enablePrintButton
 *
 */
  enablePrintButton(value): void {
    this.printButton.enabled = value;
    this.printButton.buttonMode = value;
  }
  /**
  * printPage
  *
  * param event
  *
  * Method to get call the action to get reports
  */
  printPage(): void {
    let errorLocation = 0;
    try {
      ExternalInterface.call('printPage');

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", errorLocation);
    }
  }

  /**
    * report
    *
    * @param type: String
    *
    * This is a report icon action handler method
    */
  report(type: string): void {
    let methodName: string = null;
    methodName = "exportErrors";
    try {
      ExternalInterface.call("buildExportErrorsURL", methodName, type);
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'report', 0);
    }
  }

  checkDates() {

    try {
      var startDate: any;
      var endDate: any;
      if (this.fromDateChooser.text) {
        startDate = moment(this.fromDateChooser.text, this.dateFormat.toUpperCase(), true);
      }
      if (this.toDateChooser.text) {
        endDate = moment(this.toDateChooser.text, this.dateFormat.toUpperCase(), true);
      }

      if (!startDate && endDate) {
        return false;
      }

      if (startDate && endDate && endDate.isBefore(startDate)) {
        return false;
      }

      return true;
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "checkDates", this.errorLocation);

    }
  }
  
  keyDownEventHandler(event) {

  }
  getDataProvider(rows, columnName) {
    if (rows) {

      if (!rows.length)
        rows = [rows];
      let dataProvider = [];
      rows.forEach(row => {
        let content = row[columnName] && row[columnName].content ? row[columnName].content : "";
        if (!dataProvider.some(item => item.value === content)) {
          dataProvider.push({ value: content, label: content });
        }
      });
      dataProvider.sort((a, b) => a.label.localeCompare(b.label)); // sort alphabetically
      return dataProvider;
    } else {
      return [];
    }
  }


  createColumnFilter(obj) {
    
    const dateColumn = obj.columns.column.find(column => column.dataelement === "logDate_Date");
    const dateDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "logDate_Date");
    dateColumn['FilterType'] = 'MultipleSelect'
    dateColumn['dataProvider'] = dateDataProvider;
    dateColumn['FilterInputSearch'] = true;


    const timeColumn = obj.columns.column.find(column => column.dataelement === "logDate_Time");
    const timeDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "logDate_Time");
    timeColumn['FilterType'] = 'MultipleSelect'
    timeColumn['dataProvider'] = timeDataProvider;
    timeColumn['FilterInputSearch'] = true;

    const userIdColumn = obj.columns.column.find(column => column.dataelement === "userId");
    const userIdDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "userId");
    userIdColumn['FilterType'] = 'MultipleSelect'
    userIdColumn['dataProvider'] = userIdDataProvider;
    userIdColumn['FilterInputSearch'] = true;


    const ipAddressColumn = obj.columns.column.find(column => column.dataelement === "ipAddress");
    const ipAddressDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "ipAddress");
    ipAddressColumn['FilterType'] = 'MultipleSelect'
    ipAddressColumn['dataProvider'] = ipAddressDataProvider;
    ipAddressColumn['FilterInputSearch'] = true;


    
    const processColumn = obj.columns.column.find(column => column.dataelement === "process");
    if (processColumn) {
      processColumn['FilterType'] = "InputSearch";
    }



    const actionColumn = obj.columns.column.find(column => column.dataelement === "action");
    const actionDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "action");
    actionColumn['FilterType'] = 'MultipleSelect'
    actionColumn['dataProvider'] = actionDataProvider;
    actionColumn['FilterInputSearch'] = true;
  }



}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: SystemLog }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SystemLog],
  entryComponents: []
})
export class SystemLogModule { }

