import {
  Alert, CommonService, ContextMenuItem, ExternalInterface, HTTPComms, JSONReader, JSONViewer, ScreenVersion, StringUtils, SwtAlert,
  SwtButton, SwtCanvas, SwtCheckBox, SwtComboBox, SwtCommonGrid, SwtLabel, SwtLoadingImage,
  SwtModule, SwtPopUpManager, SwtRadioButtonGroup, SwtRadioItem, SwtToolBoxModule, SwtUtil,
} from 'swt-tool-box';
import {Component, ElementRef, ModuleWithProviders, NgModule, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
declare var instanceElement: any;
@Component({
    selector: 'app-forecast-monitor-options',
    templateUrl: './ForecastMonitorOptions.html',
    styleUrls: ['./ForecastMonitorOptions.css']
})

export class ForecastMonitorOptions extends SwtModule  implements  OnInit, OnDestroy {


  /*********SwtCanvas*********/
  @ViewChild('cvTemplateGridContainer') cvTemplateGridContainer: SwtCanvas; 
  @ViewChild('cvBucketGridContainer') cvBucketGridContainer: SwtCanvas;

  /*********SwtLoadingImage*********/
  @ViewChild("loadingImage") loadingImage: SwtLoadingImage;
  /*********SwtCheckBox*********/
  @ViewChild('multiplier') multiplier: SwtCheckBox;
  @ViewChild('hideWeekend') hideWeekend: SwtCheckBox;
  @ViewChild('cumulativeTotal') cumulativeTotal: SwtCheckBox;
  @ViewChild('hideScenario') hideScenario: SwtCheckBox;
  @ViewChild('hideAssumption') hideAssumption: SwtCheckBox;
  @ViewChild('hideZeroValue') hideZeroValue: SwtCheckBox;
  @ViewChild('hideZeroSum') hideZeroSum: SwtCheckBox;
  @ViewChild('hideTotal') hideTotal: SwtCheckBox;

  /*********SwtButton*********/
  @ViewChild('btnCancel') btnCancel: SwtButton;
  @ViewChild('btnSave') btnSave: SwtButton;
  @ViewChild('btnTemplateDelete') btnTemplateDelete: SwtButton;
  @ViewChild('btnTemplateChange') btnTemplateChange: SwtButton;
  @ViewChild('btnTemplateAdd') btnTemplateAdd: SwtButton;
  @ViewChild('btnBucketAdd') btnBucketAdd: SwtButton;
  @ViewChild('btnBucketChange') btnBucketChange: SwtButton;
  @ViewChild('btnBucketDelete') btnBucketDelete: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  /*********SwtComboBox*********/
  @ViewChild('cbEntity') cbEntity: SwtComboBox;
  @ViewChild('cbCurrency') cbCurrency: SwtComboBox;

  /*********SwtLabel*********/
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('selectedCurrency') selectedCurrency: SwtLabel;
  @ViewChild('templateIdLabel') templateIdLabel: SwtLabel;
  @ViewChild('lblTemplateId') lblTemplateId: SwtLabel;
  @ViewChild('userTempLabel') userTempLabel: SwtLabel;
  @ViewChild('userBuckLabel') userBuckLabel: SwtLabel;
  /*********SwtRadioButtonGroup*********/
  @ViewChild('breakdown') breakdown: SwtRadioButtonGroup;
  @ViewChild('movementRadio') movementRadio: SwtRadioItem;
  @ViewChild('bookRadio') bookRadio: SwtRadioItem;

  /**
   * Communication Objects
   **/
  private baseURL= SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  private  requestParams=[];

  public screenVersion  = new ScreenVersion(this.commonService) ;
  public showJSONPopup: any;

  private swtAlert: SwtAlert;

  private moduleId="";

  private  userTemplateGrid: SwtCommonGrid = null; // ForecastMonitorOptionGrid;
  private  userBucketGrid: SwtCommonGrid = null; // ForecastMonitorOptionGrid;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private  inputData=new HTTPComms(this.commonService);
  private  entityData=new HTTPComms(this.commonService);
  private  saveData=new HTTPComms(this.commonService);
  private  invalidComms="";
  
  private  lastIndex = -1;
  private  closeWindow=false;
  private  screenName="Forecast Monitor Options";
  private  versionNumber="1.1.0002";
  private releaseDate = '20 May 2019';
  public  update=new Object();
  private  menuAccessIdParent=0;

  private  menuAccess = 2;
  public templateData = [];
  public prevSelectedIndex: number = -1;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }


  ngOnDestroy(): any {
    instanceElement = null;
  }

  ngOnInit(): void {
    instanceElement = this;
    this.userTemplateGrid = this.cvTemplateGridContainer.addChild(SwtCommonGrid);
    this.userBucketGrid = this.cvBucketGridContainer.addChild(SwtCommonGrid);
    this.userBucketGrid.editable = true;
    // Assining properties for controls
    this.entityLabel.text = SwtUtil.getPredictMessage('label.forecastMonitor.entity', null);
    this.cbEntity.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.selectEntity', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('label.forecastMonitor.currency', null);
    this.cbCurrency.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.selectCurrency', null);
    this.multiplier.label = SwtUtil.getPredictMessage('label.forecastMonitorOptions.applyCurrencyMultiplier', null);
    this.multiplier.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorOptions.applyCurrencyMultiplier', null);
    this.hideWeekend.label = SwtUtil.getPredictMessage('label.forecastMonitorOptions.hideweekend', null);
    this.hideWeekend.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorOptions.hideweekend', null);
    this.cumulativeTotal.label = SwtUtil.getPredictMessage('label.forecastMonitorOptions.cumulativetotal', null);
    this.cumulativeTotal.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorOptions.cumulativetotal', null);
    this.hideScenario.label = SwtUtil.getPredictMessage('label.forecastMonitorOptions.hidescenario', null);
    this.hideScenario.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorOptions.hidescenario', null);
    this.hideAssumption.toolTip = SwtUtil.getPredictMessage('label.forecastMonitorOptions.hideassumption', null);
    this.hideAssumption.label = SwtUtil.getPredictMessage('tooltip.forecastMonitorOptions.hideassumption', null);
    this.hideZeroValue.label = SwtUtil.getPredictMessage('label.forecastMonitorOptions.hidezerovalue', null);
    this.hideZeroValue.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorOptions.hidezerovalue', null);
    this.hideZeroSum.label = SwtUtil.getPredictMessage('label.forecastMonitorOptions.hidezerosum', null);
    this.hideZeroSum.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorOptions.hidezerosum', null);
    this.hideTotal.label = SwtUtil.getPredictMessage('label.forecastMonitorOptions.hidetotal', null);
    this.hideTotal.toolTip = SwtUtil.getPredictMessage('label.forecastMonitorOptions.hidetotal', null);
    this.btnCancel.label = SwtUtil.getPredictMessage('button.forecastMonitor.cancel', null);
    this.btnCancel.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.cancel', null);
    this.btnSave.label = SwtUtil.getPredictMessage('button.forecastMonitor.save', null);
    this.btnSave.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.save', null);
    this.btnTemplateAdd.label = SwtUtil.getPredictMessage('button.forecastMonitor.add', null);
    this.btnTemplateAdd.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.add', null);
    this.btnTemplateChange.label = SwtUtil.getPredictMessage('button.forecastMonitor.change', null);
    this.btnTemplateChange.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.change', null);
    this.btnTemplateDelete.label = SwtUtil.getPredictMessage('button.forecastMonitor.delete', null);
    this.btnTemplateDelete.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.delete', null);
    this.btnBucketAdd.label = SwtUtil.getPredictMessage('button.forecastMonitor.add', null);
    this.btnBucketAdd.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.add', null);
    this.btnBucketChange.label = SwtUtil.getPredictMessage('button.forecastMonitor.change', null);
    this.btnBucketChange.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.change', null);
    this.btnBucketDelete.label = SwtUtil.getPredictMessage('button.forecastMonitor.delete', null);
    this.btnBucketDelete.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.delete', null);
    this.userBuckLabel.text = SwtUtil.getPredictMessage('label.forecastMonitorOption.userBuckets', null);
    this.userTempLabel.text = SwtUtil.getPredictMessage('label.forecastMonitorOption.userTemplates', null);
  }

  onLoad() {
    try {
      this.userTemplateGrid.onRowClick = (event) => {
        this.onTemplateGridCellClick(event);
      };
      this.userBucketGrid.onRowClick = (event) => {
        this.onBucketGridCellClick(event)
      };
      this.userBucketGrid.enableDisableCells=(row) => {
        return this.enableDisableRow(row);
      };

      this.menuAccessIdParent=ExternalInterface.call('eval', 'menuAccessIdParent');
      this.inputData.cbStart=this.startOfComms.bind(this);
      this.inputData.cbStop=this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.inputData.cbFault=this.inputDataFault.bind(this);
      this.inputData.encodeURL=false;
      this.entityData.cbStart=this.startOfComms.bind(this);
      this.entityData.cbStop=this.endOfComms.bind(this);
      this.entityData.cbResult = (event) => {
        this.entityDataResult(event);
      };
      this.entityData.cbFault=this.inputDataFault.bind(this);
      this.entityData.encodeURL=false;
      this.btnTemplateChange.enabled = false;
      this.btnBucketChange.enabled = false;
      this.btnTemplateDelete.enabled = false;
      this.btnBucketDelete.enabled = false;
      // if view access, disable the ok button
      if ( this.menuAccessIdParent == 1) {
        this.btnSave.enabled=false;
      }

      this.initializeMenus();
      this.actionPath="forecastMonitor.do?";
      this.actionMethod="method=displayForecastMonitorOptions";
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.entityData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.saveData.cbResult = (event) => {
        this.saveDataResult(event);
      };
      this.saveData.cbFault=this.inputDataFault.bind(this);
      this.saveData.encodeURL=false;


    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'Forecast Monitor Options', 'onLoad');
    }
  }
//fixme
    validateGrid(event):void {
      let oldValue = event.listData.oldValue;
      let newValue = event.listData.newValue;
      let  bucketList =this.userBucketGrid.originalDataprovider;
      for( let i = 0 ; i < this.userBucketGrid.dataProvider.length; i++) {
        if (String(newValue) != String(oldValue)) {
        if (String(newValue) != String(bucketList[i].daysto)) {
          //if (this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto == String(bucketList[i].daysto)) {
            if (oldValue == "T" && Number(this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].bucketid) == 1 && this.userBucketGrid.dataProvider.length > 1) {
              this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'lesserThanNext', 'The value should be lesser than the next value'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
              this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto = oldValue;
              break;
            }
          if (this.userBucketGrid.dataProvider.length != this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].bucketid && this.userBucketGrid.dataProvider.slickgrid_rowcontent[i+1].daysto.content != undefined &&
            Number(this.userBucketGrid.dataProvider[i+1].daysto) <= Number(newValue)) {
            this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'lesserThanNext', 'The value should be lesser than the next value'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
            this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto = oldValue;
            break;
          }
          if (this.userBucketGrid.dataProvider.length != 1 && this.userBucketGrid.dataProvider.length == this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].bucketid && this.userBucketGrid.dataProvider[i-1] != undefined &&
            (Number(this.userBucketGrid.dataProvider[i-1].daysto) > Number(newValue)) || (oldValue == "T" && Number(this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex]) != 1)) {
            this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'greaterThanPrevious', 'The value should be greater than the previous value'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
            this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto = oldValue;
            break;
          }
          /*else if (bucketList.length != 1 && bucketList.length() == event.target.data.bucketid &&
                (Number(event.currentTarget.dataProvider[Number(event.target.data.bucketid) - 2].daysto) > Number(event.target.cbo.selectedItem.value)) || (oldValue == "T" && Number(event.target.data.bucketid) != 1)) {
                this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'greaterThanPrevious', 'The value should be greater than the previous value'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
                this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto = oldValue;
                break;
              }*/
         // }
         /* if (this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto == String(bucketList[i].daysto)) {
            // If the value is lesser than previous, then show the alert
            if (bucketList.length() != 1 && bucketList.length() == event.target.data.bucketid &&
              (Number(event.currentTarget.dataProvider[Number(event.target.data.bucketid) - 2].daysto) > Number(event.target.cbo.selectedItem.value)) || (oldValue == "T" && Number(event.target.data.bucketid) != 1)) {
              this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'greaterThanPrevious', 'The value should be greater than the previous value'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
              this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto = oldValue;
              break;
            }

            // If the value is lesser than previous, then show the alert
            else if (event.target.cbo.selectedItem.value == "T" && event.target.data.bucketid == Number(event.target.data.bucketid) > 1) {
              this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'greaterThanPrevious', 'The value should be greater than the previous value'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
              this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto = oldValue
              break;
            }
            // If the value is lesser than previous, then show the alert and check today & today+1
            else if (oldValue != "T" && newValue == "T+1" && Number(this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].bucketid) > 2) {
              this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'greaterThanPrevious', 'The value should be greater than the previous value'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
              this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto = oldValue
              break;
            }
            else {
              /!*xml.daysto = event.target.cbo.selectedItem.value;
              if (xml.modifystate == "save")
                xml.modifystate = "saveupdate";
              else
                xml.modifystate = "update";*!/
            }
          }*/
        } else {
          // set the day value
          this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto = oldValue;
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'bucketExist', 'Selected bucket already exists'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
          break;
        }
      }
    }
    }

  private enableDisableRow(row:any):boolean
  {
    if(row.id == this.prevSelectedIndex )
      return true;
    return false;
  }
  startOfComms(): void {
    this.disableFields();
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.enableFields();
  }


  /**
   * Disable interface, turn off certain UI elements when a request is made to the server
   */
  private  disableFields(): void {
    // Setting the visibility of refresh button to false
    this.btnSave.enabled=false;
    this.btnSave.buttonMode=false;
    this.btnCancel.enabled=false;
    this.btnCancel.buttonMode=false;
  }

  /**
   * Enable interface, turn on certain UI elements when a request is made to the server
   */
  private  enableFields(): void {
    if (this.menuAccessIdParent != 1) {
      this.btnSave.enabled=true;
      this.btnSave.buttonMode=true;
    }
    this.btnCancel.enabled=true;
    this.btnCancel.buttonMode=true;
  }



  /**
   * doHelp
   *
   * @param event: Event
   *
   * Function is called when "Help" button is click. Displays help window
   */

  doHelp(event): void {
    ExternalInterface.call("help");
  }


  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   * @param event:FaultEvent
   **/
  private  inputDataFault(event): void {
    this.invalidComms=event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }


  /**
   * The  initializes the menus in the right click event on the Entity Monitor options screen.
   * The links are redirected to their respective pages.
   */
  private  initializeMenus(): void {
    // Creating a new instance for AboutScreen
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
    let contextMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    contextMenuItem.MenuItemSelect = this.showJSONSelect.bind(this);
    this.screenVersion.svContextMenu.customItems.push(contextMenuItem);
    this.contextMenu = this.screenVersion.svContextMenu;
  }

  /**
   * This  is used to display the XML for the monitor selected in the monitor combo
   * @param event :ContextMenuEvent
   */
  private  showJSONSelect(event): void {
    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });

    // showXML.baseURL="forecastMonitor.do?method=displayForecastMonitorDetails";
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }



  /**
   * This method is called when click the user template grid
   * @param event:CellEvent
   */
  private  onTemplateGridCellClick(event): void {
    if(this.userTemplateGrid.selectedIndices.length > 0) {
      this.btnTemplateChange.enabled = true;
      this.btnTemplateDelete.enabled = true;
    } else {
      this.btnTemplateChange.enabled = false;
      this.btnTemplateDelete.enabled = false;
    }
  }

  /**
   * This method is called when click the user bucket grid
   * @param event:CellEvent
   */
  private  onBucketGridCellClick(event): void {
    if (this.userBucketGrid.selectedIndices.length > 0) {
      this.btnBucketChange.enabled = true;
      if (this.userBucketGrid.selectedItem.bucketid.content == "1" || this.userBucketGrid.selectedItem.bucketid.content == "2") {
        this.btnBucketDelete.enabled = false;
      } else
        this.btnBucketDelete.enabled = true;
    } else {
      this.btnBucketChange.enabled = false;
      this.btnBucketDelete.enabled = false;
      // for each( xml in this.lastRecievedJSON.monitoroptions.bucketgrid.rows.row.(modifystate != "delete")){
      //   xml.bucketstate.expandenable = true;
      //   xml.daysto.expandenable = true;
      // }
      // this.userBucketGrid.active = this.lastRecievedJSON.selects.select.(@id=="type");
    }
  }



  /**
   * This method closes the Entity Monitor Options window upon saving any changes made.<br>
   * @param event:ResultEvent
   */
  private  saveDataResult(event): void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON=event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      // if Request Reply Status is ok
      if (this.jsonReader.getRequestReplyStatus()) {
        ExternalInterface.call("closeWindow");
      }
    }
  }


  /**
   * This method closes the Entity Monitor Options window upon saving any changes made.<br>
   * @param event:ResultEvent
   */
  private  entityDataResult(event): void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      let xmlReaderForEntity: JSONReader =new JSONReader();
      let entityXML=(event);
      xmlReaderForEntity.setInputJSON(entityXML);
      if (xmlReaderForEntity.getRequestReplyStatus()) {
        this.cbEntity.setComboData(xmlReaderForEntity.getSelects());
        this.selectedEntity.text =this.cbEntity.selectedItem.value;
        this.cbCurrency.setComboData(xmlReaderForEntity.getSelects());
        this.selectedCurrency.text = this.cbCurrency.selectedItem.value;
      }
    }
    if (StringUtils.trim(this.cbCurrency.selectedLabel) == "") {
      this.swtAlert.error(SwtUtil.getPredictMessage("alert.currencyAccess", null), 'Error');
    }
  }

  deepCopy( mainObj ) {
    const objCopy = []; // objCopy will store a copy of the mainObj
    let key;
    for ( key in mainObj ) {
      objCopy[key] = mainObj[key]; // copies each property to the objCopy object
    }
    return objCopy;
  }


  /**
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   * */
  private  inputDataResult(event): void {
    this.lastRecievedJSON=(event);
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    if (this.jsonReader.getRequestReplyStatus()) {
      if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
        this.cbEntity.setComboData(this.jsonReader.getSelects());
        this.selectedEntity.text=this.cbEntity.selectedItem.value;
        this.cbCurrency.setComboData(this.jsonReader.getSelects());
        this.selectedCurrency.text=this.cbCurrency.selectedItem.value;

        if (this.lastRecievedJSON.monitoroptions.options.multiplier == "Y") {
          this.multiplier.selected = true;
        } else {
          this.multiplier.selected = false;
        }

        if (this.lastRecievedJSON.monitoroptions.options.hideweekend == "Y") {
          this.hideWeekend.selected = true;
        } else {
          this.hideWeekend.selected = false;
        }
        if (this.lastRecievedJSON.monitoroptions.options.hidezerosum == "Y") {
          this.hideZeroSum.selected = true;
        } else {
          this.hideZeroSum.selected = false;
        }

        if (this.lastRecievedJSON.monitoroptions.options.hidezerovalue == "Y") {
          this.hideZeroValue.selected = true;
        } else {
          this.hideZeroValue.selected = false;
        }

        if (this.lastRecievedJSON.monitoroptions.options.cumulativetotal == "Y") {
          this.cumulativeTotal.selected = true;
        } else {
          this.cumulativeTotal.selected = false;
        }

        if (this.lastRecievedJSON.monitoroptions.options.hidetotal == "Y") {
          this.hideTotal.selected = true;
        } else {
          this. hideTotal.selected = false;
        }

        if (this.lastRecievedJSON.monitoroptions.options.hideassumption == "Y") {
          this.hideAssumption.selected = true;
        } else {
          this.hideAssumption.selected = false;
        }

        if (this.lastRecievedJSON.monitoroptions.options.hidescenario == "Y") {
          this.hideScenario.selected = true;
        } else {
          this.hideScenario.selected = false;
        }

        if (!this.jsonReader.isDataBuilding()) {
            this.userTemplateGrid.CustomGrid(this.lastRecievedJSON.monitoroptions.templategrid.metadata);
          if (this.lastRecievedJSON.monitoroptions.templategrid.rows.size > 0) {
            this.userTemplateGrid.rowHeight = 24;
            this.userTemplateGrid.gridData = {row: this.lastRecievedJSON.monitoroptions.templategrid.rows.row, size: this.lastRecievedJSON.monitoroptions.templategrid.rows.size};
          }

          this.userBucketGrid.gridComboDataProviders(this.lastRecievedJSON.monitoroptions.selects);
          this.userBucketGrid.CustomGrid(this.lastRecievedJSON.monitoroptions.bucketgrid.metadata);
            if (this.lastRecievedJSON.monitoroptions.bucketgrid.rows.size > 0) {
              this.userBucketGrid.rowHeight = 24;
              this.userBucketGrid.gridData = {row: this.lastRecievedJSON.monitoroptions.bucketgrid.rows.row, size: this.lastRecievedJSON.monitoroptions.bucketgrid.rows.size};
            }
          this.userBucketGrid.ITEM_CHANGED.subscribe((event) => {
            this.validateGrid(event);
          });

        } else {
          // dataBuildingText.visible=true;
        }

        this.prevRecievedJSON=this.lastRecievedJSON;
       // this.ccyList=(this.lastRecievedJSON["selects"].children().(@id.search("currency") != -1)).option;

      }
    } else {
      if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        if (this.lastRecievedJSON.request_reply.closewindow == "true") {
          this.closeWindow=true;
        }
      }
      this.swtAlert.error(
        this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(),
        'Error',
        Alert.OK,
        this,
        this.errorHandler.bind(this));
    }

    if (StringUtils.trim(this.cbCurrency.selectedLabel) == "") {
      this.swtAlert.error( 'Invalid: your role does not specify access to currencies/groups for this entity', 'Error');
    }
     
  }



  /**
   * Combobox event handling when there is a change in the in one of the combo's
   * @param e :Event
   */
  changeCombo(e): void {
    this.selectedCurrency.text = this.cbCurrency.selectedItem.value;
  }

  /**
   * Update the data, this is called whenever a fresh of the data is required.
   * This could be called from either a change in a combobox selection of from the timer
   **/
  updateData(): void {
    if (StringUtils.trim(this.cbCurrency.selectedLabel) == "") {
      this.swtAlert.show( 'Invalid: your role does not specify access to currencies/groups for this entity', 'Error');
    } else {
      this.btnSave.enabled=false;
      this.requestParams=[];
      this.actionMethod="method=saveForecastMonitorOptions";
      let multipliers="";
      let hideWeekendsVal="";
      let hideTotalVal="";
      let  hideAssumptionVal="";
      let hideScenarioVal="";
      let  cumulativeTotals="";
      let hideZeroSums="";
      let hideZeroValues="";
      let entity="";
      let currency="";
      let updateTemplateString= [];
      let updateBucketString= [];
      if (this.multiplier.selected) {
        multipliers="Y";
      } else {
        multipliers="N";
      }

      if (this.hideWeekend.selected) {
        hideWeekendsVal="Y";
      } else {
        hideWeekendsVal="N";
      }
      if (this.hideTotal.selected) {
        hideTotalVal="Y";
      } else {
        hideTotalVal="N";
      }
      // setting the check box value for Hide Weekends
      if (this.hideAssumption.selected) {
        hideAssumptionVal="Y";
      } else {
        hideAssumptionVal="N";
      }

      if (this.hideScenario.selected) {
        hideScenarioVal="Y";
      } else {
        hideScenarioVal="N";
      }

      if (this.cumulativeTotal.selected) {
        cumulativeTotals="Y";
      } else {
        cumulativeTotals="N";
      }

      if (this.hideZeroSum.selected) {
        hideZeroSums="Y";
      } else {
        hideZeroSums="N";
      }

      if (this.hideZeroValue.selected) {
        hideZeroValues="Y";
      } else {
        hideZeroValues="N";
      }
      let templateChanges = this.userTemplateGrid.changes.getValues();
      let bucketsChanges = this.userBucketGrid.dataProvider;
      for (let i= 0; i < templateChanges.length; i++) {
        let operation = templateChanges[i].crud_operation.substring(0, 1);
        if(operation == "I")
          operation = "save";
        else if(operation == "U")
          operation = 'update';
        else
          operation = 'delete';
        this.requestParams[templateChanges[i].crud_data.currency + "_D_" + templateChanges[i].crud_data.templateid + "_D_" + templateChanges[i].crud_data.entity] = operation;
        updateTemplateString.push(templateChanges[i].crud_data.currency + "_D_" + templateChanges[i].crud_data.templateid + "_D_" + templateChanges[i].crud_data.entity)
      }
      //Buckets Changes
      for (let i= 0; i < bucketsChanges.length; i++) {
        updateBucketString.push(bucketsChanges[i].bucketid + "_" + bucketsChanges[i].daysto + "_" + bucketsChanges[i].slickgrid_rowcontent.bucketstate.content  + "_" + "update")
      }
      // collect all keys
      this.requestParams["updateTemplate"]=updateTemplateString.toString();
      this.requestParams["updateBucket"]=updateBucketString.toString();
      entity=this.cbEntity.selectedLabel;
      currency=this.cbCurrency.selectedLabel;
      this.requestParams["forecastMonitorOptions.hideZeroSum"]=hideZeroSums;
      this.requestParams["forecastMonitorOptions.hideZeroValue"]=hideZeroValues;
      this.requestParams["forecastMonitorOptions.cumulativeBucketTotal"]=cumulativeTotals;
      this.requestParams["forecastMonitorOptions.useCurrencyMultiplier"]=multipliers;
      this.requestParams["forecastMonitorOptions.hideWeekends"]=hideWeekendsVal;
      this.requestParams["forecastMonitorOptions.hideTotal"]=hideTotalVal;
      this.requestParams["forecastMonitorOptions.hideAssumption"]=hideAssumptionVal;
      this.requestParams["forecastMonitorOptions.hideScenario"]=hideScenarioVal;
      this.requestParams["forecastMonitorOptions.entity"]=entity;
      this.requestParams["forecastMonitorOptions.currency"]=currency;
      this.saveData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.saveData.send(this.requestParams);
      this.userBucketGrid.changes.clear();
      this.userTemplateGrid.changes.clear();
    }

  }


  /**
   * Listener for Error alert message and
   * perform action for when click ok button
   * @param event:CloseEvent
   * */
  private  errorHandler(event): void {
    if (event.detail == Alert.OK) {
      if (this.closeWindow) {
        this.closeHandler();
        this.closeWindow=false;
      }
    }
  }

  /**
   * Function called to close the window when close button is clicked
   */
  closeHandler(): void {
    ExternalInterface.call("closeWindow");
  }

  addNewBucket(): void {
    this.btnBucketChange.enabled = false;
    this.btnBucketDelete.enabled = false;
    this.userBucketGrid.selectedIndex = -1;
    let flag = true;
    let lastValue = 1;
    let lastBucketId = 1;
    let daysToValue: string;
    for( let i = 0 ; i<  this.userBucketGrid.dataProvider.length; i++) {
      if (this.userBucketGrid.dataProvider[i].daysto == "30") {
        flag = false;
        this.swtAlert.warning(SwtUtil.getPredictMessage("alert.forecastMonitorOption.maxValue", null),  'Warning');
        break;
      } else {
        if (this.userBucketGrid.dataProvider[i].daysto != "T" && this.userBucketGrid.dataProvider[i].daysto != "T+1") {
          lastValue = Number(this.userBucketGrid.dataProvider[i].daysto) + 1;
          lastBucketId = Number(this.userBucketGrid.dataProvider[i].bucketid) + 1;
        } else if(this.userBucketGrid.dataProvider[i].daysto == "T") {
          lastValue = 2;
          lastBucketId = 2;
        } else if(this.userBucketGrid.dataProvider[i].daysto == "T+1") {
          lastValue = 3;
          lastBucketId = 3;
        }
      }
    }
    if (lastValue == 1) {
      daysToValue = "T";
    } else if (lastValue == 2) {
      daysToValue = "T+1";
    } else {
      daysToValue = String(lastValue);
    }
    if (flag) {
      let newRow = {
        bucketid : {content :String(lastBucketId)},
        daysto :{content :  String(daysToValue)},
        bucketstate :{content :  "true"},
        modifystate :{content :  "save"},
      };
      this.userBucketGrid.appendRow(newRow, true);
      this.userBucketGrid.originalDataprovider = [...this.userBucketGrid.dataProvider]; //this line is added for combo change
      this.lastIndex = lastBucketId;
    }


  }

  /**
   * Function called to change selected bucket
   */
  changeBucket(): void {
    this.prevSelectedIndex = this.userBucketGrid.selectedIndex;
    this.userBucketGrid.refresh();

  }

  /**
   * Function called to delete bucket
   */
  deleteBucket(): void {
    this.userBucketGrid.removeSelected();
    this.btnBucketDelete.enabled = false;
    this.btnBucketChange.enabled = false;
  }

  /**
   * This method is called when clcik the option button
   * @param method:string
   */
 templateOptionClick(method: string): void {
    let usertemplate = new Object();
    let wrapperFunction: string = null;
    this.templateData = [...this.userTemplateGrid.dataProvider];
    if (method == "add") {
      wrapperFunction = "openTemplateOptionsWindow";
      usertemplate = String(this.lastRecievedJSON.monitoroptions.templategrid.rows);
      ExternalInterface.call(wrapperFunction,usertemplate);
    } else if (method == "change") {
      usertemplate["entity"] = String(this.userTemplateGrid.selectedItem.entity.content);
      usertemplate["currency"] = String(this.userTemplateGrid.selectedItem.currency.content);
      usertemplate["template"] = String(this.userTemplateGrid.selectedItem.templateid.content);
      usertemplate["xml"] =String(this.lastRecievedJSON.monitoroptions.templategrid.rows);
      wrapperFunction = "openTemplateOptionsWindow";
      // call the jsp method for open options screen
      ExternalInterface.call(wrapperFunction,usertemplate);
    } else {
      this.userTemplateGrid.removeSelected();
      //fixme
      // for each ( newXml in this.lastRecievedJSON.monitoroptions.templategrid.rows.row){
      //   if (newXml.currency == String(this.userTemplateGrid.selectedItem.currency) &&
      //     newXml.entity == String(this.userTemplateGrid.selectedItem.entity)&&
      //     newXml.templateid == String(this.userTemplateGrid.selectedItem.templateid)){
      //     newXml.modifystate = "delete";
      //   }
      // }
      this.btnTemplateDelete.enabled = false;
      this.btnTemplateChange.enabled = false;
     // this.userTemplateGrid.gridData = this.deepCopy(this.lastRecievedJSON.monitoroptions.templategrid.rows.row).filter(x=>x.modifystate != "delete");
    }


  }



  /**
   * The  is called when reload the options screen
   * @param entity:string
   * @param currency:string
   * @param template:string
   */
  public  reloadOption(entity: string, currency: string, template: string): void {
    let changeFlag = true;
    let row;
    let newRow = {
      currency : {content :String(currency)},
      templateid :{content :  String(template)},
      modifystate :{content :  "save"},
      entity :{content :  String(entity)},
    };
    for ( let i = 0; i< this.userTemplateGrid.dataProvider.length; i++) {
      row = this.userTemplateGrid.dataProvider[i];

      if (this.userTemplateGrid.dataProvider[i].currency == currency && String(this.userTemplateGrid.dataProvider[i].entity) == entity) {
        this.userTemplateGrid.dataProvider[i].templateid = template;
        this.userTemplateGrid.dataProvider[i].slickgrid_rowcontent.templateid.content = template;
        this.userTemplateGrid.dataProvider[i].modifystate = "update";
        this.userTemplateGrid.dataProvider[i].slickgrid_rowcontent.modifystate.content = "update";
        changeFlag = false;
        this.userTemplateGrid.refresh();
        break;
      }
    }
    if (changeFlag) {
      this.userTemplateGrid.appendRow(newRow, true);
    }
this.userTemplateGrid.selectedIndex = -1;
    this.btnTemplateChange.enabled = false;
    this.btnTemplateDelete.enabled = false;

  }
  

  /**
   * Update the data, this is called whenever a fresh of the data is required.
   * This could be called from either a change in a combobox selection of from the timer
   * @param autorefresh:string
   **/
  updateEntityData(): void {
    this.requestParams=[];
    let currency: string=this.cbCurrency.selectedLabel;
    this.requestParams["selectedCurrency"]=currency;
    let entity: string=this.cbEntity.selectedLabel;
    this.requestParams["selectedEntityId"]=entity;
    this.entityData.cbStart=this.startOfComms.bind(this);
    this.entityData.cbStop=this.endOfComms.bind(this);
    this.entityData.send(this.requestParams);
  }

}


// Define lazy loading routes
const routes: Routes = [
  { path: '', component: ForecastMonitorOptions }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ForecastMonitorOptions],
  entryComponents: []
})
export class ForecastMonitorOptionsModule { }
