import { LogGridTab } from './com/swallow/predict/AlertInstanceSummary/AlertInstanceDisplay/Tabs/LogGridTab/LogGridTab';
import { Balances } from './com/swallow/predict/AcctMaintAdd/Tabs/Balances/Balances';
import { GeneralMaint } from './com/swallow/predict/AcctMaintAdd/Tabs/GeneralMaint/GeneralMaint';
import { Sweeping } from './com/swallow/predict/AcctMaintAdd/Tabs/Sweeping/Sweeping';
import { WorkFlowOptionsPopUp } from './com/swallow/predict/WorkFlowMonitor/WorkFlowOptions/WorkFlowOptionsPopUp';
import { BrowserModule } from '@angular/platform-browser';
import {
  ApplicationRef,
  ComponentFactoryResolver,
  CUSTOM_ELEMENTS_SCHEMA,
  NgModule,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import {
  SwtToolBoxModule,
  CommonService,
  Alert,
  SwtCommonGrid,
  SwtTextInput,
  VBox,
  HBox,
  SwtLabel,
  SwtTextArea, SwtComboBox,
} from 'swt-tool-box';
import { RouterModule, Routes, UrlSerializer, DefaultUrlSerializer, UrlTree } from '@angular/router';
import {Type} from '@angular/core/src/type';
import {AppRoutingRoutingModule} from './app-routing.module';
import {ListValues} from './com/swallow/pcm/ListValues/ListValues';
import {FlexLayoutModule} from '@angular/flex-layout';
import {GeneralTab} from './com/swallow/pcm/AccountGroupsMaintenance/tabs/GeneralTab/GeneralTab';
import {CutOffTab} from './com/swallow/pcm/AccountGroupsMaintenance/tabs/CutOffTab/CutOffTab';
import {SpreadingTab} from './com/swallow/pcm/AccountGroupsMaintenance/tabs/SpreadingTab/SpreadingTab';
import {LiquidityTab} from './com/swallow/pcm/AccountGroupsMaintenance/tabs/LiquidityTab/LiquidityTab';
import { CutOffBuildQuery } from './com/swallow/pcm/AccountGroupsMaintenance/tabs/CutOffBuildQuery/CutOffBuildQuery';
import { ThresholdAmounts } from './com/swallow/pcm/AccountGroupsMaintenance/tabs/ThresholdAmounts/ThresholdAmounts';
import {OptionsPopUp} from './com/swallow/pcm/OptionsPopUp/OptionsPopUp';
import {AppComponent} from './app.component';
import {FourEyesProcess} from './com/swallow/pcm/FourEyesProcess/FourEyesProcess';
import { CategoryList } from './com/swallow/pcm/PCMBreakdownMonitor/popUp/CategoryList/CategoryList';
import {MessageDetails} from './com/swallow/pcm/MessageDetails/MessageDetails';
import { ErrorsUpdatePayments } from './com/swallow/pcm/PCMBreakdownMonitor/popUp/ErrorsUpdatePayments/ErrorsUpdatePayments';
import { ConfirmRelease } from './com/swallow/pcm/PCMBreakdownMonitor/popUp/ConfirmRelease/ConfirmRelease';
import { ExportPages } from './com/swallow/pcm/PCMBreakdownMonitor/popUp/ExportPages/ExportPages';
import {OptionsSetting} from './com/swallow/predict/OptionsSetting/OptionsSetting';
import {CorporateAccountDetails} from "./com/swallow/predict/CentralBankMonitor/CorporateAccountDetails/CorporateAccountDetails";
import {FontSetting} from './com/swallow/predict/FontSetting/FontSetting';
import { SaveFilterPopUp } from './com/swallow/predict/MovementSummaryDisplay/SaveFilterPopUp/SaveFilterPopUp';
import {AddSumWindow} from "./com/swallow/predict/EntityMonitor/PersonalEntityList/AddSumWindow/AddSumWindow";
import {RatePopUp} from "./com/swallow/predict/RatePopUp/RatePopUp";
import { CombinedView } from './com/swallow/predict/IntraDayLiquidityMonitor/tabs/CombinedView/CombinedView';
import { GlobalView } from './com/swallow/predict/IntraDayLiquidityMonitor/tabs/GlobalView/GlobalView';
import { GroupAnalysis } from './com/swallow/predict/IntraDayLiquidityMonitor/tabs/GroupAnalysis/GroupAnalysis';
import { General } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/General/General';
import { Identification } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/Identification/Identification';
import { Instance } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/Instance/Instance';
import { GuiHighlight } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/GuiHighlight/GuiHighlight';
import { Events } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/Events/Events';
import { ShowXML } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/ShowXML/ShowXML';
import { IntraDayLiquidityMonitor } from './com/swallow/predict/IntraDayLiquidityMonitor/IntraDayLiquidityMonitor';
import {ChartTabComponent} from "./com/swallow/predict/IntraDayLiquidityMonitor/ILMThroughPutRatioMonitor/control/ChartTab/ChartTabComponent";
import { CustomSummary } from './com/swallow/predict/AlertInstanceSummary/CustomSummary/CustomSummary';
import { AttributeXML } from './com/swallow/predict/AlertInstanceSummary/AttributeXML/AttributeXML';
import { AttributesTab } from './com/swallow/predict/AlertInstanceSummary/AlertInstanceDisplay/Tabs/AttributesTab/AttributesTab';
import { XmlTab } from './com/swallow/predict/AlertInstanceSummary/AlertInstanceDisplay/Tabs/XmlTab/XmlTab';
import { PromtParamsPopup } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/Identification/PromtParamsPopup/PromtParamsPopup';
import { MsdFontSetting } from './com/swallow/predict/MovementSummaryDisplay/MsdFontSetting/MsdFontSetting';
import { MessageTab } from './com/swallow/predict/AlertInstanceSummary/AlertInstanceDisplay/Tabs/MessageTab/MessageTab';
import { MonacoEditorModule } from 'ngx-monaco-editor';
import { AddColsPopUp } from './com/swallow/predict/MovementSummaryDisplay/AdditionalColumns/AddColsPopUp/AddColPopUp';
import { ProgressBar } from './com/swallow/predict/MovementMatchSummaryDisplay/ProgressBar/ProgressBar';
import { CcyExpectedTime } from './com/swallow/predict/CriticalMvtUpdateMaintenance/CriticalMvtUpdateDetail/CcyExpectedTime/CcyExpectedTime';
import { UsersTab } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/Events/EventsAdd/ConfigureRecipients/Tab/UsersTab/UsersTab';
import { RolesTab } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/Events/EventsAdd/ConfigureRecipients/Tab/RolesTab/RolesTab';
import { OtherEmailTab } from './com/swallow/predict/EnhancedAlerting/ScenarioDetail/Tabs/Events/EventsAdd/ConfigureRecipients/Tab/OtherEmailTab/OtherEmailTab';
import { MultiMvtSummary } from './com/swallow/predict/MultipleMvtActions/MultiMvtSummary/MultiMvtSummary';
import { ProcessWindow } from './com/swallow/predict/MultipleMvtActions/ProcessWindow/ProcessWindow';

export class CustomeUrlSerializer implements UrlSerializer {
  parse(url: any): UrlTree {
      let dus = new DefaultUrlSerializer();
      return dus.parse(url);
  }
  serialize(tree: UrlTree): any {
      let path = "";
      return path;
  }
}

const components = [ Alert,
  SwtTextInput,
  VBox,
  HBox,
  SwtLabel,
  SwtTextArea,
  SwtComboBox,
  ListValues,
  OptionsPopUp,
  AppComponent,
  GeneralTab,
  CutOffTab,
  SpreadingTab,
  LiquidityTab,
  CutOffBuildQuery,
  ThresholdAmounts,
  FourEyesProcess,
  CategoryList,
  MessageDetails,
  ErrorsUpdatePayments,
  ConfirmRelease,
  ExportPages,
  OptionsSetting,
  WorkFlowOptionsPopUp,
  CorporateAccountDetails,
  FontSetting,
  MsdFontSetting,
  SaveFilterPopUp,
  AddSumWindow,
  RatePopUp,
  PromtParamsPopup,
  GroupAnalysis,
  GlobalView,
  CombinedView,
  General,
  Identification,
  Instance,
  GuiHighlight,
  AddColsPopUp,
  Balances,
  GeneralMaint,
  Sweeping,
  Events, ShowXML,IntraDayLiquidityMonitor, ChartTabComponent,CustomSummary,AttributeXML, AttributesTab, XmlTab,MessageTab, LogGridTab,ProgressBar, CcyExpectedTime, UsersTab, RolesTab, OtherEmailTab, MultiMvtSummary, ProcessWindow

];
@NgModule({
  declarations: [
    // Main
    ListValues,
    GeneralTab,
    CutOffTab,
    SpreadingTab,
    LiquidityTab,
    CutOffBuildQuery,
    ThresholdAmounts,
    OptionsPopUp,
    AppComponent,
    FourEyesProcess,
    CategoryList,
    MessageDetails,
    ErrorsUpdatePayments,
    ConfirmRelease,
    ExportPages,
    OptionsSetting,
    WorkFlowOptionsPopUp,
    CorporateAccountDetails,
    FontSetting,
    MsdFontSetting,
    SaveFilterPopUp,
    PromtParamsPopup,
    AddSumWindow,
    RatePopUp,
    GroupAnalysis,
    GlobalView,
    CombinedView,
    General,
    Identification,
    Instance,
    GuiHighlight,
    Events,
    ShowXML,
    Balances,
    GeneralMaint,
    Sweeping,
    IntraDayLiquidityMonitor,
    LogGridTab,
    ChartTabComponent,CustomSummary, AttributeXML, AttributesTab, XmlTab,MessageTab,AddColsPopUp,ProgressBar, CcyExpectedTime, UsersTab, RolesTab, OtherEmailTab, MultiMvtSummary, ProcessWindow


  ],
  imports: [
    BrowserModule,
    SwtToolBoxModule,
    HttpClientModule,
    AppRoutingRoutingModule,
    MonacoEditorModule.forRoot(),
    FlexLayoutModule



  ],
  providers: [CommonService,
      { provide: UrlSerializer, useClass: CustomeUrlSerializer }
    ],

  bootstrap: [AppComponent],
  entryComponents: [components],
  schemas: [
    CUSTOM_ELEMENTS_SCHEMA,
    NO_ERRORS_SCHEMA
  ]

})
 export class AppModule {
  constructor(private resolver: ComponentFactoryResolver) { }
  ngDoBootstrap(appRef: ApplicationRef) {
    components.forEach((componentDef: Type<{}>) => {
      const factory = this.resolver.resolveComponentFactory(componentDef);
      if (document.querySelector(factory.selector)) {
        appRef.bootstrap(factory);
      }
    });
  }
 }
