<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox  height="100%" width="100%" paddingLeft="10" paddingRight="10" paddingBottom="10" paddingTop="10">
    <SwtCanvas width="100%" height="10%">
      <Grid  height="100%" width="100%">
        <GridRow width="100%" height="100%">
          <GridItem width="100%" height="100%">
            <SwtLabel  #templateIdLabel paddingTop="-4" fontWeight="bold"></SwtLabel>
            <SwtLabel id="lblTemplateId" #lblTemplateId paddingTop="-4" fontWeight="bold"></SwtLabel>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <SwtCanvas  id="forecastMonitorAssumptionsCanvas"
                #forecastMonitorAssumptionsCanvas
                width="100%"
                height="80%"></SwtCanvas>
    <SwtCanvas  width="100%" height="9%" >
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true"
                     id="addButton"
                     #addButton
                     width="70"
                     (click)="assumptionClick('add')"></SwtButton>
          <SwtButton #changeButton
                     id="changeButton"
                     width="70"
                     enabled="false"
                     (click)="assumptionClick('change')"></SwtButton>
          <SwtButton #deleteButton
                     id="deleteButton"
                     width="70"
                     enabled="false"
                     (click)="deleteAssumption()"></SwtButton>
          <SwtButton #closeButton
                     id="closeButton"
                     width="70"
                     (click)="closeHandler()"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" top="3">
          <SwtText    #lostConnectionText
                      visible="false"
                      color="red"
                      fontWeight="bold"
                      id="lostConnectionText"
                      paddingTop="1"
                      (click)="connError($event)">
          </SwtText>
          <DataExport  #dataExport id="dataExport"></DataExport>
          <SwtHelpButton id="helpIcon"
                           #helpIcon (click)="doHelp()"></SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>

