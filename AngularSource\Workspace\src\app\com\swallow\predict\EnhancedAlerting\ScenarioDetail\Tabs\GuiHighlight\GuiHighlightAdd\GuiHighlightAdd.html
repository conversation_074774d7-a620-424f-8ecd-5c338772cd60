<SwtModule (creationComplete)="onLoad()"  width="100%" height="100%">
  <VBox width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingBottom="5" paddingRight="5">
    <SwtCanvas width="100%" height="90%">
      <VBox width="100%" height="100%">
    <Grid width="100%" height="20%" paddingTop="10">
      <GridRow>
        <GridItem width="25%">
          <SwtLabel #scenarioIdLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput #scenarioIdtxt width="200" enabled="false"></SwtTextInput>
        </GridItem>
      </GridRow>
      <GridRow>
        <GridItem width="25%">
          <SwtLabel #guiFacilityId></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtComboBox #guiFacilityCombo dataLabel="guiHighlightFacilityList" (change)="changeGuiFacility()"></SwtComboBox>
        </GridItem>
        <GridItem>
          <SwtLabel #selectedGuiFacility fontWeight="normal"></SwtLabel>
        </GridItem>
      </GridRow>
      <GridRow>
        <GridItem width="25%">
          <SwtLabel #reqScenLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtCheckBox #reqScenCheck enabled="false"></SwtCheckBox>
        </GridItem>
      </GridRow>
    </Grid>
    <SwtCanvas #subGuiCanvas width="100%" height="53%"></SwtCanvas>
    <Grid width="100%" height="20%" paddingTop="10">
      <GridRow>
        <GridItem width="15%">
          <SwtLabel #parameterIdLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtText #parameterIdTxt></SwtText>
        </GridItem>
      </GridRow>
      <GridRow>
        <GridItem width="15%">
          <SwtLabel #desLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtText #descTxt></SwtText>
        </GridItem>
      </GridRow>
      <GridRow>
        <GridItem width="15%">
          <SwtLabel #mapFromLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput #mapFromTxt width="200" enabled="false"></SwtTextInput>
        </GridItem>
      </GridRow>
      <GridRow>
        <GridItem width="15%">

        </GridItem>
        <GridItem>
          <SwtButton #updateButton (click)="updateHandle()" enabled="false"></SwtButton>
        </GridItem>
      </GridRow>
    </Grid>
      </VBox>
    </SwtCanvas>
    <SwtCanvas width="100%" height="7%">
      <HBox width="100%" horizontalGap="5">
        <SwtButton #saveButton (click)="saveHandler()"></SwtButton>
        <SwtButton #cancelButton (click)="cancelHandler()"></SwtButton>
        <SwtButton #showXMLButton (click)="showXmlHandler()" enabled="false"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
