<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><s:text name="sweepcancelQ.title.window" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
var countrow = 0;
var selectedList = "";
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
var totalCount = '${totalCount}';
var currentFilter = "${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var currPage = '${requestScope.currentPage}';
var filterValues = new Array();
var currentFilterValues = currentFilter.split("|");
var sortingValues = currentSort.split("|");
var sortedValues = new Array();
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1]; 

var lastRefTime = "${requestScope.lastRefTime}"; 

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var requestURL = new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>";
var idy = requestURL.indexOf('/' + appName + '/');
requestURL = requestURL.substring(0, idy + 1);

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

/**
* This method is used to submit data for server side
* 
* @param Method Name
* @param Entity Id
 * @return	 
*/
function submitForm(methodName, fromEntity) {
  
    var buttonId = null;
	//To get the window tittle from properties 
    var title = '<s:text name="accountmonitor.confirm.title"/>';
	//To get the Alert message form properties
    var cancelConfirmMsg = '<s:text name="sweep.confirm.cancelMsg"/>';
	//To disable the refresh buttion  while click cancel button
    document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	//To set the entity id 
    document.forms[0].fromEntity.value = fromEntity;
	//To set the method name
    document.forms[0].method.value = methodName;
	//To set the  Selected Sort value
    document.forms[0].selectedSort.value = "";
	//To set the  Selected filter value 
    document.forms[0].selectedFilter.value = "";
	//To Check the method name
    if (methodName == "submit") {
        document.forms[0].selectedList.value = getSelectedList();
        ShowErrMsgWindowWithBtn("", cancelConfirmMsg, YES_NO, yesCancel, noCancel);
		
    } else {
        document.forms[0].submit();
    }
   
}
function yesCancel() {
	document.forms[0].submit();
}
function noCancel() {
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshenablebutton").innerHTML;

}
function onFilterandSort2() {
    updateColors2();
}

function onFilter() {
    disableAllButtons();
    updateColors();
}
var x2;

function updateColors2() {
    var rows = x2.dataTable.tBody.rows;
    var l = rows.length;
    var count = 0;
    for (var i = 0; i < l; i++) {
        if (x2.isRowVisible(i)) {
            removeClassName(rows[i], count % 2 ? "odd" : "even");
            addClassName(rows[i], count % 2 ? "even" : "odd");
            count++;
        }
    }
}

function getSelectedList() {
    var table = document.getElementById("sweepDetailList");
    var tbody = table.getElementsByTagName("tbody")[0];
    var rows = tbody.getElementsByTagName("tr");
    var selectedList = "";
    var selectedAmount = "";
    for (i = 0; i < rows.length; i++) {
        if (isRowSelected(rows[i])) {
            selectedList = selectedList + rows[i].cells[10].innerText + ",";
        }
    }
    return selectedList;
}

function clickLink(element) {
    element.href += '&method=next';
    element.href += '&selectedSort=';
    element.href += currentSort;
    element.href += '&selectedFilter=';
    element.href += currentFilter;
    element.href += '&entityId='
    element.href += document.forms[0].elements['sweepQueue.entityId'].value;
    element.href += '&currGrpId='
    element.href += document.forms[0].elements['sweepQueue.currencyCode'].value;
    element.href += '&accountType='
    element.href += document.forms[0].elements['sweepQueue.accountType'].value;
}

function optionClick_server_filter_JSP(index, value, action) {
    if (action == "filter") {
        value = value.trim();
        value = replace(value, '&nbsp;', ' ');
        var filterValue = "";
        if (currentFilter == "all" || currentFilter == "undefined") {
            for (var idx = 0; idx < xl.numColumns; ++idx) {
                if (idx == index) filterValue += value + "|";
                else filterValue += "All" + "|";
            }
        } else {
            var filter = currentFilter.split("|");
            filter[index] = value;
            for (var idx = 0; idx < xl.numColumns; ++idx) {
                filterValue += filter[idx] + "|";
            }
        }
        document.forms[0].selectedSort.value = currentSort;
        document.forms[0].selectedFilter.value = filterValue;
    } else {
        var sortColum = index;
        var sortDesc = value;
        document.forms[0].selectedSort.value = sortColum + "|" + sortDesc;
        document.forms[0].selectedFilter.value = currentFilter;
    }
    document.getElementById('ddscrolltable').innerHTML = '';
    document.forms[0].method.value = 'displayList';
    document.forms[0].currentPage.value = 1;
    document.forms[0].initialPageCount.value = 0;
    document.forms[0].maxPages.value = 0;
    document.forms[0].pageNoValue.value = 1;
    document.forms[0].submit();
}

function onMultiSelectTableRow(rowElement, isSelected) {
	if (selectedTable=='othersDetailList') {
			if (isSelected) {
				unHighLightTableRow(rowElement);
				countrow--;
			} else {
				var rows = document.getElementById('sweepDetailList').getElementsByTagName('tbody')[0].getElementsByTagName('tr'); 
				highLightTableRow(rowElement);
				for ( var M = 0; M < rows.length; M++) {
					if (isRowSelected(rows[M])) {
						unHighLightTableRow(rows[M]);
						if (countrow > 0) {
							countrow--;
						}
					}
				}
				countrow++;
			}
			if (menuEntityCurrGrpAccess == 0 && countrow == 1) {
				   document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
				   document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
				} else  {
				   document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
				   document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
				}
		} else if (selectedTable=='sweepDetailList') {
			if (isSelected) {
				unHighLightTableRow(rowElement);
				countrow--;
			} else {
				var rowsOthers = document.getElementById('othersDetailList').getElementsByTagName('tbody')[0].getElementsByTagName('tr'); 
				highLightTableRow(rowElement);
				for ( var k = 0; k < rowsOthers.length; k++) {
					if (isRowSelected(rowsOthers[k])) {
						unHighLightTableRow(rowsOthers[k]);
						if (countrow > 0) {
							countrow--;
						}
					}
				}
				countrow++;
			}
			if (menuEntityCurrGrpAccess == 0 && countrow == 1) {
				document.getElementById("viewbutton").innerHTML = document
						.getElementById("viewenablebutton").innerHTML;

			} else {
				document.getElementById("viewbutton").innerHTML = document
						.getElementById("viewdisablebutton").innerHTML;
			}
			if (menuEntityCurrGrpAccess == 0 && countrow > 0) {
				document.getElementById("cancelbutton").innerHTML = document
						.getElementById("cancelenablebutton").innerHTML;
			} else {
				document.getElementById("cancelbutton").innerHTML = document
						.getElementById("canceldisablebutton").innerHTML;
			}
		}
		accountAccess();
	}

function accountAccess() {
    var table = document.getElementById("sweepDetailList");
    var tbody = table.getElementsByTagName("tbody")[0];
    var rows = tbody.getElementsByTagName("tr");
    var selectedList = "";
    var flag = true
    var entity = document.forms[0].elements['sweepQueue.entityId'].value
    for (i = 0; i < rows.length; i++) {
        if (isRowSelected(rows[i])) {
        	var entityIdCr = $(rows[i]).find('td.entityIdCr')[0].innerText;
            var accountId = rows[i].cells[5].innerText;
            flag = accountAccessConfirm(accountId.trim(), entityIdCr)
            if (flag == "false") {
                document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
            }
			var entityIdDr = $(rows[i]).find('td.entityIdDr')[0].innerText;
            accountId = rows[i].cells[8].innerText;
            flag = accountAccessConfirm(accountId.trim(), entityIdDr)
            if (flag == "false") {
                document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
            }
        }
    }
}

function accountAccessConfirm(accountId, entity) {
    var oXMLHTTP = new XMLHttpRequest();
    var sURL = requestURL + appName + "/accountAccess.do?method=acctAccessConfirm";
    sURL = sURL + "&accountId=" + accountId;
    sURL = sURL + "&entityId=" + entity;
    sURL = sURL + "&status=Sweeping";
    oXMLHTTP.open("POST", sURL, false);
    oXMLHTTP.send();
    var str = oXMLHTTP.responseText;
    return str;
}

function disableAllButtons() {
    document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
    document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
    countrow = 0;
}
/*This function is used for to display sweepsearch details
 *param methodName
 *
 */
function openSearch(methodName) {
       //variable decleredfor sweepsearch
    var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Sweep Search");
        //variable declered for MenuName
    var menuName = new String('<s:text name="sweepSearch.title.window"/>');
      // variable declered for smart predict
    var smrtPredPos = menuName.search('<s:text name="alert.SmartPredict"/>');
    menuName = menuName.substr(0, smrtPredPos - 3);
    if (menuAccessIdOfChildWindow == 2) {
    	ShowErrMsgWindowWithBtn("", '<s:text name="alert.AccessNotAvl"/>' + menuName + '<s:text name="alert.ContactSysAdm"/>', null);
    } else {
        var param = 'sweepsearch.do?method=' + methodName + '&entityCode=';
        param += document.forms[0].elements['sweepQueue.entityId'].value;
        param += '&actType=' + document.forms[0].elements['sweepQueue.accountType'].value;
        param += '&status=' + document.forms[0].elements['sweepQueue.queueName'].value;
        param += '&currency=' + document.forms[0].elements['sweepQueue.currencyCode'].value;
        param += '&menuAccessId=' + menuAccessIdOfChildWindow;
          /*Start:Code Modified for Mantis 1577 on 19-01-2011:The purpose Alignment width and height */
        openWindow(param, 'SweepSearchWindow', 'width=911,height=488,toolbar=0, status=yes,resizable=yes, scrollbars=yes', 'true');
              /*End:Code Modified for Mantis 1577 on 19-01-2011:The purpose Alignment width and height */
    }
}

function bodyOnLoad() {
    countrow = 0;
    var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
    xl = new XLSheet("sweepDetailList", "table_1", [dateFormat, "String", currencyFormat, "String","String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String"], "1100110110011111000", "false", currentFilterValues, sortedValues);
    x2 = new XLSheet("othersDetailList", "table_2", [dateFormat, "String", "String", "String", "String","String", "String", "String", "String", "String", "String", "String", "String", "String"], "00000000000000");
    xl.onsort = xl.onfilter = onFilter;
    x2.onsort = x2.onfilter = onFilterandSort2;
    highlightMultiTableRows("sweepDetailList");
    highlightMultiTableRows("othersDetailList");
    var cancelcloseElements = new Array(2);
    cancelcloseElements[0] = "closebutton";
    cancelcloseElements[0] = "cancelbutton";
    document.getElementById("entityName").innerText = '${entityName}';
    var entitydropBox = new SwSelectBox(document.forms[0].elements["sweepQueue.entityId"], document.getElementById("entityName"));
    var currencydropBox = new SwSelectBox(document.forms[0].elements["sweepQueue.currencyCode"], document.getElementById("currencyName"));
    document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
    document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
    document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}"; 
   
    document.getElementById("lastRefTime").innerText = lastRefTime; 
   
}
 

function buildViewSweepDisplayURL(methodName) {
    var param = 'sweepsearch.do?method=view&sweepId=';
    param += getSelectedSweepID();
    param += '&entityid=';
    param += getSelectedEntityID();
    return param;
} 


function openSweepQueueDetail() {
    openWindow('sweepdetail.do?method=displayQueue&swpid=' + getSelectedSweepID() + '&entid=' + document.forms[0].entityId.value + '&qname=<s:property value='#request.sweepQueueForm.sweepQueue.queueName' />', 'SweepDtlWindow', 'width=1021,height=635,toolbar=0, status=yes,resizable=yes, scrollbars=yes', 'true');
}

function getSelectedSweepID() {
		var table = document.getElementById("sweepDetailList");
		var tbody = table.getElementsByTagName("tbody")[0];
		var rows = tbody.getElementsByTagName("tr");
		var selectedSweepId = "";

		var tableOthers = document.getElementById("othersDetailList");
		var tbodyOthers = tableOthers.getElementsByTagName("tbody")[0];
		var rowsOthers = tbodyOthers.getElementsByTagName("tr");

		for (var i = 0; i < rows.length; i++) {
			if (isRowSelected(rows[i])) {
				selectedSweepId = rows[i].cells[10].innerText;
			}
		}
		if (selectedSweepId == "")
			for (var j = 0; j < rowsOthers.length; j++) {
				if (isRowSelected(rowsOthers[j])) {
					selectedSweepId = rowsOthers[j].cells[10].innerText;
				}
			}
		return selectedSweepId;
	}
	
	
function getSelectedEntityID() {
	var table = document.getElementById("sweepDetailList");
	var tbody = table.getElementsByTagName("tbody")[0];
	var rows = tbody.getElementsByTagName("tr");
// 	var selectedSweepId = "";
	var entityIdCr = "";
	var tableOthers = document.getElementById("othersDetailList");
	var tbodyOthers = tableOthers.getElementsByTagName("tbody")[0];
	var rowsOthers = tbodyOthers.getElementsByTagName("tr");

	for (var i = 0; i < rows.length; i++) {
		if (isRowSelected(rows[i])) {
			entityIdCr = $(rows[i]).find('td.entityIdCr')[0].innerText;
		}
	}
	if (entityIdCr == "")
		for (var j = 0; j < rowsOthers.length; j++) {
			if (isRowSelected(rowsOthers[j])) {
				entityIdCr = $(rowsOthers[j]).find('td.entityIdCr')[0].innerText;
			}
		}
	return entityIdCr;
}

function validatePageNumber(strObject) {
    var re = /^\d+$/;
    if (strObject && (re.test(strObject.value) && strObject.value != 0)) {
        var maxPage = "${requestScope.maxPage}";
        if (parseInt(strObject.value) > maxPage) {
            strObject.value = maxPage;
        }
        goToResultsPage(strObject.value);
    } else {
        alert("Please enter a valid page number");
        strObject.value = currPage;
    }
}

function goToResultsPage(goToPageNo) {
    document.forms[0].method.value = "next";
    document.forms[0].goToPageNo.value = goToPageNo;
    var entityId = document.forms[0].elements['sweepQueue.entityId'].value;
    var currCode = document.forms[0].elements['sweepQueue.currencyCode'].value;
    document.forms[0].entityId.value = entityId;
    document.forms[0].currGrpId.value = currCode;
    document.forms[0].accountType.value = document.forms[0].elements['sweepQueue.accountType'].value;
    document.forms[0].currentPage.value = "${requestScope.currentPage}";;
    document.forms[0].selectedSort.value = currentSort;
    document.forms[0].selectedFilter.value = currentFilter;
    document.forms[0].maxPages.value = "${requestScope.maxPage}";
    document.forms[0].totalCount.value = "${totalCount}";
    document.forms[0].submit();
}

/*  START:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */
function clickLink(goToPageNo) {
	var baseUrl = '<%=SwtUtil.convertBeanToUrlParams((PageDetails)((ArrayList<PageDetails>)request.getAttribute("pageSummaryList")).get(0), "pageDetails") %>';
    var url='sweepcancelqueue.do?'+baseUrl;		   
    url+= 'method=next&selectedSort='+currentSort+'&selectedFilter='+currentFilter+'&entityId='+document.forms[0].elements['sweepQueue.entityId'].value;
    url+= '&currGrpId='+document.forms[0].elements['sweepQueue.currencyCode'].value+'&accountType='+document.forms[0].elements['sweepQueue.accountType'].value+'&goToPageNo='+goToPageNo;
    submitFormFromURL(url,window);     
    
}
</script>
</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');"
	onunload="call()">

<s:form action="sweepcancelqueue.do">
	<s:hidden name='sweepQueue.queueName' />
	<s:hidden name="method" value="display" />
	<s:hidden name="selectedList" value="" />
	<s:hidden name="fromEntity" value="" />
	<input name="entityId" type="hidden" value="">
	<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
	<input name="selectedSort" type="hidden" value='${selectedSort}'>
	<input name="currentPage" type="hidden" value="">
	<input name="initialPageCount" type="hidden" value="">
	<input name="maxPages" type="hidden" value="">
	<input name="pageNoValue" type="hidden" value="">
	<input name="menuAccessId" type="hidden">
	<input name="goToPageNo" type="hidden" value="">
	<input name="currGrpId" type="hidden" value="">
	<input name="accountType" type="hidden" value="">
	<input name="totalCount" type="hidden" value="">
	<s:set var="CDM" value="#request.session.CDM" />

	<div id="SweepingQueues"
		style="position: absolute; left: 2; top: 2; width: 1236; height: 85px; visibility: visible; border: 2px outset;"
		color="#7E97AF">
	<div id="SweepingQueues"
		style="position: absolute; z-index: 99; left: 8px; top: 5px; width: 600px; height: 85px;">
	<table width="552" border="0" cellspacing="0" cellpadding="0"
		class="content">
		<tr height="24" color="black" border="0">
			<td width="90"><b><s:text name="sweep.entity" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="135px"><s:select cssClass="htmlTextAlpha" tabindex="1" titleKey="tooltip.selectEntityid" id="sweepQueue.entityId" name="sweepQueue.entityId" onchange="submitForm('displayList','E')" cssStyle="width:140px" list="#request.entityList" listKey="value" listValue="label" /></td>
			<td width="20px">&nbsp;</td>
			<td width="280px"><span id="entityName" name="entityName"
				class="spantext"></td>
		</tr>
		<tr height="24">
			<td width="120"><b><s:text name="sweep.currencyGroup" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="135px"><s:select id="sweepQueue.currencyCode" name="sweepQueue.currencyCode" cssClass="htmlTextAlpha" tabindex="2" titleKey="tooltip.selectCurrencyCode" onchange="submitForm('displayList')" cssStyle="width:140px" list="#request.currencyGroupList" listKey="value" listValue="label" /></td>
			<td width="20px">&nbsp;</td>
			<td width="280px"><span id="currencyName" name="currencyName"
				class="spantext" /></td>
		</tr>
		<tr height="24">
			<td width="110"><b><s:text name="sweep.accountType" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="135"><s:select id="sweepQueue.accountType" name="sweepQueue.accountType" cssClass="htmlTextAlpha" tabindex="3" titleKey="tooltip.selectAccountType" onchange="submitForm('displayList')" cssStyle="width: 140px;" list="#request.accountList" listKey="value" listValue="label" /></td>
			<td width="20px">&nbsp;</td>
			<td width="280px"><span id="accountType" name="accountType"
				class="spantext"></td>
		</tr>
	</table>
	</div>
	<div id="pageSummaryList"
		style="position: absolute; left: 730px; top: 0px; width: 220px; height: 25px; border: 2px;">
	<s:if test='true!=#request.hidePagination' >
		<table width="238px" border="0" cellpadding="0" cellspacing="1"
			height="25px">
			<tr height="25px">
				<%
					int countPage = 1;
				%>
				<%
					String currentPageAsString = (String) request
							.getAttribute("currentPage");
				%>
				<%
					String maxPageAsString = (String) request.getAttribute("maxPage");
				%>
				<%
					int currentPage = Integer.parseInt(currentPageAsString);
				%>
				<%
					int maxPage = Integer.parseInt(maxPageAsString);
				%>

				<s:iterator value='#request.pageSummaryList' var='pageSummaryList' >

					<%
						if (countPage <= 12) {
							++countPage;
					%>				
					<td height="34"><b>Page</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input
						class="htmlTextNumeric" id="pageNoText" name="pageNo" size="5"
						height="34" align="top" value="<%=currentPageAsString %>"
						onchange="validatePageNumber(this);"
						onkeydown="if (event.keyCode == 13)validatePageNumber(this);">
					</td>
					<td><s:if test='true==#request.prevEnabled' >
						  <a href="#"  onclick="clickLink(-1);">
							<img alt="Next page" src="images/page_up.png" align="top"
								border="0" height="12" style=" padding-top:6px; height: 11px;" width="18"></img><br />
					      </a> 
					</s:if> <s:if test='true!=#request.prevEnabled' >
						<img alt="Next page" src="images/page_up.png" align="top"
							border="0" height="12" style=" padding-top:6px; height: 11px;" width="18"></img><br />
							
					</s:if> <s:if test='true==#request.prevEnabled' >
					    <a href="#"  onclick="clickLink(-2);">
							<img alt="Previous page" src="images/page_down.png"
								align="bottom" height="12" style=" padding-bottom:6px; height: 11px;" width="18" border="0"></img><br />
					      </a> 
					</s:if> <s:if test='true!=#request.prevEnabled' >
						<img alt="Previous page" style=" padding-bottom:6px; height: 11px;" src="images/page_down.png" align="bottom"
							height="12" width="18" border="0"></img><br />
					</s:if></td>
					<td style="text-align: center;">&nbsp;&nbsp;of&nbsp;&nbsp; <input
						class="textAlpha" style="background: transparent; border: 0;"
						readonly name="maxPageNo" value="<%=maxPageAsString %>" size="5">
					</td>
					<%
						}
					%>
				</s:iterator>
			</tr>
		</table>
	</s:if></div>
	</div>

	<div id="SweepingQueues" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 2px; top: 92px; width: 1236px; height: 325px;">
	<div id="SweepingQueues"
		style="position: absolute; left: 0; top: 1; width: 1234; height: 1px; visibility: visible;">
	<table width="1234" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td width="13%" height="10px" class="topbar"><b>&nbsp;&nbsp;<s:text name="sweep.cancelPanel" /></b></td>
		</tr>
	</table>
	</div>
	<div id="SweepingQueues"
		style="position: absolute; left: 0; top: 18; width: 1216; height: 275px; visibility: visible;">
	<div id="SweepingQueues"
		style="position: absolute; z-index: 99; left: 0px; top: 6px; width: 1219px; height: 10px;">
	<table class="sort-table" id="table_1" bgcolor="#B0AFAF" width="2362"
		border="0" cellspacing="1" cellpadding="0" height="15">
		<thead>
			<tr>
				<td width="80" height="20px" style="border-left-width: 0px;" align="center"
					title='<s:text name="tooltip.sortValueDate"/>'><b><s:text name="sweep.valueDate" /></b></td>
				<td width="67" height="20px" align="center"
					title='<s:text name="tooltip.sortCurrencyCode"/>'><b><s:text name="sweep.currencyCode" /></b></td>
				<td width="150" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepAmount"/>'><b><s:text name="sweep.currentAmt" /></b></td>
				<td width="150" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepAmount"/>'><b><s:text name="sweep.NewAmt" /></b></td>
				<td width="100" height="20px" align="center"
					title='<s:text name = "tooltip.sortEntityId"/>'><b><s:text name="sweepsearch.entityCr" /></b></td>
				<td width="220" height="20px" align="center"
					title='<s:text name="tooltip.sortCrAccID"/>'><b><s:text name="sweep.accountIdCr" /></b></td>
				<td width="180" height="20px" align="center"
					title='<s:text name = "tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
				<td width="100" height="20px" align="center"
					title='<s:text name = "tooltip.sortEntityId"/>'><b><s:text name="sweepsearch.entityDr" /></b></td>	
				<td width="220" height="20px" align="center"
					title='<s:text name="tooltip.sortDrAccID"/>'><b><s:text name="sweep.accountIdDr" /></b></td>
				<td width="180" height="20px" align="center"
					title='<s:text name = "tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
				<td align="center" width="120" height="20px" align="center"
					title='<s:text name="tooltip.sortSweepID"/>'><b><s:text name="sweep.sweepId" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.crdIntMsg"/>'><b><s:text name="sweep.crdIntMsg" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.crdExtMsg"/>'><b><s:text name="sweep.crdExtMsg" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.drIntMsg"/>'><b><s:text name="sweep.drIntMsg" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.drExtMsg"/>'><b><s:text name="sweep.drExtMsg" /></b></td>
				<td width="76" height="20px" align="center"
					title='<s:text name="tooltip.sortSweeptype"/>'><b><s:text name="sweep.sweepType" /></b></td>
				<td width="144" height="20px" align="center"
					title='<s:text name="tooltip.sortSweepUser"/>'><b><s:text name="sweep.sweepUser" /></b></td>
				<td width="95" height="20px" align="center"
					title='<s:text name="tooltip.SweepStatus" />'><b><s:text name="sweep.sweepStatus" /></b></td>
				<td width="230" height="20px" align="center"
					title='<s:text name="tooltip.sortSweepDTU"/>'><b><s:text name="sweep.sweepDateTimeUser" /></b></td>
			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 7px; width: 1234px; height: 298px; overflow: scroll">
	<div id="SweepingQueues"
		style="position: absolute; left: 0px; top: 20px; width: 1216px; height: 10px;">
	<table class="sort-table" id="sweepDetailList" width="2362" border="0"
		cellspacing="1" cellpadding="0" height="260">
		<tbody>
			<%
				int count = 0;
			%>
			<s:iterator value='#request.sweepDetailList' var='sweepDetailList' >
				<%
					if (count % 2 == 0) {
				%><tr class="even">
					<%
						} else {
					%>
				
				<tr class="odd">
					<%
						}
						++count;
					%>
					<s:hidden name="sweepDetailList.entityId" value="%{#sweepDetailList.entityId}" disabled="true"/>
					<s:if test='"Y"==#sweepDetailList.cutOffExceeded' >
						<td width="80" style="color: red" align="center"><s:property value='#sweepDetailList.valueDateAsString' /></td>
						<td width="67" style="color: red"><s:property value='#sweepDetailList.currencyCode' /></td>
						<td align="right" width="150" style="color: red"><s:property value='#sweepDetailList.displaySweepAmount' /></td>
						<td align="right" width="150" style="color: red"><s:property value='#sweepDetailList.newCalulatedAmount' /></td>
						<td width="100" style="color: red"><s:property value='#sweepDetailList.entityIdCr' /></td>
						<td width="220" style="color: red"><s:property value='#sweepDetailList.accountIdCr' /></td>
						<td width="180" style="color: red"><s:property value='#sweepDetailList.accountCr.acctname' /></td>
						<td width="100" style="color: red"><s:property value='#sweepDetailList.entityIdDr' /></td>
						<td width="220" style="color: red"><s:property value='#sweepDetailList.accountIdDr' /></td>
						<td width="180" style="color: red"><s:property value='#sweepDetailList.accountDr.acctname' /></td>
						<td width="120" align="right" style="color: red"><s:property value='#sweepDetailList.id.sweepId' /></td>
						<td width="140" style="color: red"><s:property value='#sweepDetailList.accountCr.acctNewCrInternal' /></td>
						<td width="140" style="color: red"><s:property value='#sweepDetailList.accountCr.acctNewCrExternal' /></td>
						<td width="140" style="color: red"><s:property value='#sweepDetailList.accountDr.acctNewDrInternal' /></td>
						<td width="140" style="color: red"><s:property value='#sweepDetailList.accountDr.acctNewDrExternal' /></td>
						<s:if test='"A"==#sweepDetailList.sweepType' >
							<td width="76" style="color: red"><s:text name="sweepCancel.colValue.Auto" /></td>
						</s:if>
						<s:if test='"M"==#sweepDetailList.sweepType' >
							<td width="76" style="color: red"><s:text name="sweepCancel.colValue.Man" /></td>
						</s:if>
						<td width="144" style="color: red"><s:property value='#sweepDetailList.displayUser' /></td>
						<td width="95px" style="color: red"><s:property value='#sweepDetailList.displayStatus' />&nbsp;</td>
						<td width="220px" align="center" style="color: red"><s:property value='#sweepDetailList.displayDateTimeUser' />&nbsp;</td>
							<td class="entityIdCr" style="display: none"><s:property value='#sweepDetailList.entityIdCr' /></td>	
						<td class="entityIdDr" style="display: none"><s:property value='#sweepDetailList.entityIdDr' /></td>	
					</s:if>

					<s:if test='"Y"!=#sweepDetailList.cutOffExceeded' >
						<td width="80" align="center"><s:property value='#sweepDetailList.valueDateAsString' /></td>
						<td width="67"><s:property value='#sweepDetailList.currencyCode' /></td>
						<td align="right" width="150"><s:property value='#sweepDetailList.displaySweepAmount' /></td>
						<td align="right" width="150" style="color: red"><s:property value='#sweepDetailList.newCalulatedAmount' /></td>
						<td width="100" style="color: red"><s:property value='#sweepDetailList.entityIdCr' /></td>
						<td width="220" style="color: red"><s:property value='#sweepDetailList.accountIdCr' /></td>
						<td width="180" style="color: red"><s:property value='#sweepDetailList.accountCr.acctname' /></td>
						<td width="100" style="color: red"><s:property value='#sweepDetailList.entityIdDr' /></td>
						<td width="220"><s:property value='#sweepDetailList.accountIdDr' /></td>
						<td width="180" style="color: red"><s:property value='#sweepDetailList.accountDr.acctname' /></td>
						<td align="right" width="120"><s:property value='#sweepDetailList.id.sweepId' /></td>
						<td width="140"><s:property value='#sweepDetailList.accountCr.acctNewCrInternal' /></td>
						<td width="140"><s:property value='#sweepDetailList.accountCr.acctNewCrExternal' /></td>
						<td width="140"><s:property value='#sweepDetailList.accountDr.acctNewDrInternal' /></td>
						<td width="140"><s:property value='#sweepDetailList.accountDr.acctNewDrExternal' /></td>
						<s:if test='"A"==#sweepDetailList.sweepType' >
							<td width="76"><s:text name="sweepCancel.colValue.Auto" /></td>
						</s:if>
						<s:if test='"M"==#sweepDetailList.sweepType' >
							<td width="76"><s:text name="sweepCancel.colValue.Man" /></td>
						</s:if>
						<td width="144"><s:property value='#sweepDetailList.displayUser' /></td>
						<td width="95px"><s:property value='#sweepDetailList.displayStatus' />&nbsp;</td>
						<td width="230px" align="center"><s:property value='#sweepDetailList.displayDateTimeUser' />&nbsp;</td>
						<td class="entityIdCr" style="display: none"><s:property value='#sweepDetailList.entityIdCr' /></td>	
						<td class="entityIdDr" style="display: none"><s:property value='#sweepDetailList.entityIdDr' /></td>	
					</s:if>
				</tr>
			</s:iterator>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="17"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>
	</div>
	<div id="SweepingQueues" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 2px; top: 422px; width: 1236px; height: 155px;">
	<div id="SweepingQueues"
		style="position: absolute; left: 0; top: 1; width: 1234; height: 1px; visibility: visible;">
	<table width="1234" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td width="13%" height="10px" class="topbar"><b>&nbsp;&nbsp;<s:text name="sweep.otherPanel" /></b></td>
		</tr>
	</table>
	</div>
	<div id="SweepingQueues"
		style="position: absolute; left: 0; top: 18; width: 1216px; height: 100px; visibility: visible;">
	<div id="SweepingQueues"
		style="position: absolute; z-index: 99; left: 0px; top: 6px; width: 1219px; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="2362px"
		border="0" cellspacing="1" cellpadding="0" height="15px">
		<thead>
			<tr>
				<td width="80" height="20px" align="center" style="border-left-width: 0px;"
					title='<s:text name="tooltip.sortValueDate"/>'><b><s:text name="sweep.valueDate" /></b></td>
				<td width="67" height="20px" align="center"
					title='<s:text name="tooltip.sortCurrencyCode"/>'><b><s:text name="sweep.currencyCode" /></b></td>
				<td width="150" height="20px" align="center"
					title='<s:text name="tooltip.sortSweepAmount"/>'><b><s:text name="sweep.currentAmt" /></b></td>
				<td width="150" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepAmount"/>'><b><s:text name="sweep.NewAmt" /></b></td>
				<td width="100" height="20px" align="center"
					title='<s:text name = "tooltip.sortEntityId"/>'><b><s:text name="sweepsearch.entityCr" /></b></td>
				<td width="220" height="20px" align="center"
					title='<s:text name="tooltip.sortCrAccID"/>'><b><s:text name="sweep.accountIdCr" /></b></td>
				<td width="180" height="20px" align="center"
					title='<s:text name = "tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
				<td width="100" height="20px" align="center"
					title='<s:text name = "tooltip.sortEntityId"/>'><b><s:text name="sweepsearch.entityDr" /></b></td>
				<td width="220" height="20px" align="center"
					title='<s:text name="tooltip.sortDrAccID"/>'><b><s:text name="sweep.accountIdDr" /></b></td>
				<td width="180" height="20px" align="center"
					title='<s:text name = "tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
				<td align="center" width="120" height="20px" align="center"
					title='<s:text name="tooltip.sortSweepID"/>'><b><s:text name="sweep.sweepId" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.crdIntMsg"/>'><b><s:text name="sweep.crdIntMsg" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.crdExtMsg"/>'><b><s:text name="sweep.crdExtMsg" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.drIntMsg"/>'><b><s:text name="sweep.drIntMsg" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.drExtMsg"/>'><b><s:text name="sweep.drExtMsg" /></b></td>
				<td width="76" height="20px" align="center"
					title='<s:text name="tooltip.sortSweeptype"/>'><b><s:text name="sweep.sweepType" /></b></td>
				<td width="144" height="20px" align="center"
					title='<s:text name="tooltip.sortSweepUser"/>'><b><s:text name="sweep.sweepUser" /></b></td>
				<td width="95px" height="20px" align="center"
					title='<s:text name="tooltip.SweepStatus"/>'><b><s:text name="sweep.sweepStatus" /></b></td>
				<td width="230px" height="20px" align="center"
					title='<s:text name="tooltip.sortSweepDTU"/>'><b><s:text name="sweep.sweepDateTimeUser" /></b></td>
			</tr>
		</thead>
	</table>
	</div>

	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 7px; width: 1234px; height: 128px; overflow: scroll">
	<div id="SweepingQueues"
		style="position: absolute; left: 0px; top: 20px; width: 1216px; height: 10px;">
	<table class="sort-table" id="othersDetailList" width="2362px"
		border="0" cellspacing="1" cellpadding="0" height="90">
		<tbody>
			<%
				count = 0;
			%>
			<s:iterator value='#request.othersDetailList' var='othersDetailList' >
				<%
					if (count % 2 == 0) {
				%><tr class="even">
					<%
						} else {
					%>
				
				<tr class="odd">
					<%
						}
						++count;
					%>

					<s:if test='"Y"==#othersDetailList.cutOffExceeded' >
						<td width="80" style="color: red"><s:property value='#othersDetailList.valueDateAsString' /></td>
						<td width="67" style="color: red"><s:property value='#othersDetailList.currencyCode' /></td>
						<td align="right" width="150" style="color: red"><s:property value='#othersDetailList.displaySweepAmount' /></td>
						<td align="right" width="150" style="color: red"><s:property value='#othersDetailList.newCalulatedAmount' /></td>
						<td width="100" style="color: red"><s:property value='#othersDetailList.entityIdCr' /></td>
						<td width="220" style="color: red"><s:property value='#othersDetailList.accountIdCr' /></td>
						<td width="180" style="color: red"><s:property value='#othersDetailList.accountCr.acctname' /></td>
						<td width="100" style="color: red"><s:property value='#othersDetailList.entityIdDr' /></td>
						<td width="220" style="color: red"><s:property value='#othersDetailList.accountIdDr' /></td>
						<td width="180" style="color: red"><s:property value='#othersDetailList.accountDr.acctname' /></td>
						<td align="right" width="120" style="color: red"><s:property value='#othersDetailList.id.sweepId' /></td>
						<td width="140" style="color: red"><s:property value='#othersDetailList.accountCr.acctNewCrInternal' /></td>
						<td width="140" style="color: red"><s:property value='#othersDetailList.accountCr.acctNewCrExternal' /></td>
						<td width="140" style="color: red"><s:property value='#othersDetailList.accountDr.acctNewDrInternal' /></td>
						<td width="140" style="color: red"><s:property value='#othersDetailList.accountDr.acctNewDrExternal' /></td>
						<s:if test='"A"==#othersDetailList.sweepType' >
							<td width="76" style="color: red"><s:text name="sweepCancel.colValue.Auto" /></td>
						</s:if>
						<s:if test='"M"==#othersDetailList.sweepType' >
							<td width="76" style="color: red"><s:text name="sweepCancel.colValue.Man" /></td>
						</s:if>
						<td width="144" style="color: red"><s:property value='#othersDetailList.displayUser' /></td>
						<td width="95px" style="color: red"><s:property value='#othersDetailList.displayStatus' />&nbsp;</td>
						<td width="230px" style="color: red"><s:property value='#othersDetailList.displayDateTimeUser' />&nbsp;</td>
						<td class="entityIdCr" style="display: none"><s:property value='#othersDetailList.entityIdCr' /></td>	
						<td class="entityIdDr" style="display: none"><s:property value='#othersDetailList.entityIdDr' /></td>	
					</s:if>
					<s:if test='"Y"!=#othersDetailList.cutOffExceeded' >
						<td width="80"><s:property value='#othersDetailList.valueDateAsString' />&nbsp;</td>
						<td width="67" style="color: red"><s:property value='#othersDetailList.currencyCode' /></td>
						<td align="right" width="150"><s:property value='#othersDetailList.displaySweepAmount' /></td>
						<td align="right" width="150"><s:property value='#othersDetailList.newCalulatedAmount' /></td>
						<td width="100" style="color: red"><s:property value='#othersDetailList.entityIdCr' /></td>
						<td width="220"><s:property value='#othersDetailList.accountIdCr' /></td>
						<td width="180" style="color: red"><s:property value='#othersDetailList.accountCr.acctname' /></td>
						<td width="100" style="color: red"><s:property value='#othersDetailList.entityIdDr' /></td>
						<td width="220"><s:property value='#othersDetailList.accountIdDr' /></td>
						<td width="180" style="color: red"><s:property value='#othersDetailList.accountDr.acctname' /></td>
						<td align="right" width="120" style="color: red"><s:property value='#othersDetailList.id.sweepId' /></td>
						<td width="140"><s:property value='#othersDetailList.accountCr.acctNewCrInternal' /></td>
						<td width="140"><s:property value='#othersDetailList.accountCr.acctNewCrExternal' /></td>
						<td width="140"><s:property value='#othersDetailList.accountDr.acctNewDrInternal' /></td>
						<td width="140"><s:property value='#othersDetailList.accountDr.acctNewDrExternal' /></td>
						<s:if test='"A"==#othersDetailList.sweepType' >
							<td width="76"><s:text name="sweepCancel.colValue.Auto" /></td>
						</s:if>
						<s:if test='"M"==#othersDetailList.sweepType' >
							<td width="76"><s:text name="sweepCancel.colValue.Auto" /></td>
						</s:if>
						<td width="144"><s:property value='#othersDetailList.displayUser' />&nbsp;</td>
						<td width="95px"><s:property value='#othersDetailList.displayStatus' />&nbsp;</td>
						<td width="230px"><s:property value='#othersDetailList.displayDateTimeUser' />&nbsp;</td>
						<td class="entityIdCr" style="display: none"><s:property value='#othersDetailList.entityIdCr' /></td>	
						<td class="entityIdDr" style="display: none"><s:property value='#othersDetailList.entityIdDr' /></td>							
					</s:if>
				</tr>
			</s:iterator>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="17"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>
	</div>
 
	<div id="SweepingQueues"
		style="position: absolute; left: 1140; top: 590; width: 70; height: 15px; visibility: visible;">
	<table width="60" cellspacing="0" cellpadding="0" height="20"
		border="0">
		<tr>
			<td align="Right"><a tabindex="9" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Sweep Cancel Queue'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<s:text name="tooltip.helpScreen"/>'></a></td>

			<td align="right" valign="middle" id="Print"><a tabindex="10"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif" name="Print" border="0"
				title='<s:text name="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 2; top: 582; width: 1236px; height: 39px; visibility: visible;">
	<div id="SweepingQueues"
		style="position: absolute; left: 6; top: 4; width: 280px; height: 15px; visibility: visible;">
	<table width="350" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="cancelbutton"></td>
			<td id="viewbutton"></td>

			<td id="refreshbutton"><a tabindex="6"
				title='<s:text name="tooltip.refreshScreen"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onkeydown="submitEnter(this,event)"
				onclick="submitForm('displayList')"><s:text name="sweep.refresh" /></a></td>
			<td id="searchbutton"><a tabindex="7"
				title='<s:text name="tooltip.searchSweep"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onkeydown="submitEnter(this,event)"
				onclick="openSearch('displaysearch')"><s:text name="sweep.search" /></a></td>
			<td id="closebutton"><a onMouseOut="collapsebutton(this)"
				tabindex="8" title='<s:text name="tooltip.close"/>'
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onkeydown="submitEnter(this,event)"
				onclick="confirmClose('P');"><s:text name="sweep.close" /></a></td>
		</tr>
	</table>
	</div>

	<table height="33">
		<tr>
			<td id="lastRefTimeLable" width="1004px" align="right"><s:text name="label.lastRefTime" /></td>
			<td id="lastRefTime"><input class="textAlpha"
				style="background: transparent; border: 1;" tabindex="-1" readonly
				name="maxPageNo" value="" size="14"></td>
		</tr>
	</table>
	
	<div
		style="position: absolute; left: 6; top: 4; width: 683px; height: 15px; visibility: hidden;">
	<table width="350" border="0" cellspacing="0" cellpadding="0"
		height="20" style="visibility: hidden">
		<tr>
			<td id="cancelenablebutton"><a tabindex="4"
				title='<s:text name="tooltip.canceelSelSweep"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onClick="submitForm('submit')"
				onkeydown="submitEnter(this,event)"><s:text name="sweep.cancel" /></a></td>
			<td id="canceldisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="sweep.cancel" /></a></td>
			<td id="viewenablebutton">
			<a tabindex="5" title='<s:text name="tooltip.viewSweepDisp"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onkeydown="submitEnter(this,event)"
				onClick="javascript:openWindow(buildViewSweepDisplayURL('${methodName}'),'sweepsearchlistWindow','left=50,top=190,width=796,height=745,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.view" /></a>
			</td>
			<td id="viewdisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="button.view" /></a></td>
			<td id="refreshdisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="button.Refresh" /></a></td>
		
			<td id="refreshenablebutton">	
				<a tabindex="6"
					title='<s:text name="tooltip.refreshScreen"/>'
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
					onMouseUp="highlightbutton(this)" onkeydown="submitEnter(this,event)"
					onclick="submitForm('displayList')"><s:text name="sweep.refresh" /></a>
			</td>	
			
		</tr>
	</table>
	</div>
	</div>
	
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</s:form>
</body>
</html>