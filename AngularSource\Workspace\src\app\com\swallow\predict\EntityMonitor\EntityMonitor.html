<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" verticalGap="0">
    <HBox height="11" width="100%">
      <HBox width="99%"></HBox>
      <HBox horizontalAlign="right" width="1%">
        <SwtButton #imgShowHideControlBar id="imgShowHideControlBar"(click)="showHideControlBar($event)" styleName="minusIcon"></SwtButton>
      </HBox>
    </HBox>
        <SwtCanvas minWidth="1000" #swtControlBar height="60" width="99%" marginTop="-5">

          <Grid width="50%" height="100%" paddingLeft="10">
            <GridRow height="50%">
              <GridItem width="110">
                <SwtLabel #ccyLabel textDictionaryId="label.entityMonitor.currency.group" ></SwtLabel>
              </GridItem>
              <GridItem>
                <SwtComboBox id="currGrpCombo" #currGrpCombo  width="130" (change)="changeCombo($event)" dataLabel="currencygroup"></SwtComboBox>
              </GridItem>
              <GridItem paddingLeft="3">
                <SwtLabel #selectedGroup fontWeight="normal"></SwtLabel>
              </GridItem>
            </GridRow>
            <GridRow height="50%" paddingTop="2">
              <GridItem width="110">
                <SwtLabel #dateLabel textDictionaryId="label.entityMonitor.date"  ></SwtLabel>
              </GridItem>
              <GridItem>
                <SwtDateField #startDate (change)="validateDate()" width="70"></SwtDateField>
              </GridItem>
              <GridItem paddingLeft="35">
                <SwtCheckBox #chkEntityOffset  selected="true" (change)="entityOffsetTime()"></SwtCheckBox>
              </GridItem>

            </GridRow>
          </Grid>
          <HBox width="50%" horizontalAlign="right">
            <fieldset>
              <legend>Breakdown</legend>
              <SwtRadioButtonGroup #breakdown id="breakdown" align="horizontal">
                <SwtRadioItem value="A"
                              groupName="breakdown"
                              selected="true"
                              id="accountRadio" #accountRadio></SwtRadioItem>
                <SwtRadioItem value="M"
                              groupName="breakdown"
                              id="movementRadio" #movementRadio></SwtRadioItem>
                <SwtRadioItem  value="B"
                               groupName="breakdown"
                               id="bookRadio" #bookRadio></SwtRadioItem>
                <SwtRadioItem  value="G"
                               groupName="breakdown"
                               id="groupRadio" #groupRadio></SwtRadioItem>
                <SwtRadioItem value="MG"
                              groupName="breakdown"
                              id="metagroupRadio" #metagroupRadio></SwtRadioItem>
              </SwtRadioButtonGroup>
            </fieldset>
          </HBox>
        </SwtCanvas>
    <VBox #vboxCanvas width="100%" height="100%" verticalGap="0" minWidth="1000">
    <SwtCanvas #cvGridContainer styleName="canvasWithGreyBorder" width="100%" height="100%" border="false"></SwtCanvas>
    <SwtCanvas #cvTotalsContainer styleName="canvasWithGreyBorder" width="100%" height="40" border="false"></SwtCanvas>
    </VBox>
    <HBox height="11" width="100%">
      <HBox width="99%"></HBox>
      <HBox horizontalAlign="right" width="1%">
        <SwtButton #imgShowHideButtonBar id="imgShowHideButtonBar" (click)="showHideButtonBar($event)" styleName="minusIcon"></SwtButton>
      </HBox>
    </HBox>
    <SwtCanvas #swtButtonBar marginTop="-5" marginBottom="0" minWidth="1000" width="99%" height="33">
      <HBox width="100%">
      <HBox width="60%">
          <SwtButton buttonMode="true"
                     id="refreshButton" #refreshButton
                     (click)="updateData('yes')"> </SwtButton>
        <SwtButton (click)="optionClick()"
                   buttonMode="true"
                   id="optionsButton" #optionsButton></SwtButton>
        <SwtButton buttonMode="true"
                   id="closeButton" #closeButton
                   (click)="closeHandler()"> </SwtButton>
      </HBox>
      <HBox horizontalAlign="right">
        <SwtLabel visible="false" color="red"  #dataBuildingText></SwtLabel>
        <SwtLabel visible="false" color="red" #lostConnectionText></SwtLabel>
        <SwtLabel #lastRefText fontWeight="normal"></SwtLabel> <SwtLabel #lastRefTime fontWeight="normal"></SwtLabel>
        <div>
          <DataExport  #dataExport id="dataExport"></DataExport>
        </div>
        <SwtHelpButton id="helpIcon"
                       #helpIcon (click)="doHelp()">
        </SwtHelpButton>
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
      </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
