<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" minWidth="1100">
      <HBox width="100%" height="100%" paddingLeft="5" paddingRight="5">
        <VBox width="100%" height="100%" verticalGap="0">
          <HBox width="100%" height="25">
          <HBox width="100%" height="25">
            <SwtLabel id="entityLabel" #entityLabel width="100">
            </SwtLabel>
            <SwtComboBox id="entityCombo" #entityCombo dataLabel="entityList" width="135" (change)="onEntityChange()">
            </SwtComboBox>
            <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal" paddingLeft="10">
            </SwtLabel>
          </HBox>
          <HBox #pageBox width="100%" horizontalAlign="right">
            <SwtCommonGridPagination  #numstepper></SwtCommonGridPagination>
          </HBox>
        </HBox>
          <HBox width="100%" height="25">
            <SwtLabel id="balanceTypeLabel" #balanceTypeLabel width="100">
            </SwtLabel>
            <SwtComboBox id="balanceTypeCombo" #balanceTypeCombo dataLabel="balanceTypeList" width="135" (change)="onBalanceTypeChange()">
            </SwtComboBox>
          </HBox>
          <HBox width="100%" height="25">
            <SwtLabel id="currencyLabel" #currencyLabel width="100">
            </SwtLabel>
            <SwtComboBox id="currencyCombo" #currencyCombo dataLabel="currencyList" width="70" (change)="onCurrencyChange()">
            </SwtComboBox>
            <SwtLabel id="selectedCurrency" #selectedCurrency fontWeight="normal" paddingLeft="75">
            </SwtLabel>
          </HBox>
          <HBox width="100%" height="25">
            <SwtLabel id="dateLabel" #dateLabel width="100">
            </SwtLabel>
            <SwtDateField id="forDateField" #forDateField (change)="validateDateField(forDateField)"
             width="70"></SwtDateField>
          </HBox>
        </VBox>
      </HBox>
    </SwtCanvas>

    <SwtCanvas id="dataGridContainer" #dataGridContainer paddingBottom="5" styleName="canvasWithGreyBorder"
      marginTop="10" border="false" width="100%" height="100%" minHeight="100" minWidth="1100"></SwtCanvas>

    <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5" minWidth="1100">
      <HBox width="100%">
        <HBox paddingLeft="5" width="50%">
          <SwtButton #viewButton width="70" id="viewButton" enabled="false" (click)="doBuildChangeBalMaintURL('view')"></SwtButton>
          <SwtButton #changeButton width="70" id="changeButton" enabled="false" (click)="doBuildChangeBalMaintURL('change')">
          </SwtButton>
          <SwtButton #logButton width="70" id="logButton" enabled="false" (click)="doBuildViewBalLogURL()"></SwtButton>
          <SwtButton #reasonButton width="70" id="reasonButton" enabled="false" (click)="doBuildEditReason()"></SwtButton>
          <SwtButton #closeButton width="70" (click)="closeHandler()" id="closeButton"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" width="50%" >
          <SwtLabel visible="false" color="red" #dataBuildingText></SwtLabel>
          <SwtLabel visible="false" color="red" #lostConnectionText></SwtLabel>
          <DataExportMultiPage #exportContainer id="exportContainer"></DataExportMultiPage>
          <SwtHelpButton id="helpIcon" [buttonMode]="true" enabled="true" helpFile="spread-profile" (click)="doHelp()">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>