import { Column, Formatter } from "angular-slickgrid/public_api";
import { SwtUtil } from "swt-tool-box";

export const SwtStatusItemRenderer: Formatter = (row: number, cell: number, value: any, columnDef: any, dataContext: any) => {

    let datagrid = columnDef.params.grid;
    let dataRow = dataContext['slickgrid_rowcontent'];
    let dataIndex = dataRow['id'];
    let columnName = columnDef.field;

    let backgroundColor = columnDef.params.rowColorFunction(datagrid, dataIndex, 'transparent');
    let enabledFlag = columnDef.params.enableDisableCells(dataRow, columnName);
    let showHideCells = columnDef.params.showHideCells(dataRow, columnName);

    let type = columnDef['columnType'] ? String(columnDef['columnType']) : null;
    let imageSource = null;
    let style = columnDef.properties ? columnDef.properties.style : '';
    let buttonMode: boolean = columnDef.properties ? columnDef.properties._buttonMode : false;
    let toolTipFlag: boolean = columnDef.properties ? columnDef.properties._toolTipFlag : false;
    let toolTip = '';

    let useHandCursor = false;

    if (showHideCells) {
        if (columnName == 'enabled' && dataRow.enabled.content == "DISABLED") {
            imageSource = columnDef.properties ? columnDef.properties.imageDisabled : null;
        } else if (columnName == 'enabled' && dataRow.enabled.content == "ENABLED") {
            imageSource = columnDef.properties ? columnDef.properties.imageEnabled : null;
        }
        if (buttonMode) {
            useHandCursor = true;
        }
    }
    if (imageSource != null) {
        return `<img src="${imageSource}"   style="margin-top: 3px; ${style} ${useHandCursor ? 'cursor: pointer;' : ''}" title="${toolTip}">
        </img>`;
    }
    return ``;

};
