import {Component, ElementRef, ViewChild, OnInit} from '@angular/core';
import {
  H<PERSON><PERSON>omms,
  JSONReader,
  SwtAlert,
  SwtUtil,
  SwtTextInput,
  CommonService,
  SwtModule,
  SwtComboBox, SwtCanvas, SwtCommonGrid, SwtLabel
} from 'swt-tool-box';

@Component({
  selector: 'PCSpreadingTab',
  templateUrl: './SpreadingTab.html',
  styleUrls: ['./SpreadingTab.css']
})
export class SpreadingTab extends SwtModule implements OnInit{

  /*********SwtCombo***************/
  @ViewChild('spreadProfileCombo') spreadProfileCombo: SwtComboBox;
  @ViewChild('targetCalculationCombo') targetCalculationCombo: SwtComboBox;
  @ViewChild('targetCalculationLabel') targetCalculationLabel: SwtLabel;
  @ViewChild('spreadProfileLabel') spreadProfileLabel: SwtLabel;

  /*******Canvas*******/
  @ViewChild('spreadCanvas') spreadCanvas: SwtCanvas;
/******TextInput*******/
@ViewChild('spreadNameTextInput') spreadNameTextInput:  SwtTextInput;
  /**
   * Data Objects
   **/
  public jsonReader= new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public parentDocument: any;
  public screenName:string = null;
  public  title:string= null;
  public selectedRow: string;
  public spreadGrid: SwtCommonGrid;
  private swtAlert: SwtAlert;
  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
ngOnInit(): void {
  this.spreadProfileLabel.text = SwtUtil.getPredictMessage('spreadingTab.label.spreadProfile', null);
  this.spreadProfileCombo.toolTip = SwtUtil.getPredictMessage('spreadingTab.tooltip.spreadProfile', null);
  this.targetCalculationLabel.text  = SwtUtil.getPredictMessage('spreadingTab.label.targetCalculation', null);
  this.targetCalculationCombo.toolTip = SwtUtil.getPredictMessage('spreadingTab.tooltip.targetCalculation', null);
}

  onLoad() {
    try{
      this.screenName = this.parentDocument.screenName;
      // this.spreadGrid = <SwtCommonGrid>this.spreadCanvas.addChild(SwtCommonGrid);
      if (this.screenName == 'view') {
        this.enableDisbaleComponent(false);
      } else  {
        this.enableDisbaleComponent(true);
      }
      this.spreadGrid.parentTabId = "spreading";

    } catch(error){

    }
  }

  enableDisbaleComponent(value: boolean): void {
    this.spreadProfileCombo.enabled = value;
    this.targetCalculationCombo.enabled = value;

  }
  changeCombo(): void {
    let spreadList = [];
    try {

      this.spreadNameTextInput.text = this.spreadProfileCombo.selectedItem.value;
      if(this.spreadProfileCombo.selectedItem.content == "") {
        this.spreadNameTextInput.text = ""
      }
      /**********Filter the spreading process point table  by spread id**********/
      for(let i = 0; i < this.parentDocument.spreadRows.size; i++) {
        if(this.parentDocument.spreadRows.row[i].spreadId.content == this.spreadProfileCombo.selectedLabel) {
          spreadList.push(this.parentDocument.spreadRows.row[i]);

        }
      }


      this.spreadGrid.gridData = {row: spreadList, size: spreadList.length};
      this.spreadGrid.refresh();

    } catch (e) {
      console.log('changeCombo', e)
    }

  }

}
