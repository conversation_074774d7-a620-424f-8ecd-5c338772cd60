.field-selector {
    margin-bottom: 20px;
}
.input-group {
    margin-bottom: 15px;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}
button:hover {
    background-color: #2980b9;
}
.modal {
    display: none;
    position: fixed;
    z-index: 1000 !important;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4) !important;
}
.modal-content {
    background-color: rgb(214, 227, 254);
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 1200px;
    max-height: 80vh; /* Set maximum height */
    overflow-y: auto; /* Enable vertical scrolling if content exceeds max height */
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.modal-content2 {
    background-color: rgb(214, 227, 254);
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 1200px;
    max-height: 100vh; /* Set maximum height */
    overflow-y: hidden; /* Enable vertical scrolling if content exceeds max height */
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}


.close {
    color: #000;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 10px; /* Adjust the top position as needed */
    right: 10px; /* Adjust the right position as needed */
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}
.keyword {
    display: inline-block;
    background-color: #e1f5fe;
    border: 1px solid #81d4fa;
    border-radius: 4px;
    padding: 2px 5px;
    margin: 0 2px;
    font-size: 0.9em;
    cursor: pointer;
}
.keyword:hover {
    background-color: #b3e5fc;
}
.keyword-remove {
    color: #e74c3c;
    margin-left: 5px;
    font-weight: bold;
}

#subjectLineContainer:focus{
    border: 1px solid #7F9DB9;
    outline-style: solid;
    outline-width: 1px;
    outline-color: #49B9FF;

}


.input-content{
    border: 1px solid #7f9db9;
    width: 300px;
    font-size: 11px;
    height: 23px;
    line-height: 23px;
    cursor: text;
    color: #000;
}
.input-group-2 {
    display: flex;
    align-items: center; /* Aligns label and input vertically */
    margin-bottom: 10px; /* Optional spacing between input groups */
}

.input-group-2 label {
    width: 200px;
    margin-right: 10px; /* Space between the label and input */
    font-size: 11px;
    color: black;
    height: 23px;
    line-height: 22px;
    font-family: verdana, helvetica;
    vertical-align: bottom;
    pointer-events: auto !important;
}

.input-group-2 .input-content {
    flex-grow: 1; /* Allows the input to take up the remaining space */
}