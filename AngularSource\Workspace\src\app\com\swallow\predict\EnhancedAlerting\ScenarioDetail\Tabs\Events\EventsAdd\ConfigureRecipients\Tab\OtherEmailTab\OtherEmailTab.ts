import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { SwtModule, CommonService, SwtCanvas, SwtLabel, SwtCheckBox, SwtCommonGrid, SwtUtil } from 'swt-tool-box';
import { Router } from '@angular/router';
declare var instanceElement: any;
@Component({
  selector: 'app-other-email-tab',
  templateUrl: './OtherEmailTab.html',
  styleUrls: ['./OtherEmailTab.css']
})
export class OtherEmailTab extends SwtModule implements OnInit{
  @ViewChild('otherEmailCanvas') otherEmailCanvas: SwtCanvas;
  @ViewChild('selectAllLbl') selectAllLbl: SwtLabel;
  @ViewChild('selectAllCheck') selectAllCheck: SwtCheckBox;
  
  public otherEmailGrid: SwtCommonGrid;
  public parentDocument: any;

  constructor(private commonService: CommonService, private element: ElementRef, private router: Router) {
    super(element, commonService);
   // this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.otherEmailGrid = <SwtCommonGrid>this.otherEmailCanvas.addChild(SwtCommonGrid);
    this.otherEmailGrid.editable = true;
    this.selectAllLbl.text = SwtUtil.getPredictMessage("scenario.role.selectAll.label", null);
    
  }

  onLoad(){

  }


  selectAll(){

  }

}
