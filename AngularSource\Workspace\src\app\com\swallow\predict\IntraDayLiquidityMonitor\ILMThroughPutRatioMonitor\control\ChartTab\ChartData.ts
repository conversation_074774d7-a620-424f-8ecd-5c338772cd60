export class ChartData {


    private _id: string = "";
    private _timeData: string = "";
    private _percentData: string = "";
    private _threshold1Time: string = "";
    private _threshold2Time: string = "";
    private _threshold1Color: string = "";
    private _threshold2Color: string = "";
    private _startTime: string = "";

    private _endTime: string = "";


    private _threshold1Percentage: number;
    private _threshold2Percentage: number;
    private _threshold1State: number;
    private _threshold2State: number;

    public get startTime(): string {
        return this._startTime;
    }
    public set startTime(value: string) {
        this._startTime = value;
    }
    public get endTime(): string {
        return this._endTime;
    }
    public set endTime(value: string) {
        this._endTime = value;
    }
    public get threshold1Color(): string {
        return this._threshold1Color;
    }
    public set threshold1Color(value: string) {
        this._threshold1Color = value;
    }
    public get threshold2Color(): string {
        return this._threshold2Color;
    }
    public set threshold2Color(value: string) {
        this._threshold2Color = value;
    }

    public get id(): string {
        return this._id;
    }
    public set id(value: string) {
        this._id = value;
    }
    public get timeData(): string {
        return this._timeData;
    }
    public set timeData(value: string) {
        this._timeData = value;
    }
    public get percentData(): string {
        return this._percentData;
    }
    public set percentData(value: string) {
        this._percentData = value;
    }
    public get threshold1Time(): string {
        return this._threshold1Time;
    }
    public set threshold1Time(value: string) {
        this._threshold1Time = value;
    }

    public get threshold2Time(): string {
        return this._threshold2Time;
    }
    public set threshold2Time(value: string) {
        this._threshold2Time = value;
    }
    public get threshold1Percentage(): number {
        return this._threshold1Percentage;
    }
    public set threshold1Percentage(value: number) {
        this._threshold1Percentage = value;
    }
    public get threshold2Percentage(): number {
        return this._threshold2Percentage;
    }
    public set threshold2Percentage(value: number) {
        this._threshold2Percentage = value;
    }
    public get threshold1State(): number {
        return this._threshold1State;
    }
    public set threshold1State(value: number) {
        this._threshold1State = value;
    }
    public get threshold2State(): number {
        return this._threshold2State;
    }
    public set threshold2State(value: number) {
        this._threshold2State = value;
    }




}