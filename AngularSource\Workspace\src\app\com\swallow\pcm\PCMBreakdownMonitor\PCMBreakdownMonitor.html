
<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas minWidth="1200" width="100%" height="230">
      <HBox width="100%" height="100%">
        <HBox width="50%">
          <VBox width="100%" height="100%" verticalGap="0">
            <HBox  width="100%">
              <SwtLabel id="currencyLabel"
                        #currencyLabel width="100">
              </SwtLabel>
              <SwtComboBox id="ccyCombo" #ccyCombo
                           (change)="changeCombo()" (click)="previouCcy()"
                           dataLabel="currencyList"
                           width="200">
              </SwtComboBox>
              <spacer width="27"></spacer>
              <SwtLabel id="selectedCcy" #selectedCcy>
              </SwtLabel>
            </HBox>
            <HBox width="100%">
              <SwtLabel id="entityLabel"
                        #entityLabel width="100">
              </SwtLabel>
              <SwtComboBox id="entityCombo" #entityCombo
                           (change)="changeComboEntity(entityCombo,entityMoreItemsButton,selectedEntity)"
                           dataLabel="entityList"
                           width="200">
              </SwtComboBox>
              <SwtButton   #entityMoreItemsButton
                           id="entityMoreItemsButton"
                           label="..."
                           buttonMode="false"
                           enabled="false"
                           width="25"
                           (click)="multipleListSelect(entityCombo.id, selectedEntity)">
              </SwtButton>
              <SwtLabel id="selectedEntity" #selectedEntity
                        textAlign="left" marginRight="2" >
              </SwtLabel>
            </HBox>
            <HBox  width="100%">
              <SwtLabel #acagLabel
                        id="acagLabel" width="100">
              </SwtLabel>
              <SwtComboBox id="acctGrpCombo" #acctGrpCombo
                           (change)="changeCombo()" (click)="previouAcctGrp()"
                           dataLabel="AcctGrpList"
                           width="200">
              </SwtComboBox>
              <spacer width="27"></spacer>
              <SwtLabel id="selectedAcctGrp" #selectedAcctGrp  width="200" >
              </SwtLabel>
            </HBox>
            <HBox width="100%">
              <SwtLabel id="accountLabel"
                        #accountLabel width="100">
              </SwtLabel>
              <SwtComboBox id="accountCombo" #accountCombo
                           (change)="changeCombo()"
                           dataLabel="AcctList"
                           width="200">
              </SwtComboBox>
              <spacer width="27"></spacer>
              <SwtLabel  id="selectedAccount" #selectedAccount width="200"  >
              </SwtLabel>
            </HBox>
            <HBox  width="100%" >
              <HBox>
                <SwtLabel #statusLabel
                          id="statusLabel" width="100">
                </SwtLabel>
                <SwtComboBox  id="statusCombo" #statusCombo
                              (change)="changeCombo()" (click)="previouStatus()"
                              dataLabel="statusList"
                              width="200">
                </SwtComboBox>
              </HBox>
              <HBox #blokedHbox visible="false">
                <SwtLabel #blokedLabel
                          id="blokedLabel" width="70">
                </SwtLabel>
                <SwtComboBox  id="blokedCombo" #blokedCombo
                              (change)="changeCombo()"
                              dataLabel="blockedList"
                              width="190">
                </SwtComboBox>
              </HBox>
            </HBox>
            <HBox width="100%">
              <SwtLabel #dateLabel id="dateLabel" width="100">
              </SwtLabel>
              <HBox width="200">
              <SwtDateField id="startDate" #startDate
                            width="70"
                             (change)="validateDate(startDate)"
                            [editable]="true"
                            restrict="0-9/">
              </SwtDateField>
              </HBox>
              <HBox #inputSinceHbox visible="false">
                <SwtLabel #inputSinceLabel width="75" marginLeft="5"></SwtLabel>
                <SwtDateField id="inputSince" #inputSince
                              width="70"
                              (change)="validateDate(inputSince)"
                              [editable]="true"
                              restrict="0-9/">
                </SwtDateField>
              </HBox>

            </HBox>
          </VBox>
        </HBox>
        <HBox width="20%">
          <VBox width="100%" height="100%" verticalGap="3">

            <HBox width="100%"  height="26">
              <SwtLabel #applyCurrencyLabel width="170" textAlign="right"></SwtLabel>
              <SwtCheckBox paddingLeft="15" #applyCurrencyCheck (change)="updateData('keepSelected')"></SwtCheckBox>
            </HBox>
            <HBox width="100%"  height="26">
              <SwtLabel #applyAbsoluteLabel width="170" textAlign="right"></SwtLabel>
              <SwtCheckBox paddingLeft="15" #applyAbsoluteCheck (change)="updateData('keepSelected')"></SwtCheckBox>
            </HBox>
            <HBox width="100%" height="21">
              <SwtLabel #timeFrameLabel width="170" textAlign="right"></SwtLabel>
            </HBox>
              <VBox width="100%" height="60" paddingLeft="65">
                <SwtRadioButtonGroup #timeFrameRadioGroup   id="timeFrameRadioGroup"
                                     align="vertical" (change)="updateData('keepSelected')">
                  <HBox><SwtLabel #entityRadio width="80"fontWeight="normal" textAlign="right"></SwtLabel> <SwtRadioItem #radioE id="radioE" value="entity"    groupName="timeFrameRadioGroup"  selected="true"></SwtRadioItem></HBox>
                  <HBox><SwtLabel #currencyRadio width="80" fontWeight="normal" textAlign="right"></SwtLabel> <SwtRadioItem #radioC id="radioC" value="currency" groupName="timeFrameRadioGroup"></SwtRadioItem></HBox>
                  <HBox><SwtLabel #systemRadio width="80" fontWeight="normal" textAlign="right"></SwtLabel> <SwtRadioItem #radioS id="radioS" value="system" groupName="timeFrameRadioGroup"  ></SwtRadioItem></HBox>
                </SwtRadioButtonGroup>
              </VBox>
            <VBox >


        </VBox>
          </VBox>
        </HBox>

        <HBox width="35%" visible="false" #hboxTextInput horizontalAlign="right">
          <VBox width="100%" height="100%" verticalGap="0">
            <HBox width="100%" height="28">
              <SwtLabel width="65%" textAlign="right" #sodLabel id="sodLabel">
              </SwtLabel>
              <SwtTextInput id="sodText" #sodText
                            textAlign="right"
                            styleName="textInputPadding"
                            width="35%" enabled="false">
              </SwtTextInput>
            </HBox>
            <HBox width="100%" height="28">
              <SwtLabel width="65%" textAlign="right" #confirmedLabel id="confirmedLabel" >
              </SwtLabel>
              <SwtTextInput id="confirmedText" #confirmedText
                            textAlign="right"
                            styleName="textInputPadding"
                            width="35%"enabled="false">
              </SwtTextInput>
            </HBox>
            <HBox width="100%" height="28">
              <SwtLabel width="65%" textAlign="right" #creditLabel id="creditLabel" >
              </SwtLabel>
              <SwtTextInput id="creditText" #creditText
                            textAlign="right"
                            styleName="textInputPadding"
                            width="35%" enabled="false">
              </SwtTextInput>
            </HBox>
            <HBox width="100%" height="28">
              <SwtLabel width="65%" textAlign="right" #releasedPayLabel id="releasedPayLabel" >
              </SwtLabel>
              <SwtTextInput id="releasedPayText" #releasedPayText
                            textAlign="right"
                            styleName="textInputPadding"
                            width="35%"  enabled="false">
              </SwtTextInput>
            </HBox>
            <HBox width="100%" height="28">
              <SwtLabel width="65%" textAlign="right" #otherPaymentsLabel id="otherPaymentsLabel" >
              </SwtLabel>
              <SwtTextInput id="otherPaymentsText" #otherPaymentsText
                            textAlign="right"
                            styleName="textInputPadding"
                            width="35%"  enabled="false">
              </SwtTextInput>
            </HBox>
            <HBox width="100%" height="28">
              <SwtLabel width="65%" textAlign="right" #excludeCLlabel id="excludeCLlabel" >
              </SwtLabel>
              <SwtTextInput id="excludeCLText" #excludeCLText
                            textAlign="right"
                            styleName="textInputPadding"
                            width="35%" enabled="false">
              </SwtTextInput>
            </HBox>
            <HBox width="100%" height="28">
              <SwtLabel width="65%" textAlign="right" #includeCLlabel id="includeCLlabel" >
              </SwtLabel>
              <SwtTextInput id="includeCLText" #includeCLText
                            textAlign="right"
                            styleName="textInputPadding"
                            width="35%" enabled="false">
              </SwtTextInput>
            </HBox>
            <HBox width="100%" height="28">
              <SwtLabel width="65%" textAlign="right" #reservelabel id="reservelabel" >
              </SwtLabel>
              <SwtTextInput id="reserveText" #reserveText
                            textAlign="right"
                            styleName="textInputPadding"
                            width="35%" enabled="false">
              </SwtTextInput>
            </HBox>

          </VBox>
        </HBox>
      </HBox>
    </SwtCanvas>
    <SwtTabNavigator #tabs id="tabs"  minWidth="1200"  (onChange)="tabIndexchangeHandler()" width="100%" height="2%" borderBottom="false" >
    </SwtTabNavigator>
    <VBox width="100%" height="65%"  minWidth="1200"  paddingLeft="5"  verticalGap="1" styleName="borderVBox">
      <SwtCommonGridPagination #numstepper></SwtCommonGridPagination>
      <SwtCanvas #gridCanvas id="gridCanvas" width="100%" height="95%"></SwtCanvas>
    </VBox>
    <SwtCanvas  id="canvasButtons"   minWidth="1200"  width="100%" height="5%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="80%">
          <SwtButton #displayButton    id="displayButton"
                     [buttonMode]="true"
                     (click)="displayPayment($event)"
                     enabled="false"
                     width="120">
          </SwtButton>
          <SwtButton #releaseButton   id="releaseButton"
                     [buttonMode]="true"
                     (click)="checkBeforeRelease($event)"
                     enabled="false"
                     width="120">
          </SwtButton>
          <SwtButton #spreadButton    id="spreadButton"
                     [buttonMode]="true"
                     enabled="false"
                     (click)="spreadDisplay($event)"
                     width="120">
          </SwtButton>
          <SwtButton #unStopButton   id="unStopButton"
                     enabled="false"
                     (click)="checkBeforeUnstop($event)"
                     width="70">
          </SwtButton>
          <SwtButton #changeCatgButton   id="changeCatgButton"
                     enabled="false"
                     (click)="changeCategory($event)"
                     width="120">
          </SwtButton>

          <SwtButton #refreshButton
                     id="refreshButton"
                     [buttonMode]="true"
                     (click)="updataDataWithPagination()"
                     width="70">
          </SwtButton>
          <SwtButton  #closeButton
                      id="closeButton"
                      [buttonMode]="true"
                      (click)="closeHandler()"
                      width="70"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" width="20%">
            <DataExport  #dataExport id="dataExport"></DataExport>
          <SwtButton buttonMode="true"
                     id="printIcon"
                     #printIcon
                     enabled="false"
                     styleName="printIcon"
                     (click)="print()">
          </SwtButton>
          <SwtHelpButton id="helpIcon"
                         #helpIcon
                         (click)="doHelp()">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
