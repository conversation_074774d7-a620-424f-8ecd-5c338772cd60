
<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas minWidth="1100" width="100%" height="90">
      <HBox width="100%" height="100%" paddingLeft="5" paddingRight="5">
          <VBox width="100%" height="100%"  verticalGap="0">
            <HBox width="100%" height="25">
              <SwtLabel id="entityLabel"
                        #entityLabel
                        width="120"  >
              </SwtLabel>
              <SwtComboBox id="entityCombo" #entityCombo
                           dataLabel="entityList"
                           width="140"
                           (change)="updateData($event, 'entity')" >
              </SwtComboBox>
              <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal"  paddingLeft="10" >
              </SwtLabel>
            </HBox>
            <HBox height="25" width="100%">
              <SwtLabel id="ccyGroupLabel"
                        #ccyGroupLabel
                        width="120" >
              </SwtLabel>
              <SwtComboBox id="ccyGroupCombo" #ccyGroupCombo
                           (change)="updateData($event, 'currency')"
                           dataLabel="currencyGrpList"
                           width="140">
              </SwtComboBox>
              <SwtLabel id="selectedCcyGroup" #selectedCcyGroup
                        fontWeight="normal" paddingLeft="10"  >
              </SwtLabel>

            </HBox>
            <HBox height="25" width="100%">
              <SwtLabel #accountTypeLabel
                        id="accountTypeLabel"
                        width="120" >
              </SwtLabel>
              <SwtComboBox id="acctTypeCombo" #acctTypeCombo
                           dataLabel="acctTypeList"
                           (change)="updateData($event, 'account')"
                           width="92">
              </SwtComboBox>
              <SwtLabel id="selectedAcctType" #selectedAcctType
              fontWeight="normal" paddingLeft="58"  >
              </SwtLabel>
            </HBox>

          </VBox>

      </HBox>
    </SwtCanvas>

    <SwtCanvas id="dataGridContainer1" #dataGridContainer1 minWidth="1200" width="100%" height="50%"></SwtCanvas>
    <SwtCanvas id="dataGridContainer2" #dataGridContainer2 minWidth="1200" width="100%" height="27%"></SwtCanvas>
    
    <SwtCanvas  id="canvasButtons" minWidth="1100" height="40" marginBottom="0"  width="100%">
      <HBox width="100%" height="100%">
        <HBox paddingLeft="5" width="50%" >
          <SwtButton #submitButton
                     width="70"
                     id="submitButton"
                     enabled="false"
                     (click)="submit($event)"
                     [buttonMode]="true"></SwtButton>
          <SwtButton #authButton
                     width="70"
                     id="authButton"
                     enabled="false"
                     (click)="submit($event)"
                     [buttonMode]="true"></SwtButton>
          <SwtButton #changeButton width="70"
                     id="changeButton"
                     enabled="false"
                     (click)="openSweepQueueDetail()"></SwtButton>
          <SwtButton id= "refreshButton" #refreshButton
                     width="70"
                     (click)="updateData($event, null)"></SwtButton>
          <SwtButton  #searchButton
                      width="70"
                      id="searchButton"
                      (click)="openSearch()"></SwtButton>
          <SwtButton  #closeButton
                      width="70"
                      (click)="closeHandler()"
                      id="closeButton"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" width="50%" paddingTop="5" >
          <SwtLabel visible="false" color="red"  #dataBuildingText ></SwtLabel>
          <SwtLabel visible="false" color="red"  #lostConnectionText></SwtLabel>
         <SwtLabel #lastRefTimeLabel fontWeight="normal"></SwtLabel> <SwtLabel #lastRefTime fontWeight="normal"></SwtLabel>
         
         <SwtHelpButton id="helpIcon" 
         [buttonMode]="true" 
         enabled="true" 
         helpFile="spread-profile" 
         (click)="doHelp()">
        </SwtHelpButton>
         
         <SwtButton [buttonMode]="true" 
                    #printButton 
                    id="printButton" 
                    styleName="printIcon" 
                    (click)="printPage()"
                    (keyDown)="keyDownEventHandler($event)">
        </SwtButton>

          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
