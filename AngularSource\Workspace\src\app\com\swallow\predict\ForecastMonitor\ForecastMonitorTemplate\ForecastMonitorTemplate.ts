
import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService, ExternalInterface,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas, SwtCommonGrid,
  SwtLoadingImage, SwtModule, SwtUtil, SwtToolBoxModule, SwtDataExport, ExportEvent,
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";
declare var instanceElement: any;
@Component({
  selector: 'app-forcast-monitor',
  templateUrl: './ForecastMonitorTemplate.html',
  styleUrls: ['./ForecastMonitorTemplate.css']
})

export class ForecastMonitorTemplate extends  SwtModule {

  @ViewChild('forecastMonitorTemplateCanvas') forecastMonitorTemplateCanvas: SwtCanvas;

  /********SwtButton*********************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  /*********LodingImage*************/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('dataExport') dataExport: SwtDataExport;

   /* Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;
  private swtAlert: SwtAlert;
  private forecastMonitorTemplateGrid: SwtCommonGrid;
  public menuAccessId: string = null;
  private  templateId:string=null;
  private userId:string=null;
  private  templateName:string=null;
  private  isPublic:string=null;
  public fourEyesRequired: boolean = true;

  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    Window['Main'] = this;
    this.swtAlert = new SwtAlert(commonService);
  }

  public static ngOnDestroy(): any {
    instanceElement = null;
  }
  ngOnInit(): void {
    instanceElement = this;
  }
  onLoad() {
    this.requestParams = [];
    this.forecastMonitorTemplateGrid = <SwtCommonGrid> this.forecastMonitorTemplateCanvas.addChild(SwtCommonGrid);
    this.forecastMonitorTemplateGrid.onFilterChanged = this.disableButtons.bind(this);
    this.forecastMonitorTemplateGrid.onSortChanged = this.disableButtons.bind(this);
    try {
      this.menuAccessId=ExternalInterface.call('eval', 'menuAccessId');
      if (this.menuAccessId != "0")
      {
        this.addButton.enabled=false;
      }
      this.actionMethod = 'method=displayMonitorTemplate';
      this.actionPath = 'forecastMonitorTemplate.do?';
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      //this.requestParams['method']= 'displayMonitorTemplate';
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url =  this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.forecastMonitorTemplateGrid.onRowClick = (event) => {
        this.cellLogic(event);
      };
      this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
      this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
      this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
      this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
      this.addButton.toolTip = SwtUtil.getPredictMessage('button.add', null);
      this.changeButton.toolTip = SwtUtil.getPredictMessage('button.change', null);
      this.deleteButton.toolTip = SwtUtil.getPredictMessage('button.delete', null);
      this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
      ExportEvent.subscribe((type) => {
        this.report(type)
      });

    } catch (e) {
      console.log('errr', e);
    }
  }

  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  public inputDataResult(data): void {

    try {
      if (this.inputData && this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (this.jsonReader.getRequestReplyStatus()) {

          if (!this.jsonReader.isDataBuilding()) {
            const obj = {columns: this.jsonReader.getColumnData()};
            this.forecastMonitorTemplateGrid.CustomGrid(obj);
            if (this.jsonReader.getGridData().size > 0) {
              this.forecastMonitorTemplateGrid.gridData = this.jsonReader.getGridData();
              this.forecastMonitorTemplateGrid.setRowSize = this.jsonReader.getRowSize();

            } else {
              this.forecastMonitorTemplateGrid.dataProvider = [];
              this.forecastMonitorTemplateGrid.selectedIndex = -1;
            }

          }
          this.prevRecievedJSON = this.lastRecievedJSON;

        }
        // }

      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }

refreshdetails():void
  {

    this.menuAccessId=ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId != "0")
    {
      this.addButton.enabled=false;
    }

    this.inputData.cbStart=this.startOfComms.bind(this);
    this.inputData.cbStop=this.endOfComms.bind(this);
    //result event 
    this.inputData.cbResult= (event) => {
      this.inputDataRefresh(event);
    };
    //fault event
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    //action url	
    this.actionPath="forecastMonitorTemplate.do?method=";
    //Then declare the action method:					
    this.actionMethod="displayMonitorTemplate";
   // this.addEventListener("CellClick", cellLogic, true);

    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    //Make initial request
    this.inputData.send(this.requestParams);
  }
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  inputDataFault(): void {
    this.swtAlert.error('genericthis.exception');

  }


  addTemplate(): void {
    try {

      ExternalInterface.call("clearSessionInstance");
      /* Url to load Add template screen */
      this.actionMethod="displayAddMonitorTemplate";
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      ExternalInterface.call("openChildWindow",this.actionMethod);


    } catch (e) {

      console.log('error add', e);
    }

  }
  changeTemplate(): void {
    try {
      let lockedBy: string = ExternalInterface.call("lockTemplate", this.templateId, this.templateName, this.userId, this.isPublic);

      // Condition to check whether the template is being locked by any user
      if (lockedBy == null || lockedBy == "")
      {
        // Clear if any templates exist for the current user in session
        ExternalInterface.call("clearSessionInstance");
        /* Url to load Add template screen */
        this.actionMethod="displayChangeMonitorTemplate";
        this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
        this.actionMethod= this.actionMethod + "&templateId=" + this.templateId;
        this.actionMethod=this.actionMethod + "&templateName=" +this.templateName;
        this.actionMethod=this.actionMethod + "&userId=" + this.userId;
        this.actionMethod=this.actionMethod + "&isPublic=" + this.isPublic;
        ExternalInterface.call("openChildWindow", this.actionMethod);
      }
      else
      {
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecasttemplate.templatelocked', null) + lockedBy);
      }
    } catch (e) {
      console.log('error add', e);
    }
  }
  deleteTemplate(): void {
    this.swtAlert.question('Are you sure you want to delete this template?', null, Alert.OK | Alert.CANCEL, null, this.templateRemoveAlertListener.bind(this), null);
  }

  private  templateRemoveAlertListener(event):void
  {
    /* Condition to check Ok button is selected */
    this.requestParams = [];
    if (event.detail == Alert.OK)
    {
      let lockedBy: string =ExternalInterface.call("lockTemplate", this.templateId, this.templateName, this.userId, this.isPublic);
      if (lockedBy == null || lockedBy == "")
      {
        /* Url to delete selected role */
        this.actionMethod='method=deleteMonitorTemplate';
        this.inputData.cbStart=this.startOfComms.bind(this);
        this.inputData.cbStop=this.endOfComms.bind(this);
        this.inputData.cbResult=(event) => {
          this.inputDataRefresh(event);
        };
        this.inputData.cbFault= this.inputDataFault;
        this.inputData.encodeURL=false;
        this.requestParams["templateId"]=this.templateId;
        this.requestParams["userId"]= this.userId;
        this.requestParams["templateName"]=this.templateName;
        this.requestParams["method"]="deleteMonitorTemplate";
        this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);
        this.forecastMonitorTemplateGrid.selectedIndex = -1;
        this.changeButton.enabled = false;
        this.deleteButton.enabled = false;
      }
      else
      {
        this.swtAlert.warning( 'Template ID is locked by ' + lockedBy);
      }

    }
  }
   closeHandler():void
  {
    //call for close window
    ExternalInterface.call("close");
  }


  doHelp() : void {
    ExternalInterface.call('help');
  }
  /**
   * cellLogic
   *
   * @param event: Event
   *
   * This method is used to maintain the button status when a row is clicked
   */
  cellLogic(event: Event): void {
    try {
      if(this.menuAccessId == "0" && this.forecastMonitorTemplateGrid.selectedIndex >=0 && this.forecastMonitorTemplateGrid.selectedItem) {
        this.templateId= this.forecastMonitorTemplateGrid.selectedItem.templateid.content;
        this.userId=this.forecastMonitorTemplateGrid.selectedItem.userid.content;
        this.templateName=this.forecastMonitorTemplateGrid.selectedItem.templatename.content;
        this.isPublic=this.forecastMonitorTemplateGrid.selectedItem.ispublic.content;
        this.disableOrEnableButtons(true);
      } else {
        this.disableOrEnableButtons(false);
      }

    } catch (e) {
      console.log('error event click', e);

    }
  }

  /**
   * Method to disable or enable buttons
   * @param status:Boolean
   * @return
   */
   disableOrEnableButtons(status:boolean):void
  {
    if (status) {
      this.changeButton.enabled=true;
      this.deleteButton.enabled= !((this.templateId == "*DEFAULT*" && this.userId == "*DEFAULT*"));
    } else {
      this.changeButton.enabled=false;
      this.deleteButton.enabled=false;
    }
  }
  disableButtons() {
     if(this.forecastMonitorTemplateGrid.selectedIndex == -1){
       this.changeButton.enabled=false;
       this.deleteButton.enabled=false;
     }
  }


  /**
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   * */
   inputDataRefresh(event):void
  {
    try {
      if (this.inputData && this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);

        if (this.jsonReader.getRequestReplyStatus()) {

          if (!this.jsonReader.isDataBuilding()) {
            const obj = {columns: this.jsonReader.getColumnData()};
            this.forecastMonitorTemplateGrid.CustomGrid(obj);
            if (this.jsonReader.getGridData().size > 0) {
              this.forecastMonitorTemplateGrid.gridData = this.jsonReader.getGridData();
              this.forecastMonitorTemplateGrid.setRowSize = this.jsonReader.getRowSize();
              this.changeButton.enabled=false;
              this.deleteButton.enabled=false;
            } else {
              this.forecastMonitorTemplateGrid.dataProvider = [];
              this.forecastMonitorTemplateGrid.selectedIndex = -1;
            }
          }
        }

      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }
  report(str): void {

    this.dataExport.convertData(this.lastRecievedJSON.forecastmonitor.grid.metadata.columns , this.forecastMonitorTemplateGrid, null , null, str, false);
  }

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ForecastMonitorTemplate }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ForecastMonitorTemplate],
  entryComponents: []
})
export class ForecastMonitorTemplateModule {}
