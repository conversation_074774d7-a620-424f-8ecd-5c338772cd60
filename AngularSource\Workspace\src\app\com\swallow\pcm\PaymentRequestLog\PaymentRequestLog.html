<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <SwtPanel width="100%" height="100%">
    <VBox  width="100%" height="100%" paddingLeft="5" paddingTop="10" paddingRight="5" paddingBottom="5">
      <SwtCanvas width="100%" height="90%" #customGrid>
        <SwtCommonGrid  #paymentRequestLogGrid  ></SwtCommonGrid>
      </SwtCanvas>
      <SwtCanvas width="100%" height="40">
        <HBox>
            <HBox width="100%" >
            <SwtButton #closeButton
                       label="Close"
                       (click)="closeCurrentTab()"
                       toolTip="Close window"></SwtButton>
            </HBox>
            <HBox horizontalAlign="right">
            </HBox>
      </HBox>
      </SwtCanvas>
  </VBox>
</SwtPanel>
</SwtModule>
