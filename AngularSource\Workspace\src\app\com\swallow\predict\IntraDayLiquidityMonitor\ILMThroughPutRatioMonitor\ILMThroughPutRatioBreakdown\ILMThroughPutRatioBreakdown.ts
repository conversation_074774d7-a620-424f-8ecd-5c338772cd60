import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CommonService, SwtAlert, SwtModule, SwtToolBoxModule, SwtDateField, SwtButton, SwtLoadingImage, SwtComboBox, SwtLabel, SwtCanvas, SwtCommonGrid, HTTPComms, JSONReader, ScreenVersion, Timer, ExternalInterface, SwtUtil, SwtCommonGridPagination, SwtCheckBox, SwtDataExport, SwtPopUpManager, ExportEvent, DataExportMultiPage } from 'swt-tool-box';
import { PaginationChangedArgs } from 'angular-slickgrid';
import { FontSetting } from '../../../FontSetting/FontSetting';

@Component({
  selector: 'ilmthrough-put-ratio-breakdown',
  templateUrl: './ILMThroughPutRatioBreakdown.html',
  styleUrls: ['./ILMThroughPutRatioBreakdown.css']
})
export class ILMThroughPutRatioBreakdown extends SwtModule implements OnInit {

  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('optionButton') optionButton: SwtButton;
  @ViewChild('noteButton') noteButton: SwtButton;
  @ViewChild('movementButton') movementButton: SwtButton;
  @ViewChild('messageButton') messageButton: SwtButton;


  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('groupCombo') groupCombo: SwtComboBox;
  @ViewChild('scenarioCombo') scenarioCombo: SwtComboBox;

  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('ccyLabel') ccyLabel: SwtLabel;

  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('selectedGroup') selectedGroup: SwtLabel;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;

  @ViewChild('lastRefLabel') lastRefLabel: SwtLabel;
  @ViewChild('displaycontainer') displaycontainer: SwtCanvas;
  @ViewChild('numstepper', { read: SwtCommonGridPagination }) numstepper: SwtCommonGridPagination;




  @ViewChild('foreOutlflowsCheckbox') foreOutlflowsCheckbox: SwtCheckBox;
  @ViewChild('actOutlflowsCheckbox') actOutlflowsCheckbox: SwtCheckBox;
  @ViewChild('foreIntlflowsCheckbox') foreIntlflowsCheckbox: SwtCheckBox;
  @ViewChild('actInlflowsCheckbox') actInlflowsCheckbox: SwtCheckBox;
  @ViewChild('ccyThresholdCheckbox') ccyThresholdCheckbox: SwtCheckBox;
  @ViewChild('unsettledOutflows') unsettledOutflows: SwtCheckBox;
  @ViewChild('dataExport') dataExport: DataExportMultiPage;

  private moduleId = "PREDICT.THROUGHPUTBREAKDOWN";
  private baseURL = SwtUtil.getBaseURL();
  private throughputBreakdownGrid: SwtCommonGrid;
  public inputData = new HTTPComms(this.commonService);
  public cancelExport = new HTTPComms(this.commonService);
  private invalidComms: string = null;
  private actionMethod: string = "";
  private actionPath: string = "";
  private ilmConfParams: Object;
  private requestParams = [];
  private jsonReader: JSONReader = new JSONReader();
  public currencyFormat: string;
  public screenVersion = new ScreenVersion(this.commonService);
  private showJSONPopup: any;
  public lastRecievedJSON;
  public prevRecievedJSON;
  public dateFormat: string = "";
  public sysdate: Date;
  private autoRefresh: Timer;
  private refreshRate: number = 150;
  private comboOpen: boolean = false;
  private comboChange: boolean = false;
  private entitycomboChange: boolean = false;
  private screenName: string = "";
  public lastNumber: number = 0;


  public entityId: string = null;
  public currencyCode: string = null;
  public ilm_group: string = null;
  public scenarioId: string = null;
  public drilldownColumn: string = null;
  public valueDate: string = null;
  public menuEntityCurrGrpAccess :string = "2";
  private swtAlert: SwtAlert;
  private previousSort = "7|true|";
  public win: any;

  private lastSelectedFilterCheckboxes: string = "YNNNN";

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {

    if (window.opener && window.opener.Main) {
      let params = window.opener.Main.getParams();
      if (params.length == 6) {
        this.entityId = params[0];
        this.currencyCode = params[1];
        this.ilm_group = params[2];
        this.scenarioId = params[3];
        this.drilldownColumn = params[4];
        this.valueDate = params[5];
      }
      
    }


    this.throughputBreakdownGrid = <SwtCommonGrid>this.displaycontainer.addChild(SwtCommonGrid);
    this.throughputBreakdownGrid.uniqueColumn = "movement";
    this.throughputBreakdownGrid.clientSideSort = false;
    this.throughputBreakdownGrid.clientSideFilter = false;


    this.foreOutlflowsCheckbox.label = SwtUtil.getPredictMessage("ilmthroughputbreakdown.foreOutlflowsCheckbox");
    this.actOutlflowsCheckbox.label = SwtUtil.getPredictMessage("ilmthroughputbreakdown.actOutlflowsCheckbox");
    this.foreIntlflowsCheckbox.label = SwtUtil.getPredictMessage("ilmthroughputbreakdown.foreIntlflowsCheckbox");
    this.actInlflowsCheckbox.label = SwtUtil.getPredictMessage("ilmthroughputbreakdown.actInlflowsCheckbox");
    this.unsettledOutflows.label = SwtUtil.getPredictMessage("ilmthroughputbreakdown.unsettledOutflows");
    this.ccyThresholdCheckbox.label = SwtUtil.getPredictMessage("ilmthroughputbreakdown.ccyThresholdCheckbox");


    this.screenName = ExternalInterface.call('getBundle', 'text', 'label-screenName', 'Inra-Day Liquidity Monitor - Main Screen');
    this.entityLabel.text = ExternalInterface.call('getBundle', 'text', 'entity', 'Entity');
    this.entityCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'entity', 'Select an entity ID');
    this.ccyLabel.text = ExternalInterface.call('getBundle', 'text', 'currency', 'Currency');
    this.ccyCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'currency', 'Select currency code');

    this.refreshButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-refresh', 'Refresh window');
    this.refreshButton.label = ExternalInterface.call('getBundle', 'text', 'button-refresh', 'Refresh');

    this.closeButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Close');
    this.closeButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-close', 'Close window');


    this.optionButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-options', 'Options');
    this.optionButton.label = ExternalInterface.call('getBundle', 'text', 'button-options', 'Options');
    this.noteButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-notes', 'Movement notes');
    this.noteButton.label = ExternalInterface.call('getBundle', 'text', 'button-notes', 'Notes');
    this.movementButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-movement', 'Show selected movement in detail');
    this.movementButton.label = ExternalInterface.call('getBundle', 'text', 'button-movement', 'Mvmnt');
    this.messageButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-message', 'Messages on selected movement');
    this.messageButton.label = ExternalInterface.call('getBundle', 'text', 'button-message', 'Message');
    this.lastRefLabel.text = ExternalInterface.call('getBundle', 'text', 'lastrefresh', 'Last Refresh:');

    this.throughputBreakdownGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    };
    this.throughputBreakdownGrid.onPaginationChanged = (event) => {
      this.paginationChanged(event);
    };
    this.throughputBreakdownGrid.onSortChanged = (event) => {
      this.paginationChanged(event);
    };
    this.throughputBreakdownGrid.onFilterChanged = (event) => {
      this.paginationChanged(event);
    };

    ExportEvent.subscribe((exportEvent) => {
      this.report(exportEvent.type, exportEvent.startPage, exportEvent.noOfPages);
    });


  }
  exportCancel(event):void {
    // Calls to start the Http communication
    this.cancelExport.cbStart=this.startOfComms.bind(this);
    // Calls to stop the Http communication
    this.cancelExport.cbStop=this.endOfComms.bind(this);
    // Calls the inputDataResult function to load the datagrid
    this.cancelExport.cbResult= (event) => {
      this.ExportCanceled(event);
    };
    // Sets the action path for Interface Monitor
    this.actionPath="outstandingmovement.do?";
    // Sets the action method to get the Interface Monitor Details
    this.actionMethod="method=cancelILMExport";
    // Sets the full URL for Interface Monitor
    this.cancelExport.url=this.baseURL + this.actionPath + this.actionMethod;
    // Sets the flag for encoding URL to false
    this.cancelExport.encodeURL=false;
    // Calls the inputDataFault function
    this.cancelExport.cbFault=this.inputDataFault.bind(this);
    this.requestParams["cancelExport"] = "true";
    // Send the request to the server
    this.cancelExport.send(this.requestParams);

  }
  ExportCanceled(event):void{
  }

  report(type: string, startPage:number, noOfPages:number): void {
    try {
      
      this.requestParams["exportType"] = type;
      this.requestParams["currentPage"] = startPage;
      this.requestParams['pageCount']= noOfPages ;
      this.requestParams["applyThreshold"] = this.ccyThresholdCheckbox.selected?'Y':"N";
      this.requestParams["currentFilter"] = this.getCurrentFilter();
      this.requestParams["entityId"] = this.entityId;
      this.requestParams["currencyId"] = this.currencyCode;
      this.requestParams["selectedScenario"] = this.scenarioId;
      this.requestParams["selectedDate"] = this.valueDate;
      this.requestParams["accountGroup"] = this.ilm_group;
      let selectedSort = (this.throughputBreakdownGrid.getMsdSortedGridColumn()) ? this.throughputBreakdownGrid.getMsdSortedGridColumn() + '|' : this.previousSort;
      let selectedFilter = this.throughputBreakdownGrid.getFilteredGridColumns();
      //provisoire jusqu'a ajout de empty not empty in filter
      if (selectedFilter.indexOf('||') != -1) {
        selectedFilter = selectedFilter.replace('||', '|(EMPTY)|')
      }

      this.requestParams["selectedSort"] = selectedSort;
      this.requestParams["selectedFilter"] = selectedFilter;
      this.requestParams['tokenForDownload'] = new Date().getTime();
      this.requestParams['screen'] = SwtUtil.getPredictMessage('ilmthroughputbreakdown.title.window', null);
      let requestParams =Object.assign({},this.requestParams);
      ExternalInterface.call("sendParams", requestParams)
    } catch (error) {
        
    }
  }
  onLoad() {
    this.actionPath = 'ilmAnalysisMonitor.do?';
    this.actionMethod = 'method=getThroughputBreakdownDetails';
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.inputDataResult(data);
    };
    if (this.drilldownColumn) {
      if (this.drilldownColumn == 'actinf') {
        this.actInlflowsCheckbox.selected = true;
      } else if (this.drilldownColumn == 'actout') {
        this.actOutlflowsCheckbox.selected = true;
      } else if (this.drilldownColumn == 'unsetout') {
        this.unsettledOutflows.selected = true;
      } else if (this.drilldownColumn == 'forcinf') {
        this.foreIntlflowsCheckbox.selected = true;
      } else if (this.drilldownColumn == 'forcout') {
        this.foreOutlflowsCheckbox.selected = true;
      }
    }
    this.checkIfAtLeastOneSelected();
    this.requestParams["foreOutlflowsCheckbox"] = this.foreOutlflowsCheckbox.selected;
    this.requestParams["actOutlflowsCheckbox"] = this.actOutlflowsCheckbox.selected;
    this.requestParams["foreIntlflowsCheckbox"] = this.foreIntlflowsCheckbox.selected;
    this.requestParams["actInlflowsCheckbox"] = this.actInlflowsCheckbox.selected;
    this.requestParams["unsettledOutflows"] = this.unsettledOutflows.selected;
    this.requestParams["ccyThresholdCheckbox"] = this.ccyThresholdCheckbox.selected;
    this.requestParams["currentFilter"] = this.getCurrentFilter();

    this.requestParams["entityId"] = this.entityId;
    this.requestParams["entityChanged"] = this.entitycomboChange;
    this.requestParams["currencyId"] = this.currencyCode;
    this.requestParams["selectedScenario"] = this.scenarioId;
    this.requestParams["selectedDate"] = this.valueDate;
    this.requestParams["accountGroup"] = this.ilm_group;


    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.dataExport.exportCancelFunction = this.exportCancel.bind(this);
  }

  checkIfAtLeastOneSelected() {
    if (!this.foreOutlflowsCheckbox.selected && !this.actOutlflowsCheckbox.selected && !this.foreIntlflowsCheckbox.selected &&
      !this.actInlflowsCheckbox.selected && !this.unsettledOutflows.selected) {
      this.restoreLastCheckboxesState();
      return false;
    }
    return true;

  }
  getCurrentFilter() {
    let currentFilter = "";
    if (this.unsettledOutflows.selected) {
      return "N|N|Y|";
    } else {
      if (this.foreOutlflowsCheckbox.selected || this.foreIntlflowsCheckbox.selected) {
        if (this.foreOutlflowsCheckbox.selected && this.foreIntlflowsCheckbox.selected)
          currentFilter += 'F|'
        else if (this.foreOutlflowsCheckbox.selected) {
          currentFilter += 'FO|'
        } else {
          currentFilter += 'FI|'
        }
      } else {
        currentFilter += 'N|';
      }
      if (this.actInlflowsCheckbox.selected || this.actOutlflowsCheckbox.selected) {
        if (this.actInlflowsCheckbox.selected && this.actOutlflowsCheckbox.selected)
          currentFilter += 'A|'
        else if (this.actInlflowsCheckbox.selected) {
          currentFilter += 'AI|'
        } else {
          currentFilter += 'AO|'
        }
      } else {
        currentFilter += 'N|';
      }

      currentFilter += 'N|';
    }

    return currentFilter;
  }

  /**
* inputDataResult
*
* @param data: ResultEvent
*
* This is a callback method, to handle result event
*
*/
  public inputDataResult(data): void {
    let maxPage: String = null;
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        this.lastRecievedJSON = data;
        this.lostConnectionText.visible = false;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {
              //Gets the current page from xml
              this.numstepper.value = Number(data.ILMThroughPutRatioBreakdown.grid.paging.currentpage);
              //Gets the maximum no of pages value
              maxPage = data.ILMThroughPutRatioBreakdown.grid.paging.maxpage;
              //Sets the Numeric stepper maximum value
              this.numstepper.maximum = Number(maxPage);

              //Fill comboboxes and set selected items
              this.entityCombo.setComboData(this.jsonReader.getSelects());
              this.ccyCombo.setComboData(this.jsonReader.getSelects());
              this.groupCombo.setComboData(this.jsonReader.getSelects());
              this.scenarioCombo.setComboData(this.jsonReader.getSelects());

              this.selectedEntity.text = this.entityCombo.selectedValue;
              this.selectedCcy.text = this.ccyCombo.selectedValue;
              this.selectedGroup.text = this.groupCombo.selectedValue;

              this.lastRefTime.text = this.jsonReader.getSingletons().lastRefTime;
              this.menuEntityCurrGrpAccess = this.jsonReader.getSingletons().menuEntityCurrGrpAccess;


              this.throughputBreakdownGrid.CustomGrid(data.ILMThroughPutRatioBreakdown.grid.metadata);
              this.throughputBreakdownGrid.paginationComponent = this.numstepper;
              if (data.ILMThroughPutRatioBreakdown.grid.rows.row && this.jsonReader.getGridData().size > 0 && Number(maxPage) > 0) {
                this.throughputBreakdownGrid.gridData = this.jsonReader.getGridData();
                this.throughputBreakdownGrid.setRowSize = this.jsonReader.getRowSize();
                //this.throughputBreakdownGrid.doubleClickEnabled = true;
                this.throughputBreakdownGrid.gridData = data.ILMThroughPutRatioBreakdown.grid.rows;
                this.cellClickEventHandler(event);
                this.dataExport.enabled = true;
              } else {
                this.throughputBreakdownGrid.dataProvider = [];
                this.throughputBreakdownGrid.selectedIndex = -1;
                this.dataExport.enabled = false;
              }
            }
            this.dataExport.maxPages=Number(maxPage);
            this.dataExport.totalPages=Number(maxPage);
            this.dataExport.currentPage = this.numstepper.value; //this.jsonReader.getScreenAttributes()["pages"].currentPage;
            if (this.jsonReader.getRefreshRate())
              this.refreshRate = parseInt(this.jsonReader.getRefreshRate());

            // Triggers the auto refresh if it's null
            if (this.autoRefresh == null) {
              this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
              this.autoRefresh.addEventListener("timer", this.doRefreshPage.bind(this, true));
              this.autoRefresh.start();
            } else {
              // Sets delay to auto refresh and triggers it
              this.autoRefresh.delay(this.refreshRate * 1000);
            }
            // this.throughputBreakdownGrid.selectedIndex = -1;
            this.prevRecievedJSON = this.lastRecievedJSON;

            this.lastSelectedFilterCheckboxes = this.getCurrentCheckboxesState();

          } else {
            if (this.jsonReader.getRequestReplyMessage() == "errors.DataIntegrityViolationExceptioninDelete") {
              this.swtAlert.error("Unable to delete, this spread profile is linked to an existing account group");
            } else {
              this.swtAlert.error(SwtUtil.getCommonMessages('alert.generic_exception'));
            }
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
    }
    this.entitycomboChange = false;
  }




  getCurrentCheckboxesState(): string {
    let currentState = "";
    currentState += this.foreOutlflowsCheckbox.selected ? "Y" : "N";
    currentState += this.foreIntlflowsCheckbox.selected ? "Y" : "N";
    currentState += this.actOutlflowsCheckbox.selected ? "Y" : "N";
    currentState += this.actInlflowsCheckbox.selected ? "Y" : "N";
    currentState += this.unsettledOutflows.selected ? "Y" : "N";
    return currentState;
  }


  restoreLastCheckboxesState(): void {

    if (this.lastSelectedFilterCheckboxes.length > 0 && this.lastSelectedFilterCheckboxes.length == 5) {
      this.foreOutlflowsCheckbox.selected = this.lastSelectedFilterCheckboxes[0] == "Y";
      this.foreIntlflowsCheckbox.selected = this.lastSelectedFilterCheckboxes[1] == "Y";
      this.actOutlflowsCheckbox.selected = this.lastSelectedFilterCheckboxes[2] == "Y";
      this.actInlflowsCheckbox.selected = this.lastSelectedFilterCheckboxes[3] == "Y";
      this.unsettledOutflows.selected = this.lastSelectedFilterCheckboxes[4] == "Y";
    }

  }

  cellClickEventHandler(event): void {
    let enableButtons = false;
    if (this.throughputBreakdownGrid.selectedIndices.length > 0) {
      enableButtons = true;
    }
    this.messageButton.enabled = enableButtons;
    this.messageButton.buttonMode = enableButtons;
    this.movementButton.enabled = enableButtons;
    this.movementButton.buttonMode = enableButtons;
    this.noteButton.enabled = enableButtons;
    this.noteButton.buttonMode = enableButtons;

  }



  openNotes(): void {

    let tempFlag: boolean = false;
    if (this.throughputBreakdownGrid.selectedIndices.length > 0) {
      tempFlag = true;
    }
    if (tempFlag) {
      let sMovementId: string = this.throughputBreakdownGrid.selectedItem.movementId.content;
      ExternalInterface.call("movementNotesFromSearch", "notes", sMovementId, "", this.entityId, this.currencyCode, this.valueDate, this.menuEntityCurrGrpAccess);
    }
  }

  /**
   * This method is used to check whether the selected movement has messages or
   * not, If it has messages then the messages will displayed in a separate
   * window.
   */
  openMessages(): void {
    let result: string;
    let noOfMovements: string;
    let msgId: string;
    let tempFlag: boolean = false;
    if (this.throughputBreakdownGrid.selectedIndices.length > 0) {
      tempFlag = true;
    }
    if (tempFlag) {
      let sMovementId: string = this.throughputBreakdownGrid.selectedItem.movementId.content;
      result = ExternalInterface.call("setMsgButtonStatus", sMovementId);
      noOfMovements = result.substring(0, result.indexOf('|'));
      msgId = result.substring(result.indexOf('|') + 1, result.length);
      ExternalInterface.call("openMsgDisplayWindow", sMovementId, noOfMovements, msgId);
    }
  }

  /**
   * This method is used to display the selected movement details in a new
   * window.
   */
  openMovement(): void {
    let sEntityId: string;
    let tempFlag: boolean = false;
    if (this.throughputBreakdownGrid.selectedIndices.length > 0) {
      tempFlag = true;
    }
    if (tempFlag) {
      let sMovementId: string = this.throughputBreakdownGrid.selectedItem.movementId.content;
      sEntityId = (this.entityCombo.selectedLabel).toString();
      ExternalInterface.call("showMvmnt", "showMovementDetails", sMovementId, sEntityId, "");
    }
  }


  /**
   * This function helps in instantiating the Font setting window with the event handling functionality for setting up font size.
   */
  fontSettingHandler(): void {
    this.win = SwtPopUpManager.createPopUp(this, FontSetting, {
      title: "Options", //SwtUtil.getPredictMessage('cutOff.title.addRule', null), // childTitle,
      fontSizeValue: this.selectedFont,
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '360';
    this.win.height = '140';
    this.win.showControls = true;
    this.win.id = "optionsWindow";
    this.win.onClose.subscribe((res) => {
      this.submitFontSize(res);
    });

    this.win.display();
  }

  // Declares selectedFont
  private selectedFont: number = 0;
  // Declares the fontValue
  private fontValue: string = "";

  /**
   * This function is used in setting font size of the datagrid on MSD screen based on the user's choice.
   */
  submitFontSize(res): void {
    this.fontValue = res.fontSize.value;
    // this.fontLabel=res.fontSize.label;
    // Sets the datagrid style based on the font value
    if (this.fontValue == "N") {
      this.selectedFont = 0;
      this.throughputBreakdownGrid.styleName = "dataGridNormal";
      this.throughputBreakdownGrid.rowHeight = 18;
    }
    else if (this.fontValue == "S") {
      this.selectedFont = 1;
      this.throughputBreakdownGrid.styleName = "dataGridSmall";
      this.throughputBreakdownGrid.rowHeight = 15;
    }
  }


  closeHandler() {
    ExternalInterface.call("close");
  }

  /**
 * Function called for combo change event.
 **/
  entityChangeCombo(e): void {
    // e.stopImmediatePropagation();
    this.comboChange = true;
    this.entitycomboChange = true;
    this.doRefreshPage(true);
  }
  /**
 * Function called for combo change event.
 **/
  changeCombo(e): void {
    // e.stopImmediatePropagation();
    this.comboChange = true;
    this.doRefreshPage(true);
    this.entitycomboChange = false;
  }

  /**
   * When a combobox is open then any requests to the server need to be cancelled
   **/
  openedCombo(event): void {
    this.comboOpen = true;
  }

  /**
   * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
   **/
  closedCombo(event): void {
    this.comboOpen = false;
  }
  /**
* inputDataFault
*
* @param event:  FaultEvent
*
* This is a callback function, used to handle fault event.
* Shows fault message in alert window.
*/
  inputDataFault(event): void {
    try {
      this.lostConnectionText.visible = true;
      this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
      this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
      if (this.autoRefresh != null) {
        if (!this.autoRefresh.running) {
          this.autoRefresh.start();
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'inputDataFault', 0);
    }
  }


  paginationChanged(event: PaginationChangedArgs) {
    this.numstepper.processing = true;
    this.doRefreshPage();
  }
  doHelp() {

  }

  doRefreshPage(fromRefresh: boolean = false, checkBoxOrigin: string = null): void {
    //To hold current page
    var currentPage: number;
    try {
      if (this.numstepper.value > 0) {
        if (((this.numstepper.value <= this.numstepper.maximum) && (this.numstepper.value != 0)) || fromRefresh) {

          // Get the SortedGridColumn
          // selectedSort = this.throughputBreakdownGrid.sortedGridColumn;

          //Gets the current page value
          currentPage = this.numstepper.value;
          this.lastNumber = currentPage;
          //Initialising the request params
          this.requestParams = [];
          //Adding request params
          this.requestParams["currentPage"] = currentPage;

          if (checkBoxOrigin) {
            if (checkBoxOrigin == 'isActualOrForecast') {
              this.unsettledOutflows.selected = false;
            } else if (checkBoxOrigin == 'isUnsettled') {
              this.foreOutlflowsCheckbox.selected = false;
              this.actOutlflowsCheckbox.selected = false;
              this.foreIntlflowsCheckbox.selected = false;
              this.actInlflowsCheckbox.selected = false;
            }
          }

          const check = this.checkIfAtLeastOneSelected();
          if (!check) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.throuputbreakdown.atleastOneFilter', null), 'Warning');
            return;
          }
          this.requestParams["foreOutlflowsCheckbox"] = this.foreOutlflowsCheckbox.selected;
          this.requestParams["actOutlflowsCheckbox"] = this.actOutlflowsCheckbox.selected;
          this.requestParams["foreIntlflowsCheckbox"] = this.foreIntlflowsCheckbox.selected;
          this.requestParams["actInlflowsCheckbox"] = this.actInlflowsCheckbox.selected;
          this.requestParams["unsettledOutflows"] = this.unsettledOutflows.selected;
          this.requestParams["ccyThresholdCheckbox"] = this.ccyThresholdCheckbox.selected;
          this.requestParams["currentFilter"] = this.getCurrentFilter();

          this.entityId = this.entityCombo.selectedLabel;
          this.currencyCode = this.ccyCombo.selectedLabel;
          this.scenarioId = this.scenarioCombo.selectedLabel;
          this.ilm_group = this.groupCombo.selectedLabel;

          this.requestParams["entityId"] = this.entityId;
          this.requestParams["entityChanged"] = this.entitycomboChange;
          this.requestParams["currencyId"] = this.currencyCode;
          this.requestParams["selectedScenario"] = this.scenarioId;
          this.requestParams["selectedDate"] = this.valueDate;
          this.requestParams["accountGroup"] = this.ilm_group;


          let selectedSort = (this.throughputBreakdownGrid.getMsdSortedGridColumn()) ? this.throughputBreakdownGrid.getMsdSortedGridColumn() + '|' : this.previousSort;

          let selectedFilter = this.throughputBreakdownGrid.getFilteredGridColumns();
          //provisoire jusqu'a ajout de empty not empty in filter
          if (selectedFilter.indexOf('||') != -1) {
            selectedFilter = selectedFilter.replace('||', '|(EMPTY)|')
          }

          this.requestParams["selectedSort"] = selectedSort;
          this.requestParams["selectedFilter"] = selectedFilter;


          this.previousSort = selectedSort;

          this.actionPath = 'ilmAnalysisMonitor.do?';
          this.actionMethod = 'method=getThroughputBreakdownDetails';
          //set http url
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;

          //send request to server
          this.inputData.send(this.requestParams);
        }
      }
    }
    catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'inputDataFault', 0);

    }

  }

  enableDisableControls(value: boolean) {
    this.entityCombo.enabled = value;
    this.ccyCombo.enabled = value;
    this.groupCombo.enabled = value;
    this.scenarioCombo.enabled = value;
    this.numstepper.enabled = value;

    this.foreOutlflowsCheckbox.enabled = value;
    this.actOutlflowsCheckbox.enabled = value;
    this.foreIntlflowsCheckbox.enabled = value;
    this.actInlflowsCheckbox.enabled = value;
    this.unsettledOutflows.enabled = value;
    this.ccyThresholdCheckbox.enabled = value;

  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
    this.enableDisableControls(false);


  }

  endOfComms(): void {
    if (!this.inputData.isBusy()) {
      this.loadingImage.setVisible(false);
    }
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
    this.enableDisableControls(true);
  }


}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ILMThroughPutRatioBreakdown }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ILMThroughPutRatioBreakdown],
  entryComponents: []
})
export class ILMThroughPutRatioBreakdownModule { }
