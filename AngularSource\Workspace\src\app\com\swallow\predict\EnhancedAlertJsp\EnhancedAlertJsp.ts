import { Component, ModuleWithProviders, NgModule, OnInit, ViewChild, ElementRef } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Observable } from 'rxjs';
import 'rxjs/add/observable/fromEvent';
import { CommonService, EnhancedAlertingTooltip, ExternalInterface, Logger, SwtToolBoxModule, SwtUtil, SwtModule, JSONReader, HTTPComms } from 'swt-tool-box';
declare var instanceElement: any;
@Component({
  selector: 'app-enhanced-alert-jsp',
  templateUrl: './EnhancedAlertJsp.html',
  styleUrls: ['./EnhancedAlertJsp.css']
})
export class EnhancedAlertJsp extends SwtModule implements OnInit{
  @ViewChild('customTooltip') customTooltip: EnhancedAlertingTooltip;
    /***Module****/
  @ViewChild('swtModule') swtModule: SwtModule;
  tooltipEntityId = null;
  tooltipCurrencyCode = null;
  tooltipFacilityId = null;
  tooltipSelectedDate = null;
  tooltipSelectedAccount = null;
  tooltipMvtId = null;
  tooltipOtherParams = [];
  private positionX:number;
  private positionY:number;
  
  /**
   * Data Objects
   **/
  private jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;


  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private updateRefreshRate = new HTTPComms(this.commonService);
  // Instantiates HTTPComms
  private updateFontSize = new HTTPComms(this.commonService);

  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private invalidComms: string = "";
  private hostId;
  private selectedNodeId = null;
  private treeLevelValue = null;
  constructor(private commonService: CommonService, private element: ElementRef) 
  { 
    super(element, commonService);
  }

  private eventsCreated = false;


  public static ngOnDestroy(): any {
    instanceElement = null;
  }

  ngOnInit() {
    instanceElement = this;
      this.customTooltip.parentDocument= this;
    Observable.fromEvent(document.body, 'click').subscribe(e => {
      this.positionX=e["clientX"];
      this.positionY=e["clientY"];
    });
    this.tooltipEntityId = ExternalInterface.call('eval', 'tooltipEntityId');
    this.tooltipCurrencyCode = ExternalInterface.call('eval', 'tooltipCurrencyCode');
    this.tooltipFacilityId = ExternalInterface.call('eval', 'tooltipFacilityId');
    this.tooltipSelectedDate = ExternalInterface.call('eval', 'tooltipSelectedDate');
    this.tooltipSelectedAccount = ExternalInterface.call('eval', 'tooltipSelectedAccount');
    this.tooltipMvtId = ExternalInterface.call('eval', 'tooltipMvtId');
    this.tooltipOtherParams = ExternalInterface.call('eval', 'tooltipOtherParams');
    //event for display list button click
    setTimeout(() => {

      if (!this.eventsCreated) {
        this.customTooltip.DISPLAY_LIST_CLICK.subscribe((target) => {
          this.lastSelectedTooltipParams = target.dataParams;
          ExternalInterface.call("openAlertInstanceSummary", "openAlertInstSummary", this.selectedNodeId, this.treeLevelValue);
        });
      }
    }, 0);

    //event for link to specific button click
    setTimeout(() => {

      if (!this.eventsCreated) {
        this.customTooltip.LINK_TO_SPECIF_CLICK.subscribe((target) => {
          this.getScenarioFacility(target.noode.data.scenario_id);
          this.lastSelectedTooltipParams = target.dataParams;
          this.hostId = target.hostId;
        });
      }
    }, 0);


    
    //event for tree item click
    setTimeout(() => {

      if (!this.eventsCreated) {
        this.customTooltip.ITEM_CLICK.subscribe((target) => {
          this.selectedNodeId= target.noode.data.id;
          this.treeLevelValue= target.noode.data.treeLevelValue;
          this.customTooltip.linkToSpecificButton.enabled = false;
          if (target.noode.data.count== 1 && target.noode.data.treeLevelName !='category_id' ) {
            this.customTooltip.linkToSpecificButton.enabled = true;
          }
        });
      }
    }, 0);
  }

  getScenarioFacility(scenarioId) {
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "scenarioSummary.do?";
    // Define method the request to access
    this.actionMethod = 'method=getScenarioFacility';
    this.requestParams['scenarioId'] = scenarioId;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.cbResult = (event) => {
      this.openGoToScreen(event);
    };
    this.inputData.send(this.requestParams);
  }

  openGoToScreen(event){
    if(event && event.ScenarioSummary && event.ScenarioSummary.scenarioFacility){
    var facilityId = event.ScenarioSummary.scenarioFacility;

    
    const selectedEntity = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['entity_id'] != null?this.lastSelectedTooltipParams['entity_id']:this.tooltipEntityId;
    const selectedcurrency_code = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['currency_code'] != null?this.lastSelectedTooltipParams['currency_code']:this.tooltipCurrencyCode;
    const selectedmatch_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['match_id'] != null?this.lastSelectedTooltipParams['match_id']:null;
    const selectedmovement_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['movement_id'] != null?this.lastSelectedTooltipParams['movement_id']:this.tooltipMvtId
    const selectedsweep_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['sweep_id'] != null?this.lastSelectedTooltipParams['sweep_id']:null;


    ExternalInterface.call("goTo", facilityId, this.hostId, selectedEntity, selectedmatch_id, selectedcurrency_code, selectedmovement_id, selectedsweep_id, "");
    }
    // ExternalInterface.call("goTo" ,facilityId, this.hostId, this.tooltipEntityId, "", this.tooltipCurrencyCode, this.tooltipMvtId, sweepId, this.tooltipOtherParams);
  }
  private lastSelectedTooltipParams = null;
  getParamsFromParent() {
    // const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId };
    // return params;

    const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId, selectedNodeId:this.selectedNodeId, treeLevelValue:this.treeLevelValue ,
      tooltipCurrencyCode :this.tooltipCurrencyCode , tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate,tooltipSelectedAccount: this.tooltipSelectedAccount,
      tooltipOtherParams:this.tooltipOtherParams, tooltipMvtId :this.tooltipMvtId};
    return params;
  }
  /**
   * inputDataFault
   * @param event:FaultEvent
   * If a fault occurs with the connection with the server then display the lost connection label
   */
  inputDataFault(event): void {
    this.invalidComms = event.fault.faultString + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail;
  }
  /**
* Part of a callback function to all for control of the loading swf from the HTTPComms Object
**/
  startOfComms(): void {
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: EnhancedAlertJsp }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [EnhancedAlertJsp],
  entryComponents: []
})
export class EnhancedAlertJspModule { }