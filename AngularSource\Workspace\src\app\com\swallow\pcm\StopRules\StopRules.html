<SwtModule (creationComplete)='onLoad()' width="100%" height="100%" paddingTop="10">
  <VBox height="100%" width="100%" paddingLeft="10" paddingRight="10" >
    <SwtCanvas width="100%" height="9%">
          <HBox height="28" paddingLeft="10" marginTop="8">
              <SwtLabel text="Status" width="115" paddingTop="5"></SwtLabel>
              <SwtRadioButtonGroup #statusGroup id="statusGroup" (change)="refreshGrid()" align="horizontal">
                <SwtRadioItem #activeRadioButton id="activeRadioButton" value="A" label="Active"
                  groupName="statusRadioGroup" width="160"></SwtRadioItem>
                <SwtRadioItem #inactiveRadioButton id="inactiveRadioButton" value="I"
                  label="Inactive" groupName="statusRadioGroup" width="160"></SwtRadioItem>
                  <SwtRadioItem #bothRadioButton id="bothRadioButton" value="B"
                  label="Both" groupName="statusRadioGroup" width="160"></SwtRadioItem>
              </SwtRadioButtonGroup>
            </HBox>
    </SwtCanvas>
    <SwtCanvas id="canvasStopRules" #canvasStopRules width="100%" height="82%" >
    </SwtCanvas>
    <SwtCanvas width="100%" height="40" >
      <HBox width="100%">
      <HBox  width="100%" paddingLeft="5">
        <SwtButton [buttonMode]="true" id="addButton" #addButton width="70" enabled="false" (click)="doAddPCStopRule($event)" (keyDown)="keyDownEventHandler($event)">
        </SwtButton>
        <SwtButton [buttonMode]="true" id="activateButton" #activateButton width="70" (click)="doActivatePCStopRuleEventHandler('activate')"
          (keyDown)="keyDownEventHandler($event)">
        </SwtButton>
        <SwtButton [buttonMode]="true" id="deactivateButton" #deactivateButton width="80" (click)="doActivatePCStopRuleEventHandler('deactivate')"
          (keyDown)="keyDownEventHandler($event)">
        </SwtButton>
        <SwtButton [buttonMode]="true" id="changeButton"  width="70" #changeButton (click)="doChangePCStopRule($event)"
          (keyDown)="keyDownEventHandler($event)">
        </SwtButton>
        <SwtButton [buttonMode]="true" id="viewButton" width="70" #viewButton (click)="doViewPCStopRule($event)"
          (keyDown)="keyDownEventHandler($event)">
        </SwtButton>
        <SwtButton [buttonMode]="true" id="deleteButton" width="70" #deleteButton (click)="doDeletePCStopRule($event)"
          (keyDown)="keyDownEventHandler($event)">
        </SwtButton>
        <SwtButton [buttonMode]="true" id="closeButton" width="70" #closeButton (click)="closeCurrentTab($event)"
          (keyDown)="keyDownEventHandler($event)">
        </SwtButton>
      </HBox>
      <HBox horizontalAlign="right" paddingRight="10">
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
        <SwtHelpButton id="helpIcon" [buttonMode]="true" enabled="true" helpFile="groups-of-rules" (click)="doHelp()">

        </SwtHelpButton>
      </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
