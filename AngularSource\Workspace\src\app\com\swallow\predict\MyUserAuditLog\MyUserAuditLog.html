<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%">
      <Grid width="100%" height="100%" paddingLeft="5" paddingRight="5">
        <GridRow width="100%" height="25">
          <GridItem width="65%">
            <GridItem width="200">
              <GridItem width="70">
                <SwtLabel id="user" #user></SwtLabel>
              </GridItem>
              <GridItem>
                <SwtTextInput id="userTxtInput" #userTxtInput width="150"
                editable="false" enabled="false"> </SwtTextInput>
              </GridItem>
            </GridItem>
            <GridItem paddingLeft="50">
              <SwtLabel id="userDesc" #userDesc fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="100%">
            <HBox #pageBox horizontalAlign="right" width="100%" visible="false">
              <SwtCommonGridPagination #numstepper></SwtCommonGridPagination>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow width="100%" height="25">
            <GridItem width="70">
            <SwtLabel #startDateLabel id="startDateLabel" styleName="labelBold"></SwtLabel>
            </GridItem>
            <GridItem paddingRight="70">
              <SwtDateField id="fromDateChooser" #fromDateChooser (change)="validateDateField(fromDateChooser)" 
                toolTip="Enter from Date" width="60"></SwtDateField>
            </GridItem>
            <GridItem width="50" paddingLeft="20">
              <SwtLabel #endDateLabel id="endDateLabel" styleName="labelBold"></SwtLabel>
            </GridItem>
            <GridItem >
              <SwtDateField id="toDateChooser" #toDateChooser (change)="validateDateField(toDateChooser)"
                toolTip="Enter to Date" width="60"></SwtDateField>
            </GridItem>
          </GridRow>
      </Grid>
    </SwtCanvas>

    <SwtCanvas id="dataGridContainer" #dataGridContainer paddingBottom="5" styleName="canvasWithGreyBorder"
      marginTop="10" border="false" width="100%" height="100%" minHeight="100"></SwtCanvas>

      <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5">
        <HBox width="100%">
          <HBox paddingLeft="5" paddingTop="2" width="100%" >
            <SwtButton #refreshButton id="refreshButton" (click)="refresh()"></SwtButton>
            <SwtButton #viewButton id="viewButton" enabled="false" (click)="viewDetails()"></SwtButton>
            <SwtButton #closeButton width="70" (click)="closeHandler()" id="closeButton"></SwtButton>
          </HBox>
          <HBox horizontalAlign="right" width="100%" paddingTop="2" >
            <SwtLabel visible="false" color="red" #dataBuildingText></SwtLabel>
            <SwtLabel visible="false" color="red" #lostConnectionText></SwtLabel>
            <SwtLabel #lastRefTimeLabel fontWeight="normal"></SwtLabel>
            <SwtLabel #lastRefTime fontWeight="normal"></SwtLabel>
      
            <HBox horizontalAlign="right" paddingRight="10" paddingTop="2" >
              <SwtHelpButton id="helpIcon" #helpIcon (click)="doHelp()"></SwtHelpButton>
              <SwtButton [buttonMode]="true" #printButton id="printButton" styleName="printIcon"
                (click)="printPage()">
              </SwtButton>
              <SwtLoadingImage #loadingImage></SwtLoadingImage>
            </HBox>
          </HBox>
        </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>