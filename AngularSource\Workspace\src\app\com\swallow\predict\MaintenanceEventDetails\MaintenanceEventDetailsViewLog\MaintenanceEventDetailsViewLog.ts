import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, JSONReader, HTTPComms, SwtUtil, ExternalInterface, CommonService, SwtAlert, SwtCanvas, SwtCommonGrid, Logger, SwtButton, SwtLoadingImage } from "swt-tool-box";
import moment from 'moment';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-acct-ccy-period-maint-view-log',
  templateUrl: './MaintenanceEventDetailsViewLog.html',
  styleUrls: ['./MaintenanceEventDetailsViewLog.css']
})
export class MaintenanceEventDetailsViewLog implements OnInit {

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  /***********SwtCanvas***********/
  @ViewChild('viewLogGridContainer') viewLogGridContainer: SwtCanvas;

    /***********SwtButton***********/
  @ViewChild('closeButton') closeButton: SwtButton;
  
  /**
  * Data Objects
  **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private swtAlert: SwtAlert;
  private _invalidComms: string;
  private menuAccessId;
  private requestParams = [];
  private parameters;
  private viewLogGrid;
  private logger: Logger = null;

  
  constructor(private commonService: CommonService, private element: ElementRef) {
    //super(element, commonService);
    this.logger = new Logger('Account Currency Period maintenance', this.commonService.httpclient);
  }

  ngOnInit() {
    this.viewLogGrid=<SwtCommonGrid>this.viewLogGridContainer.addChild(SwtCommonGrid);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
  }

  
  onLoad(){
    this.requestParams = [];
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.parameters = JSON.parse(ExternalInterface.call('eval', 'params'));
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    errorLocation = 10;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 20;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "accountPeriod.do?";
    this.actionMethod = 'method=displayViewLog';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['reference'] = this.parameters[0].reference;
    this.requestParams['userId'] = this.parameters[0].userId;
    this.requestParams['logDate'] = this.parameters[0].date; 
    this.requestParams['logTime'] = this.parameters[0].time;
    this.requestParams['ipAddress'] = this.parameters[0].ipAddress;
    //Mantis 6135
    this.requestParams['ccyCode'] = window.opener.instanceElement.ccyCode;
    errorLocation = 30;
    this.requestParams['action'] = this.parameters[0].action;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 30;
    this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "onLoad", errorLocation);
    }
  }



  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      errorLocation = 10;
      //this.dataExport.enabled = true;

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {

        if (!this.jsonReader.isDataBuilding()) {
          const obj = { columns: this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodViewLogGrid.metadata.columns };
          errorLocation = 20;
          this.viewLogGrid.CustomGrid(obj);
          errorLocation = 30;
          var gridRows = this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodViewLogGrid.rows;
          errorLocation = 40;
          if (gridRows.size > 0) {
            this.viewLogGrid.gridData = gridRows;
            this.viewLogGrid.setRowSize = this.jsonReader.getRowSize();
            errorLocation = 50;
            this.viewLogGrid.refresh();
            //this.dataExport.enabled = true;
          }
          else {
            this.viewLogGrid.gridData = { size: 0, row: [] };
            //this.dataExport.enabled = false;
          }
          this.prevRecievedJSON = this.lastRecievedJSON;
        }
      }
    } else {
      if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }

        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "inputDataResult", errorLocation);
    }
  }

  closeHandler() {
    window.close();
  }

startOfComms(): void {
 this.loadingImage.setVisible(true);
}

/**
 * Part of a callback function to all for control of the loading swf from the HTTPComms Object
 */
endOfComms(): void {
  this.loadingImage.setVisible(false);
}


/**                                                                                                                  
 * If a fault occurs with the connection with the server then display the lost connection label                      
 * @param event:FaultEvent                                                                                           
 **/
private inputDataFault(event): void {
  this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  this.swtAlert.show("fault " + this._invalidComms);
}

}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: MaintenanceEventDetailsViewLog}
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [MaintenanceEventDetailsViewLog],
  entryComponents: []
})
export class MaintenanceEventDetailsViewLogModule {
 }