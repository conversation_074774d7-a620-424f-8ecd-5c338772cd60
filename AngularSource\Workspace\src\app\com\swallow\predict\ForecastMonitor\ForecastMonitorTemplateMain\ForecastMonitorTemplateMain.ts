
import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService, ExternalInterface, HashMap,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas, SwtCommonGrid,
  SwtLoadingImage, SwtModule, SwtUtil, SwtToolBoxModule, SwtLabel, SwtTextInput, SwtComboBox, SwtCheckBox, StringUtils,
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";
declare let instanceElement: any;
@Component({
  selector: 'app-forecast-monitor-template-main',
  templateUrl: './ForecastMonitorTemplateMain.html',
  styleUrls: ['./ForecastMonitorTemplateMain.css']
})
export class ForecastMonitorTemplateMain extends  SwtModule {

  @ViewChild('forecastMonitorTemplateMainCanvas') forecastMonitorTemplateMainCanvas: SwtCanvas;

  /********SwtButton*********************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('cpyFromButton') cpyFromButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('imgUpButton') imgUpButton: SwtButton;
  @ViewChild('imgDownButton') imgDownButton: SwtButton;
  /*****Labels*******/
  @ViewChild('lblTempId') lblTempId: SwtLabel;
  @ViewChild('templateNameLabel') templateNameLabel: SwtLabel;
  @ViewChild('lblUserId') lblUserId: SwtLabel;
  @ViewChild('publicLabel') publicLabel: SwtLabel;
  /****SwtTextInput***********/
  @ViewChild('txtTemplateName') txtTemplateName: SwtTextInput;
  @ViewChild('txtTemplateId') txtTemplateId: SwtTextInput;
  /****Combo***********/
  @ViewChild('cbUserId') cbUserId: SwtComboBox;
  /*********Checkbox********/
  @ViewChild('chbPublic') chbPublic: SwtCheckBox;

  /*********LodingImage*************/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  /* Data Objects
  **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private saveData = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;
  private swtAlert: SwtAlert;
  private forecastMonitorTemplateMainGrid: SwtCommonGrid;
  public menuAccessId: string = null;
  private  templateId:string=null;
  private  templateName:string=null;
  private isPublic:string=null;
  private selectedUserId: string = null;
  private screenName: string = null;
  private callFrom: string = null;
  private copyFromFlag:boolean=false;
  private copiedTemplateId:string=null;
  private copiedUserId:string=null;
  private color:string=null;
  private closeWindow:boolean=false;
  private  gridJSONList: any;
  private ordinalPos: string=null;
  private  rowId: number=0;
  private columnId: string=null;
  private displayName: string=null;
  private description: string=null;
  private type: string=null;
  private modify: string=null;
  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  public static ngOnDestroy(): any {
    instanceElement = null;
  }
  ngOnInit(): void {
    instanceElement = this;
    this.lblTempId.text = SwtUtil.getPredictMessage('label.forecastMonitorTemplateAdd.templateId', null);
    this.txtTemplateId.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAdd.templateId', null);
    this.lblUserId.text = SwtUtil.getPredictMessage('label.forecastMonitorTemplateAdd.userId', null);
    this.cbUserId.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAdd.userId', null);
    this.templateNameLabel.text = SwtUtil.getPredictMessage('label.forecastMonitorTemplateAdd.templateName', null);
    this.txtTemplateName.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAdd.templateName', null);
    this.publicLabel.text = SwtUtil.getPredictMessage('label.forecastMonitorTemplateAdd.public', null);
    this.chbPublic.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAdd.public', null);
    this.imgUpButton.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAdd.moveUp', null);
    this.imgDownButton.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAdd.moveDown', null);
    this.cpyFromButton.label = SwtUtil.getPredictMessage('button.cpyFrom', null);
    this.cpyFromButton.toolTip = SwtUtil.getPredictMessage('button.cpyFrom', null);
    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('button.add', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('button.change', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('button.delete', null);
    this.saveButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.saveButton.toolTip = SwtUtil.getPredictMessage('button.close', null);
  }
  onLoad() {
    this.forecastMonitorTemplateMainGrid = <SwtCommonGrid> this.forecastMonitorTemplateMainCanvas.addChild(SwtCommonGrid);

    try {
      this.selectedUserId=ExternalInterface.call('eval', 'userId');
      this.templateId=ExternalInterface.call('eval', 'templateId');
      this.templateName=ExternalInterface.call('eval', 'templateName');
      this.isPublic=ExternalInterface.call('eval', 'isPublic');
      this.screenName=ExternalInterface.call('eval', 'screenName');
      this.callFrom=ExternalInterface.call('eval', 'callFrom');
      this.actionPath = 'forecastMonitorTemplate.do?';

      if (this.screenName != null && this.screenName == "changeScreen")
      {
        //Then declare the action method:
        this.actionMethod="method=displayChangeMonitorTemplate&loadFlex=true";
        this.actionMethod=this.actionMethod + "&templateId=" + this.templateId;
        this.actionMethod=this.actionMethod + "&templateName=" + this.templateName;
        this.actionMethod=this.actionMethod + "&userId=" + this.selectedUserId;
        this.actionMethod=this.actionMethod + "&isPublic=" + this.isPublic;

      }
      else
      {
        this.lblTempId.text=this.lblTempId.text + " *";
        this.lblUserId.text=this.lblUserId.text + " *";
        //Then declare the action method:
        this.actionMethod="method=displayAddMonitorTemplate&loadFlex=true";

      }

      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url =  this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.forecastMonitorTemplateMainGrid.onRowClick = (event) => {
        this.cellLogic(event);
      };

    } catch (e) {
      console.log('errr', e);
    }
  }
  /**
   * copyResult
   * @param event:ResultEvent
   * Refresh the grid with copid details
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   */
copyResult(event):void
  {
    //get the received xml
    this.lastRecievedJSON = event;
    // Parse result json
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    this.imgDownButton.enabled=false;
    this.imgUpButton.enabled=false;
    this.forecastMonitorTemplateMainGrid.gridData=this.jsonReader.getGridData();
    this.gridJSONList= this.jsonReader.getGridData();
  }

  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  public inputDataResult(data): void {

    try {
      if (this.inputData && this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (this.jsonReader.getRequestReplyStatus()) {

          if (!this.jsonReader.isDataBuilding()) {
            this.cbUserId.setComboData(this.jsonReader.getSelects(), true);
            this.txtTemplateId.text= this.jsonReader.getScreenAttributes()["selectedTemplateId"];
            this.txtTemplateName.text = this.jsonReader.getScreenAttributes()["selectedTemplateName"];
            this.changeHighLightBorder();

            if (this.screenName != null && this.screenName == "changeScreen")
            {
              // Disable values for change screen
              this.txtTemplateId.enabled=false;
              this.cbUserId.enabled=false;
              this.chbPublic.selected=!(this.isPublic == "No");

            }
            const obj = {columns: this.jsonReader.getColumnData()};
            this.forecastMonitorTemplateMainGrid.CustomGrid(obj);
            if (this.jsonReader.getGridData()) {
              this.forecastMonitorTemplateMainGrid.gridData = this.jsonReader.getGridData();
              this.gridJSONList= this.jsonReader.getGridData();
              this.forecastMonitorTemplateMainGrid.rowColorFunction = ( dataContext, dataIndex, color ) => {
                return this.drawRowBackground( dataContext, dataIndex, color );
              };
              this.forecastMonitorTemplateMainGrid.setRowSize = this.jsonReader.getRowSize();
              this.deleteButton.enabled = false;
              this.changeButton.enabled = false;
              this.imgDownButton.enabled=false;
              this.imgUpButton.enabled=false;
            } else {
              this.forecastMonitorTemplateMainGrid.dataProvider = [];
              this.forecastMonitorTemplateMainGrid.selectedIndex = -1;
            }
            if (this.cbUserId.selectedLabel == "*DEFAULT*"|| this.txtTemplateId.text == "*DEFAULT*")
            {
              this.chbPublic.enabled=false;
              this.chbPublic.selected=true;
            }
            else
            {
              this.chbPublic.enabled=true;
            }

          }
          this.prevRecievedJSON = this.lastRecievedJSON;

        } else {
          this.swtAlert.error('Generic exception error');
        }

        // }

      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }
  public drawRowBackground( dataContext, dataIndex, color ): string {

    let rColor: string;
    try {
      let colorFlag: string;
      colorFlag = dataContext.slickgrid_rowcontent.columnno.color;
      if (colorFlag == "grey") {
        rColor = "#C9C9C9";
      } else if(colorFlag == "red") {
        rColor = "#FA8C8C";
      } else {

      }
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }
  /**
   * close the window from the close button
   **/
   closeHandler():void
  {
    this.swtAlert.question(SwtUtil.getPredictMessage('alert.forecasttemplate.savetemplate', null), "Confirm", Alert.YES | Alert.NO,null, this.saveChanges.bind(this), null); //default button flag
  }

  /**
   * save the changes
   **/
  saveChanges(event):void
  {
    let saveOrdinalPos = [];
    let saveDisplayName = [];
    if (event.detail == Alert.YES) {
      let isExistFlag:String=ExternalInterface.call("checkTemplateExists", this.txtTemplateId.text.toString(), this.cbUserId.selectedLabel);

      if (StringUtils.trim(this.txtTemplateId.text.toString()) != "" && StringUtils.trim(this.txtTemplateName.text.toString()) != "")
      {

        if (this.screenName == "changeScreen" || isExistFlag != "true")
        {
          this.saveData.cbStart=this.startOfComms.bind(this);
          this.saveData.cbStop=this.endOfComms.bind(this);
          //result event
          this.saveData.cbResult=(event) => {
            this.saveDataResult(event);
          };
          this.saveData.cbFault=this.inputDataFault.bind(this);
          this.saveData.encodeURL=false;
          this.actionPath="forecastMonitorTemplate.do?method=";
          if (this.screenName == "changeScreen") {
            this.actionMethod="updateTemplates";
          }
          else {
            this.actionMethod="saveTemplates";
          }

          this.requestParams["templateId"]=StringUtils.trim(this.txtTemplateId.text.toString());
          this.requestParams["templateName"]=StringUtils.trim(this.txtTemplateName.text.toString());
          this.requestParams["userId"]=this.cbUserId.selectedLabel;
          if (this.chbPublic.selected == true)
            this.requestParams["isPublic"]="Y";
          else
            this.requestParams["isPublic"]="N";


          if (this.copyFromFlag)
          {
            this.requestParams["copyFromFlag"]="true";
            this.requestParams["copiedTemplateId"]=this.copiedTemplateId;
            this.requestParams["copiedUserId"]=this.copiedUserId;
          }
          if(this.forecastMonitorTemplateMainGrid.dataProvider.length >0) {
            for (let i=0; i < this.forecastMonitorTemplateMainGrid.dataProvider.length ; i++)
            {
              saveOrdinalPos[i]=this.forecastMonitorTemplateMainGrid.dataProvider[i].rowid;
              saveDisplayName[i]=this.forecastMonitorTemplateMainGrid.dataProvider[i].displayname;

            }
          }

          this.requestParams["saveOrdinalPos"]=saveOrdinalPos;
          this.requestParams["saveDisplayName"]=saveDisplayName;
          //Then apply them to the url member of the HTTPComms object:
          this.saveData.url=this.baseURL +this.actionPath + this.actionMethod;

          //Make initial request
          this.saveData.send(this.requestParams);

        }
        else
        {
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecasttemplate.templateexist', null))
        }
      }
      else
      {
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecasttemplate.mandatory', null));

        if (this.txtTemplateId.text == "")
        {
          this.txtTemplateId.setFocus();
        }
        else if (this.txtTemplateName.text == "")
        {
          this.txtTemplateName.setFocus();
        }

      }
    }
    else
    {
      ExternalInterface.call("clearSessionInstance");
      ExternalInterface.call("close");

    }

  }
  /**
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   * */
  saveDataResult(event):void
  {
    let lastReceivedJSON = event;
    this.imgDownButton.enabled=false;
    // disable the upButton
    this.imgUpButton.enabled=false;
    // End : Code added by Vivekanandan A for issue identified in 1053 Beta 4 on 23-09-2011
    if (this.callFrom != null && this.callFrom != "")
    {
      ExternalInterface.call("clearSessionInstance");
      ExternalInterface.call("refreshMonitor");
    }
    else {
      this.jsonReader.setInputJSON(lastReceivedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        ExternalInterface.call("clearSessionInstance");
        if (window.opener && window.opener.instanceElement) {
          window.opener.instanceElement.refreshdetails();
      }
        window.close();
    }
        //ExternalInterface.call("refreshParent");
      else
      {
        if (this.lastRecievedJSON.hasOwnProperty("requestthis.reply"))
        {
          //get the attribute for closewindow  flag
          if (this.lastRecievedJSON.requestthis.reply.closewindow == "true")
          {
            this.closeWindow=true;
          }
        }

        this.swtAlert.warning(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error", Alert.OK, this, null);
      }
    }
  }
  /**
   * Method to disable or enable buttons
   * @param status:Boolean
   * @return
   */
  disableOrEnableButtons(status: boolean):void
  {
    if (status)
    {
      this.changeButton.enabled=true;
      this.deleteButton.enabled=(this.color != "red");

    }
    else
    {
      this.changeButton.enabled=false;
      this.deleteButton.enabled=false;
    }
  }
   changeHighLightBorder():void {
    this.txtTemplateId.required = (this.txtTemplateId.text=="");
    this.txtTemplateName.required = (this.txtTemplateName.text=="");
  }
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  inputDataFault(): void {
    this.swtAlert.error('Generic exception error');

  }
   moveRecord(buttonPressed: string):void {
    let gridJSON: any;
    let json: any;
    let selectedIndex: number=this.forecastMonitorTemplateMainGrid.selectedIndex;
    if (buttonPressed == "imgUpButton") {
      json= this.gridJSONList.row[selectedIndex];
      this.gridJSONList.row[selectedIndex]=this.gridJSONList.row[selectedIndex - 1];
      this.gridJSONList.row[selectedIndex].rowid.content = this.forecastMonitorTemplateMainGrid.dataProvider[selectedIndex].rowid;
      this.gridJSONList.row[selectedIndex - 1] = json;
      this.gridJSONList.row[selectedIndex -1].rowid.content = this.forecastMonitorTemplateMainGrid.dataProvider[selectedIndex -1].rowid;
      gridJSON = this.gridJSONList;
      this.forecastMonitorTemplateMainGrid.gridData = gridJSON;
      this.forecastMonitorTemplateMainGrid.selectedIndex = selectedIndex - 1;
    } else {
      json=this.gridJSONList.row[selectedIndex];
      this.gridJSONList.row[selectedIndex]=this.gridJSONList.row[selectedIndex + 1];
      this.gridJSONList.row[selectedIndex].rowid.content = this.forecastMonitorTemplateMainGrid.dataProvider[selectedIndex].rowid;
      this.gridJSONList.row[selectedIndex + 1]=json;
      this.gridJSONList.row[selectedIndex +1].rowid.content = this.forecastMonitorTemplateMainGrid.dataProvider[selectedIndex +1].rowid;

      gridJSON= this.gridJSONList;
      this.forecastMonitorTemplateMainGrid.gridData= gridJSON;// new XMLList(gridXML);
      this.forecastMonitorTemplateMainGrid.selectedIndex = selectedIndex + 1;
    }
    this.cellClickEventHandler(event);

  }
  /**
   * cellLogic
   * @param e:CellEvent
   * When item click on the datagrd is method will be called
   * @param e:CellEvent
   **/
   cellLogic(e:Event):void
  {
    if (this.forecastMonitorTemplateMainGrid.selectedIndex > -1)
    {
      this.ordinalPos=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].ordinalpos;
      this.rowId=parseInt(this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].rowid);
      this.columnId=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].columnno;
      this.displayName=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].displayname;
      this.description=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].description;
      this.type=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].type;
      this.modify=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].modify;
      this.color=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].slickgrid_rowcontent.columnno.color;
      if (this.color != "grey")
        this.disableOrEnableButtons(true);
      else
        this.disableOrEnableButtons(false);
    }
    else
    {
      this.disableOrEnableButtons(false);
    }

   this.cellClickEventHandler(e);

  }

  /**
   * cellClickEventHandler
   *
   * @param event: Event
   *
   * This method is used to maintain the up/down button status when a row is clicked
   */
 cellClickEventHandler(event:Event):void
  {
    //local variables
    let selectedIndex: number=0;
    let count: number=0;
    let currentSelectedItem: any;
    let prevItem: any;
    let nextItem: any;


    if (this.forecastMonitorTemplateMainGrid.selectedIndices.length == 0 || this.forecastMonitorTemplateMainGrid.isFiltered)
    {
      this.imgUpButton.enabled=false;
      this.imgDownButton.enabled=false;
    }
    else if (this.forecastMonitorTemplateMainGrid.selectable)
    {
      selectedIndex=this.forecastMonitorTemplateMainGrid.selectedIndex;
      count=this.forecastMonitorTemplateMainGrid.dataProvider.length;
      currentSelectedItem=this.forecastMonitorTemplateMainGrid.dataProvider[selectedIndex];
      if (currentSelectedItem && currentSelectedItem.type != "Fixed")
      {
        if ((selectedIndex + 1) < count)
        {
          nextItem=this.forecastMonitorTemplateMainGrid.dataProvider[selectedIndex + 1];
        }
        if ((selectedIndex - 1) >= 0)
        {
          prevItem=this.forecastMonitorTemplateMainGrid.dataProvider[selectedIndex - 1];
        }
          
        this.imgUpButton.enabled=(prevItem && prevItem.type != "Fixed");
        this.imgDownButton.enabled=(nextItem && nextItem.type != "Fixed");
      }
      else
      {
        this.imgDownButton.enabled=false;
        this.imgUpButton.enabled=false;
      }
    }

  }


  doHelp() : void {
    ExternalInterface.call('help');
  }

  /**
   * loadCopyfrom()
   *
   * This method is used to open Copy from Forecast screen
   */
 loadCopyfrom():void
  {
    this.actionMethod="loadCopyFrom";
    if (this.screenName == "changeScreen")
    {
      this.actionMethod= this.actionMethod + "&templateId=" +this.templateId;
    }
    this.actionMethod=this.actionMethod + "&userId=" + this.cbUserId.selectedLabel;
    ExternalInterface.call("openCopyFromWindow",this.actionMethod);

  }
  /**
   * reloadCopy()
   * Method to Reload the screem with the copied template id
   * @param selectedUserId
   * @param templateId
   **/
 reloadCopy(selectedUserId:String, templateId:String):void
  {
    this.actionMethod="method=displayCopyMonitorTemplate&loadFlex=true";
    this.actionMethod=this.actionMethod + "&templateId=" + templateId;
    this.actionMethod=this.actionMethod + "&userId=" + selectedUserId;
    this.actionMethod=this.actionMethod + "&actualUserId=" + this.selectedUserId;
    this.actionMethod=this.actionMethod + "&screen=copyFrom";
    this.copyFromFlag=true;
    this.inputData.cbResult= (event) => {
      this.copyResult(event);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    //Make initial request
    this.inputData.send(this.requestParams);
  }


  /**
   * addClickHandler()
   *
   * This method is used to open Add forecast detail screen
   */
  addClickHandler():void
  {
    if (StringUtils.trim(this.txtTemplateId.text.toString()) != "" && StringUtils.trim(this.txtTemplateName.text.toString()) != "")
    {
      let isExistFlag:String=ExternalInterface.call("checkTemplateExists", this.txtTemplateId.text.toString(), this.cbUserId.selectedLabel);
      if (this.screenName == "changeScreen" || isExistFlag != "true")
      {
        /* Url to load Add template screen */
        this.actionMethod="addForecastDetails&templateId=" + this.txtTemplateId.text;
        this.actionMethod= this.actionMethod + "&templateName=" + this.txtTemplateName.text;
        this.actionMethod= this.actionMethod + "&userId=" + this.cbUserId.selectedLabel;
        this.actionMethod=this.actionMethod + "&addClick=" + true;
        ExternalInterface.call("openChildWindow", this.actionMethod);
      }
      else
      {
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecasttemplate.templateexist', null));
      }
    }
    else
    {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecasttemplate.mandatory', null));

      if (this.txtTemplateId.text == "")
      {
        this.txtTemplateId.setFocus();
      }
      else if (this.txtTemplateName.text == "")
      {
        this.txtTemplateName.setFocus();
      }


    }
  }
  /**
   * changeClickHandler()
   *
   * This method is used to open Change forecast detail screen
   */
changeClickHandler():void
  {
    if (StringUtils.trim(this.txtTemplateId.text.toString()) != "" && StringUtils.trim(this.txtTemplateName.text.toString()) != "") {
      this.actionMethod="changeForecastDetails";
      if (this.modify == "save")
        this.actionMethod=this.actionMethod + "&ordinalPos=" + this.columnId;
      else {
        if (this.displayName == "Total") {
          this.actionMethod=this.actionMethod + "&ordinalPos=" + this.ordinalPos;
        } else {
          this.actionMethod=this.actionMethod + "&ordinalPos=" + this.rowId;
        }
      }
      this.actionMethod=this.actionMethod + "&userId=" + this.cbUserId.selectedLabel;
      this.actionMethod=this.actionMethod + "&columnId=" + this.columnId;
      this.actionMethod=this.actionMethod + "&templateId=" + this.txtTemplateId.text;
      this.actionMethod=this.actionMethod + "&templateName=" + this.txtTemplateName.text;
      this.actionMethod=this.actionMethod + "&shortName=" + this.displayName;
      this.actionMethod=this.actionMethod + "&description=" + escape(escape(this.description));
      this.actionMethod=this.actionMethod + "&type=" + this.type;
      this.actionMethod=this.actionMethod + "&modify=" + this.modify;


      if (this.copyFromFlag)
      {
        this.actionMethod=this.actionMethod + "&copyFromFlag=" + "true";
        this.actionMethod=this.actionMethod + "&copiedTemplateId=" + this.copiedTemplateId;
        this.actionMethod=this.actionMethod + "&copiedUserId=" + this.copiedUserId;
      }
      this.actionMethod=this.actionMethod + "&modify=" + this.modify;
      ExternalInterface.call("openChildWindow", this.actionMethod);
    }
    else
    {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecasttemplate.mandatory', null));

      if (this.txtTemplateId.text == "")
      {
        this.txtTemplateId.setFocus();
      }
      else if (this.txtTemplateName.text == "")
      {
        this.txtTemplateName.setFocus();
      }


    }
  }


  /**
   * deleteHandler()
   *
   * This method is used to delete the selected record
   */
  deleteHandler():void
  {
    this.swtAlert.question(SwtUtil.getPredictMessage('alert.forecasttemplate.columndelete', null), null, Alert.YES | Alert.NO, null, this.removeRecord.bind(this), null);

  }
  /**
   * removeRecord()
   *
   * This method is used to delete the selected record after confirm
   */
  removeRecord(closeEvent):void
  {
    // Condition to chcek user selected yes for deleting
    if (closeEvent.detail == Alert.YES)
    {
      this.requestParams["shortName"] = this.forecastMonitorTemplateMainGrid.selectedItem.displayname.content;
      this.actionMethod="method=deleteForecastColumn";
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      if (this.chbPublic.selected)
        this.isPublic="Yes";
      else
        this.isPublic="No";
      this.forecastMonitorTemplateMainGrid.selectedIndex = -1;
      this.changeButton.enabled = false;
      this.deleteButton.enabled = false;
    }
  }
  /**
   * userChange()
   *
   * This method is used to check and disable the public if user selected to *DEFAULT*
   */
   userChange():void
  {
    if (this.cbUserId.selectedLabel == "*DEFAULT*")
    {
      this.chbPublic.enabled=false;
      this.chbPublic.selected=true;
    }
    else
    {
      this.chbPublic.selected=false;
      this.chbPublic.enabled=true;
    }

  }
  /**
   * reloadMain()
   *
   * This method to refresh detail grid for sub total
   */
  reloadMain():void
  {
    this.actionMethod="method=refreshMonitorTemplate";
    this.inputData.cbResult= (event) => {
      this.refreshDetailGrid(event);
    };
    this.inputData.cbFault= this.inputDataFault;
    this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }
  /**
   * refreshDetailGrid(event:ResultEvent)
   *
   * This method to refresh detail grid
   */
   refreshDetailGrid(event):void
  {
    //get the received xml
    this.lastRecievedJSON=event;
   this.jsonReader.setInputJSON(this.lastRecievedJSON);
    this.imgDownButton.enabled=false;
    this.imgUpButton.enabled=false;
    this.deleteButton.enabled=false;
    this.changeButton.enabled=false;
    this.forecastMonitorTemplateMainGrid.gridData= this.jsonReader.getGridData();
    this.gridJSONList= this.jsonReader.getGridData();
    this.forecastMonitorTemplateMainGrid.setRowSize= this.jsonReader.getRowSize();

  }

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ForecastMonitorTemplateMain }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ForecastMonitorTemplateMain],
  entryComponents: []
})
export class ForecastMonitorTemplateMainModule {}
