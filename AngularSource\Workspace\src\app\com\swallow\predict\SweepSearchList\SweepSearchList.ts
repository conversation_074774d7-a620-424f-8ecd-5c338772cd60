import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, SwtAlert, CommonService, JSONReader, HTTPComms, SwtUtil, ExternalInterface, SwtLabel, SwtComboBox, SwtCanvas, SwtButton, SwtCommonGrid, SwtLoadingImage, Alert, ScreenVersion, SwtPopUpManager, ContextMenuItem, JSONViewer, SwtDateField, Logger, SwtCommonGridPagination, HBox, SwtCheckBox, SwtTextInput } from 'swt-tool-box';
declare var instanceElement: any;

@Component({
  selector: 'app-sweep-search-list',
  templateUrl: './SweepSearchList.html',
  styleUrls: ['./SweepSearchList.css']
})
export class SweepSearchList extends SwtModule implements OnInit {

  /***********SwtLabel***********/
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('acctTypeLabel') acctTypeLabel: SwtLabel;

  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedCurrency') selectedCurrency: SwtLabel;

  /***********SwtComboBox***********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('currencyCombo') currencyCombo: SwtComboBox;

  @ViewChild('acctTypeText') acctTypeText: SwtTextInput;

  /***********SwtCanvas***********/
  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
  @ViewChild('numstepper') numstepper: SwtCommonGridPagination;
  @ViewChild('pageBox') pageBox: HBox;

  
  /***********SwtButton***********/
  @ViewChild("viewButton") viewButton: SwtButton;
  @ViewChild("closeButton") closeButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  //Variable to store the last page no
  public lastNumber: Number = 0;
  private actionPath;
  private actionMethod: string = "";
  private requestParams;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private mainGrid: SwtCommonGrid;
  private defaultEntity;
  private selectedCurr;
  private selectedAcct;
  private defaultAcctType;
  private menuAccessId;
  private _invalidComms: string;
  public moduleId = "Predict";

  private errorLocation = 0;
  private menuAccess = "";
  public screenVersion = new ScreenVersion(this.commonService);
  public lastReceivedJSON;
  private showJsonPopup = null;
  private logger: Logger = null;

  private entityId: string;
  private currencyCode: string;
  private accounttype: string;
  private archiveId: String;
  private amountover: String;
  private amountunder: String;
  private currencyGroup: String;
  private accountId: String;
  private bookCode: String;
  private valueFromDateAsString: String;
  private valueToDateAsString: String;
  private generatedby: String;
  private postcutoff: String;
  private submittedby: String;
  private authorisedby: String;
  private sweeptype: String;
  private msgFormat: String;
  private status: String;
  
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.mainGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.mainGrid.allowMultipleSelection = false;

    this.entityLabel.text = SwtUtil.getPredictMessage('entity.id', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('sweep.currencyGroup', null);    
    this.acctTypeLabel.text = SwtUtil.getPredictMessage('sweepsearch.accType', null);

    this.entityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.currencyCombo.toolTip = SwtUtil.getPredictMessage('tooltip.currencyGroupId', null);

    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('tooltip.viewSweepDisp', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');

    this.entityCombo.enabled = false;
    this.currencyCombo.enabled = false;
    this.acctTypeText.enabled = false;

  }

  onLoad() {
    this.requestParams = [];

    this.loadingImage.setVisible(false);

    if (this.menuAccess) {
      if (this.menuAccess !== "") {
        this.menuAccessId = Number(this.menuAccess);
      }
    }
    // set version number
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };

    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "sweepsearch.do?";
    this.actionMethod = 'method=searchAngular';
      this.entityId = ExternalInterface.call("eval", "entityId");
      this.requestParams["entityId"] = this.entityId;
  
      this.amountover = ExternalInterface.call("eval", "amountover");
      this.requestParams["amountover"] = this.amountover;
  
      this.amountunder = ExternalInterface.call("eval", "amountunder");
      this.requestParams["amountunder"] = this.amountunder;
  
      this.currencyCode = ExternalInterface.call("eval", "currencyCode");
      this.requestParams["currencyCode"] = this.currencyCode;
  
      this.currencyGroup = ExternalInterface.call("eval", "currencyGroup");
      this.requestParams["currencyGroup"] = this.currencyGroup;
  
      this.accountId = ExternalInterface.call("eval", "accountId");
      this.requestParams["accountId"] = this.accountId;
  
      this.bookCode = ExternalInterface.call("eval", "bookCode");
      this.requestParams["bookCode"] = this.bookCode;
  
      this.valueFromDateAsString = ExternalInterface.call("eval", "valueFromDateAsString");
      this.requestParams["valueFromDateAsString"] = this.valueFromDateAsString;
  
      this.valueToDateAsString = ExternalInterface.call("eval", "valueToDateAsString");
      this.requestParams["valueToDateAsString"] = this.valueToDateAsString;
  
      this.generatedby = ExternalInterface.call("eval", "generatedby");
      this.requestParams["generatedby"] = this.generatedby;
  
      this.postcutoff = ExternalInterface.call("eval", "postcutoff");
      this.requestParams["postcutoff"] = this.postcutoff;
  
      this.submittedby = ExternalInterface.call("eval", "submittedby");
      this.requestParams["submittedby"] = this.submittedby;
  
      this.authorisedby = ExternalInterface.call("eval", "authorisedby");
      this.requestParams["authorisedby"] = this.authorisedby;
  
      this.accounttype = ExternalInterface.call("eval", "accounttype");
      this.requestParams["accounttype"] = this.accounttype;
  
      this.sweeptype = ExternalInterface.call("eval", "sweeptype");
      this.requestParams["sweeptype"] = this.sweeptype;
  
      this.status = ExternalInterface.call("eval", "status");
      this.requestParams["status"] = this.status;
  
      this.msgFormat = ExternalInterface.call("eval", "msgFormat");
      this.requestParams["msgFormat"] = this.msgFormat;
      this.archiveId = ExternalInterface.call("eval", "archiveId");
      this.requestParams["archiveId"] = this.archiveId;
      //
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    this.mainGrid.onRowClick = () => {
      this.cellClickEventHandler();
    };
  }

  cellClickEventHandler(): void {
    if (this.mainGrid.selectedIndex >= 0) {
      this.viewButton.enabled = true;
      this.viewButton.buttonMode = true;
    } else {
      this.viewButton.enabled = false;
      this.viewButton.buttonMode = false;
    }
  }


  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.acctTypeText.text = this.accounttype;
        // Checks the inputData and stops the communication
        if (this.inputData.isBusy()) {
          this.inputData.cbStop();
        } else {
          this.lastRecievedJSON = event;
          this.jsonReader.setInputJSON(this.lastRecievedJSON);

          if (this.jsonReader.getRequestReplyStatus()) {
            if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
              errorLocation = 10;
              this.entityCombo.setComboData(this.jsonReader.getSelects());
              this.currencyCombo.setComboData(this.jsonReader.getSelects());

              this.defaultEntity = this.jsonReader.getSingletons().defaultEntity;
              if (this.defaultEntity != undefined) {
                this.entityCombo.selectedLabel = this.defaultEntity;
              }
              this.selectedCurr =  this.currencyGroup;
              if (this.selectedCurr != undefined) {
                this.currencyCombo.selectedLabel = this.selectedCurr;
              }
              this.selectedAcct = this.jsonReader.getSingletons().selectedAcctType;
              if (this.selectedAcct != undefined) {
                this.acctTypeText.text = this.selectedAcct;
              }
              errorLocation = 20;
              this.selectedEntity.text = this.entityCombo.selectedValue;
              this.selectedCurrency.text = this.currencyCombo.selectedValue;

              if (!this.jsonReader.isDataBuilding()) {
                const obj = { columns: this.lastRecievedJSON.SweepSearchList.grid.metadata.columns };
                //Sweep Search grid
                this.mainGrid.CustomGrid(obj);
                var gridRows = this.lastRecievedJSON.SweepSearchList.grid.rows;
                errorLocation = 30;
                if (gridRows.size > 0) {
                  this.mainGrid.gridData = gridRows;              
                }
                else {
                  this.mainGrid.gridData = { size: 0, row: [] };
                }
                this.prevRecievedJSON = this.lastRecievedJSON;
              }
            }
          } else {
            if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
              this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
            }
          }
        }
      } catch (error) {
        SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'SweepSearchList.ts', "inputDataResult", errorLocation);
      }
    }


  openSweepDetail() {
    ExternalInterface.call("buildViewSweepDisplayURL", "view",
      this.mainGrid.selectedItem.sweepId.content,
      this.mainGrid.selectedItem.valueDate.content,
      this.mainGrid.selectedItem.accountIdCr.content,
      this.mainGrid.selectedItem.accountIdDr.content,
      this.mainGrid.selectedItem.OriginalSweepAmtasstring.content,
      this.mainGrid.selectedItem.sweepType.content,
      this.mainGrid.selectedItem.status1.content,
      this.mainGrid.selectedItem.inputUser.content,
      this.mainGrid.selectedItem.SubmitUser.content,
      this.mainGrid.selectedItem.AuthorizedUser.content,
      this.mainGrid.selectedItem.MovementIdCr.content,
      this.mainGrid.selectedItem.MovementIdDr.content,
      this.mainGrid.selectedItem.OriginalSweepAmtasstring.content,
      this.mainGrid.selectedItem.AuthorizeSweepAmtasstring.content,
      this.mainGrid.selectedItem.EntityId.content,
      this.jsonReader.getSingletons().archiveId
    );
  }
  closeHandler() {
    ExternalInterface.call("close");
  }


  doHelp(): void {
    ExternalInterface.call("help");
  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  /**
 * enablePrintButton
 *
 */
  enablePrintButton(value): void {
    this.printButton.enabled = value;
    this.printButton.buttonMode = value;
  }

  /**
  * printPage
  *
  * param event
  *
  * Method to get call the action to get printPage
  */
  printPage(): void {
    let errorLocation = 0;
    try {
      ExternalInterface.call('printPage');

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", errorLocation);
    }
  }

  keyDownEventHandler(event) {

  }

  /** This function is used to display the XML for the monitor selected in the monitor combo
      */
  showJSONSelect(event): void {

    this.showJsonPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastReceivedJSON,
      });
    this.showJsonPopup.width = "700";
    this.showJsonPopup.title = "Last Received JSON";
    this.showJsonPopup.height = "500";
    this.showJsonPopup.enableResize = false;
    this.showJsonPopup.showControls = true;
    this.showJsonPopup.display();
  }
}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: SweepSearchList }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SweepSearchList],
  entryComponents: []
})
export class SweepSearchListModule { }