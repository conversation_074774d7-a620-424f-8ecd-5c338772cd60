<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width="100%" height="100%" paddingBottom="5" paddingTop="5" paddingLeft="5" paddingRight="5">
    <Grid width='100%' height="100%">
      <GridRow width="100%" height="18%">
        <GridItem width="100%" height="100%">
          <SwtCanvas width="100%" height="100%" minWidth="750">
            <HBox width="100%" height="100%">
              <HBox width="100%">
                <SwtLabel id="entityLabel" #entityLabel width="80">
                </SwtLabel>
                <SwtComboBox #entityCombo id="entityCombo" dataLabel="entityList"
                  (change)="changeCombo(entityCombo,entityMoreItemsButton,selectedEntity)">
                </SwtComboBox>
                <SwtButton #entityMoreItemsButton id="entityMoreItemsButton" buttonMode="false" enabled="false"
                  width="22" (click)="multipleListSelect(entityCombo.id, selectedEntity)">
                </SwtButton>
                <SwtLabel id="selectedEntity" #selectedEntity paddingLeft="10" text="" textAlign="left">
                </SwtLabel>
              </HBox>
              <VBox width="50%" height="100%" verticalGap="0">
                <HBox width="100%" height="20%">
                  <HBox width="90%" height="100%">
                    <SwtLabel #dateLabel id="dateLabel" width="82">
                    </SwtLabel>
                    <SwtDateField #startDate id="startDate" width="70" (change)="handleDate()" restrict="0-9/"
                      toolTip="Enter value Date {{dateFormatUpper}}">
                    </SwtDateField>
                  </HBox>
                  <HBox width="10%" height="20%">
                    <div #divColor id="divColor" class="square"></div>
                  </HBox>
                </HBox>
                <HBox width="100%" height="20%">
                  <SwtLabel width=" 172" #applyThresholdLabel></SwtLabel>
                  <SwtCheckBox #applyThresholdCheck id="applyThresholdCheck" (change)="updateData($event)">
                  </SwtCheckBox>
                </HBox>
                <HBox width="100%" height="20%">
                  <SwtLabel width="172" #applyMultiplierLabel></SwtLabel>
                  <SwtCheckBox #applyMultiplierCheck id="applyMultiplierCheck" (change)="updateData($event)">
                  </SwtCheckBox>
                </HBox>
                <HBox width="100%" height="20%">
                  <SwtLabel width="172" #spreadLabel></SwtLabel>
                  <SwtCheckBox #spreadCheck id="spreadCheck" width="150" (change)="updateData($event)">
                  </SwtCheckBox>
                </HBox>
                <HBox width="100%" height="20%">
                  <SwtLabel width="172" #valueVolume>
                  </SwtLabel>
                  <SwtRadioButtonGroup #displayValueGroup id="displayValueGroup" (change)="changeRadioGroup()"
                    align="horizontal">
                    <SwtRadioItem #value id="value" value="N" width="90" groupName="displayValueGroup" selected="true">
                    </SwtRadioItem>
                    <SwtRadioItem #volume id="volume" value="Y" width="90" groupName="displayValueGroup"></SwtRadioItem>
                  </SwtRadioButtonGroup>
                </HBox>
              </VBox>
            </HBox>
          </SwtCanvas>
        </GridItem>
      </GridRow>
      <GridRow width="100%"  height="3%">
        <GridItem paddingTop="5" width="100%" height="100%">
          <SwtTabNavigator #tabs id="tabs" width="100%" height="100%" (onChange)="tabIndexChangeHandler()">
          </SwtTabNavigator>

        </GridItem>
      </GridRow>
      <GridRow width="100%" height="73%">
        <GridItem width="100%" height="100%">
          <SwtCanvas width="100%" height="100%" minWidth="750">
            <AdvancedDataGrid #advancedDataGrid id="advancedDataGrid" width="100%" height="100%"></AdvancedDataGrid>
          </SwtCanvas>
        </GridItem>
      </GridRow>
      <GridRow width="100%" height="6%">
        <GridItem paddingTop="5" width="100%" height="100%">
          <SwtCanvas  id="canvasButtons" width="100%" height="100%" minWidth="750">
            <HBox width="100%">
              <HBox paddingLeft="5" width="76%">
                <SwtButton #refreshButton id="refreshButton" (click)="updateData($event)" [buttonMode]="true">
                </SwtButton>
                <SwtButton #rateButton id="rateButton" (click)="optionsHandler()"></SwtButton>
                <SwtButton #closeButton (click)="closeHandler()" id="closeButton"></SwtButton>
              </HBox>
              <HBox horizontalAlign="right" width="22%">
                <SwtLabel #lastRef id="lastRef" fontWeight="normal"></SwtLabel>
                <SwtLabel id="lastRefTime" #lastRefTime fontWeight="normal"></SwtLabel>

                <SwtLoadingImage #loadingImage></SwtLoadingImage>
                <div>
                  <DataExport #dataExport id="dataExport"></DataExport>
                </div>
                <SwtButton [buttonMode]="true" #printIcon id="printIcon" styleName="printIcon"
                  (click)="printPage($event)">
                </SwtButton>
                <HBox>
                </HBox>
              </HBox>
            </HBox>
          </SwtCanvas>
        </GridItem>
      </GridRow>
    </Grid>
  </VBox>
</SwtModule>
