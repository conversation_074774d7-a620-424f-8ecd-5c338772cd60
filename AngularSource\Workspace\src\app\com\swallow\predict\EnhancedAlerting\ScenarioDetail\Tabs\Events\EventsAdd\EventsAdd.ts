import { Compo<PERSON>, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild } from '@angular/core';
import { RouterModule, Routes } from "@angular/router";
import { CommonService, ExternalInterface, Grid, HTTPComms, JSONReader, SwtAlert, SwtButton, SwtCanvas, SwtCheckBox, SwtComboBox, SwtCommonGrid, SwtLabel, SwtModule, SwtPopUpManager, SwtText, SwtTextInput, SwtToolBoxModule, SwtUtil, SwtRadioButtonGroup, SwtRadioItem, SwtTextArea } from 'swt-tool-box';
import { ShowXML } from "../../ShowXML/ShowXML";
declare var require: any;
const $ = require('jquery');
let convert = require('xml-js');
var prettyData = require('pretty-data');
declare var instanceElement: any;
import moment from "moment";
@Component({
  selector: 'app-events-add',
  templateUrl: './EventsAdd.html',
  styleUrls: ['./EventsAdd.css']
})
export class EventsAdd extends SwtModule implements OnInit {
  @ViewChild('scenarioIdLbl') scenarioIdLbl: SwtLabel;
  @ViewChild('executeWhenLbl') executeWhenLbl: SwtLabel;
  @ViewChild('eventSeqLbl') eventSeqLbl: SwtLabel;
  @ViewChild('eventFacilityLbl') eventFacilityLbl: SwtLabel;
  @ViewChild('selectedEventFacility') selectedEventFacility: SwtLabel;
  @ViewChild('eventFacilityDescLbl') eventFacilityDescLbl: SwtLabel;
  @ViewChild('allowRepLbl') allowRepLbl: SwtLabel;
  @ViewChild('desLbl') desLbl: SwtLabel;
  @ViewChild('parameterIdLbl') parameterIdLbl: SwtLabel;
  @ViewChild('mapFromLbl') mapFromLbl: SwtLabel;
  @ViewChild('valueLbl') valueLbl: SwtLabel;
  @ViewChild('msgLabel') msgLabel: SwtLabel;  
  @ViewChild('infoLbl') infoLbl: SwtLabel;
  @ViewChild('emailFormatLbl') emailFormatLbl: SwtLabel;
  @ViewChild('emailFormatDescLbl') emailFormatDescLbl: SwtLabel;
  @ViewChild('usersLbl') usersLbl: SwtLabel;
  @ViewChild('rolesLbl') rolesLbl: SwtLabel;
  //@ViewChild('emailLbl') emailLbl: SwtLabel;

  @ViewChild('scenarioIdtxt') scenarioIdtxt: SwtTextInput;
  @ViewChild('eventSeqTxt') eventSeqTxt: SwtTextInput;
  @ViewChild('valueTxt') valueTxt: SwtTextInput; 
  @ViewChild('eventFacilityDescTxt') eventFacilityDescTxt: SwtTextInput;

  @ViewChild('valueCombo') valueCombo: SwtComboBox;

  @ViewChild('descTxt') descTxt: SwtText;
  @ViewChild('parameterIdTxt') parameterIdTxt: SwtText;

  @ViewChild('excecuteCombo') excecuteCombo: SwtComboBox;
  @ViewChild('eventFacilityCombo') eventFacilityCombo: SwtComboBox;
  @ViewChild('msgCombo') msgCombo: SwtComboBox; 
  @ViewChild('emailFormatCombo') emailFormatCombo: SwtComboBox;

  @ViewChild('allowRepeatCheck') allowRepeatCheck: SwtCheckBox;

  @ViewChild('subEventCanvas') subEventCanvas: SwtCanvas;
  @ViewChild('emailCanvas') emailCanvas: SwtCanvas;
  @ViewChild('msgCanvas') msgCanvas: SwtCanvas;

  @ViewChild('updateButton') updateButton: SwtButton;
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('showXMLButton') showXMLButton: SwtButton;
  @ViewChild('addFormatButton') addFormatButton: SwtButton;
  @ViewChild('configureButton') configureButton: SwtButton;


  /***********SwtRadioButtonGroup***********/
  @ViewChild('mapFrom') mapFrom: SwtRadioButtonGroup;
  @ViewChild('instAttr') instAttr: SwtRadioItem;
  @ViewChild('literal') literal: SwtRadioItem;
  @ViewChild('ignore') ignore: SwtRadioItem;
  @ViewChild('null') null: SwtRadioItem;

  @ViewChild('msgGrid') msgGrid: Grid
  @ViewChild('emailGrid') emailGrid: Grid
  @ViewChild('subEventGrid') subEventGrid: Grid

  @ViewChild('usersGridContainer') usersGridContainer: SwtCanvas;
  @ViewChild('rolesGridContainer') rolesGridContainer: SwtCanvas;
  //@ViewChild('otherEmailGridContainer') otherEmailGridContainer: SwtCanvas;

  @ViewChild('infoText') infoText: SwtLabel; 

  private swtalert: SwtAlert;
  private subEventsGrid: SwtCommonGrid;
  public win : any;
  /**
   * Communication Objects
   */
  private inputData: HTTPComms = new HTTPComms(this.commonService);
  private requestParams = [];
  public baseURL = SwtUtil.getBaseURL();
  private actionPath = "";
  private actionMethod = "";
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public xmlDocument: any;
  public xmlAsString: string;
  public queryColumns=[];
  public txtQueryColumns=[];
  public nbrQueryColumns=[];
  public dateQueryColumns=[];
  public copyData=[];
  public nbrQueryColsCopy=[];
  private emailGridRows=[];
  private eventXml = "";
  private scenarioId: string = null;
  private msgComboList;
  private menuAccessId;
  private methodName;
  private savedMsgComboLabel;
  public usersMainGrid: SwtCommonGrid;
  public rolesMainGrid: SwtCommonGrid;
  //public otherEmailMainGrid: SwtCommonGrid;
  public selectedUsers =[];
  public selectedRoles =[];
  //public selectedOtherEmails =[];

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
    window["Main"] = this;
  }

  ngOnInit() {
    instanceElement = this;
    this.usersMainGrid = <SwtCommonGrid>this.usersGridContainer.addChild(SwtCommonGrid);
    this.rolesMainGrid = <SwtCommonGrid>this.rolesGridContainer.addChild(SwtCommonGrid);
    //this.otherEmailMainGrid = <SwtCommonGrid>this.otherEmailGridContainer.addChild(SwtCommonGrid);
    this.txtQueryColumns=[];
    this.nbrQueryColumns=[];
    this.dateQueryColumns=[];
    this.subEventsGrid = <SwtCommonGrid>this.subEventCanvas.addChild(SwtCommonGrid);
    this.subEventsGrid.editable=true;
    this.msgCombo.required=true;
    let paramsEventsFromParent: any;
    if (window.opener && window.opener.instanceElement2) {
      paramsEventsFromParent = window.opener.instanceElement2.sendEventDataToSub();
      this.methodName=paramsEventsFromParent.operation;
      this.queryColumns = paramsEventsFromParent.listMapFrom;
      this.txtQueryColumns = paramsEventsFromParent.listTxtMapFrom;
      this.nbrQueryColumns = paramsEventsFromParent.listNbrMapFrom;
      this.dateQueryColumns = paramsEventsFromParent.listDateMapFrom;
      if(this.queryColumns){
      this.copyData = $.extend(true, [], this.queryColumns);
      this.copyData.splice(0, 1);
      //add instance ID to queryColumns array
      this.copyData.unshift({type: "", value: '', selected: 0, content: ""},
      { type: "", value: this.queryColumns.length, selected: 0, content: '"INSTANCE_ID"' });
      }

      if(this.nbrQueryColumns){
        this.nbrQueryColsCopy = $.extend(true, [], this.nbrQueryColumns);
        this.nbrQueryColsCopy.splice(0, 1);
        //add instance ID to nbrQueryColumns array
        this.nbrQueryColsCopy.unshift({type: "", value: '', selected: 0, content: ""},
        { type: "", value: this.nbrQueryColumns.length, selected: 0, content: '"INSTANCE_ID"' });
      }
      
      this.scenarioIdtxt.text = paramsEventsFromParent.scenarioId;
      this.eventFacilityCombo.dataProvider = paramsEventsFromParent.eventFacilityList.option;
      this.excecuteCombo.dataProvider = paramsEventsFromParent.executeWhenList.option;
      this.subEventsGrid.CustomGrid(paramsEventsFromParent.gridData.metadata);
      this.usersMainGrid.CustomGrid(paramsEventsFromParent.usersMainGridData.metadata);
      this.usersMainGrid.hideHorizontalScrollBar = true;
      this.rolesMainGrid.CustomGrid(paramsEventsFromParent.rolesMainGridData.metadata);
      this.rolesMainGrid.hideHorizontalScrollBar = true;

      let templatesList=  paramsEventsFromParent.emailTemplatesList?paramsEventsFromParent.emailTemplatesList.option:"";
      // to avoid issue when we have only one option
      if (templatesList) {
        if (!templatesList.length)
        templatesList = [templatesList];
          this.emailFormatCombo.setComboData(templatesList);
          this.emailFormatCombo.dataProvider = templatesList;
      }
      this.emailFormatDescLbl.text  = this.emailFormatCombo.selectedValue;

      //this.otherEmailMainGrid.CustomGrid(paramsEventsFromParent.otherEmailMainGridData.metadata);
      this.eventFacilityArray = paramsEventsFromParent.eventFacilityArray;
      this.eventSeqTxt.text = paramsEventsFromParent.eventSequence;
      this.msgComboList=  paramsEventsFromParent.messageFormatsList?paramsEventsFromParent.messageFormatsList.option:"";
      // to avoid issue when we have only one option
      if (this.msgComboList) {
        if (!this.msgComboList.length)
          this.msgComboList = [this.msgComboList];
          this.msgCombo.setComboData(this.msgComboList);
          this.msgCombo.dataProvider = this.msgComboList;
      } else {
        this.msgComboList = [];
      }      
      this.msgLabel.text  = this.msgCombo.selectedValue;
      this.scenarioId= paramsEventsFromParent.scenarioId;
      if(paramsEventsFromParent.parentMethodName=='add'){
        this.updateMsgFormatCombo();
      }
      if(paramsEventsFromParent.operation != "add") {
        //disable event facility ID combo
        this.eventFacilityCombo.enabled=false;
        this.eventFacilityCombo.selectedLabel = paramsEventsFromParent.selectedEventId;
        this.selectedEventFacility.text = this.eventFacilityCombo.selectedValue;
        this.eventFacilityDescTxt.text = paramsEventsFromParent.userDescription?paramsEventsFromParent.userDescription:"";
        this.xmlAsString = paramsEventsFromParent.parameterXML;
        this.excecuteCombo.selectedValue = paramsEventsFromParent.selectedExecuteWhen;
        this.allowRepeatCheck.selected = paramsEventsFromParent.allowRepeat =="Y";

        setTimeout(()=> {
          if (this.eventFacilityCombo.selectedLabel != "SEND_MESSAGE"){
          this.showXMLButton.enabled= true;
          this.showXMLButton.buttonMode= true;
          this.generateGridRows();
          }
        },0)
      }else{
        this.showXMLButton.enabled= false;
        this.showXMLButton.buttonMode= false;
      }
    }



    this.scenarioIdLbl.text = SwtUtil.getPredictMessage('scenario.scenarioId', null);
    this.eventSeqLbl.text = SwtUtil.getPredictMessage('scenario.events.eventSeq', null);
    this.executeWhenLbl.text = SwtUtil.getPredictMessage('scenario.events.executeWhen', null);
    this.allowRepLbl.text = SwtUtil.getPredictMessage('scenario.events.allowRepeat', null);
    this.eventFacilityLbl.text = SwtUtil.getPredictMessage('scenario.events.eventFacility', null)+'*';
    this.eventFacilityDescLbl.text = SwtUtil.getPredictMessage('scenario.events.eventFacilityDesc', null);
    this.valueLbl.text = SwtUtil.getPredictMessage('scenario.events.value', null);
    this.parameterIdLbl.text = SwtUtil.getPredictMessage('scenario.guiHighlight.paramId', null);
    this.desLbl.text = SwtUtil.getPredictMessage('scenario.guiHighlight.decription', null);
    this.mapFromLbl.text = SwtUtil.getPredictMessage('scenario.guiHighlight.mapFrom', null);
    this.infoLbl.text = SwtUtil.getPredictMessage('scenario.events.info', null);
    this.usersLbl.text = SwtUtil.getPredictMessage('scenario.events.usersLbl', null);
    this.rolesLbl.text = SwtUtil.getPredictMessage('scenario.events.rolesLbl', null);
    //this.emailLbl.text = SwtUtil.getPredictMessage('scenario.events.emailLbl', null);
    this.emailFormatLbl.text= SwtUtil.getPredictMessage('scenario.events.emailFormat', null); 
    this.updateButton.label = SwtUtil.getPredictMessage('button.update', null);
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.showXMLButton.label = SwtUtil.getPredictMessage('screen.showXML', null);
    this.configureButton.label= SwtUtil.getPredictMessage('scenario.events.button.configuration', null);
    this.updateButton.toolTip = SwtUtil.getPredictMessage('button.update', null);
    this.okButton.toolTip = SwtUtil.getPredictMessage('button.ok', null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage('button.cancel', null);
    this.showXMLButton.toolTip = SwtUtil.getPredictMessage('screen.showXML', null);
    this.configureButton.toolTip= SwtUtil.getPredictMessage('scenario.events.button.configuration', null);
    //radio group buttons tooltips
    this.instAttr.label = SwtUtil.getPredictMessage('scenario.events.instAttr', null);
    this.literal.label = SwtUtil.getPredictMessage('scenario.events.literal', null);
    this.ignore.label = SwtUtil.getPredictMessage('scenario.events.ignore', null);
    this.null.label = SwtUtil.getPredictMessage('scenario.events.null', null);
    //tooltips part
    this.scenarioIdtxt.toolTip= SwtUtil.getPredictMessage('scenario.events.tooltip.scenarioId', null);
    this.excecuteCombo.toolTip=SwtUtil.getPredictMessage('scenario.events.tooltip.executeWhen', null);
    this.allowRepeatCheck.toolTip=SwtUtil.getPredictMessage('scenario.events.tooltip.allowRepeat', null);
    this.eventSeqTxt.toolTip=SwtUtil.getPredictMessage('scenario.events.tooltip.eventSeq', null);
    this.eventFacilityCombo.toolTip=SwtUtil.getPredictMessage('scenario.events.tooltip.eventFacility', null);
    this.instAttr.toolTip = SwtUtil.getPredictMessage('scenario.events.tooltip.instAttr', null);
    this.literal.toolTip = SwtUtil.getPredictMessage('scenario.events.tooltip.literal', null);
    this.ignore.toolTip = SwtUtil.getPredictMessage('scenario.events.tooltip.ignore', null);
    this.null.toolTip = SwtUtil.getPredictMessage('scenario.events.tooltip.null', null);
    this.valueCombo.toolTip=SwtUtil.getPredictMessage('scenario.events.tooltip.value', null);
    this.msgCombo.toolTip=SwtUtil.getPredictMessage('scenario.events.tooltip.msgCombo', null);
    this.emailFormatCombo.toolTip=SwtUtil.getPredictMessage('scenario.events.tooltip.emailFormatCombo', null);
    this.eventFacilityDescTxt.toolTip= SwtUtil.getPredictMessage('scenario.events.tooltip.eventFacilityDesc', null);

    if((paramsEventsFromParent.operation == "change" || paramsEventsFromParent.operation == "view") && this.eventFacilityCombo.selectedLabel == "SEND_MESSAGE"){
      this.addFormatButton.label = SwtUtil.getPredictMessage('scenario.events.msgFormat', null);
      this.addFormatButton.toolTip = SwtUtil.getPredictMessage('scenario.events.msgFormat', null);
      this.eventFacilityCombo.required = false;
      this.eventFacilityCombo.editable = false;
      this.eventFacilityCombo.enabled=false;  
      this.msgCombo.required=true;
      this.msgCombo.selectedLabel = paramsEventsFromParent.parameterXML;
      this.savedMsgComboLabel=paramsEventsFromParent.parameterXML;
      this.msgLabel.text = this.msgCombo.selectedValue;
      this.openSendMessageView();
    } else if((paramsEventsFromParent.operation == "change" || paramsEventsFromParent.operation == "view") && this.eventFacilityCombo.selectedLabel == "SEND_EMAIL"){
      this.emailGrid.visible=true;
      this.emailGrid.includeInLayout=true;
      this.emailCanvas.visible=true;
      this.emailCanvas.includeInLayout=true;
      this.emailFormatLbl.visible=true;
      this.emailFormatLbl.includeInLayout=true;
      this.emailFormatCombo.visible=true;
      this.emailFormatDescLbl.visible=true;
      this.emailFormatDescLbl.includeInLayout=true;
      this.configureButton.visible=true;
      this.configureButton.includeInLayout=true;
      this.emailFormatDescLbl.text  = this.emailFormatCombo.selectedValue;
      this.configureButton.label = SwtUtil.getPredictMessage('scenario.events.button.configuration', null);
      this.configureButton.toolTip = SwtUtil.getPredictMessage('scenario.events.button.configuration', null); 

      this.msgGrid.visible = false;
      this.msgGrid.includeInLayout = false;
      this.msgCanvas.visible = false;
      this.msgCanvas.includeInLayout = false;
      this.msgCombo.visible = false;
      this.addFormatButton.visible = false;
      this.addFormatButton.includeInLayout = false;
      this.addFormatButton.label = SwtUtil.getPredictMessage('scenario.events.msgFormat', null);
      this.addFormatButton.toolTip = SwtUtil.getPredictMessage('scenario.events.msgFormat', null);     
      this.eventFacilityCombo.required = true;

      this.subEventGrid.visible = false;
      this.subEventGrid.includeInLayout = false;
      this.subEventCanvas.visible = false;
      this.subEventCanvas.includeInLayout = false;
      this.subEventsGrid.visible = false;
      this.subEventsGrid.includeInLayout = false;
      this.parameterIdLbl.visible = false;
      this.parameterIdLbl.includeInLayout = false;
      this.parameterIdTxt.visible = false;
      this.parameterIdTxt.includeInLayout = false;
      this.desLbl.visible = false;
      this.desLbl.includeInLayout = false;
      this.descTxt.visible = false;
      this.descTxt.includeInLayout = false;
      this.mapFromLbl.visible = false;
      this.mapFromLbl.includeInLayout = false;
      this.mapFrom.visible = false;
      this.valueCombo.visible=false;
      this.valueTxt.visible=false;
      this.valueTxt.includeInLayout=false;
      this.updateButton.visible = false;
      this.updateButton.includeInLayout = false;
    }
    else{
      //make value component not visible
      this.valueTxt.visible=false;
      this.valueTxt.includeInLayout=false;
      //make email event components not visible
      this.emailGrid.visible=false;
      this.emailGrid.includeInLayout=false;
      this.emailCanvas.visible=false;
      this.emailCanvas.includeInLayout=false;
      this.emailFormatLbl.visible=false;
      this.emailFormatLbl.includeInLayout=false;
      this.emailFormatCombo.visible=false;
      this.emailFormatDescLbl.visible=false;
      this.emailFormatDescLbl.includeInLayout=false;
      this.configureButton.visible=false;
      this.configureButton.includeInLayout=false;
      this.configureButton.label = SwtUtil.getPredictMessage('scenario.events.button.configuration', null);
      this.configureButton.toolTip = SwtUtil.getPredictMessage('scenario.events.button.configuration', null); 
      //make create message event components not visible
      this.msgGrid.visible = false;
      this.msgGrid.includeInLayout = false;
      this.msgCanvas.visible = false;
      this.msgCanvas.includeInLayout = false;
      this.msgCombo.visible = false;
      this.addFormatButton.visible = false;
      this.addFormatButton.includeInLayout = false;
      this.addFormatButton.label = SwtUtil.getPredictMessage('scenario.events.msgFormat', null);
      this.addFormatButton.toolTip = SwtUtil.getPredictMessage('scenario.events.msgFormat', null);     
      this.eventFacilityCombo.required = true;
    }


  }
  private eventFacilityArray = [];

  onLoad() {
    //disable all component in case of view
    if (this.methodName=='view'){
    this.excecuteCombo.enabled=false;
    this.allowRepeatCheck.enabled=false;
    this.eventFacilityCombo.enabled=false;  
    this.eventFacilityDescTxt.enabled=false;
    this.subEventGrid.enabled=false;
    this.mapFrom.enabled=false;
    this.valueCombo.enabled=false;
    this.addFormatButton.enabled=false;
    this.msgCombo.enabled=false;
    this.okButton.enabled=false;
    this.cancelButton.enabled=true;
    this.configureButton.enabled=false;
    this.emailFormatCombo.enabled=false;
    this.usersMainGrid.enabled=false;
    this.rolesMainGrid.enabled=false;
    }else if (this.methodName=='change'){ 
      this.excecuteCombo.enabled=true;
      this.allowRepeatCheck.enabled=true;
      this.eventFacilityCombo.enabled=false; 
      this.eventFacilityDescTxt.enabled=true; 
      this.subEventGrid.enabled=true;
      this.mapFrom.enabled=true;
      this.valueCombo.enabled=true;
      this.addFormatButton.enabled=true;
      this.msgCombo.enabled=true;
      this.okButton.enabled=true;
      this.cancelButton.enabled=true;
      this.configureButton.enabled=true;
      this.emailFormatCombo.enabled=true;
      this.usersMainGrid.enabled=true;
      this.rolesMainGrid.enabled=true;
    }else{
      this.excecuteCombo.enabled=true;
      this.allowRepeatCheck.enabled=true;
      this.eventFacilityCombo.enabled=true; 
      this.eventFacilityDescTxt.enabled=true; 
      this.subEventGrid.enabled=true;
      this.mapFrom.enabled=false;
      this.valueCombo.enabled=false;
      this.addFormatButton.enabled=true;
      this.msgCombo.enabled=true;
      this.okButton.enabled=true;
      this.cancelButton.enabled=true;
      this.configureButton.enabled=true;
      this.emailFormatCombo.enabled=true;
      this.usersMainGrid.enabled=true;
      this.rolesMainGrid.enabled=true; 
    }
    this.subEventsGrid.onRowClick =()=> {
      this.rowClick();
    };

  }
  rowClick() {
    this.valueTxt.text="";
    let currenctType= this.subEventsGrid.selectedItem.subEvType.content;

    if(this.subEventsGrid.selectedIndex >=0) {
      if(currenctType=="text"){
        this.valueCombo.setComboData(this.txtQueryColumns);
        this.valueCombo.dataProvider =this.txtQueryColumns;
      }else if (currenctType=="number" || currenctType=="integer"){
          this.valueCombo.dataProvider =this.nbrQueryColsCopy;
      }else{
          this.valueCombo.setComboData(this.dateQueryColumns);
          this.valueCombo.dataProvider =this.dateQueryColumns;
      }

      this.parameterIdTxt.text = this.subEventsGrid.selectedItem.subEvId.content;
      this.descTxt.text = this.subEventsGrid.selectedItem.subEvDescription.content;
      if(this.getMapFromValue(this.subEventsGrid.selectedItem.subEvMapFrom.content)=="A"){
      this.valueCombo.selectedLabel = this.subEventsGrid.selectedItem.subEvMapFromValue.content ?this.subEventsGrid.selectedItem.subEvMapFromValue.content : "";
      }else{
      this.valueTxt.text = this.subEventsGrid.selectedItem.subEvMapFromValue.content ?this.subEventsGrid.selectedItem.subEvMapFromValue.content : "";
      }
      this.mapFrom.selectedValue = this.subEventsGrid.selectedItem.subEvMapFrom.content ? this.getMapFromValue(this.subEventsGrid.selectedItem.subEvMapFrom.content) : "A";
      this.changeValueComponent();
      this.updateButton.enabled = true;
      this.mapFrom.enabled = true;
      this.valueCombo.enabled = true;
    } else {
      this.parameterIdTxt.text = "";
      this.descTxt.text = "";
      this.mapFrom.selectedValue = "A";
      this.changeValueComponent();
      this.valueCombo.selectedLabel = "";
      this.updateButton.enabled = false;
      this.mapFrom.enabled = false;
      this.valueCombo.enabled = false;
    }
  }
  changeEventFacility() {
    this.changeComponents();
    this.selectedEventFacility.text = this.eventFacilityCombo.selectedValue;
    this.showXMLButton.enabled = this.checkEmpty(this.eventFacilityCombo)  && this.eventFacilityCombo.selectedLabel != "SEND_MESSAGE" ;
    this.subEventsGrid.gridData = {row: [], size: 0};
    if(this.eventFacilityCombo.selectedLabel) {
      this.requestParams = [];
      this.actionPath = 'scenMaintenance.do?';
      this.actionMethod = 'method=getEventFacilityData';
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.requestParams["selectedEventFacility"] = this.eventFacilityCombo.selectedLabel;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    }
  }

  changeMsgFormat(){
    this.msgLabel.text  = this.msgCombo.selectedValue;
  }

  changeEmailFormat(){
    this.emailFormatDescLbl.text  = this.emailFormatCombo.selectedValue;
  }

  changeValueComponent(){
    if (this.mapFrom.selectedValue == "A") {
      this.valueTxt.visible=false;
      this.valueTxt.includeInLayout=false;
      this.valueCombo.visible=true;
      this.infoText.text= SwtUtil.getPredictMessage('scenario.events.instanceAttribute.info', null);
    }else if (this.mapFrom.selectedValue == "L") {
      const subEvType = this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvType;
      const additionalInfo = this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvAddInfo;
      const regularExp = this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvRegExp;
      this.valueTxt.visible=true;
      this.valueTxt.includeInLayout=true;
      this.valueTxt.editable=true;
      this.valueTxt.enabled=true;
      this.valueCombo.visible=false;
      this.infoText.text= additionalInfo?additionalInfo:"";
    }else{
      this.valueTxt.text="";
      this.valueTxt.visible=true;
      this.valueTxt.includeInLayout=true;
      this.valueTxt.editable=false;
      this.valueTxt.enabled=false;
      this.valueCombo.visible=false;
      this.infoText.text= "";
    }
  }

  changeComponents() {
    //this is added for email facility event
    if (this.eventFacilityCombo.selectedLabel == "SEND_EMAIL") {
      this.msgGrid.visible = false;
      this.msgGrid.includeInLayout = false;
      this.msgCanvas.visible = false;
      this.msgCanvas.includeInLayout = false;
      this.msgCombo.visible = false;
      this.addFormatButton.visible = false;
      this.addFormatButton.includeInLayout = false;

      this.emailGrid.visible=true;
      this.emailGrid.includeInLayout=true;
      this.emailCanvas.visible=true;
      this.emailCanvas.includeInLayout=true;
      this.emailFormatLbl.visible=true;
      this.emailFormatLbl.includeInLayout=true;
      this.emailFormatCombo.visible=true;
      this.emailFormatDescLbl.visible=true;
      this.emailFormatDescLbl.includeInLayout=true;
      this.configureButton.visible=true;
      this.configureButton.includeInLayout=true;
      this.configureButton.label = SwtUtil.getPredictMessage('scenario.events.button.configuration', null);
      this.configureButton.toolTip = SwtUtil.getPredictMessage('scenario.events.button.configuration', null);

      this.subEventGrid.visible = false;
      this.subEventGrid.includeInLayout = false;
      this.subEventCanvas.visible = false;
      this.subEventCanvas.includeInLayout = false;
      this.subEventsGrid.visible = false;
      this.subEventsGrid.includeInLayout = false;
      this.parameterIdLbl.visible = false;
      this.parameterIdLbl.includeInLayout = false;
      this.parameterIdTxt.visible = false;
      this.parameterIdTxt.includeInLayout = false;
      this.desLbl.visible = false;
      this.desLbl.includeInLayout = false;
      this.descTxt.visible = false;
      this.descTxt.includeInLayout = false;
      this.mapFromLbl.visible = false;
      this.mapFromLbl.includeInLayout = false;
      this.mapFrom.visible = false;
      this.valueCombo.visible=false;
      this.valueTxt.visible=false;
      this.valueTxt.includeInLayout=false;
      this.updateButton.visible = false;
      this.updateButton.includeInLayout = false;
    } else if (this.eventFacilityCombo.selectedLabel == "SEND_MESSAGE")  {
      this.openSendMessageView();
    } else {
      this.msgGrid.visible = false;
      this.msgGrid.includeInLayout = false;
      this.msgCanvas.visible = false;
      this.msgCanvas.includeInLayout = false;
      this.msgCombo.visible = false;
      this.addFormatButton.visible = false;
      this.addFormatButton.includeInLayout = false;

      this.emailGrid.visible=false;
      this.emailGrid.includeInLayout=false;
      this.emailCanvas.visible=false;
      this.emailCanvas.includeInLayout=false;
      this.emailFormatLbl.visible=false;
      this.emailFormatLbl.includeInLayout=false;
      this.emailFormatCombo.visible=false;
      this.emailFormatDescLbl.visible=false;
      this.emailFormatDescLbl.includeInLayout=false;
      this.configureButton.visible=false;
      this.configureButton.includeInLayout=false;

      this.subEventGrid.visible = true;
      this.subEventGrid.includeInLayout = true;
      this.subEventCanvas.visible = true;
      this.subEventCanvas.includeInLayout = true;
      this.subEventsGrid.visible = true;
      this.subEventsGrid.includeInLayout = true;
      this.parameterIdLbl.visible = true;
      this.parameterIdLbl.includeInLayout = true;
      this.parameterIdTxt.visible = true;
      this.parameterIdTxt.includeInLayout = true;
      this.desLbl.visible = true;
      this.desLbl.includeInLayout = true;
      this.descTxt.visible = true;
      this.descTxt.includeInLayout = true;
      this.mapFromLbl.visible = true;
      this.mapFromLbl.includeInLayout = true;
      this.mapFrom.visible = true;
      this.valueCombo.visible=true;
      this.valueTxt.visible=false;
      this.valueTxt.includeInLayout=false;
      this.updateButton.visible = true;
      this.updateButton.includeInLayout = true;
    }
  }


    openSendMessageView(){
      this.msgGrid.visible = true;
      this.msgGrid.includeInLayout = true;
      this.msgCanvas.visible = true;
      this.msgCanvas.includeInLayout = true;
      this.msgCombo.visible = true;
      this.addFormatButton.visible = true;
      this.addFormatButton.includeInLayout = true;

      this.emailGrid.visible=false;
      this.emailGrid.includeInLayout=false;
      this.emailCanvas.visible=false;
      this.emailCanvas.includeInLayout=false;
      this.emailFormatLbl.visible=false;
      this.emailFormatLbl.includeInLayout=false;
      this.emailFormatCombo.visible=false;
      this.emailFormatDescLbl.visible=false;
      this.emailFormatDescLbl.includeInLayout=false;
      this.configureButton.visible=false;
      this.configureButton.includeInLayout=false;

      this.subEventGrid.visible = false;
      this.subEventGrid.includeInLayout = false;
      this.subEventCanvas.visible = false;
      this.subEventCanvas.includeInLayout = false;
      this.subEventsGrid.visible = false;
      this.subEventsGrid.includeInLayout = false;
      this.parameterIdLbl.visible = false;
      this.parameterIdLbl.includeInLayout = false;
      this.parameterIdTxt.visible = false;
      this.parameterIdTxt.includeInLayout = false;
      this.desLbl.visible = false;
      this.desLbl.includeInLayout = false;
      this.descTxt.visible = false;
      this.descTxt.includeInLayout = false;
      this.mapFromLbl.visible = false;
      this.mapFromLbl.includeInLayout = false;
      this.mapFrom.visible = false;
      this.valueCombo.visible=false;
      this.valueTxt.visible=false;
      this.valueTxt.includeInLayout=false;
      this.updateButton.visible = false;
      this.updateButton.includeInLayout = false;

    }

  addMsgFormat(){
    ExternalInterface.call("openMsgFormatScreen", "unspecified", this.scenarioId);
  }

  changeMessageFormat(){
    ExternalInterface.call("openMsgFormatScreen", "unspecified");
  }




  inputDataResult(event) {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.eventFacilityCombo.selectedLabel != "SEND_MESSAGE") {
        this.xmlAsString = (this.jsonReader.getSingletons().parameterXML).replace(/#/g, ">");
        this.generateGridRows();
      }
      }

    }

  generateGridRows() {
    if(this.xmlAsString) {
      let options = {
        object: false,
        reversible: false,
        coerce: false,
        sanitize: true,
        trim: true,
        arrayNotation: false,
        alternateTextNode: false,
        compact: true
      };
      let jsonData = convert.xml2js(this.xmlAsString, options);
      //if from add button we need to get it from P_SCENARIO_EVENT_FACILITY else from P_SCENARIO_EVENT_MAPPING
      let data = jsonData.mappedParameters ? jsonData.mappedParameters.parameter : jsonData.requiredParameters.parameter ;
      
          let item: any;
          let item1: any;
          let item2: any;
          if (this.eventFacilityCombo.selectedLabel == "SEND_EMAIL"){
            if (data[0]){
            this.emailFormatCombo.selectedLabel= data[0].templateId && data[0].templateId._cdata && data[0].templateId._cdata!="undefined" ?data[0].templateId._cdata : ""
            this.emailFormatDescLbl.text=this.emailFormatCombo.selectedValue;
          } 
            
            if (data[1]){
              let dataUsers = data[1].users.user;
              if (!dataUsers.length){
               dataUsers= [dataUsers];
             }
              for (let i = 0; i < dataUsers.length; i++) {
             item1 = {
               'userId': {'content': dataUsers[i].userId && dataUsers[i].userId._cdata && dataUsers[i].userId._cdata!="undefined" ?dataUsers[i].userId._cdata : ""},             
               'name': {'content': dataUsers[i].name && dataUsers[i].name._cdata && dataUsers[i].name._cdata!="undefined" ?dataUsers[i].name._cdata : ""},             
   
             };
             this.usersMainGrid.appendRow(item1, true, true);
             this.selectedUsers.push(dataUsers[i].userId._cdata);
           }
         }

            if (data[2]){
            let dataRoles = data[2].roles.role;
            if (!dataRoles.length){
              dataRoles= [dataRoles];
            }
            for (let i = 0; i < dataRoles.length; i++) {

            item = {
              'roleId': {'content': dataRoles[i].roleId && dataRoles[i].roleId._cdata && dataRoles[i].roleId._cdata!="undefined" ?dataRoles[i].roleId._cdata : ""},             
              'name': {'content': dataRoles[i].name && dataRoles[i].name._cdata && dataRoles[i].name._cdata!="undefined" ?dataRoles[i].name._cdata : ""},             
  
            };
            this.rolesMainGrid.appendRow(item, true, true);
            this.selectedRoles.push(dataRoles[i].roleId._cdata);
             }
          } 


         /* let dataEmails = data[2].emails.email;
          
          if (!dataEmails.length){
            dataEmails= [dataEmails];
          } 
          for (let i = 0; i < dataEmails.length; i++) {
            item2 = {
              'emailAddress': {'content': dataEmails[i].emailAddress && dataEmails[i].emailAddress._cdata && dataEmails[i].emailAddress._cdata!="undefined" ?dataEmails[i].emailAddress._cdata : ""},             
              'description': {'content': dataEmails[i].description && dataEmails[i].description._cdata && dataEmails[i].description._cdata!="undefined" ?dataEmails[i].description._cdata : ""},             
  
            };
            this.otherEmailMainGrid.appendRow(item2, true, true);
          }*/
          }else{
          for (let i = 0; i < data.length; i++) {
          item = {
            'subEvReq': {'content': data[i].isMandatory && data[i].isMandatory._cdata && data[i].isMandatory._cdata!="undefined" ?data[i].isMandatory._cdata : ""},
            'subEvId': {'content': data[i].name && data[i].name._cdata!="undefined" ? data[i].name._cdata: ""},
            'subEvDescription': {'content': data[i].description  && data[i].description._cdata && data[i].description._cdata!="undefined" ? data[i].description._cdata : ""},
            'subEvType': {'content': data[i].data_type && data[i].data_type._cdata && data[i].data_type._cdata!="undefined" ? data[i].data_type._cdata : ""},
            'subEvMapFrom': {'content': (data[i].useType && data[i].useType._cdata && data[i].useType._cdata!="undefined" ? data[i].useType._cdata : "")},
            'subEvMapFromValue': {'content': (data[i].value && data[i].value._cdata && data[i].value._cdata!="undefined" ? data[i].value._cdata : "")},
            'subEvAddInfo': {'content': (data[i].additional_infomation && data[i].additional_infomation._cdata && data[i].additional_infomation._cdata!="undefined" ? data[i].additional_infomation._cdata : "")},
            'subEvRegExp': {'content': (data[i].regular_expression && data[i].regular_expression._cdata && data[i].regular_expression._cdata!="undefined" ? data[i].regular_expression._cdata : "")},
            'subEvRegExpMsg': {'content': (data[i].regular_expression_msg && data[i].regular_expression_msg._cdata && data[i].regular_expression_msg._cdata!="undefined" ? data[i].regular_expression_msg._cdata : "")}

          };
          item.validation={ 'length': {'content': (data[i].length && data[i].length._cdata && data[i].length._cdata!="undefined" ? data[i].length._cdata : "")},
          'maximum': {'content': (data[i].maximum && data[i].maximum._cdata && data[i].maximum._cdata!="undefined" ? data[i].maximum._cdata : "")},
          'minimum': {'content': (data[i].minimum && data[i].minimum._cdata && data[i].minimum._cdata!="undefined" ? data[i].minimum._cdata : "")}}
          this.subEventsGrid.appendRow(item, true, true);

        }
      }
    }
    //this.subEventsGrid.scrollToIndex(0);
    // this.subEventsGrid.selectedIndex=-1;

  }
  inputDataFault() {
    this.swtalert.error(SwtUtil.getPredictMessage('alert.generic_exception'));
  }
  updateHandle() {

    //populate map from value inside the grid
    if (this.mapFrom.selectedValue == "A") {
    this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvMapFrom = this.getMapFromLabel(this.mapFrom.selectedValue);
    this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.subEvMapFrom.content = this.getMapFromLabel(this.mapFrom.selectedValue);
    this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvMapFromValue = this.valueCombo.selectedLabel;
    this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.subEvMapFromValue.content = this.valueCombo.selectedLabel;
    }else{
      if(this.mapFrom.selectedValue == "L"){
        const regularExp= new RegExp(this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvRegExp);
        const regularExpMsg= this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvRegExpMsg;

        if(this.checkRegularExpression(regularExp, this.valueTxt.text)){
          this.swtalert.error(regularExpMsg);
          this.valueTxt.text="";     
          return;
        }else{
        const subEvType = this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvType;
        const validation= this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.validation;
        const maxChars = validation.length?validation.length.content:"";    
        const maximum = validation.maximum?validation.maximum.content:"";
        const minimum = validation.minimum?validation.minimum.content:"";
        if ('date'== subEvType ||'datetime' == subEvType){

          let dateWithTime = moment(this.valueTxt.text, 'YYYY-MM-DD HH:mm:ss', true );
          let dateWithoutTime = moment(this.valueTxt.text, 'YYYY-MM-DD', true );
          if(!dateWithTime.isValid() && !dateWithoutTime.isValid()) {
            this.swtalert.error(SwtUtil.getPredictMessage('Please enter a valid Date'));
            return;
          }

        }else if('number'==subEvType){
         if(isNaN(this.valueTxt.text) &&  isNaN(parseFloat(this.valueTxt.text))){
          this.swtalert.error(SwtUtil.getPredictMessage('Please enter a valid Number'));
          return;
         }
         if(''!=maximum && Number(this.valueTxt.text)>maximum){
          this.swtalert.error(SwtUtil.getPredictMessage('Please enter a valid Number lower than '+maximum));
          return;
         }
         if(''!=minimum && Number(this.valueTxt.text)<minimum){
          this.swtalert.error(SwtUtil.getPredictMessage('Please enter a valid Number greater than '+minimum));
          return;
        }

        }else if('integer' == subEvType){
          let isInteger = new RegExp(/^(0|-*[1-9]+[0-9]*)$/).test(this.valueTxt.text);
          if(!isInteger){
              this.swtalert.error(SwtUtil.getPredictMessage('Please enter a valid Integer'));
              return;
          }

          if(''!=maximum && Number(this.valueTxt.text)>maximum){
            this.swtalert.error(SwtUtil.getPredictMessage('Please enter a valid Number lower than '+maximum));
            return;
           }
           if(''!=minimum && Number(this.valueTxt.text)<minimum){
            this.swtalert.error(SwtUtil.getPredictMessage('Please enter a valid Number greater than '+minimum));
            return;
          }

        }else {
          if(''!=maxChars && Number(this.valueTxt.text.length)>maxChars){
            this.swtalert.error(SwtUtil.getPredictMessage('Value cannot be greater than '+maxChars+' characters'));
            return;
          }
        }

      this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvMapFrom = this.getMapFromLabel(this.mapFrom.selectedValue);
      this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.subEvMapFrom.content = this.getMapFromLabel(this.mapFrom.selectedValue);
      this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvMapFromValue = this.valueTxt.text;
      this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.subEvMapFromValue.content = this.valueTxt.text;
      }
    }
    }
    this.subEventsGrid.refresh();
    //prepare event grid xml
    this.gridParamsToXml();
  }

  gridParamsToXml() {
    this.eventXml = "";
    if (this.subEventsGrid.gridData.length > 0) {
      this.eventXml = "<mappedParameters>";
      for (let i = 0; i < this.subEventsGrid.gridData.length; i++) {
            let mapFrom=this.subEventsGrid.gridData[i].subEvMapFrom ? this.subEventsGrid.gridData[i].subEvMapFrom : "";
            let mapFromVal= this.subEventsGrid.gridData[i].subEvMapFromValue ? this.subEventsGrid.gridData[i].subEvMapFromValue : "";
            let subEvAddInfo= this.subEventsGrid.gridData[i].subEvAddInfo ? this.subEventsGrid.gridData[i].subEvAddInfo : "";
            let subEvRegExp= this.subEventsGrid.gridData[i].subEvRegExp ? this.subEventsGrid.gridData[i].subEvRegExp : "";
            let subEvRegExpMsg= this.subEventsGrid.gridData[i].subEvRegExpMsg ? this.subEventsGrid.gridData[i].subEvRegExpMsg : "";
            
            this.eventXml += "<parameter>";
            //to be checked with atef but i need them in case of change (req, desc, type)
            this.eventXml += "<isMandatory><![CDATA[" + this.subEventsGrid.gridData[i].subEvReq + "]]></isMandatory>";
            this.eventXml += "<data_type><![CDATA[" + this.subEventsGrid.gridData[i].subEvType  + "]]></data_type>";
            this.eventXml += "<description><![CDATA[" + this.subEventsGrid.gridData[i].subEvDescription + "]]></description>";
            this.eventXml += "<name><![CDATA[" + this.subEventsGrid.gridData[i].subEvId + "]]></name>";
            this.eventXml += "<useType><![CDATA[" + mapFrom + "]]></useType>";
            this.eventXml += "<value><![CDATA[" + mapFromVal + "]]></value>";
            this.eventXml += "<regular_expression><![CDATA[" + subEvRegExp + "]]></regular_expression>";
            this.eventXml += "<regular_expression_msg><![CDATA[" + subEvRegExpMsg + "]]></regular_expression_msg>";
            this.eventXml += "<additional_infomation><![CDATA[" + subEvAddInfo + "]]></additional_infomation>";
            this.eventXml += "</parameter>";
      }
      this.eventXml += "</mappedParameters>";
      this.eventXml = prettyData.pd.xml(this.eventXml);
    }
    this.xmlAsString=this.eventXml;
    //this.parentDocument.eventXml = this.eventXml;
  }

  saveHandler() {
    if (this.eventFacilityCombo.selectedLabel == "SEND_MESSAGE"){
      this.xmlAsString= this.msgCombo.selectedLabel;
    }
    if (this.eventFacilityCombo.selectedLabel == "SEND_EMAIL"){
      this.prepareEmailXml();
    }
    if(!this.checkMandatoryFilled()){
      this.swtalert.warning(SwtUtil.getPredictMessage('scenario.events.mandatoryFiels', null));
    }
    else if(this.eventFacilityCombo.selectedLabel =="") {
      this.swtalert.warning(SwtUtil.getPredictMessage('scenario.eventTab.alert.missingFacility', null));
    }else if (this.msgCombo.selectedLabel =="" && this.eventFacilityCombo.selectedLabel == "SEND_MESSAGE"){
      this.swtalert.warning(SwtUtil.getPredictMessage('scenario.eventTab.alert.missingMsgFormat', null));
    }
    /*else if(this.eventFacilityArray.indexOf(this.eventFacilityCombo.selectedLabel) != -1) {
      this.swtalert.warning(SwtUtil.getPredictMessage('scenario.eventTab.alert.facilityExists', null));
    }*/ else {
      if (window.opener && window.opener.instanceElement2) {
        window.opener.instanceElement2.refreshParent( this.eventFacilityCombo.selectedLabel, this.eventFacilityDescTxt.text, this.xmlAsString, (this.allowRepeatCheck.selected ? "Y": "N"), this.excecuteCombo.selectedValue);
        window.opener.instanceElement2.selectedUsers= this.selectedUsers;
        window.opener.instanceElement2.selectedRoles= this.selectedRoles;
       // window.opener.instanceElement2.selectedOtherEmails= this.selectedOtherEmails;
        ExternalInterface.call('close');
      }
    }

  }

  prepareEmailXml() {

  let eventXml = "";

  eventXml = "<mappedParameters>";
  eventXml += "<parameter>";
  eventXml += "<templateId><![CDATA[" +  (this.emailFormatCombo.selectedLabel?this.emailFormatCombo.selectedLabel:"") + "]]></templateId>";
  eventXml += "</parameter>";
  if(this.usersMainGrid.gridData.length>0) {
  eventXml += "<parameter>";
  eventXml += "<users>";
  for(let i =0; i< this.usersMainGrid.gridData.length; i++) {
  eventXml += "<user>";
  eventXml += "<userId><![CDATA[" + this.usersMainGrid.gridData[i ].userId+ "]]></userId>";
  eventXml += "<name><![CDATA[" +  this.usersMainGrid.gridData[i ].name + "]]></name>";
  eventXml += "</user>";
  }
  eventXml += "</users>";
  eventXml += "</parameter>";
}

if(this.rolesMainGrid.gridData.length>0) {
  eventXml += "<parameter>";
  eventXml += "<roles>";
  for(let i =0; i< this.rolesMainGrid.gridData.length; i++) {
  eventXml += "<role>";
  eventXml += "<roleId><![CDATA[" + this.rolesMainGrid.gridData[i ].roleId+ "]]></roleId>";
  eventXml += "<name><![CDATA[" +  this.rolesMainGrid.gridData[i ].name + "]]></name>";
  eventXml += "</role>";
  }
  eventXml += "</roles>";
  eventXml += "</parameter>";
}

 
/*if(this.otherEmailMainGrid.gridData.length>0) {
  eventXml += "<parameter>";
  eventXml += "<emails>";
  for(let i =0; i< this.otherEmailMainGrid.gridData.length; i++) {
  eventXml += "<email>";
  eventXml += "<emailAddress><![CDATA[" + this.otherEmailMainGrid.gridData[i ].emailAddress+ "]]></emailAddress>";
  eventXml += "<description><![CDATA[" +  this.otherEmailMainGrid.gridData[i ].description + "]]></description>";
  eventXml += "</email>";
  }
  eventXml += "</emails>";
  eventXml += "</parameter>";
}*/

  eventXml += "</mappedParameters>";
  eventXml = prettyData.pd.xml(eventXml);
  this.xmlAsString= eventXml;
}

  //check if mandatory fields are filled or not before saving xml
  checkMandatoryFilled() {
    var valid = [];
    for (let i = 0; i < this.subEventsGrid.gridData.length; i++) {
      if ((this.subEventsGrid.gridData[i].subEvReq == "M") &&
      (!this.subEventsGrid.gridData[i].subEvMapFromValue) && 
      (this.subEventsGrid.gridData[i].subEvMapFrom !='Ignore') && (this.subEventsGrid.gridData[i].subEvMapFrom !='Null')){
        valid.push("false");
      }else{
        valid.push("true");
      }
    }

    if (valid.includes("false")) {
      return false;
    } else {
      return true;
    }
  }


  cancelHandler() {
    ExternalInterface.call('close');
  }
  showXmlHandler() {
    if (this.eventFacilityCombo.selectedLabel == "SEND_EMAIL"){
      this.prepareEmailXml();
    }
    let modifiedXml=this.xmlAsString.replace(/<\!\[CDATA\[|\]\]>/g,'');
    this.win =  SwtPopUpManager.createPopUp(this, ShowXML, {
      title: "Show XML",
      xmlData: modifiedXml
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '400';
    this.win.height = '500';
    this.win.showControls = true;
    this.win.id = "guiShowXML";
    this.win.display();
  }

  updateMsgFormatCombo(){
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.setMsgFormatComboValues(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "scenMaintenance.do?";
    this.actionMethod = 'method=getMsgFormatsList';
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  setMsgFormatComboValues(event){
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
  
        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) { 
            this.msgCombo.setComboData(this.jsonReader.getSelects());
            this.msgCombo.selectedLabel= this.savedMsgComboLabel?this.savedMsgComboLabel:"";
            this.msgLabel.text= this.msgCombo.selectedValue ? this.msgCombo.selectedValue:"";
            if (!this.jsonReader.isDataBuilding()) {
              this.prevRecievedJSON = this.lastRecievedJSON; 
            }
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            //this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }
        }

      }
  
    }
  
    checkEmpty(combo) {
      if (combo.selectedLabel) {
        return true;
      } else {
        return false;
      }
    }

    getMapFromLabel(value){
      let label='';

      switch (value) {
      case 'A':
        label = 'Instance Attribute';
        break;
      case 'L':
        label = 'Literal';
        break;
      case 'I':
        label = 'Ignore';
        break;
      case 'N':
        label = 'Null';
        break;
      default:
        break;
      }
  
      return label;
    }

    getMapFromValue(label){
      let value='';

      switch (label) {
      case 'Instance Attribute':
        value = 'A';
        break;
      case 'Literal':
        value = 'L';
        break;
      case 'Ignore':
        value = 'I';
        break;
      case 'Null':
        value = 'N';
        break;
      default:
        break;
      }
  
      return value;
    }

  checkRegularExpression(exp, value){
    var alertFlag=false;
    //to test the regular expression
    var result:Object=exp.test(value);
    if (!result)
    {
      alertFlag=true;
    } 
    return alertFlag;   
  }

  configureRecipient(): void {
    ExternalInterface.call("openConfigScreen", "configureRecipients");
  }

  refreshParent(userGridData, roleGridData){
    this.usersMainGrid.gridData = { size: userGridData.length, row: userGridData };
    this.usersMainGrid.refresh();

    this.rolesMainGrid.gridData = { size: roleGridData.length, row: roleGridData };
    this.rolesMainGrid.refresh();

    /*this.otherEmailMainGrid.gridData = { size: otherEmailGridData.length, row: otherEmailGridData };
    this.otherEmailMainGrid.refresh();*/
  }



  changeEmailTemplate(){

  }


  startOfComms(): void {
    //this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    //this.loadingImage.setVisible(false);
  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: EventsAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [EventsAdd],
  entryComponents: []
})
export class EventsAddModule {}

