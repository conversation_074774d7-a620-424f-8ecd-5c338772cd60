
import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService, ExternalInterface, HashMap,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas, SwtCommonGrid,
  SwtLoadingImage, SwtModule, SwtUtil, SwtToolBoxModule, SwtLabel, SwtTextInput, SwtComboBox, SwtCheckBox, StringUtils,
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";

@Component({
  selector: 'app-copy-forecast-template',
  templateUrl: './CopyForecastTemplate.html',
  styleUrls: ['./CopyForecastTemplate.css']
})
export class CopyForecastTemplate extends  SwtModule{

  @ViewChild('lblCopy') lblCopy: SwtLabel;
  @ViewChild('cbTemplate') cbTemplate: SwtComboBox;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtLoadingImage;
  /* Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private saveData = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;
  private swtAlert: SwtAlert;
  private templateId: string;
  private userId: string;

  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  onLoad() {
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop=this.endOfComms.bind(this);
    this.templateId=ExternalInterface.call('eval', 'templateId');
    this.userId=ExternalInterface.call('eval', 'userId');
    //result event 
    this.inputData.cbResult=(event) => {
      this.inputDataResult(event);
    }
    //fault event
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    //action url	
    this.actionPath="forecastMonitorTemplate.do?method=";
    this.actionMethod="loadCopyFrom&loadFlex=true";
    if (this.templateId != "" && this.userId != "")
    {
      this.actionMethod=this.actionMethod + "&templateId=" + this.templateId;
      this.actionMethod=this.actionMethod + "&userId=" + this.userId;
    }
    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    //Make initial request
    this.inputData.send(this.requestParams);
  }
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  inputDataFault(event): void {
    this.swtAlert.error('generic_exception');
  }
 inputDataResult(event):void
  {
    //get the received xml
    this.lastRecievedJSON= event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    //test the reply status first, as if the reply status is false then the data will not be valid
    if (this.jsonReader.getRequestReplyStatus())
    {
      //Some monitors have a database job that runs to build the data. If this is running then the databuilding flag will be set
      //If the previousXML is different to new xml then allow an update. Otherwise leave it alone
      if ((this.lastRecievedJSON != this.prevRecievedJSON))
      {
        this.cbTemplate.setComboData(this.jsonReader.getSelects(), true);
      }
      this.prevRecievedJSON=this.lastRecievedJSON;
    }
    else
    {


      this.swtAlert.warning(
        this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(),//text
        "Error", Alert.OK,
        this); //close handler
    }
  }

 OkHandler():void {
    let selectedUserId: string= this.cbTemplate.selectedValue;
    //ExternalInterface.call("refreshParent", selectedUserId, this.cbTemplate.selectedLabel);
    if (window.opener && window.opener.instanceElement) {
      window.opener.instanceElement.reloadCopy(selectedUserId, this.cbTemplate.selectedLabel);
      this.closeHandler();
    }

  }
  closeHandler():void {
    ExternalInterface.call("close");
  }
  doHelp() : void {
    ExternalInterface.call('help');
  }

  }
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: CopyForecastTemplate }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CopyForecastTemplate],
  entryComponents: []
})
export class CopyForecastTemplateModule {}
