<SwtModule (creationComplete)='onLoad()'  width="100%" height="100%">
  <VBox width="100%" height="100%" paddingLeft="10" paddingRight="10" paddingBottom="10" paddingTop="10">
    <SwtCanvas id="canvasGrid" #canvasGrid  width="100%" height="90%">
    </SwtCanvas>
    <SwtCanvas width="100%" id="canvasButtons" >
      <HBox width="100%" >
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true"
                     id="addButton"
                     #addButton
                     (click)="doAddCurrency($event)"
                     (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                     id="changeButton"
                     #changeButton
                     (click)="doChangeCurrency($event)"
                     (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                     id="deleteButton"
                     #deleteButton
                     (click)="doDeleteCurrency($event)"
                     (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                     id="viewButton"
                     #viewButton
                     (click)="doViewCurrency($event)"
                     (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
          <SwtButton [buttonMode]="true"
                     id="closeButton"
                     #closeButton
                     (click)="closeCurrentTab($event)"
                     (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="5">
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
          <SwtHelpButton id="helpIcon"
                         [buttonMode]="true"
                         enabled="true"
                         helpFile="groups-of-rules"
                         (click)="doHelp()">
          </SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
