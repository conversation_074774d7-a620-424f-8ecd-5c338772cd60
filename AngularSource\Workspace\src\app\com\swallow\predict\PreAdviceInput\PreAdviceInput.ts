import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import moment from 'moment';
import { CommonService, ExportEvent, ExternalInterface, focusManager, HTTPComms, JSONReader, SwtAlert, SwtButton, SwtCanvas, SwtComboBox, SwtCommonGrid, SwtDataExport, SwtDateField, SwtFieldSet, SwtLabel, SwtLoadingImage, SwtModule, SwtRadioButtonGroup, SwtRadioItem, SwtTextInput, SwtToolBoxModule, SwtUtil } from 'swt-tool-box';
declare var instanceElement: any;
declare function validateCurrencyPlaces(strField, strPat, currCode): any;
@Component({
  selector: 'app-pre-advice-input',
  templateUrl: './PreAdviceInput.html',
  styleUrls: ['./PreAdviceInput.css']
})
export class PreAdviceInput extends SwtModule implements OnInit {
  /***********SwtCanvas***********/
  @ViewChild('preAdviceGridContainer') preAdviceGridContainer: SwtCanvas;
  /***********SwtLabel***********/
  @ViewChild('entity') entity: SwtLabel;
  @ViewChild('entityDesc') entityDesc: SwtLabel;
  @ViewChild('positionLevel') positionLevel: SwtLabel;
  @ViewChild('positionName') positionName: SwtLabel;
  @ViewChild('valueDate') valueDate: SwtLabel;
  @ViewChild('postingDate') postingDate: SwtLabel;
  @ViewChild('type') type: SwtLabel;
  @ViewChild('productType') productType: SwtLabel;
  @ViewChild('amount') amount: SwtLabel;
  @ViewChild('account') account: SwtLabel;
  @ViewChild('accountLabel') accountLabel: SwtLabel;
  @ViewChild('bookCode') bookCode: SwtLabel;
  @ViewChild('bookCodeLabel') bookCodeLabel: SwtLabel;
  @ViewChild('reference') reference: SwtLabel;
  @ViewChild('counterParty') counterParty: SwtLabel;
  @ViewChild('counterPartyLabel') counterPartyLabel: SwtLabel;
  @ViewChild('matchingParty') matchingParty: SwtLabel;
  @ViewChild('matchingPartyLabel') matchingPartyLabel: SwtLabel;
  @ViewChild('entity1') entity1: SwtLabel;
  @ViewChild('inputBy') inputBy: SwtLabel;

  /***********SwtComboBox***********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('positionCombo') positionCombo: SwtComboBox;
  @ViewChild('currencyCombo') currencyCombo: SwtComboBox;
  @ViewChild('signCombo') signCombo: SwtComboBox;
  @ViewChild('entityCombo1') entityCombo1: SwtComboBox;
  @ViewChild('bookCodeCombo') bookCodeCombo: SwtComboBox;
  @ViewChild('accountCombo') accountCombo: SwtComboBox;


  /***********SwtDateField***********/
  @ViewChild('valueDateField') valueDateField: SwtDateField;
  @ViewChild('postingDateField') postingDateField: SwtDateField;

  /***********SwtFieldSet***********/
  @ViewChild('fieldSet') fieldSet: SwtFieldSet;
  @ViewChild('fieldSet1') fieldSet1: SwtFieldSet;
  @ViewChild('fieldSet2') fieldSet2: SwtFieldSet;


  /***********SwtRadioButtonGroup***********/
  @ViewChild('predictStatus') predictStatus: SwtRadioButtonGroup;
  @ViewChild('includedRadio') includedRadio: SwtRadioItem;
  @ViewChild('excludedRadio') excludedRadio: SwtRadioItem;
  @ViewChild('cancelledRadio') cancelledRadio: SwtRadioItem;
  @ViewChild('typeRadios') typeRadios: SwtRadioButtonGroup;
  @ViewChild('cashRadio') cashRadio: SwtRadioItem;
  @ViewChild('securitiesRadio') securitiesRadio: SwtRadioItem;
  @ViewChild('user') user: SwtRadioButtonGroup;
  @ViewChild('allRadio') allRadio: SwtRadioItem;
  @ViewChild('currentRadio') currentRadio: SwtRadioItem;


  /***********SwtTextInput***********/
  @ViewChild('productTypeTxtInput') productTypeTxtInput: SwtTextInput;
  @ViewChild('amountTxtInput') amountTxtInput: SwtTextInput;
  @ViewChild('referenceTxtInput') referenceTxtInput: SwtTextInput;
  @ViewChild('counterPartyTxtInput') counterPartyTxtInput: SwtTextInput;
  @ViewChild('counterPartyTxtInput1') counterPartyTxtInput1: SwtTextInput;
  @ViewChild('matchingPartyTxtInput') matchingPartyTxtInput: SwtTextInput;


  /***********SwtButton***********/
  @ViewChild('accountButton') accountButton: SwtButton;
  @ViewChild('bookCodeButton') bookCodeButton: SwtButton;
  @ViewChild('counterPartyButton') counterPartyButton: SwtButton;
  @ViewChild('matchingPartyButton') matchingPartyButton: SwtButton;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('importButton') importButton: SwtButton;
  @ViewChild('mvtButton') mvtButton: SwtButton;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('dataExport') dataExport: SwtDataExport;
  private swtAlert: SwtAlert;
  private menuAccessId;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  /**
   * Variable to hold the grid
   **/
  private preAdviceInputGrid: SwtCommonGrid;
  //xmlList to hold combo values
  private accountComboList = null;
  private bookCodeComboList = null;
  private currencyComboList = null;
  private positionComboList = null;
  private defaultEntity: string = "";
  private defaultCurrency: string = "";
  private lastSelectedCurrency = "";
  private currencyPattern: string;
  private dateFormat: string;
  private lastSelectedEntity: string = "";
  //private fromCellClick = false;
  private mvtType;
  private selectedtRow;
  private displayedDate;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);

  }

  ngOnInit() {
    instanceElement = this;
    this.preAdviceInputGrid = <SwtCommonGrid>this.preAdviceGridContainer.addChild(SwtCommonGrid);
    this.entity.text = SwtUtil.getPredictMessage('movement.entity.id', null);
    this.positionLevel.text = SwtUtil.getPredictMessage('movement.positionLevel', null);
    this.valueDate.text = SwtUtil.getPredictMessage('movement.valueDate', null) + "*";
    this.postingDate.text = SwtUtil.getPredictMessage('movement.postingDate', null);
    this.type.text = SwtUtil.getPredictMessage('movement.movementType', null);
    this.productType.text = SwtUtil.getPredictMessage('movement.productType', null);
    this.amount.text = SwtUtil.getPredictMessage('movementDisplay.amount', null) + "*";
    this.account.text = SwtUtil.getPredictMessage('movement.accountId', null) + "*";
    this.bookCode.text = SwtUtil.getPredictMessage('movement.bookCode', null);
    this.reference.text = SwtUtil.getPredictMessage('preadviceInput.reference', null);
    this.counterParty.text = SwtUtil.getPredictMessage('movement.counterParty', null);
    this.matchingParty.text = SwtUtil.getPredictMessage('movement.matchingParty', null);
    this.entity1.text = SwtUtil.getPredictMessage('movement.entity.id', null);
    this.inputBy.text = SwtUtil.getPredictMessage('movement.inputBy', null);
    this.counterPartyLabel.text = "";
    this.matchingPartyLabel.text = "";

    this.entityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.positionCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectPosLevel', null);
    this.currencyCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectCurrencyCode', null);
    this.signCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectAmountSign', null);
    this.accountCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectAccountId', null);
    this.bookCodeCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectBookcode', null);

    this.amountTxtInput.toolTip = SwtUtil.getPredictMessage('tooltip.enterAmount', null);
    this.referenceTxtInput.toolTip = SwtUtil.getPredictMessage('tooltip.enterMvmRef1', null);
    this.counterPartyTxtInput.toolTip = SwtUtil.getPredictMessage('tooltip.counterPartId', null);
    this.counterPartyTxtInput1.toolTip = SwtUtil.getPredictMessage('tooltip.enterCPartytext', null);
    this.matchingPartyTxtInput.toolTip = SwtUtil.getPredictMessage('tooltip.enterMatchingParty', null);
    this.productTypeTxtInput.toolTip = SwtUtil.getPredictMessage('tooltip.enterProductType', null);

    this.fieldSet.legendText = SwtUtil.getPredictMessage('movementDisplay.predictStatus', null);
    this.fieldSet1.legendText = SwtUtil.getPredictMessage('preAdviceInput.fieldSet1.legendText', null);
    this.fieldSet2.legendText = SwtUtil.getPredictMessage('preAdviceInput.fieldSet2.legendText', null);

    this.includedRadio.label = SwtUtil.getPredictMessage('movementDisplay.included', null);
    this.excludedRadio.label = SwtUtil.getPredictMessage('movementDisplay.excluded', null);
    this.cancelledRadio.label = SwtUtil.getPredictMessage('movementDisplay.cancelled', null);
    this.cashRadio.label = SwtUtil.getPredictMessage('movementDisplay.cash', null);
    this.securitiesRadio.label = SwtUtil.getPredictMessage('movementDisplay.securities', null);
    this.allRadio.label = SwtUtil.getPredictMessage('tooltip.preadvice.allUsers', null);
    this.currentRadio.label = SwtUtil.getPredictMessage('tooltip.preadvice.currentUser', null);

    this.counterPartyButton.toolTip = SwtUtil.getPredictMessage('tooltip.clickSelCounterId', null);
    this.matchingPartyButton.toolTip = SwtUtil.getPredictMessage('tooltip.clickSelMatchingParty', null);
    this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    this.saveButton.toolTip = SwtUtil.getPredictMessage('tooltip.SaveChanges', null);

    this.importButton.label = SwtUtil.getPredictMessage('button.import', null);
    this.importButton.toolTip = SwtUtil.getPredictMessage('toolTip.import', null);
    this.mvtButton.label = SwtUtil.getPredictMessage('button.move', null);
    this.mvtButton.toolTip = SwtUtil.getPredictMessage('tooltip.move', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.Refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refresh', null);

    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.valueDateField.toolTip = SwtUtil.getPredictMessage('tooltip.clickValueDate', null);
    this.postingDateField.toolTip = SwtUtil.getPredictMessage('tooltip.enterPostingDate', null);

    this.entityCombo.required = true;
    this.amountTxtInput.required = true;
    this.accountCombo.required = true;

  }


  onLoad() {
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "preadviceinput.do?";
    this.actionMethod = 'method=display';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['movType'] = this.typeRadios.selectedValue;
    this.requestParams['entityId1'] = "All";
    this.requestParams['inputBy'] = this.user.selectedValue;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.preAdviceInputGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    };

    ExportEvent.subscribe((type) => {
      this.export(type);
    });


  }

  export(type: string): void {
    var selects = [];
    // Sets the column name
    //selects.push("Entity=" + this.entityCombo1.selectedLabel);
    //selects.push("InputBy=" + this.user.selectedValue);
    this.dataExport.convertData(this.lastRecievedJSON.PreAdviceInput.preAdviceInputGrid.metadata.columns, this.preAdviceInputGrid, null, selects, type, false);

  }

  inputDataResult(event): void {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.dataExport.enabled = true;

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) { 
          this.displayedDate=this.jsonReader.getSingletons().displayedDate;
          this.dateFormat = this.jsonReader.getSingletons().dateFormat;
          this.valueDateField.formatString = this.dateFormat.toLowerCase();
          this.postingDateField.formatString = this.dateFormat.toLowerCase();
          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.defaultEntity = this.jsonReader.getSingletons().defaultEntity;
          this.entityCombo.selectedLabel = this.defaultEntity;
          this.entityCombo1.setComboData(this.jsonReader.getSelects());
          if (this.lastSelectedEntity != undefined)
            this.entityCombo1.selectedLabel = this.lastSelectedEntity;
          this.positionCombo.setComboData(this.jsonReader.getSelects());
          this.currencyCombo.setComboData(this.jsonReader.getSelects());
          this.defaultCurrency = this.jsonReader.getSingletons().defaultCurrency;
          this.currencyCombo.selectedLabel = this.defaultCurrency;
          this.signCombo.setComboData(this.jsonReader.getSelects());
          this.accountCombo.setComboData(this.jsonReader.getSelects());
          this.bookCodeCombo.setComboData(this.jsonReader.getSelects());
          this.valueDateField.text = this.displayedDate;
          this.postingDateField.text = this.displayedDate;
          this.entityDesc.text = this.entityCombo.selectedValue;
          this.positionName.text = this.positionCombo.selectedValue;
          this.accountLabel.text = this.accountCombo.selectedValue != undefined ? this.accountCombo.selectedValue : "";
          this.bookCodeLabel.text = this.bookCodeCombo.selectedValue;
          this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
          //this.amountTxtInput.text="";
          if (!this.jsonReader.isDataBuilding()) {
            const obj = { columns: this.lastRecievedJSON.PreAdviceInput.preAdviceInputGrid.metadata.columns };
            this.preAdviceInputGrid.CustomGrid(obj);
            var gridRows = this.lastRecievedJSON.PreAdviceInput.preAdviceInputGrid.rows;
            if (gridRows.size > 0) {

              this.preAdviceInputGrid.gridData = gridRows;

              this.preAdviceInputGrid.setRowSize = this.jsonReader.getRowSize();
              this.dataExport.enabled = true;
            }
            else {
              this.preAdviceInputGrid.gridData = { size: 0, row: [] };
              this.dataExport.enabled = false;
            }

            this.prevRecievedJSON = this.lastRecievedJSON;

          }
        }
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }
      }
      // clear text input after save and update actions
      this.clearComponents();

    }

  }


  clearComponents(){
    this.productTypeTxtInput.text = "";
    this.amountTxtInput.text = "";
    this.referenceTxtInput.text = "";
    this.counterPartyTxtInput.text = "";
    this.counterPartyTxtInput1.text = "";
    this.matchingPartyTxtInput.text = "";
    this.typeRadios.selectedValue = "C";
    this.bookCodeLabel.text="";
    this.counterPartyLabel.text="";
    this.matchingPartyLabel.text="";
    this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    this.saveButton.toolTip = SwtUtil.getPredictMessage('tooltip.SaveChanges', null);
    //this.fromCellClick=false; 
    this.predictStatus.selectedValue="I";
  }


  refreshComboList() {
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.setComboLists(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "preadviceinput.do?";
    this.actionMethod = 'method=getLists';
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['movType'] = this.typeRadios.selectedValue;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.entityDesc.text=this.entityCombo.selectedValue;

  }


  setComboLists(event) {
    this.accountComboList = event.RefreshedComboList.selects.select[0].option;
    if (this.accountComboList) {
      this.accountCombo.dataProvider = this.accountComboList;
      this.accountCombo.setComboData(this.accountComboList);
      this.accountLabel.text = this.accountCombo.selectedValue != undefined ? this.accountCombo.selectedValue : "";
      /*if (this.fromCellClick){
      this.accountCombo.selectedLabel = this.preAdviceInputGrid.selectedItem.Account_ID.content;
      }*/
    }
    this.bookCodeComboList = event.RefreshedComboList.selects.select[1].option;
    if (this.bookCodeComboList) {
      this.bookCodeCombo.dataProvider = this.bookCodeComboList;
      this.bookCodeCombo.setComboData(this.bookCodeComboList);
      this.bookCodeLabel.text = this.bookCodeCombo.selectedValue;
      /*if (this.fromCellClick) {
        this.bookCodeCombo.selectedLabel = this.preAdviceInputGrid.selectedItem.Book_Code.content;
      }*/
    }

    this.currencyComboList = event.RefreshedComboList.selects.select[2].option;
    if (this.currencyComboList) {
      this.currencyCombo.dataProvider = this.currencyComboList;
      this.currencyCombo.setComboData(this.currencyComboList);
      this.currencyCombo.selectedLabel = event.RefreshedComboList.defaultCurrency ;
    }
    /*if (this.fromCellClick) {
      this.currencyCombo.selectedLabel = this.preAdviceInputGrid.selectedItem.Ccy.content;
    }*/


    this.positionCombo.setComboData(this.jsonReader.getSelects());
    this.positionName.text = this.positionCombo.selectedValue;

    this.positionComboList = event.RefreshedComboList.selects.select[3].option;
    if (this.positionComboList) {
      this.positionCombo.dataProvider = this.positionComboList;
      this.positionCombo.setComboData(this.positionComboList);
      this.positionName.text = this.positionCombo.selectedValue;
      // this.positionCombo.selectedLabel = event.RefreshedComboList.defaultCurrency ;
    }
  }


  refreshAccountComboList(){
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.setAccountList(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "preadviceinput.do?";
    this.actionMethod = 'method=getUpdatedAccountList';
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['currencyCode'] = this.currencyCombo.selectedLabel;
    this.requestParams['movType'] = this.typeRadios.selectedValue;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }


  setAccountList(event) {
    this.accountComboList = event.RefreshedComboList.selects.select.option;
    if (this.accountComboList) {
      this.accountCombo.dataProvider = this.accountComboList;
      this.accountCombo.setComboData(this.accountComboList);
      this.accountLabel.text = this.accountCombo.selectedValue != undefined ? this.accountCombo.selectedValue : "";
      /*if (this.fromCellClick) {
        this.accountCombo.selectedLabel = this.preAdviceInputGrid.selectedItem.Account_ID.content;
      }*/
    }

  }

  refreshLabel() {
    this.accountLabel.text = this.accountCombo.selectedValue != undefined ? this.accountCombo.selectedValue : "";
    this.bookCodeLabel.text = this.bookCodeCombo.selectedValue != undefined ? this.bookCodeCombo.selectedValue: "";

  }

  preSearchParties(): void {

    //ExternalInterface.call("party","N","movement.counterPartyId","counterPartyDesc",this.entityCombo.selectedLabel);
    ExternalInterface.call('openChildPartyWindow', 'partySearch', this.entityCombo.selectedLabel);
  }


  setSelectedPartieItemsForPreadvice(partyId, partyName) {
    let eventString: string = null;
    eventString = (Object(focusManager.getFocus()).name).id;
    if (eventString == "counterPartyButton") {
      this.counterPartyTxtInput.text = partyId;
      this.counterPartyLabel.text = partyName;
    } else if (eventString == "matchingPartyButton") {
      this.matchingPartyTxtInput.text = partyId;
      this.matchingPartyLabel.text = partyName;
    }

  }

  savePreAdvice() {
    this.requestParams = [];
    if (this.entityCombo.selectedValue == '' || this.entityCombo.selectedValue == undefined || this.amountTxtInput.text == ''
      || this.accountCombo.selectedValue == '' || this.accountCombo.selectedValue == undefined) {
      this.swtAlert.error(SwtUtil.getPredictMessage('alert.pleaseFillAllMandatoryFields', null));
    } else {
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.displayPreAdvice(event);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "preadviceinput.do?";
      /*if(this.fromCellClick){
      this.actionMethod = 'method=updatePreAdvice';
      this.requestParams['movement.id.movementId'] = this.preAdviceInputGrid.selectedItem.Movement.content; 
      }
      else{*/
      this.actionMethod = 'method=savePreAdvice';
      //}
      this.requestParams['movement.accountId'] = this.accountCombo.selectedLabel;
      this.requestParams['movement.amountAsString'] = this.amountTxtInput.text;
      this.requestParams['movement.bookCode'] = this.bookCodeCombo.selectedLabel;
      this.requestParams['movement.counterPartyId'] = this.counterPartyTxtInput.text;
      this.requestParams['movement.counterPartyText1'] = this.counterPartyTxtInput1.text;
      this.requestParams['movement.currencyCode'] = this.currencyCombo.selectedLabel;
      this.requestParams['movement.id.entityId'] = this.entityCombo.selectedLabel;
      this.requestParams['movement.matchingParty'] = this.matchingPartyTxtInput.text;
      this.requestParams['movement.matchStatus'] = "";
      this.requestParams['movement.movementType'] = this.typeRadios.selectedValue;
      this.requestParams['movement.positionLevelAsString'] = this.positionCombo.selectedLabel;
      this.requestParams['movement.postingDateAsString'] = this.postingDateField.text;
      this.requestParams['movement.predictStatus'] = this.predictStatus.selectedValue;
      this.requestParams['movement.productType'] = this.productTypeTxtInput.text;
      this.requestParams['movement.reference1'] = this.referenceTxtInput.text;
      this.requestParams['movement.sign'] = this.signCombo.selectedLabel;
      this.requestParams['movement.valueDateAsString'] = this.valueDateField.text;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

    }


  }

  displayPreAdvice(event) {
    if (this.jsonReader.getRequestReplyStatus()) {
      this.swtAlert.info(event.preAdviceInput.status);
    }
    this.updateData();
  }

  validateReserve(): any {
    let validAmount = SwtUtil.getPredictMessage('alert.validAmount', null);
    if (!(validateCurrencyPlaces(this.amountTxtInput, this.currencyPattern, this.currencyCombo.selectedLabel))) {
      this.swtAlert.warning(validAmount);
      return false;
    }
  }

  validateDateField(dateField) {
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      if (dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase(), true);
        if (!date.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
            this.setFocusDateField(dateField)
          });
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          this.setFocusDateField(dateField)
        });
        return false;
      }
      dateField.selectedDate = date.toDate();
    } catch (error) {
    }

    return true;
  }


  setFocusDateField(dateField) {
    dateField.setFocus();
    dateField.text = this.jsonReader.getSingletons().displayedDate;
  }

  /**
 * Update the data, this is called whenever a fresh of the data is required.
 **/
  public updateData(): void {
    this.requestParams = [];
    this.entityCombo.enabled=true;
    this.preAdviceInputGrid.selectedIndex = -1;
    this.mvtButton.enabled=false;
    this.mvtButton.buttonMode=false;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "preadviceinput.do?";
    // Define method the request to access
    this.actionMethod = 'method=display';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['movType'] = this.typeRadios.selectedValue;
    this.requestParams['entityId1'] = this.entityCombo1.selectedLabel;
    this.requestParams['inputBy'] = this.user.selectedValue;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.send(this.requestParams);
    this.lastSelectedEntity = this.entityCombo1.selectedLabel;

  }


  /** This method is called when selecting a row from bottom grid**/

  cellClickEventHandler(event): void {
    if(this.preAdviceInputGrid.selectedIndex >=0){
    //this.fromCellClick=true;
    this.mvtButton.enabled = true;
    this.mvtButton.buttonMode = true;
    //this.entityCombo.enabled=false;
    this.selectedtRow=this.preAdviceInputGrid.selectedItem;
    //this.getMvtType(this.selectedtRow.Account_ID.content);
    }else{
      //this.fromCellClick=false;
      this.mvtButton.enabled = false;
      this.mvtButton.buttonMode = false;
      this.valueDateField.text = this.displayedDate;
      this.postingDateField.text = this.displayedDate;
      this.clearComponents();
      this.refreshComboList();
    }
  }

  openMvtDisplayScreen(): void {
    var movementId = this.preAdviceInputGrid.selectedItem.Movement.content;
    var entityId = this.preAdviceInputGrid.selectedItem.entityid.content;
    ExternalInterface.call("showMvmnt", "showMovementDetails", movementId, entityId, "");
  }

  openImportMvtScreen(): void {
    ExternalInterface.call("openImportWindow", "importMvt");
  }

  /**
* 
**/
  public getMvtType(account): void {
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "preadviceinput.do?";
    // Define method the request to access
    this.actionMethod = 'method=getMvtType';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['accountId'] = account;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.cbResult = (event) => {
      this.populateValues(event);
    };
    this.inputData.send(this.requestParams);

  }


  populateValues(event){
    this.mvtType=event.preAdviceInput.mvtType;
    this.entityCombo.selectedLabel=this.selectedtRow.entityid.content;
    this.valueDateField.text=this.selectedtRow.Value_Date.content !=undefined?this.selectedtRow.Value_Date.content: "";
    this.postingDateField.text=this.selectedtRow.Post_Date.content != undefined?this.selectedtRow.Post_Date.content:"";
    this.typeRadios.selectedValue=this.mvtType !=undefined?this.mvtType:"";
    this.predictStatus.selectedValue=this.getPredictStatusCode(this.selectedtRow["Pred.Status"].content);
    this.productTypeTxtInput.text = this.selectedtRow.Product_Type.content !=undefined?this.selectedtRow.Product_Type.content:"";
    this.amountTxtInput.text = this.selectedtRow.Amount.content !=undefined?this.selectedtRow.Amount.content:"";
    this.currencyCombo.selectedLabel= this.selectedtRow.Ccy.content !=undefined?this.selectedtRow.Ccy.content :""; 
    this.signCombo.selectedLabel= this.selectedtRow.Sign.content !=undefined?this.selectedtRow.Sign.content :"";
    this.refreshComboList(); 
    this.bookCodeCombo.selectedLabel = this.selectedtRow.Book_Code.content !=undefined?this.selectedtRow.Book_Code.content:"";
    this.referenceTxtInput.text = this.selectedtRow.Reference.content !=undefined?this.selectedtRow.Reference.content:"";
    this.counterPartyTxtInput.text = this.selectedtRow.CounterParty_ID.content != undefined?this.selectedtRow.CounterParty_ID.content:"";
    this.counterPartyTxtInput1.text = this.selectedtRow.Cparty_Text.content !=undefined?this.selectedtRow.Cparty_Text.content:"";
    this.matchingPartyTxtInput.text = this.selectedtRow.Match_Party.content !=undefined?this.selectedtRow.Match_Party.content:"";
    this.saveButton.label = SwtUtil.getPredictMessage('button.update', null);
    this.saveButton.toolTip = SwtUtil.getPredictMessage('tooltip.SaveUpdates', null);

  }

  getPredictStatusCode(status) {
    var statusCode="";
    switch (status) {
      case "Included":
        statusCode = "I";
        break;
      case "Excluded":
        statusCode = "E";
        break;
      case "Cancelled":
        statusCode = "C";
        break;

      default:
        break;
    }
    return statusCode;
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }





  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  closeHandler(): void {
    ExternalInterface.call("close");
  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: PreAdviceInput }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PreAdviceInput],
  entryComponents: []
})
export class PreAdviceInputModule { }