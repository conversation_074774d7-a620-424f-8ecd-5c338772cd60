<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" height="80" minWidth="1340" >
      <SwtComboBox id ="entityCombo" #entityCombo dataLabel ="entity" visible = "false"  enabled="false">
      </SwtComboBox>
      <Grid width="80%" height="100%" paddingLeft="5" verticalGap="2">
        <GridRow width="100%" height="30%">
            <GridItem width="70">
              <SwtLabel id="entityLabel" fontWeight="bold"
                        #entityLabel ></SwtLabel>
            </GridItem>
            <GridItem width="130">
              <SwtLabel id="selectedEntityId" #selectedEntityId
                        fontWeight="normal"></SwtLabel>
            </GridItem>
            <GridItem width="20%">
              <SwtLabel id="selectedEntityName" #selectedEntityName
                        fontWeight="normal"></SwtLabel>
            </GridItem>
        </GridRow>
        <GridRow width="100%" height="30%">
          <GridItem width="70">
            <SwtLabel id="matchLabel"
                      fontWeight="bold"
                      #matchLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width="160">
            <SwtTextInput  id="matchIdText" #matchIdText
                           width="150"
                           textAlign="right"
                           height="21"
                           restrict="0-9"
                           (keydown)="getMatchId($event)"
                           maxChars="12">
            </SwtTextInput>
          </GridItem>
          <GridItem width="90">
            <SwtLabel id="matchIndices" styleName ="labelMatchGreen" #matchIndices >
            </SwtLabel>
          </GridItem>
          <GridItem width="75">
            <SwtLabel id="matchStatus" styleName ="labelMatchGreen" #matchStatus ></SwtLabel>
            </GridItem>
          <GridItem width="17">
            <SwtLabel id="matchQuality" styleName ="labelMatchGreen" #matchQuality></SwtLabel>
          </GridItem>
          <GridItem width="75">
            <SwtLabel id="updateDate" styleName ="labelMatchGreen" #updateDate ></SwtLabel>
          </GridItem>
          <GridItem width="55">
            <SwtLabel id="updateUser" styleName ="labelMatchGreen" #updateUser ></SwtLabel>
          </GridItem>
          <GridItem width="2%">
            <SwtImage id="noteImage"
                      #noteImage ></SwtImage>
          </GridItem>
          <GridItem width="2%" paddingTop="2">
            <SwtImage id="alertImage"
                      #alertImage width="17" (click)="imageClickFunction($event)"></SwtImage>
          </GridItem>
        </GridRow>
        <GridRow width="100%" height="30%">
          <GridItem width="70">
            <SwtLabel id="currencyLabel" fontWeight="bold"
                      #currencyLabel width="60">
            </SwtLabel>
          </GridItem>
          <GridItem width="65%">
            <SwtLabel id="currencyCode" #currencyCode  fontWeight="normal">
            </SwtLabel>
          </GridItem>
        </GridRow>
      </Grid>
      <Grid width="22%" height="100%" paddingLeft="5" verticalGap="2">
        <GridRow width="100%" height="60%"></GridRow>
        <GridRow width="100%"  height="30%">
        <GridItem width="55%">
          <SwtLabel id="ccyTolLabel" #ccyTolLabel  fontWeight="bold">
          </SwtLabel>
        </GridItem>
        <GridItem width="30%">
          <SwtLabel id="ccyTolValue" #ccyTolValue  fontWeight="normal">
          </SwtLabel>
        </GridItem>
      </GridRow>
      </Grid>
      <fieldset style="height:95%;width: 12%">
          <legend>   {{posTotal}} </legend>
        <Grid width="100%" height="100%">
          <GridRow>
            <GridItem>
              <SwtLabel id="internalLabel" fontWeight="bold"
                            #internalLabel>
              </SwtLabel>
            </GridItem>
            <GridItem paddingLeft="3"
                         paddingTop="8">
            </GridItem>
            <GridItem paddingRight="3">
              <SwtLabel id="posIntlvl" #posIntlvl paddingLeft="20">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem>
                  <SwtLabel id="externalLabel" fontWeight="bold"
                            #externalLabel>
                  </SwtLabel>
            </GridItem>
            <GridItem paddingLeft="3"
                         paddingTop="0">
            </GridItem>
            <GridItem paddingRight="3"
                      paddingBottom="3">
              <SwtLabel id="posExtlvl" #posExtlvl paddingLeft="20">
              </SwtLabel>
            </GridItem>
          </GridRow>
        </Grid>
        </fieldset>
    </SwtCanvas>
    <SwtCanvas #gridCanvas id="gridCanvas" width="100%" height="100%" minWidth="1340" minHeight="100"></SwtCanvas>
    <SwtCanvas  id="canvasButtons" height="40" width="100%"  minWidth="1340" marginTop="5" marginBottom="0">
      <HBox width="100%">
        <HBox  id="buttonsContainer" #buttonsContainer paddingLeft="5" width="100%" horizontalGap="2">
      </HBox>
        <HBox horizontalAlign="right" horizontalGap="3">
          <SwtText id="dataBuildingText"
                    #dataBuildingText
                    width="155"
                    height="16"
                    text="DATA BUILD IN PROGRESS"
                    visible="false">
          </SwtText>
          <SwtText id="lostConnectionText"
                   #lostConnectionText
                   text="CONNECTION ERROR"
                   (click)="connError()"
                   visible="false">
          </SwtText>
          <VBox height="100%" verticalGap="0" marginRight="10">
            <SwtLabel #diffLabel id="diffLabel" fontWeight="bold" width="70"  marginTop="-3"></SwtLabel>
            <SwtLabel #diffValue id="diffValue" fontWeight="normal" marginTop="-15"></SwtLabel>
          </VBox>
          <div>
            <DataExport #exportContainer id="exportContainer" enabled="false"></DataExport>
          </div>
          <SwtLoadingImage id="loadingImage" #loadingImage></SwtLoadingImage>
          <SwtHelpButton id="helpIcon"
                         #helpIcon
                         (click)="helpHandler()">
          </SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
