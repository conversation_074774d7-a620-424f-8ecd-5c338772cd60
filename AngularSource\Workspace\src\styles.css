/* You can add global styles to this file, and also import other style files */

input[type="text"]:disabled {
   color: grey !important;
 }
input[type="text"]:not(:disabled){
  background-color: white !important;
}

.swtcombobox-filter-input {
  border-bottom: 1px solid #7f9db9 !important;
  border-top: 1px solid #7f9db9 !important;
  border-left: 1px solid #7f9db9 !important;
  padding-left: 5px !important;
  font-size: 11px !important;
}
.swtcomboBox-dropDown-li {
  font-size: 11px !important;
}

.input-group-addon{
  border-bottom: 1px solid #7f9db9 !important;
  border-top: 1px solid #7f9db9 !important;
  border-right: 1px solid #7f9db9 !important;
}
.timeInput-time {
  border-bottom: 1px solid #7f9db9 !important;
  border-top: 1px solid #7f9db9 !important;
  border-left: 1px solid #7f9db9 !important;
  padding-left: 5px !important;
}
.timeInput-spiners {
  border-bottom: 1px solid #7f9db9 !important;
  border-top: 1px solid #7f9db9 !important;
  border-right: 1px solid #7f9db9 !important;
}

.hboxRow{
  width: 100% !important;
  height:28px !important;
}
.arrow-up{
  border-top-right-radius: 0 !important;
  margin-top: -6px !important;
}
.arrow-down {
  border-bottom-right-radius: 0 !important;
}

/* label.ellipsisDisabled {
  padding-top: 3px !important;
} */

.radioButtonGroup{
  padding-left: 5px !important;
  padding-top: 5px !important;
}

.swtmodule[_ngcontent-c1] {
  background-color: #d6e3fe !important;
}

.alert-heading {
  background-color: #529fed !important;
  background-image: none !important;
}
.alert-body {
  background-color: #d6e3fe !important;

}
.alert-content {
  border: 1px solid #529fed !important;
}

.slick-cell {
	border-right: 1px solid silver !important;
	border-bottom: 1px solid #dfdfdf !important;
	border-top: 0px!important;
}
.window-container {
  border-bottom: 8px solid #d6e3fe !important;
  border-right: 8px solid #d6e3fe !important;
  border-left: 8px solid #d6e3fe !important;
  border-top: 2px solid #d6e3fe !important;
  border: 2px solid silver !important;
  background-color: #d6e3fe !important ;
}
.window-heading {
  background-color: #fefefb !important;
  color: black !important;
}

.slickgrid-container input[type=checkbox]  {
  background-color : rgba(0, 0, 0, 0) !important; 
}

/*.slick-viewport.slick-viewport-top.slick-viewport-left{*/
  /*  height: 100%!important;*/
/*}*/

/* input[type="text"]:disabled{
  font-size: 12px !important;
  margin: 0px 5px 5px 0px !important;
} */

input[type="radio"]:disabled{
  background-color : rgba(0, 0, 0, 0) !important; 
}
.ui-datepicker table {
  background-color: white !important;
}
.ui-datepicker {
  z-index: 999999999999 !important;
}

.ui-datepicker th {
  width: 23px ;
}
.ui-widget-content {
  background-color: white !important;
}

.rblabel{
  font-size: 11px !important;
}
.swtTextArea{
  border-bottom: 1px solid #7f9db9 !important;
  border-top: 1px solid #7f9db9 !important;
  border-left: 1px solid #7f9db9 !important;
  border-right: 1px solid #7f9db9 !important;
}

div[selector="SwtModule"] {
  background-color: #d6e3fe !important;
}

.form-control {
  color: black !important;
  background-color: white !important;
}

.select2-results> ul > li:nth-child(odd) {
	background: #E0F0FF !important;
 }

 .ms-drop > ul > li:nth-child(odd) {
	background: #E0F0FF  !important;
}

.underlineText{
  text-decoration: underline !important
}
/*Added by FMrad */
.borderVBox{
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
.textInputPadding {
  padding-right: 5px !important;
}
.underline {
  text-decoration: underline;
}
legend{
  width: auto !important;
  font-size: 11px !important;
  margin: 0px 0px 0px 8px !important;
  border-bottom: 1px solid transparent !important;
}
fieldset{
  border:1px solid #999!important;
  padding: 0px 5px 5px 5px !important;
  margin-top: 5px !important;
}
.linked {
  text-decoration: underline;
}

.labelMatchGreen {
  text-align:right !important;
  color:green !important;
  font-weight:normal;
}

.withoutMargin > div{
  margin : 0px 0px 0px 0px !important;
  height: 20px !important;
}
body {
  margin: 0px !important;
}

.fancytree-folder span.fancytree-title {
  font-weight: normal !important;
}

input:focus {
  border: 1px solid #7F9DB9;
  outline-style: solid;
  outline-width: 1px;
  outline-color: #49B9FF;
}
