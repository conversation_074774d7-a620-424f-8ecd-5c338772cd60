<SwtModule #swtModule (creationComplete)='onLoad()' height='100%' width='100%'>
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas width='100%' height="93%">
      <VBox width='100%' height="100%">
        <Grid width='100%' height="42%" marginTop="3">
          <GridRow>
            <GridItem width="15%">
              <SwtLabel  id="categoryIdLabel" #categoryIdLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width='25%'>
              <SwtTextInput id="categoryIdTxtInput"
                            #categoryIdTxtInput
                            restrict="a-zA-Z0-9\-_"
                            required="true"
                            maxChars="20"
                            width="200">
              </SwtTextInput>
            </GridItem>
            <GridItem width='6%'>
              <SwtLabel #categoryNameLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width='45%'>
              <SwtTextInput id="categoryNameTxtInput"
                            #categoryNameTxtInput
                            restrict="A-Za-z0-9\d_ !\&quot;#$%&'()*+,\-.\/:;&lt;=&gt;?@[\\\]^`{|}~"
                            maxChars="50"
                            required="true"
                            width="400">
              </SwtTextInput>
            </GridItem>
            <GridItem width='8%'>
              <SwtCheckBox id="actCheckbox"
                           #actCheckbox
                           styleName="checkbox"
                           value ="Y"
                           selected="true">
              </SwtCheckBox>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="15%">
              <SwtLabel  id="orderLabel"
                         #orderLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width='85%'>
              <SwtNumericInput id="ordinalNumInput" class="numericInput"
                               #ordinalNumInput
                               required="true"
                               width="60"
                               textAlign="right">
              </SwtNumericInput>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="25%">
              <SwtLabel  id="ruleAssignPriorityLabel"
                         #ruleAssignPriorityLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width='75%'>
              <SwtNumericInput id="ruleAssignPriorityInput" class="numericInput"
                               #ruleAssignPriorityInput
                               width="60"
                               maxChars="3"
                               required="true"
                               textAlign="right"
                               (focusOut)="checkIfPriorityExists()">
              </SwtNumericInput>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="25%">
              <SwtLabel  #instRelGroupLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width='70%'>
              <SwtRadioButtonGroup #instRelGroup   id="instRelGroup"
                                   (change) ="changeInstantRelease()"
                                   align="horizontal"
                                   width='100%'>
                <SwtRadioItem #urgent id="urgent" value="U" width="120"  groupName="instRelGroup" ></SwtRadioItem>
                <SwtRadioItem #spread id="spread" value="S"  width="120" groupName="instRelGroup" selected="true" ></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
          </GridRow>
          <GridRow #gridRowRelTime >
            <GridItem width="25%">
              <SwtLabel #labelRelTime>
              </SwtLabel>
            </GridItem>
            <GridItem width='55%'>
              <SwtRadioButtonGroup #relTimeGroup id="relTimeGroup"
                                   (change) ="changeTimeGroup($event)"
                                   align="horizontal" width="100%">
                <SwtRadioItem #radioK id="radioK" value="K"  width="120"   groupName="relTimeGroup" selected="true" ></SwtRadioItem>
                <SwtRadioItem #radioU id="radioU" value="U"  width="160"   groupName="relTimeGroup"  ></SwtRadioItem>
                <SwtRadioItem #radioI id="radioI" value="I"  width="100"   groupName="relTimeGroup"  ></SwtRadioItem>
                <SwtRadioItem #radioT id="radioT" value="T"  width="100"   groupName="relTimeGroup"  ></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
            <GridItem width='10%'>
              <SwtTextInput id="timeInput" class="numericInput"
                            #timeInput
                            width="50"
                            required="true"
                            maxChars="5"
                            textAlign="center"
                            pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"
                            (focusOut)="validateTime(timeInput)">
              </SwtTextInput>
            </GridItem>
          </GridRow>
          <GridRow #gridRowOffsetDays >
            <GridItem width="25%">
              <SwtLabel  #labelOffsetDays>
              </SwtLabel>
            </GridItem>
            <GridItem width="70%">
              <SwtNumericInput #offsetNum id="offsetNum"
                               class="numericInput"
                               width="60"
                               (focusOut)="validateUseLiquidity()"
                               textAlign="right"
                               maxChars="4">
              </SwtNumericInput>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="25%">
              <SwtLabel  #assigMetLabel >
              </SwtLabel>
            </GridItem>
            <GridItem width="70%">
              <SwtRadioButtonGroup #assigMetGroup   id="assigMetGroup"
                                   align="horizontal">
                <SwtRadioItem #radioM id="radioM" value="M"  width="120"   groupName="assigMetGroup"  ></SwtRadioItem>
                <SwtRadioItem #radioS id="radioS" value="S"   width="160"  groupName="assigMetGroup"  ></SwtRadioItem>
                <SwtRadioItem #radioB id="radioB" value="B"  width="120"   groupName="assigMetGroup" selected="true" ></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="25%">
              <SwtLabel    #useliqLabel></SwtLabel>
            </GridItem>
            <GridItem width="70%">
              <SwtCheckBox id="useliqCheckBox"
                           #useliqCheckBox
                           styleName="checkbox"
                           value ="N"
                           selected="false">
              </SwtCheckBox>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="25%">
              <SwtLabel     #incTargLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width="70%">
              <SwtCheckBox id="incTargCheckBox"
                           #incTargCheckBox
                           styleName="checkbox"
                           value ="Y"
                           selected="true">
              </SwtCheckBox>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="25%">
              <SwtLabel    #incLiqLabel  >
              </SwtLabel>
            </GridItem>
            <GridItem width="70%">
              <SwtCheckBox id="incLiqCheckBox"
                           #incLiqCheckBox
                           styleName="checkbox"
                           value ="Y"
                           selected="true">
              </SwtCheckBox>
            </GridItem>
          </GridRow>
        </Grid >
        <SwtPanel id="panelRulesGrid" #panelRulesGrid width="100%"  height="30%">
          <HBox height="100%" width="100%" paddingTop="5" paddingBottom="5">
            <SwtCanvas id="canvasRulesGrid"  border="false"
                       #canvasRulesGrid
                       height="100%"
                       width="90%">
            </SwtCanvas>
            <VBox height="100%" width="10%" verticalAlign="middle" horizontalAlign="center" >
              <SwtButton #addRuleButton  id="addRuleButton"
                         width="60"
                         (click)="doAddCategoryRule($event)"
                         (keyDown)="keyDownEventHandler($event)" marginBottom="6"></SwtButton>
              <SwtButton #changeRuleButton  id="changeRuleButton"
                         width="60"
                         (click)="doChangeCategoryRule($event)"
                         (keyDown)="keyDownEventHandler($event)" marginBottom="6"></SwtButton>
              <SwtButton #viewRuleButton  id="viewRuleButton"
                         width="60"
                         (click)="doViewCategoryRule($event)"
                         (keyDown)="keyDownEventHandler($event)" marginBottom="6">
              </SwtButton>
              <SwtButton id="deleteRuleButton" #deleteRuleButton
                         width="60"
                         (click)="doDeleteCategoryRule($event)"
                         (keyDown)="keyDownEventHandler($event)"></SwtButton>
            </VBox>
          </HBox>
        </SwtPanel>
        <SwtPanel  id="panelSpreadGrid" #panelSpreadGrid width='100%' paddingTop="5" height="25%">
          <HBox height="100%" width="100%" paddingTop="5" paddingBottom="5">
            <SwtCanvas  border="false" id="canvasSpreadGrid" #canvasSpreadGrid width='100%' height='100%'></SwtCanvas>
          </HBox>
        </SwtPanel>
      </VBox>

    </SwtCanvas>
    <SwtCanvas id="canvasContainer" width='100%' height="5%">
      <HBox width='100%' height="100%">
        <SwtButton #saveButton  id="saveButton"
                   (click)="checkPriorityBeforeSave()"
                   (keyDown)="keyDownEventHandler($event)"></SwtButton>
        <SwtButton buttonMode="true"
                   id="cancelButton"
                   #cancelButton
                   (click)="popupClosed()"
                   (keyDown)="keyDownEventHandler($event)"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
