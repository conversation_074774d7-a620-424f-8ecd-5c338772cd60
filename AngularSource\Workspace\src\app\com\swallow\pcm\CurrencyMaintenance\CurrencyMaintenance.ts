import {
  Component, ElementRef, NgModule,
  ViewChild,
} from '@angular/core';
import {
  HTTPComms,
  SwtButton,
  SwtCommonGrid,
  JSONReader,
  SwtAlert,
  SwtUtil,
  SwtLoadingImage,
  CommonService,
  SwtCanvas,
  SwtPopUpManager,
  SwtPanel,
  Keyboard,
  focusManager,
  Alert, ExternalInterface, SwtModule, SwtLabel, SwtToolBoxModule, ModuleLoader, ModuleEvent
} from 'swt-tool-box';

import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";

@Component({
  selector: 'app-pccurrency-maintenance',
  templateUrl: './CurrencyMaintenance.html',
  styleUrls: ['./CurrencyMaintenance.css']
})
export class CurrencyMaintenance extends  SwtModule {

  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********Combobox*********/
  /*********SwtLabel*********/
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  /********SwtButton*********************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('exportButton') exportButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('settingButton') settingButton: SwtButton;
  @ViewChild('csv') csv: SwtButton;
  @ViewChild('excel') excel: SwtButton;
  @ViewChild('pdf') pdf: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;


  private currencyGrid: SwtCommonGrid;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public maxOrder;
  public listOrder;
  public currencyPattern: string;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  private win;

  /* - START -- Screen Name and Version Number ---- */
  private moduleName = 'Currency Maintenance';
  private versionNumber = '1.00.00';
  private releaseDate = '20 February 2019';
  /* - END -- Screen Name and Version Number ---- */

  // to open a pop up
  private child: SwtPanel;
  private swtAlert: SwtAlert;
  private  menuAccess = 2;
  public  helpURL: string = null;
  private  message: string = null;
  public  title: string = null;
  private errorLocation = 0;
  public moduleId = '';
  private ccy: string = null;
  private order = 0;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit(): void {
    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.deleteButton.label =SwtUtil.getPredictMessage('button.delete', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    // Assining properties for controls
    this.addButton.setFocus();
    this.message = SwtUtil.getPredictMessage('alert.delete', null);
  }

  onLoad() {

    this.currencyGrid = this.canvasGrid.addChild(SwtCommonGrid) as SwtCommonGrid;
    this.currencyGrid.uniqueColumn = "ccy";
    this.currencyGrid.onFilterChanged = this.disableButtons.bind(this);
    this.currencyGrid.onSortChanged =  this.disableButtons.bind(this);
    try {
      this.title ='Currency Maintenace';
      this.actionMethod = 'method=display';
      this.actionPath = 'currencyPCM.do?';
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.currencyGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };
    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'CurrencyMaintenance', 'onLoad');
    }

  }

  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(data): void {
    let jsonList = null;
    let header: string;
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {
            this.helpURL = this.jsonReader.getSingletons().helpurl;
            this.currencyPattern  = this.jsonReader.getSingletons().currencyPattern;
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {
              this.menuAccess = Number(this.jsonReader.getScreenAttributes()["menuaccess"]);
              this.enableAddButton(this.menuAccess==0);
              this.disableOrEnableButtons(false);
              jsonList = this.jsonReader.getColumnData();
              for (let i = 0; i < jsonList.column.length; i++) {
                header = SwtUtil.getAMLMessages(jsonList.column[i].heading);
                jsonList.column[i].heading = header;
              }
              const obj = {columns: jsonList};
              if (this.currencyGrid === null || this.currencyGrid === undefined) {
                this.currencyGrid.componentID = this.jsonReader.getSingletons().screenid;
              }
              this.currencyGrid.doubleClickEnabled = true;
              this.currencyGrid.currencyFormat =  this.currencyPattern;
              this.currencyGrid.CustomGrid(obj);
              if (this.jsonReader.getGridData().size > 0) {
                this.order = Number(this.jsonReader.getSingletons().maxOrdinal);
                this.listOrder = this.jsonReader.getSingletons().ordinaList;
                this.listOrder= this.listOrder.replace("[","").replace("]","");
                this.listOrder = this.listOrder.split(',').map(Number);
                this.currencyGrid.dataProvider = null;
                this.currencyGrid.gridData = this.jsonReader.getGridData();
                this.currencyGrid.setRowSize = this.jsonReader.getRowSize();
                this.currencyGrid.doubleClickEnabled = true;
              } else {
                this.currencyGrid.dataProvider = null;
                this.currencyGrid.selectedIndex = -1;
              }
            }
            // Condition to check data is not empty to enable or disable export icons
            if (this.jsonReader.getRowSize() < 1) {
              this.enableReportButton(false);
            } else {
              this.enableReportButton(true);
            }
            this.prevRecievedJSON = this.lastRecievedJSON;

          } else {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage());
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'inputDataResult', this.errorLocation);
    }
  }

  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    try {
      this.loadingImage.setVisible(true);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'startOfComms', this.errorLocation);
    }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    try {
      this.loadingImage.setVisible(false);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'endOfComms', this.errorLocation);
    }
  }

  /**
   * inputDataFault
   *
   * @param event:  FaultEvent
   *
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    try {
      let message = SwtUtil.getPredictMessage('label.genericException', null);
      this.swtAlert.error(message);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'inputDataFault', this.errorLocation);
    }
  }



  /**
   * logicUpdateResult
   *
   * @param event: ResultEvent
   *
   * Method to get result of the group rules
   */
  logicUpdateResult(event): void {
    try {
      const JsonResponse = event;
      const JsonResult: JSONReader = new JSONReader();
      JsonResult.setInputJSON(JsonResponse);
      if (JsonResult.getRequestReplyStatus()) {
        this.updateData();
        this.disableOrEnableButtons(false);
        this.addButton.setFocus();
      } else {
        this.swtAlert.error(JsonResult.getRequestReplyMessage());
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'logicUpdateResult', this.errorLocation);
    }
  }

  /**
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  public updateData(): void {
    try {
      this.currencyGrid.selectedIndex = -1;
      this.requestParams =[];
      // Define the action to send the request
      this.actionPath = 'currencyPCM.do?';
      // Define method the request to access
      this.actionMethod = 'method=display';
      this.inputData.url =  this.baseURL +  this.actionPath +  this.actionMethod;
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.inputData.send( this.requestParams);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'CurrencyMaintenance', 'updateData',  this.errorLocation);
    }
  }


  public refreshScreen(): void {
    try {
      this.currencyGrid.selectedIndex = -1;
      this.disableOrEnableButtons(false);
      this.requestParams =[];
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'CurrencyMaintenance', 'updateData',  this.errorLocation);
    }
  }


  private moduleReadyEventHandler(event, screenName) {
    this.win.addChild(event.target);
    this.win.display();
    this.win.onClose.subscribe(() => {
      console.log('res', this.win.getChild().result);
    }, error => {
      console.log(error);
    });
  }


  /**
   * doAddCurrency
   *
   * @param event: Event
   *
   * Method to open Add Currency  details screen
   */
  doAddCurrency(event): void {
    try {
      this.currencyGrid.selectedIndex = -1;
      this.disableOrEnableButtons(false);
      this.maxOrder=this.order  + 1;
      this.win = SwtPopUpManager.createPopUp(this);
      this.win.title= "Add Currency - Payment Control"; // childTitle,
      this.win.isModal = true;
      this.win.maxOrder=this.maxOrder;
      this.win.listOrder= this.listOrder;
      this.win.width = "450";
      this.win.height = "280";
      this.win.id = "currencyMaintenanceAdd";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win['screenName'] = 'add';
      this.win['maxOrder'] = this.maxOrder;
      this.win['listOrder'] = this.listOrder;
      const mLoader = new ModuleLoader(this.commonService);
      mLoader.addEventListener(ModuleEvent.READY, (event) => this.moduleReadyEventHandler(event, 'add'));
      mLoader.loadModule("currencyMaintenanceAdd");

    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'doAddCurrency', this.errorLocation);
    }
  }

  /**
   * doChangeCurrency
   *
   * @param event: Event
   *º
   * Method to open Change Currency  details screen
   */
  doChangeCurrency(event): void {
    try {
      this.maxOrder = this.order +1;
      this.win = SwtPopUpManager.createPopUp(this);
      this.win.title= "Change Currency - Payment Control"; // childTitle,
      this.win.isModal = true;
      this.win.maxOrder=this.maxOrder;
      this.win.listOrder= this.listOrder;
      this.win.width = "450";
      this.win.height = "280";
      this.win.id = "currencyMaintenanceAdd";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win['screenName'] = 'change';
      this.win['ccy'] = this.currencyGrid.selectedItem.ccy.content;
      this.win['ccyName'] = this.currencyGrid.selectedItem.ccyName.content;
      this.win['maxOrder'] = this.maxOrder;
      this.win['listOrder'] = this.listOrder;
      const mLoader = new ModuleLoader(this.commonService);
      mLoader.addEventListener(ModuleEvent.READY, (event) => this.moduleReadyEventHandler(event, 'change'));
      mLoader.loadModule("currencyMaintenanceAdd");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'doChangeCurrency', this.errorLocation);
    }
  }

  /**
   * doViewCurrency
   *
   * @param event: Event
   *
   * Method to open View Currency  details screen
   */
  doViewCurrency(event): void {
    try {
      this.win = SwtPopUpManager.createPopUp(this);
      this.win.title= "Details Currency - Payment Control"; // childTitle,
      this.win.isModal = true;
      this.win.maxOrder=this.maxOrder;
      this.win.listOrder= this.listOrder;
      this.win.width = "450";
      this.win.height = "280";
      this.win.id = "currencyMaintenanceAdd";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win['screenName'] = 'view';
      this.win['ccy'] = this.currencyGrid.selectedItem.ccy.content;
      this.win['ccyName'] = this.currencyGrid.selectedItem.ccyName.content;
      const mLoader = new ModuleLoader(this.commonService);
      mLoader.addEventListener(ModuleEvent.READY, (event) => this.moduleReadyEventHandler(event, 'view'));
      mLoader.loadModule("currencyMaintenanceAdd");

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'doViewCurrency', this.errorLocation);
    }
  }


  /**
   * doDeleteCurrency
   *
   * @param event: Event
   *
   * Method to  delete confirmation
   *
   */
  doDeleteCurrency(event): void {
    try {
      Alert.yesLabel = "Yes";
      Alert.noLabel = "No";

      this.swtAlert.confirm(this.message, 'Alert', Alert.OK | Alert.CANCEL, null, this.deleteCurrency.bind(this));
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'doDeleteCurrency', this.errorLocation);
    }
  }


  /**
   * deleteCurrency
   *
   * @param event: CloseEvent
   *
   * Method to remove selected currency
   */
  deleteCurrency(event): void {
    try {
      // Condition to check Ok Button is selected
      if (event.detail === Alert.OK) {
        this.requestParams =[];
        this.actionMethod="method=remove";
        this.requestParams['ccy'] = this.ccy;
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);
        this.currencyGrid.selectedIndex = -1;
        this.disableOrEnableButtons(false);

      }
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'deleteCurrency', this.errorLocation);
    }
  }



  /**
   * popupClosedEventHandler
   *
   * @param event :Event
   *
   * Method called when pop up is closed to Enable Buttons after pop up closed
   *
   */
  public popupClosedEventHandler(event): void {
    try {
      this.currencyGrid.selectable = true;
      this.currencyGrid.doubleClickEnabled = true;
      // data grid is selectable and a row is selected enable buttons
      if (this.currencyGrid.selectedIndices.length === 1 && this.currencyGrid.selectable) {
        // Condition to check menu access is not 0 to disable buttons
        this.disableOrEnableButtons(true);
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'popupClosedEventHandler', this.errorLocation);
    }
  }
  /**
   * doHelp
   *
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      // SwtHelpWindow.open(this.baseURL + "help.do?method=print&screenName=Currency+Maintenance");
      ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'CurrencyMaintenance', 'doHelp',   this.errorLocation);
    }
  }


  /**
   * cellClickEventHandler
   *
   * @param event: Event
   *
   * This method is used to manumberain the button status when a row is clicked
   */
  cellClickEventHandler(event): void {
    try {
      if (this.currencyGrid.selectedIndex >= 0 && this.currencyGrid.selectable) {
        this.ccy = this.currencyGrid.selectedItem.ccy.content;
        this.disableOrEnableButtons(true);
        event.stopPropagation();
        this.addButton.setFocus();
      } else {
        this.disableOrEnableButtons(false);
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'cellClickEventHandler', this.errorLocation);
    }
  }



  /**
   * report
   *
   * @param type: string
   *
   * This is a report icon action handler method
   */
  report(type: string): void {
    let selectedFilter: string = null;
    let selectedSort = '';
    try {
      if (this.currencyGrid.filteredGridColumns !=='') {
        selectedFilter = this.currencyGrid.getFilteredGridColumns();
      } else {
        selectedFilter = '';
      }
      selectedSort = this.currencyGrid.getSortedGridColumn();
      // set the action path
      this.actionPath = 'currencyPCM.do?';
      this.actionMethod = 'method=exportCurrency';
      this.requestParams["type"] = type;
      this.requestParams["selectedFilter"] = selectedFilter;
      this.requestParams["selectedSort"] = selectedSort;
      this.requestParams["print"] = 'ALL';
      this.logicUpdate.url=this.baseURL + this.actionPath + this.actionMethod;
      //    this.logicUpdate.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CurrencyMaintenance", "report");
    }
  }

  /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(Button)
   */
  keyDownEventHandler(event): void {
    try {
      const eventString = Object(focusManager.getFocus()).name;
      if ((event.keyCode === Keyboard.ENTER)) {
        if (eventString === 'addButton') {
          this.doAddCurrency(event);
        } else if (eventString === 'changeButton') {
          this.doChangeCurrency(event);
        } else if (eventString === 'viewButton') {
          this.doViewCurrency(event);
        } else if (eventString === 'closeButton') {
          this.closeCurrentTab(event);
        } else if (eventString === 'csv') {
          this.report('csv');
        } else if (eventString === 'excel') {
          this.report('xls');
        } else if (eventString === 'pdf') {
          this.report('pdf');
        } else if (eventString === 'helpIcon') {
          this.doHelp();
        } else if (eventString === 'deleteButton') {
          this.doDeleteCurrency(event);
        } else if (eventString === 'cancelButton') {
          this.reset(event);
        } else if (eventString === 'printButton') {
          this.printPage();
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'CurrencyMaintenance', 'keyDownEventHandler',   this.errorLocation);
    }
  }


  /**
   * reset
   * @param event: Event
   * Function called to reset data.
   */
  public reset(event): void {
    try {
      this.actionMethod = 'method=display';
      this.actionPath =  'currencyPCM.do?';
      this.requestParams = [];
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.cbResult = this.inputDataResult;
      this.inputData.send(this.requestParams);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'reset', this.errorLocation);
    }
  }

  /**
   * closeCurrentTab
   *
   * Function called when close button is called
   *
   * @param event:Event
   */
  closeCurrentTab(event): void {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'CurrencyMaintenance', 'closeCurrentTab', this.errorLocation);
    }
  }

  /**
   * dispose
   *
   * This is an event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.currencyGrid = null;
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this. menuAccess = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      this.child = null;
      ExternalInterface.call("close");
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'dispose', this.errorLocation);
    }
  }


  disableOrEnableButtons(isRowSelected: boolean): void {
    if(isRowSelected) {
      this.enableChangeButton(this.menuAccess == 0);
      this.enableViewButton(this.menuAccess < 2);
      this.enableDeleteButton(this.menuAccess == 0);
    } else {
      this.enableChangeButton(false);
      this.enableViewButton(false);
      this.enableDeleteButton(false);
    }
  }


  disableButtons() {
    if(this.currencyGrid.selectedIndex == -1) {
      this.disableOrEnableButtons(false);
    } else {
      this.disableOrEnableButtons(true);
    }
  }

  /**
   * enableReportButton
   *
   */
  enableReportButton(value): void {
    this.csv.enabled = value;
    this.csv.buttonMode = value;
    this.excel.enabled = value;
    this.excel.buttonMode = value;
    this.pdf.enabled = value;
    this.pdf.buttonMode = value;
    this.settingButton.enabled = value;
    this.settingButton.buttonMode = value;
    this.enablePrintButton(value);
  }

  /**
   * printPage
   *
   * param event
   *
   * Method to get call the action to get reports
   */
  printPage(): void {
    try {
      // set the action path
      this.actionPath= 'currencyPCM.do?';
      this.actionMethod="type=" + "pdf";
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod=this.actionMethod + "&currentModuleId=" + this.moduleId;
      this.actionMethod=this.actionMethod + "&print=" + "PAGE";
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "CurrencyMaintenance", "printPage", this.errorLocation);
    }
  }




  /**
   * enableAddButton
   *
   */
  enableAddButton(value: boolean): void {
    this.addButton.enabled = value;
    this.addButton.buttonMode = value;
  }
  /**
   * enableChangeButton
   *
   */
  enableChangeButton(value): void {
    this.changeButton.enabled = value;
    this.changeButton.buttonMode = value;
  }
  /**
   * enableViewButton
   *
   */
  enableViewButton(value: boolean): void {
    this.viewButton.enabled = value;
    this.viewButton.buttonMode = value;
  }
  /**
   * enableDeleteButton
   *
   */
  enableDeleteButton(value): void {
    this.deleteButton.enabled = value;
    this.deleteButton.buttonMode = value;
  }

  /**
   * enablePrintButton
   *
   */
  enablePrintButton(value): void {
    this.printButton.enabled = value;
    this.printButton.buttonMode = value;
  }

}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: CurrencyMaintenance }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CurrencyMaintenance],
  entryComponents: []
})
export class CurrencyMaintenanceModule {}
