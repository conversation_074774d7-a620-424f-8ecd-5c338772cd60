import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {
  SwtToolBoxModule,
  JSONReader,
  HTTPComms,
  Timer,
  SwtCommonGrid,
  CommonService,
  SwtModule,
  SwtAlert,
  SwtUtil,
  ExternalInterface,
  SwtButton,
  SwtDateField,
  SwtNumericInput,
  SwtLoadingImage,
  SwtLabel,
  focusManager,
  Alert,
  SwtComboBox,
  CommonUtil,
  StringUtils,
  SwtCanvas,
  Keyboard,
  SwtDataExport,
  ExportEvent,
  SwtRadioButtonGroup,
  genericEvent,
  SwtRadioItem,
  SwtTotalCommonGrid, ContextMenuItem, SwtPopUpManager, JSONViewer, ScreenVersion, EnhancedAlertingTooltip
} from 'swt-tool-box';
import moment from "moment";
import { Observable } from 'rxjs';
import 'rxjs/add/observable/fromEvent';
import { AlertingRenderer } from '../EnhancedAlerting/Render/AlertingRenderer';
declare var instanceElement: any;

declare var require: any;
const $ = require('jquery');
@Component({
  selector: 'currency-monitor',
  templateUrl: './CurrencyMonitor.html',
  styleUrls: ['./CurrencyMonitor.css'],
  encapsulation: ViewEncapsulation.None
})
export class CurrencyMonitor extends SwtModule implements OnInit {


  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('optionsButton') optionsButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;


  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;

  /***********SwtDateField***********/
  @ViewChild('fromDate') fromDate: SwtDateField;


  @ViewChild('showDays') showDays: SwtNumericInput;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;


  @ViewChild('dataBuildingText') dataBuildingText: SwtLabel;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('daysLabel') daysLabel: SwtLabel;


  @ViewChild('totalsContainer') totalsContainer: SwtCanvas;
  @ViewChild('displaycontainer') displaycontainer: SwtCanvas;


  @ViewChild('dataExport') dataExport: SwtDataExport;
  @ViewChild('breakdown') breakdown: SwtRadioButtonGroup;

  
  @ViewChild('accountRadio') accountRadio: SwtRadioItem;
  @ViewChild('movementRadio') movementRadio: SwtRadioItem;
  @ViewChild('bookRadio') bookRadio: SwtRadioItem;
  @ViewChild('meatagroupRadio') meatagroupRadio: SwtRadioItem;
  @ViewChild('groupRadio') groupRadio: SwtRadioItem;


  /**
         * Display Objects
         **/
  private currencyGrid: SwtCommonGrid;
  private totalsGrid: SwtTotalCommonGrid;

  /**
   * Data Objects
   **/
  private jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;


  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public alertingData = new HTTPComms(this.commonService);
  private updateRefreshRate = new HTTPComms(this.commonService);
  // Instantiates HTTPComms
  private updateFontSize = new HTTPComms(this.commonService);

  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private invalidComms: string = "";
  private showJSONPopup: any;
  public screenVersion  = new ScreenVersion(this.commonService);

  /**
   * Timer Objects
   **/
  //Main Timer. 
  private autoRefresh: Timer;
  private refreshRate = 10;

  private refreshStatus: string = "N";
  //Background refresh
  //FIXME:
  private backGroundTimer: Timer; //:BackgroundTimer=new BackgroundTimer();

  /**
   * Logic Objects
   **/
  //private currencyLogic:CurrencyLogic;

  private comboOpen: boolean = false;
  private datePickerOpen: boolean = false;
  private comboChange: boolean = false;

  // code added by Sudhakar on 10-6-2011 for Mantis 1376:Monitors: Display a sensible message when user has no currency access
  // Variable Declaration for dateFlag 
  private dateFlag: string;
  private currencyPattern: string;

  /**
   * Popup Objects
   **/
  // private showXML:ShowXML;
  // private refreshRatePopup:RefreshPopUp;

  // private commonLogic:CommonLogic=new CommonLogic;
  /* - START -- Screen Name and Version Number ---- */
  private screenName = 'Currency Monitor';
  /* Screenversion- Changed from 1.1.0031 to 1.1.0032 for Mantis 1416 & Mantis 1308:Show 'Last refresh' time in screens that have refresh function by Venkat on 30-MAR-2011 */
  /* Screenversion- Changed from 1.1.0032 to 1.1.0033 for Mantis 1462:Input Exception : Problem while generating report while Interface Id has special characters by chidambaram on 20-MAY-2011 */
  /* Screenversion- Changed from 1.1.0033 to 1.1.0034 for Mantis 1386: "User defined option to show normal and small fonts" by Marshal on 19-May-2011*/
  /* Screenversion- Changed from 1.1.0034 to 1.1.0035  for Mantis 1376 Monitors: Display a sensible message when user has no currency access by Sudhakar on 10-6-2011  */
  /* Screenversion- Changed from 1.1.0035 to 1.1.0036  for Mantis 1413 - SwtToolbox changes for Forecast Monitor by Bala on 09-Aug-2011*/
  /* Screenversion- Changed from 1.1.0036 to 1.1.0037  for Mantis 1775 - Avoid validations on click of close button  by Imed B on 28-03-2012  */
  /* Screenversion- Changed from 1.1.0037 to 1.1.0038  for Mantis 1967:  Improving and integrating SWFProfiler tool on all Flex screens on 06/09/2012 by KaisBS */
  /* Screenversion- Changed from 1.1.0038 to 1.1.0039  for Mantis 2016: Enhanced date range selection on all screens with a From and To Date field on 08/10/2012 by KaisBS */
  /* Screenversion- Changed from 1.1.0039 to 1.1.0040 for Mantis 2015: 'Data Build In Progress' when fetching dates not currently available on 06/11/2012 by KaisBS */

  private versionNumber: string = "1.1.0040";
  /* - END -- Screen Name and Version Number ---- */
  private systemDate: string;
  private dateCompare: string = "";

  // Initializes fontValue
  private fontValue: string = "";
  // Initializes fontLabel
  private fontLabel: string = "";
  // Initializes fontRequest
  private fontRequest: string = "";
  // Initializes currencyFontSize
  private currentFontSize: string = "";
  // Declares selectedFont
  private selectedFont;
  // Initializes tempFontSize
  private tempFontSize: string = "";

  // Added for Mantis 2016 By KaisBS and Meftah
  private d1: Date;
  private d2: Date;
  private prevChosenDate: Date;
  private prevNumberOfDays: string;
  private dateChanged: Boolean = false;
  private swtAlert: SwtAlert;
  private dateFormat;

  private showBuildInProgress = false;

  public errorLocation = 0;
  public moduleId = "Predict";
  tooltipEntityId = null;
  tooltipCurrencyCode = null;
  tooltipFacilityId = null;
  tooltipSelectedDate = null;
  tooltipOtherParams = [];
  private positionX:number;
  private positionY:number;
  private hostId;
  private entityId;
  private currencyId;
  private selectedNodeId = null;
  private treeLevelValue = null;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  ngOnDestroy(): any {
    instanceElement = null;
  }



  ngOnInit() {
    instanceElement = this;
    this.entityCombo.toolTip= ExternalInterface.call('getBundle','tip', 'entity', 'Select an Entity ID');
    this.ccyCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'currency', 'Select Currency Group');
    this.showDays.toolTip = ExternalInterface.call('getBundle', 'tip', 'showdays', 'Number of days to show');
    this.optionsButton.label = ExternalInterface.call('getBundle', 'text', 'button-options', 'Options');
    this.optionsButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-options', 'Change options');


    this.refreshButton.label = ExternalInterface.call('getBundle', 'text', 'button-refresh', 'Refresh window');
    this.refreshButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-refresh', 'Refresh');
    
    this.closeButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Close');
    this.closeButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-close', 'Close window');



    this.accountRadio.toolTip = ExternalInterface.call('getBundle', 'tip', 'acctbrkdown', 'Select Account to view Account Monitor');
    this.accountRadio.label = ExternalInterface.call('getBundle', 'text', 'acctbrkdown', 'Account');
    
    this.movementRadio.toolTip = ExternalInterface.call('getBundle', 'tip',  'mvmntbrkdown', 'Select Movement to view Movement Summary Detail');
    this.movementRadio.label = ExternalInterface.call('getBundle', 'text', 'mvmntbrkdown', 'Movement');

    this.bookRadio.toolTip = ExternalInterface.call('getBundle', 'tip', 'bookbrkdown', 'Select Book to view Book Monitor');
    this.bookRadio.label = ExternalInterface.call('getBundle', 'text', 'bookbrkdown', 'Bookcode');

    
    this.groupRadio.toolTip = ExternalInterface.call('getBundle', 'tip', 'grpmonitor', 'Select Group to view Group Monitor');
    this.groupRadio.label = ExternalInterface.call('getBundle', 'text', 'grpmonitor', 'Group');
    
    this.meatagroupRadio.toolTip = ExternalInterface.call('getBundle', 'tip', 'mtagrpmonitor', 'Select Metagroup to view Metagroup Monitor');
    this.meatagroupRadio.label = ExternalInterface.call('getBundle', 'text', 'mtagrpmonitor', 'Metagroup');
      

  }




  /**
			 * Upon completion of loading into the flash player this method is called
			 **/
  onLoad(): void {
    this.currencyGrid = <SwtCommonGrid>this.displaycontainer.addChild(SwtCommonGrid);
    this.totalsGrid = <SwtTotalCommonGrid>this.totalsContainer.addChild(SwtTotalCommonGrid);
    this.totalsGrid.initialColumnsToSkip = 1;
    this.currencyGrid.uniqueColumn = "ccy";
    this.totalsGrid.selectable = false;
    this.currencyGrid.columnWidthChanged.subscribe((event) => {
      this.resizeGrids(event);
    });
    this.currencyGrid.columnOrderChanged.subscribe((event) => {
      this.resizeGrids(event);
    });
    Observable.fromEvent(document.body, 'click').subscribe(e => {
      this.positionX=e["clientX"];
      this.positionY=e["clientY"];
    });
    this.currencyGrid.listenHorizontalScrollEvent = true;
    this.totalsGrid.fireHorizontalScrollEvent = true;
    this.totalsGrid.lockedColumnCount = 2;
    this.currencyGrid.lockedColumnCount = 2;
    //Initialize the context menu
    this.initializeMenus();
    //disable option Button in the fisrt loading of data.
    this.optionsButton.enabled = false;
    /*
    The testdate will be available (always) in the JSP page which loads the flash file:
    inputConfigLogic.testDate = ExternalInterface.call('eval','testDate');
    */


    //FIXME:
    this.dateFormat = ExternalInterface.call('eval', 'dateFormat').toLowerCase();

    // currencyLogic.dateFormat=ExternalInterface.call('eval', 'dateFormat');
    this.systemDate = ExternalInterface.call('eval', 'dbDate');
    // currencyLogic.testDate=systemDate;


    if (this.dateFormat == "dd/mm/yyyy") {
      this.fromDate.toolTip = ExternalInterface.call('getBundle', 'tip', 'datefromDDMMYY', 'Enter value date (\'dd/mm/yyyy\')');
    }
    else {
      this.fromDate.toolTip = ExternalInterface.call('getBundle', 'tip', 'datefromMMDDYY', 'Enter value date (\'dd/mm/yyyy\')');
    }
    this.showDays.toolTip = ExternalInterface.call('getBundle', 'tip', 'showdays', 'Number of days to show');


    //Initialize the communication objects
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.inputDataResult(data);
    };

    // this.inputDataResult(data);

    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    // For accessing a local file

    //For accessing an action on the context
    //first declare the actionPath:				
    this.actionPath = "currmonitorNew.do?";

    //Even if no method name is given the unspecified method in action class will be 
    //executed,inorder to avoid the Invalid Chunck ignored warning in the server log,
    //method name unspecified is explicitly called. 					
    this.actionMethod = "method=unspecified";


    //Add the event listener to listen for a cell click on a datagrid, be it the main one or a totals grid


    /*this.addEventListener("CellClick", cellLogic, true);*/

    // this.currencyGrid.onRowClick = (event) => {

    //   this.cellLogic(event);
    // };

    // this.currencyGrid.cellLinkClick.subscribe((selectedRowData) => {
    //   this.cellLogic(selectedRowData);
    // });

    this.currencyGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.itemClickFunction(selectedRowData);
      this.cellLogic(selectedRowData);
    });

    // this.currencyGrid.addEventListener(genericEvent.CELL_CLICK, (cell) => {
    //   if (cell.getItemRander().type === "link:num" ) {
    //   }
    // }

    //Event listener to listen out for the background timer



    //TODO: Fix those commented lines
    //call back method to restart the auto-refresh process
    // ExternalInterface.addCallback("AutoRefreshStart", this.startAutoRefresh);
    // this.backGroundTimer.addEventListener("BackgroundTimer", (function handle(evt:Event):void
    // {
    //   this.updateData('no')
    // }));

    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    //Make initial request
    this.requestParams["systemDate"] = this.systemDate;

    this.inputData.send(this.requestParams);

    ExportEvent.subscribe((type) => {
      this.export(type)
    });

  }
  resizeGrids(event) {
    try {
      this.totalsGrid.setRefreshColumnWidths(this.currencyGrid.gridObj.getColumns());

    } catch(e) {
      console.log("resizeGrids", e)
    }

  }

  private lastSelectedTooltipParams = null;
  getParamsFromParent() {
    const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId, selectedNodeId:this.selectedNodeId, treeLevelValue:this.treeLevelValue ,
      tooltipCurrencyCode :this.tooltipCurrencyCode , tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate};

    return params;
  }

  private eventsCreated = false;
  private customTooltip: any = null;
  public createTooltip (){
      if(this.customTooltip && this.customTooltip.close)
        this.removeTooltip();
      try {    
      const toolTipWidth = 410;
      this.customTooltip = SwtPopUpManager.createPopUp(parent, EnhancedAlertingTooltip, {
      });
      this.customTooltip.enableResize = false;
      this.customTooltip.width = ''+toolTipWidth;
      this.customTooltip.height = "450";
      this.customTooltip.enableResize = false;
      this.customTooltip.title = "Alert Summary Tooltip";
      this.customTooltip.showControls = true;
      if (window.innerHeight < this.positionY+450)
        this.positionY=200;
      this.customTooltip.setWindowXY(this.positionX+20, this.positionY);
      this.customTooltip.showHeader = false;
      this.customTooltip.parentDocument = this;
      this.customTooltip.processBox = this;
      this.customTooltip.display();
      //event for display list button click
      setTimeout(() => {

        if (!this.eventsCreated) {
          this.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe((target) => {
            this.lastSelectedTooltipParams = target.noode.data
            ExternalInterface.call("openAlertInstanceSummary", "openAlertInstSummary", this.selectedNodeId, this.treeLevelValue);
          });
        }
      }, 0);

        //event for link to specific button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe((target) => {
              this.getScenarioFacility(target.noode.data.scenario_id);
              this.lastSelectedTooltipParams = target.noode.data
              this.hostId= target.hostId;
              this.entityId = this.lastSelectedTooltipParams.ENTITY;
              this.currencyId= this.lastSelectedTooltipParams.CCY;
            });
          }
        }, 0);

        //event for tree item click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().ITEM_CLICK.subscribe((target) => {
              this.selectedNodeId= target.noode.data.id;
              this.treeLevelValue= target.noode.data.treeLevelValue;
              this.customTooltip.getChild().linkToSpecificButton.enabled=false; 
              // if(target.noode.data.count ==1 && target.noode.isBranch ==false ) {
              // this.customTooltip.getChild().linkToSpecificButton.enabled=true; 
              // }
            });
          }
        }, 0);

      } catch (error) {
          console.log("SwtCommonGrid -> createTooltip -> error", error)
              
      }
  }

  getScenarioFacility(scenarioId) {
    this.requestParams = [];
    this.alertingData.cbStart = this.startOfComms.bind(this);
    this.alertingData.cbStop = this.endOfComms.bind(this);
    this.alertingData.cbFault = this.inputDataFault.bind(this);
    this.alertingData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "scenarioSummary.do?";
    // Define method the request to access
    this.actionMethod = 'method=getScenarioFacility';
    this.requestParams['scenarioId'] = scenarioId;
    this.alertingData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.alertingData.cbResult = (event) => {
      this.openGoToScreen(event);
    };
    this.alertingData.send(this.requestParams);
  }

  openGoToScreen(event){
    if(event && event.ScenarioSummary && event.ScenarioSummary.scenarioFacility){
    var facilityId = event.ScenarioSummary.scenarioFacility;

    const selectedEntity = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['entity_id'] != null?this.lastSelectedTooltipParams['entity_id']:this.tooltipEntityId;
    const selectedcurrency_code = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['currency_code'] != null?this.lastSelectedTooltipParams['currency_code']:this.tooltipCurrencyCode;
    const selectedmatch_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['match_id'] != null?this.lastSelectedTooltipParams['match_id']:null
    const selectedmovement_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['movement_id'] != null?this.lastSelectedTooltipParams['movement_id']:null
    const selectedsweep_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['sweep_id'] != null?this.lastSelectedTooltipParams['sweep_id']:null


    ExternalInterface.call("goTo", facilityId, this.hostId, selectedEntity, selectedmatch_id, selectedcurrency_code, selectedmovement_id, selectedsweep_id, "");
    }

  }

  public removeTooltip (){
    if(this.customTooltip != null)
      this.customTooltip.close();
  }


  itemClickFunction(event) {
    if (event.target != null && event.target.field != null && event.target.field == "alerting" && (event.target.data.alerting=="C" || event.target.data.alerting=="Y")) {
      this.tooltipCurrencyCode = event.target.data.ccy;
      this.tooltipEntityId = this.entityCombo.selectedLabel;
      this.tooltipFacilityId = "CURRENCY_MONITOR_CCY_ROW";
      this.tooltipSelectedDate = null;
      this.tooltipOtherParams = [];
      setTimeout(() => {
        this.createTooltip();
      }, 100);
    }else {
      this.removeTooltip();
    }
  }

  private cellLogic(e): void {

    try {
      if (e != null) {
        let fieldName = e.target.field;
        let isClickable = ( e.target.data.slickgrid_rowcontent[fieldName]) ? e.target.data.slickgrid_rowcontent[fieldName].clickable : "";
        let selectedCcy = e.target.data.ccy.substring(0,3);

        if (isClickable) {
          this.d1 = new Date(this.fromDate.selectedDate);
          this.d2 = new Date(this.fromDate.selectedDate);
          this.d2.setDate(this.d2.getDate() + parseInt(this.showDays.text) - 1);


          var sD1: String = "";
          var sD2: String = "";

          if (this.dateFormat.toLowerCase() === "dd/mm/yyyy") {
            // sD1 = CommonUtil.formatDate(this.d1, "DD/MM/YYYY");
            sD1 = CommonUtil.formatDate(this.d1, "DD/MM/YYYY");
            sD2 = CommonUtil.formatDate(this.d2, "DD/MM/YYYY");

          }
          else {
            // sD1 = CommonUtil.formatDate(this.d1, "MM/DD/YYYY")
            sD1 = CommonUtil.formatDate(this.d1, "MM/DD/YYYY");
            sD2 = CommonUtil.formatDate(this.d2, "MM/DD/YYYY");
          }

          if (fieldName == "loro") {


            this.clickLoro(this.entityCombo.selectedLabel, selectedCcy, sD1);
          }
          else {

            var selectedDateFromEvent =  e.target.data.slickgrid_rowcontent[fieldName].date;;
            this.clickLink(this.entityCombo.selectedLabel, selectedCcy, selectedDateFromEvent, (this.breakdown.selectedValue).toString());
          }
        }
      }
    } catch (e) {
      console.log("e", e);

    }

  }

  clickLoro (sEntityId :String, sCurrencyCode :String, sFromDate :String) :void {
    ExternalInterface.call("clickLoro", sEntityId, sCurrencyCode, sFromDate);
    //clickLoro (sEntityId, sCurrencyCode, sFromDate)
  }
  
  
  clickLink ( sEntityId :String, sCurrencyCode :String, sColumnDate :String, sActionCode :String) :void { 
    
    ExternalInterface.call("clickLink", sEntityId, sCurrencyCode, sColumnDate, sActionCode);
  }

  /**
   * auto refresh initializer method
   *
   **/
  startAutoRefresh(refreshStart: String): void {
    if (refreshStart == "Y") {
      this.updateData('no', true);
      this.autoRefresh.start();
    }
  }

  /**
     * Added by Mefteh for Mantis 2016
     * this method is called on clicking on refresh Button
     **/
  updateDatafromRefresh(event): void {

    if ((Object(focusManager.getFocus()).id == "showDays" || (Object(focusManager.getFocus()).id == "startDate"))) {
      setTimeout(() => {
        this.updateData('yes')
      }, 0)
    }
    else if ((Object(focusManager.getFocus()).id != "showDays" && Object(focusManager.getFocus()).id != "startDate")) {
      setTimeout(() => {
        this.updateData('yes', true)
      }, 0)
    }
  }

  /**
			 * Timing result methods
			 **/
  dataRefresh(event): void {

    this.refreshStatus = "Y";
    //Check on the comboOpen flag, do not want to update if there is a combobox open as this would cause it to close
    if (!this.comboOpen && !this.datePickerOpen) {
      if ((Object(focusManager.getFocus()).id == "showDays" || Object(focusManager.getFocus()).id == "fromDate") && this.validateDate(null, this.fromDate) && this.validateNumberOfDays(this.showDays)) {
        if ((this.showDays.text != this.prevNumberOfDays) || (this.fromDate.selectedDate.toDateString() != this.prevChosenDate.toDateString()))
          setTimeout(() => {
            this.updateData('yes')
          }, 0)
        else
          setTimeout(() => {
            this.updateData('yes', true)
          }, 0)

        this.refreshButton.setFocus();
      }
      else if (Object(focusManager.getFocus()).id != "showDays" && Object(focusManager.getFocus()).id != "fromDate" && this.validateDate(null, this.fromDate) && this.validateNumberOfDays(this.showDays))
        this.updateData('yes', true);

    }
    this.autoRefresh.stop();
    // this.dataExport.enabled = false;
  }


  /**
   * Update the data, this is called whenever a fresh of the data is required.
   * This could be called from either a change in a combobox selection of from the timer
   **/
  // Changed by KaisBS and Meftah for mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
  /* 
  The update of data is realized in two cases: 
  1- If we don't have an excess of the date range.
  2- If we have the excess of the date range and the user clicks OK from the displayed alert "The data for this date range selection may not be available...".
  */
  updateData(autorefresh: string, fromCheckDateRange: Boolean = false, cancelled: Boolean = false): void {
    this.d1 = new Date(this.fromDate.selectedDate);
    this.d2 = new Date(this.fromDate.selectedDate);
    this.d2.setDate(this.d2.getDate() + parseInt(this.showDays.text) - 1);
    let d1AsMoment = moment(this.fromDate.text, this.dateFormat.toUpperCase());
    let d2AsMomnet = d1AsMoment.add(Number(this.showDays.text) -1 , 'days');

    // If the call of updateData is from CommonLogic.checkDateRange and the user chose the cancel button from the displayed alert, 
    // then stay focus on text field and do not continue
    if (cancelled) {

      this.fromDate.selectedDate = this.prevChosenDate;
      this.d1 = this.prevChosenDate;
      this.showDays.text = this.prevNumberOfDays;
      this.d2.setDate(this.d2.getDate() + parseInt(this.showDays.text) - 1);
      this.showDays.setFocus();
      return;
    }

    // If the call of 'updateData' is not from CommonLogic.checkDateRange and the call to commonLogic.checkDateRange returned a dateRangeExceeded=true, then do not continue
    // I.e give the hand to commonLogic.checkDateRange to perform the data update...
    if (!fromCheckDateRange && this.checkDateRange(autorefresh, d1AsMoment, d2AsMomnet, this.showDays, this.systemDate, this.dateFormat, this.updateData)){
      return;
    }

    /*if(fromCheckDateRange) {
      this.showBuildInProgress = false;
    }*/
    this.prevChosenDate = this.d1;
    this.requestParams = [];

    /*
    First step is to obtain the selection values of any objects that could be selected.
    These are then placed in the "requestParams" object
    In this case of this example it will be the dropdown boxes and tick boxes
    */




    var sD1: string = "";
    var sD2: string = "";
    var pattern: RegExp = null;
    //Get the currency group
    var currGrp: string = this.ccyCombo.selectedItem.content;
    //Get the entity group
    var entityId: string = this.entityCombo.selectedItem.content;
    if (autorefresh == null)
      this.dateFlag = "fromDatechange";
    else {
      this.dateFlag = "toDatechange";
      var daysInMilliseconds: number = 1000 * 60 * 60 * 24;

      // Added by KaisBS for Mantis 1868 (issue 1054_SEL_091)
      // alert the warning if the user select a date out of range according to the default days
      if (Math.round((this.d2.getTime() - this.d1.getTime()) / daysInMilliseconds + 1) > 14) {
        //FIXME:
        this.swtAlert.show("To Date is not on default days range", 'Warning');
      }
    }


    if (this.d1 && this.d2) {


      if (this.dateFormat.toLowerCase() === "dd/mm/yyyy") {

        /*   sD1 = this.d1 ? (this.d1.getDate() < 10 ? "0" + this.d1.getDate() : this.d1.getDate()) + "/" + (this.d1.getMonth() + 1 < 10 ? "0" + (this.d1.getMonth() + 1) : this.d1.getMonth() + 1) + "/" + this.d1.getFullYear() : null;
           sD2 = this.d2 ? (this.d2.getDate() < 10 ? "0" + this.d2.getDate() : this.d2.getDate()) + "/" + (this.d2.getMonth() + 1 < 10 ? "0" + (this.d2.getMonth() + 1) : this.d2.getMonth() + 1) + "/" + this.d2.getFullYear() : null;
   */
        sD1 = CommonUtil.formatDate(this.d1, "DD/MM/YYYY");
        sD2 = CommonUtil.formatDate(this.d2, "DD/MM/YYYY");
        pattern = /^(0[1-9]|[12][0-9]|3[01]|[1-9])\/(0[1-9]|1[012]|[1-9])\/(\d{4}|\d{2})$/

      }
      else {
        /*   sD1 =this.d1 ? ((this.d1.getMonth() + 1) < 10 ? "0" + (this.d1.getMonth() + 1) : (this.d1.getMonth() + 1)) + "/" + (this.d1.getDate() < 10 ? "0" + (this.d1.getDate()) :this.d1.getDate()) + "/" +this.d1.getFullYear() : null;
           sD2 = this.d2 ? ((this.d2.getMonth() + 1) < 10 ? "0" +(this.d2.getMonth() + 1) :(this.d2.getMonth() + 1)) + "/" +(this.d2.getDate() < 10 ? "0" +(this.d2.getDate()) : this.d2.getDate()) + "/" + this.d2.getFullYear() : null;
   */
        sD1 = CommonUtil.formatDate(this.d1, "MM/DD/YYYY")
        sD2 = CommonUtil.formatDate(this.d2, "MM/DD/YYYY")

        pattern = /^(0[1-9]|1[012]|[1-9])\/(0[1-9]|[12][0-9]|3[01]|[1-9])\/(\d{4}|\d{2})$/
      }

      if (pattern.test(this.fromDate.text).toString() == "true") {
        /*if (autorefresh != null) {
          if (this.calculateDays(this.d1, this.d2) > 13) { //if the user has picked more thqan 14 days. Limit to 14 days
            this.d2.setTime(this.d1.getTime() + (daysInMilliseconds * 13));
            if (this.fromDate.formatString == "dd/mm/yyyy") {
              sD2 = this.d2 ?(this.d2.getDate() < 10 ? "0" + this.d2.getDate() : this.d2.getDate()) + "/" +(this.d2.getMonth() + 1 < 10 ? "0" +(this.d2.getMonth() + 1) : this.d2.getMonth() + 1) + "/" + this.d2.getFullYear() : null;

            }
            else {
              sD2 = this.d2 ? ((this.d2.getMonth() + 1) < 10 ? "0" +(this.d2.getMonth() + 1) :(this.d2.getMonth() + 1)) + "/" +(this.d2.getDate() < 10 ? "0" +(this.d2.getDate()) : this.d2.getDate()) + "/" + this.d2.getFullYear() : null;

            }
          }
        }*/
        this.requestParams["currencyMonitor.fromDateAsString"] = sD1;

        this.requestParams["currencyMonitor.toDateAsString"] = sD2;
        if (autorefresh != null)
          this.requestParams["autoRefresh"] = autorefresh;
        this.requestParams["systemDate"] = this.systemDate;
        //code added by Sudhakar on 10-6-2011 for Mantis 1376:Monitors: Display a sensible message when user has no currency access
        //set the dateFlag in requestparams 
        this.requestParams["dateFlag"] = this.dateFlag;

        // Added by KaisBS for Mantis 1868 (issue 1054_SEL_091)
        // Changes in Monitor options not reflected in currency monitor screen 
        this.requestParams["defaultDays"] = 14;
      }
    }

    this.requestParams["currencyMonitor.entityId"] = entityId;
    this.requestParams["currencyMonitor.currGrp"] = currGrp;
    this.inputData.send(this.requestParams);

  }


  /**
		 * This function validates the date field - Mantis 1262. <br>
		 * The date field is an editable one where the user can type the desired date.<br>
		 * The date is taken as an argument and it is validate against certain rules.<br>
		 *
		 * Author: Marshal.<br>
		 * Date: 19-10-2010<br>
		 */
  //FIXME:   Fix method!
  validateDate(event: FocusEvent, startDay: SwtDateField){
    try{
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      if(startDay.text) {

        date = moment(startDay.text, this.dateFormat.toUpperCase() , true);

        if(!date.isValid()) {
          //this.swtAlert.warning(alert+ this.dateFormat.toUpperCase());
          this.swtAlert.error(alert, null, null, null, () => {
            this.setFocusDateField(startDay)
          });
          this.autoRefresh.stop();
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          this.setFocusDateField(startDay);
        });
        this.autoRefresh.stop();
        return false;
      }
      startDay.selectedDate = date.toDate();
    }catch(error){
      console.log('error in validateDate', error);
    }

    return true;
  }
  setFocusDateField(startDay) {
    startDay.setFocus();
    startDay.text = this.jsonReader.getScreenAttributes()["from"];
  }

  formatDate(date: Date, format: String) {
    if (format && date) {

      if (format.toLowerCase() === "dd/mm/yyyy") {
        return (date.getDate()) + "/" + (date.getMonth() + 1) + "/" + date.getFullYear();
      } else {

        return (date.getMonth() + 1) + "/" + (date.getDate()) + "/" + date.getFullYear();
      }
    } else
      return "";
  }


  /**
 * it validates the number of days entered in the showdays text field   
 **/
  validateNumberOfDays(showDays): Boolean {

    var parsedDays = parseInt(showDays.text);


    if (isNaN(parsedDays) || parsedDays <= 0) {
      this.showAlertForNumberOfDays(showDays);
      return false;
    } else
      return true;

  }
  // Added by KaisBS ans Mefteh for mantis 2016: Enhanced date range selection on all screens with a From and To Date field
  /**
   * Displays an alert if the user choses an invalid show days value
   **/
  showAlertForNumberOfDays(showDays): void {
    this.swtAlert.show(" 'Show' value must be between 1 and 14. ", "Error", Alert.OK, null, this.showDayFocus.bind(this));
  }
  showDayFocus(event) {
    if(event.detail == Alert.OK) {
      this.showDays.setFocusAndSelect();
      if (this.autoRefresh != null) {
        this.autoRefresh.stop();
      }
    }
  }

  /**
 * Part of a callback function to all for control of the loading swf from the HTTPComms Object
 **/
  startOfComms(): void {
    this.loadingImage.setVisible(true);
    // Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
    if (this.showBuildInProgress == true)
      this.dataBuildingText.visible = true;
    else
      this.dataBuildingText.visible = false;
    this.disableInterface();

    this.fromDate.enabled = false;
    this.showDays.enabled = false;
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.enableInterface();
    this.fromDate.enabled = true;
    this.showDays.enabled = true;
  }
  /**
 * Disable interface, turn off certain UI elements when a request is made to the server
 **/
  disableInterface(): void {
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
  }
  /**
  * Enable interface, turn on certain UI elements when a request is made to the server
  **/
  enableInterface(): void {
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
  }

  //FIXME:Add this method inputDataFault
  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  // inputDataFault(event): void {
  // {
  // 	this.lostConnectionText.visible=true;
  // 	this.invalidComms=event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  // 	// If autoRefresh is not equal to null, start the timer.
  // 	if (this.autoRefresh != null)
  // 	{
  // 		if (!this.autoRefresh.running)
  // 		{
  // 			this.autoRefresh.start();
  // 		}
  // 	}
  // }




  /* Start:code added by Sudhakar on 10-6-2011 for Mantis 1376:Monitors: Display a sensible message when user has no currency access */
  /**
   * Show Alert Message when user has no currency access
   **/
  showAlertCurrencyAccess(): void {
    this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert.currencyAccess', 'Invalid: your role does not specify access to currencies/groups for this entity'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
  }

  /**
			 * Communication Result Methods
			 **/
  inputDataResult(data): void {
    let jsonList = null;
    let header: string;
    let obj;
    // Get result as xml
    this.lastRecievedJSON = data;

    // Parse result json
    this.jsonReader.setInputJSON(this.lastRecievedJSON);

    var refreshGridHeader: boolean = false;

    //If the code has reached this point then a successful request was made, turn off the lostConnectionText
    this.lostConnectionText.visible = false;

    // Condition to check request reply status is true
    //test the reply status first, as if the reply status is false then the data will not be valid
    if (this.jsonReader.getRequestReplyStatus()) {
      {
        //Some monitors have a database job that runs to build the data. If this is running then the databuilding flag will be set 
        //If the previousXML is different to new xml then allow an update. Otherwise leave it alone
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {

          //Date format needs to be set first on the logic object
          this.fromDate.formatString = this.dateFormat;

          // enable the option Button after the first loading of data  
          if (!this.optionsButton.enabled)
            this.optionsButton.enabled = true;

          // Gets the refreshRate from xmlReader
          this.refreshRate = parseInt(this.jsonReader.getRefreshRate());
          this.systemDate = this.jsonReader.getScreenAttributes()["sysDateFrmSession"];//xmlReader.getScreenAttributes()["sysDateFrmSession"];


          this.dateCompare = (this.jsonReader.getScreenAttributes()["dateComparing"]);

          let lastRefAsString: string = this.jsonReader.getScreenAttributes()["lastRefTime"];
          lastRefAsString = this.convertFromUnicodeToString(lastRefAsString);
          this.lastRefTime.text = lastRefAsString;
          //this code is hidden by Fatma because when changing entity the holidays header are not updated
          /*if (dateCompare == "true")
          {
            refreshGridHeader=true;
          	
          }*/
          this.currencyPattern = (this.jsonReader.getScreenAttributes()["currencyFormat"]);
          refreshGridHeader = true;
          this.fromDate.showToday = false;
          this.d1 = moment(this.jsonReader.getScreenAttributes()["from"], this.dateFormat.toUpperCase()).toDate();

          // this.d1 = new Date(CommonUtil.dateFromString(this.jsonReader.getScreenAttributes()["from"], "dd/mm/yyyy"));

          this.prevChosenDate = this.d1;
          this.d2 = moment(this.jsonReader.getScreenAttributes()["to"], this.dateFormat.toUpperCase()).toDate();
          this.fromDate.selectedDate = this.d1;

          var daysInMilliseconds: number = 1000 * 60 * 60 * 24;
          this.showDays.text = Math.round((this.d2.getTime() - this.d1.getTime()) / daysInMilliseconds + 1).toString();
          this.prevNumberOfDays = this.showDays.text;
          this.updateDayLabel();
          this.entityCombo.setComboData(this.jsonReader.getSelects(), false);
          this.selectedEntity.text = this.entityCombo.selectedValue;
          // START: added by Meftah for Mantis 1762: task 1054_SEL_079:When ‘All’ Entity is selected:the Currenty Group is selected to ‘All’ and is disabled. 
          if (this.entityCombo.selectedItem == "All") {
            this.ccyCombo.selectedItem = "All";
            this.selectedCcy.text = "All";
            this.ccyCombo.enabled = false;
          }
          else {
            this.ccyCombo.setComboData(this.jsonReader.getSelects(), false);
            this.selectedCcy.text = this.ccyCombo.selectedValue;
            this.ccyCombo.enabled = true;
          }
          // END: added by Meftah for Mantis 1762: task 1054_SEL_079.							
          /* Start:code added by Sudhakar on 10-6-2011 for Mantis 1376:Monitors: Display a sensible message when user has no currency access */

          // Modified by KaisBS for mantis 2004: message not being displayed when user has no access to currencies
          if (StringUtils.trim(this.ccyCombo.selectedLabel) == "") {
            //check the condition autoRefresh is not equal to null
            if (this.autoRefresh != null) {
              this.autoRefresh.stop();
            }
            this.comboOpen = true;
            setTimeout(() => {
              this.showAlertCurrencyAccess();
            }, 0)
          }

          /* End:code added by Sudhakar on 10-6-2011 for Mantis 1376:Monitors: Display a sensible message when user has no currency access */
          /*
          If the main datagrid has not been initialize, for example it is the first time data as been recieved then initialise it
          and add it to the appropriate display container.
          */


          if (!StringUtils.isTrue(this.jsonReader.getScreenAttributes()["databuilding"])) {
            //If the code has reached this point then the database is not databuilding, turn off the dataBuildingText
            this.dataBuildingText.visible = false;
            // if (this.currencyGrid == null /*|| selectedEntity.text == "All"*/)
            // {
            jsonList = this.jsonReader.getColumnData();
            for (let i = 0; i < jsonList.column.length; i++) {
              header = SwtUtil.getAMLMessages(jsonList.column[i].heading);
              jsonList.column[i].heading = header;
            }
            obj = { columns: jsonList };
            this.currencyGrid.hideHorizontalScrollBar = true;
            this.currencyGrid.screenID = "";
            this.currencyGrid.componentID = "13";
            this.currencyGrid.currencyFormat = this.currencyPattern;
            this.currencyGrid.CustomGrid(data.currencymonitor.grid.metadata);
            this.totalsGrid.CustomGrid(obj);
            if (this.jsonReader.getRowSize() < 1)
            {
            	this.dataExport.enabled=false;
            }
            else
            {
            	this.dataExport.enabled=true;
            }
            this.currentFontSize=this.jsonReader.getScreenAttributes()["currfontsize"];
            for (let i = 0; i < this.currencyGrid.columnDefinitions.length; i++) {
              let column = this.currencyGrid.columnDefinitions[i];
              if (column.field == "alerting") {
                const alertUrl = "./"+ ExternalInterface.call('eval', 'alertOrangeImage');
                const alerCrittUrl = "./"+ ExternalInterface.call('eval', 'alertRedImage');

                if (this.currentFontSize == "Normal"){
                  column['properties'] = {
                    enabled: false,
                    columnName: 'alerting',
                    imageEnabled: alertUrl,
                    imageCritEnabled:alerCrittUrl,
                    imageDisabled: "",
                    _toolTipFlag: true,
                    style: ' display: block; margin-left: auto; margin-right: auto;'

                  };

                }else{
                  column['properties'] = {
                    enabled: false,
                    columnName: 'alerting',
                    imageEnabled: alertUrl,
                    imageCritEnabled:alerCrittUrl,
                    imageDisabled: "",
                    _toolTipFlag: true,
                    style: 'height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;'

                  };
                };
                this.currencyGrid.columnDefinitions[i].editor = null;
                this.currencyGrid.columnDefinitions[i].formatter = AlertingRenderer;
              }
            }

            /* End:code added by Sudhakar on 10-6-2011 for Mantis 1376:Monitors: Display a sensible message when user has no currency access */
            this.currencyGrid.gridData = this.jsonReader.getGridData();
            this.currencyGrid.setRowSize = this.jsonReader.getRowSize();
            this.currencyGrid.colWidthURL ( this.baseURL+"currmonitorNew.do?");
            this.currencyGrid.saveWidths = true;

            // Update the totals grid
            this.totalsGrid.gridData = this.jsonReader.getTotalsData();
            //TODO:
            //var totalsGridHBar:number=this.totalsGrid.horizontalScrollPosition;
            //this.totalsGrid.horizontalScrollPosition=totalsGridHBar;
            //this.totalsGrid.updateGrid(this.jsonReader.getTotalsData(), this.jsonReader.getColumnData());

            /*var maxGridHScrollPosition:int=totalsGrid.maxHorizontalScrollPosition;
            if (totalsGrid.horizontalScrollPosition > 0)
            {
              if (refreshStatus == "N")
              {
                totalsGrid.horizontalScrollPosition=0;
                currencyGrid.horizontalScrollPosition=0;
              }
              else if (refreshStatus == "Y")
              {
                if (totalsGridHBar >= maxGridHScrollPosition)
                {
                  currencyGrid.horizontalScrollPosition=maxGridHScrollPosition;
                  totalsGrid.horizontalScrollPosition=maxGridHScrollPosition;
                  refreshStatus="N";
                }
                else
                {
                  currencyGrid.horizontalScrollPosition=totalsGridHBar;
                  totalsGrid.horizontalScrollPosition=totalsGridHBar;
                  refreshStatus="N";
                }
              }
            }*/
            //TODO:
            //Some screens require the entityid when saving the columns width or order	
            this.currencyGrid.entityID=this.entityCombo.selectedLabel;
            // Checks and gets the font size value from xmlReader
            	// Gets the current font size from xmlReader
             
            // Sets the data grid style based on the font size
            if (this.currentFontSize == "Normal")
            {
            	this.currencyGrid.styleName="dataGridNormal";
            	this.currencyGrid.rowHeight=18;
              this.totalsGrid.styleName="dataGridNormal";
              this.totalsGrid.rowHeight=18;
            }
            else if (this.currentFontSize == "Small")
            {
            	this.currencyGrid.styleName="dataGridSmall";
            	/* Code Modified for Mantis 1386: "User defined option to show normal or small fonts" by Venkat on 25-July-2011 */
            	this.currencyGrid.rowHeight=15;
              this.totalsGrid.styleName="dataGridSmall";
              this.totalsGrid.rowHeight=15;
            }
          }else{
            this.dataBuildingText.visible = true;
          }
          this.showBuildInProgress = false;
        }
        // Triggers the auto refresh if it's null
        if (this.autoRefresh == null) {
          this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
          this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
        }
        else {
          // Sets delay to auto refresh and triggers it
          this.autoRefresh.delay(this.refreshRate * 1000);
        }

        this.prevRecievedJSON = this.lastRecievedJSON
      }
    }


    // If autoRefresh is not equal to null, start the timer. 
    if (this.autoRefresh != null) {

      if (!this.autoRefresh.running) {

        this.autoRefresh.start();
      }
    }

  }

  convertFromUnicodeToString(strTmp) {

    var r = /\\u([\d\w]{4})/gi;
    strTmp = strTmp.replace(r, function (match, grp) {
      return String.fromCharCode(parseInt(grp, 16));
    });
    strTmp = unescape(strTmp);

    return strTmp;
  }
  /**
* inputDataFault
*
* @param event:  FaultEvent
*
* This is a callback function, used to handle fault event.
* Shows fault message in alert window.
*/
  inputDataFault(event): void {
    try {
      this.lostConnectionText.visible = true;
      this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
      this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
      if (this.autoRefresh != null) {
        if (!this.autoRefresh.running) {
          this.autoRefresh.start();
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'inputDataFault', this.errorLocation);
    }
  }

  //TODO: Add export
  /* End:code added by Sudhakar on 10-6-2011 for Mantis 1376:Monitors: Display a sensible message when user has no currency access */

  export(type): void {

    var selects = []
    var filter: Object = new Object();
    var isTotalGrid: boolean = false;
    selects.push("Entity=" + this.entityCombo.selectedLabel);
    selects.push("Ccy=" + this.ccyCombo.selectedLabel);

    this.d1 = this.fromDate.selectedDate;
    this.d2 = new Date(this.fromDate.selectedDate);
    this.d2.setDate(this.d2.getDate() + parseInt(this.showDays.text) - 1);

    var sD1: String = "";
    var sD2: String = "";



    if (this.dateFormat.toLowerCase() == "dd/mm/yyyy") {

      /*   sD1 = this.d1 ? (this.d1.getDate() < 10 ? "0" + this.d1.getDate() : this.d1.getDate()) + "/" + (this.d1.getMonth() + 1 < 10 ? "0" + (this.d1.getMonth() + 1) : this.d1.getMonth() + 1) + "/" + this.d1.getFullYear() : null;
         sD2 = this.d2 ? (this.d2.getDate() < 10 ? "0" + this.d2.getDate() : this.d2.getDate()) + "/" + (this.d2.getMonth() + 1 < 10 ? "0" + (this.d2.getMonth() + 1) : this.d2.getMonth() + 1) + "/" + this.d2.getFullYear() : null;
 */
      sD1 = CommonUtil.formatDate(this.d1, "DD/MM/YYYY");
      sD2 = CommonUtil.formatDate(this.d2, "DD/MM/YYYY");

    }
    else {
      /*   sD1 =this.d1 ? ((this.d1.getMonth() + 1) < 10 ? "0" + (this.d1.getMonth() + 1) : (this.d1.getMonth() + 1)) + "/" + (this.d1.getDate() < 10 ? "0" + (this.d1.getDate()) :this.d1.getDate()) + "/" +this.d1.getFullYear() : null;
         sD2 = this.d2 ? ((this.d2.getMonth() + 1) < 10 ? "0" +(this.d2.getMonth() + 1) :(this.d2.getMonth() + 1)) + "/" +(this.d2.getDate() < 10 ? "0" +(this.d2.getDate()) : this.d2.getDate()) + "/" + this.d2.getFullYear() : null;
 */
      sD1 = CommonUtil.formatDate(this.d1, "MM/DD/YYYY")
      sD2 = CommonUtil.formatDate(this.d2, "MM/DD/YYYY")

    }
    // this.totalsGrid.gridData = this.jsonReader.getTotalsData();
    selects.push("From Date=" + sD1);
    selects.push("To Date=" + sD2);

    // if(this.lastRecievedJSON.currencymonitor.grid.totals) {
    //   isTotalGrid = true
    // }
    isTotalGrid = true
    //FIXME:
    this.dataExport.convertData(this.lastRecievedJSON.currencymonitor.grid.metadata.columns, this.currencyGrid, this.totalsGrid.gridData, selects, type, isTotalGrid);
  }

  /**
   * When a combobox is open then any requests to the server need to be cancelled
   **/
  openedCombo(event): void {
    //FIXME:
    // if(typeof(event.target) == "SwtComboBox")
    // {
    // 	this.comboOpen=true;
    // }else if (event.currentTarget is SwtDateField)
    // {
    // 	this.datePickerOpen = true;
    // }

    if (this.inputData.isBusy()) {
      this.enableInterface();
      this.inputData.cancel();
    }
  }


  /**
   * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
   **/
  closedCombo(event): void {
    this.comboOpen = false;
    this.datePickerOpen = false;
    if (this.comboChange == false && this.validateDate(null, this.fromDate) && this.validateNumberOfDays(this.showDays))
      if ((this.showDays.text != this.prevNumberOfDays) || (this.fromDate.selectedDate.toDateString() != this.prevChosenDate.toDateString()))
        setTimeout(() => {
          this.updateData('no')
        }, 0)
      else
        setTimeout(() => {
          this.updateData('no', true)
        }, 0)

    this.comboChange = false;
  }


  /**
   * Combobox event handling
   **/
  /**
   * When there is a change in the in one of the combo's
   **/
  changeCombo(e): void {
    //FIXME:add event stop + fix ignore function
    //e.stopPropagation();
    this.comboChange = true;
    /* Start:code added by Meftah for Mantis 1762: Issue 1054_SEL_144: Refresh button is disable while loading currency monitor. */
    this.refreshButton.enabled = false;
    /* End:code added by Meftah for Mantis 1762: Issue 1054_SEL_144 */
    setTimeout(() => {
      this.updateData('no', true)
    }, 0)
  }



  // Modified By Imed B on 28-03-2012 start
  validateStartDate(e, type): void {

    // Added for mantis 2016 to fix the non update of columns
    try {
      this.dateChanged = true;
      if (type === 'focusOut') {
        if (Object(focusManager.getFocus()).id == "closeButton") {
          //event.stopImmediatePropagation();
          this.closeHandler();

        }

        else if (this.validateDate(null, this.fromDate)) {
         /* if (Object(focusManager.getFocus()).id == "showDays")
            event.stopImmediatePropagation();

          else */
            if ((this.showDays.text != this.prevNumberOfDays) || (this.fromDate.selectedDate.toDateString() != this.prevChosenDate.toDateString()))
            //{

            this.showDays.setFocus();
         /* }
        } else {*/
          //event.stopImmediatePropagation();

        }
      }
      else if (type === "change" && this.validateDate(null, this.fromDate)) {
        this.showDays.setFocusAndSelect();
      }

    } catch (e) {
      console.log('error in ', e)
    }

  }


  /**
   * @param event:  KeyboardEvent
   * 
   * Added by KaisBS and Meftah for mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
   * this method is the enter key handler of the showdays field
   * */
  keyDownInNumberOfDays(event): void {
//event.keyCode == Keyboard.ENTER
    if (event.keyCode ==13) {
      if (this.validateNumberOfDays(this.showDays) && this.validateDate(null, this.fromDate)) {
        if ((this.showDays.text != this.prevNumberOfDays) || (this.fromDate.selectedDate.toDateString() != this.prevChosenDate.toDateString()))
          setTimeout(() => {
            this.updateData('no')
          }, 0)
        else
          setTimeout(() => {
            this.updateData('no', true)
          }, 0)


        this.refreshButton.setFocus();
        //FIXME:
        //this.showDays.selectRange(0,showDays.text.length);
      }
    }
  }

  /**
   * @param event: Event
   * 
   * Added by KaisBS and Meftah for mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
   * this method is the focus out handler of the showdays field
   * */
  validateShowDaysValue(event: Event): void {
  try {
    let fromDateAsString = moment(this.fromDate.text,this.dateFormat.toUpperCase());
    let prevChosenDate = moment(this.prevChosenDate ,this.dateFormat.toUpperCase());
    this.updateDayLabel();
      let showDayText: String = "" + this.showDays.text;
      if (
        (showDayText == "")
        ||
        (showDayText.indexOf('0') != 0 && showDayText != "")
        ||
        (showDayText.indexOf('0') == 0 && showDayText.indexOf("0", 1) == 1)
        ||
        (showDayText.indexOf('0') == 0 && showDayText.indexOf("0", 1) == -1)
      ) {

        if (this.validateNumberOfDays(this.showDays) && (this.validateDate(event as FocusEvent, this.fromDate)))
          if ((showDayText != this.prevNumberOfDays) || (  (fromDateAsString.diff(prevChosenDate) != 0))) {
            setTimeout(() => {
              this.updateData('no')
            }, 0);
            this.autoRefresh.stop();
          }
      }
    this.datePickerOpen = false;
  } catch (e) {
    console.log('errior in focus oyut', e);
  }

  }



  optionHandler(): void {
    ExternalInterface.call("openOptionsWindow");
    if (this.autoRefresh != null) {
      this.autoRefresh.stop();
      this.refreshStatus = "N";
    }
  }

  /**
   * close the window from the close button
   **/
  closeHandler(): void {
    ExternalInterface.call("close");
  }




  connError(event: MouseEvent): void {
    this.swtAlert.show("" + this.invalidComms, ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));

  }










  /**
		 * Added for mantis by KaisBS 2016 + 1468
		 * _Mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
		 * According to prior and ahead dates, plus the from and to date values, we check if we has the excess of the date range.
		 * If yes, the alert "The data for this date range selection may not be available..." displays and the data will be updated only if the user clicks OK
		 * 
		 * _Mantis 1468 (Input Exception Monitor - Problem in selection of a new period)
		 * 	Note that mantis concerns other screens that contain from and to date.
		 **/
  checkDateRange(autoRefreshOrcheckLocalDateRange: string, fromDate, toDate, showDays: SwtNumericInput, systemDate: string, dateFormat: string, updateDataFunction: Function = null): Boolean {

    var dateRangeExceeded: boolean = false;
    var nDaysPriorToToday: number = ExternalInterface.call('eval', 'nDaysPriorToToday');
    let priorDate = moment(systemDate, this.dateFormat.toUpperCase()).subtract(nDaysPriorToToday, 'days');

    var nDaysAheadToToday: number = ExternalInterface.call('eval', 'nDaysAheadToToday');
    let  aheadDate = moment(systemDate, this.dateFormat.toUpperCase()).add(nDaysAheadToToday, 'days');
    //aheadDate.setDate(aheadDate.getDate() - nDaysAheadToToday);
    if (fromDate.diff(priorDate) <0 || toDate.diff(aheadDate) > 0 ) {
      dateRangeExceeded = true;
      var warningMessage: string = "The data for this date range selection may not be available " + "<br/>"+
        "in the cache and will take time to be calculated. Do you want to continue?";


      this.swtAlert.confirm(warningMessage, "", Alert.OK |  Alert.CANCEL, null, (data)=>   {
        this.checkDateRangeListener(data,autoRefreshOrcheckLocalDateRange );
      },  Alert.CANCEL);
      // this.swtAlert.show(warningMessage, null, Alert.OK | Alert.CANCEL, null, 
      // 	function(event:CloseEvent):void
      // 	{
      // 		if (event.detail == Alert.OK){
      // 			// Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
      // 			this.showBuildInProgress = true;
      // 			this.updateData(autoRefreshOrcheckLocalDateRange, true);
      // 		}else
      // 			updateDataFunction(autoRefreshOrcheckLocalDateRange, true, true);
      // 	}
      // 	, null, Alert.OK);
    }

    return dateRangeExceeded;
  }


  checkDateRangeListener(event, autoRefreshOrcheckLocalDateRange: string) {
    try {
      if (event.detail == Alert.OK) {
        // Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
        this.showBuildInProgress = true;
        this.updateData(autoRefreshOrcheckLocalDateRange, true);
      } else
        this.updateData(autoRefreshOrcheckLocalDateRange, true, true);
    } catch (e) {

    }
  }


  calculateDays(start: Date, end: Date): number {
    var daysInMilliseconds: number = 1000 * 60 * 60 * 24;
    return ((end.getTime() - start.getTime()) / daysInMilliseconds);
  }












  /**
     * Added by KaisBS and Meftah for Mantis 2016 (Enhanced date range selection on all screens with a From and To Date field ) 
     * Change the dayLabel to day/days according to the number in the showdays field
     **/
  updateDayLabel(): void {
    if (parseInt(this.showDays.text) == 0 || parseInt(this.showDays.text) == 1) {
      this.daysLabel.text = ExternalInterface.call('getBundle', 'text', 'day', 'day');
    } else
      this.daysLabel.text = ExternalInterface.call('getBundle', 'text', 'days', 'days');
  }
  doHelp() {
    ExternalInterface.call("help");
  }
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, 'Currency Monitor', this.versionNumber, "");
    let addMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }


  /** This function is used to display the JSON
   *    for data showed in grid
   */
  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }


}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: CurrencyMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CurrencyMonitor],
  entryComponents: []
})
export class CurrencyMonitorModule { }
