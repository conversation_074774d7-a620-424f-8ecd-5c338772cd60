{"name": "pfc-angular7", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve  --port 4400", "build": "ng build --prod --delete-output-path=true --aot=false --named-chunks --output-hashing none --base-href ./ --deploy-url angularSources/ --show-circular-dependencies --verbose", "build_aot": "ng build --prod --deleteOutputPath=true  --build-optimizer=false --namedChunks --outputHashing=none --baseHref=./ --deploy-url angularSources/ --showCircularDependencies=true --verbose=false", "build_aot_named": "node --max-old-space-size=4096 ./node_modules/@angular/cli/bin/ng build --prod --deleteOutputPath=true --aot=true --build-optimizer=false --namedChunks=false --baseHref=./ --deploy-url angularSources/ --showCircularDependencies=true --verbose=false", "test": "ng test", "lint": "ng lint", "kill": "taskkill /f /im node.exe", "e2e": "ng e2e", "bundle-report": "webpack-bundle-analyzer dist/stats.json"}, "private": true, "dependencies": {"@angular/animations": "7.2.4", "@angular/cdk": "7.3.2", "@angular/common": "7.2.4", "@angular/compiler": "7.2.4", "@angular/core": "7.2.4", "@angular/flex-layout": "7.0.0-beta.23", "@angular/forms": "7.2.4", "@angular/http": "7.2.4", "@angular/language-service": "9.0.1", "@angular/material": "7.3.2", "@angular/platform-browser": "7.2.4", "@angular/platform-browser-dynamic": "7.2.4", "@angular/router": "7.2.4", "@types/jquery": "3.3.29", "advanced-json-path": "1.0.8", "angular-slickgrid": "2.24.0", "classlist.js": "1.1.20150312", "core-js": "2.6.4", "crossvent": "1.5.5", "crypto-js": "^4.2.0", "custom-event-polyfill": "1.0.6", "dom-to-image": "2.6.0", "fast-xml-parser": "3.17.5", "highcharts": "8.2.0", "html-to-image": "1.11.11", "html2canvas": "1.0.0-rc.5", "jquery": "3.3.1", "jquery.fancytree": "^2.38.3", "jsonpath": "1.0.0", "moment-business-days": "1.1.3", "ng2-dragula": "2.1.1", "ngx-monaco-editor": "8.1.1", "ngx-simple-modal": "1.4.10", "pretty-data": "0.40.0", "resize-observer-polyfill": "^1.5.1", "rxjs": "6.4.0", "rxjs-compat": "6.4.0", "slickgrid": "2.4.17", "swt-tool-box": "file:bin/swt-tool-box-1.0.0.tgz", "tinymce": "5.1.5", "tslib": "1.9.0", "worker-timers": "7.0.7", "xlsx": "0.16.8", "xml-beautifier": "0.4.1", "xml-js": "1.6.11", "zone.js": "0.8.29"}, "devDependencies": {"@angular-devkit/build-angular": "0.13.0", "@angular/cli": "7.3.2", "@angular/compiler-cli": "7.2.0", "@types/jasmine": "2.8.8", "@types/jasminewd2": "2.0.3", "@types/moment": "2.13.0", "@types/node": "8.9.4", "codelyzer": "4.5.0", "jasmine-core": "2.99.1", "jasmine-spec-reporter": "4.2.1", "karma": "3.1.1", "karma-chrome-launcher": "2.2.0", "karma-coverage-istanbul-reporter": "2.0.1", "karma-jasmine": "1.1.2", "karma-jasmine-html-reporter": "0.2.2", "protractor": "5.4.0", "ts-node": "7.0.0", "tslint": "5.11.0", "typescript": "^3.2.2"}}