import { Component, OnInit, ElementRef, ModuleWithProviders, NgModule, ViewChild } from '@angular/core';
import { SwtModule, CommonService, SwtAlert, SwtToolBoxModule, SwtLabel, SwtComboBox, SwtCanvas, SwtButton, SwtLoadingImage, JSONReader, HTTPComms, SwtUtil, SwtCommonGrid, SwtGroupedCommonGrid, ExternalInterface, Alert, StringUtils, HBox, SwtCommonGridPagination } from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
import { PaginationChangedArgs } from 'angular-slickgrid';

declare var instanceElement: any;

@Component({
  selector: 'app-sweep-queue-cancel',
  templateUrl: './SweepQueueCancel.html',
  styleUrls: ['./SweepQueueCancel.css']
})
export class SweepQueueCancel extends SwtModule implements OnInit {
  
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('ccyGroupLabel') ccyGroupLabel: SwtLabel;
  @ViewChild('selectedCcyGroup') selectedCcyGroup: SwtLabel;
  @ViewChild('accountTypeLabel') accountTypeLabel: SwtLabel;
  @ViewChild('selectedAcctType') selectedAcctType: SwtLabel;
  @ViewChild('lastRefTimeLabel') lastRefTimeLabel: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
   
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyGroupCombo') ccyGroupCombo: SwtComboBox;
  @ViewChild('acctTypeCombo') acctTypeCombo: SwtComboBox;
  
  @ViewChild('dataGridContainer1') dataGridContainer1: SwtCanvas;
  @ViewChild('dataGridContainer2') dataGridContainer2: SwtCanvas;

  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('searchButton') searchButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  
  @ViewChild('pageBox') pageBox: HBox;
  @ViewChild('numstepper', { read: SwtCommonGridPagination }) numstepper: SwtCommonGridPagination;
  //Variable to store the last page no
  public lastNumber: Number = 0;

  private actionPath;
  private actionMethod: string = "";
  private requestParams;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private topGrid: SwtCommonGrid;
  private bottomGrid: SwtCommonGrid;
  private defaultEntity;
  private defaultCcyGrp;
  private defaultAcctType;
  private lastRefTimeValue;
  private menuAccessId;
  private queueName;
  private totalCount;
  private currentFilter;
  private currentSort;
  private currPage; 
  private maxPage;
  private menuEntityCurrGrpAccess;
  private msgsList;
  private _invalidComms: string;
  public moduleId = "Predict";
  private errorMsg;
  private  errorSweepAmount;
  private errorCutOff;
  private errorAccountBreach;
  private errorSweeps;
  private bypassChangedSweep;
  private bypassCutOff;
  private bypassAccountBreach;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.topGrid = <SwtGroupedCommonGrid>this.dataGridContainer1.addChild(SwtGroupedCommonGrid);
    this.bottomGrid = <SwtGroupedCommonGrid>this.dataGridContainer2.addChild(SwtGroupedCommonGrid);
    this.topGrid.allowMultipleSelection = true;
    this.bottomGrid.allowMultipleSelection = true;
    this.entityLabel.text= SwtUtil.getPredictMessage('sweep.entity', null); 
    this.ccyGroupLabel.text= SwtUtil.getPredictMessage('sweep.currencyGroup', null); 
    this.accountTypeLabel.text= SwtUtil.getPredictMessage('sweep.accountType', null); 
    this.entityCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.ccyGroupCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectCurrencyCode', null);
    this.acctTypeCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectAccountType', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('sweep.cancel', null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage('tooltip.canceelSelSweep', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('tooltip.view', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('sweep.refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refreshScreen', null);
    this.searchButton.label = SwtUtil.getPredictMessage('sweep.search', null);
    this.searchButton.toolTip = SwtUtil.getPredictMessage('tooltip.searchSweep', null);
    this.closeButton.label = SwtUtil.getPredictMessage('sweep.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.lastRefTimeLabel.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);

    this.topGrid.onPaginationChanged = (event) => {
      this.paginationChanged(event);
    };
    this.topGrid.clientSideFilter = false;
    this.topGrid.clientSideSort = false;
  }

  onLoad(){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId'); 
    this.queueName= ExternalInterface.call('eval', 'queueName');
    this.totalCount = ExternalInterface.call('eval', 'totalCount');
    this.currentFilter = ExternalInterface.call('eval', 'currentFilter');
    this.currentSort = ExternalInterface.call('eval', 'currentSort');
    this.currPage = ExternalInterface.call('eval', 'currPage');
    this.maxPage = ExternalInterface.call('eval', 'maxPage');
    errorLocation = 20;
    this.menuEntityCurrGrpAccess= ExternalInterface.call('eval', 'menuEntityCurrGrpAccess');
    if (this.menuAccessId) {
      errorLocation = 30;
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    errorLocation = 40;
    this.inputData.cbStop = this.endOfComms.bind(this);
    errorLocation = 50;
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 60;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "sweepcancelqueue.do?";
    this.actionMethod = 'method=displayAngular';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams["currentPage"] = this.currPage;
    this.requestParams['queueName'] = this.queueName;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    errorLocation = 70;
    this.topGrid.onRowClick = (event) => {
      errorLocation = 80;
      this.onMultiSelectTableRow(event, "topGrid");

    };
    errorLocation = 90;
    this.bottomGrid.onRowClick = (event) => {
      errorLocation = 100;
      this.onMultiSelectTableRow(event,  "bottomGrid");

    };

    this.topGrid.onFilterChanged = this.dataRefreshGrid.bind(this);
    this.topGrid.onSortChanged = this.dataRefreshGrid.bind(this);
    


  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "onLoad", errorLocation);
 }
  }

  inputDataResult(event): void {
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      errorLocation = 20;
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      errorLocation = 30;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      errorLocation = 40;
      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          errorLocation = 50;
          //enable refresh button
          this.refreshButton.enabled = true;
          this.refreshButton.buttonMode = true; 
          errorLocation = 60;
          this.topGrid.selectedIndex = -1;
          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.ccyGroupCombo.setComboData(this.jsonReader.getSelects());
          this.acctTypeCombo.setComboData(this.jsonReader.getSelects());
          errorLocation = 70;
          this.defaultEntity = this.jsonReader.getSingletons().defaultEntity;
          this.defaultCcyGrp = this.jsonReader.getSingletons().defaultCcyGrp;
          this.defaultAcctType = this.jsonReader.getSingletons().defaultAcctType;
          errorLocation = 80;
          this.lastRefTime.text= this.jsonReader.getSingletons().lastRefTime;
          this.maxPage= this.jsonReader.getSingletons().maxPage;
          this.currPage= this.jsonReader.getSingletons().currentPage;
            //Gets the current page from xml
            this.numstepper.value = Number(event.SweepQueue.authoriseQueueGrid.paging.currentpage);
            //Gets the maximum no of pages value
            this.maxPage = event.SweepQueue.authoriseQueueGrid.paging.maxpage;
            //Sets the Numeric stepper maximum value
            this.numstepper.maximum = Number(this.maxPage);
            this.topGrid.paginationComponent = this.numstepper;
            let totalCount= this.jsonReader.getSingletons().totalCount;
          errorLocation = 90;
          this.entityCombo.selectedLabel = this.defaultEntity;
          errorLocation = 100;
          if (this.defaultEntity != undefined)
            this.entityCombo.selectedLabel = this.defaultEntity;
            errorLocation = 110;
          if (this.defaultCcyGrp != undefined)
            this.ccyGroupCombo.selectedLabel = this.defaultCcyGrp;
            errorLocation = 120;
          if (this.defaultAcctType != undefined)
            this.acctTypeCombo.selectedValue = this.defaultAcctType;  
            errorLocation = 130;
          this.selectedEntity.text = this.entityCombo.selectedValue;
          this.selectedCcyGroup.text = this.ccyGroupCombo.selectedValue;
          errorLocation = 140;

          if (!this.jsonReader.isDataBuilding()) {
            errorLocation = 150;
            const obj = { columns: this.lastRecievedJSON.SweepQueue.authoriseQueueGrid.metadata.columns };
            errorLocation = 160;
            //Authorise queue grid
            this.topGrid.CustomGrid(obj);
            errorLocation = 170;
            var gridRows = this.lastRecievedJSON.SweepQueue.authoriseQueueGrid.rows;
            errorLocation = 180;
            if (gridRows.size > 0) {
              this.topGrid.gridData = gridRows;
              errorLocation = 190;
              this.topGrid.setRowSize = totalCount; 
            }
            else {
              errorLocation = 200;
              this.topGrid.gridData = { size: 0, row: [] };
            }
            //View queue grid
            const obj1 = { columns: this.lastRecievedJSON.SweepQueue.viewQueueGrid.metadata.columns };
            errorLocation = 210
            this.bottomGrid.CustomGrid(obj1);
            errorLocation = 220;
            var gridRows = this.lastRecievedJSON.SweepQueue.viewQueueGrid.rows;
            errorLocation = 230;
            if (gridRows.size > 0) {
              errorLocation = 240;
              this.bottomGrid.gridData = gridRows;
              errorLocation = 250;
              this.bottomGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              errorLocation = 260;
              this.bottomGrid.gridData = { size: 0, row: [] };
            }

            
          if(this.maxPage > 1) {
            this.pageBox.visible = true;
            this.numstepper.minimum=1;
            this.numstepper.maximum=this.maxPage;
          } else {
            this.pageBox.visible = false;
          }

            this.topGrid.refresh();
            this.prevRecievedJSON = this.lastRecievedJSON;

          }
        }
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }
      }

    }
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "inputDataResult", errorLocation);
 }
  }

  public checkResult(event): void {
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      errorLocation = 20;
      this.lastRecievedJSON = event;
      errorLocation = 30;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      errorLocation = 40;
      if (this.jsonReader.getRequestReplyStatus()) {
        errorLocation = 50;
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          errorLocation = 60; 
          this.msgsList= this.jsonReader.getSingletons().listOfMsgs;
          this.errorSweepAmount= this.jsonReader.getSingletons().errorSweepAmount;
          this.errorCutOff= this.jsonReader.getSingletons().errorCutOff;
          this.errorAccountBreach= this.jsonReader.getSingletons().errorAccountBreach;
          this.errorSweeps= this.jsonReader.getSingletons().errorSweeps;
          this.bypassCutOff= this.jsonReader.getSingletons().bypassCutOff;
          this.bypassChangedSweep= this.jsonReader.getSingletons().bypassChangedSweep;
          this.bypassAccountBreach= this.jsonReader.getSingletons().bypassAccountBreach;
          let totalCount= this.jsonReader.getSingletons().totalCount;
          errorLocation = 70;
          if(this.errorSweeps){
            errorLocation = 80;
            if(this.msgsList){
              errorLocation = 90;
              this.swtAlert.confirm(this.msgsList, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.yesCancel.bind(this));
              }
          }else{
            errorLocation = 100;
          if(this.msgsList){
            errorLocation = 110;
          this.swtAlert.show(this.msgsList, "Warning", Alert.OK);
          
          } 
          

          if (!this.jsonReader.isDataBuilding()) {
            errorLocation = 120;
            const obj = { columns: this.lastRecievedJSON.SweepQueue.authoriseQueueGrid.metadata.columns };
            errorLocation = 130;
            //Authorise queue grid
            this.topGrid.CustomGrid(obj);
            errorLocation = 140;
            var gridRows = this.lastRecievedJSON.SweepQueue.authoriseQueueGrid.rows;
            errorLocation = 150;
            if (gridRows.size > 0) {
              errorLocation = 160;
              this.topGrid.gridData = gridRows;
              errorLocation = 170;
              this.topGrid.setRowSize = totalCount;
            }
            else {
              errorLocation = 180;
              this.topGrid.gridData = { size: 0, row: [] };
            }
            //View queue grid
            const obj1 = { columns: this.lastRecievedJSON.SweepQueue.viewQueueGrid.metadata.columns };
            errorLocation = 190;
            this.bottomGrid.CustomGrid(obj1);
            errorLocation = 200;
            var gridRows = this.lastRecievedJSON.SweepQueue.viewQueueGrid.rows;
            errorLocation = 210;
            if (gridRows.size > 0) {
              errorLocation = 220;
              this.bottomGrid.gridData = gridRows;
              errorLocation = 230;
              this.bottomGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              errorLocation = 240;
              this.bottomGrid.gridData = { size: 0, row: [] };
            }

            this.prevRecievedJSON = this.lastRecievedJSON;
            this.topGrid.selectedIndex=-1;
          }
        }
        }
      } else {

        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
       }
      }

    }
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "checkResult", errorLocation);
 }
  }



  /*change data grid*/
  updateData(event): void {
    // Variable to hold error location
    var errorLocation: number = 0;
    if (this.refreshButton) this.refreshButton.enabled = false;
    try {
      errorLocation = 10;
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    errorLocation = 20;
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
        errorLocation = 30;
      }
    }
  this.inputData.cbStart = this.startOfComms.bind(this);
  this.inputData.cbStop = () => {
    this.endOfComms();
    if (this.refreshButton) this.refreshButton.enabled = true;
  };
  this.inputData.cbResult = (event) => {
    this.inputDataResult(event);
  };
  this.inputData.cbFault = (error) => {
    this.inputDataFault(error);
    if (this.refreshButton) this.refreshButton.enabled = true;
  };
    errorLocation = 50;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    errorLocation = 60;
    this.inputData.encodeURL = false;
    this.actionPath = "sweepcancelqueue.do?";
    this.actionMethod = 'method=displayList';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['selectedList'] = this.getSelectedList();
    this.requestParams['bypassChangedSweep'] = "N";
    this.requestParams['bypassAccountBreach'] = "N";
    this.requestParams['bypassCutOff'] = "N";
    this.requestParams['queueName'] = this.queueName;
    this.requestParams['accountType'] = this.acctTypeCombo.selectedValue;
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['currencyCode'] = this.ccyGroupCombo.selectedLabel; 
    this.requestParams['maxPage'] = this.maxPage;
    this.requestParams["currentPage"] = this.currPage;
    this.requestParams['parentScreen'] = "";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 70;
    this.inputData.send(this.requestParams);
    errorLocation = 80;
    //disable refresh button
    this.refreshButton.enabled= false;
    this.refreshButton.buttonMode= false;
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "updateData", errorLocation);
 }
  }

  confirmCancel(event){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
    Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
    errorLocation = 20;
    var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('sweep.confirm.cancelMsg', null)));
    errorLocation = 30;
    this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.cancel.bind(this));
    errorLocation = 40;
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "confirmCancel", errorLocation);
 }
  }


  cancel(event){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    if (event.detail == Alert.YES) {
      errorLocation = 20;
    this.requestParams = [];
     errorLocation = 30;
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    errorLocation = 40;
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        errorLocation = 50;
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    errorLocation = 60;
    this.inputData.cbStop = this.endOfComms.bind(this);
    errorLocation = 70;
    this.inputData.cbResult = (event) => {
      this.checkResult(event);
    };
    errorLocation = 80;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    errorLocation = 90;
    this.inputData.encodeURL = false;
    this.actionPath = "sweepcancelqueue.do?";
    this.actionMethod = 'method=submit';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['selectedList'] = this.getSelectedList();
    this.requestParams['queueName'] = this.queueName;
    this.requestParams['accountType'] = this.acctTypeCombo.selectedValue;
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['currencyCode'] = this.ccyGroupCombo.selectedLabel; 
    this.requestParams['totalCount'] = this.totalCount;
    this.requestParams['selectedFilter'] = this.topGrid.filteredGridColumns;
    this.requestParams['selectedSort'] = this.topGrid.sortedGridColumn;
    this.requestParams['currentPage'] = this.currPage;
    errorLocation = 100;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 110;
    this.inputData.send(this.requestParams);
    }else{
      errorLocation = 120;
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
    }
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "cancel", errorLocation);
 }
  }

  getSelectedSweepID(){ 
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10; 
    let selectedRows = this.topGrid.selectedItems;
    errorLocation = 20; 
    let selectedOtherRows = this.bottomGrid.selectedItems;
    errorLocation = 30; 
    var selectedList = "";
    for (let i=0; i < selectedRows.length; i++) 
    {
      errorLocation = 40; 
     if( selectedRows[i]){
      errorLocation = 50; 
          selectedList = selectedList + selectedRows[i].sweepId.content;
       }
    } 
    
    if(selectedList == ""){
      errorLocation = 60; 
      for (let j=0; j < selectedOtherRows.length; j++) 
      {
        errorLocation = 70; 
        selectedList = selectedList + selectedOtherRows[j].sweepId.content;
      }
    }
   
    return selectedList;
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "getSelectedSweepID", errorLocation);
 }
}

  getSelectedEntityID() {
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10; 
      let selectedRows = this.topGrid.selectedItems;
      errorLocation = 20;
      let selectedOtherRows = this.bottomGrid.selectedItems;
      errorLocation = 30;
      var entityIdCr = "";

      for (let i=0; i < selectedRows.length; i++) 
      {
        errorLocation = 40;
       if( selectedRows[i]){
        errorLocation = 50;
        entityIdCr = entityIdCr + selectedRows[i].entityDr.content;
      }
      } 
      
      if(entityIdCr == ""){
        errorLocation = 60;
        for (let j=0; j < selectedOtherRows.length; j++) 
        {
          errorLocation = 70;
        entityIdCr = entityIdCr + selectedOtherRows[j].entityDr.content;
        }
      }

    return entityIdCr;
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "getSelectedEntityID", errorLocation);
 }
  }

   
  getSelectedList(){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;   
    let selectedRows = this.topGrid.selectedItems;
    errorLocation = 20;
    var selectedList = "";
    for (let i=0; i < selectedRows.length; i++) 
    {
      errorLocation = 30;
     if( selectedRows[i]){
      errorLocation = 40;
          selectedList = selectedList + selectedRows[i].sweepId.content + ",";
       }
    }  		
    return selectedList;
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "getSelectedList", errorLocation);
 }
   }

  closeHandler() {
    ExternalInterface.call("close");
  }
  
    buildViewSweepDisplayURL(){
      ExternalInterface.call('buildViewSweepDisplayURL', this.getSelectedSweepID(), this.getSelectedEntityID());
    }

  openSearch(){
    ExternalInterface.call("openSearch", "displaysearch", this.entityCombo.selectedLabel, this.acctTypeCombo.selectedLabel, this.ccyGroupCombo.selectedLabel, this.queueName);
  }


   onMultiSelectTableRow(event, source) {
     // Variable to hold error location
     var errorLocation: number = 0;
     try {
       errorLocation = 10; 
    if ("bottomGrid" == source){
      errorLocation = 20; 
    if(this.topGrid.selectedIndex >=0){
      errorLocation = 30;    
       this.topGrid.selectedIndex=-1;
    }

        if (this.menuEntityCurrGrpAccess == 0 && this.bottomGrid.selectedItems.length == 1) {
          errorLocation = 40; 
          this.viewButton.enabled = true;
          this.viewButton.buttonMode = true;
          this.cancelButton.enabled = false;
          this.cancelButton.buttonMode = false;
          } else  {
            errorLocation = 50; 
          this.viewButton.enabled = false;
          this.viewButton.buttonMode = false;
          this.cancelButton.enabled = false;
          this.cancelButton.buttonMode = false;
          }
    } else {
      if(this.bottomGrid.selectedIndex >=0){  
        errorLocation = 60;  
        this.bottomGrid.selectedIndex=-1;
     }

         if (this.menuEntityCurrGrpAccess == 0 && this.topGrid.selectedItems.length == 1) {
          errorLocation = 70; 
           this.viewButton.enabled = true;
           this.viewButton.buttonMode = true;

          } else  {
          errorLocation = 80; 
           this.viewButton.enabled = false;
           this.viewButton.buttonMode = false;

          }

          if (this.menuEntityCurrGrpAccess == 0 && this.topGrid.selectedItems.length > 0) {
            errorLocation = 90; 
            this.cancelButton.enabled = true;
            this.cancelButton.buttonMode = true;
            } else  {
            errorLocation = 100; 
            this.cancelButton.enabled = false;
            this.cancelButton.buttonMode = false;
          }

      }
      errorLocation = 110; 
      this.accountAccess();
    }catch (error) {
      SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "onMultiSelectTableRow", errorLocation);
   }
    }

    accountAccess() {
      // Variable to hold error location
      var errorLocation: number = 0;
      try {
        errorLocation = 10;
      let selectedRows= this.topGrid.selectedItems;
      errorLocation = 20;
      let selectedList = "";
      let flag = "true";
      for (let i=0; i < selectedRows.length; i++) {
            let entityIdCr = selectedRows[i].entityCr.content;
            errorLocation = 30;
            let accountIdCr = selectedRows[i].accountIdCr.content;
            errorLocation = 40;
            let entityIdDr = selectedRows[i].entityDr.content;
            errorLocation = 50;
            let accountIdDr = selectedRows[i].accountIdDr.content;
            errorLocation = 60;
              flag = this.accountAccessConfirm(entityIdCr, entityIdDr, accountIdCr.trim(), accountIdDr.trim());
              errorLocation = 70;
              if (flag == "false") {
                errorLocation = 80;
                this.cancelButton.enabled = false;
                this.cancelButton.buttonMode = false;  
                break;            
              }

      }
    }catch (error) {
      SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "accountAccess", errorLocation);
   }
  }


   accountAccessConfirm(entityCr, entityDr, accountIdCr, accountIdDr) {
   let lockflag= "false";
   lockflag=ExternalInterface.call("accountAccessConfirm", entityCr, entityDr, accountIdCr, accountIdDr);
   return lockflag;
  }

  yesCancel(event){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
    if (event.detail == Alert.YES) {
      errorLocation = 20;
    this.topGrid.selectedIndex=-1; 
    this.refreshButton.enabled= true;
    this.refreshButton.buttonMode= true;
    this.cancelButton.enabled= false;
    this.cancelButton.buttonMode= false; 
    this.viewButton.enabled= false;
    this.viewButton.buttonMode= false;  
    }else{
      errorLocation = 30;
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    errorLocation = 40;
    this.inputData.cbStop = this.endOfComms.bind(this);
    errorLocation = 50;
    this.inputData.cbResult = () => {
      this.inputDataResult(event);
    };
    errorLocation = 60;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    errorLocation = 70;
    this.inputData.encodeURL = false;
    this.actionPath = "sweepcancelqueue.do?";
    this.actionMethod = 'method=submit';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['selectedList'] = this.getSelectedList();
    this.requestParams['totalCount'] = this.totalCount;
    this.requestParams['selectedFilter'] = this.topGrid.filteredGridColumns;
    this.requestParams['selectedSort'] = this.topGrid.sortedGridColumn;
    this.requestParams['queueName'] = this.queueName;
    this.requestParams['accountType'] = this.acctTypeCombo.selectedValue;
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['currencyCode'] = this.ccyGroupCombo.selectedLabel; 
    this.requestParams['currentPage'] = this.currPage;
    this.requestParams['queueName'] = this.queueName;
    errorLocation = 80;
    if(this.errorCutOff && this.errorCutOff=="Y"){
    this.requestParams['bypassCutOff'] = "Y";
    }
    errorLocation = 90;
    if(this.errorSweepAmount && this.errorSweepAmount=="Y"){
      this.requestParams['bypassChangedSweep'] = "Y";
      this.requestParams['bypassCutOff'] = this.bypassCutOff;
    }

    errorLocation = 100;
    if(this.errorAccountBreach && this.errorAccountBreach=="Y"){
    this.requestParams['bypassAccountBreach'] = "Y";
    this.requestParams['bypassCutOff'] = this.bypassCutOff;
    this.requestParams['bypassChangedSweep'] = this.bypassChangedSweep;
    }
    this.requestParams['accountType'] = this.acctTypeCombo.selectedValue;
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['currencyCode'] = this.ccyGroupCombo.selectedLabel; 
    this.requestParams['parentScreen'] = "";
    errorLocation = 110;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 120;
    this.inputData.send(this.requestParams);
  } 
}catch (error) {
  SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "yesCancel", errorLocation);
}
}

  doHelp(): void {
    ExternalInterface.call("help");
  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

    /**
   * enablePrintButton
   *
   */
  enablePrintButton(value): void {
    this.printButton.enabled = value;
    this.printButton.buttonMode = value;
  }

   /**
   * printPage
   *
   * param event
   *
   * Method to get call the action to get reports
   */
  printPage(): void {
    let errorLocation = 0;
    try {
      ExternalInterface.call('printPage');

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", errorLocation);
    }
  }

  keyDownEventHandler(event){

  }

  paginationChanged(event: PaginationChangedArgs) {
    this.numstepper.processing = true;
    this.doRefreshPage();
  }

  private doRefreshPage(): void {
    //To hold selected filter value
    var selectedFilter: string = null;

    //To hold selected sort value
    var selectedSort: string = null;

    //To hold current page
    var currentPage: string = null;
    var maxPage = this.lastRecievedJSON.SweepQueue.authoriseQueueGrid.paging.maxpage;
    try {
      if (this.numstepper.value > 0) {
        if (((this.numstepper.value <= this.numstepper.maximum) && (this.numstepper.value != this.lastNumber) && (this.numstepper.value != 0))) {

          // Get the SortedGridColumn
          selectedFilter = this.topGrid.filteredGridColumns;

          // Get the SortedGridColumn
          selectedSort = this.topGrid.sortedGridColumn;

          //Get the current page value
          currentPage = (this.numstepper.value).toString();
          // Initialising the request params
          this.requestParams = {};
          this.inputData.cbStart = this.startOfComms.bind(this);
          this.inputData.cbStop = this.endOfComms.bind(this);
          this.inputData.cbResult = (event) => {
            this.inputDataResult(event);
          };
          this.inputData.cbFault = this.inputDataFault.bind(this);
          this.inputData.encodeURL = false;
          // Adding request params
          this.requestParams['menuAccessId'] = this.menuAccessId;
          this.requestParams['queueName'] = this.queueName;
          this.requestParams["selectedSort"] = selectedSort;
          this.requestParams["selectedFilter"] = selectedFilter;
          this.requestParams["entityId"] = this.entityCombo.selectedLabel;
          this.requestParams['accountType'] = this.acctTypeCombo.selectedValue;
          this.requestParams['currGrpId'] = this.ccyGroupCombo.selectedLabel;  
          this.requestParams['maxPage'] = maxPage; 
          this.requestParams["currentPage"] = currentPage;
          this.actionPath = "sweepcancelqueue.do?";
          this.actionMethod = 'method=nextAngular';
          //set http url
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;

          //send request to server
          this.inputData.send(this.requestParams);
        }
      }
    }
    catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'inputDataFault', 0);
    }

  }

  dataRefreshGrid(){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;

    if (this.menuAccessId) {
      errorLocation = 30;
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    errorLocation = 40;
    this.inputData.cbStop = this.endOfComms.bind(this);
    errorLocation = 50;
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 60;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "sweepcancelqueue.do?";
    this.actionMethod = 'method=displayAngular';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['queueName'] = this.queueName;
    this.requestParams["selectedSort"] = this.topGrid.sortedGridColumn;
    this.requestParams["selectedFilter"] = this.topGrid.filteredGridColumns;
    this.requestParams["entityId"] = this.entityCombo.selectedLabel;
    this.requestParams['accountType'] = this.acctTypeCombo.selectedValue;
    this.requestParams['currCode'] = this.ccyGroupCombo.selectedLabel;  
    this.requestParams['maxPage'] = this.maxPage;
    this.requestParams["currentPage"] = this.currPage;

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "onLoad", errorLocation);
 }
  }


}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: SweepQueueCancel }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SweepQueueCancel],
  entryComponents: []
})
export class SweepQueueCancelModule { }