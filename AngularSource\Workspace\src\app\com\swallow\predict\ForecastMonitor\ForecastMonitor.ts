import {Component, ElementRef, ModuleWithProviders, NgModule, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {
  CommonService,
  HTTPComms,
  JSONReader,
  SwtLoadingImage,
  SwtComboBox,
  SwtLabel,
  SwtModule,
  SwtButton,
  SwtAlert,
  SwtText,
  ExternalInterface,
  SwtUtil,
  Keyboard,
  focusManager,
  Timer,
  SwtPopUpManager,
  TitleWindow,
  SwtToolBoxModule,
  SwtTextInput,
  SwtDataExport,
  HDividedBox,
  ScreenVersion,
  ContextMenuItem,
  JSONViewer,
  VBox,
  SwtCanvas,
  SwtRadioButtonGroup,
  SwtRadioItem,
  StringUtils,
  SwtCommonGrid, ExportEvent, HDividedEndResizeEvent,

} from 'swt-tool-box';

import {RatePopUp} from "../RatePopUp/RatePopUp";
import {RouterModule, Routes} from '@angular/router';
declare var require: any;
const $ = require( 'jquery' );
declare var instanceElement: any;
@Component({
  selector: 'app-forecast-monitor',
  templateUrl: './ForecastMonitor.html',
  styleUrls: ['./ForecastMonitor.css'],
  encapsulation: ViewEncapsulation.None
})
export class ForecastMonitor extends  SwtModule implements  OnInit, OnDestroy {


  /***********SwtCommonGrid**********/
  @ViewChild("forecastMonitorGrid") forecastMonitorGrid: SwtCommonGrid;
  @ViewChild("forecastMonitorGridSub") forecastMonitorGridSub: SwtCommonGrid;

  /***********SwtCanvas**********/
  @ViewChild('customGrid') customGrid: SwtCanvas;
  @ViewChild('customGridSub') customGridSub: SwtCanvas;
  /***********VBox**********/
  @ViewChild("appContainer") appContainer: VBox;
  @ViewChild("vbGridContainer") vbGridContainer: VBox;
  @ViewChild("swtControlBar") swtControlBar: SwtCanvas;
  @ViewChild("swtButtonBar") swtButtonBar: SwtCanvas;
  /***********HDividedBox**********/
  @ViewChild("cvGridContainer") cvGridContainer: HDividedBox;
  /*********SwtLoadingImage*********/
  @ViewChild("loadingImage") loadingImage: SwtLoadingImage;

  /*********SwtImage*********/
  @ViewChild('imgShowHideControlBar') imgShowHideControlBar: SwtButton;
  @ViewChild('imgShowHideButtonBar') imgShowHideButtonBar: SwtButton;
  /*********SwtComboBox*********/
  @ViewChild('cbEntity') cbEntity: SwtComboBox;
  @ViewChild('cbCurrency') cbCurrency: SwtComboBox;

  /*********SwtLabel*********/
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('selectedCurrency') selectedCurrency: SwtLabel;
  @ViewChild('templateIdLabel') templateIdLabel: SwtLabel;
  @ViewChild('lblTemplateId') lblTemplateId: SwtLabel;

  /*********SwtText*********/
  @ViewChild('lastRef') lastRef: SwtText;
  @ViewChild('lastRefTime') lastRefTime: SwtText;
  @ViewChild('dataBuildingText') dataBuildingText: SwtTextInput;
  @ViewChild('lostConnectionText') lostConnectionText: SwtTextInput;

  /*********SwtButton*********/
  @ViewChild('rateButton') rateButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('optionsButton') optionsButton: SwtButton;
  /*********SwtDataExport*********/
  @ViewChild('exportContainer') exportContainer: SwtDataExport;
  @ViewChild('helpIcon') helpIcon: SwtButton;

  /*********SwtRadioButtonGroup*********/
  @ViewChild('breakdown') breakdown: SwtRadioButtonGroup;
  @ViewChild('movementRadio') movementRadio: SwtRadioItem;
  @ViewChild('bookRadio') bookRadio: SwtRadioItem;


  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private sendData = new HTTPComms(this.commonService);
  private resizeData = new HTTPComms(this.commonService);
  private updateRefreshRate = new HTTPComms(this.commonService);
  private invalidComms = '';
  public screenVersion = new ScreenVersion(this.commonService);
  public showJSONPopup: any;
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  public requestParams = [];
  private comboChange = false;
  /**
   * Timer Objects
   **/
  private refreshRate = 30;
  private autoRefresh: Timer;
  // private  backGroundTimer;
  /**
   * Logic Objects
   **/
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;


  private swtAlert: SwtAlert;
  private win: TitleWindow;

  private refreshFlag = true;
  private interval = null;
  private moduleId = "";
  private errorLocation = 0;
  private status;
  private states: any = {};
  private level1;
  private level2;
  private level3;
  private menuAccessIdParent = 0;

  public CANCELLED = "cancelled";
  public OTHER = "other";
  public NEW_COLUMN = "newColumn";
  public NEW_ROW = "newRow";
  /* - START -- Screen Name and Version Number ---- */
  private screenName = 'Forecast Monitor Screen';
  private versionNumber = '1.1.0002';
  private releaseDate = '20 May 2019';
  /* - END -- Screen Name and Version Number ---- */

  private menuAccess = 2;
  private expandColumn = '/assets/images/plus.gif';
  private collapseColumn = '/assets/images/minus.gif';


  // private forecastMonitorGridSub: SwtCommonGrid;
  // private forecastMonitorGrid: SwtCommonGrid;

  private maintainWidth = 750;
  private previousWidth = 750;

  private controlBarHideFlag: boolean;
  private buttonBarHideFlag: boolean;
  public editdataChanged = new Object();
  public editupdate = new Object();
  public prevSelectedIndex = -1;
  private alertPreventFlag = false;


  private showXML: any;
  private itemId = "";

  private refreshStatus = "N";
  private columnLength = 0;
  private selectIndex = -1;
  private itemBeginFlag = false;

  // flag for HDivideBox
  private dividerFlag = false;

  // Previous xml
  private preColumnXml;
  private mainXml;
  // Declaring the flag that sets the status for Collapsing the Header(Control Bar)
  private oldDividerFlag = true;
  private columnChangeFlag;
  private lastX = 0;
  private lastY = 0;
  private rateRequest = "";

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    window['Main'] = this;
  }


  ngOnDestroy(): any {
    instanceElement = null;
  }

  ngOnInit(): void {
    instanceElement = this;
    // Assining properties for controls
    this.forecastMonitorGrid.uniqueColumn = "date";
    //this.forecastMonitorGrid.lockedColumnCount = 1;
    this.forecastMonitorGridSub.editable = true;
    this.forecastMonitorGrid.enableRowSelection = false;
    this.forecastMonitorGridSub.lockedColumnCount = 0;
    this.forecastMonitorGridSub.forceSameColumnSize = true;
    this.forecastMonitorGrid.forceSameColumnSize = true;
    this.forecastMonitorGrid.forceSameColumnException =['expand'];
    this.entityLabel.text = SwtUtil.getPredictMessage('label.forecastMonitor.entity', null);
    this.cbEntity.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.selectEntity"', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('label.forecastMonitor.currency', null);
    this.cbCurrency.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.selectCurrency', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.forecastMonitor.refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.refreshWindow', null);
    this.rateButton.label = SwtUtil.getPredictMessage('button.forecastMonitor.rate', null);
    this.rateButton.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.rateButton', null);
    this.optionsButton.label = SwtUtil.getPredictMessage('button.forecastMonitor.option', null);
    this.optionsButton.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.option', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.forecastMonitor.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.close', null);
    this.templateIdLabel.text = SwtUtil.getPredictMessage('label.forecastMonitor.templateId', null);
    this.movementRadio.label = SwtUtil.getPredictMessage("label.entityMonitor.movementId", null);
    this.movementRadio.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.mvmntBrkdown", null);
    this.bookRadio.label = SwtUtil.getPredictMessage("label.entityMonitor.bookCode", null);
    this.bookRadio.toolTip = SwtUtil.getPredictMessage("tooltip.entityMonitor.bookBrkdown", null);
    this.lastRef.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);
    this.refreshButton.setFocus();

    this.forecastMonitorGrid.ITEM_CLICK.subscribe((event) => {
      this.changeTreeData(event);
    });
    // imgShowHideControlBar.source=collapseColumn;
    this.imgShowHideControlBar.toolTip = "Hide Control Bar";
    // imgShowHideButtonBar.source=collapseColumn;
    this.imgShowHideButtonBar.toolTip = "Hide Button Bar";
    this.initializeMenus();
    ExportEvent.subscribe((type) => {
      this.export(type);
    });

    this.forecastMonitorGrid.listenVerticalScrollEvent = true;
    this.forecastMonitorGridSub.fireVerticalScrollEvent = true;
    this.forecastMonitorGridSub.listenVerticalScrollEvent = true;
    this.forecastMonitorGrid.fireVerticalScrollEvent = true;
    this.forecastMonitorGrid.hideVerticalScrollBar = true;
    // ExternalInterface.addCallback("AutoRefreshStart", this.startAutoRefresh);
    // get the menuAccessId
  }

  onLoad() {
    try {
      this.initializeMenus();

      this.menuAccessIdParent = ExternalInterface.call('eval', 'menuAccessIdParent');
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.sendData.cbStart = this.startOfComms.bind(this);
      this.sendData.cbStop = this.endOfComms.bind(this);
      this.sendData.cbResult = (data) => {
        this.sendDataResult(data);
      };
      this.sendData.cbFault = this.sendDataFault.bind(this);
      this.sendData.encodeURL = false;
      this.actionPath = "forecastMonitor.do?";
      this.actionMethod = "" + "menuAccess=" + ((this.menuAccessIdParent) ? this.menuAccessIdParent : 0);
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.sendData.url = this.baseURL + this.actionPath + "method=saveForecastScenario";
      this.inputData.send(this.requestParams);
      HDividedEndResizeEvent.subscribe((event) => {
        this.currDividerPosition = Number(this.cvGridContainer.widthLeftPixel);
        this.updateWidths(event);
      });
      this.forecastMonitorGridSub.columnWidthChanged.subscribe((event) => {
        this.columnWidthChange(event, "sub");
      });
      this.forecastMonitorGrid.columnWidthChanged.subscribe((event) => {
        this.columnWidthChange(event, "main");
      });

      } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'Forecast Monitor', 'onLoad');
    }
  }

  private currDividerPosition: number;
  /**
   * When the tree data is changed, this method is called
   * @param event:TreeEvent
   */
  private gridDataExpand = [];
  private subGridDataExpand = [];

  changeTreeData(event): void {
    let colIndex: number = event.columnIndex;
    let expand: boolean = event.target.data != null && event.target.data.expand == "Y";
    if (colIndex == 0 && expand) {
      //remoe seleced index when expanding/collapsing like flex
      this.forecastMonitorGrid.selectedIndex = -1;
      this.forecastMonitorGridSub.selectedIndex = -1;
      let date: string = event.target.data.date;
      if (date == "Grand total") {
        let isGrandTotalExpanded = event.target.data.slickgrid_rowcontent.expand.opened;
        for (let i = 0; i < this.mainXml.forecastmonitor.grid.rows.row.length; i++) {
          let e = this.mainXml.forecastmonitor.grid.rows.row[i];
          if (e.date.content != "Grand total") {
            this.mainXml.forecastmonitor.grid.rows.row[i].expand.opened = !isGrandTotalExpanded;// !e.expand.opened;
            this.mainXml.forecastmonitor.grid.rows.row[i].visible = !isGrandTotalExpanded;//!e.visible;
            this.mainXml.forecastmonitor.gridSub.rows.row[i].visible = !isGrandTotalExpanded;
          } else if (e.date.content == "Grand total") {
            this.mainXml.forecastmonitor.grid.rows.row[i].expand.opened = !e.expand.opened;
          }
        }


      } else {
        for (let m = 0; m < this.mainXml.forecastmonitor.grid.rows.row.length; m++) {
          let gXml = this.mainXml.forecastmonitor.grid.rows.row[m];
          if (gXml.bucket.content == event.target.data.bucket && (String(gXml.date.content).search("otal") < 0)) {
            this.mainXml.forecastmonitor.grid.rows.row[m].visible = !gXml.visible;
          } else if (gXml.bucket.content == event.target.data.bucket && (String(gXml.date.content).search("otal") > 0)) {
            this.mainXml.forecastmonitor.grid.rows.row[m].expand.opened = !gXml.expand.opened;
          }
        }

        for (let k = 0; k < this.mainXml.forecastmonitor.gridSub.rows.row.length; k++) {
          let gXmlSub = this.mainXml.forecastmonitor.gridSub.rows.row[k];
          if (gXmlSub.bucket == event.target.data.bucket && (String(gXmlSub.date.content).search("otal") < 0)) {
            this.mainXml.forecastmonitor.gridSub.rows.row[k].visible = !gXmlSub.visible;
          }
        }
      }
      let grid2dataset = [];
      JSONReader.jsonpath(this.mainXml, '$.forecastmonitor.grid.rows.row.*').forEach(function (value) {
        if (value.visible == true || value.visible == "true") {
          if (value === Object(value)) {
            grid2dataset.push(value);
          }
        }
      });
      this.gridDataExpand = grid2dataset;
      let grid2datasetSub = [];
      JSONReader.jsonpath(this.mainXml, '$.forecastmonitor.gridSub.rows.row.*').forEach(function (value) {
        if (value.visible == true || value.visible == "true") {
          if (value === Object(value)) {
            grid2datasetSub.push(value);
          }
        }
      });
      this.subGridDataExpand = grid2datasetSub;
      this.forecastMonitorGrid.gridData = {row: this.gridDataExpand, size: this.gridDataExpand.length};
      this.forecastMonitorGridSub.gridData = {row: this.subGridDataExpand, size: this.subGridDataExpand.length};
    } else {
      //manage selected index when column different of expand
      if (this.forecastMonitorGrid.selectedIndices.length > 0) {
        this.forecastMonitorGridSub.selectedIndex = this.forecastMonitorGrid.selectedIndex;
        this.selectIndex = this.forecastMonitorGrid.selectedIndex;
      } else {
        this.forecastMonitorGridSub.selectedIndex = -1;
        this.forecastMonitorGrid.selectedIndex = -1
        this.selectIndex = -1;
      }
    }
    //else {
    // let date: string = event.target.data.date;
    // if (date == "Grand total") {
    //   // This block will open all nodes in the tree
    //   for( let k = 0 ; k<this.mainXml.grid.rows.row.length; k++) {
    //     let grand = this.mainXml.grid.rows.row[k];
    //     if (grand.date.content != "Grand total") {
    //       grand.visible=true;
    //       grand.expand.opened=true;
    //     } else if (grand.date.content == "Grand total") {
    //       grand.expand.opened=true;
    //     }
    //   }
    //   this.forecastMonitorGrid.gridData= this.deepCopy(this.mainXml.grid.rows.row).filter(x=>x.visible == true);
    //   this.forecastMonitorGridSub.gridData= this.deepCopy(this.mainXml.grid.rows.row).filter(x=>x.visible == true);
    //  } else {
    // for( let j = 0 ; j<this.mainXml.grid.rows.row.length; j++) {
    //   let total = this.mainXml.grid.rows.row[j];
    //   if (total.bucket == event.target.data.bucket && (String(total.date).search("otal") < 0)) {
    //     total.visible=true;
    //   } else if (total.bucket == event.target.data.bucket && (String(total.date).search("otal") > 0)) {
    //     total.expand.open=true;
    //   }
    // }
    // this.forecastMonitorGrid.gridData= this.deepCopy(this.mainXml.grid.rows.row).filter(x=>x.visible == true);
    // this.forecastMonitorGridSub.gridData= this.deepCopy(this.mainXml.grid.rows.row).filter(x=>x.visible == true);
    // This block will open nodes in the subnode
    // }
    // }
  }
  changeTreeDataOnLoad(): void {
      //remoe seleced index when expanding/collapsing like flex
      this.forecastMonitorGrid.selectedIndex = -1;
      this.forecastMonitorGridSub.selectedIndex = -1;
      let grid2dataset = [];
      JSONReader.jsonpath(this.mainXml, '$.forecastmonitor.grid.rows.row.*').forEach(function (value) {
        if (value.visible == true || value.visible == "true") {
          if (value === Object(value)) {
            grid2dataset.push(value);
          }
        }
      });
      this.gridDataExpand = grid2dataset;
      let grid2datasetSub = [];
      JSONReader.jsonpath(this.mainXml, '$.forecastmonitor.gridSub.rows.row.*').forEach(function (value) {
        if (value.visible == true || value.visible == "true") {
          if (value === Object(value)) {
            grid2datasetSub.push(value);
          }
        }
      });
      this.subGridDataExpand = grid2datasetSub;
      this.forecastMonitorGrid.gridData = {row: this.gridDataExpand, size: this.gridDataExpand.length};
      this.forecastMonitorGridSub.gridData = {row: this.subGridDataExpand, size: this.subGridDataExpand.length};

  }
  deepCopy(mainObj) {
    const objCopy = [];
    let key;
    for (key in mainObj) {
      objCopy[key] = mainObj[key];
    }
    return objCopy;
  }

  /**
   * The function initializes the menus in the right click event on the Entity Monitor screen.
   * The links are redirected to their respective pages.
   */
  initializeMenus(): void {
    // Creating a new instance for AboutScreen
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
    let contextMenuItem: ContextMenuItem = new ContextMenuItem('Show JSON');
    contextMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(contextMenuItem);
    this.contextMenu = this.screenVersion.svContextMenu;
  }

  /** This function is used to display the JSON
   *    for data showed in grid
   */
  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });

    // showXML.baseURL="forecastMonitor.do?method=displayForecastMonitorDetails";
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }

  startOfComms(): void {
    // disable the fields
    this.disableFields();
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.enableFields();
  }

  /**
   * Disable interface, turn off certain UI elements when a request is made to the server
   */
  private disableFields(): void {
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
  }

  /**
   * Enable interface, turn on certain UI elements when a request is made to the server
   */
  private enableFields(): void {
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
  }

  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   * @param event:ResultEvent
   */
  sendDataResult(event): void {

    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        this.updateData("yes");
      }
    }
  }

  inputDataResult(event) {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop;
      } else {

        // Retrieves the Json from ResultEvent
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);

        if (this.jsonReader.getRequestReplyStatus()) {
          let sourceColumnXml = this.deepCopy(this.lastRecievedJSON);
          this.preColumnXml = this.deepCopy(this.lastRecievedJSON);
          this.exportContainer.enabled = true;
          this.dataBuildingText.visible = false;
          let lastRef = this.jsonReader.getScreenAttributes()["lastRefTime"];
          this.lastRefTime.text = lastRef.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');
          this.refreshRate = Number(this.jsonReader.getScreenAttributes()["refresh"]);
          this.lblTemplateId.text = this.jsonReader.getScreenAttributes()["templateId"];
          this.lblTemplateId.toolTip = this.jsonReader.getScreenAttributes()["templateName"];
          if ((this.preColumnXml != this.prevRecievedJSON || !this.refreshFlag)) {
            this.cbEntity.setComboData(this.jsonReader.getSelects(), false);
            this.selectedEntity.text = this.cbEntity.selectedItem.value;
            this.cbCurrency.setComboData(this.jsonReader.getSelects(), false);
            this.selectedCurrency.text = this.cbCurrency.selectedItem.value;

            // if(!isGrandTotalExpanded){
            //   this.subGridDataCollapsedNodes.push(this.mainXml.forecastmonitor.grid.rows.row[i]);
            // }
            // this.mainXml.forecastmonitor.grid.rows.row[i].expand.opened = !isGrandTotalExpanded;// !e.expand.opened;
            // this.mainXml.forecastmonitor.grid.rows.row[i].visible = !isGrandTotalExpanded;//!e.visible;
            // this.mainXml.forecastmonitor.gridSub.rows.row[i].visible = !isGrandTotalExpanded;

            
            let listOfCollapsedNodes = [];
            let isGrandTotalCollapsed = false;

            if (!this.jsonReader.isDataBuilding()) {

              for (let i = 0; i < this.forecastMonitorGrid.dataProvider.length; i++) {
                if(this.forecastMonitorGrid.dataProvider[i].slickgrid_rowcontent.expand.opened == false){
                  listOfCollapsedNodes.push(this.forecastMonitorGrid.dataProvider[i].slickgrid_rowcontent.bucket.content)
                  if(this.forecastMonitorGrid.dataProvider[i].slickgrid_rowcontent.date.content=='Grand total')
                    isGrandTotalCollapsed = true;
                }
  
              }

            


              this.forecastMonitorGrid.GroupId = "date";
              this.forecastMonitorGrid.CustomGrid(this.lastRecievedJSON.forecastmonitor.grid.metadata);
              this.forecastMonitorGrid.rowHeight = 22;
              this.forecastMonitorGrid.styleName = "dataGridNormal";
              let rowData = this.lastRecievedJSON.forecastmonitor.grid.rows;
              let rowDataSub = this.lastRecievedJSON.forecastmonitor.gridSub.rows;
              for (let i = 0; i < this.forecastMonitorGrid.columnDefinitions.length; i++) {
                let column = this.forecastMonitorGrid.columnDefinitions[i];
                if (column.field == "date") {
                  for (let j = 0; j < rowData.row.length; j++) {
                    rowData.row[j].date.bold = Boolean(rowData.row[j].date.haschildren);
                  }
                }
              }

                for (let i = 0; i < this.lastRecievedJSON.forecastmonitor.grid.rows.row.length; i++) {
                  let e = this.lastRecievedJSON.forecastmonitor.grid.rows.row[i];
                  if (e.date.content != "Grand total") {
                    this.lastRecievedJSON.forecastmonitor.grid.rows.row[i].expand.opened = !isGrandTotalCollapsed;// !e.expand.opened;
                    this.lastRecievedJSON.forecastmonitor.grid.rows.row[i].visible = !isGrandTotalCollapsed;//!e.visible;
                    this.lastRecievedJSON.forecastmonitor.gridSub.rows.row[i].visible = !isGrandTotalCollapsed;
                  } else if (e.date.content == "Grand total") {
                    // this.lastRecievedJSON.forecastmonitor.grid.rows.row[i].expand.opened = !e.expand.opened;
                  }
                }

              for (let j = 0; j < rowData.row.length; j++) {
                if(listOfCollapsedNodes.includes(rowData.row[j].bucket.content)){
                  if(rowData.row[j].date.content.indexOf('otal') !== -1) {
                    rowData.row[j].expand.opened = false;
                  }else {
                    rowData.row[j].visible = false;
                    rowDataSub.row[j].visible = false;
                  }
                }
              }


              
             

              //save expand/collapse state after refresh
             /* if (this.gridDataExpand.length > 0) {
                this.forecastMonitorGrid.gridData = {row: this.gridDataExpand, size: this.gridDataExpand.length};
              } else {
                */
                this.forecastMonitorGrid.gridData = this.jsonReader.getGridData(); // this.deepCopy(this.lastRecievedJSON.forecastmonitor.grid.rows.row).filter(x=>x.modifystate != "delete");
                this.forecastMonitorGrid.setRowSize = this.lastRecievedJSON.forecastmonitor.grid.rows.size;
              //}


              this.columnChangeFlag = true;
              this.forecastMonitorGridSub.CustomGrid(this.lastRecievedJSON.forecastmonitor.gridSub.metadata);
              this.forecastMonitorGridSub.rowHeight = 22;
              this.forecastMonitorGridSub.styleName = "dataGridNormal";

             
              for (let i = 0; i < this.forecastMonitorGridSub.columnDefinitions.length; i++) {
                let column = this.forecastMonitorGridSub.columnDefinitions[i];

                if (column.field == "assumption") {
                  for (let j = 0; j < rowDataSub.row.length; j++) {
                    rowDataSub.row[j].assumption.clickable = Boolean(!rowDataSub.row[j].date.bold);
                  }
                  break;
                }
              }


              setTimeout(() => {
                this.changeTreeDataOnLoad()
              }, 0);
              //save expand/collapse state after refresh
              /*if (this.subGridDataExpand.length > 0) {
                this.forecastMonitorGridSub.gridData = {
                  row: this.subGridDataExpand,
                  size: this.subGridDataExpand.length
                };
              } else {*/

                this.forecastMonitorGridSub.gridData = this.lastRecievedJSON.forecastmonitor.gridSub.rows; // this.deepCopy(this.lastRecievedJSON.forecastmonitor.grid.rows.row).filter(x=>x.modifystate != "delete");
                this.forecastMonitorGridSub.setRowSize = this.lastRecievedJSON.forecastmonitor.gridSub.rows.size;

                
              //}
              let num: number = this.jsonReader.getScreenAttributes()["dividerWidth"];
              if(!this.isUpdateData)
               this.cvGridContainer.widthLeft = ((num*100)/(window.screen.width)).toFixed(2) +"%"
              //this.forecastMonitorGridSub.width = Number(this.cvGridContainer.width) - 6 - this.forecastMonitorGrid.width;

              this.forecastMonitorGridSub.ITEM_CLICK.subscribe((selectedRowData) => {
                this.onSubGridCellClick(selectedRowData);
              });

              this.forecastMonitorGridSub.onRowClick = (event) => {
                this.subGridRowChange(event);
              };

              // this.forecastMonitorGridSub.addEventListener(CellEvent.CELL_CLICK, onSubGridCellClick);
              // this.forecastMonitorGridSub.addEventListener(AdvancedDataGridEvent.ITEM_EDIT_END, validate);
              // this.forecastMonitorGridSub.addEventListener(AdvancedDataGridEvent.ITEM_EDIT_BEGIN, itemBegin);
              // this.forecastMonitorGridSub.addEventListener(AdvancedDataGridEvent.ITEM_FOCUS_OUT, itemFocusOut);
              // this.forecastMonitorGridSub.addEventListener(AdvancedDataGridEvent.ITEM_EDIT_BEGINNING, itemBeginning);
              // this.forecastMonitorGridSub.addEventListener(MouseEvent.MOUSE_DOWN, subGridRowChange);
              // this.forecastMonitorGridSub.addEventListener(ScrollEvent.SCROLL, scrollUpdate);
              // this. forecastMonitorGridSub.setListeners();
              //  }

              this.forecastMonitorGridSub.validate = (newValue, previousValue) => {
                if(newValue['scenario'] !=previousValue['scenario']) {
                  this.validate(newValue, previousValue);
                  //this.forecastMonitorGridSub.refresh();
                }
              };
              // FIXME:
              // if (this.columnChangeFlag) {
              //   let columnListXml = this.lastRecievedJSON.forecastmonitor.grid.metadata;
              //   this.columnLength = this.preColumnXml.forecastmonitor.grid.metadata.columns.column.length;
              //   this.forecastMonitorGrid.CustomGrid(columnListXml);
              //   let sourceSubColumnXmlList= this.deepCopy(sourceColumnXml);
              //
              //   let subColumnXmlList = sourceSubColumnXmlList.forecastmonitor.gridSub.metadata.columns;
              //   this.forecastMonitorGridSub.CustomGrid(subColumnXmlList);
              //   this.selectIndex=-1;
              // }

              if ((this.preColumnXml != this.prevRecievedJSON || !this.refreshFlag)) {
                this.mainXml = this.deepCopy(this.preColumnXml);
                // FIXME:
                // this.forecastMonitorGrid.gridData= this.mainXml.grid.rows.row.(@visible == "true");
                // this.forecastMonitorGrid.setRowSize=(this.mainXml.grid.rows.row ).length;
                // this.forecastMonitorGridSub.gridData = this.mainXml.grid.rows.row.(@visible == "true");
                // this.forecastMonitorGridSub.setRowSize=this.mainXml.grid.rows.row.length;
                this.selectIndex = -1;
              }

              this.refreshFlag = true;
              if (this.refreshStatus == "Y") {
                this.forecastMonitorGrid.selectedIndex = this.selectIndex;
                this.forecastMonitorGridSub.selectedIndex = this.selectIndex;
              }

              if (this.preColumnXml.forecastmonitor.grid.rows.row.length < 1) {
                this.exportContainer.enabled = false;
              } else {
                this.exportContainer.enabled = true;
              }

              // FIXME:
              // let forecastGridHBar: number = this.forecastMonitorGrid.horizontalScrollPosition;
              // this.forecastMonitorGrid.horizontalScrollPosition = forecastGridHBar;
              // let maxGridHScrollPosition: number= this.forecastMonitorGrid.maxHorizontalScrollPosition;
              // if (this.forecastMonitorGrid.horizontalScrollPosition > 0) {
              if (this.refreshStatus == "N") {
                //     this.forecastMonitorGrid.horizontalScrollPosition=0;
              } else if (this.refreshStatus == "Y") {
                //     if (forecastGridHBar >= maxGridHScrollPosition) {
                //       this.forecastMonitorGrid.horizontalScrollPosition=maxGridHScrollPosition;
                this.refreshStatus = "N";
                //     } else {
                //       this.forecastMonitorGrid.horizontalScrollPosition=forecastGridHBar;
                //       this.refreshStatus="N";
                //     }
              }
              // }

              // FIXME:
              // if (this.forecastMonitorGridSub.horizontalScrollPosition > 0) {
              if (this.refreshStatus == "N") {
                //     this.forecastMonitorGridSub.horizontalScrollPosition=0;
              } else if (this.refreshStatus == "Y") {
                //     if (forecastSubGridHBar >= maxSubGridHScrollPosition) {
                //       this.forecastMonitorGridSub.horizontalScrollPosition=maxSubGridHScrollPosition;
                this.refreshStatus = "N";
                //     } else {
                //       this.forecastMonitorGridSub.horizontalScrollPosition= forecastSubGridHBar;
                //       this.refreshStatus="N";
                //     }
              }
              // }
              this.prevRecievedJSON = this.lastRecievedJSON;
            } else {
              this.dataBuildingText.visible = true;
            }
          }

          // if the currency combo box is empty, then display the alert
          if (StringUtils.trim(this.cbCurrency.selectedLabel) == "") {

            this.swtAlert.error(SwtUtil.getPredictMessage("alert.currencyAccess", null), 'Error');
            if (this.autoRefresh != null) {
              if (this.autoRefresh.running) {
                this.autoRefresh.stop();
                this.autoRefresh = null;
              } else {
                this.autoRefresh = null;
              }
            }
            // enable the exportContainer
            this.exportContainer.enabled = false;
          } else if (this.autoRefresh == null) {
            this.refreshRate = parseInt(this.jsonReader.getRefreshRate(), 10);
            this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
            this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
          }

        } else {
          if (this.lastRecievedJSON != null && this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), 'Error');
          }
        }

        if (this.autoRefresh != null) {
          if (!this.autoRefresh.running) {
            this.autoRefresh.start();
          }
        }
        // else
      }
    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'Forecast Monitor', 'inputDataResult');
    }

  }


  /**
   * This is used to validate the editable cell value
   * @param event:AdvancedDataGridEvent
   */
  private index = 0;
  validate(newValue, previousValue): void {
    let currBal: string;
    let newVal: string = newValue;

    let oldVal: string = previousValue;
      this.index ++;
     if (this.index %2 ==0) {
        this.alertPreventFlag = true;
        if ((newVal["date"]).search("otal") > 0) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecastMonitor.totalValues', null), 'Warning', null, null, ()=> {
            newVal['scenario'] = oldVal["scenario"]
            this.forecastMonitorGridSub.dataProvider[this.forecastMonitorGridSub.selectedIndex].slickgrid_rowcontent.scenario.content = oldVal["scenario"]
            this.forecastMonitorGridSub.refresh();
          });

          this.autoRefresh.start();
          this.refreshStatus = "N";
        } else if (newVal["scenario"].length == 0) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecastMonitor.enterNumber', null), 'Warning', null, null, ()=> {
            newVal['scenario'] = oldVal["scenario"]
            this.forecastMonitorGridSub.dataProvider[this.forecastMonitorGridSub.selectedIndex].slickgrid_rowcontent.scenario.content = oldVal["scenario"]
            this.forecastMonitorGridSub.refresh();
          });
        } else {
          let minusStr: string= newVal["scenario"].charAt(0);
          if (minusStr == "-") {
             currBal= newVal["scenario"].substring(1, newVal["scenario"].length);
          } else {
            currBal=newVal["scenario"];
          }
          let changedBal: string =  ExternalInterface.call("formatCurrency", currBal);

          if (minusStr == "-") {
            changedBal="-" + changedBal;
          }
          if (changedBal == "invalid" || changedBal == null || changedBal.length <= 0) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecastMonitor.validNumber', null), 'Warning');
          } else {
            this.saveHandle();
          }
        }
      }

  }

  /**
   * This function is called when main grid row click
   * @param event:ListEvent
   */
  mainGridRowChange(event): void {
    // set the forecast monitor subgrid selected index
    if (this.forecastMonitorGrid.selectedIndices.length > 0) {
      this.forecastMonitorGridSub.selectedIndex = this.forecastMonitorGrid.selectedIndex;
    }
  }

  /**
   * This function is called when Sub grid row click
   * @param event:ListEvent
   */
  subGridRowChange(event): void {
    // set the forecast monitor maingrid selected index
    if (this.forecastMonitorGridSub.selectedIndices.length > 0) {
      this.forecastMonitorGrid.selectedIndex = this.forecastMonitorGridSub.selectedIndex;
    }
  }

  /**
   * This function is called when main grid click
   * @param event:CellEvent
   */
  onMainGridCellClick(event): void {
    if (this.forecastMonitorGrid.selectedIndices.length > 0) {
      this.forecastMonitorGridSub.selectedIndex = this.forecastMonitorGrid.selectedIndex;
      this.selectIndex = this.forecastMonitorGrid.selectedIndex;
    } else {
      this.forecastMonitorGridSub.selectedIndex = -1;
      this.selectIndex = -1;
    }
  }

  /**
   * This function is called when sub grid click
   * @param event:CellEvent
   */
  onSubGridCellClick(event): void {
    if (this.forecastMonitorGridSub.selectedIndices.length > 0) {
      this.forecastMonitorGrid.selectedIndex = this.forecastMonitorGridSub.selectedIndex;
      this.selectIndex = this.forecastMonitorGridSub.selectedIndex;
    } else {
      this.forecastMonitorGrid.selectedIndex = -1;
      this.selectIndex = -1;
    }
    let fieldName = event.target.field;
    let isClickable = (event.target.data.slickgrid_rowcontent[fieldName].content) ? event.target.data.slickgrid_rowcontent[fieldName].content : "";
    let assumptions = new Object();
    if (isClickable) {
      if (fieldName == "assumption") {
        assumptions["entity"] = this.cbEntity.selectedLabel;
        assumptions["currency"] = this.cbCurrency.selectedLabel;
        assumptions["date"] = String(event.target.data.slickgrid_rowcontent['date'].content);
        assumptions["templateId"] = this.lblTemplateId.text;
        let isGrandTotal = (event.target.data.slickgrid_rowcontent['maingroup'] && String(event.target.data.slickgrid_rowcontent['maingroup'].content).toLowerCase().search("total") > 0);
        if (!isGrandTotal) {
          let wrapperFunction = "openAssumptionWindow";
         ExternalInterface.call(wrapperFunction, assumptions);
          if (this.autoRefresh != null) {
            this.autoRefresh.stop();
            this.refreshStatus = "N";
          }
        }
      }
    }
    // End: code modified by Bala for 1053 Beta testing on 14-Sep-2011 - selection should be same for both grids
  }

  export(type: string): void {
    let selects = [];
    // Sets the column name
    selects.push("Entity=" + this.cbEntity.selectedLabel);
    if (this.selectedCurrency.text.search("(Millions)") > -1) {
      selects.push("Ccy=" + this.cbCurrency.selectedLabel + " (Millions)");
    } else if (this.selectedCurrency.text.search("(Billions)") > -1) {
      selects.push("Ccy=" + this.cbCurrency.selectedLabel + " (Billions)");
    } else if (this.selectedCurrency.text.search("(Thousands)") > -1) {
      selects.push("Ccy=" + this.cbCurrency.selectedLabel + " (Thousands)");
    } else {
      selects.push("Ccy=" + this.cbCurrency.selectedLabel);
    }
    selects.push("Template ID=" + this.lblTemplateId.text);
    const copyMeta = $.extend(true, {}, this.lastRecievedJSON.forecastmonitor.grid.metadata.columns);
    for (let i = 0; i < copyMeta.column.length; i++) {
      if (copyMeta.column[i].dataelement == "expand") {
        copyMeta.column.splice(i, 1); // remove expand column
        break;
      }
    }
    this.exportContainer.convertData(copyMeta, this.forecastMonitorGrid, null, selects, type, true);

  }


  /**
   * If a fault occurs with the connection with the server then display the lost connection label.<br>
   * @param event:FaultEvent
   **/
  sendDataFault(event): void {
    this.lostConnectionText.visible = true;
    // this.lostConnectionText.buttonMode=true;
    // this.lostConnectionText.useHandCursor=true;
    // this.lostConnectionText.mouseChildren=false;
    this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }


  /**
   * This method is used to display the connection error
   **/
  connError(): void {
    this.swtAlert.error("" + this.invalidComms, "Error");
  }

  /**
   * This function serves when the rate button is clicked it opens the popup withthe
   * autorefresh rate set.
   **/
  rateHandler(): void {
    try {
      this.autoRefresh.stop();
      // Instantiates the OptionPopUp object
      this.win = SwtPopUpManager.createPopUp(this,
        RatePopUp,
        {
          title: "Refresh Rate", // childTitle,
          refreshText: this.refreshRate
        });
      this.win.width = "340";
      this.win.height = "150";
      this.win.id = "rateWindow";
      this.win.enableResize = false;
      this.win.isModal = true;
      this.win.showControls = true;
      this.win.display();
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "Forecast Monitor", "rateHandler", this.errorLocation);
    }
  }


  /**
   * This function serves when the rate button is clicked it opens the popup withthe
   * autorefresh rate set.
   **/
  optionClick(): void {
    try {
      ExternalInterface.call("openForecastOptionsWindow");
      // Starts the auto refresh if it is not null
      if (this.autoRefresh != null) {
        this.autoRefresh.stop();
        this.refreshStatus = "N";
      }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "Forecast Monitor", "optionsHandler", this.errorLocation);
    }
  }

  /**
   * This function handles the submitted refresh rate and updates it.
   *
   * @param res
   */
  public saveRefreshRate(res): void {
    try {
      if (isNaN(parseInt(res, 10))) {
        this.swtAlert.error(SwtUtil.getPredictMessage('alert.forecastMonitor.notNumber', null), "Error");
      } else {
        let selectedRateBelowMinimum = false;
        this.refreshRate = Number(res);
        if (this.refreshRate < 5) {
          this.refreshRate = 5;
          selectedRateBelowMinimum = true;
        }
        if (selectedRateBelowMinimum) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecastMonitor.refreshRate', null), "Warning");
        }
/*
        if (this.autoRefresh) {
          this.autoRefresh.delay(this.refreshRate * 1000);
        }
*/
        this.itemId = ExternalInterface.call('eval', 'itemId');
        this.rateRequest = ExternalInterface.call("getUpdateRefreshRequest", this.refreshRate, this.itemId);
        if (this.rateRequest != null && this.rateRequest != "") {
          this.updateRefreshRate.encodeURL = false;
          this.updateRefreshRate.url = this.baseURL + this.rateRequest;
          this.updateRefreshRate.send();
        }
        this.autoRefreshAfterStop();
      }
    } catch (e) {
      console.log(e, this.moduleId, 'Forecast Monitor', 'saveRefreshRate');
    }
  }


  optionDateResult() {
    this.updateData("no");
  }


  autoRefreshAfterStop() {
    let timeLeft = this.refreshRate;
    clearInterval(this.interval);
    this.interval = setInterval(() => {
      this.dataRefresh();
    }, timeLeft * 1000);
  }

  /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  keyDownEventHandler(event): void {
    try {
      let eventString = Object(focusManager.getFocus()).name;
      if ((event.keyCode == Keyboard.ENTER)) {
        if (eventString == "closeButton") {
          this.closeHandler();
        } else if (eventString == "csv") {
          this.report('csv');
        } else if (eventString == "excel") {
          this.report('xls');
        } else if (eventString == "pdf") {
          this.report('pdf');
        } else if (eventString === 'helpIcon') {
          this.doHelp();
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, 'Forecast Monitor', 'keyDownEventHandler', this.errorLocation);
    }
  }


  /**
   * report
   *
   * @param type: String
   *
   * This is a report icon action handler method
   */
  report(type: string): void {
    try {

    } catch (error) {
      console.log("error", error);
      SwtUtil.logError(error, this.moduleId, 'Forecast Monitor', 'report', this.errorLocation);
    }
  }

  /**
   * printPage
   *
   * @param event:Event
   *
   * Method to get call the action to get reports
   */
  printPage(event): void {
    try {
      ExternalInterface.call('printPage');

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, 'Forecast Monitor', 'printPage', this.errorLocation);
    }
  }

  /**
   * dataRefresh()
   * Timing result methods
   **/
  dataRefresh(): void {
    try {
      this.updateData("no");
      // stop the timer
      // this.autoRefresh.stop();
    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, 'Forecast Monitor', 'dataRefresh', this.errorLocation);
    }
  }


  entityChangeCombo(event) {
    try {
      this.comboChange = true;
      this.selectedEntity.text = this.cbEntity.selectedItem.value;
      this.comboUpdateData('no', 'entity');
    } catch (e) {
      console.log(e, this.moduleId, 'Forecast Monitor', 'changeCombo');
    }

  }

  currencyChangeCombo(event): void {
    this.comboChange = true;
    this.comboUpdateData('no', 'currency');
    this.selectedCurrency.text = this.cbCurrency.selectedItem.value;
  }

  /**
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  private isUpdateData = false;
  public updateData(autoRefresh: string): void {
    try {
      this.isUpdateData = true;
      this.requestParams = [];
      this.actionPath = "forecastMonitor.do?";
      this.actionMethod = "" + "menuAccess=" + ((this.menuAccessIdParent) ? this.menuAccessIdParent : 0);
      this.requestParams["selectedEntityId"] = this.cbEntity.selectedItem.content;
      this.requestParams["selectedCurrency"] = this.cbCurrency.selectedItem.content;
      this.requestParams["autoRefresh"] = autoRefresh;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, "Forecast Monitor", "updateData");
      SwtUtil.logError(e, this.moduleId, 'Forecast Monitor', 'updateData', this.errorLocation);
    }
  }


  /**
   * Update the data, this is called whenever a fresh of the data is required.
   * This could be called from either a change in a combobox selection of from the timer
   * @param autoRefresh:String
   * @param combo:String
   **/
  comboUpdateData(autoRefresh: string, combo: string): void {
    this.requestParams = [];
    this.isUpdateData= true;
    if (combo == "entity") {
      this.requestParams["selectedCurrency"] = "";
      this.requestParams["retainCurrency"] = this.cbCurrency.selectedItem.content;
    } else {
      this.requestParams["selectedCurrency"] = this.cbCurrency.selectedItem.content;
    }
    this.requestParams["selectedEntityId"] = this.cbEntity.selectedItem.content;
    this.requestParams["autoRefresh"] = autoRefresh;

    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.send(this.requestParams);
  }


  showHideControlBar(): void {
    if (this.controlBarHideFlag) {
      this.swtControlBar.visible = true;
      this.imgShowHideControlBar.styleName = 'minusIcon';
      this.imgShowHideControlBar.toolTip = "Hide Button Bar";

    } else {
      this.swtControlBar.visible = false;
      this.swtControlBar.includeInLayout = false;
      this.imgShowHideControlBar.styleName = 'plusIcon';
      this.imgShowHideControlBar.toolTip = 'Show Button Bar';
      this.vbGridContainer.height = "100%";
      this.forecastMonitorGrid.resizeGrid();
      this.forecastMonitorGridSub.resizeGrid();
    }
    this.controlBarHideFlag = !this.controlBarHideFlag;
  }

  showHideButtonBar(): void {
    // Checks the status of the Button Bar
    if (this.buttonBarHideFlag) {
      this.swtButtonBar.visible = true;
      this.imgShowHideButtonBar.styleName = 'minusIcon';
      this.imgShowHideButtonBar.toolTip = "Hide Button Bar";
    } else {
      this.swtButtonBar.visible = false;
      this.swtButtonBar.includeInLayout = false;
      this.imgShowHideButtonBar.styleName = 'plusIcon';
      this.imgShowHideButtonBar.toolTip = 'Show Button Bar';
      this.vbGridContainer.height = "100%";
      this.forecastMonitorGrid.resizeGrid();
      this.forecastMonitorGridSub.resizeGrid();
    }
    this.buttonBarHideFlag = !this.buttonBarHideFlag;
  }


  /**
   * This function is used to set the refresh rate for the monitors.
   */
  closeHandler() {
    try {
      ExternalInterface.call("close");
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'Forecast Monitor', ' closeHandler', this.errorLocation);
    }
  }


  inputDataFault(): void {
    this.lostConnectionText.visible = true;
    if (this.autoRefresh != null) {
      if (!this.autoRefresh.running) {
        this.autoRefresh.start();
      }
    }
  }

  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ForecastMonitor', 'doHelp', this.errorLocation);
    }
  }
    saveHandle() {
      var requestParams = [];
      this.forecastMonitorGridSub.refresh();
      let subGridChanges = this.forecastMonitorGridSub.changes.getValues();
      if(subGridChanges.length >0) {
        for (let i =0; i< subGridChanges.length; i++) {
          if(this.forecastMonitorGridSub.selectedItem.date.content == subGridChanges[i].crud_data.date) {
            requestParams[subGridChanges[i].crud_data.date]=subGridChanges[i].crud_data.scenario;
            requestParams["editupdate"]=subGridChanges[i].crud_data.date;
            requestParams["entity"]=this.cbEntity.selectedLabel;
            requestParams["currency"]= this.cbCurrency.selectedLabel;
          }

        }


        // Checks if there is anything to be updated, else an alert message will be shown
        if ((requestParams["editupdate"] as String).length > 0)
        {
          this.refreshFlag=false;
          this.sendData.send(requestParams);
          this.forecastMonitorGridSub.changes.clear();
        }
      }

    }
  updateWidths(event): void {
    let columnWidth=[];
    // add divider value
    columnWidth.push("divider=" + this.currDividerPosition);
    this.requestParams=[];
    this.resizeData.encodeURL = false;
    this.requestParams["width"]=columnWidth.toString();
    this.actionPath = 'forecastMonitor.do?';
    this.actionMethod = 'method=saveColumnWidth&';
    this.resizeData.cbStart= this.startOfComms.bind(this);
    this.resizeData.cbStop= this.endOfComms.bind(this);
    this.resizeData.cbResult = ( event) => {
      this.inputDataResultColumnsChange(event);
    };
    this.resizeData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.resizeData.send(this.requestParams);
  }
  columnWidthChange(event, grid:string): void {
    this.requestParams = [];
    this.resizeData.encodeURL = false;
    this.actionMethod = 'method=saveColumnWidth';
    this.actionPath = 'forecastMonitor.do?';
    this.requestParams['method'] = 'saveColumnWidth';
    let gridColumns :any;
    if(grid =="main")
       gridColumns = this.forecastMonitorGrid.gridObj.getColumns();
     else
     gridColumns = this.forecastMonitorGridSub.gridObj.getColumns();
    for (let i = 0; i < gridColumns.length; i++) {
      if (gridColumns[i].id != "dummy" && gridColumns[i].width != gridColumns[i].previousWidth) {
        let width = gridColumns[i].width;
        if (width < 20) {
          width = 20;
        }
        if (width > 200) {
          width = 200;
        }
        if(grid == "main")
         this.requestParams['width'] = "main="+width;
       else
         this.requestParams['width'] = "sub="+width;
        this.resizeData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.resizeData.send(this.requestParams);
      }

    }
  }

  inputDataResultColumnsChange(event): void {
    let index: any;
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      let jsonResponse: JSONReader = new JSONReader();
      jsonResponse.setInputJSON(event);
      if (jsonResponse.getRequestReplyMessage() !== "Column width saved ok") {
        this.swtAlert.error(SwtUtil.getPredictMessage('error.contactAdmin', null) + '\n' + jsonResponse.getRequestReplyMessage());
      }
    }
  }
  startAutoRefresh(refreshStart: string): void {

    if (this.cbCurrency.selectedLabel != "") {
      if (refreshStart == "Y") {
        this.updateData('no');
        if (this.autoRefresh) {
          this.autoRefresh.start();
        }

      }
    }
  }

}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: ForecastMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ForecastMonitor],
  entryComponents: []
})
export class ForecastMonitorModule { }
