<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" height="85%">
      <VBox width="100%" height="100%">
        <Grid>
          <GridRow>
            <GridItem width="15%">
              <SwtLabel id="entityLabel" fontWeight="bold" #entityLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width="50%">
              <SwtComboBox id="entityCombo" #entityCombo
                           (change)="changeCombo($event)"
                           dataLabel="entity"
                           width="135">
              </SwtComboBox>
            </GridItem>
            <GridItem width="35%">
              <SwtLabel
                id="selectedEntity"
                #selectedEntity
                textAlign="left"
                fontWeight="normal">

              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="15%">
              <SwtLabel id="currencyLabel" fontWeight="bold"
                        #currencyLabel>
              </SwtLabel>
            </GridItem>
            <GridItem width="50%">
              <SwtComboBox id="ccyCombo" #ccyCombo
                           (change)="changeCombo($event)"
                           dataLabel="currency"
                           width="85">
              </SwtComboBox>
            </GridItem>
            <GridItem width="35%">
              <SwtLabel id="selectedCcy"
                        #selectedCcy
                        fontWeight="normal">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow><GridItem width="15%">
            <SwtLabel id="accountLabel" fontWeight="bold"
                      #accountLabel>
            </SwtLabel>
          </GridItem>
            <GridItem width="50%">
              <SwtComboBox id="accountCombo" #accountCombo
                           (change)="changeCombo($event)"
                           dataLabel="accounts"
                           width="300">
              </SwtComboBox>
            </GridItem>
            <GridItem width="35%">
              <SwtLabel id="selectedAccount"
                        #selectedAccount
                        fontWeight="normal">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="15%">
              <SwtLabel #attributeLabel
                        id="attributeLabel">
              </SwtLabel>
            </GridItem>
            <GridItem width="50%">
              <SwtComboBox  id="attributeCombo" #attributeCombo
                            (change)="changeCombo($event)"
                            prompt = "Please select ..."
                            dataLabel="attributes"
                            width="300">
              </SwtComboBox>
            </GridItem>
            <GridItem width="35%">
              <SwtLabel  id="selectedAttribute"
                         #selectedAttribute
                         fontWeight="normal">
              </SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="15%">
              <SwtLabel #startDateLabel id="startDateLabel" fontWeight="bold">
              </SwtLabel>
            </GridItem>
            <GridItem width="17%">
              <SwtDateField id="effectiveDate" #effectiveDate
                            width="70"
                            (change)="selectedDate_changeHandler(effectiveDate)"
                            restrict="0-9/">
              </SwtDateField>
            </GridItem>
            <GridItem width="7%">
              <SwtLabel  #timeLabel textAlign="left" fontWeight="bold"></SwtLabel>
            </GridItem>
            <GridItem width="40%">
              <SwtStepper  #timeHours
                           (focusOut)="formatNumber(timeHours)"
                           width="20"
                           minimum="0"
                           maximum="23">
              </SwtStepper>
              <SwtStepper  #timeMinutes
                           (focusOut)="formatNumber(timeMinutes)"
                           width="20"
                           minimum="0"
                           maximum="59">
              </SwtStepper>
              <SwtStepper  #timeSeconds
                           (focusOut)="formatNumber(timeSeconds)"
                           width="20"
                           minimum="0"
                           maximum="59">
              </SwtStepper>
            </GridItem>
          </GridRow>
          <GridRow>
            <GridItem width="15%">
              <SwtLabel  #typeLabel width="100" fontWeight="bold"></SwtLabel>
            </GridItem>
            <GridItem width="70%">
              <SwtComboBox id="typesCombo"
                           #typesCombo
                           dataLabel="types"
                           enabled="false"
                           width="100">
              </SwtComboBox>
            </GridItem>
          </GridRow>
          <GridRow #hboxTypeNumeric>
            <GridItem width="15%">
              <SwtLabel #valueNumLabel fontWeight="bold">
              </SwtLabel>
            </GridItem>
            <GridItem width="70%">
              <SwtTextInput id="valueNumField"
                            #valueNumField
                            maxChars="38"
                            restrict="0-9.,-"
                            textAlign="right"
                            (focusOut)="numberValidator(valueNumField)"
                            width="180">
              </SwtTextInput>
            </GridItem>
          </GridRow>
          <GridRow #hboxTypeDate>
            <GridItem width="15%">
              <SwtLabel #valueDateLabel id="valueDateLabel">
              </SwtLabel>
            </GridItem>
            <GridItem width="17%">
              <SwtDateField #valueDate id="valueDate"
                            width="70"
                            (change)="selectedDate_changeHandler(valueDate)"
                            [editable]="true"
                            restrict="0-9/">
              </SwtDateField>
            </GridItem>
            <GridItem width="7%">
              <SwtLabel  #valueTimeLabel textAlign="left" fontWeight="bold">
              </SwtLabel>
            </GridItem>
            <GridItem width="40%">
              <SwtStepper  #valueTimeHours
                           width="20"
                           (focusOut)="formatNumber(valueTimeHours)"
                           minimum="0"
                           maximum="23"
              pattern="">
              </SwtStepper>
              <SwtStepper  #valueTimeMinutes
                           width="20"
                           (focusOut)="formatNumber(valueTimeMinutes)"
                           minimum="0"
                           maximum="59">
              </SwtStepper>
              <SwtStepper  #valueTimeSeconds
                           (focusOut)="formatNumber(valueTimeSeconds)"
                           width="20"
                           minimum="0"
                           maximum="59">
              </SwtStepper>
            </GridItem>
          </GridRow>
          <GridRow #hboxTypeText>
            <GridItem width="15%">
              <SwtLabel #valueLabel fontWeight="bold">
              </SwtLabel>
            </GridItem>
            <GridItem width="70%">
              <SwtTextInput id="valueField"
                            #valueField
                            required ="false"
                            width="420">
              </SwtTextInput>
            </GridItem>
          </GridRow>
        </Grid>
      </VBox>
    </SwtCanvas>
    <SwtCanvas  id="canvasButtons"  width="100%"  height="15%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton #saveButton
                     id="saveButton"
                     [buttonMode]="true"
                     (click)="saveClickHandler()"
                     width="70">
          </SwtButton>
          <SwtButton  #closeButton
                      id="closeButton"
                      [buttonMode]="true"
                      (click)="closeHandler()"
                      width="70">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right">
          <SwtHelpButton id="helpIcon"
                         (click)="doHelp()">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
