<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox height="100%" width="100%">
    <SwtCanvas width="100%" height="7%">
      <VBox width="100%" height="100%">
        <HBox height="100%">
          <SwtLabel text="Archive" id="archiveLabel" #archiveLabel width="15%"></SwtLabel>
          <SwtComboBox id="archiveCombo" #archiveCombo   (change)="changeLocalCombo($event)" toolTip="Select Archive Database"
          dataLabel="archiveList" width="150">
        </SwtComboBox>
        <SwtLabel paddingLeft="20" id="selectedArchive" fontWeight="normal" #selectedArchive></SwtLabel>
        </HBox>
      </VBox>
    </SwtCanvas>
    <SwtCanvas width="100%" height="83%">
      <HBox width="100%" height="100%">
        <VBox width="15%" height="100%">
          <HBox width="100%" height="30%">
            <SwtPanel width="100%" height="100%" title="Status">
              <VBox width="100%" height="100%" paddingLeft="5">
                <SwtRadioButtonGroup #statusGroup id="statusGroup" align="vertical">
                  <SwtRadioItem #radioWaiting id="radioWaiting" value="W" label="Waiting" groupName="statusGroup">
                  </SwtRadioItem>
                  <SwtRadioItem #radioBlocked id="radioBlocked" value="B" label="Blocked" groupName="statusGroup">
                  </SwtRadioItem>
                  <SwtRadioItem #radioReleased id="radioReleased" value="R" label="Released" groupName="statusGroup">
                  </SwtRadioItem>
                  <SwtRadioItem #radioCancelled id="radioCancelled" value="C" label="Cancelled" groupName="statusGroup">
                  </SwtRadioItem>
                  <SwtRadioItem #radioStopped id="radioStopped" value="S" label="Stopped" groupName="statusGroup">
                  </SwtRadioItem>
                  <SwtRadioItem #radioAllStatus id="radioAllStatus" value="A" selected="true" label="All"
                    groupName="statusGroup"></SwtRadioItem>
                </SwtRadioButtonGroup>
              </VBox>
            </SwtPanel>
          </HBox>
          <HBox paddingTop="5" width="100%" height="17%">
            <SwtPanel width="100%" height="100%" title="Type">

              <VBox width="100%" height="100%" paddingLeft="5">
                <SwtRadioButtonGroup #typeGroup id="typeGroup" align="vertical">
                  <SwtRadioItem #radioWaiting id="radioCash" value="C" label="Cash" groupName="typeGroup">
                  </SwtRadioItem>
                  <SwtRadioItem #radioBlocked id="radioSecurties" value="U" label="Securities" groupName="typeGroup">
                  </SwtRadioItem>
                  <SwtRadioItem #radioReleased id="radioBoth" value="B" label="Both" selected="true"
                    groupName="typeGroup"></SwtRadioItem>
                </SwtRadioButtonGroup>
              </VBox>

            </SwtPanel>
          </HBox>
          <HBox paddingTop="5" width="100%" height="17%">
            <SwtPanel width="100%" height="100%" title="Time-Frame">

              <VBox width="100%" height="100%" paddingLeft="5">
                <SwtRadioButtonGroup #timeFrameRadioGroup   id="timeFrameRadioGroup"
                align="vertical">
                <SwtRadioItem #radioE id="radioE" value="E"   width="80" label="Entity" groupName="timeFrameRadioGroup" selected="true" ></SwtRadioItem>
                <SwtRadioItem #radioC id="radioC" value="C"  width="90" label="Currency" groupName="timeFrameRadioGroup"  ></SwtRadioItem>
                <SwtRadioItem #radioS id="radioS" value="S"   width="80" label="System" groupName="timeFrameRadioGroup"  ></SwtRadioItem>
                </SwtRadioButtonGroup>
              </VBox>

            </SwtPanel>
          </HBox>
        </VBox>
        <VBox width="85%" height="100%">
          <SwtCanvas width="100%" height="100%">
            <VBox width="100%" height="100%" verticalGap="0">
              <HBox height="6%" width="100%" horizontalGap="0">
                <HBox width="45%">
                <SwtLabel text="Amount From" width="150"></SwtLabel>
                <SwtTextInput id="amountFromTextInput" restrict="0-9,.TBMtbm"
                toolTip="Enter the From Amount"
                (focusOut)="validateAmount('amountFromTextInput')"
                 textAlign="right"
                #amountFromTextInput width="150">
                </SwtTextInput>
                </HBox>
                <HBox width="55%">
                <SwtLabel text="To" width="70"></SwtLabel>
                <SwtTextInput id="amountToTextInput" restrict="0-9,.TBMtbm"
                toolTip="Enter the To Amount"
                (focusOut)="validateAmount('amountToTextInput')"
                 textAlign="right"
                #amountToTextInput width="150"></SwtTextInput>
                </HBox>
              </HBox>
              <HBox height="6%" width="100%" horizontalGap="0">
                <HBox width="45%">
                <SwtLabel text="Value Date From" width="150"></SwtLabel>
                <SwtDateField #frmDateChooser id="frmDateChooser" textAlign="right" width="115" editable="true"
                              toolTip="Enter value Date From in {{dateFormatUpper}}"></SwtDateField>
                </HBox>
                <HBox width="55%">
                <SwtLabel text="To" width="70"></SwtLabel>
                <SwtDateField #toDateChooser id="toDateChooser" textAlign="right" width="115" editable="true"
                              toolTip="Enter value Date To in {{dateFormatUpper}}"></SwtDateField>
                </HBox>
              </HBox>

              <HBox height="6%" width="100%">
                <SwtLabel id="entityLabel" #currencyLabel text="Entity" width="150">
                </SwtLabel>
                <SwtComboBox id="entityCombo" #entityCombo (change)="changeCombo('entityCombo')" toolTip="Select Entity"
                  dataLabel="entityList" width="150">
                </SwtComboBox>
                <SwtLabel paddingLeft="20" id="selectedEntity" #selectedEntity>
                </SwtLabel>
              </HBox>
              <HBox height="6%" width="100%">
                <SwtLabel id="currencyLabel" #currencyLabel text="Currency" width="150">
                </SwtLabel>
                <SwtComboBox id="ccyCombo" #ccyCombo (change)="changeCombo('ccyCombo')" toolTip="Select currency code"
                  dataLabel="currencyList" width="100">
                </SwtComboBox>
                <SwtLabel paddingLeft="20" id="selectedCcy" #selectedCcy>
                </SwtLabel>
              </HBox>

              <HBox height="6%" width="100%">
                <SwtLabel text="Message Format" width="150">
                </SwtLabel>
                <SwtComboBox id="messageFormatCombo" #messageFormatCombo  (change)="changeLocalCombo($event)"
                  dataLabel="messageType" width="150">
                </SwtComboBox>
                <SwtLabel paddingLeft="20" id="selectedMessageType" #selectedMessageType>
                  </SwtLabel>
              </HBox>

              <HBox height="6%" width="100%">
                <SwtLabel text="Source" width="150">
                </SwtLabel>
                <SwtComboBox id="sourceCombo" #sourceCombo (change)="changeLocalCombo($event)" dataLabel="source"
                  width="150">
                </SwtComboBox>
                <SwtLabel paddingLeft="20" id="selectedSource" #selectedSource>
                  </SwtLabel>
              </HBox>

              <HBox height="6%" width="100%">
                <SwtLabel text="Category" width="150">
                </SwtLabel>
                <SwtComboBox id="categoryCombo" #categoryCombo (change)="changeLocalCombo($event)"
                  dataLabel="categoryList" width="150">
                </SwtComboBox>
                <SwtLabel paddingLeft="20" id="selectedCategory" #selectedCategory>
                </SwtLabel>
              </HBox>
              <!-- <HBox width="100%">
                <SwtLabel text="Action" width="150">
                </SwtLabel>
                <SwtComboBox id="actionCombo" #actionCombo dataLabel="actionList" width="150">
                </SwtComboBox>
              </HBox> -->



              <!-- Parties -->

              <HBox height="6%" width="100%">
                <SwtLabel id="senderBicLabel" #senderBicLabel text="Sender" width="150">
                </SwtLabel>
                <SwtTextInput restrict="0-9a-zA-Z" id="senderBicCombo" #senderBicCombo  width="150">
                </SwtTextInput>
                <SwtButton id="senderBicButton" #senderBicButton width="15"
                  (click)="partySelect(senderBicCombo,selectedSenderBic);"
                  (keyDown)="keyDownEventHandler($event)"></SwtButton>
                <SwtLabel paddingLeft="20" id="selectedSenderBic" #selectedSenderBic>
                </SwtLabel>
              </HBox>

              <HBox height="6%" width="100%">
                <SwtLabel id="receiverBicLabel" #receiverBicLabel text="Receiver" width="150">
                </SwtLabel>
                <SwtTextInput restrict="0-9a-zA-Z" id="receiverBicCombo" #receiverBicCombo
                   width="150">
                </SwtTextInput>
                <SwtButton id="receiverBicButton" #receiverBicButton width="15"
                  (click)="partySelect(receiverBicCombo,selectedreceiverBic);"
                  (keyDown)="keyDownEventHandler($event)"></SwtButton>
                <SwtLabel paddingLeft="20" id="selectedreceiverBic" #selectedreceiverBic>
                </SwtLabel>
              </HBox>

              <HBox height="6%" width="100%">
                <SwtLabel id="orderingInstBicLabel" #orderingInstBicLabel text="Ordering Inst" width="150">
                </SwtLabel>
                <SwtTextInput restrict="0-9a-zA-Z" id="orderingInstBicCombo" #orderingInstBicCombo
                   width="150">
                </SwtTextInput>
                <SwtButton id="orderingInstBicButton" #orderingInstBicButton width="15"
                  (click)="partySelect(orderingInstBicCombo,selectedorderingInstBic);"
                  (keyDown)="keyDownEventHandler($event)"></SwtButton>
                <SwtLabel paddingLeft="20" id="selectedorderingInstBic" #selectedorderingInstBic>
                </SwtLabel>
              </HBox>

              <HBox height="6%" width="100%">
                <SwtLabel id="beneficiaryInstBicLabel" #beneficiaryInstBicLabel text="Beneficiary Inst" width="150">
                </SwtLabel>
                <SwtTextInput restrict="0-9a-zA-Z" id="beneficiaryInstBicCombo" #beneficiaryInstBicCombo
                  width="150">
                </SwtTextInput>
                <SwtButton id="beneficiaryInstBicButton" #beneficiaryInstBicButton width="15"
                  (click)="partySelect(beneficiaryInstBicCombo,selectedbeneficiaryInstBic);"
                  (keyDown)="keyDownEventHandler($event)"></SwtButton>
                <SwtLabel paddingLeft="20" id="selectedbeneficiaryInstBic" #selectedbeneficiaryInstBic>
                </SwtLabel>
              </HBox>


              <!-- account-->
              <HBox height="6%" width="100%">
                <SwtLabel #acagLabel id="acagLabel" text="Account Group" width="150">
                </SwtLabel>
                <SwtComboBox id="acctGrpCombo" #acctGrpCombo (change)="changeCombo('acctGrpCombo')"
                  toolTip="Select account group" dataLabel="AcctGrpList" width="250">
                </SwtComboBox>
                <SwtLabel paddingLeft="20" id="selectedAcctGrp" #selectedAcctGrp>
                </SwtLabel>
              </HBox>
              <HBox height="6%" width="100%">
                <SwtLabel id="accountLabel" #accountLabel text="Account" width="150">
                </SwtLabel>
                <SwtComboBox id="accountCombo" #accountCombo dataLabel="AcctList" (change)="changeLocalCombo($event)"
                  width="250">
                </SwtComboBox>
                <SwtLabel paddingLeft="20" id="selectedAccount" #selectedAccount>
                </SwtLabel>
              </HBox>
              <!-- <HBox width="100%">
                <HBox>
                  <SwtLabel #statusLabel id="statusLabel" text="Status" width="150">
                  </SwtLabel>
                  <SwtComboBox id="statusCombo" #statusCombo (change)="changeCombo($event)" toolTip="Select status"
                    dataLabel="statusList" width="200">
                  </SwtComboBox>
                </HBox>
                <HBox #blokedHbox visible="false" paddingLeft="20">
                  <SwtLabel #blokedLabel id="blokedLabel" text="Blocked" width="150">
                  </SwtLabel>
                  <SwtComboBox id="blokedCombo" #blokedCombo (change)="changeCombo($event)" dataLabel="blockedList"
                    toolTip="Select bloked" width="200">
                  </SwtComboBox>
                </HBox>
              </HBox> -->

              <HBox height="6%" width="100%">
                <SwtLabel text="Input Date" width="150"></SwtLabel>
                <SwtDateField #inputDatefrmDateChooser id="inputDatefrmDateChooser" textAlign="right" width="115"
                  editable="true" toolTip="from"></SwtDateField>
                <spacer width="28"></spacer>
                <SwtLabel text="Time From" width="90"></SwtLabel>
                <SwtTextInput id="inputDateTimeFromTextInput" pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$" #inputDateTimeFromTextInput width="50"  maxChars="5" toolTip="From Time"
                  textAlign="center"  (focusOut)="validateTime(inputDateTimeFromTextInput)">
                </SwtTextInput>
                <spacer width="20"></spacer>
                <SwtLabel text="To" width="50"></SwtLabel>
                <SwtTextInput id="inputDateTimeToTextInput" pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$" #inputDateTimeToTextInput width="50" maxChars="5" toolTip="To Time"
                textAlign="center"  (focusOut)="validateTime(inputDateTimeToTextInput)">
                </SwtTextInput>
              </HBox>



              <VBox height="18%" width="100%" verticalGap="0">
                <HBox width="100%">
                  <SwtLabel text="Reference" width="150"></SwtLabel>
                  <spacer width="200"></spacer>
                  <SwtLabel text="Like" width="60"></SwtLabel>
                  <SwtLabel text="Source" width="60"></SwtLabel>
                  <SwtLabel text="Front" width="60"></SwtLabel>
                  <SwtLabel text="Back" width="60"></SwtLabel>
                  <SwtLabel text="Payment" width="70"></SwtLabel>
                  <SwtLabel text="Related" width="60"></SwtLabel>
                </HBox>
                <HBox width="100%" paddingLeft="80">
                  <SwtLabel text="Include" width="70"></SwtLabel>
                  <SwtTextInput id="includeTextInput" #includeTextInput width="150">
                  </SwtTextInput>
                  <spacer width="50"></spacer>
                  <SwtCheckBox #inlcudeLike id="inlcudeLike" width="75" styleName="checkbox"></SwtCheckBox>
                  <SwtCheckBox #inlcudeSource id="inlcudeSource" width="60" selected="true" styleName="checkbox">
                  </SwtCheckBox>
                  <SwtCheckBox #inlcudeFront id="inlcudeFront" width="65" selected="true" styleName="checkbox">
                  </SwtCheckBox>
                  <SwtCheckBox #inlcudeBack id="inlcudeBack" width="70" selected="true" styleName="checkbox">
                  </SwtCheckBox>
                  <SwtCheckBox #inlcudePayment id="inlcudePayment" width="70" selected="true" styleName="checkbox">
                  </SwtCheckBox>
                  <SwtCheckBox #inlcudeRelated id="inlcudeRelated" width="65" selected="true" styleName="checkbox">
                  </SwtCheckBox>


                </HBox>
                <HBox width="100%" paddingLeft="80">
                  <SwtLabel text="Exclude" width="70"></SwtLabel>
                  <SwtTextInput id="excludeTextInput" #excludeTextInput width="150">
                  </SwtTextInput>
                  <spacer width="50"></spacer>
                  <SwtCheckBox #excludeLike id="excludeLike" width="75" styleName="checkbox"></SwtCheckBox>
                  <SwtCheckBox #excludeSource id="excludeSource" width="60" selected="true" styleName="checkbox">
                  </SwtCheckBox>
                  <SwtCheckBox #excludeFront id="excludeFront" width="65" selected="true" styleName="checkbox">
                  </SwtCheckBox>
                  <SwtCheckBox #excludeBack id="excludeBack" width="70" selected="true" styleName="checkbox">
                  </SwtCheckBox>
                  <SwtCheckBox #excludePayment id="excludePayment" width="70" selected="true" styleName="checkbox">
                  </SwtCheckBox>
                  <SwtCheckBox #excludeRelated id="excludeRelated" width="65" selected="true" styleName="checkbox">
                  </SwtCheckBox>

                </HBox>
              </VBox>









            </VBox>
          </SwtCanvas>
        </VBox>

      </HBox>
    </SwtCanvas>
    <SwtCanvas id="canvasContainer" width="100%">
      <HBox width="100%">
        <HBox width="100%" paddingLeft="5">

          <SwtButton #searchButton (click)="doSearch()" label="Search" (keyDown)="keyDownEventHandler($event)"
            id="searchButton" width="70"></SwtButton>

          <SwtButton buttonMode="true" label="Close" id="closeButton" width="70" #closeButton (click)="closeCurrentTab($event);"
            (keyDown)="keyDownEventHandler($event)"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10" top="3">
          <SwtHelpButton id="helpIcon" [buttonMode]="true" enabled="true" helpFile="groups-of-rules" (click)="doHelp()">
          </SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
