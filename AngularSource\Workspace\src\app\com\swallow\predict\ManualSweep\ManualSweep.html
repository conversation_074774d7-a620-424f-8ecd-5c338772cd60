<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" minWidth="1100">
      <Grid width="100%" height="100%" paddingLeft="5" paddingRight="5">
          <GridRow width="100%" height="25">
            <GridItem>
            <SwtLabel id="entityLabel" #entityLabel width="100">
            </SwtLabel>
          </GridItem>
          <GridItem  width="140" >
            <SwtComboBox id="entityCombo" #entityCombo dataLabel="entityList" width="135" (change)="onEntityChange()">
            </SwtComboBox>
          </GridItem>
          <GridItem>
            <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal" paddingLeft="10">
            </SwtLabel>
          </GridItem> 
          
          <GridItem  width="140" ></GridItem>

          <GridItem>
            <SwtLabel id="secondEntityLabel" #secondEntityLabel width="100">
            </SwtLabel>
          </GridItem>
          <GridItem>
            <SwtComboBox id="secondEntityCombo" #secondEntityCombo dataLabel="secondEntityList" width="135" (change)="onCurrencyChange()">
            </SwtComboBox>
          </GridItem>
          <GridItem>
            <SwtLabel id="selectedSecondEntity" #selectedSecondEntity fontWeight="normal" paddingLeft="10">
            </SwtLabel>
          </GridItem>

          <GridItem  width="140" ></GridItem>

          <GridItem>
            <SwtLabel width=" 172" #hideAccountsAfterCutoffLabel></SwtLabel>
           </GridItem>
          <GridItem paddingLeft="10">
            <SwtCheckBox id="hideAccountsAfterCutoffCheck" #hideAccountsAfterCutoffCheck  selected="true"
            (change)="onCurrencyChange()">
            </SwtCheckBox>
          </GridItem>


          </GridRow>
            <GridRow width="100%" height="25">
              <GridItem>
              <SwtLabel id="currencyLabel" #currencyLabel width="100">
              </SwtLabel>
            </GridItem>
              <GridItem  width="140" >
              <SwtComboBox id="currencyCombo" #currencyCombo dataLabel="currencyList" width="70"
              (change)="onCurrencyChange()"></SwtComboBox>
            </GridItem>
          <GridItem>
              <SwtLabel id="selectedCurrency" #selectedCurrency fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>

            </GridRow>
            <GridRow width="100%" height="25">
            <GridItem>
              <SwtLabel id="acctTypeLabel" #acctTypeLabel width="100">
              </SwtLabel>
            </GridItem>
            <GridItem  width="140" >
              <SwtComboBox id="acctTypeCombo" #acctTypeCombo dataLabel="acctTypeList" width="50" 
              (change)="onCurrencyChange()"></SwtComboBox>
            </GridItem>
          <GridItem>
              <SwtLabel id="selectedAcctType" #selectedAcctType fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
            </GridRow>
      </Grid>
    </SwtCanvas>

    <SwtTabNavigator #tabs id="tabs" minWidth="1100" (onChange)="tabIndexchangeHandler()" borderBottom="false"   width="100%" height="3%">

    </SwtTabNavigator>

    <SwtCanvas id="dataGridContainer" #dataGridContainer paddingBottom="5" styleName="canvasWithGreyBorder"
      marginTop="10" border="false" width="100%" height="100%" minHeight="100" minWidth="1100"></SwtCanvas>

    <Grid width="100%" height="30" paddingLeft="5" paddingRight="5">
        <GridRow width="100%" height="25" paddingTop="5">
          <GridItem>
          <SwtLabel id="selectedAcctsLbl" #selectedAcctsLbl width="120">
          </SwtLabel>
          </GridItem>
          <GridItem>
            <SwtLabel id="selectedAcctsVal" #selectedAcctsVal>
            </SwtLabel>
          </GridItem>
        </GridRow>
    </Grid> 

    <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5" minWidth="1100">
      <HBox width="100%">
        <HBox paddingLeft="5" width="50%">
          <SwtButton #refreshButton width="70" id="refreshButton" (click)="updateData('false')"></SwtButton>
          <SwtButton #sweepButton width="70" id="sweepButton"  (click)="openSweepDetailWindow()" enabled="false" >
          </SwtButton>
          <SwtButton #clearButton width="70" id="clearButton" (click)="clearSelectedAccounts()" enabled="false" ></SwtButton>
          <SwtButton #closeButton width="70" (click)="closeHandler()" id="closeButton"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" width="50%" >
          <!--<SwtLabel visible="false" color="red" #dataBuildingText></SwtLabel>
          <SwtLabel visible="false" color="red" #lostConnectionText></SwtLabel>-->
          <SwtLabel #lastRef id="lastRef" fontWeight="normal"></SwtLabel>
          <SwtLabel id="lastRefTime" #lastRefTime fontWeight="normal"></SwtLabel>
          <DataExport #dataExport id="dataExport"></DataExport>
          <SwtHelpButton id="helpIcon" [buttonMode]="true" enabled="true" helpFile="spread-profile" (click)="doHelp()">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>