<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas width="100%" height="120">
      <Grid width="100%" height="100%">
        <GridRow>
          <GridItem width="50%">
            <GridItem width="120">
              <SwtLabel id="entityLabel" #entityLabel textDictionaryId="ilmthroughput.entity">
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="entityCombo" width="200" (open)="openedCombo($event)" (close)="closedCombo($event)"
                #entityCombo dataLabel="entityList" (change)="entityChangeCombo($event)">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
              <SwtLabel width='480' #valueDateLabel id="valueDateLabel" textDictionaryId="ilmthroughputbreakdown.currentFilter">
              </SwtLabel>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="50%">
            <GridItem width="120">
              <SwtLabel id="ccyLabel" #ccyLabel textDictionaryId="ilmthroughput.currency">
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox  id="ccyCombo" #ccyCombo width="200" (change)="changeCombo($event)"
                (open)="openedCombo($event)" (close)="closedCombo($event)" dataLabel="currencyList">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel id="selectedCcy" #selectedCcy fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
              <GridItem>
                <SwtCheckBox width='160' id="foreOutlflowsCheckbox"  #foreOutlflowsCheckbox  (change)="doRefreshPage(true, 'isActualOrForecast')"></SwtCheckBox>
                <SwtCheckBox width='160' id="actOutlflowsCheckbox" #actOutlflowsCheckbox (change)="doRefreshPage(true, 'isActualOrForecast')"></SwtCheckBox>
                <SwtCheckBox width='160' id="ccyThresholdCheckbox" selected="true" #ccyThresholdCheckbox  (change)="doRefreshPage(true)"></SwtCheckBox>
              </GridItem>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow id="selectGroup" #selectGroup>
          <GridItem width="50%">
            <GridItem width="120">
              <SwtLabel #groupcomboLabel id="groupcomboLabel" textDictionaryId="ilmthroughput.group">
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox dataLabel="AcctGrpList" id="groupCombo" #groupCombo width="200"
                (change)="changeCombo($event)">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel id="selectedGroup" #selectedGroup fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
              <GridItem>
                <SwtCheckBox width='160' id="foreIntlflowsCheckbox" #foreIntlflowsCheckbox  (change)="doRefreshPage(true, 'isActualOrForecast')"></SwtCheckBox>
                <SwtCheckBox width='160' id="actInlflowsCheckbox" #actInlflowsCheckbox (change)="doRefreshPage(true, 'isActualOrForecast')"></SwtCheckBox>
                <SwtCheckBox width='160' id="unsettledOutflows" #unsettledOutflows  (change)="doRefreshPage(true, 'isUnsettled')"></SwtCheckBox>
              </GridItem>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
              <SwtCommonGridPagination  #numstepper></SwtCommonGridPagination>
          </HBox>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" paddingRight="30" horizontalAlign="right">
              <SwtComboBox  dataLabel="scenarioList" id="scenarioCombo" #scenarioCombo width="350"
                (change)="changeCombo($event)">
              </SwtComboBox>
            </HBox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <VBox width="100%" height="100%" verticalGap="1">
      <SwtCanvas styleName="canvasWithGreyBorder" id="displaycontainer" border="false" width="100%" height="100%"
        #displaycontainer>
      </SwtCanvas>
    </VBox>
    <SwtCanvas width="100%" marginBottom="0" height="40">
      <HBox width="100%" top="1">
        <HBox paddingLeft="5" width="100%">
          <SwtButton  id="refreshButton" #refreshButton enabled="false"     (click)="doRefreshPage(true)">
          </SwtButton>
          <SwtButton  id="noteButton" #noteButton enabled="false" (click)="openNotes()">
          </SwtButton>
          <SwtButton  id="movementButton" #movementButton enabled="false" (click)="openMovement()">
          </SwtButton>
          <SwtButton  id="messageButton" #messageButton enabled="false" (click)="openMessages()">
          </SwtButton>
          <SwtButton  id="optionButton" #optionButton (click)="fontSettingHandler()">
          </SwtButton>
          <SwtButton  id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
        </HBox>
        <HBox width="100%" horizontalAlign="right" paddingRight="10">
          <SwtLabel visible="false" text="CONNECTION ERROR" color="red" #lostConnectionText id="lostConnectionText"
          right="300" height="16">
        </SwtLabel>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">



          <SwtLabel textDictionaryId="screen.lastRefresh" #lastRefLabel id="lastRefLabel" right="220" height="16" fontWeight="normal"> </SwtLabel>
          <SwtLabel id="lastRefTime" #lastRefTime right="180" height="16" styleName="labelLeftRefTime"
            fontWeight="normal">
          </SwtLabel>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">
          <div>
            <DataExportMultiPage enabled="false" #dataExport id="dataExport"></DataExportMultiPage>
          </div>
          <SwtHelpButton (click)="doHelp()">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>

      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
