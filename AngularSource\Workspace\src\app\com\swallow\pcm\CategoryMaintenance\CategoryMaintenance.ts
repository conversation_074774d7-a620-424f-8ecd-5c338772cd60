import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService,
  ExternalInterface,
  focusManager,
  HashMap,
  HTTPComms,
  JSONReader,
  Keyboard,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCommonGrid,
  SwtLoadingImage,
  SwtModule,
  SwtPanel,
  SwtToolBoxModule,
  SwtUtil,
  TitleWindow
} from 'swt-tool-box';
declare var instanceElement: any;
import { Routes, RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/compiler/src/core';


@Component({
  selector: 'app-pccategory-maintenance',
  templateUrl: './CategoryMaintenance.html',
  styleUrls: ['./CategoryMaintenance.css']
})
export class CategoryMaintenance  extends  SwtModule {

  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /********SwtButton*********************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('settingButton') settingButton: SwtButton;
  @ViewChild('csv') csv: SwtButton;
  @ViewChild('excel') excel: SwtButton;
  @ViewChild('pdf') pdf: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;


  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public  requestParams = [];

  private categoryGrid: SwtCommonGrid;
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';

  /* - START -- Screen Name and Version Number ---- */
  private moduleName = 'Category Maintenance';
  private versionNumber = '1.00.00';
  private releaseDate = '20 February 2019';
  /* - END -- Screen Name and Version Number ---- */

  // to open a pop up
  private child: SwtPanel;
  private swtAlert: SwtAlert;
  private  menuAccess = 2;

  private programId = '';
  private componentId = false;
  private errorLocation = 0;
  private mapMultiplierList: HashMap = new HashMap();

  public  helpURL: string = null;
  public  title: string = null;
  public moduleId = 'PCM';
  public categoryId= '';
  public screenName;
  public listOrder;
  public listRuleAssignPriority;
  public pcmInputFlag;
  public maxOrder;
  private fourEyesRequired = false;
  private win: TitleWindow ;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }

  ngOnInit(): void {
    instanceElement = this;
    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.deleteButton.label =SwtUtil.getPredictMessage('button.delete', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    // Assining properties for controls
    this.addButton.setFocus();
  }

  onLoad() {
    try {
      this.categoryGrid = this.canvasGrid.addChild(SwtCommonGrid) as SwtCommonGrid;
      this.categoryGrid.uniqueColumn = "categoryId";
      this.categoryGrid.onFilterChanged = this.disableButtons.bind(this);
      this.categoryGrid.onSortChanged =  this.disableButtons.bind(this);
      this.actionMethod = 'method=display';
      this.actionPath = 'categoryPCM.do?';
      this.title = "Payment Category Rules";
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);

      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.categoryGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };

    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'CategoryMaintenance', 'onLoad');
    }

  }

  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  public inputDataResult(data): void {
    let jsonList = null;
    let header: string;
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {
            this.helpURL = this.jsonReader.getSingletons().helpurl;
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {
              this.menuAccess = Number(this.jsonReader.getScreenAttributes()["menuaccess"]);
              this.fourEyesRequired = this.jsonReader.getScreenAttributes()["fourEyesRequired"];
              this.pcmInputFlag= this.jsonReader.getSingletons().pcmInputFlag
              this.enableAddButton(this.menuAccess==0);
              this.disableOrEnableButtons(false);
              // this.categoryGrid.CustomGrid(data.category.grid.metadata);
              jsonList = this.jsonReader.getColumnData();
              for (let i = 0; i < jsonList.column.length; i++) {
                header = SwtUtil.getAMLMessages(jsonList.column[i].heading);
                jsonList.column[i].heading = header;
              }
              this.categoryGrid.enableColumn("inclInAvailableLiq", false);
              this.categoryGrid.enableColumn("inclTargetPercent", false);
              this.categoryGrid.enableColumn("useLiqCheck", false);
              this.categoryGrid.enableColumn("isActive", false);
              const obj = {columns: jsonList};
              if (this.categoryGrid === null || this.categoryGrid === undefined) {
                this.categoryGrid.screenID = this.programId;
                this.categoryGrid.componentID = this.jsonReader.getSingletons().screenid;
                this.categoryGrid.CustomGrid(obj);
              }
              this.componentId = this.lastRecievedJSON.screenid;
              this.categoryGrid.doubleClickEnabled = true;
              this.categoryGrid.CustomGrid(obj);
              if (this.jsonReader.getGridData().size > 0) {
                this.maxOrder = Number(this.jsonReader.getSingletons().maxOrdinal);

                this.listOrder = this.jsonReader.getSingletons().ordinaList;
                this.listOrder= this.listOrder.replace("[","").replace("]","");
                this.listOrder = this.listOrder.split(',').map(Number);

                this.listRuleAssignPriority = this.jsonReader.getSingletons().priorityList;
                this.listRuleAssignPriority= this.listRuleAssignPriority.replace("[","").replace("]","");
                this.listRuleAssignPriority = this.listRuleAssignPriority.split(',').map(Number);

                this.categoryGrid.dataProvider = null;
                this.categoryGrid.gridData = this.jsonReader.getGridData();
                this.categoryGrid.setRowSize = this.jsonReader.getRowSize();
                this.categoryGrid.doubleClickEnabled = true;
                this.categoryGrid.refreshFilters();
              } else {
                this.categoryGrid.dataProvider = null;
                this.categoryGrid.selectedIndex = -1;

              }
            }
            // Condition to check data is not empty to enable or disable export icons
            if (this.jsonReader.getRowSize() < 1) {
              this.enableReportButton(false);
            } else {
              this.enableReportButton(true);
            }
            this.prevRecievedJSON = this.lastRecievedJSON;
          } else {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage());
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error inputDataResult', e);
    }
  }


  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    try {
      this.loadingImage.setVisible(true);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'startOfComms', this.errorLocation);
    }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    try {
      this.loadingImage.setVisible(false);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'endOfComms', this.errorLocation);
    }
  }

  /**
   * inputDataFault
   *
   * @param event:  FaultEvent
   *
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    try {
      let message = SwtUtil.getPredictMessage('label.genericException', null);
      this.swtAlert.error(message);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'inputDataFault', this.errorLocation);
    }
  }


  /**
   * logicUpdateResult
   *
   * @param event: ResultEvent
   *
   * Method to get result of the group rules
   */
  logicUpdateResult(event): void {
    try {
      const JsonResponse = event;
      const JsonResult: JSONReader = new JSONReader();
      JsonResult.setInputJSON(JsonResponse);
      if (JsonResult.getRequestReplyStatus()) {
        this.updateData();
        this.categoryGrid.selectedIndex= -1;
        this.disableOrEnableButtons(false);
        this.addButton.setFocus();
      } else {
        this.swtAlert.error(JsonResult.getRequestReplyMessage());
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'logicUpdateResult', this.errorLocation);
    }
  }

  /**
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  public updateData(): void {
    try {
      this.requestParams =[];
      // Define the action to send the request
      this.actionPath = 'categoryPCM.do?';
      // Define method the request to access
      this.actionMethod = 'method=display';
      this.inputData.url =  this.baseURL +  this.actionPath +  this.actionMethod;
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.inputData.send( this.requestParams);
      this.categoryGrid.refresh();
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'CategoryMaintenance', 'updateData',  this.errorLocation);
    }
  }

  public refreshGrid() {
    this.categoryGrid.selectedIndex = -1;
    this.categoryGrid.selectable= true;
    this.disableOrEnableButtons(false);
    this.enableAddButton(this.menuAccess==0);
  }

  /**
   * doAddCategory
   *
   * @param event: Event
   *
   * Method to open Add category Maintenance details screen
   */
  doAddCategory(event): void {
    try {
      this.screenName= "add";
      this.categoryGrid.selectedIndex = -1;
      this.disableOrEnableButtons(false);
      // let newWindow = window.open("/categoryAdd", 'category Rule maintenance Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }
      ExternalInterface.call('openChildWindow', 'categoryAdd');

    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'doAddCategoryMaintenance', this.errorLocation);
    }
  }

  /**
   * doChangeCategory
   *
   * @param event: Event
   *
   * Method to open Change group rules details screen
   */
  doChangeCategory(event): void {
    try {
      this.screenName= "change";
      this.categoryId= this.categoryGrid.selectedItem.categoryId.content;
      // let newWindow = window.open("/categoryAdd", 'category Rule maintenance Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }
      ExternalInterface.call('openChildWindow', 'categoryAdd');

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'doChangeCategory', this.errorLocation);
    }
  }

  /**
   * doViewCategory
   *
   * @param event: Event
   *
   * Method to open View group rules details screen
   */
  doViewCategory(event): void {
    try {
      this.screenName= "view";
      this.categoryId= this.categoryGrid.selectedItem.categoryId.content;
      // let newWindow = window.open("/categoryView", 'category Rule maintenance View', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }
      ExternalInterface.call('openChildWindow', 'categoryView');
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'doViewCategory', this.errorLocation);
    }
  }


  getParamsFromParent() {
    let params = [];
    if(this.screenName == "add") {
      params = [
        { screenName: this.screenName, categoryId: '', maxOrder: this.maxOrder,  listOrder: this.listOrder,  listRuleAssignPriority: this.listRuleAssignPriority, pcmInputFlag: this.pcmInputFlag}];
    } else {
      params = [
        { screenName: this.screenName, categoryId:  this.categoryId, maxOrder: 0,  listOrder: this.listOrder,  listRuleAssignPriority: this.listRuleAssignPriority, pcmInputFlag: this.pcmInputFlag},
      ];
    }
    return params;
  }

  /**
   * doDeleteCategory
   * @param event: Event
   * Method to pop up delete confirmation
   */
  doDeleteCategory(event): void {
    try {
      Alert.yesLabel = "Yes";
      Alert.noLabel = 'No';
      const message = "Do you wish to delete this row?";
      this.swtAlert.confirm(message, 'Alert', Alert.OK | Alert.CANCEL, null, this.removeCategoryHandler.bind(this));
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'doDeleteCategory', this.errorLocation);
    }
  }


  removeCategoryHandler(event) {
    if (event.detail === Alert.OK) {
      // if (this.fourEyesRequired == true && this.categoryGrid.selectedItem.isActive.content === "Y" ) {
      //   this.win = SwtPopUpManager.createPopUp(this, FourEyesProcess, {
      //     title: 'Four Eyes Process',
      //   });
      //
      //   this.win.enableResize = false;
      //   this.win.width = '510';
      //   this.win.height = '215';
      //   this.win.showControls = true;
      //   this.win.isModal = true;
      //   this.win.onClose.subscribe((res) => {
      //     if (this.win.getChild().result) {
      //       if (this.win.getChild().result.login == "SUCCESS") {
      //        this.deleteCategory();
      //       }
      //     }
      //
      //   });
      //   this.win.display();
      // } else {
        this.deleteCategory();
     // }
    }
  }


  /**
   * deleteCategory
   *
   * @param event:
   *
   * Method to remove selected Category
   */
  deleteCategory(): void {
    try {
        this.categoryId= this.categoryGrid.selectedItem.categoryId.content;
        this.requestParams =[];
        this.actionMethod = 'method=delete';
        this.actionPath = 'categoryPCM.do?';
        this.requestParams['categoryId']=this.categoryId;
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);
        this.categoryGrid.selectedIndex = -1;
        this.disableOrEnableButtons(false);

    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'deleteCategory', this.errorLocation);
    }
  }

  /**
   * printPage
   *
   * param event
   *
   * Method to get call the action to get reports
   */
  printPage(): void {
    try {
      // set the action path
      this.actionPath="categoryPCM.do?";
      this.actionMethod="type=" + "pdf";
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod=this.actionMethod + "&currentModuleId=" + this.moduleId;
      this.actionMethod=this.actionMethod + "&print=" + "PAGE";
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", this.errorLocation);
    }
  }

  /**
   * popupClosedEventHandler
   *
   * @param event :Event
   *
   * Method called when pop up is closed to Enable Buttons after pop up closed
   *
   */
  public popupClosedEventHandler(event): void {
    try {
      this.categoryGrid.doubleClickEnabled = true;
      // data grid is selectable and a row is selected enable buttons
      if (this.categoryGrid.selectedIndices.length === 1 && this.categoryGrid.selectable) {
        // Condition to check menu access is not 0 to disable buttons
        this.disableOrEnableButtons(true);
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'popupClosedEventHandler', this.errorLocation);
    }
  }




  /**
   * cellClickEventHandler
   *
   * @param event: Event
   *
   * This method is used to manumberain the button status when a row is clicked
   */
  cellClickEventHandler(event: Event): void {
    try {
      if (this.categoryGrid.selectedIndex >= 0 && this.categoryGrid.selectable) {
        this.disableOrEnableButtons(true);
      } else {
        this.disableOrEnableButtons(false);
      }
      event.stopPropagation();
      this.addButton.setFocus();
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'cellClickEventHandler', this.errorLocation);
    }
  }



  /**
   * report
   *
   * @param type: string
   *
   * This is a report icon action handler method
   */
  report(type: string): void {
    let selectedFilter: string = null;
    let selectedSort = '';
    let moduleId: string = null;
    try {
      moduleId = this.moduleId;
      if (this.categoryGrid.filteredGridColumns !=='') {
        selectedFilter = this.categoryGrid.getFilteredGridColumns();
      } else {
        selectedFilter = '';
      }
      selectedSort = this.categoryGrid.getSortedGridColumn();
      // set the action path
      this.actionPath = 'categoryPCM.do?';
      this.actionMethod = 'type=' +type;
      this.actionMethod = this.actionMethod + '&programId=' + this.programId;
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod = this.actionMethod + '&selectedFilter=' + selectedFilter;
      this.actionMethod = this.actionMethod + '&selectedSort=' + selectedSort;
      this.actionMethod = this.actionMethod + '&currentModuleId=' + moduleId;
      this.actionMethod = this.actionMethod + '&moduleId=' + this.moduleId;
      this.actionMethod = this.actionMethod + '&print=' + 'ALL';
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, moduleId, 'CategoryMaintenance', 'report', this.errorLocation);
    }
  }


  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      // SwtHelpWindow.open(this.baseURL + "help.do?method=print&screenName=Category+Maintenance");
      ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'CategoryMaintenance', 'doHelp',   this.errorLocation);
    }
  }


  /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(Button)
   */
  keyDownEventHandler(event): void {
    try {
      const eventString = Object(focusManager.getFocus()).name;
      if ((event.keyCode === Keyboard.ENTER)) {
        if (eventString === 'addButton') {
          this.doAddCategory(event);
        } else if (eventString === 'changeButton') {
          this.doChangeCategory(event);
        } else if (eventString === 'viewButton') {
          this.doViewCategory(event);
        } else if (eventString === 'closeButton') {
          this.closeCurrentTab(event);
        } else if (eventString === 'csv') {
          this.report('csv');
        } else if (eventString === 'excel') {
          this.report('xls');
        } else if (eventString === 'pdf') {
          this.report('pdf');
        } else if (eventString === 'helpIcon') {
          this.doHelp();
        } else if (eventString === 'deleteButton') {
          this.doDeleteCategory(event);
        } else if (eventString === 'printButton') {
          this.printPage();
        } else if (eventString === 'cancelButton') {
          this.reset(event);
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'CategoryMaintenance', 'keyDownEventHandler',   this.errorLocation);
    }
  }


  /**
   * reset
   * @param event: Event
   * Function called to reset data.
   */
  public reset(event): void {
    try {
      this.actionMethod = 'method=display';
      this.actionPath = 'categoryPCM.do?';
      this.requestParams = [];
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.cbResult = this.inputDataResult;
      // this.inputData.send(this.requestParams);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'reset', this.errorLocation);
    }
  }

  /**
   * closeCurrentTab
   *
   * Function called when close button is called
   *
   * @param event:Event
   */
  closeCurrentTab(event): void {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'CategoryMaintenance', ' closeCurrentTab', this.errorLocation);
    }
  }

  /**
   * dispose
   *
   * This is an event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.categoryGrid = null;
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this. menuAccess = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      this.child = null;
      this.mapMultiplierList = null;
      ExternalInterface.call("close");
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'dispose', this.errorLocation);
    }
  }

  /**
   * disableOrEnableButtons()
   *
   * Method called to disable  or enable components
   *
   * @param isRowSelected:boolean
   */

  disableOrEnableButtons(isRowSelected: boolean ): void {
    try {
      if(isRowSelected) {
        this.enableChangeButton(this.menuAccess == 0);
        this.enableViewButton(this.menuAccess < 2);
        this.enableDeleteButton(this.menuAccess == 0);
      } else {
        this.enableChangeButton(false);
        this.enableViewButton(false);
        this.enableDeleteButton(false);
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'disableOrEnableButtons', this.errorLocation);
    }
  }


  disableButtons() {
    if(this.categoryGrid.selectedIndex == -1) {
      this.disableOrEnableButtons(false);
    } else {
      this.disableOrEnableButtons(true);
    }
  }


  /**
   * enableReportButton
   *
   */
  enableReportButton(value): void {
    // this.csv.enabled = value;
    // this.csv.buttonMode = value;
    // this.excel.enabled = value;
    // this.excel.buttonMode = value;
    // this.pdf.enabled = value;
    // this.pdf.buttonMode = value;
    // this.settingButton.enabled = value;
    // this.settingButton.buttonMode = value;
    // this.enablePrintButton(value);
  }


  /**
   * enableAddButton
   *
   */
  enableAddButton(value): void {
    this.addButton.enabled = value;
    this.addButton.buttonMode = value;
  }
  /**
   * enableChangeButton
   *
   */
  enableChangeButton(value): void {
    this.changeButton.enabled = value;
    this.changeButton.buttonMode = value;
  }
  /**
   * enableViewButton
   *
   */
  enableViewButton(value: boolean): void {
    this.viewButton.enabled = value;
    this.viewButton.buttonMode = value;
  }
  /**
   * enableDeleteButton
   *
   */
  enableDeleteButton(value): void {
    this.deleteButton.enabled = value;
    this.deleteButton.buttonMode = value;
  }

  /**
   * enablePrintButton
   *
   */
  enablePrintButton(value): void {
    this.printButton.enabled = value;
    this.printButton.buttonMode = value;
  }


}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: CategoryMaintenance }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CategoryMaintenance],
  entryComponents: []
})
export class CategoryMaintenanceModule {}
