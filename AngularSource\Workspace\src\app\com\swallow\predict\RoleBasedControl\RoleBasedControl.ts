import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { SwtModule, SwtToolBoxModule, CommonService, SwtLoadingImage, SwtCanvas, SwtButton, SwtCommonGrid, SwtUtil, Swt<PERSON><PERSON>t, Logger, JSONReader, HTTPComms, ExternalInterface } from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
declare var instanceElement: any;

@Component({
  selector: 'app-role-based-control',
  templateUrl: './RoleBasedControl.html',
  styleUrls: ['./RoleBasedControl.css']
})
export class RoleBasedControl  extends SwtModule implements OnInit {
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /***********SwtCanvas***********/
  @ViewChild('roleBasedControlGridContainer') roleBasedControlGridContainer: SwtCanvas;

  /***********SwtButton***********/
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  
  private roleBasedControlGrid;
  private requestParams = [];
  private swtAlert: SwtAlert;
  private menuAccessId;
  private logger: Logger = null;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private _invalidComms: string;
  private gridChangesList= [];
  private configExistFlag=null;
  private operation = null;
  
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.roleBasedControlGrid = <SwtCommonGrid>this.roleBasedControlGridContainer.addChild(SwtCommonGrid);
    this.roleBasedControlGrid.editable = true;
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    this.okButton.toolTip = SwtUtil.getPredictMessage('tooltip.ok', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage('tooltip.cancelbutton', null);
  }


  onLoad() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      errorLocation = 10;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 60;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      errorLocation = 70;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "role.do?";
      this.actionMethod = 'method=displayRoleBasedControl';
      this.requestParams['menuAccessId'] = this.menuAccessId;
      
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 80;
      this.inputData.send(this.requestParams);

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctSweepBalGrp.ts', "onLoad", errorLocation);
    }
  }



  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        errorLocation = 10;
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        errorLocation = 20;
        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            if (!this.jsonReader.isDataBuilding()) {
              this.configExistFlag= this.jsonReader.getSingletons().configExistFlag; 
              this.operation= this.jsonReader.getSingletons().operation; 
              const obj = {columns: this.jsonReader.getColumnData()};
              this.roleBasedControlGrid.CustomGrid(obj);
              this.roleBasedControlGrid.gridData=this.jsonReader.getGridData();
              this.roleBasedControlGrid.setRowSize=this.jsonReader.getRowSize();

              if(this.operation=="view"){
              this.roleBasedControlGrid.editable=false;
              this.roleBasedControlGrid.selectable= false;
              }else{
              this.roleBasedControlGrid.editable=true;
              this.roleBasedControlGrid.selectable= true; 
              }

              this.prevRecievedJSON = this.lastRecievedJSON;
            }
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }

        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctSweepBalGrp.ts', "inputDataResult", errorLocation);
    }
  }


saveHandler(){
    if (this.operation!='view'){
    this.gridChanges();
    this.requestParams = [];
    this.actionPath = 'role.do?';
    this.actionMethod = 'method=saveFacilityAccessInSession';
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.saveDataResult(data);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.requestParams["gridChangesList"] = JSON.stringify(this.gridChangesList);
    this.requestParams["configExistFlag"] = this.configExistFlag;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
   }else{
    ExternalInterface.call("close"); 
   }
  }

  saveDataResult(event):void
  {
    this.jsonReader.setInputJSON(event);
    if ( this.jsonReader.getRequestReplyStatus())
    {
      ExternalInterface.call("close");
    }
    else
    {
      this.swtAlert.error(this.jsonReader.getRequestReplyMessage());
    }
  }

  gridChanges() {
    let row = {};
    for (let i = 0; i < this.roleBasedControlGrid.gridData.length; i++) {
        row = {
          'FACILITY_ID': this.roleBasedControlGrid.gridData[i].facilityId,
          'FACILITY_DESC': this.roleBasedControlGrid.gridData[i].facilityDesc, 
          'REQ_AUTH': this.roleBasedControlGrid.gridData[i].reqAuth,
          'REQ_OTHERS': this.roleBasedControlGrid.gridData[i].reqOthers
        };
       
        this.gridChangesList.push(row);
    }
  }

  cancelHandler(){
    ExternalInterface.call("close");
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: RoleBasedControl }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [RoleBasedControl],
  entryComponents: []
})
export class RoleBasedControlModule { }
