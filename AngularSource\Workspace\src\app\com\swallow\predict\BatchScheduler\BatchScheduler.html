<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas minWidth="1200" width="100%" height="40">
      <HBox width="100%" height="100%" paddingLeft="5" paddingRight="5">
        <VBox width="100%" height="100%" verticalGap="0">
          <HBox width="100%" height="25">
            <HBox width="100%" height="100%">
              <SwtLabel id="scheduledJobTypeLabel" 
                        #scheduledJobTypeLabel
                        width="180">
              </SwtLabel>
              <SwtComboBox id="jobTypeList" 
                           #jobTypeList
                           dataLabel="jobTypeList"
                           width="140"
                           (change)="updateData($event)"
                           [visible]="showJobTypeDropdown">
              </SwtComboBox>
              <SwtLabel id="selectedJobType" 
                        #selectedJobType 
                        [visible]="!showJobTypeDropdown"
                        fontWeight="normal" 
                        paddingLeft="10">
              </SwtLabel>
            </HBox>
          </HBox>
        </VBox>
      </HBox>
    </SwtCanvas>

    <SwtCanvas id="dataGridContainer" #dataGridContainer minWidth="1200" width="100%" height="100%"></SwtCanvas>
    
    <SwtCanvas id="canvasButtons" minWidth="1200" height="40" marginBottom="0" width="100%">
      <HBox width="100%" height="100%">
        <HBox paddingLeft="5" width="70%">
          <SwtButton #refreshButton
                     width="70"
                     id="refreshButton"
                     (click)="updateData($event)"
                     [buttonMode]="true">
          </SwtButton>
          <SwtButton #executeButton
                     width="70"
                     id="executeButton"
                     enabled="false"
                     (click)="submitExecute($event)"
                     [buttonMode]="true">
          </SwtButton>
          <SwtButton #enableButton
                     width="70"
                     id="enableButton"
                     enabled="false"
                     (click)="jobEnable($event)"
                     [buttonMode]="true">
          </SwtButton>
          <SwtButton #disableButton
                     width="70"
                     id="disableButton"
                     enabled="false"
                     (click)="jobDisable($event)"
                     [buttonMode]="true">
          </SwtButton>
          <SwtButton #addButton
                     width="70"
                     id="addButton"
                     (click)="openAddJob()"
                     [buttonMode]="true">
          </SwtButton>
          <SwtButton #changeButton
                     width="70"
                     id="changeButton"
                     enabled="false"
                     (click)="changeRecord()"
                     [buttonMode]="true">
          </SwtButton>
          <SwtButton #removeButton
                     width="70"
                     id="removeButton"
                     enabled="false"
                     (click)="submitDeleteForm()"
                     [buttonMode]="true">
          </SwtButton>
          <SwtButton #entityButton
                     width="70"
                     id="entityButton"
                     [visible]="showEntityButton"
                     [includeInLayout]="showEntityButton"
                     (click)="openEntityProcess()"
                     marginRight= "8"
                     [buttonMode]="true">
          </SwtButton>
          <SwtButton #closeButton
                     width="70"
                     id="closeButton"
                     (click)="closeHandler()"
                     [buttonMode]="true">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" width="30%" paddingTop="5">
          <SwtLabel #lastRefTimeLabel fontWeight="normal"></SwtLabel>
          <SwtLabel #lastRefTime fontWeight="normal"></SwtLabel>
          
          <SwtHelpButton id="helpButton" 
                         [buttonMode]="true" 
                         enabled="true" 
                         helpFile="batch-scheduler" 
                         (click)="doHelp()">
          </SwtHelpButton>

          <SwtButton [buttonMode]="true" 
                     #printButton 
                     id="printButton" 
                     styleName="printIcon" 
                     (click)="printPage()"
                     (keyDown)="keyDownEventHandler($event)">
          </SwtButton>

          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>