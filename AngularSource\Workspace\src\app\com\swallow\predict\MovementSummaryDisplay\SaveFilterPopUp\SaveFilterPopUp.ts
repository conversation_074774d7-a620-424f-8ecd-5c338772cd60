import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {
  CommonService, StringUtils,
  SwtAlert,
  SwtComboBox,
  SwtCommonGrid,
  SwtModule,
  SwtRadioButtonGroup,
  SwtRadioItem, Alert, SwtTextInput, SwtEditableComboBox
} from "swt-tool-box";

@Component({
  selector: 'app-save-filter-pop-up',
  templateUrl: './SaveFilterPopUp.html',
  styleUrls: ['./SaveFilterPopUp.css']
})
export class SaveFilterPopUp extends SwtModule {
  @ViewChild('fixedRadio') fixedRadio: SwtRadioItem;
  @ViewChild('relativeRadio') relativeRadio: SwtRadioItem;
  @ViewChild('date') date: SwtRadioButtonGroup;
  @ViewChild('profileCombo') profileCombo: SwtEditableComboBox;
  public filterCombo: SwtComboBox;
  public saveProfileCollection = [];
  private swtAlert: SwtAlert;
  public selectedComboItem: string;

  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  onLoad() {
    //build data provider
    //receive selected label
    this.profileCombo.dataProvider = this.saveProfileCollection;
    this.profileCombo.selectedLabel = (this.selectedComboItem) ? this.selectedComboItem : "";
  }
  saveFilterClickHandler() {
       let selectedFilter:string = StringUtils.trim(this.profileCombo.selectedLabel);
       if(selectedFilter.length == 0){
         this.swtAlert.show( 'Please fill the filter name before saving');
       }
       else if(this.filterCombo.getIndexOf(selectedFilter) != -1 || this.filterCombo.getIndexOf("*"+selectedFilter) != -1 ){
         this.swtAlert.show( 'Are you sure you want to overwrite this filter?', 'Warning', Alert.OK | Alert.CANCEL,
           null,
           this.overwriteAlertListener.bind(this), //close handler
           null); //icon and default button
       }
       else {
         this.saveFilterHandler();
       }

  }
    /**
     * This function is used to listen to the alert
     *
     * @param eventObj CloseEvent
     */
    overwriteAlertListener(eventObj):void {
        // Checks for Alert OK
        if (eventObj.detail == Alert.OK){
      // Recalculate data if "OK" is clicked
      this.saveFilterHandler();
    }
  }

    saveFilterHandler():void {
        this.parentDocument.currentFilterConf = StringUtils.trim(this.profileCombo.selectedLabel);
        this.parentDocument.dateBehaviour = this.date.selectedValue.toString();
        this.parentDocument.filterScreen("saveFilter", this.parentDocument.currentFilterConf);
        this.close();
       }

}
