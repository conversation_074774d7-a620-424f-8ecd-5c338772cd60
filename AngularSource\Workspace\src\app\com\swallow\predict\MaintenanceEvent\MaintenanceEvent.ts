import { Component, OnInit, ModuleWithProviders, NgModule, OnDestroy, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, CommonService, Logger, SwtAlert, SwtLabel, SwtFieldSet, SwtTextInput, SwtUtil, SwtButton, SwtTabNavigator, Tab, SwtCanvas, SwtCommonGrid, ExternalInterface, HTTPComms, JSONReader, SwtLoadingImage, SwtComboBox, SwtDateField, SwtRadioButtonGroup, SwtRadioItem, SwtCheckBox } from "swt-tool-box";
import moment from 'moment';
declare var instanceElement: any;



@Component({
  selector: 'app-maint-event',
  templateUrl: './MaintenanceEvent.html',
  styleUrls: ['./MaintenanceEvent.css']
})
export class MaintenanceEvent extends SwtModule implements OnInit  {

    @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
    /***********SwtCanvas***********/
    @ViewChild('maintEventGridContainer') maintEventGridContainer: SwtCanvas;
    /***********SwtLabel***********/
    // @ViewChild('entity') entity: SwtLabel;
    // @ViewChild('entityDesc') entityDesc: SwtLabel;
    @ViewChild('user') user: SwtLabel;
    @ViewChild('userDesc') userDesc: SwtLabel;
    // @ViewChild('user') show: SwtLabel;
    /***********SwtComboBox***********/
    // @ViewChild('entityCombo') entityCombo: SwtComboBox;
    @ViewChild('userCombo') userCombo: SwtComboBox;
    @ViewChild('facilityCombo') facilityCombo: SwtComboBox;
    
    /***********SwtDateField***********/
    @ViewChild('fromDateField') fromDateField: SwtDateField;
    @ViewChild('toDateField') toDateField: SwtDateField;
    
    /***********SwtRadioButtonGroup***********/
    @ViewChild('showGrp') showGrp: SwtRadioButtonGroup;
    // @ViewChild('currentRadio') currentRadio: SwtRadioItem;
    @ViewChild('allRadio') allRadio: SwtRadioItem;
    @ViewChild('forDateRadio') forDateRadio: SwtRadioItem;

    @ViewChild('pendingBox') pendingBox: SwtCheckBox;
    @ViewChild('acceptedBox') acceptedBox: SwtCheckBox;
    @ViewChild('rejectedBox') rejectedBox: SwtCheckBox;


    /***********SwtButton***********/
    // @ViewChild('addButton') displayButton: SwtButton;
    @ViewChild('displayButton') displayButton: SwtButton;
    // @ViewChild('deleteButton') deleteButton: SwtButton;
    @ViewChild('closeButton') closeButton: SwtButton;
    @ViewChild('searchButton') searchButton: SwtButton;


// fromDateLabel
// fromDateField
// toDateLabel
// toDateField
// status
// // statusGrp
// // pendingRadio
// // acceptedRadio
// // rejectedRadio
// // allRadio
// user
// userCombo
// userDesc


  private swtAlert: SwtAlert;
  private menuAccessId;
  private logger: Logger = null;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  private dateFormat: string;
  private maintEventGrid: SwtCommonGrid;
  private selectedEntity: string = "";
  private selectedCurrency: string = "";
  private showValue: string = "";
  private fromMethod: string = "";
  private currencyComboList = null;
  private fromDate;
  private toDate;
  public operation: string;
  private selectedtRow;
  private currencyPattern: string;
  private reference: string = "";
  private ccyCode: string = "";
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Account Currency Period maintenance', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
    this.logger.info("method [constructor] - START/END ");
  }

  ngOnInit() {
    instanceElement = this;
    this.maintEventGrid = <SwtCommonGrid>this.maintEventGridContainer.addChild(SwtCommonGrid);
    
    // this.displayButton.label = SwtUtil.getPredictMessage('button.change', null);
    // this.displayButton.toolTip = SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.change', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);


    this.displayButton.label = SwtUtil.getPredictMessage('maintenanceevent.summary.dispalybutton.label', null);
    this.displayButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.summary.dispalybutton.tooltip', null);
    
    this.searchButton.label = SwtUtil.getPredictMessage('maintenanceevent.summary.seearchbutton.label', null);
    this.searchButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.summary.seearchbutton.tooltip', null);

    
    this.acceptedBox.toolTip=SwtUtil.getPredictMessage('maintenanceevent.summary.checkbox.accepted');
    this.pendingBox.toolTip=SwtUtil.getPredictMessage('maintenanceevent.summary.checkbox.pending');
    this.rejectedBox.toolTip=SwtUtil.getPredictMessage('maintenanceevent.summary.checkbox.rejected');

    this.allRadio.toolTip = SwtUtil.getPredictMessage('maintenanceevent.summary.dateselection.all');
    this.forDateRadio.toolTip = SwtUtil.getPredictMessage('maintenanceevent.summary.dateselection.forDateSelection');
    
    this.fromDate.toolTip = SwtUtil.getPredictMessage('maintenanceevent.summary.dateselection.from');
    this.toDate.toolTip = SwtUtil.getPredictMessage('maintenanceevent.summary.dateselection.to');
    
    this.userCombo.toolTip = SwtUtil.getPredictMessage('maintenanceevent.summary.userselection');
    
  }
  onLoad(){
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }

    this.maintEventGrid.uniqueColumn = "maintEventId";
    errorLocation = 10;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 20;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "maintenanceEvent.do?";
    this.actionMethod = 'method=displayMaintenanceEventList';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['isPendingChecked'] = "Y";
    
    // this.requestParams['fromMethod'] = "onLoad";
    // this.requestParams['show'] = this.showGrp.selectedValue;
    // this.requestParams['entityId'] = "All";
    // this.requestParams['currencyCode'] =  "All";
    // this.requestParams['forDate'] = this.forDateField.text;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    errorLocation = 30;
    this.maintEventGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    };


  } catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "onLoad", errorLocation);
  }
  }

  getParams(){
    return {
      "maintEventId":this.selectedtRow.maintEventId.content,
      "menuAccessId": this.menuAccessId,
      "maintFacilityId" : this.selectedtRow.maintFacilityIdValue.content,
      "status": this.selectedtRow.statusValue.content,
      "requestUser": this.selectedtRow.requestUser.content
    };
  }

  dataRefreshGrid(event){
    // let selectedSort = (this.maintEventGrid.getMsdSortedGridColumn()) ;
    // console.log("🚀 ~ file: MaintenanceEvent.ts:179 ~ MaintenanceEvent ~ dataRefreshGrid ~ selectedSort:", selectedSort)
    // let selectedFilter = this.maintEventGrid.getFilteredGridColumns();
    // console.log("🚀 ~ file: MaintenanceEvent.ts:181 ~ MaintenanceEvent ~ dataRefreshGrid ~ selectedFilter:", selectedFilter)
  }

  /** This method is called when selecting a row from bottom grid**/

  cellClickEventHandler(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.maintEventGrid.refresh();
    errorLocation = 10;
    if (this.maintEventGrid.selectedIndex >= 0) {
      this.displayButton.enabled = true;
      this.displayButton.buttonMode = true;
      errorLocation = 20;
      this.selectedtRow = this.maintEventGrid.selectedItem;
       console.log("🚀 ~ file: MaintenanceEvent.ts:179 ~ MaintenanceEvent ~ cellClickEventHandler ~ this.selectedtRow:", this.selectedtRow)
       //prepare reference to be used in log screen
      // this.reference = this.selectedtRow.entityId.content + '/' + this.selectedtRow.accountId.content +"/"+ moment(this.selectedtRow.startDate.content, this.dateFormat.toUpperCase() , true).format('YYYYMMDD')+'%';
      //prepare ccyCode to be used in log screen(Mantis 6135)
      // this.ccyCode= this.selectedtRow.maintEventId.content; 
    } else {
      errorLocation = 30;
      this.displayButton.enabled = false;
      this.displayButton.buttonMode = false;
    }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "cellClickEventHandler", errorLocation);
    }
  }

  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      errorLocation = 10;
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      //this.dataExport.enabled = true;
      errorLocation = 20;
      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          //reset grid selection and buttons status
          this.maintEventGrid.selectedIndex=-1;
          this.displayButton.enabled = false;
          errorLocation = 30;
          // this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
          errorLocation = 40;
          this.dateFormat = this.jsonReader.getSingletons().dateformat;
          errorLocation = 50;

          
          this.fromDateField.formatString = this.dateFormat.toLowerCase();
          this.fromDate=this.jsonReader.getSingletons().fromDate;
          errorLocation = 60;
          this.fromDateField.text = this.fromDate;

          this.toDateField.formatString = this.dateFormat.toLowerCase();
          this.toDate=this.jsonReader.getSingletons().toDate;
          errorLocation = 60;
          this.toDateField.text = this.toDate;
          
          if("Y" == this.jsonReader.getSingletons().isAllDates){
            this.allRadio.selected = true;
            this.showGrp.selectedValue = "A"; 
          }
          else {
            this.showGrp.selectedValue = "D"; 
            this.forDateRadio.selected = true;
          }


          this.pendingBox.selected = ("Y" == this.jsonReader.getSingletons().isPendingChecked);
          this.acceptedBox.selected = ("Y" == this.jsonReader.getSingletons().isAcceptedChecked);
          this.rejectedBox.selected = ("Y" == this.jsonReader.getSingletons().isRejectedChecked);


          this.userCombo.setComboData(this.jsonReader.getSelects());
          this.userCombo.selectedLabel = this.jsonReader.getSingletons().selectedUser;
          this.facilityCombo.setComboData(this.jsonReader.getSelects());
          this.facilityCombo.selectedValue = this.jsonReader.getSingletons().selectedFacility;
          // this.forDateField.formatString = this.dateFormat.toLowerCase();
          // this.displayedDate=this.jsonReader.getSingletons().displayedDate;
          // errorLocation = 60;
          // this.forDateField.text = this.displayedDate;
          // this.selectedEntity = this.jsonReader.getSingletons().selectedEntity;
          // this.selectedCurrency = this.jsonReader.getSingletons().selectedCurrency;
          // this.showValue = this.jsonReader.getSingletons().show;
          // this.fromMethod = this.jsonReader.getSingletons().fromMethod; 
          // errorLocation = 70;
          // this.entityCombo.setComboData(this.jsonReader.getSelects());
          // this.entityCombo.selectedLabel = this.selectedEntity;
          // errorLocation = 80;
          // this.ccyCombo.setComboData(this.jsonReader.getSelects());
          // this.ccyCombo.selectedLabel = this.selectedCurrency;
          // errorLocation = 90;
          // this.entityDesc.text = this.entityCombo.selectedValue;
          // this.ccyDesc.text = this.ccyCombo.selectedValue;
          errorLocation = 100;
          if (!this.jsonReader.isDataBuilding()) {
            const obj = { columns: this.lastRecievedJSON.maintenanceEvent.maintEventGrid.metadata.columns };
            errorLocation = 110;
            this.maintEventGrid.CustomGrid(obj);
            errorLocation = 120;
            var gridRows = this.lastRecievedJSON.maintenanceEvent.maintEventGrid.rows;
            if (gridRows.size > 0 && gridRows.row) {
              this.maintEventGrid.gridData = gridRows;
              errorLocation = 130;
              this.maintEventGrid.setRowSize = this.jsonReader.getRowSize();
              errorLocation = 140;
              this.maintEventGrid.refresh();
              //this.dataExport.enabled = true;
            }
            else {
              this.maintEventGrid.gridData = { size: 0, row: [] };
              //this.dataExport.enabled = false;
            }
            this.prevRecievedJSON = this.lastRecievedJSON;
          }
        }
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }

      }
    }
    } catch (error) {
      console.log(error);
      // log the error in ERROR LOG
      this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "inputDataResult", errorLocation);
    }
    }

    displayClickHandler(){

      // var newWindow = window.open("/MaintenanceEventDetails", 'Maintenance Event Details', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }


      ExternalInterface.call('buildMaintenanceDetailsURL', 'spreadProfilesAdd');



    }

  validateDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      errorLocation = 10;
      if (dateField.text) {
        errorLocation = 20;
        date = moment(dateField.text, this.dateFormat.toUpperCase(), true);
        errorLocation = 30;
        if (!date.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
            errorLocation = 40;
            this.setFocusDateField(dateField)
          });
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          errorLocation = 50;
          this.setFocusDateField(dateField)
        });
        return false;
      }
      errorLocation = 60;
      dateField.selectedDate = date.toDate();
      errorLocation = 70;
      // if (this.showGrp.selectedValue=="D"){
      //   errorLocation = 80;
      //   this.updateData();
      // }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "validateDateField", errorLocation);
    }
    this.showGrp.selectedValue = 'D';
    return true;
  }

  setFocusDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      dateField.setFocus();
      errorLocation = 10;
      dateField.text = this.jsonReader.getSingletons().displayedDate;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [setFocusDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "setFocusDateField", errorLocation);
    }
  }

  updateData(reset=false) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.requestParams = [];
    errorLocation = 10;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    
    errorLocation = 30;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "maintenanceEvent.do?";
    this.actionMethod = 'method=displayMaintenanceEventList';
    this.requestParams['isPendingChecked'] = "Y";
    this.requestParams['selectedUser'] = this.userCombo.selectedLabel;
    this.requestParams['fromDate'] = this.fromDateField.text;
    this.requestParams['toDate'] = this.toDateField.text;
    this.requestParams['isPendingChecked'] = this.pendingBox.selected?'Y':'N';
    this.requestParams['isAcceptedChecked'] = this.acceptedBox.selected?'Y':'N';;
    this.requestParams['isRejectedChecked'] = this.rejectedBox.selected?'Y':'N';;
    this.requestParams['isAllDates'] = this.showGrp.selectedValue !== "D"?'Y':'N';;
    this.requestParams['selectedFacility'] = this.facilityCombo.selectedValue;
    
    // this.requestParams['show'] = this.showGrp.selectedValue;
    // this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    // this.requestParams['currencyCode'] = this.ccyCombo.selectedLabel;
    // this.requestParams['forDate'] = this.forDateField.text;
    // if(reset){
    //   this.requestParams['show'] = 'A';
    //   this.showGrp.selectedValue = 'A';
    // }else{
    //   this.requestParams['show'] = this.showGrp.selectedValue;
    // }
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 40;
    this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [updateData] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "updateData", errorLocation);
    }
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  closeHandler(): void {
    ExternalInterface.call("close");
  }

  
  addAcctCcyHandler() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.operation = "add";
    let params=[];
    errorLocation = 10;
    ExternalInterface.call('subAcctCcyPeriodMaint', "add", "");
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [addAcctCcyHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "addAcctCcyHandler", errorLocation);
    }
  }
  changeAcctCcyHandler() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.operation = "change";
    let params=[];
    params.push ({accountId :this.selectedtRow.accountId.content,
    ccyCode :this.selectedtRow.ccyCode.content,
    endDate : this.selectedtRow.endDate.content,
    startDate : this.selectedtRow.startDate.content,
    entityId : this.selectedtRow.entityId.content,
    fillBalance : this.selectedtRow.fillBalance.content,
    fillDays : this.selectedtRow.fillDays.content,
    minimumReserve : this.selectedtRow.minimumReserve.content,
    minTargetBalance : this.selectedtRow.minTargetBalance.content,
    excludeFillDays : this.selectedtRow.excludeFillDays.content,
    targetAvgBalance : this.selectedtRow.targetAvgBalance.content,
    eodBalanceSrc : this.selectedtRow.eodBalanceSrc.content,
    tier : this.selectedtRow.tier.content});
    errorLocation = 10;
    ExternalInterface.call('subAcctCcyPeriodMaint', "change", JSON.stringify(params));
  } catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [addAcctCcyHandler] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "addAcctCcyHandler", errorLocation);
  }
  }

  public deleteCheck(): void {
    this.swtAlert.confirm(SwtUtil.getPredictMessage('confirm.delete', null),
      SwtUtil.getPredictMessage('alert.deletion.confirm', null),
      SwtAlert.YES | SwtAlert.NO,
      null,
      this.deleteAcctCcyHandler.bind(this),
      null);
  }

public updateDateWithDelay(){
  setTimeout(() => {this.updateData()}, 500);
}
  deleteAcctCcyHandler(event) {
   
    if (event.detail == SwtAlert.YES) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    errorLocation = 10;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.updateData();
    };
    errorLocation = 20;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "accountPeriod.do?";
    this.actionMethod = 'method=deleteAcctCcyPeriod';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['entityId'] = this.selectedtRow.entityId.content;
    this.requestParams['accountId'] = this.selectedtRow.accountId.content;
    this.requestParams['startDate'] = this.selectedtRow.startDate.content;
    this.requestParams['endDate'] = this.selectedtRow.endDate.content;
    errorLocation = 30;
    this.requestParams['fromMethod'] = "update";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 40;
    this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [addAcctCcyHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaint.ts', "addAcctCcyHandler", errorLocation);
    }
  }
  }

  openLogScreen(){
    ExternalInterface.call('openLogScreen', 'displayLogScreen');
  }


}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: MaintenanceEvent}
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [MaintenanceEvent],
  entryComponents: []
})
export class MaintenanceEventModule { }