import {
  CommonService,
  ContextMenuItem,
  ExternalInterface,
  HTTPComms,
  JSONReader,
  JSONViewer, ScreenVersion, StringUtils,
  SwtAlert,
  SwtButton,
  SwtComboBox,
  SwtLabel, SwtLoadingImage,
  SwtModule,
  SwtPopUpManager,
  SwtToolBoxModule,
  SwtUtil
} from 'swt-tool-box';
import {Component, ElementRef, ModuleWithProviders, NgModule, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';


@Component({
  selector: 'app-template-options',
  templateUrl: './TemplateOptions.html',
  styleUrls: ['./TemplateOptions.css']
})

export class TemplateOptions extends SwtModule  implements  OnInit, OnDestroy {

  /*********SwtButton*********/
  @ViewChild('btnCancel') btnCancel: SwtButton;
  @ViewChild('btnSave') btnSave: SwtButton;
  @ViewChild('btnAdd') btnAdd: SwtButton;
  @ViewChild('btnChange') btnChange: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********SwtComboBox*********/
  @ViewChild('cbEntity') cbEntity: SwtComboBox;
  @ViewChild('cbCurrency') cbCurrency: SwtComboBox;
  @ViewChild('cbTemplate') cbTemplate: SwtComboBox;


  /*********SwtLabel*********/
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('selectedCurrency') selectedCurrency: SwtLabel;
  @ViewChild('templateLabel') templateLabel: SwtLabel;
  @ViewChild('selectedTemplate') selectedTemplate: SwtLabel;

  /**
   * Communication Objects
   **/
  private baseURL= SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  private  requestParams=[];

  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;

  public screenVersion  = new ScreenVersion(this.commonService) ;
  public showJSONPopup: any;

  private comboChange=false;
  private menuAccessIdParent=0;
  private ccyList=null;
  private entityList=null;
  private templateList=null;
  private screenName="Template Options";
  private versionNumber="1.1.0002";
  private releaseDate = '30 May 2019';
  public optionXML;

  private  inputData=new HTTPComms(this.commonService);
  private  refreshData=new HTTPComms(this.commonService);
  private  saveData=new HTTPComms(this.commonService);
  private  invalidComms="";

  private swtAlert: SwtAlert;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }


  ngOnDestroy(): any {

  }

  ngOnInit(): void {
    // Assining properties for controls
    this.entityLabel.text = SwtUtil.getPredictMessage('label.forecastMonitor.entity', null);
    this.cbEntity.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.selectEntity', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('label.forecastMonitor.currency', null);
    this.cbCurrency.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.selectCurrency', null);
    this.templateLabel.text = SwtUtil.getPredictMessage('label.forecastTemplateOption.template', null);
    this.cbTemplate.toolTip = SwtUtil.getPredictMessage('tooltip.forecastTemplateOption.template', null);
    this.btnCancel.label = SwtUtil.getPredictMessage('button.forecastMonitor.cancel', null);
    this.btnCancel.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.cancel', null);
    this.btnSave.label = SwtUtil.getPredictMessage('button.forecastMonitor.ok', null);
    this.btnSave.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.ok', null);
    this.btnAdd.label = SwtUtil.getPredictMessage('button.forecastMonitor.add', null);
    this.btnAdd.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.add', null);
    this.btnChange.label = SwtUtil.getPredictMessage('button.forecastMonitor.change', null);
    this.btnChange.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.change', null);
  }

  /**
   *   This method has the initial configurations for the application like:<br>
   */
  onLoad(): void {

    this.requestParams=[];
    this.menuAccessIdParent = ExternalInterface.call('eval', 'menuAccessIdParent');

    if ( this.menuAccessIdParent == 1) {
      this.btnSave.enabled=false;
    }


    this.initializeMenus();

    this.inputData.cbResult = (event) => {
       this.inputDataResult(event);
    };
    this.refreshData.cbResult = (event) => {
       this.inputDataResult(event);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.refreshData.cbFault=this.inputDataFault.bind(this);
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.encodeURL=false;
    this.actionPath="forecastMonitor.do?";
    this.actionMethod="method=displayUserTemplateOptions";
    if (ExternalInterface.call('eval', 'entityId') == "undefined") {
      this.requestParams["forecastTemplateOptions.id.entityId"] = "";
    } else {
      this.requestParams["forecastTemplateOptions.id.entityId"]= ExternalInterface.call('eval', 'entityId');
    }

    this.requestParams["forecastTemplateOptions.id.currencyCode"]= ExternalInterface.call('eval', 'currencyCode');
    this.requestParams["forecastTemplateOptions.templateId"]= ExternalInterface.call('eval', 'templateId');

    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;

    this.saveData.cbResult= (event)=> {
      this.saveDataResult(event);
    };
    this.saveData.cbFault=this.inputDataFault.bind(this);
    this.saveData.encodeURL=false;
    this.inputData.send(this.requestParams);
    //this.optionXML = ExternalInterface.call('eval', 'xmlTemplate');
    if(window.opener && window.opener.instanceElement )
      this.optionXML = window.opener.instanceElement.templateData;
  }


  /**
   * This method closes the Template Options window upon saving any changes made.<br>
   * @param event:ResultEvent
   */
   saveDataResult(event): void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON=(event);
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        ExternalInterface.call("closeWindow");
      }
    }
  }
   startOfComms(): void {
    this.loadingImage.setVisible(true);
  }
   endOfComms(): void {
    this.loadingImage.setVisible(false);
  }



  /**
   * The  initializes the menus in the right click event on the Entity Monitor options screen.
   * The links are redirected to their respective pages.
   */
  private  initializeMenus(): void {
    // Creating a new instance for AboutScreen
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
    let contextMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    contextMenuItem.MenuItemSelect = this.showJSONSelect.bind(this);
    this.screenVersion.svContextMenu.customItems.push(contextMenuItem);
    this.contextMenu = this.screenVersion.svContextMenu;
  }

  /**
   * This  is used to display the XML for the monitor selected in the monitor combo
   * @param event :ContextMenuEvent
   */
  private  showJSONSelect(event): void {
    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });

    // showXML.baseURL="forecastMonitor.do?method=displayForecastMonitorDetails";
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "200";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }


  /**
   * This method is called by the HTTPComms when result event occurs.<br>
   * @param event:ResultEvent
   */
  inputDataResult(event): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON=(event);
        this.jsonReader.setInputJSON(this.lastRecievedJSON);

        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            this.cbEntity.setComboData(this.jsonReader.getSelects(), false);
            this.cbCurrency.setComboData(this.jsonReader.getSelects(), false);
            this.cbTemplate.setComboData(this.jsonReader.getSelects(), false);
            if (StringUtils.trim(this.cbCurrency.selectedItem.content) != "") {
              this.selectedCurrency.text = this.cbCurrency.selectedItem.value;
            }
            // Displaying the entity label next to the combo box
            if (StringUtils.trim(this.cbEntity.selectedItem.content) != "") {
              this.selectedEntity.text = this.cbEntity.selectedItem.value;
            }
            // Displaying the template label next to the combo box
            if (StringUtils.trim(this.cbTemplate.selectedItem.content) != "") {
              this.selectedTemplate.text= this.cbTemplate.selectedItem.value;
            }

            this.ccyList=  this.jsonReader.getSelects()["select"].find(x => x.id == "currency").option;
            // (this.lastRecievedJSON["selects"].children().(@id.search("currency") != -1)).option;
            this.entityList =  this.jsonReader.getSelects()["select"].find(x => x.id == "entity").option;
            // (this.lastRecievedJSON["selects"].children().(@id.search("entity") != -1)).option;
            this.templateList = this.jsonReader.getSelects()["select"].find(x => x.id == "template").option;
            // (this.lastRecievedJSON["selects"].children().(@id.search("template") != -1)).option;
            if (ExternalInterface.call('eval', 'currencyCode') != "undefined") {
              this.cbCurrency.selectedItem.content=ExternalInterface.call('eval', 'currencyCode');
              this.cbEntity.selectedItem.content=ExternalInterface.call('eval', 'entityId');
              this.cbTemplate.selectedItem.content=ExternalInterface.call('eval', 'templateId');
              this.cbCurrency.enabled=false;
              this.cbEntity.enabled=false;
            }
            this.setSelectedValue();
          }
        }
      }
    } catch(e) {
      console.log('error input datarespt', e)
    }

  }

  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   * @param event :FaultEvent
   */
  inputDataFault(event): void {
    this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }


  /**
   * addTemplate
   *
   * Method to pop up add Template
   *
   */
   addTemplate(): void {
    /* Url to load Add template screen */
    this.actionMethod="displayAddMonitorTemplate";
    this.actionMethod=this.actionMethod + "&callFrom=FTO";
    ExternalInterface.call("openTemplateAddWindow", this.actionMethod);
  }


  /**
   * changeTemplate
   *
   * Method to pop up change Template
   *
   */
  changeTemplate(): void {
    let getTemplate: string=null;
    let isPublic: string=null;
    let lockedBy: string=null;
    let userId: string=ExternalInterface.call("eval", "userId");
    if (this.cbTemplate.selectedLabel == "*DEFAULT*") {
      userId="*DEFAULT*";
    }
    getTemplate=ExternalInterface.call("getTemplateDetails", this.cbTemplate.selectedLabel, userId);
    if (getTemplate != "" && getTemplate.split("_DELIMETER_").length == 2) {
      if (StringUtils.trim(getTemplate.split("_DELIMETER_")[1]) == "Y") {
        isPublic="Yes";
      } else {
        isPublic="No";
      }
      lockedBy=ExternalInterface.call("lockTemplate", this.cbTemplate.selectedLabel,  getTemplate.split("_DELIMETER_")[0].split("_DELIMETERFORUSER_")[0], getTemplate.split("_DELIMETER_")[0].split("_DELIMETERFORUSER_")[1], isPublic);
      if (lockedBy == null || StringUtils.trim(lockedBy) == "") {
        this.actionMethod="displayChangeMonitorTemplate";
        this.actionMethod=this.actionMethod + "&templateId=" + this.cbTemplate.selectedLabel;
        this.actionMethod=this.actionMethod + "&userId=" + getTemplate.split("_DELIMETER_")[0].split("_DELIMETERFORUSER_")[0];
        this.actionMethod=this.actionMethod + "&templateName=" + getTemplate.split("_DELIMETER_")[0].split("_DELIMETERFORUSER_")[1];
        this.actionMethod=this.actionMethod + "&isPublic=" + isPublic;
        this.actionMethod=this.actionMethod + "&callFrom=FTO";
        ExternalInterface.call("openTemplateChangeWindow", this.actionMethod);
      } else {
        this.swtAlert.warning(ExternalInterface.call('getBundle', 'text', 'templateLock', 'Template ID is locked by') + " " + lockedBy,'Warning');
      }
    }
  }



  /**
   * Combobox event handling when there is a change in the in one of the combo's
   * @param e :Event
   */
  changeCombo(e): void {
    this.selectedCurrency.text = this.cbCurrency.selectedItem.value;
    this.selectedEntity.text= this.cbEntity.selectedItem.value;
    this.selectedTemplate.text = this.cbTemplate.selectedItem.value;
    this.comboChange=true;
    this.updateData("yes");
  }

  /**
   * When there is a change in the currency, this method is called
   * @param e :Event
   */
  changeCurrency(e): void {
    this.selectedCurrency.text = this.cbCurrency.selectedItem.value;
  }

  /**
   * When there is a change in the template, this method is called
   * @param e :Event
   */
  changeComboTemplate(e): void {
    this.selectedTemplate.text = this.cbTemplate.selectedItem.value;
  }

  /**
   *
   * This function is used to maintain the combo box selection
   */
  setSelectedValue(): void {
    this.selectedCurrency.text = this.cbCurrency.selectedItem.value;
    this.selectedEntity.text= this.cbEntity.selectedItem.value;
    this.selectedTemplate.text = this.cbTemplate.selectedItem.value;
  }


  /**
   * This method is used to save the date
   * @param comboClick:String
   */
 updateData(comboClick: string): void {
    let dupFlaf=true;
    let xml;
    if (comboClick == "no") {
      let temp;
      if (ExternalInterface.call('eval', 'currencyCode') == "undefined") {

        for  (let i = 0; i < this.optionXML.length; i++){
          if (this.optionXML[i].currency == String(this.cbCurrency.selectedItem.content) && this.optionXML[i].entity == String(this.cbEntity.selectedItem.content) && this.optionXML[i].templateid == String(this.cbTemplate.selectedItem.content) && this.optionXML[i].modifystate != "delete") {
            dupFlaf=false;
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.templateOption.recordExist', null),  'Warning');
            break;
          } else if (this.optionXML[i].currency == String(this.cbCurrency.selectedItem.content) && this.optionXML[i].entity == String(this.cbEntity.selectedItem.content) && this.optionXML[i].modifystate != "delete") {
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.templateOption.duplicateTemplate', null),  'Warning');
            dupFlaf=false;
            break;
          }
        }
      }
      if (dupFlaf) {
        this.btnSave.enabled=false;
        let  templateParam = new Object();
        templateParam["entity"]=String(this.cbEntity.selectedItem.content);
        templateParam["currency"] =String(this.cbCurrency.selectedItem.content);
        templateParam["template"] =String(this.cbTemplate.selectedItem.content);
        ExternalInterface.call("unloadCloseWindow", templateParam);
      }
    } else {
      this.requestParams=[];
      this.requestParams["forecastTemplateOptions.id.entityId"]= this.cbEntity.selectedItem.content;
      this.requestParams["forecastTemplateOptions.templateId"]=this.cbTemplate.selectedItem.content;
      this.refreshData.encodeURL=false;
      this.actionPath="forecastMonitor.do?";
      this.actionMethod="method=displayUserTemplateOptions";
      this.refreshData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.refreshData.send(this.requestParams);
    }
  }

  /**
   * doHelp
   *
   * @param event: Event
   *
   * Function is called when "Help" button is click. Displays help window
   */

  doHelp(event): void {
    ExternalInterface.call("help");
  }

  /**
   * Function called to close the window when close button is clicked
   * @param event :MouseEvent
   */
  closeHandler(event): void {
    ExternalInterface.call("closeWindow");
  }
}



// Define lazy loading routes
const routes: Routes = [
  { path: '', component: TemplateOptions }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [TemplateOptions],
  entryComponents: []
})
export class TemplateOptionsModule { }
