import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { SwtCanvas, SwtLabel, SwtCheckBox, SwtCommonGrid, SwtUtil, SwtModule, CommonService } from 'swt-tool-box';
import { Router } from '@angular/router';
declare var instanceElement: any;
@Component({
  selector: 'app-roles-tab',
  templateUrl: './RolesTab.html',
  styleUrls: ['./RolesTab.css']
})

export class RolesTab extends SwtModule implements OnInit{

  @ViewChild('rolesCanvas') rolesCanvas: SwtCanvas;
  @ViewChild('selectAllLbl') selectAllLbl: SwtLabel;
  @ViewChild('selectAllCheck') selectAllCheck: SwtCheckBox;

  public rolesGrid: SwtCommonGrid;
  public parentDocument: any;

  constructor(private commonService: CommonService, private element: ElementRef, private router: Router) {
    super(element, commonService);
   // this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.rolesGrid = <SwtCommonGrid>this.rolesCanvas.addChild(SwtCommonGrid);
    this.rolesGrid.editable = true;
    this.selectAllLbl.text = SwtUtil.getPredictMessage("scenario.role.selectAll.label", null);
   // this.selectAllCheck.toolTip= SwtUtil.getPredictMessage("scenario.role.selectAll.tooltip", null);
  }

  onLoad(){

  }

  selectAll(){
  for (let i = 0; i < this.rolesGrid.dataProvider.length; i++) {

    if(this.selectAllCheck.selected){

      this.rolesGrid.dataProvider[i].send ="Y" ; 

    } else  {

      this.rolesGrid.dataProvider[i].send ="N" ; 
      }              

  }
  this.rolesGrid.refresh();

  }


}
