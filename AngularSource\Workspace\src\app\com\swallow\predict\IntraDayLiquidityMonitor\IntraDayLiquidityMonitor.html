<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox id="globalContainer" width="100%" height="100%" minHeight="610" minWidth="1210" paddingBottom="5"
        paddingLeft="5" paddingRight="5" paddingTop="5">
    <SwtCanvas width="100%" height="100%">
      <VBox width="100%" height="100%">
        <SwtCanvas width="100%" height="3%" border="false">
          <Grid width="100%" height="100%">
            <GridRow width="100%" height="100%">
              <GridItem width="50%">
                <GridItem width="200">
                  <SwtLabel #valueDateLabel width="90"  styleName="labelBold">
                  </SwtLabel>
                  <SwtDateField id="valueDate" #valueDate  editable="true"  restrict="0-9/" width="65"
                                (change)="updateData('valueDate');currentPorcessStateHandler(null)">
                  </SwtDateField>
                </GridItem>
                <GridItem width="200">
                  <SwtLabel #ccyTimeLabel styleName="labelBold" width="120">
                  </SwtLabel>
                  <SwtTextInput id="timeframe" #timeframe
                                height="21" width="90" editable="false">
                  </SwtTextInput>
                </GridItem>
                <GridItem width="20%">
                  <SwtLoadingImage #loadingImage></SwtLoadingImage>
                  <ProcessStatusBox #processStatus id="processStatus">

                  </ProcessStatusBox>
                </GridItem>
              </GridItem>
              <GridItem width="50%">
                <HBox width="100%" horizontalAlign="right">
                  <GridItem>
                    <SwtLabel #profileLabel text="Profile" styleName="labelBold" width="70">
                    </SwtLabel>
                    <SwtComboBox id="profileCombo" #profileCombo width="332"
                                 (change)="changeProfile($event);updateILMConf('profileCombo', null)" dataLabel="profileList">
                    </SwtComboBox>
                    <HBox paddingTop="3">
                      <SwtButton id="saveProfileImage" style="z-index: 2;"  #saveProfileImage (click)="saveProfile($event)"
                                 styleName="fileSaveIcon">
                      </SwtButton>
                      <SwtButton id="revertProfileImage" style="z-index: 2;"  #revertProfileImage
                                 (click)="revertProfileClickHandler($event)" styleName="fileRevertIcon" enabled="false">
                      </SwtButton>
                      <SwtButton id="deleteProfileImage" #deleteProfileImage style="z-index: 2;"
                                 (click)="deleteProfileClickHandler($event)" styleName="fileDeleteIcon" ></SwtButton>
                    </HBox>
                  </GridItem>
                </HBox>
              </GridItem>
            </GridRow>
          </Grid>
        </SwtCanvas>

        <HBox x="0" y="65" width="100%" height="97%">
          <!-- <mx:TabNavigator id="tabNavigator"
									 
									 tabWidth="115"
									 width="100%"
									 height="100%" backgroundColor="0xD6E3FE"
									 change="tabChanged()"
									 selectedIndex="1"
									 creationPolicy="all">
						<s:NavigatorContent label="{ExternalInterface.call('getBundle', 'text', 'globalview', 'Global View')}">
							<tabs:globalview id="globalView">
							</tabs:globalview>
						</s:NavigatorContent>
						
						<s:NavigatorContent width="100%" height="100%" label="{ExternalInterface.call('getBundle', 'text', 'groupanalysis', 'Group Analysis')}">
							<tabs:groupanalysis id="groupAnalysis">
							</tabs:groupanalysis>
						</s:NavigatorContent>
						<s:NavigatorContent label="{ExternalInterface.call('getBundle', 'text', 'combinedview', 'Combined View')}">
							<tabs:combinedview id="combinedView">
							</tabs:combinedview>
						</s:NavigatorContent>
          </mx:TabNavigator> -->

          <SwtTabNavigator #tabs id="tabs" height="100%" (onChange)="tabChanged()" borderBottom="false" width="100%">
          </SwtTabNavigator>
        </HBox>
      </VBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
