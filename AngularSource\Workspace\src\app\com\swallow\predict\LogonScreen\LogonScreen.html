<SwtModule (creationComplete)='onLoad()' height='100%' width='100%' >
  <VBox width='100%' height="100%" backGroundColor="#F6F9FB">
  <div id="mydiv" style="position:absolute;  top: 0;  left: 0;  right: 0;  bottom: 0;  width:400px;  height:250px;  margin:auto; ">
  <VBox width='100%' height="100%"  paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas  width='100%' height="100%" >
      <VBox width='100%' height="100%" paddingLeft="10" paddingTop="20" paddingRight="10" >
    <SwtCanvas backGroundColor="#1F63AA" width='100%' height="100%">

      <VBox  width='100%' height="100%">
        <HBox width='100%' > 
          <SwtLabel paddingLeft="25" fontSize="18" color='white' #lblAuthMethod text="Welcome to SMART Predict" ></SwtLabel>
        </HBox>
        <HBox #logoutHBox width='100%' paddingTop="15" horizontalAlign="center" > 
          <SwtLabel   horizontalAlign="right" color='white' #messageLabel text="You have been successfully logged out" ></SwtLabel>
        </HBox>
        <HBox #authMethodHBox width='100%' paddingTop="15" >
            <SwtLabel color='white' #lblAuthMethod text="Authentication Method" ></SwtLabel>
            <SwtComboBox #comboAuthMethod id="comboAuthMethod" dataLabel="authMethod" width="190" height="19" tabIndex="3" ></SwtComboBox>

            
        </HBox>

        <HBox #reloginBox includeInLayout="false" visible='false' width='100%' paddingTop="15" horizontalAlign="center" > 
          <SwtLabel   horizontalAlign="right" color='white' #reloginLabel text="Please click on Login Button to proceed" ></SwtLabel>
        </HBox>

        <HBox width='100%' paddingTop="25" horizontalAlign="center" > 
          <SwtLabel   horizontalAlign="right" color='white' #unauthorisedLabel text="Unauthorised Access Prohibited" ></SwtLabel>
        </HBox>
    </VBox>

    </SwtCanvas>
    <!-- <SwtCanvas id="canvasContainer" width='100%' height="50"> -->
      <HBox width="100%">
        <HBox width="100%" paddingLeft="90" paddingTop="5">
          <SwtButton #loginButton label="Login"
                     id="saveButton"
                     (click)="loginHandler()"
                     (keyDown)="keyDownEventHandler($event)"
                     width="70">
          </SwtButton>
          <SwtButton #cancelButton
                      label="Cancel"
                     id="cancelButton"
                     buttonMode="true"
                     width="70"
                     >
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingTop="10"> 
          <!-- <SwtHelpButton id="helpIcon" 
                         [buttonMode]="true"
                         enabled="true"
                         (click)="doHelp()">
          </SwtHelpButton> -->
        </HBox>
      </HBox>
    <!-- </SwtCanvas> -->
    <SwtLabel   #balanceTypeLabel  fontSize="10"  paddingTop="5"  text="© 2006 - 2021 SwallowTech">
    </SwtLabel>
  </VBox>
  </SwtCanvas>

  </VBox>
</div>
</VBox>
</SwtModule>