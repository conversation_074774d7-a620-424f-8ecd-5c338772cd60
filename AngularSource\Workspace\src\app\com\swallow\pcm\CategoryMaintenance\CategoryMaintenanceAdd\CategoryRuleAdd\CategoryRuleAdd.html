<SwtModule (creationComplete)='onLoad()' height='100%' width='100%'>
  <VBox width='100%' height='100%'  paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas #canvas1 id="canvas1" width='100%' height="90%">
      <VBox width="100%" height="100%" verticalGap="1">
      <Grid width='100%' height="50%" marginTop="3">
        <GridRow>
          <GridItem width="15%">
            <SwtLabel #categoryRuleNameLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width='85%'>
            <SwtTextInput id="categoryRuleNameTxtInput"
                          #categoryRuleNameTxtInput
                          required="true"
                          maxChars="30"
                          width="270"
                          restrict="A-Za-z0-9\d_ !\&quot;#$%&'()*+,\-.\/:;&lt;=&gt;?@[\\\]^`{|}~">
            </SwtTextInput>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="15%">
            <SwtLabel  id="orderLabel"
                       #orderLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width='85%'>
            <SwtNumericInput id="orderNumInput"
                             #orderNumInput
                             required="true"
                             width="60"
                             textAlign="right"
                             (focusOut)="focusOutOrdinalInput()">
            </SwtNumericInput>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="15%">
            <SwtLabel id="sourceLabel"
                      #sourceLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width='30%'>
            <SwtComboBox id="sourceCombo" #sourceCombo width="200"
                         (change)="sourceChangeCombo()"
                         dataLabel="source">
            </SwtComboBox>
          </GridItem>
          <GridItem width='15%'>
            <SwtLabel id="selectedSource" #selectedSource
                      text=""  textAlign="left"  >
            </SwtLabel>
          </GridItem>

        </GridRow>
        <GridRow>
          <GridItem width="15%">
            <SwtLabel id="entityLabel"
                      #entityLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width='30%'>
            <SwtComboBox id="entityCombo" #entityCombo
                         (change)="entityChangeCombo()"
                         dataLabel="entityList">
            </SwtComboBox>
          </GridItem>
          <GridItem width='15%'>
            <SwtLabel id="selectedEntity" #selectedEntity
                      text=""
                      textAlign="left" >
            </SwtLabel>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="15%">
            <SwtLabel id="currencyLabel"
                      #currencyLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width='30%'>
            <SwtComboBox id="ccyCombo"
                         #ccyCombo
                         (change)="ccyChangeCombo()"
                         dataLabel="currencyList">
            </SwtComboBox>
          </GridItem>
          <GridItem width='15%'>
            <SwtLabel id="selectedCcy"
                      #selectedCcy
                      text=""
                      textAlign="left"  >
            </SwtLabel>
          </GridItem>
        </GridRow>
      </Grid >
      <!--<SwtPanel #panelRuleExpression width="100%" height="40%" title="Payment Category Rule Expression"></SwtPanel>-->
      <Grid  width="100%" height="50%">
        <SwtPanel #panelRuleExpression title="Payment Category Rule Expression" width="100%"  height="100%" verticalGap="3">
        <GridRow  width="100%" height="100%">
          <GridItem width="80%" height="100%" paddingTop="5">
            <SwtTextArea id='queryText'
                         #queryText
                         editable="false"
                         height="90%"
                         width="90%">
            </SwtTextArea>
          </GridItem>
          <GridItem width="10%" height="100%">
            <HBox width="100%" height="90%" verticalAlign="middle">
            <SwtButton id='ruleBuilderButton'
                       #ruleBuilderButton
                       [buttonMode]='true'
                       label='Rule Builder'
                       width="100"
                       (click)="doAddRule($event)"
                       (keydown)="keyDownEventHandler($event)">
            </SwtButton>
            </HBox>
          </GridItem>
        </GridRow>
        </SwtPanel>
        </Grid>
      </VBox>
    </SwtCanvas>
    <SwtCanvas id="canvasContainer" #canvasContainer width='100%' height="9%">
      <HBox width="100%" height="100%">
        <SwtButton #saveButton
                   (click)="save()"
                   (keyDown)="keyDownEventHandler($event)"
                   id="saveButton"></SwtButton>
        <SwtButton buttonMode="true"
                   id="cancelButton"
                   #cancelButton
                   (click)="popupClosed()"
                   (keyDown)="keyDownEventHandler($event)"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
