import {Component, ElementRef, NgModule, OnDestroy, OnInit, ViewChild} from '@angular/core';
import 'rxjs/add/observable/interval';
import {
  Alert,
  CommonService, CommonUtil, ContextMenuItem,
  ExternalInterface,
  HTTPComms,
  JSONReader, JSONViewer, ScreenVersion, StringUtils,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtComboBox, SwtCommonGrid,
  SwtDateField,
  SwtLabel,
  SwtLoadingImage,
  SwtModule, SwtPopUpManager, SwtToolBoxModule,
  SwtUtil,
} from 'swt-tool-box';
import {RouterModule, Routes} from '@angular/router';
import {ModuleWithProviders} from '@angular/compiler/src/core';
import moment from "moment";


declare var instanceElement: any;

@Component({
  selector: 'app-account-attribute',
  templateUrl: './AccountAttributeMaintenance.html',
  styleUrls: ['./AccountAttributeMaintenance.css']
})
export class AccountAttributeMaintenance extends  SwtModule implements OnInit , OnDestroy {

  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert  = new SwtAlert(commonService);
    window["Main"] = this;
  }
  /*********SwtCanvas*********/
  @ViewChild('gridCanvas') gridCanvas: SwtCanvas;
  /*********Combobox*********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('attributeCombo') attributeCombo: SwtComboBox;
  @ViewChild('accountCombo') accountCombo: SwtComboBox;
  /*********SwtLabel*********/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('accountLabel') accountLabel: SwtLabel;
  @ViewChild('attributeLabel') attributeLabel: SwtLabel;
  @ViewChild('endDateLabel') endDateLabel: SwtLabel;
  @ViewChild('startDateLabel') startDateLabel: SwtLabel;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedAccount') selectedAccount: SwtLabel;
  @ViewChild('selectedAttribute') selectedAttribute: SwtLabel;

  /*********SwtDateField*********/
  @ViewChild('startDate') startDate: SwtDateField;
  @ViewChild('endDate') endDate: SwtDateField;

  /*********SwtButton*********/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('exportButton') exportButton: SwtButton;
  @ViewChild('printButton') printButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('goButton') goButton: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;

  /**
   * Communication Objects
   **/
  private accountAttributesMaintenanceGrid: SwtCommonGrid;
  private inputData  = new HTTPComms(this.commonService);
  private baseURL  = SwtUtil.getBaseURL();
  private actionMethod;
  private actionPath;
  private requestParams = [];
  private invalidComms = "";

  /**
   * Data Objects
   **/
  private jsonReader: JSONReader  = new JSONReader();
  private lastReceivedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  private errorLocation = 0;
  private showJSONPopup: any;

  public moduleId  = 'Predict';
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private  entityId = null;
  private currencyCode  = null;
  private  accountId = null;
  private  attributeId = null;
  private dateFrom: string = null;
  private dateTo: string = null;
  private  parentScreen = null;
  private screenNameForPopup = "Account Attributes Maintenance Screen";
  private releaseDate = '27 February 2019';
  private  versionNumber = "1.0";
  private  selectedEffectiveDate: string = null;
  private menuAccessId  = 0;
  private menuAccess  = "";
  private  accessIndex: number;
  private attributeChanged= false;
  private dateFormat: string = null;
 private attributeSelected = false;
  private testDate: string ;


  ngOnInit(): void {
    instanceElement  = this;

    this.accountAttributesMaintenanceGrid  = this.gridCanvas.addChild(SwtCommonGrid) as SwtCommonGrid;
    this.accountAttributesMaintenanceGrid.uniqueColumn = "sequenceKey";

    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.goButton.label =  SwtUtil.getPredictMessage('button.go', null);
    this.goButton.toolTip =  SwtUtil.getPredictMessage('button.go', null);
    // Assining properties for controls
    this.addButton.setFocus();
    this.currencyLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.currency', null);
    this.ccyCombo.toolTip  = SwtUtil.getPredictMessage('label.accountattribute.currency', null);
    this.entityLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.entity', null);
    this.entityCombo.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.entity', null);
    this.accountLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.accountId', null);
    this.accountCombo.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.account', null);
    this.attributeLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.attribute', null);
    this.attributeCombo.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.attribute', null);
    this.startDateLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.startdate', null);
    this.startDate.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.startdate', null);
    this.endDateLabel.text  = SwtUtil.getPredictMessage('label.accountattribute.enddate', null);
    this.endDate.toolTip  = SwtUtil.getPredictMessage('tip.accountattribute.enddate', null) ;
  }

  ngOnDestroy(): any {
    instanceElement  = null;
  }

  /**
   * Upon completion of loading into the flash player this method is called
   **/
  onLoad() {
    // set version number
    this.initializeMenus();
    this.menuAccess = ExternalInterface.call('eval', 'menuAccessId');
    if(this.menuAccess) {
       if(this.menuAccess !== "") {
         this.menuAccessId = Number(this.menuAccess);
       }
    }
    this.dateFormat = ExternalInterface.call('eval', 'dateFormat');
    this.testDate = ExternalInterface.call('eval', 'dbDate');
    this.entityId = ExternalInterface.call('eval', 'entityId');
    this.currencyCode = ExternalInterface.call('eval', 'currencyCode');
    this.accountId = ExternalInterface.call('eval', 'accountId');
    this.attributeId = ExternalInterface.call('eval', 'attributeId');
    this.selectedEffectiveDate = ExternalInterface.call('eval', 'effectiveDate');
    this.dateFrom = ExternalInterface.call('eval', 'dateFrom');
    this.dateTo = ExternalInterface.call('eval', 'dateTo');
    this.parentScreen = ExternalInterface.call('eval', 'parentScreen');
    if(this.attributeId) {
      this.attributeSelected = true;
      this.startDate.selectedDate = null;
      this.endDate.selectedDate = null;
    } else {
      this.startDate.formatString =  this.dateFormat;
      this.startDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate, this.dateFormat.toUpperCase()));
      this.endDate.formatString =  this.dateFormat;
      this.endDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate,  this.dateFormat.toUpperCase()));
    }

    this.inputData.cbStart  = this.startOfComms.bind(this);
    this.inputData.cbStop  = this.endOfComms.bind(this);
    this.inputData.cbResult  = (event)  => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault  = this.inputDataFault.bind(this);
    this.inputData.encodeURL  = false;
    this.actionPath  = 'accountAttribute.do?';
    // Then declare the action method:
    this.actionMethod  = 'method=displayAccountAttributes';
    this.requestParams  = [];
    this.requestParams['entityId']  = this.entityId;
    this.requestParams['currencyCode']  = this.currencyCode;
    this.requestParams['accountId']  = this.accountId;
    this.requestParams['attributeId']  = this.attributeId;
    this.requestParams['dateFrom']  = this.dateFrom;
    this.requestParams['dateTo']  = this.dateTo;

    this.inputData.url  = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    this.accountAttributesMaintenanceGrid.onRowClick  = (event)  => {
      this.cellLogic(event);
    };

    this.addButton.enabled = this.menuAccessId == 0;


  }


  /**
   * inputDataResult
   * @param event:ResultEvent
   *
   * This method is called by the HTTPComms when result event occurs.
   *
   */

  inputDataResult(event) {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastReceivedJSON  = event;
      this.jsonReader.setInputJSON(this.lastReceivedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        if (JSON.stringify(this.lastReceivedJSON)  !== JSON.stringify(this.prevRecievedJSON)) {
          this.accessIndex = parseInt(this.jsonReader.getScreenAttributes()["accessInd"], 10);
          this.entityCombo.setComboData( this.jsonReader.getSelects(), false);
          this.entityCombo.enabled = (this.parentScreen!="accountAttributeLastvalue");
          this.selectedEntity.text  = this.entityCombo.selectedItem.value;

          this.ccyCombo.setComboData( this.jsonReader.getSelects(), false);
          this.ccyCombo.enabled = (this.parentScreen!="accountAttributeLastvalue");
          this.selectedCcy.text  = this.ccyCombo.selectedItem.value;

          this.accountCombo.setComboData(this.jsonReader.getSelects(), false);
          this.accountCombo.enabled = (this.parentScreen!="accountAttributeLastvalue");
          this.selectedAccount.text  = this.accountCombo.selectedItem.value;
          if(this.attributeSelected) {
            this.attributeCombo.setComboData(this.jsonReader.getSelects(), false);
          } else {
            this.attributeCombo.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
          }
          this.attributeCombo.enabled =(this.parentScreen!="accountAttributeLastvalue");
          this.selectedAttribute.text  =  (this.attributeCombo.selectedItem)? this.attributeCombo.selectedItem.value : "";

          this.endDateLabel.visible = true;
          this.endDate.visible = true;

          if(this.jsonReader.getSingletons().fromDate != null && StringUtils.trim(this.jsonReader.getSingletons().fromDate) != "") {
            this.testDate =  String(this.jsonReader.getSingletons().fromDate);
            this.startDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate,  this.dateFormat.toUpperCase()));
          }

          if (!this.jsonReader.isDataBuilding()) {

              this.accountAttributesMaintenanceGrid.colWidthURL ( this.baseURL+"accountAttribute.do?&screenName=accountAttributeValue");
              this.accountAttributesMaintenanceGrid.colOrderURL ( this.baseURL+"accountAttribute.do?&screenName=accountAttributeValue");
              this.accountAttributesMaintenanceGrid.saveWidths = true;
              this.accountAttributesMaintenanceGrid.saveColumnOrder = true;
              /*****grid display***********/
              const obj  = {columns: this.jsonReader.getColumnData()};
              this.accountAttributesMaintenanceGrid.CustomGrid(obj);
              this.accountAttributesMaintenanceGrid.doubleClickEnabled  = true;
              if (this.jsonReader.getGridData()) {
                if (this.jsonReader.getGridData().size > 0) {
                  this.accountAttributesMaintenanceGrid.gridData  = this.jsonReader.getGridData();
                  this.accountAttributesMaintenanceGrid.setRowSize  = this.jsonReader.getRowSize();
                  this.accountAttributesMaintenanceGrid.allowMultipleSelection  = true;
                } else {
                  this.accountAttributesMaintenanceGrid.dataProvider = null;
                  this.accountAttributesMaintenanceGrid.selectedIndex = -1;
                  this.disableOrEnableButtons(false);
                }
              } else {
                this.accountAttributesMaintenanceGrid.dataProvider  = null;
                // this.disableOrEnableButtons(false);
              }

              if(this.parentScreen != "accountAttributeLastvalue") {
                this.startDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate, this.dateFormat.toUpperCase()));
                this.endDate.selectedDate = new Date(CommonUtil.parseDate(this.testDate, this.dateFormat.toUpperCase()));
              } else {
                this.CheckEffectiveDateRaquiredValue(true);
                this.populateGrid();
              }
              if (StringUtils.trim(this.ccyCombo.selectedLabel) === "") {
               this.swtAlert.error(SwtUtil.getPredictMessage('alert.currencyAccess', null), SwtUtil.getPredictMessage('screen.error', null));
               this.addButton.enabled = false;
              } else {
              /* Add button should be disabled when we don't have any attribute or we don't have
                                 access according to the selected currency/entity */
              if(this.attributeCombo.dataProvider.length == 0 || this.accessIndex != 0 ) {
                this.addButton.enabled = false;
              } else { // if not we have to follow the access of the screen
                this.addButton.enabled = this.menuAccessId == 0;
              }
            }
          }
          this.prevRecievedJSON = this.lastReceivedJSON;
        }
      } else {

          this.swtAlert.error(SwtUtil.getPredictMessage('label.errorContactSystemAdmin', null)+' \n'+ this.jsonReader.getRequestReplyMessage(), SwtUtil.getPredictMessage('screen.error', null));
      }
    }
  }

  inputDataGridResult(event): void {
    try {
      this.lastReceivedJSON = event;
      this.jsonReader.setInputJSON(this.lastReceivedJSON);
      if ( this.jsonReader.getRequestReplyStatus()) {
        if (JSON.stringify(this.lastReceivedJSON)  !== JSON.stringify(this.prevRecievedJSON)) {
          if (!this.jsonReader.isDataBuilding()) {
            this.accountAttributesMaintenanceGrid.gridData = this.jsonReader.getGridData();
            this.accountAttributesMaintenanceGrid.setRowSize =  this.accountAttributesMaintenanceGrid.gridData.length;
          }
          // this.accountAttributesMaintenanceGrid.selectedIndex = parseInt(this.jsonReader.getScreenAttributes()["selectedRow"], 10);
          this.accountAttributesMaintenanceGrid.selectedIndex = -1;
          this.disableOrEnableButtons(false);
          this.prevRecievedJSON = this.lastReceivedJSON;
        }
      } else {
        this.swtAlert.error(SwtUtil.getPredictMessage('label.errorContactSystemAdmin', null)+' \n'+ this.jsonReader.getRequestReplyMessage(), SwtUtil.getPredictMessage('screen.error', null));
      }
    } catch (e) {
      console.log(e, this.moduleId, 'AccountAttributeMaintenance', 'inputDataGridResult');
    }

  }

  /**
   *  If a fault occurs with the connection with the server then display the lost connection label
   **/
  public inputDataFault(event): void {
    this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }

   /**
   * Method to disable or enable buttons depending on the menu access id
   *  @ param status:Boolean
   *  @ return
   */
  disableOrEnableButtons(status: boolean): void {
    if (status) {
      this.enableChangeButton(this.menuAccessId === 0 && this.accessIndex == 0);
      this.enableDeleteButton(this.menuAccessId === 0 && this.accessIndex == 0);
      this.enableViewButton(this.menuAccessId < 2);
    } else {
      this.enableChangeButton(false);
      this.enableDeleteButton(false);
      this.enableViewButton(false);
    }
  }

  /**
   * cellLogic
   * @ param e:CellEvent
   * When item click on the datagrd is method will be called
   * @ param e:CellEvent
   **/
  cellLogic(event): void {
    try {
      if (this.accountAttributesMaintenanceGrid.selectedIndex > -1 ) {
        this.disableOrEnableButtons(true);
      } else {
        this.disableOrEnableButtons(false);
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'AccountAttributeMaintenance', 'cellClickEventHandler', this.errorLocation);
    }
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  /**
   * addChangeClickHandler()
   *
   * This method is used to open account attribute screen details
   */
  addChangeClickHandler(method: string): void {
    let methodName = null;
    // Depending of the selected button set methodName value
    if(method == "Add") {
      methodName= "add";
    } else if (method == "Change") {
      methodName= "change";
    } else {
      methodName = "view";
    }

    // get selected values from comboboxes
    this.entityId =  this.entityCombo.selectedItem.content;
    this.currencyCode =  this.ccyCombo.selectedItem.content;
    this.accountId =  this.accountCombo.selectedItem.content;
    this.attributeId = (this.attributeCombo.selectedItem)? this.attributeCombo.selectedItem.content: "";
    this.dateFrom =this.startDate.text;
    this.dateTo = this.endDate.text;

    // Condition to chcek user selected yes for deleting
    let gridList = this.accountAttributesMaintenanceGrid.gridData;
    // Then declare the action method:
    this.actionMethod="accountAttributesAddMaintenance";

    // Set attributes
    this.actionMethod = this.actionMethod +'&entityId=' +  this.entityId;
    this.actionMethod = this.actionMethod + '&currencyCode=' + this.currencyCode;
    this.actionMethod = this.actionMethod + '&accountId='+ this.accountId;
    this.actionMethod = this.actionMethod + '&attributeId=' + this.attributeId;
    this.actionMethod = this.actionMethod + '&dateFrom='+ this.dateFrom;
    this.actionMethod = this.actionMethod + '&dateTo=' + this.dateTo;

    // If the clicked button is different of "Add" then send selected account id, effective date, value and sequence key in request
    if (methodName !== "add") {
      this.actionMethod = this.actionMethod + '&selectedAccountId=' + (gridList.length>0 ? this.accountAttributesMaintenanceGrid.selectedItem.accountId.content : "");
      this.actionMethod = this.actionMethod + '&effectivedateTime='+ (gridList.length>0 ? this.accountAttributesMaintenanceGrid.selectedItem.effectivedateTime.content : "");
      this.actionMethod = this.actionMethod + '&value=' + (gridList.length>0 ? this.accountAttributesMaintenanceGrid.selectedItem.value.content : "");
      this.actionMethod = this.actionMethod + '&seqKey='+ (gridList.length>0 ?this.accountAttributesMaintenanceGrid.selectedItem.sequenceKey.content : "");
    } else {
      if (this.startDate.selectedDate != null) {
        this.actionMethod = this.actionMethod + '&effectivedateTime='+ this.startDate.text;
      }
    }

    this.actionMethod = this.actionMethod + '&methodName='+ methodName;
    // Open the child screen using JSP
    if (!(this.attributeCombo.selectedItem)) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.warning.emptyAcctAttribute', null));
      return;
    } else {
      ExternalInterface.call("openChildWindow", this.actionMethod);
    }

  }

  /**
   * deleteHandler()
   *
   * This method is used to delete the selected record
   */
  deleteHandler(): void {
    this.swtAlert.confirm(SwtUtil.getPredictMessage('alert.columndelete', null),SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.removeRecord.bind(this), null);
  }

  /**
   * removeRecord()
   *
   * This method is used to delete the selected record after confirm
   */
  removeRecord(closeEvent): void {
    if (closeEvent.detail == Alert.YES) {
      // Get selected record details from combobox and selected row in the grid
      this.entityId =  this.entityCombo.selectedItem.content;
      this.currencyCode =  this.ccyCombo.selectedItem.content;
      this.accountId =  this.accountCombo.selectedItem.content;
      this.attributeId =  this.attributeCombo.selectedItem.content;
      this.dateFrom = this.startDate.selectedDate != null ? this.startDate.text :null;
      this.dateTo = this.endDate.selectedDate != null ? this.endDate.text :null;

      // Condition to chcek user selected yes for deleting
      this.actionMethod = "method=deleteAccountAttribute";
      this.requestParams["sequenceId"]= this.accountAttributesMaintenanceGrid.selectedItem.sequenceKey.content;
      this.requestParams["entityId"]= this.entityId;
      this.requestParams["currencyCode"]= this.currencyCode;
      this.requestParams["accountId"]= this.accountId;
      this.requestParams["selectedAccountId"]= this.accountAttributesMaintenanceGrid.selectedItem.accountId.content;
      this.requestParams["effectivedateTime"]= this.accountAttributesMaintenanceGrid.selectedItem.effectivedateTime.content;
      this.requestParams["attributeId"]= this.attributeId;
      this.requestParams["dateFrom"]= this.dateFrom;
      this.requestParams["dateTo"]= this.dateTo;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
     // this.accountAttributesMaintenanceGrid.selectedIndex = -1;
    }
  }

  /**
   * The function initializes the menus in the right click event on the Entity Monitor screen.
   * The links are redirected to their respective pages.
   */
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenNameForPopup, this.versionNumber, this.releaseDate);
    let addMenuItem: ContextMenuItem = new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }



  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastReceivedJSON,
      });
    this.showJSONPopup.width = "700";
    // this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "400";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.display();
  }

  /**
   * This method is used to handle the entity change.
   * This loads the entity with the default currency.
   */
  changeCombo(e): void {
    // Get the last modified combobox using the event target
    if(e.target.id != "accountCombo") {
      if(e.target.id == "entityCombo") {
        this.selectedEntity.text  = this.entityCombo.selectedItem.value;
        this.updateData(true);
      } else {
          this.selectedCcy.text  = this.ccyCombo.selectedItem.value;
          this.updateData();
      }
    } else {
      this.selectedAccount.text  = this.accountCombo.selectedItem.value;
    }
  }

  refreshParent(entity, ccy,account,attribute): void {
    this.entityId = entity;
    this.currencyCode = ccy;
    this.accountId = account;
    this.attributeId = attribute;

    this.inputData.cbStart  = this.startOfComms.bind(this);
    this.inputData.cbStop  = this.endOfComms.bind(this);
    this.inputData.cbResult = (event)  => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    // action url
    this.actionPath =  'accountAttribute.do?';
    this.actionMethod = 'method=displayAccountAttributes';
    this.requestParams = [];
    this.requestParams['entityId']  = this.entityId;
    this.requestParams['currencyCode']  = this.currencyCode;
    this.requestParams['accountId']  = this.accountId;
    this.requestParams['attributeId']  = this.attributeId;
    this.requestParams['resetDate']  = false;

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;

    this.inputData.send(this.requestParams);
  }


  updateData(resetDate: boolean = false): void {
     // Condition to check menuAccess is not full access
    if ( this.menuAccessId != 0) {
       this.addButton.enabled = false;
     }

    this.entityId = this.entityCombo.selectedItem.content;
    this.currencyCode = this.ccyCombo.selectedItem.content;
    this.accountId = this.accountCombo.selectedItem.content;
    this.attributeId = (this.attributeCombo.selectedItem)? this.attributeCombo.selectedItem.content : "";
    if(this.attributeId !== "") {
       this.attributeSelected = true;
     }
    this.inputData.cbStart  = this.startOfComms.bind(this);
    this.inputData.cbStop  = this.endOfComms.bind(this);
    this.inputData.cbResult = (event)  => {
       this.inputDataResult(event);
     };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    // action url
    this.actionPath =  'accountAttribute.do?';
    this.actionMethod = 'method=displayAccountAttributes';
    this.requestParams = [];
    this.requestParams['entityId']  = this.entityId;
    this.requestParams['currencyCode']  = this.currencyCode;
    this.requestParams['accountId']  = this.accountId;
    this.requestParams['attributeId']  = this.attributeId;
    this.requestParams['resetDate']  = resetDate;

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    this.accountAttributesMaintenanceGrid.selectedIndex = -1;
  }

  validateDateFieldValue(): void {
    if(this.validateDateField(this.startDate)) {
      // this.updateData();
    } else {
      this.startDate.text = "";
    }
  }

  validateDateField(dateField) {
    try {
      let date;
      const alert = SwtUtil.getPredictMessage('alert.validDate', null);
      if(dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase() , true);

        if(!date.isValid()) {
          this.swtAlert.warning(alert+ "("+ this.dateFormat.toUpperCase()+ ")");
          return false;
        }
      }
      dateField.selectedDate = date.toDate();
    } catch(error) {
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 'AccountAttributeMaintenance', 'validateDateField', this.errorLocation);
    }

    return true;
  }

  /**
   * This method is used to check the date range value
   *
   */
  checkDateRangeValue(): boolean {
    let isValidRange = true;
    if (((this.startDate) && (this.endDate)) && (!this.checkDates())) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.validation.dateRange', null), SwtUtil.getPredictMessage('screen.alert.warning', null));
      isValidRange = false;
     }
    return isValidRange;
  }

  checkDates() {

      try {
        let startDate: any;
        let endDate: any;
        if (this.startDate.text) {
          startDate = moment(this.startDate.text, this.dateFormat.toUpperCase(), true);
        }
        if (this.endDate.text) {
          endDate = moment(this.endDate.text, this.dateFormat.toUpperCase(), true);
        }

        if (!startDate && endDate) {
          return false;
        }

        if (startDate && endDate && endDate.isBefore(startDate)) {
          return false;
        }

        return true;
      } catch (error) {
        SwtUtil.logError(error, this.moduleId, "AccountAttributeMaintenance", "checkDates", this.errorLocation);

      }
    }


  /**
   * This method is used to enable or disable date fields according
   * to the value of Effective Date
   * if the effective date is required make date fields  enable and we default their values : to_day.
   * else they must be disable and their to blank.
   */
  CheckEffectiveDateRaquiredValue(blankDateValue = false) {

    this.selectedAttribute.text = this.attributeCombo.selectedItem.value;

    const  isRequiredEffectiveDate: boolean = ExternalInterface.call("isEffectiveDateRequired", this.attributeCombo.selectedItem.content);
    if (isRequiredEffectiveDate == false) {
      this.startDate.selectedDate = null;
      this.endDate.selectedDate = null;
      this.startDate.enabled = false;
      this.endDate.enabled = false;
    } else {
      this.startDate.enabled = true;
      this.endDate.enabled = true;
      if ( !blankDateValue && this.startDate.selectedDate == null && this.endDate.selectedDate == null) {
        this.startDate.selectedDate= new Date(CommonUtil.parseDate(this.testDate, this.dateFormat.toUpperCase()));
        this.endDate.selectedDate= new Date(CommonUtil.parseDate(this.testDate, this.dateFormat.toUpperCase()));
      }
    }
    this.attributeChanged = true;
  }

  /**
   * This method is used to refresh the grid depending of the selected
   * values combobxes or dates
   */
  populateGrid(): void {
    try {
      let entityId = this.entityCombo.selectedItem.content;
      let currencyCode = this.ccyCombo.selectedItem.content;
      let accountId = this.accountCombo.selectedItem.content;
      let accountAttributeId = (this.attributeCombo.selectedItem)? this.attributeCombo.selectedItem.content:"";
      let fromDate = this.startDate.text;
      let toDate = this.endDate.text;
      if (!(this.attributeCombo.selectedItem)) {
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.warning.emptyAcctAttribute', null));
        return;
      } else {
        if (this.checkDateRangeValue()) {
          this.inputData.cbStart=this.startOfComms.bind(this);
          this.inputData.cbStop=this.endOfComms.bind(this);

          // result event
          this.inputData.cbResult =(event)  => {
            this.inputDataGridResult(event);
          };

          this.inputData.cbFault = this.inputDataFault.bind(this);
          this.inputData.encodeURL=false;

          this.actionPath="accountAttribute.do?";
          this.actionMethod="method=listAccountAttributes";

          this.requestParams=[];
          this.requestParams['entityId']  = entityId;
          this.requestParams['currencyCode']  = currencyCode;
          this.requestParams['accountId']  = accountId;
          this.requestParams['attributeId']  = accountAttributeId;
          this.requestParams['dateFrom']  = fromDate;
          this.requestParams['dateTo']  = toDate;
          this.requestParams['parentScreen']  = this.parentScreen;
          this.requestParams['selectedEffectiveDate']  = this.selectedEffectiveDate;

          this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
          this.inputData.send(this.requestParams);
        }
      }

    } catch (error) {
      console.log(error, "AccountAttributeMaintenance", "populateGrid");
    }

  }

  /**
   * enableChangeButton
   *
   */
  enableChangeButton(value): void {
    this.changeButton.enabled = value;
    this.changeButton.buttonMode = value;
  }

  /**
   * enableViewButton
   *
   */
  enableViewButton(value: boolean): void {
    this.viewButton.enabled = value;
    this.viewButton.buttonMode = value;
  }

  /**
   * enableDeleteButton
   *
   */
  enableDeleteButton(value): void {
    this.deleteButton.enabled = value;
    this.deleteButton.buttonMode = value;
  }


  /**
   * popupClosedEventHandler
   *
   * @param event :Event
   *
   * Method called when pop up is closed to Enable Buttons after pop up closed
   *
   */
  public popupClosedEventHandler(event): void {
    try {
      this.accountAttributesMaintenanceGrid.selectable = true;
      this.accountAttributesMaintenanceGrid.doubleClickEnabled = true;
      // data grid is selectable and a row is selected enable buttons
      if (this.accountAttributesMaintenanceGrid.selectedIndices.length === 1 && this.accountAttributesMaintenanceGrid.selectable) {
        // Condition to check menu access is not 0 to disable buttons
        this.disableOrEnableButtons(true);
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'AttributeMaintenance', 'popupClosedEventHandler', this.errorLocation);
    }
  }

  /**
   * doHelp
   *
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call('help');
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'AttributeMaintenance', 'doHelp',   this.errorLocation);
    }
  }


  /**
   * closeHeader
   *
   * Function called when close button is called
   *
   * @param event:Event
   */
  closeHeader(event): void {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'AttributeMaintenance', 'closeCurrentTab', this.errorLocation);
    }
  }

  /**
   * dispose
   *
   * This is an event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.accountAttributesMaintenanceGrid = null;
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this.menuAccessId = null;
      this.lastReceivedJSON = null;
      this.prevRecievedJSON = null;
      ExternalInterface.call('close');
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'AttributeMaintenance', 'dispose', this.errorLocation);
    }
  }

}


// Define lazy loading routes
const routes: Routes = [
  { path: '', component: AccountAttributeMaintenance }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AccountAttributeMaintenance],
  entryComponents: []
})
export class AccountAttributeMaintenanceModule {}
