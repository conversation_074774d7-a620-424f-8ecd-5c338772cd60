import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { SwtModule, SwtAlert, CommonService, VBox, HDividedBox } from 'swt-tool-box';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-combined-view',
  templateUrl: './CombinedView.html',
  styleUrls: ['./CombinedView.css'],
  host: {
    '(window:resize)': 'resizeTab()'
  }
})
export class CombinedView extends SwtModule implements OnInit {

  @ViewChild('borderContainerChartCombined') borderContainerChartCombined: VBox;
  @ViewChild('secondContainer') secondContainer: VBox;
  @ViewChild("divRestOfspace") divRestOfspace: ElementRef;
  protected subscriptions: Subscription[] = [];
  protected swtAlert: SwtAlert;
  private globalDivider: any;
  private analysisDivider: any;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngAfterViewInit() {
    this.resizeTab();
  }
  

  
  resizeTab() {
    const posData = this.divRestOfspace.nativeElement.getBoundingClientRect();
    //Called after every check of the component's view. Applies to components only.
    //Add 'implements AfterViewChecked' to the class.
    const calRest = (window.innerHeight - posData.top);
    this.divRestOfspace.nativeElement.style.height = Math.round(calRest) + "px";

  }

  ngOnInit() {

  }

  private onLoad(): void {

    // this.addEventListener(DividerButtonEvent.DIVIDER_BUTTON_CLICK, function dividerBoxButtonClick(event:DividerButtonEvent):void {
    //   if(event.target.toString().indexOf("borderContainerChartCombined")!=-1){
    //     (secondContainer.getElementAt(0) as HorizontalDividerBox).resizeDividedBox(event.buttonObject.toString(), true , event.dividerObject.targetTo);
    //   }
    //   else{
    //     (borderContainerChartCombined.getElementAt(0) as HorizontalDividerBox).resizeDividedBox(event.buttonObject.toString(), true , event.dividerObject.targetTo);
    //   }
    // }, true);
    // //TODO : Not finished yet
    // this.addEventListener(DividerButtonEvent.DIVIDER_DRAG_COMPLETE, function dividerBoxDrag(event:DividerButtonEvent):void {
    //   if(event.target.toString().indexOf("borderContainerChartCombined")!=-1){
    //     /*FlexGlobals.topLevelApplication.groupAnalysis.lineChartContainer.height;
    //     FlexGlobals.topLevelApplication.groupAnalysis.a.height ;*/

    //     (secondContainer.getElementAt(0) as HorizontalDividerBox).resizeDividedBox(event.buttonObject.toString(),true,(borderContainerChartCombined.getElementAt(0) as ExtendedDividedBox).getElementAt(1).width);
    //   }

    //   else{
    //     (borderContainerChartCombined.getElementAt(0) as HorizontalDividerBox).resizeDividedBox(event.buttonObject.toString(),true,(secondContainer.getElementAt(0) as ExtendedDividedBox).getElementAt(1).width);
    //   }
    // }, true);

  }

  public initData(combinedGlobalLegendsBoxWidth: number, combinedAnalysisLegendsBoxWidth: number, globalDivider: HDividedBox, analysisDivider: HDividedBox): void {
    this.resizeTab();
    this.globalDivider = globalDivider;
    this.analysisDivider = analysisDivider;
    /*this.parentDocument.entityCombo.enabled = false;
    this.parentDocument.ccyCombo.enabled = false;*/
    this.parentDocument.valueDate.enabled = false;
    //this.parentDocument.ccyMuliplierCB.enabled = false;


    // if(combinedGlobalLegendsBoxWidth != -1 ) 
    //   (secondContainer.getElementAt(0) as HorizontalDividerBox).resizeDividedBox(null,true,combinedGlobalLegendsBoxWidth);
    // else 
    //   (secondContainer.getElementAt(0) as HorizontalDividerBox).resizeDividedBox(null,true,0);


    // if(combinedAnalysisLegendsBoxWidth != -1 ) 
    //   (borderContainerChartCombined.getElementAt(0) as HorizontalDividerBox).resizeDividedBox(null,true,combinedAnalysisLegendsBoxWidth);
    // else
    //   (borderContainerChartCombined.getElementAt(0) as HorizontalDividerBox).resizeDividedBox(null,true,0);
    if (combinedGlobalLegendsBoxWidth != -1) {
      combinedGlobalLegendsBoxWidth = globalDivider.widthRightPixel;
    }
    if (combinedGlobalLegendsBoxWidth != combinedAnalysisLegendsBoxWidth) {
      combinedAnalysisLegendsBoxWidth = combinedGlobalLegendsBoxWidth;
    }

    if (combinedGlobalLegendsBoxWidth != -1)
      globalDivider.setWidthRightWithoutEvent(''+combinedGlobalLegendsBoxWidth);
    else
      globalDivider.widthRight = '' + 0;


    if (combinedAnalysisLegendsBoxWidth != -1)
      analysisDivider.setWidthRightWithoutEvent(''+combinedAnalysisLegendsBoxWidth);
    else
      analysisDivider.widthRight = '' + 0;


    this.subscriptions.push(analysisDivider.DIVIDER_DRAG_COMPLETE.subscribe((event) => {
      if(this.globalDivider.widthRight != this.analysisDivider.widthRight){
        this.globalDivider.setWidthRightWithoutEvent(this.analysisDivider.widthRight);
      }
    }));
    this.subscriptions.push(globalDivider.DIVIDER_DRAG_COMPLETE.subscribe((event) => {
      if(this.analysisDivider.widthRight != this.globalDivider.widthRight){
        this.analysisDivider.setWidthRightWithoutEvent(this.globalDivider.widthRight);
      }
    }));

  }

  resetSubscriber() {
    this.subscriptions = this.unsubscribeAllObservables(this.subscriptions);
  }


  /**
   * Unsubscribe all Observables Subscriptions
   * It will return an empty array if it all went well
   * @param subscriptions
   */
  unsubscribeAllObservables(subscriptions: Subscription[]): Subscription[] {
    if (Array.isArray(subscriptions)) {
      subscriptions.forEach((subscription: Subscription) => {
        if (subscription && subscription.unsubscribe) {
          subscription.unsubscribe();
        }
      });
      subscriptions = [];
    }

    return subscriptions;
  }

}
