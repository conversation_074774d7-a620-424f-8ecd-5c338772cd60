<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas minWidth="1000" width="100%" height="97">
      <Grid width="100%" height="100%">
        <GridRow>
          <GridItem width="50%">
            <GridItem width="120">
              <SwtLabel id="entityLabel" #entityLabel textDictionaryId="ilmthroughput.entity">
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="entityCombo" width="200" (open)="openedCombo($event)" (close)="closedCombo($event)"
                #entityCombo dataLabel="entityList" (change)="entityChangeCombo($event)">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
              <GridItem width="170">
                <SwtLabel #valueDateLabel id="valueDateLabel" textDictionaryId="ilmthroughput.date">
                </SwtLabel>
              </GridItem>
              <GridItem>

                <SwtRadioButtonGroup #dateSelected id="dateSelected" align="horizontal"
                  (change)="changeRadioGroup($event)">
                  <SwtRadioItem value="T" groupName="dateSelected" width="120" selected="true"
                    id="todayRadio" #todayRadio></SwtRadioItem>
                  <SwtRadioItem value="D" groupName="dateSelected" id="selectedDateRadio" #selectedDateRadio>
                  </SwtRadioItem>

                </SwtRadioButtonGroup>
                <SwtDateField id="valueDate" #valueDate width="70" enabled="false" (change)="validateDate()"
                  (keyup.enter)="validateDate()" restrict="0-9/" toolTip="Enter value date (DD/MM/YYYY)">
                </SwtDateField>
              </GridItem>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="50%">
            <GridItem width="120">
              <SwtLabel id="ccyLabel" #ccyLabel textDictionaryId="ilmthroughput.currency">
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="ccyCombo" #ccyCombo width="200" (change)="changeCombo($event)"
                (open)="openedCombo($event)" (close)="closedCombo($event)" dataLabel="currencyList">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel id="selectedCcy" #selectedCcy fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
            <GridItem width="180">
              <SwtLabel id="calculateAS" #entityLabel textDictionaryId="ilmthroughputCalculateAs">
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="calculateASCombo" width="230" (open)="openedCombo($event)" (close)="closedCombo($event)"
                #calculateASCombo dataLabel="calculateAS" (change)="changeCombo($event)">
              </SwtComboBox>
            </GridItem>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow id="selectGroup" #selectGroup>
          <GridItem width="50%">
            <GridItem width="120">
              <SwtLabel #groupcomboLabel id="groupcomboLabel" textDictionaryId="ilmthroughput.group">
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox dataLabel="AcctGrpList" id="groupCombo" #groupCombo width="200"
                (change)="changeCombo($event)">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel id="selectedGroup" #selectedGroup fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
              <SwtLabel width="180" id="scenarioLabel" #scenarioLabel textDictionaryId="scenario.scenarioId">
              </SwtLabel>
              <SwtComboBox dataLabel="scenarioList" id="scenarioCombo" #scenarioCombo width="230"
              (change)="changeCombo($event)">
            </SwtComboBox>
            </HBox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <SwtTabNavigator #tabs id="tabs"  minWidth="1000" [height]="calculateHeight()" borderBottom="false" width="100%">
      <SwtTab width="100%" height="100%" #summaryTab id="summaryTab" label="Summary">
        <VBox width="100%" height="100%" paddingLeft="5" styleName="borderVBox">
          <SwtCanvas styleName="canvasWithGreyBorder" id="displaycontainer" border="false" width="100%" height="100%"
            #displaycontainer>
          </SwtCanvas>
        </VBox>
      </SwtTab>
    </SwtTabNavigator>

    <SwtCanvas width="100%" height="40" marginBottom="0" minWidth="1000">
      <HBox width="100%">
        <HBox paddingLeft="5" width="50%">
          <SwtButton [buttonMode]="true" id="refreshButton" #refreshButton (click)="dataRefresh(null)">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="optionsButton" (click)="optionsHandler()" #optionsButton>
          </SwtButton>
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
        </HBox>


        <HBox horizontalAlign="right" width="50%" horizontalGap="2">
          <SwtLabel visible="false" textDictionaryId="screen.connectionError" color="red" #lostConnectionText
            id="lostConnectionText"  height="16">
          </SwtLabel>
          <SwtLabel id="lastRefLabel" #lastRefLabel styleName="labelLeftRefTime" fontWeight="normal">
          </SwtLabel>
          <SwtLabel #lastRefTime id="lastRefTime" fontWeight="normal"></SwtLabel>
        </HBox>
        <HBox horizontalAlign="right" horizontalGap="2">
          <div>
            <DataExport #dataExport id="dataExport"></DataExport>
          </div>
          <SwtHelpButton id="helpIcon" (click)="doHelp()"> </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
