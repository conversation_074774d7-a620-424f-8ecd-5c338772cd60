<div #divRestOfspace>
<SwtModule #swtModule height="100%" width="100%">
  <VDividedBox height="100%" extendedDividedBox="true" id="gridDivider" #gridDivider dividersAnimation="S" width="100%"
    liveDragging="true">
    <VBox class="top" height="50%" width="100%">
      <VBox height="100%" width="100%" #borderContainerChartCombined id="borderContainerChartCombined" ></VBox>
    </VBox>
    <VBox class="bottom" height="50%" width="100%">
      <VBox height="100%" width="100%" #secondContainer id="secondContainer" ></VBox>
    </VBox>
    </VDividedBox>

</SwtModule>
</div>