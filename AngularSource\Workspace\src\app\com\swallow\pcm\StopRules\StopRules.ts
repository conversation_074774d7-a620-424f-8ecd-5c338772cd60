import { Component, ElementRef, OnD<PERSON>roy, OnInit, ViewChild, NgModule } from '@angular/core';
import { Alert, CommonService, ExternalInterface, focusManager, HashMap, HTTPComms, JSONReader, Keyboard, SwtAlert, SwtButton, SwtCanvas, SwtCommonGrid, SwtLoadingImage, SwtModule, SwtPanel, SwtPopUpManager, SwtRadioButtonGroup, SwtRadioItem, SwtUtil, TitleWindow, SwtToolBoxModule, StringUtils } from 'swt-tool-box';
import { FourEyesProcess } from '../FourEyesProcess/FourEyesProcess';
import { Routes, RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/compiler/src/core';

declare var instanceElement: any;

@Component({
  selector: 'pcstop-rules',
  templateUrl: './StopRules.html',
  styleUrls: ['./StopRules.css']
})
export class StopRules extends SwtModule implements OnInit, OnDestroy {

  @ViewChild('canvasStopRules') canvasStopRules: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /********SwtButton*********************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('activateButton') activateButton: SwtButton;
  @ViewChild('deactivateButton') deactivateButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('settingButton') settingButton: SwtButton;

  @ViewChild('inactiveRadioButton') inactiveRadioButton: SwtRadioItem;
  @ViewChild('bothRadioButton') bothRadioButton: SwtRadioItem;
  @ViewChild('activeRadioButton') activeRadioButton: SwtRadioItem;
  @ViewChild('statusGroup') statusGroup: SwtRadioButtonGroup;
  
  @ViewChild('csv') csv: SwtButton;
  @ViewChild('excel') excel: SwtButton;
  @ViewChild('pdf') pdf: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;



  private stopRulesGrid: SwtCommonGrid;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public checkAuthData = new HTTPComms(this.commonService);
  
  private logicUpdate = new HTTPComms(this.commonService);
  public requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';

  /* - START -- Screen Name and Version Number ---- */
  private moduleName = 'Stop Rules Maintenance';
  private versionNumber = '1.00.00';
  private releaseDate = '04 March 2019';
  /* - END -- Screen Name and Version Number ---- */

  // to open a pop up
  private child: SwtPanel;
  private moduleURL: string = null;
  private swtAlert: SwtAlert;
  private menuAccess = '';
  private programId = '';
  private componentId = false;
  public helpURL: string = null;
  private message: string = null;
  public title: string = null;
  private errorLocation = 0;
  public moduleReportURL: string = null;
  public moduleId = '';
  private groupId: string = null;
  private groupName: string = null;
  private groupType: string = null;
  private moduleSearchURL: string = null;
  public searchQuery = '';
  public searchFlag = false;
  public queryToDisplay = '';
  public sQuery = '';
  private mapMultiplierList: HashMap = new HashMap();
  private mapPriorityList: HashMap = new HashMap();
  private win: TitleWindow;

  private menuaccess = 2;
  private requireAuthorisation = true;
  private faciltiyId = null;
  selectedStatus: any;
  private doUpdateRecord:boolean = false;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }


  ngOnInit(): void {

    // this.screenName = "add";
    instanceElement = this;

  }
  disableButtons() {
    if(this.stopRulesGrid.selectedIndex ==-1)
      this.disableComponents();

  }
  onLoad() {
    this.stopRulesGrid = <SwtCommonGrid>this.canvasStopRules.addChild(SwtCommonGrid);
    this.stopRulesGrid.uniqueColumn = "stopRuleId";
    this.stopRulesGrid.onFilterChanged = this.disableButtons.bind(this);
    this.stopRulesGrid.onSortChanged = this.disableButtons.bind(this);
    try {

      // this.moduleId = '';
      this.title = SwtUtil.getAMLMessages('pcpriorityMaintenanceScreen.windowtitle.help_screen');
      this.message = SwtUtil.getAMLMessages('pcpriorityMaintenanceScreen.message.help_message');
      this.actionMethod = 'method=display';
      this.actionPath = 'stopRulesPCM.do?';
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      // this.inputDataResult(data);

      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.stopRulesGrid.onRowClick = (event) => {
        this.checkIfMaintenanceEventExist(event);
      };
      this.stopRulesGrid.onRowDoubleClick = (event) => {
        //this.doViewPCPriorityMaintenance(event);
      };
      // Assining properties for controls
      this.addButton.label = "Add";//String(SwtUtil.getCommonMessages('button.add'));
      this.activateButton.label = "Activate";//String(SwtUtil.getCommonMessages('button.add'));
      this.deactivateButton.label = "Deactivate";//String(SwtUtil.getCommonMessages('button.add'));
      this.viewButton.label = "View";//String(SwtUtil.getCommonMessages('button.view'));
      this.changeButton.label = "Change";//String(SwtUtil.getCommonMessages('button.view'));
      this.deleteButton.label = "Delete";// String(SwtUtil.getCommonMessages('button.delete'));
      this.closeButton.label = "Close";//String(SwtUtil.getCommonMessages('button.close'));
      this.addButton.setFocus();
      this.disableComponents();
    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'ClassName', 'onLoad');
    }

  }

  /**
   * inputDataFault
   *
   * @param event:  FaultEvent
   *
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    try {
      this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'inputDataFault', this.errorLocation);
    }
  }
  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  public inputDataResult(data): void {
    let jsonList = null;
    let header: string;
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Get result as xml
        this.lastRecievedJSON = data;
        // Parse result json
        this.jsonReader.setInputJSON(this.lastRecievedJSON);

        
          // Condition to check request reply status is true
          if (this.jsonReader.getRequestReplyStatus()) {
            if(this.doUpdateRecord){
              if(StringUtils.isTrue(this.requireAuthorisation)){
                this.swtAlert.show("This action needs second user authorisation", "Warning", Alert.OK);
              }
              this.doUpdateRecord = false;
            }
            this.helpURL = this.jsonReader.getSingletons().helpurl;
         
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {
              jsonList = this.jsonReader.getColumnData();
              for (let i = 0; i < jsonList.column.length; i++) {
                header = SwtUtil.getAMLMessages(jsonList.column[i].heading);
                jsonList.column[i].heading = header;
              }
              const obj = { columns: jsonList };

              this.stopRulesGrid.dateFormat = this.jsonReader.getScreenAttributes()["dateformat"];

              if (this.stopRulesGrid === null || this.stopRulesGrid === undefined) {
                this.stopRulesGrid.screenID = this.programId;
                this.stopRulesGrid.componentID = this.jsonReader.getSingletons().screenid;
                this.stopRulesGrid.CustomGrid(obj);
              }

              this.componentId = this.lastRecievedJSON.screenid;
              this.stopRulesGrid.doubleClickEnabled = true;
              this.stopRulesGrid.CustomGrid(obj);

              this.stopRulesGrid.dataProvider = null;
              this.stopRulesGrid.gridData = this.jsonReader.getGridData();
              this.stopRulesGrid.setRowSize = this.jsonReader.getRowSize();
              this.stopRulesGrid.doubleClickEnabled = true;
             // this.stopRulesGrid.refreshFilters();
             this.disableButtons();
              if (this.stopRulesGrid.selectedIndex != -1) {
                //this.stopRulesGrid.selectedIndex = this.stopRulesGrid.selectedIndex;
                //this.cellClickEventHandler(null);
              }


             
              this.menuaccess = this.jsonReader.getScreenAttributes()["menuaccess"];

              this.requireAuthorisation = this.jsonReader.getScreenAttributes()["requireAuthorisation"];
              this.faciltiyId = this.jsonReader.getScreenAttributes()["faciltiyId"];
              

              if (this.menuaccess == 0) {
                this.addButton.enabled = true;
              }

            }
            this.selectedStatus = this.jsonReader.getSingletons().status;
            
            if(this.selectedStatus)
              this.statusGroup.selectedValue = this.selectedStatus;
            else 
             this.statusGroup.selectedValue = "A";

            this.prevRecievedJSON = this.lastRecievedJSON;
          } else {
            if (this.jsonReader.getRequestReplyMessage() == "PROCESS_RUNNING") {
              this.swtAlert.error("Stop rule Process is running on the selected Rule , the selected Rule cannot be deleted or deactivated for the moment");
            } else if (this.jsonReader.getRequestReplyMessage() == "OTHER_RECORD_DEPEND") {
              this.swtAlert.error("Cannot delete a STOP rule that has previously stopped a Payment Request");
            } else {
              this.swtAlert.error("Error occurred, Please contact your System Administrator");
            }
          }
      }
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error inputDataResult', e);
    }
  }
  


  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    try {
      this.loadingImage.setVisible(true);
      this.disableComponents();
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'startOfComms', this.errorLocation);
    }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    try {
      this.loadingImage.setVisible(false);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'endOfComms', this.errorLocation);
    }
  }


  checkIfMaintenanceEventExist(event:Event): void {
    try {

      if (this.stopRulesGrid.selectedIndices.length === 1 && this.stopRulesGrid.selectable) {
        let actionMethod = 'method=checkIfMaintenenanceEventExist';
        let actionPath = 'maintenanceEvent.do?';
        this.checkAuthData.cbStart = this.startOfComms.bind(this);
        this.checkAuthData.cbStop = this.endOfComms.bind(this);
        this.checkAuthData.cbResult = (data) => {
          this.checkResult(data);
        };
        this.requestParams['recordId']= this.stopRulesGrid.selectedItem.stopRuleId.content;
        this.requestParams['facilityId']= this.faciltiyId;
        this.checkAuthData.cbFault = this.inputDataFault.bind(this);
        this.checkAuthData.encodeURL = false;
        this.checkAuthData.url =  this.baseURL + actionPath + actionMethod;
        this.checkAuthData.send(this.requestParams);
      }
    } catch (e) {
      console.log(e);
      // log the error in ERROR LOG
      //SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'ClassName', 'updateData',  this.errorLocation);
    }
  }

  public checkResult(data): void {

    try {
      if (this.checkAuthData && this.checkAuthData.isBusy()) {
        this.checkAuthData.cbStop();
      } else {
        this.jsonReader.setInputJSON(data);

          
        if (this.jsonReader.getRequestReplyMessage() == "RECOD_EXIST") {
          const message = SwtUtil.getPredictMessage('maintenanceEvent.alert.cannotBeAmended', null);
          this.swtAlert.error(message);
          this.cellClickEventHandler(true);
        }  else {
          this.cellClickEventHandler(false);
        }

      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }


  /**
   * cellClickEventHandler
   *
   * @param event: Event
   *
   * This method is used to manumberain the button status when a row is clicked
   */
  cellClickEventHandler(isMaintenenanceEventRelated =false): void {

    try {

      if (this.stopRulesGrid.selectedIndices.length === 1 && this.stopRulesGrid.selectable) {
        
        if (this.menuaccess == 0 && !isMaintenenanceEventRelated) {
          if (this.stopRulesGrid.selectedItem.isActive.content == "true") {
            
            this.enableDisableDeactivateButton(true);
            
            if(this.stopRulesGrid.selectedItem.isChangable.content == "false")
              this.enableDisableChangewButton(false);
            else 
              this.enableDisableChangewButton(true);

            this.enableDisableActivateButton(false);
          }
          
          if (this.stopRulesGrid.selectedItem.isActive.content == "false") {
            this.enableDisableDeactivateButton(false);
      

            if (parseInt(this.stopRulesGrid.selectedItem.numberOfLinkedPR.content) > 0) {
                this.enableDisableActivateButton(false);
                this.enableDisableChangewButton(false);
            } else {

            if(this.stopRulesGrid.selectedItem.isChangable.content == "false")
              this.enableDisableChangewButton(false);
            else 
              this.enableDisableChangewButton(true);

              this.enableDisableActivateButton(true);
            }
          }

          this.enableDisableDeleteButton(true);

        }
        this.enableDisableViewButton(true);
      } else {
        this.enableDisableActivateButton(false);
        this.enableDisableDeleteButton(false);
        this.enableDisableDeactivateButton(false);
        this.enableDisableViewButton(false);
        this.enableDisableChangewButton(false);
      }

      this.addButton.setFocus();
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'cellClickEventHandler', this.errorLocation);
    }
  }



  /**
  * disableComponents()
  *
  * Method called to disable components
  *
  */
  disableComponents(): void {
    try {
      this.enableDisableActivateButton(false);
      this.enableDisableDeleteButton(false);
      this.enableDisableDeactivateButton(false);
      this.enableDisableViewButton(false);
      this.enableDisableChangewButton(false);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'disableComponents', this.errorLocation);
    }
  }

  /**
   * enableViewButton
   *
   */
  enableDisableViewButton(value: boolean): void {
    this.viewButton.enabled = value;
    this.viewButton.buttonMode = value;
  }
  /**
   * enableViewButton
   *
   */
  enableDisableChangewButton(value: boolean): void {
    this.changeButton.enabled = value;
    this.changeButton.buttonMode = value;
  }
  /**
   * enableDeleteButton
   *
   */
  enableDisableDeleteButton(value): void {
    this.deleteButton.enabled = value;
    this.deleteButton.buttonMode = value;
  }

  /**
 * enableDeleteButton
 *
 */
  enableDisableActivateButton(value): void {
    this.activateButton.enabled = value;
    this.activateButton.buttonMode = value;
  }

  /**
 * enableDeleteButton
 *
 */
  enableDisableDeactivateButton(value): void {
    this.deactivateButton.enabled = value;
    this.deleteButton.buttonMode = value;
  }


  /**
  * keyDownEventHandler
  *
  * @param event:  KeyboardEvent
  *
  * This is a key event listener, used to perform the operation
  * when hit the enter key based on the currently focused property(Button)
  */
  keyDownEventHandler(event): void {
    try {
      const eventstring = Object(focusManager.getFocus()).name;
      if ((event.keyCode === Keyboard.ENTER)) {
        if (eventstring === 'addButton') {
          // this.doAddPCPriorityMaintenance(event);
        } else if (eventstring === 'viewButton') {
          //this.doViewPCPriorityMaintenance(event);
        } else if (eventstring === 'closeButton') {
          this.closeCurrentTab(event);
        } else if (eventstring === 'csv') {
          this.report('csv');
        } else if (eventstring === 'excel') {
          this.report('xls');
        } else if (eventstring === 'pdf') {
          this.report('pdf');
        } else if (eventstring === 'helpIcon') {
          this.doHelp();
        } else if (eventstring === 'deleteButton') {
          // this.doDeletePCPriorityMaintenance(event);
        } else if (eventstring === 'settingButton') {
          // this.getSetting();
        } else if (eventstring === 'cancelButton') {
          //this.reset(event);
        } else if (eventstring === 'activateButton') {
          this.doActivatePCStopRuleEventHandler(event);
        } else if (eventstring === 'deactivateButton') {
          this.doActivatePCStopRuleEventHandler(event);
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'keyDownEventHandler', this.errorLocation);
    }
  }





  /**
   * report
   *
   * @param type: string
   *
   * This is a report icon action handler method
   */
  report(type: string): void {
    let selectedFilter: string = null;
    let selectedSort = '';
    let moduleId: string = null;
    try {
      moduleId = this.moduleId;
      if (this.stopRulesGrid.filteredGridColumns !== '') {
        selectedFilter = this.stopRulesGrid.getFilteredGridColumns();
      } else {
        selectedFilter = '';
      }
      selectedSort = this.stopRulesGrid.getSortedGridColumn();
      //set the action path
      this.actionPath = 'aml/PCPriorityMaintenance!displayReport.do?';
      this.actionMethod = 'type=' + type;
      this.actionMethod = this.actionMethod + '&programId=' + this.programId;
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod = this.actionMethod + '&selectedFilter=' + selectedFilter;
      this.actionMethod = this.actionMethod + '&selectedSort=' + selectedSort;
      this.actionMethod = this.actionMethod + '&currentModuleId=' + moduleId;
      this.actionMethod = this.actionMethod + '&sQuery=' + this.searchQuery;
      this.actionMethod = this.actionMethod + '&searchFlag=' + this.searchFlag;
      this.actionMethod = this.actionMethod + '&moduleId=' + this.moduleId;
      this.actionMethod = this.actionMethod + '&print=' + 'ALL';
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, moduleId, 'ClassName', 'report', this.errorLocation);
    }
  }

  /**
 * closeCurrentTab
 *
 * Function called when close button is called
 *
 * @param event:Event
 */
  closeCurrentTab(event): void {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'ClassName', 'refreshGrid', this.errorLocation);
    }

  }

  /**
  * dispose
  *
  * This is an event handler, used to close the current tab/window
  */
  dispose(): void {
    try {
      this.stopRulesGrid = null;
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this.menuAccess = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      this.child = null;
      this.mapMultiplierList = null;
      this.searchQuery = '';
      this.searchFlag = false;
      ExternalInterface.call("close");
      if (this.titleWindow) {
        this.close();
      } else {
        window.close();
      }
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'dispose', this.errorLocation);
    }
  }

  private childScreenName = '';

  getParamsFromParent() {
    let stopRuleId = '';
    if (this.stopRulesGrid.selectedIndex > -1) {
      stopRuleId = this.stopRulesGrid.selectedItem.stopRuleId.content;
    }


    let params = [
      { screenName: this.childScreenName, stopRuleId: stopRuleId },
    ];
    return params;
  }



  doAddPCStopRule(event) {

    try {


      // this.win =  SwtPopUpManager.createPopUp(this, PCStopRulesAdd, {
      //         title: 'Stop Rules Add Details', // childTitle,
      //         screenName: 'add',
      //     });
      // this.win.enableResize = false;
      // this.win.width = '1120';
      // this.win.height = '750';
      // this.win.id = "stopRuleChildTitleWindow";
      // this.win.showControls = true;
      // this.win.onClose.subscribe((res) => {

      // });
      // this.win.display();

      this.childScreenName = 'add';
   
   
      // var newWindow = window.open("/stopRuleAdd", 'Stop Rule Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
      // if (window.focus) {
      //   newWindow.focus();
      // }

       ExternalInterface.call('openChildWindow', 'stopRuleAdd');

    } catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'doAddPCStopRule', this.errorLocation);
    }

  }



  doActivatePCStopRule() {


    // if (this.fourEyesRequired == true) {
    //   this.win = SwtPopUpManager.createPopUp(this, FourEyesProcess, {
    //     title: SwtUtil.getPredictMessage("label.fourEyes", null),
    //   });

    //   this.win.enableResize = false;
    //   this.win.width = '510';
    //   this.win.height = '215';
    //   this.win.showControls = true;
    //   this.win.isModal = true;
    //   this.win.onClose.subscribe((res) => {
    //     if (this.win.getChild().result) {
    //       if (this.win.getChild().result.login == "SUCCESS") {
    //         try {

    //           this.requestParams = [];
    //           this.requestParams["stopRuleId"] = this.stopRulesGrid.selectedItem.stopRuleId.content;
    //           this.requestParams["selectedStatus"] = this.statusGroup.selectedValue;
    //           this.requestParams["active"] = true;
    //           this.actionMethod = 'method=activateDesactive';
    //           this.actionPath = 'stopRulesPCM.do?';
    //           this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    //           this.inputData.send(this.requestParams);
        
    //           this.stopRulesGrid.selectedIndex = -1;
        
    //         } catch (error) {
    //           SwtUtil.logError(error, this.moduleId, 'ClassName', 'doActivatePCStopRule', this.errorLocation);
    //         }

    //       }
    //     }

    //   });
    //   this.win.display();
    // } else {
	    try {
        this.doUpdateRecord = true;
        this.requestParams = [];
        this.requestParams["stopRuleId"] = this.stopRulesGrid.selectedItem.stopRuleId.content;
        this.requestParams["selectedStatus"] = this.statusGroup.selectedValue;
        this.requestParams["active"] = true;
        this.actionMethod = 'method=activateDesactive';
        this.actionPath = 'stopRulesPCM.do?';
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);
  
        this.stopRulesGrid.selectedIndex = -1;
  
      } catch (error) {
        SwtUtil.logError(error, this.moduleId, 'ClassName', 'doActivatePCStopRule', this.errorLocation);
      }
   
    // }
    


  }

  doActivatePCStopRuleEventHandler(action) {
      if (action == "activate") {
        this.doActivatePCStopRule();
      } else {
        this.doDeactivateEventHanderPCStopRule();
      }


  }



  doDeactivateEventHanderPCStopRule() {

    var message: string = "When deactivated the attached Payments will be ";
      message+="'"+this.stopRulesGrid.selectedItem.actionOnDeactivation.content+"' . Do you want to continue?";
    this.swtAlert.confirm(message, null, Alert.YES | Alert.NO, null, this.doDeactivatePCStopRule.bind(this));

  }
  doDeactivatePCStopRule(event) {


    if (event.detail === Alert.YES) {


      // if (this.fourEyesRequired == true) {
      //   this.win = SwtPopUpManager.createPopUp(this, FourEyesProcess, {
      //     title: SwtUtil.getPredictMessage("label.fourEyes", null),
      //   });
  
      //   this.win.enableResize = false;
      //   this.win.width = '510';
      //   this.win.height = '215';
      //   this.win.showControls = true;
      //   this.win.isModal = true;
      //   this.win.onClose.subscribe((res) => {
      //     if (this.win.getChild().result) {
      //       if (this.win.getChild().result.login == "SUCCESS") {
      //         try {
      //           this.requestParams = [];
      //           this.requestParams["stopRuleId"] = this.stopRulesGrid.selectedItem.stopRuleId.content;
      //           this.requestParams["selectedStatus"] = this.statusGroup.selectedValue;
      //           this.requestParams["active"] = false;
      //           this.actionMethod = 'method=activateDesactive';
      //           this.actionPath = 'stopRulesPCM.do?';
      //           this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      //           this.inputData.send(this.requestParams);
      
      //           this.stopRulesGrid.selectedIndex = -1;
      //         } catch (error) {
      //           SwtUtil.logError(error, this.moduleId, 'ClassName', 'doDeactivatePCStopRule', this.errorLocation);
      //         }
  
      //       }
      //     }
  
      //   });
      //   this.win.display();
      // } else {
        try {
          this.doUpdateRecord = true;
          this.requestParams = [];
          this.requestParams["stopRuleId"] = this.stopRulesGrid.selectedItem.stopRuleId.content;
          this.requestParams["selectedStatus"] = this.statusGroup.selectedValue;
          this.requestParams["active"] = false;
          this.actionMethod = 'method=activateDesactive';
          this.actionPath = 'stopRulesPCM.do?';
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
          this.inputData.send(this.requestParams);

          this.stopRulesGrid.selectedIndex = -1;
        } catch (error) {
          SwtUtil.logError(error, this.moduleId, 'ClassName', 'doDeactivatePCStopRule', this.errorLocation);
        }
     
      }
      


      // }

    }

    doViewPCStopRule(event) {
      try {
        this.childScreenName = 'view';
    
        // var newWindow = window.open("/stopRuleView", 'Stop Rule Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
        // if (window.focus) {
        //   newWindow.focus();
        // }

        ExternalInterface.call('openChildWindow', 'stopRuleAdd');


      } catch (error) {
        SwtUtil.logError(error, this.moduleId, 'ClassName', 'doViewPCStopRule', this.errorLocation);
      }

    }
    doChangePCStopRule(event) {
      try {
        this.childScreenName = 'change';
       
        // var newWindow = window.open("/stopRuleAdd", 'Stop Rule Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
        // if (window.focus) {
        //   newWindow.focus();
        // }

        ExternalInterface.call('openChildWindow', 'stopRuleAdd');


      } catch (error) {
        SwtUtil.logError(error, this.moduleId, 'ClassName', 'doViewPCStopRule', this.errorLocation);
      }

    }



    doDeletePCStopRule(event) {

      if (parseInt(this.stopRulesGrid.selectedItem.numberOfLinkedPR.content) > 0) {
        this.swtAlert.warning('Cannot delete Stop Rule: Stop Rule has stopped Payment Request(s).')
      } else {
        try {
          this.swtAlert.confirm('Do you wish to delete this row', 'Alert', Alert.YES | Alert.NO, null, this.stopRuleRemoveHandler.bind(this));
        } catch (error) {
          SwtUtil.logError(error, this.moduleId, 'ClassName', 'doDeletePCStopRule', this.errorLocation);
        }
      }


   

    }

    stopRulesRowRemove() {
      // this.stopRulesGrid.removeSelected();
      this.enableDisableActivateButton(false);
      this.enableDisableDeleteButton(false);
      this.enableDisableDeactivateButton(false);
      this.enableDisableViewButton(false);

      this.doUpdateRecord = true;
      this.requestParams = [];
      this.requestParams["stopRuleId"] = this.stopRulesGrid.selectedItem.stopRuleId.content;
      this.actionMethod = 'method=delete';
      this.actionPath = 'stopRulesPCM.do?';
      this.requestParams["selectedStatus"] = this.statusGroup.selectedValue;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      this.stopRulesGrid.selectedIndex = -1;

    }

    stopRuleRemoveHandler(event) {
      if (event.detail === Alert.YES) {
        // if (this.fourEyesRequired == true) {
        //   this.win = SwtPopUpManager.createPopUp(this, FourEyesProcess, {
        //     title: SwtUtil.getPredictMessage("label.fourEyes", null),
        //   });

        //   this.win.enableResize = false;
        //   this.win.width = '510';
        //   this.win.height = '215';
        //   this.win.showControls = true;
        //   this.win.isModal = true;
        //   this.win.onClose.subscribe((res) => {
        //     if (this.win.getChild().result) {
        //       if (this.win.getChild().result.login == "SUCCESS") {
        //         this.stopRulesRowRemove();
        //       }
        //     }

        //   });
        //   this.win.display();
        // } else {
          this.stopRulesRowRemove();
        // }
      }
    }

    refreshGrid() {
      this.requestParams = [];
      this.doUpdateRecord = false;
      this.actionMethod = 'method=display';
      this.actionPath = 'stopRulesPCM.do?';
      this.requestParams["selectedStatus"] = this.statusGroup.selectedValue;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.stopRulesGrid.selectedIndex = -1;
      this.inputData.send(this.requestParams);
    }


    doHelp() {
      try {
        ExternalInterface.call("help");
      } catch (error) {
        SwtUtil.logError(error, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
      }

    }


  }

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: StopRules }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [StopRules],
  entryComponents: []
})
export class StopRulesModule { }



