import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { SwtModule, SwtTextArea, CommonService, SwtButton, SwtUtil } from 'swt-tool-box';

@Component({
  selector: 'app-multi-mvt-summary',
  templateUrl: './MultiMvtSummary.html',
  styleUrls: ['./MultiMvtSummary.css']
})
export class MultiMvtSummary  extends SwtModule implements OnInit {
  
  @ViewChild("summaryTxt") summaryTxt: SwtTextArea;
  @ViewChild("closeButton") closeButton: SwtButton;

  public summary;
  constructor(private element: ElementRef, private commonService: CommonService) {
    super(element, commonService);
  }

  ngOnInit() {
    this.closeButton.label = SwtUtil.getPredictMessage('multipleMvtActions.label.closeButton', null);
  }

  onLoad(){
  this.summaryTxt.text=this.summary;
  }

  removePopUp() {
    this.close();
  }

}
