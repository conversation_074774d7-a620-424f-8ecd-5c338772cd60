import {
  <PERSON>mponent,
  OnInit,
  OnD<PERSON>roy,
  ModuleWithProviders,
  NgModule,
  ElementRef,
  ViewChild,
  EventEmitter, Output
} from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, CommonService, SwtModule, Logger, SwtAlert, JSONReader, HTTPComms, CommonLogic, SwtCommonGrid, SwtButton, ExternalInterface, SwtUtil, SwtCanvas, ExportEvent, SwtText, DataExportMultiPage, HBox, SwtLoadingImage, SwtLabel, Encryptor, SwtCommonGridPagination, VBox, Keyboard, ScreenVersion, ContextMenuItem, SwtPopUpManager, JSONViewer } from 'swt-tool-box';
import { GenericJSONReader } from './GenericJSONReader';

declare var instanceElement: any;
declare var require: any;
const $ = require('jquery');

@Component({
  selector: 'app-generic-display',
  templateUrl: './GenericDisplay.html',
  styleUrls: ['./GenericDisplay.css']
})
export class GenericDisplay extends SwtModule implements OnInit, OnDestroy {


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Generic Display', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }
  @ViewChild('loadingImage', { read: SwtLoadingImage }) loadingImage: SwtLoadingImage;
  @ViewChild('dataGridContainer', { read: SwtCanvas }) dataGridContainer: SwtCanvas;
  @ViewChild('exportContainer') exportContainer: DataExportMultiPage;

  @ViewChild('buttonBox') buttonBox: HBox;
  @ViewChild('paginationData') paginationData: HBox;
  @ViewChild('numStepper', { read: SwtCommonGridPagination }) numStepper: SwtCommonGridPagination;

  @ViewChild('lastRefTime', { read: SwtText }) lastRefTime: SwtText;
  @ViewChild('lastRefTimeLabel', { read: SwtText }) lastRefTimeLabel: SwtText;

  @ViewChild('scenarioIdAndTitle', { read: SwtLabel }) scenarioIdAndTitle: SwtLabel;
  @ViewChild('page', { read: SwtLabel }) page: SwtLabel;
  // @ViewChild('pageLeft', { read: SwtLabel }) pageLeft: SwtLabel;

  private swtAlert: SwtAlert;
  private logger: Logger;
  public testJquery = $;
  /**
  * Data Objects
  **/
  //  public xmlReader: GenericXmlHandler = new GenericXmlHandler();
  // public jsonReader: JSONReader = new JSONReader();
  public jsonReader: GenericJSONReader = new GenericJSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  // Declaration to hold the initially received XML
  private initReceivedJSON;

  /**
   * Communication Objects
   **/
  public inputData: HTTPComms = new HTTPComms(this.commonService);
  public baseURL = "";
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];
  private genLogic: CommonLogic;
  // private showXML: ShowXML;
  private invalidComms = "";
  public baseQuery = "";
  public scenarioFacilityId = "";
  public scenarioId = "";
  public refColumns = "";
  public facilityRefColumns = "";
  public refParams = "";
  public facilityRefParams = "";
  public fromSummaryScreen = "";
  public additionalParams = "";
  public filter = "";
  public selectedCurrencyGroup = "";
  public applyCurrencyThreshold = "";
  

  // -- Screen Name and Version Number ---- 
  // private screenName: string = ExternalInterface.call('getBundle', 'text', 'label-genericDisplay', 'Generic Display - SMART Predict');
  // private versionNumber: string = "1.0";
  /* - START -- Screen Name and Version Number ---- */
  private screenName = 'Generic Display';
  private versionNumber = '1.0';
  private releaseDate = '31 January 2020';
  /* - END -- Screen Name and Version Number ---- */

  // Init the generic details grid
  private gGrid: SwtCommonGrid;


  // Buttons that are used for Generic Display screen 
  // private refreshButton: SwtButton = new SwtButton();
  // private goToButton: Button = new Button();
  // private closeButton: Button = new Button();
  @ViewChild('refreshButton', { read: SwtButton }) refreshButton: SwtButton;
  @ViewChild('goToButton', { read: SwtButton }) goToButton: SwtButton;
  @ViewChild('closeButton', { read: SwtButton }) closeButton: SwtButton;

  // numeric stepper for pagination
  // private numStepper: NumericStepper = new NumericStepper();
  // @ViewChild('numStepper', { read: NumericStepper }) numStepper: NumericStepper;

  // Used when refreshing the data (sorting or filtering) based on columns name 
  public columnNamesArr = [];

  private lastselectedIndex: Object = null;

  // Initializes currencyFontSize
  private currentFontSize = "";

  // Hold the columns width of the grid after resizing in order to update the received XML in Java part
  public columnsWidth = "";
  // Hold the columns order of the grid after reordering in order to update the received XML in Java part
  public columnsOrder = "";
  //
  private hostID = "";
  private entityID = "";
  private currencyCodeKey = "";
  private movementIdKey = "";
  private matchIdKey = "";
  private sweepIdKey = "";
  private previousVerticalScrollBarPosition = 0;
  private previousHorizontalScrollBarPosition = 0;
  public lastSortedIndex = -1;

  public screenVersion  = new ScreenVersion(this.commonService) ;
  private showXMLPopup: any;





  /**
   * Grid utility
   */


   /**
		 *
		 * This method is used to refresh grid data when click on sorting and filtering
		 * 
		 */

    public prevSortColumn="";
    private applySort = false;


/**
		 * Remember the sorted column when changing the column header sorting
		 * 
		 */

     private prevSelectedItem ;
     private prevColumnSort = new Object();

  ngOnInit() {
    instanceElement = this;

    this.refreshButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-refresh', 'Refresh window');
    this.refreshButton.label = ExternalInterface.call('getBundle', 'text', 'button-refresh', 'Refresh');
    this.goToButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-goto-tooltip', 'Go to');
    this.goToButton.label = ExternalInterface.call('getBundle', 'text', 'button-goto', 'Go to');
    this.closeButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-close', 'Close window');
    this.closeButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Close');

  }

  ngOnDestroy(): any {
    instanceElement = null;

  }

  /**
			 * The functon is used when the page loads initially the generic display screen
			 */
  onLoad(): void {
    // Variable Number Error Location
    let errorLocation = 0;

    try {
      errorLocation = 10;
      this.logger.info('method [onLoad] - START ');

      this.gGrid = this.dataGridContainer.addChild(SwtCommonGrid) as SwtCommonGrid;
      this.gGrid.clientSideSort = false;
      this.gGrid.clientSideFilter = false;

      this.gGrid.onSortChanged = this.rememberSortedColumn.bind(this);
      this.gGrid.onFilterChanged = this.filterCall.bind(this);
      this.gGrid.addEmptyNotEmptyFilter = true;

      this.gGrid.onPaginationChanged = this.numPager.bind(this);
      this.gGrid.paginationComponent = this.numStepper;
      this.numStepper.enabled = false;

      // Loads the GenLogic 
      this.genLogic = new CommonLogic();
      // Obtain the application base url http://[host]:[Port]//swallowtech by caling the obtainURL method
      this.baseURL = SwtUtil.getBaseURL();
      // Method to initialize the menu to be shown in the flex screen
      this.initializeMenus();
      // Add the event listener to listen for a cell click on a datagrid, be it the main one or a totals grid
      this.gGrid.ITEM_CLICK.subscribe((selectedRowData) => {
        this.cellLogic(selectedRowData);
      });

      /**
       * 
       */
      ExportEvent.subscribe((exportEvent) => {
      this.export(exportEvent.type, exportEvent.startPage, exportEvent.noOfPages);
      });

      this.exportContainer.exportCancelFunction = this.exportCancel.bind(this);
      // // add a call back which we can trigger the closePopup function from jsp part 
      // ExternalInterface.addCallback("closePopup", closePopup);
      // // add a call back which we can trigger the downloadError function from jsp part 
      // ExternalInterface.addCallback("downloadError", downloadError);
      // Get and stores the testDate from groupmonitorflex.jsp
      this.genLogic.testDate = ExternalInterface.call('eval', 'testDate');
      // Initialize the communication objects
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;

      // Displays the Refresh button
      this.refreshButton.click = (event) => {
        // this.dataRefresh(event);
      };

      // Displays the Refresh button

      // this.goToButton.addEventListener(MouseEvent.CLICK, goTo);
      this.goToButton.click = (event) => {
        // this.goTo(event);
      };
      this.goToButton.enabled = false;

       // this.closeButton.addEventListener(MouseEvent.CLICK, closeHandler);
      this.closeButton.click = (event) => {
        this.closeHandler(event);
      };

      // this.buttonBox.addElement(this.refreshButton);
      // this.buttonBox.addElement(this.closeButton);
      // this.buttonBox.addElement(this.goToButton);
      this.refreshButton.visible = true;
      this.closeButton.visible = true;
      this.goToButton.visible = true;

      // Fill the scenarioIdAndTitle label with the related elements come from jsp part
      this.scenarioIdAndTitle.text = ExternalInterface.call('eval', 'scenarioID') + " - " + ExternalInterface.call('eval', 'scenarioTitle');

      this.fromSummaryScreen = ExternalInterface.call('eval', 'fromSummaryScreen');
      if (this.fromSummaryScreen == 'true') {
        this.scenarioId = ExternalInterface.call('eval', 'scenarioID');
        this.filter = ExternalInterface.call('eval', 'filter');

        this.selectedCurrencyGroup = ExternalInterface.call('eval', 'selectedCurrencyGroup');
        this.applyCurrencyThreshold = ExternalInterface.call('eval', 'applyCurrencyThreshold');
        // Get the facility id of the scenario
        this.scenarioFacilityId = ExternalInterface.call('eval', 'facilityID');
        this.refColumns = ExternalInterface.call('eval', 'refColumns');
        this.facilityRefColumns = ExternalInterface.call('eval', 'facilityRefColumns');
        this.refParams = ExternalInterface.call('eval', 'refParams');
        this.facilityRefParams = ExternalInterface.call('eval', 'facilityRefParams');
        // Define the action to send the request
        this.actionPath = "genericdisplay.do?";
        this.actionMethod = "method=refreshGenericDisplayData";
        this.actionMethod += '&scenarioID=' + this.scenarioId;
        this.actionMethod += '&filter=' + this.filter;
        this.actionMethod += '&selectedCurrencyGroup=' + this.selectedCurrencyGroup;
        this.actionMethod += '&applyCurrencyThreshold=' + this.applyCurrencyThreshold;
        this.actionMethod += '&facilityId=' + this.scenarioFacilityId;
        this.actionMethod += '&refColumns=' + this.refColumns;
        this.actionMethod += '&facilityRefColumns=' + this.facilityRefColumns;
        this.actionMethod += '&refParams=' + this.refParams;
        this.actionMethod += '&facilityRefParams=' + this.facilityRefParams;
        this.actionMethod += '&fromSummaryScreen=' + this.fromSummaryScreen; // ExternalInterface.call('eval', 'fromSummaryScreen');



      } else {
        // The base query set by the user in the advanced details screen
        this.baseQuery = ExternalInterface.call('eval', 'basequery');
        /* We encounter a problem of encoding when sending the baseQuery parameter. So we have to encode it*/
        this.baseQuery = Encryptor.encode64(this.baseQuery);
        // Get the facility id of the scenario
        this.scenarioFacilityId = ExternalInterface.call('eval', 'facilityID');
        this.refColumns = ExternalInterface.call('eval', 'refColumns');
        this.facilityRefColumns = ExternalInterface.call('eval', 'facilityRefColumns');
        this.refParams = ExternalInterface.call('eval', 'refParams');
        this.facilityRefParams = ExternalInterface.call('eval', 'facilityRefParams');
        // Define the action to send the request
        this.actionPath = "genericdisplay.do?";
        this.actionMethod = "method=getGenericDisplayData";
        this.actionMethod += '&facilityId=' + this.scenarioFacilityId;
        this.actionMethod += '&refColumns=' + this.refColumns;
        this.actionMethod += '&facilityRefColumns=' + this.facilityRefColumns;
        this.actionMethod += '&refParams=' + this.refParams;
        this.actionMethod += '&facilityRefParams=' + this.facilityRefParams;


      }


      // Define the result format
      // inputData.resultFormat = "e4x";
      // Set the complete URL for inputData Http
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      // Make the request with requestParams if any
      this.requestParams["basequery"] = this.baseQuery;
      // this.inputData.method = "POST";
      this.inputData.send(this.requestParams);

      this.logger.info('method [onLoad] - END ');
    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, 'Predict', 'GenericDisplay.ts', 'onLoad', errorLocation);
      this.logger.error('GenericDisplay - method [onLoad] - error ', error);

    }
  }


  /**
  		 * The function initializes the menus in the right click event on the generic display monitor screen.
  		 * The links are redirected to their respective pages.
  		 */
  		initializeMenus(): void {
        this.screenVersion.loadScreenVersion(this, 'Generic Display', this.versionNumber, this.releaseDate);
        let addMenuItem: ContextMenuItem=null;
        addMenuItem=new ContextMenuItem(ExternalInterface.call('getBundle', 'text', 'label-showXML', 'Show JSON'));
        // add the listener to addMenuItem
        addMenuItem.MenuItemSelect = this.showXMLSelect.bind(this);
        this.screenVersion.svContextMenu.customItems.push(addMenuItem);
        this.contextMenu=this.screenVersion.svContextMenu;
  		}

  		/** 
  		 * This function is used to display the XML for the generic's data screen
  		 * @param event
  		 **/
  		showXMLSelect(even): void {
       
        this.showXMLPopup = SwtPopUpManager.createPopUp(this,
          JSONViewer,
          {
            jsonData: this.lastRecievedJSON,
        });
        this.showXMLPopup.width = "700";
        this.showXMLPopup.title = "Last Received JSON";
        this.showXMLPopup.height = "350";
        this.showXMLPopup.enableResize = false;
        this.showXMLPopup.showControls = true;
        this.showXMLPopup.display();
  		}

  /**
			 * Called when the user clicks on the data grid 
			 **/
  private cellLogic(event): void {
    this.logger.info('method [cellLogic] - START ');
    // Variable Number Error Location
    let errorLocation = 0;

    try {
      errorLocation = 10;
      // Get the facility id of the scenario
      let facilityID: string = ExternalInterface.call('eval', 'facilityID');
      errorLocation = 20;

      // event.stopPropagation();
      errorLocation = 30;
      // Enable the go to button only when the user selects a row also test the facility id of the scenario
      if (this.gGrid.selectedIndices.length == 1) {
        errorLocation = 40;
        // if the facility id of the screnario is 'None', 'MSD', 'GENERIC' or 'MATCH_DISPLAY_MANY',
        // we have to disable the button
        if (facilityID != ExternalInterface.call('eval', 'NoneFacility')
          && facilityID != ExternalInterface.call('eval', 'msdFacilityId')
          && facilityID != ExternalInterface.call('eval', 'genericFacilityId')
          && facilityID != ExternalInterface.call('eval', 'inputExceptionFacilityId')
          && facilityID != ExternalInterface.call('eval', 'matchDisplayManyFacilityId')) {

          this.goToButton.enabled = true;

        } else {
          this.goToButton.enabled = false;
        }
      } else {
        this.goToButton.enabled = false;
      }

      this.logger.info('method [cellLogic] - END ');
    } catch (error) {
      SwtUtil.logError(error, 'Predict', 'GenericDisplay.ts', 'cellLogic', errorLocation);
      this.logger.error('GenericDisplay - method [cellLogic] - error ', error);
    }
  }

  /**
			 * Event listener called when the user press on a key in the stepper
			 * event
			 **/
  keyDownPager(event): void {
    this.logger.info('method [keyDownPager] - START ');
    // Variable Number Error Location
    let errorLocation = 0;

    try {
      errorLocation = 10;
      // Send a request only when the user press on ENTER key
      if (event.charCode == Keyboard.ENTER) {
        if (this.jsonReader.getCurrent_Page() != this.numStepper.value && this.numStepper.value <= this.jsonReader.getMaxPages()) {
          let url = '&method=refreshGenericDisplayData';

          if (this.fromSummaryScreen == 'true') {
            url += '&scenarioID=' + ExternalInterface.call('eval', 'scenarioID');
            url += '&filter=' + ExternalInterface.call('eval', 'filter');
            url += '&selectedCurrencyGroup=' + ExternalInterface.call('eval', 'selectedCurrencyGroup');
            url += '&applyCurrencyThreshold=' + ExternalInterface.call('eval', 'applyCurrencyThreshold');
            url += '&fromSummaryScreen=' + this.fromSummaryScreen;
            url += '&refColumns=' + this.refColumns;
            url += '&facilityRefColumns=' + this.facilityRefColumns;
            url += '&refParams=' + this.refParams;
            url += '&facilityRefParams=' + this.facilityRefParams;
          } else {
            url += "&basequery=" + this.baseQuery;
          }

          url += "&selectedSort=" + this.jsonReader.getSelectedSort()
            + "&selectedFilter=" + this.jsonReader.getSelectedFilter()
            + "&maxPage=" + this.jsonReader.getMaxPages()
            + "&totalCount=" + this.jsonReader.getRowSize()
            + "&pageNoValue=" + this.numStepper.value
            + "&currentPage=" + this.jsonReader.getCurrent_Page()
            + "&columnsWidth=" + this.columnsWidth
            + "&columnsOrder=" + this.columnsOrder
            + "&refColumns=" + this.refColumns
            + "&facilityRefColumns=" + this.facilityRefColumns
            + "&refParams=" + this.refParams
            + "&facilityRefParams=" + this.facilityRefParams;


          this.inputData.url = this.baseURL + this.actionPath + url;
          // Make the request with requestParams if any
          this.inputData.send(this.requestParams);
        } else if (isNaN(this.numStepper.value)) {
          this.numStepper.value = Number(this.jsonReader.getCurrent_Page());
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-validPageNumber', 'Please enter a valid page number'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
        }
      }
      // Reset the horizontal and vertical scroll position 
      this.previousHorizontalScrollBarPosition = 0;
      this.previousVerticalScrollBarPosition = 0;
      this.logger.info('method [keyDownPager] - END ');
    } catch (error) {
      SwtUtil.logError(error, 'Predict', 'GenericDisplay.ts', 'keyDownPager', errorLocation);
      this.logger.error('GenericDisplay - method [keyDownPager] - error ', error);
    }
  }

  /**
			 * Event listener called when the user changes the stepper value by up down buttons
			 *
			 **/
  private numPager(event): void {
    this.logger.info('method [numPager] - START ');
    // Variable Number Error Location
    let errorLocation = 0;

    try {

      if (this.jsonReader.getCurrent_Page() != this.numStepper.value) {
        if (isNaN(this.numStepper.value)) {
          this.numStepper.value = Number(this.jsonReader.getCurrent_Page());
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-validPageNumber', 'Please enter a valid page number'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
        } else {
          let url = '&method=refreshGenericDisplayData';
          if (this.fromSummaryScreen == 'true') {
            url += '&scenarioID=' + ExternalInterface.call('eval', 'scenarioID');
            url += '&filter=' + ExternalInterface.call('eval', 'filter');
            url += '&selectedCurrencyGroup=' + ExternalInterface.call('eval', 'selectedCurrencyGroup');
            url += '&applyCurrencyThreshold=' + ExternalInterface.call('eval', 'applyCurrencyThreshold');
            url += '&fromSummaryScreen=' + this.fromSummaryScreen;
            url += '&refColumns=' + this.refColumns;
            url += '&facilityRefColumns=' + this.facilityRefColumns;
            url += '&refParams=' + this.refParams;
            url += '&facilityRefParams=' + this.facilityRefParams;
          } else {
            url += "&basequery=" + this.baseQuery;
          }

          url += "&selectedSort=" + this.jsonReader.getSelectedSort()
            + "&selectedFilter=" + this.jsonReader.getSelectedFilter()
            + "&maxPage=" + this.jsonReader.getMaxPages()
            + "&totalCount=" + this.jsonReader.getRowSize()
            + "&pageNoValue=" + this.numStepper.value
            + "&currentPage=" + this.jsonReader.getCurrent_Page()
            + "&columnsWidth=" + this.columnsWidth
            + "&columnsOrder=" + this.columnsOrder
            + "&refColumns=" + this.refColumns
            + "&facilityRefColumns=" + this.facilityRefColumns
            + "&refParams=" + this.refParams
            + "&facilityRefParams=" + this.facilityRefParams;
          this.inputData.url = this.baseURL + this.actionPath + url;
          this.inputData.send(this.requestParams);
        }
        // Reset the horizontal and vertical scroll position 
        this.previousHorizontalScrollBarPosition = 0;
        this.previousVerticalScrollBarPosition = 0;
      }
      this.logger.info('method [numPager] - END ');
    } catch (error) {
      SwtUtil.logError(error, 'Predict', 'GenericDisplay.ts', 'numPager', errorLocation);
      this.logger.error('GenericDisplay - method [numPager] - error ', error);
    }
  }

  /**
      * This function is used to load the main and the bottom grid on load of the screen.<br>
      *
      * @param event - ResultEvent
      */
  private inputDataResult(event): void {
    this.logger.info('method [inputDataResult] - START ');
    // Variable Number Error Location
    let errorLocation = 0;
    let sortDir = false;
    let sortDirection: string = null;
    let lastSelectedIndexFound = false;
    // Get the facility id of the scenario
    let facilityID: string = ExternalInterface.call('eval', 'facilityID');

    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        errorLocation = 10;
        this.lastRecievedJSON = event;
        errorLocation = 20;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        errorLocation = 30;

        errorLocation = 40;
        this.lastRefTimeLabel.visible = true;
        this.lastRefTime.visible = true;
        this.lastRefTimeLabel.text = SwtUtil.getPredictMessage('screen.lastRefresh', null); // ExternalInterface.call('getBundle', 'text', 'label-lastRefresh', 'Last Refresh:');
        this.lastRefTimeLabel.color = "black"; // .setStyle("color","black");
        this.lastRefTime.text = this.jsonReader.getScreenAttributes()["lastRefTime"];
        // Enabling export container
        if (this.jsonReader.getRowSize() != 0) {
          this.exportContainer.enabled = true;
        } else {
          this.exportContainer.enabled = false;
        }
        // this.exportContainer.addEventListener(ExportEvent.EXPORT_CLICK, export);
        // Test the reply status first, as if the reply status is false then the data will not be valid
        if (this.jsonReader.getRequestReplyStatus()) {
          errorLocation = 50;

          // If the previousXML is different to new xml then allow an update. Otherwise leave it alone
          if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {

            if (!this.jsonReader.isDataBuilding()) {
              errorLocation = 60;

              // this.dataGridContainer.removeAllElements();
              // this.paginationData.removeAllElements();

              this.exportContainer.maxPages=ExternalInterface.call('eval', 'exportMaxPages');
              this.exportContainer.totalPages=this.jsonReader.getMaxPages();
              this.exportContainer.currentPage = this.jsonReader.getCurrent_Page();

              if (this.gGrid != null && this.gGrid.selectedIndex != -1) {
                this.lastselectedIndex = this.gGrid.dataProvider[this.gGrid.selectedIndex];
              }
              if (this.gGrid != null && this.gGrid.sortColumnIndex != -1) {
                this.lastSortedIndex = this.gGrid.sortColumnIndex;
              }
              // Assigning the gGrid to appropriate container
              // gGrid = new GenericGrid(xmlReader.getColumnData(), baseURL);
              // dataGridContainer.addElement(gGrid);

              // Variable to put the metadata of header of datagrid
              let columnData = event.genericgrid.grid.metadata;
              if (window.opener && window.opener.instanceElement3) {
                window.opener.instanceElement3.sendColumns(columnData)
              }
              this.gGrid.CustomGrid(columnData);
              this.gGrid.saveColumnOrder = true;
              this.gGrid.saveWidths = true;
              this.gGrid.uniqueColumn = "generic";

              // this.gGrid.setListeners();

              // Set event listeners to handle cell click
              this.gGrid.gridData = this.jsonReader.getGridData();
              this.gGrid.setRowSize = this.jsonReader.getRowSize();
              // set additional params of the facility
              this.additionalParams = this.jsonReader.getScreenAttributes()["scenarioRefParams"];

              let selectedFilteredValueArr = [];

              let selectedFilteredValue: string = Encryptor.decode64(this.jsonReader.getSelectedFilter());
              this.numStepper.value = Number(this.jsonReader.getCurrent_Page());
              selectedFilteredValueArr = selectedFilteredValue.split('|');
              let selectedSort: string = Encryptor.decode64(this.jsonReader.getSelectedSort());
              let selectedSortColumn: string = selectedSort.substr(0, selectedSort.indexOf("|"));
              let colOrder = [];
              let colNames = [];
              let colHeading = [];

              let columnElement = this.jsonReader.getColumnData().column;

              for (let i = 0; i < columnElement.length; i++) {
                let pname: string = columnElement[i].dataelement;
                // var lstClientList = this.jsonReader.getSelects()['select'].find(x => x.id == 'lstClientList');
                let columnDetails = this.jsonReader.getColumnData().column.find(x => x.dataelement == pname);
                let num: string = columnDetails.columnNumber;
                let name: string = columnDetails.dataelement;
                let heading: string = columnDetails.heading;

                colOrder[pname] = num;
                colNames[pname] = name;
                colHeading[pname] = heading;

              }

              this.columnNamesArr = colNames;
              for (let sortColCounter = 0; sortColCounter < colOrder.length; sortColCounter++) {
                if ("\"" + colHeading[sortColCounter] + "\"" == selectedSortColumn) {
                  // this.gGrid.sortColumn = colHeading[sortColCounter];
                  this.gGrid.sortColumnIndex = sortColCounter;
                }
              }
              // FIXME:
              // // for resetting the Filtering array
              // this.gGrid.filteredColumns = new Array();
              // this.gGrid.selectedFilteredColumns = new Array();
              // if (selectedFilteredValue != "all") {
              //   for (var colCounter: number = 0; colCounter < this.columnNamesArr.length; colCounter++) {
              //     if (selectedFilteredValueArr[colCounter] != "All" && selectedFilteredValueArr[colCounter] != "all" && selectedFilteredValueArr[colCounter] != "") {
              //       var values;
              //       this.gGrid.filteredValue = selectedFilteredValueArr[colCounter];
              //       this.gGrid.filteredColumn = this.columnNamesArr[colCounter];
              //       values[this.gGrid.filteredColumn] = this.gGrid.filteredValue;
              //       this.gGrid.filteredColumns.push(values);
              //       this.gGrid.selectedFilteredColumns[this.gGrid.filteredColumn] = true;
              //       this.gGrid.isFiltered = true;
              //     }
              //   }
              // }
              // this.gGrid.refreshHeaders(this.gGrid.selectedFilteredColumns);
              // sortDirection = selectedSort.substr((selectedSort.indexOf("|") + 1), ((selectedSort.length) - 3));

              // if ((sortDirection == "ASC") || (sortDirection == "ASC|"))
              //   sortDir = false;
              // else
              //   sortDir = true;

              // this.gGrid.refreshHeadersforSortArrowGen(selectedSortColumn, sortDir, this.gGrid.selectedFilteredColumns, colHeading);
              let paginationXMLList = this.jsonReader.getPages();
              // For pagination, init the numeric stepper and its label if we have more than one page
              if (paginationXMLList.page && paginationXMLList.page.length > 0) {
                // var page: Label = new Label();
                // page.setStyle("paddingTop", 6);
                this.page.text = "            " + SwtUtil.getPredictMessage('genericDisplayMonitor.labelPage', null); // ExternalInterface.call('getBundle', 'text', 'label-page', 'days');
                this.page.fontWeight = "bold";
                // paginationData.addElement(page);
                this.numStepper.minimum = 1;
                this.numStepper.maximum = this.jsonReader.getMaxPages();
                this.numStepper.enabled = true;
                // FIXME:
                // this.numStepper.styleObject = "fontWeight: bold";
                // this.numStepper.addEventListener(MouseEvent.CLICK, numPager);
                // this.numStepper.addEventListener(KeyboardEvent.KEY_DOWN, keyDownPager);
                // this.numStepper.onPaginationChanged = this.doRefreshPage.bind(this);
                // this.numStepper.onPageChanged  KEY_DOWN.subscribe((selectedRowData) => {
                //   this.cellLogic(selectedRowData);
                // });
                // this.numStepper.width = 75;
                // paginationData.addElement(numStepper);
                // var pageLeft: Label = new Label();
                // pageLeft.setStyle("paddingTop", 6);
                // this.pageLeft.text = 'Of '/*SwtUtil.getPredictMessage('genericDisplayMonitor.labelOf', null) /*ExternalInterface.call('getBundle', 'text', 'label-of', 'of')*/ + this.jsonReader.getMaxPages();
                // paginationData.addElement(pageLeft);
              } else {
                this.page.text = "            " + SwtUtil.getPredictMessage('genericDisplayMonitor.labelPage', null); // ExternalInterface.call('getBundle', 'text', 'label-page', 'days');
                this.page.fontWeight = "bold";
                // paginationData.addElement(page);
                this.numStepper.minimum = 1;
                this.numStepper.maximum = 1;
                this.numStepper.enabled = false;
              }
            }
            // else {
            //   // Sets the data building image to visible
            //   this.dataBuildingText.visible = true;
            // }
            // Gets the font size from xml reader
            if (true/*ExternalInterface.available*/) {
              // Gets the current font size from the xmlReader
              this.currentFontSize = this.jsonReader.getFontSize();
            }
            // Sets the data grid style based on the font size
            if (this.currentFontSize == "Normal") {
              this.gGrid.styleName = "dataGridNormal";
              this.gGrid.rowHeight = 18;
            } else if (this.currentFontSize == "Small") {
              this.gGrid.styleName = "dataGridSmall";
              this.gGrid.rowHeight = 15;
            }

            // Sets the previous received xml
            // this.prevRecievedJSON = this.lastRecievedJSON;
          }

          if (this.initReceivedJSON == null && this.lastRecievedJSON != null) {
            this.initReceivedJSON = this.lastRecievedJSON;
          }

          if (this.lastselectedIndex != null) {
            for (let i = 0; i < this.gGrid.dataProvider.length; i++) {
              if (this.gGrid.dataProvider[i] == this.lastselectedIndex) {
                this.gGrid.selectedIndex = i;
                lastSelectedIndexFound = true;
                if (facilityID != ExternalInterface.call('eval', 'NoneFacility')
                  && facilityID != ExternalInterface.call('eval', 'msdFacilityId')
                  && facilityID != ExternalInterface.call('eval', 'genericFacilityId')
                  && facilityID != ExternalInterface.call('eval', 'matchDisplayManyFacilityId')) {

                  this.goToButton.enabled = true;

                } else {
                  this.goToButton.enabled = false;
                }
              }
            }

            if (!lastSelectedIndexFound) {
              this.gGrid.selectedIndex = -1;
              this.goToButton.enabled = false;
            }
          }

          // callLater(resetScrollBarPostions);
          this.resetScrollBarPostions();
        } else {
          // Alert to display the error and its location
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-contactAdmin', 'Error occurred, Please contact your System Administrator:') + "\n" + this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
        }
      }
    } catch (error) {
      SwtUtil.logError(error, 'Predict', 'GenericDisplay.ts', 'inputDataResult', errorLocation);
      this.logger.error('GenericDisplay - method [inputDataResult] - error ', error);
    }
  }


  /**
     * This function is used to load the main and the bottom grid on load of the screen.<br>
     *
     * @param event - ResultEvent
     */
  private pageDataResult(event): void {
    this.logger.info('method [pageDataResult] - START ');
    // Variable Number Error Location
    let errorLocation = 0;
    let sortDir = false;
    let sortDirection: string = null;
    let lastSelectedIndexFound = false;
    // Get the facility id of the scenario
    let facilityID: string = ExternalInterface.call('eval', 'facilityID');

    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        errorLocation = 10;
        this.lastRecievedJSON = event;
        errorLocation = 20;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        errorLocation = 30;

        errorLocation = 40;
        this.lastRefTimeLabel.visible = true;
        this.lastRefTime.visible = true;
        this.lastRefTimeLabel.text = SwtUtil.getPredictMessage('screen.lastRefresh', null); // ExternalInterface.call('getBundle', 'text', 'label-lastRefresh', 'Last Refresh:');
        this.lastRefTimeLabel.color = "black"; // .setStyle("color","black");
        this.lastRefTime.text = this.jsonReader.getScreenAttributes()["lastRefTime"];
        // Enabling export container
        if (this.jsonReader.getRowSize() != 0) {
          this.exportContainer.enabled = true;
        } else {
          this.exportContainer.enabled = false;
        }
        // Test the reply status first, as if the reply status is false then the data will not be valid
        if (this.jsonReader.getRequestReplyStatus()) {
          errorLocation = 50;

          // If the previousXML is different to new xml then allow an update. Otherwise leave it alone
          if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {

            if (!this.jsonReader.isDataBuilding()) {
              errorLocation = 60;

              if (this.gGrid != null && this.gGrid.selectedIndex != -1) {
                this.lastselectedIndex = this.gGrid.dataProvider[this.gGrid.selectedIndex];
              }
              if (this.gGrid != null && this.gGrid.sortColumnIndex != -1) {
                this.lastSortedIndex = this.gGrid.sortColumnIndex;
              }

              // Set event listeners to handle cell click
              this.gGrid.gridData = this.jsonReader.getGridData();
              this.gGrid.setRowSize = this.jsonReader.getRowSize();
              // set additional params of the facility
              this.additionalParams = this.jsonReader.getScreenAttributes()["scenarioRefParams"];

              let selectedFilteredValueArr = [];

              // var selectedFilteredValue: string = Encryptor.decode64(this.jsonReader.getSelectedFilter());
              this.numStepper.value = Number(this.jsonReader.getCurrent_Page());
              // selectedFilteredValueArr = selectedFilteredValue.split('|');
              // var selectedSort: string = Encryptor.decode64(this.jsonReader.getSelectedSort());
              // var selectedSortColumn: string = selectedSort.substr(0, selectedSort.indexOf("|"));
              // var colOrder = [];
              // var colNames = [];
              // var colHeading = [];

              // var columnElement = this.jsonReader.getColumnData().column;

              // for (var i = 0; i < columnElement.length; i++) {
              //   var pname: string = columnElement[i].dataelement;
              //   // var lstClientList = this.jsonReader.getSelects()['select'].find(x => x.id == 'lstClientList');
              //   var columnDetails = this.jsonReader.getColumnData().column.find(x => x.dataelement == pname);
              //   var num: string = columnDetails.columnNumber;
              //   var name: string = columnDetails.dataelement;
              //   var heading: string = columnDetails.heading;

              //   colOrder[pname] = num;
              //   colNames[pname] = name;
              //   colHeading[pname] = heading;

              // }

              // this.columnNamesArr = colNames;
              // for (var sortColCounter: number = 0; sortColCounter < colOrder.length; sortColCounter++) {
              //   if ("\"" + colHeading[sortColCounter] + "\"" == selectedSortColumn) {
              //     // this.gGrid.sortColumn = colHeading[sortColCounter];
              //     this.gGrid.sortColumnIndex = sortColCounter;
              //   }
              // }

              // this.gGrid.refreshHeadersforSortArrowGen(selectedSortColumn, sortDir, this.gGrid.selectedFilteredColumns, colHeading);
              let paginationXMLList = this.jsonReader.getPages();
              // For pagination, init the numeric stepper and its label if we have more than one page
              if (paginationXMLList.page.length > 0) {
                // var page: Label = new Label();
                // page.setStyle("paddingTop", 6);
                this.page.text = "            " + SwtUtil.getPredictMessage('genericDisplayMonitor.labelPage', null); // ExternalInterface.call('getBundle', 'text', 'label-page', 'days');
                this.page.fontWeight = "bold";
                // paginationData.addElement(page);
                this.numStepper.minimum = 1;
                this.numStepper.maximum = this.jsonReader.getMaxPages();
                this.numStepper.enabled = true;
                // this.numStepper.styleObject = "fontWeight: bold";
                // this.numStepper.addEventListener(MouseEvent.CLICK, numPager);
                // this.numStepper.addEventListener(KeyboardEvent.KEY_DOWN, keyDownPager);
                // this.numStepper.onPaginationChanged = this.doRefreshPage.bind(this);
                // this.numStepper.onPageChanged  KEY_DOWN.subscribe((selectedRowData) => {
                //   this.cellLogic(selectedRowData);
                // });
                // this.numStepper.width = 75;
                // paginationData.addElement(numStepper);
                // var pageLeft: Label = new Label();
                // pageLeft.setStyle("paddingTop", 6);
                // this.pageLeft.text = 'Of '/*SwtUtil.getPredictMessage('genericDisplayMonitor.labelOf', null) /*ExternalInterface.call('getBundle', 'text', 'label-of', 'of')*/ + this.jsonReader.getMaxPages();
                // paginationData.addElement(pageLeft);
              }
            }

          }

          if (this.initReceivedJSON == null && this.lastRecievedJSON != null) {
            this.initReceivedJSON = this.lastRecievedJSON;
          }

          if (this.lastselectedIndex != null) {
            for (let i = 0; i < this.gGrid.dataProvider.length; i++) {
              if (this.gGrid.dataProvider[i] == this.lastselectedIndex) {
                this.gGrid.selectedIndex = i;
                lastSelectedIndexFound = true;
                if (facilityID != ExternalInterface.call('eval', 'NoneFacility')
                  && facilityID != ExternalInterface.call('eval', 'msdFacilityId')
                  && facilityID != ExternalInterface.call('eval', 'genericFacilityId')
                  && facilityID != ExternalInterface.call('eval', 'matchDisplayManyFacilityId')) {

                  this.goToButton.enabled = true;

                } else {
                  this.goToButton.enabled = false;
                }
              }
            }

            if (!lastSelectedIndexFound) {
              this.gGrid.selectedIndex = -1;
              this.goToButton.enabled = false;
            }
          }

          // callLater(resetScrollBarPostions);
          this.resetScrollBarPostions();
        } else {
          // Alert to display the error and its location
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-contactAdmin', 'Error occurred, Please contact your System Administrator:') + "\n" + this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
        }
      }
    } catch (error) {
      SwtUtil.logError(error, 'Predict', 'GenericDisplay.ts', 'pageDataResult', errorLocation);
      this.logger.error('GenericDisplay - method [pageDataResult] - error ', error);
    }
  }

  /**
	 * This function is called to reset scroll bar postions 
	 * */
  private resetScrollBarPostions(): void {

    // this.gGrid.verticalScrollPosition = this.previousVerticalScrollBarPosition;
    // this.gGrid.horizontalScrollPosition = this.previousHorizontalScrollBarPosition;						
  }

  /**
	* This function is called when clicking refresh button
	* @param event
	**/
  dataRefresh(event, cancelExport: boolean = false): void {
    this.actionPath = "genericdisplay.do?";
    this.actionMethod = "method=refreshGenericDisplayData";
    if (this.fromSummaryScreen == 'true') {
      this.actionMethod += '&scenarioID=' + ExternalInterface.call('eval', 'scenarioID');
      this.actionMethod += '&filter=' + ExternalInterface.call('eval', 'filter');
      this.actionMethod += '&selectedCurrencyGroup=' + ExternalInterface.call('eval', 'selectedCurrencyGroup');
      this.actionMethod += '&applyCurrencyThreshold=' + ExternalInterface.call('eval', 'applyCurrencyThreshold');
      this.actionMethod += '&fromSummaryScreen=' + this.fromSummaryScreen;
    } else {
      this.actionMethod += "&basequery=" + this.baseQuery;
    }

    this.actionMethod += "&currentPage=" + this.jsonReader.getCurrent_Page();

    this.actionMethod += '&selectedSort=' + this.jsonReader.getSelectedSort();
    this.actionMethod += '&selectedFilter=' + this.jsonReader.getSelectedFilter();

    this.actionMethod += "&maxPage=" + this.jsonReader.getMaxPages();
    this.actionMethod += "&totalCount=" + this.jsonReader.getRowSize();
    this.actionMethod += "&columnsWidth=" + this.columnsWidth;
    this.actionMethod += "&columnsOrder=" + this.columnsOrder;
    this.actionMethod += "&cancelExport=" + cancelExport;
    this.actionMethod += '&facilityId=' + this.scenarioFacilityId;
    this.actionMethod += '&refColumns=' + this.refColumns;
    this.actionMethod += '&facilityRefColumns=' + this.facilityRefColumns;
    this.actionMethod += '&refParams=' + this.refParams;
    this.actionMethod += '&facilityRefParams=' + this.facilityRefParams;
    // Framing final url
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    // Sending request
    this.inputData.send(this.requestParams);

    // Save previous horizontal and vertical scrollBar position
    // this.previousHorizontalScrollBarPosition = this.gGrid.horizontalScrollPosition;
    // this.previousVerticalScrollBarPosition = this.gGrid.verticalScrollPosition;
  }


  /**
			 * Fault event handler for inputData HTTPComms
			 * @param event
			 **/
  private inputDataFault(event): void {
    this.lastRefTime.visible = false;
    this.lastRefTimeLabel.text = ExternalInterface.call('getBundle', 'text', 'label-connectionError', 'CONNECTION ERROR');
    this.lastRefTimeLabel.color = 'red'; // .setStyle("color", "Red");
    this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  private startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.disableInterface();
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  private endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.enableInterface();
  }

  /**
   * Disable interface, turn off certain UI elements when a request is made to the server
   **/
  private disableInterface(): void {
    // numStepper.enabled = false;
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
  }

  /**
   * Enable interface, turn on certain UI elements when a request is made to the server
   **/
  private enableInterface(): void {
    // numStepper.enabled = true;
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
  }

  /**
   * Function called to close the window when close button is clicked
   * @param event
   **/
  closeHandler(event): void {
    ExternalInterface.call("close");
  }

  /**
   * This function is invoked when export button is clicked,
   * used to export the grid data.
   *
   * @param reportType: String - Report type CSV/Excel/PDF
   * @param startPage: int - Start page no
   * @param noOfPages: int - No of pages to be exported
   **/
  export(reportType: string, startPage: number, noOfPages: number): void {
    let selectedFilter: string = this.jsonReader.getSelectedFilter().replace(/&/g, "amp;").replace(/\+/g, "plus;");
    let selectedSort: string = this.jsonReader.getSelectedSort();
    // Call the javascript function 'onExport' with required parameters
    ExternalInterface.call('onExport', this.baseQuery, selectedFilter, selectedSort, reportType, noOfPages, startPage, this.filter, this.fromSummaryScreen, this.selectedCurrencyGroup, this.applyCurrencyThreshold);
  }

  /**
   * This function is used to close the pop up window
   **/
  public closePopup(): void {
    this.exportContainer.closeCancelPopup();
  }

  /**
   * This function is used to display an alert message for the user when an error occurs on server side.
   **/
  public downloadError(): void {
    this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-errorServerSide', 'SERVER SIDE ERROR'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
  }

  /**
			 * This function is used to call the data refresh, whenever click on cancel button
			 **/
  private exportCancel(event): void {
    
  this.dataRefresh(event, true);
  }

  /**
   * Called when the user press on help icon
   **/
  public helphandler(): void {
    ExternalInterface.call("help");
  }

  goTo(event): void {

    // Set the parameters which will be sent to open the facility screen after clicking the go to button
    let columnsXML = this.jsonReader.getColumnData();
    let hostIDAlias: string;
    let entityIDAlias: string;
    let currencyCodeKeyAlias: string;
    let movementIdKeyAlias: string;
    let matchIdKeyAlias: string;
    let sweepIdKeyAlias: string;

    hostIDAlias = columnsXML.column.find(x => x.heading == "HOST_ID").dataelement;
    this.hostID = this.gGrid.dataProvider[this.gGrid.selectedIndex][hostIDAlias];
    if (columnsXML.column.find(x => x.heading == "ENTITY_ID") != undefined) {
      entityIDAlias = columnsXML.column.find(x => x.heading == "ENTITY_ID").dataelement;
      this.entityID = this.gGrid.dataProvider[this.gGrid.selectedIndex][entityIDAlias];
    }
    if (columnsXML.column.find(x => x.heading == "CURRENCY_CODE") != undefined) {
      currencyCodeKeyAlias = columnsXML.column.find(x => x.heading == "CURRENCY_CODE").dataelement;
      this.currencyCodeKey = this.gGrid.dataProvider[this.gGrid.selectedIndex][currencyCodeKeyAlias];
    }
    if (columnsXML.column.find(x => x.heading == "MOVEMENT_ID") != undefined) {
      movementIdKeyAlias = columnsXML.column.find(x => x.heading == "MOVEMENT_ID").dataelement;
      this.movementIdKey = this.gGrid.dataProvider[this.gGrid.selectedIndex][movementIdKeyAlias];
    }
    if (columnsXML.column.find(x => x.heading == "MATCH_ID") != undefined) {
      matchIdKeyAlias = columnsXML.column.find(x => x.heading == "MATCH_ID").dataelement;
      this.matchIdKey = this.gGrid.dataProvider[this.gGrid.selectedIndex][matchIdKeyAlias];
    }
    if (columnsXML.column.find(x => x.heading == "SWEEP_ID") != undefined) {
      sweepIdKeyAlias = columnsXML.column.find(x => x.heading == "SWEEP_ID").dataelement;
      this.sweepIdKey = this.gGrid.dataProvider[this.gGrid.selectedIndex][sweepIdKeyAlias];
    }

    ExternalInterface.call("goTo", this.hostID, this.entityID, this.matchIdKey, this.currencyCodeKey, this.movementIdKey, this.sweepIdKey, this.additionalParams);
  }
    
		dataRefreshFilterAndSort(): void {
			// String Variable to hold empty action method 
			let actionMethod="";
			// String Variable to hold access from xmlReader
			let access: string=this.jsonReader.getScreenAttributes()["access"];
			// String Variable to hold actionPath 
			let actionPath="genericdisplay.do?";
			let colOrder = [];
			let colNames = [];
			let colTypes = [];
			let columnsTmp = [];
   let columnTemp ;
      
			for (let i = 0 ; i < this.jsonReader.getColumnData().column.length; i++) {

        columnTemp =  this.jsonReader.getColumnData().column[i];
				    let num: string=columnTemp["columnNumber"];
				    let name: string=columnTemp["heading"];
				    let type: string=columnTemp["type"];
				    let dataelement: string=columnTemp["dataelement"];
				    colOrder[name]=num;
				    colNames[name]=name;
        colTypes[name]=type;
        columnsTmp[dataelement] = {num, name , type};
      }
      
			let prevSelectedSort: string=Encryptor.decode64(this.jsonReader.getSelectedSort());

			// String Variable to hold empty selectedSort
			let selectedSort="";
			// creating column sort grid
			if(this.applySort) {
				for (let key in colNames) {
          // FIXME: check this.gGrid.sortedGridColumn
					if (key == this.gGrid.sortedGridColumn) {
						selectedSort+=key;
						if (this.gGrid.sortColumnIndex == this.lastSortedIndex) {	
							if (prevSelectedSort.indexOf("|DESC|") > 0) {
								selectedSort="\""+selectedSort+"\""+"|ASC|";
							} else {
								selectedSort="\""+selectedSort+"\""+"|DESC|";
							}
						} else {
							selectedSort="\""+selectedSort+"\""+"|DESC|";
						}
					}
				}
			} else {
        selectedSort = prevSelectedSort;
			}			

        
			// String Variable to hold empty selectedFilter
			let selectedFilter="";
			// Object Variable to hold filterObject
			let filterObject: string;
   let filterFlag=false;
      // FIXME:Check is correct
			let filteredValue="";
      // creating  column filter grid
      

      // for (var key in colNames) {
      //   //FIXME: check this.gGrid.sortedGridColumn
      //   if (key == this.gGrid.sortedGridColumn)
      //   {
      //     selectedSort+=key;
      //     if (this.gGrid.sortColumnIndex == this.lastSortedIndex)
      //     {	
      //       if (prevSelectedSort.indexOf("|DESC|") > 0)
      //       {
      //         selectedSort="\""+selectedSort+"\""+"|ASC|";
      //       }
      //       else
      //       {
      //         selectedSort="\""+selectedSort+"\""+"|DESC|";
      //       }
      //     }else{
      //       selectedSort="\""+selectedSort+"\""+"|DESC|";
      //     }
      //   }
      // }

      

      // let colCounter :number = 0;
   selectedFilter = "";
   for (let column in columnsTmp) {
        if(this.gGrid.getCurrentFilter()[column] !== undefined) {
          filteredValue = this.gGrid.getCurrentFilter()[column];
          if(!filteredValue) {
            filteredValue = "(EMPTY)";
          } else {
            if(typeof(filteredValue) == "string") {
              selectedFilter+="\""+columnsTmp[column].name+"\"$#$"+filteredValue.replace(/&/g, "amp;").replace(/\+/g, "plus;") + "$#$"+columnsTmp[column].type+"|";
            }
          }
        } else {
          filteredValue = null;
          selectedFilter+="All|";
        }
      }
      

      // for (var key in this.columnNamesArr) {
      //   filterFlag=false;
			// 	for (var filterCounter:number=0; filterCounter < this.gGrid.filteredGridColumns.length; filterCounter++)
			// 	{
			// 		for (filterObject in this.gGrid.filteredGridColumns[filterCounter])
			// 		{
			// 			filteredColumn=(filterObject as String);
			// 			filteredValue=(filteredColumns[filterCounter])[filterObject];
			// 			if (parentApplication.columnNamesArr[colCounter] == filteredColumn)
			// 			{
			// 				filterFlag=true;
			// 				break;
			// 			}
			// 		}
			// 		if (filterFlag)
			// 			break;
			// 	}
			// 	if (filterFlag)
			// 	{
			// 		if (filteredValue.split("$#$").length>1)
			// 			filteredValue=filteredValue.split("$#$")[1];
			// 		if(filteredValue==null ||filteredValue.length==0)
			// 			filteredValue = "(EMPTY)";
			// 		selectedFilter+="\""+colNames[colCounter]+"\"$#$"+filteredValue.replace(/&/g, "amp;").replace(/\+/g, "plus;") + "$#$"+colTypes[colCounter]+"|";
			// 	}
			// 	else
			// 		selectedFilter+="All|";



      //     colCounter++;

      // }
			for (let colCounter = 0; colCounter < this.columnNamesArr.length; colCounter++) {        
        filterFlag=false;
        // FIXME:this.gGrid.filteredGridColumns check
				    for (let  filterCounter=0; filterCounter < this.gGrid.currentFilterColumns.length; filterCounter++) {
					for (filterObject in this.gGrid.currentFilterColumns[filterCounter]) {
						this.gGrid.FilteredColumn=(filterObject as String);
						filteredValue=(this.gGrid.currentFilterColumns[filterCounter])[filterObject];
						if (this.columnNamesArr[colCounter] == this.gGrid.FilteredColumn) {
							filterFlag=true;
							break;
						}
					}
					if (filterFlag) {
						break;
					}
				}
				    if (filterFlag) {
					if (filteredValue.split("$#$").length>1) {
						filteredValue=filteredValue.split("$#$")[1];
					}
					if(filteredValue==null ||filteredValue.length==0) {
						filteredValue = "(EMPTY)";
					}
					selectedFilter+="\""+colNames[colCounter]+"\"$#$"+filteredValue.replace(/&/g, "amp;").replace(/\+/g, "plus;") + "$#$"+colTypes[colCounter]+"|";
				} else {
					selectedFilter+="All|";
								}
			}
			selectedFilter = Encryptor.encode64(selectedFilter);
			selectedSort = Encryptor.encode64(selectedSort);
			// Assign url value in actionMethod
			actionMethod+="method=refreshGenericDisplayData";
			if(this.fromSummaryScreen!=null && this.fromSummaryScreen=='true') {	
				actionMethod+='&scenarioID='+this.scenarioId;
				actionMethod+='&filter='+ this.filter;
				actionMethod+='&selectedCurrencyGroup='+ this.selectedCurrencyGroup;
				actionMethod+='&applyCurrencyThreshold='+ this.applyCurrencyThreshold;
				actionMethod+='&fromSummaryScreen='+  this.fromSummaryScreen;
			} else { 
				actionMethod+="&basequery=" + this.baseQuery;
			}
			
			actionMethod+="&maxPage=" + this.jsonReader.getRowSize();
			actionMethod+="&currentPage=1";
			actionMethod+="&selectedSort=" + selectedSort;
			actionMethod+="&selectedFilter=" + selectedFilter.substring(0, (selectedFilter.length - 1));
			actionMethod+="&totalCount=" + this.jsonReader.getRowSize();
			actionMethod+="&columnsWidth="+ this.columnsWidth;
			actionMethod+="&columnsOrder="+ this.columnsOrder;
			actionMethod+='&refColumns='+this.refColumns;
			actionMethod+='&facilityRefColumns='+this.facilityRefColumns;
			actionMethod+='&refParams='+this.refParams;
			actionMethod+='&facilityRefParams='+this.facilityRefParams;
			this.inputData.url=this.baseURL + actionPath + actionMethod;
			let requestParams = [];
			this.inputData.send(requestParams);
		}
		rememberSortedColumn(): void {
      
      // FIXME: Check the hide column
			// if (!ctrlKeyDown)
			// {
				
				if ((this.gGrid.selectedIndex != -1) && (this.gGrid.selectable == true)) {
					if (this.gGrid.uniqueColumn == "") {
            this.prevSelectedItem.id=null;
					} else {
            this.prevSelectedItem.id=this.gGrid.selectedItem[this.gGrid.uniqueColumn];
					}
				}
				
				this.gGrid.columns[this.gGrid.sortColumnIndex]['sortDescending']=!(this.gGrid.columns[this.gGrid.sortColumnIndex]['sortDescending']);
				this.prevColumnSort["ColumnName"]=this.gGrid.columns[this.gGrid.sortColumnIndex]['name'];
				this.prevColumnSort["Direction"]=this.gGrid.columns[this.gGrid.sortColumnIndex]['sortDescending'];
				this.prevColumnSort["Numeric"]=this.gGrid.columns[this.gGrid.sortColumnIndex]['columnType'] == "num";
    this.prevColumnSort["PrevCol"]=this.gGrid.sortColumnIndex;

        
        // FIXME:CHECK FILTER WORK WITH SORT
				// var filteredXML:XMLList=(new XMLList(dataProvider));
				// if (this.gGrid.isFiltered)
				// 	filteredXML=filterData(filteredXML);
				this.gGrid.sortedGridColumn=this.gGrid.columns[this.gGrid.sortColumnIndex]['name'];
				if (this.gGrid.columns[this.gGrid.sortColumnIndex]['sortable'] != false) {	
						this.applySort = true;
						this.dataRefreshFilterAndSort();
						
					}
			// }
			// else
			// 	createMenu();
    }
    

    /**
		 * This method is used to Filter the cell based on the filter value & sort value, 
		 * frame the filter and sort string, then send to server.
		 *
		 * @param event.
		 */
		filterCall(event): void {
      
			// // stop the immediate Propagation.
			// var values = [];
			// // check selected value is all. 
      // var i:number;
      
			// if ((event.comboDTO["target"] as ComboBox).selectedLabel == "(ALL)")
			// {
			// 	// Assign the current filtered column data field.
			// 	filteredColumn=event.comboDTO["datafield"];
			// 	// set the filteredValue as All, because user not selected value in filter.
			// 	filteredValue="All";
			// 	selectedFilteredColumns[filteredColumn]=false;
			// 	// checks the filtered columns array, if already exists then removed from filtered columns arrays. 
			// 	for (i=0; i < filteredColumns.length; i++)
			// 	{
			// 		if (filteredColumns[i].hasOwnProperty(filteredColumn))
			// 		{
			// 			filteredColumns.splice(i, 1);
			// 			break;
			// 		}
			// 	}
			// 	// check already filterd.
			// 	if (filteredColumns.length < 1)
			// 		isFiltered=false;
			// }
			// else
			// { 	//user picks value
			// 	var alreadyFilter:Boolean=false;
			// 	// checks the current filtered column already exist in Filtered columns array.
			// 	for (i=0; i < filteredColumns.length; i++)
			// 	{
			// 		var filterVal:Array=filteredColumns[i];
			// 		// if user select value add the filtervalue in existing Filter.
			// 		filteredColumn=event.comboDTO["datafield"];
			// 		for (var j:Object in filterVal)
			// 		{
			// 			// Compare current filter column and existing column to check already filtered or not.
			// 			if (((j as String) == filteredColumn) && (filterVal[j] == (event.comboDTO["target"] as ComboBox).selectedLabel))
			// 				alreadyFilter=true;
			// 		}
			// 	}
			// 	// if not already filtered then added to the filtered columns arrays.
			// 	if (!alreadyFilter)
			// 	{
			// 		filteredValue=(event.comboDTO["target"] as ComboBox).selectedLabel;
			// 		filteredColumn=event.comboDTO["datafield"];
			// 		for (var k:int=0; k < filteredColumns.length; k++)
			// 		{
			// 			if (filteredColumns[k].hasOwnProperty(filteredColumn))
			// 			{
			// 				filteredColumns.splice(k, 1);
			// 				break;
			// 			}
			// 		}
			// 		values[filteredColumn]=filteredValue;
			// 		filteredColumns.push(values);
			// 		selectedFilteredColumns[filteredColumn]=true;
			// 		isFiltered=true;
			// 	}
			// }
			// to load the filtered data.
			   this.dataRefreshFilterAndSort();
}

  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,"Predict", 'GenericDisplay', 'doHelp', 0);
    }
  }

}


// Define lazy loading routes
const routes: Routes = [
  { path: '', component: GenericDisplay }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [GenericDisplay],
  entryComponents: []
})
export class GenericDisplayModule { }
