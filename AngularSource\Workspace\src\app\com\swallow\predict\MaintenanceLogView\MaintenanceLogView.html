<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingTop="5">
    <SwtCanvas width="100%" height="110" minWidth="640">
      <Grid width="100%" height="100%">
        <GridRow width="100%" height="20">
          <GridItem width="140">
            <SwtLabel id="facilityLbl" #facilityLbl></SwtLabel>
          </GridItem>
          <GridItem width="220">
            <SwtLabel id="facilityVal" #facilityVal fontWeight="normal">
            </SwtLabel>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="20">
          <GridItem width="140">
            <SwtLabel id="dateLbl" #dateLbl></SwtLabel>
          </GridItem>
          <GridItem width="250">
            <SwtLabel id="dateVal" #dateVal fontWeight="normal">
            </SwtLabel>
          </GridItem>

          <GridItem width="100">
            <SwtLabel id="ipAddressLbl" #ipAddressLbl></SwtLabel>
          </GridItem>
          <GridItem width="220">
            <SwtLabel id="ipAddressVal" #ipAddressVal fontWeight="normal">
            </SwtLabel>
          </GridItem>

        </GridRow>
        
        <GridRow width="100%" height="20">
          <GridItem width="140">
            <SwtLabel id="userLbl" #userLbl></SwtLabel>
          </GridItem>
          <GridItem width="100">
            <SwtLabel id="userIdVal" #userIdVal fontWeight="normal">
            </SwtLabel>
          </GridItem>
          <GridItem width="220">
            <SwtLabel id="userNameVal" #userNameVal fontWeight="normal">
            </SwtLabel>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="20">
          <GridItem width="140">
            <SwtLabel id="recordRefLbl" #recordRefLbl></SwtLabel>
          </GridItem>
          <GridItem width="220">
            <SwtLabel id="recordRefVal" #recordRefVal fontWeight="normal">
            </SwtLabel>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="20">
          <GridItem width="140">
            <SwtLabel id="actionLbl" #actionLbl></SwtLabel>
          </GridItem>
          <GridItem width="220">
            <SwtLabel id="actionVal" #actionVal fontWeight="normal">
            </SwtLabel>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>

    
      <VDividedBox height="100%" width="100%" #vDivider minHeight="250">
        <SwtCanvas class="top" width="100%" height="50%" minWidth="640">
          <SwtCanvas #viewLogGridContainer id="viewLogGridContainer" styleName="canvasWithGreyBorder" width="100%"
            border="false" height="100%" width="100%"></SwtCanvas>
        </SwtCanvas>
        <SwtCanvas width="100%" height="50%" class="bottom" minWidth="640">
          <VBox width="100%" height="100%">
            <HBox width="100%" height="20">
              <SwtLabel id="fullDetailsLbl" #fullDetailsLbl></SwtLabel>
            </HBox>
            <HBox width="100%" height="20">
              <SwtLabel id="oldValLbl" #oldValLbl fontWeight="normal" width="49%"></SwtLabel>
              <SwtLabel id="newValLbl" #newValLbl fontWeight="normal" width="51%"></SwtLabel>
            </HBox>
      
            <div class="row" height="100%">
              <div class="col-md-12 editor editor-size" height="100%">
            <ngx-monaco-diff-editor id="diffeditor" [options]="diffOptions" [originalModel]="originalModel"
                  [modifiedModel]="modifiedModel" style="height: 100%;"></ngx-monaco-diff-editor>
          </div>
        </div>
          </VBox>
    </SwtCanvas>
      </VDividedBox>
    
    <SwtCanvas width="100%" height="35">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
        </HBox>
        <HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>

      </HBox>
    </SwtCanvas>

  </VBox>
</SwtModule>