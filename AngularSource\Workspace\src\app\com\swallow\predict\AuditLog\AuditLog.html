<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" minWidth="570">
      <Grid width="100%" height="100%" paddingLeft="5" paddingRight="5">
        <GridRow width="100%" height="25" #parentGrid>
          <GridItem width="65%">
            <GridItem width="200">
              <GridItem width="70">
                <SwtLabel id="user" #user></SwtLabel>
              </GridItem>
              <GridItem>
                <SwtComboBox id="userCombo" #userCombo width="150" (change)="updateData('true')" 
                  dataLabel="userList"> </SwtComboBox>
              </GridItem>
            </GridItem>
            <GridItem paddingLeft="50">
              <SwtLabel id="userDesc" #userDesc fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="100%">
            <HBox #pageBox horizontalAlign="right" width="100%" visible="false">
              <SwtCommonGridPagination #numstepper></SwtCommonGridPagination>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow width="100%" height="25">
          <GridItem width="70">
            <SwtLabel #startDateLabel id="startDateLabel" styleName="labelBold"></SwtLabel>
          </GridItem>
          <GridItem paddingRight="70">
            <SwtDateField id="fromDateChooser" #fromDateChooser (change)="validateDateField(fromDateChooser)" 
              toolTip="Enter from Date" width="60"></SwtDateField>
          </GridItem>
          <GridItem width="50" paddingLeft="20">
            <SwtLabel #endDateLabel id="endDateLabel" styleName="labelBold"></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtDateField id="toDateChooser" #toDateChooser (change)="validateDateField(toDateChooser)"
              toolTip="Enter to Date" width="60"></SwtDateField>
          </GridItem>
          
          <!-- Adjusted GridItem to take available space and align right -->
          <GridItem  width="100%" #mvmtParent visible="false" includeInLayout="false">
            <HBox horizontalAlign="right" width="100%" paddingRight="10" marginTop="5">
              <SwtLabel id="movIdLabel" #movIdLabel></SwtLabel>
              <SwtLabel id="mvmtId" #mvmtId fontWeight="normal"></SwtLabel>
            </HBox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>

    <SwtCanvas id="dataGridContainer" #dataGridContainer paddingBottom="5" styleName="canvasWithGreyBorder"
      marginTop="10" border="false" width="100%" height="100%" minHeight="100" minWidth="570"></SwtCanvas>

      <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5"  minWidth="570">
        <HBox width="100%">
          <HBox paddingLeft="5" paddingTop="2" width="100%" >
            <SwtButton #refreshButton id="refreshButton" (click)="refresh()"></SwtButton>
            
            <SwtButton #viewButton id="viewButton" enabled="false" (click)="viewDetails()"></SwtButton>
            <SwtButton #closeButton width="70" (click)="closeHandler()" id="closeButton"></SwtButton>
          </HBox>
          <HBox horizontalAlign="right" width="100%" paddingTop="2" >
            <SwtLabel visible="false" color="red" #dataBuildingText></SwtLabel>
            <SwtLabel visible="false" color="red" #lostConnectionText></SwtLabel>
            <SwtLabel #lastRefTimeLabel fontWeight="normal"></SwtLabel>
            <SwtLabel #lastRefTime fontWeight="normal"></SwtLabel>
      
            <HBox horizontalAlign="right" paddingRight="10" paddingTop="2" >
              <DataExport #dataExport id="dataExport"></DataExport>
              <SwtHelpButton id="helpIcon" #helpIcon (click)="doHelp()"></SwtHelpButton>
              <SwtButton [buttonMode]="true" #printButton id="printButton" styleName="printIcon"
              (click)="printPage()">
            </SwtButton>
            <SwtLoadingImage #loadingImage></SwtLoadingImage>
            </HBox>
          </HBox>
        </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>