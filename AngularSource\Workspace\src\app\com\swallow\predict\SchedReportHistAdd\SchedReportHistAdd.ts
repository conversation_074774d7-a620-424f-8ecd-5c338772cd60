import { <PERSON>mpo<PERSON>, <PERSON>Child, NgModule, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  SwtToolBoxModule,
  SwtModule,
  CommonService,
  SwtAlert,
  JSONReader,
  ExternalInterface,
  HTTPComms,
  Logger,
  SwtLabel,
  SwtUtil,
  CommonLogic,
  SwtTextInput,
  ScreenVersion,
  SwtButton,
} from "swt-tool-box";
import { Routes, RouterModule } from "@angular/router";
import { ModuleWithProviders } from "@angular/compiler/src/core";

@Component({
  selector: 'app-sched-report-hist-add',
  templateUrl: './SchedReportHistAdd.html',
  styleUrls: ['./SchedReportHistAdd.css']
})
export class SchedReportHistAdd extends SwtModule {

  //**********************  SwtLabel ***************************************************/ 
  @ViewChild('selectedScheduleId', { read: SwtLabel }) selectedScheduleId: SwtLabel;
  @ViewChild('selectedJobId', { read: SwtLabel }) selectedJobId: SwtLabel;
  @ViewChild('selectedReportType', { read: SwtLabel }) selectedReportType: SwtLabel;
  @ViewChild('mailResultLabel', { read: SwtLabel }) mailResultLabel: SwtLabel;
  @ViewChild('lblFileId', { read: SwtLabel }) lblFileId: SwtLabel;
  @ViewChild('lblScheduleId', { read: SwtLabel }) lblScheduleId: SwtLabel;
  @ViewChild('lblJobId', { read: SwtLabel }) lblJobId: SwtLabel;
  @ViewChild('lblReportTypeId', { read: SwtLabel }) lblReportTypeId: SwtLabel;
  @ViewChild('lblReportName', { read: SwtLabel }) lblReportName: SwtLabel;
  @ViewChild('lblFileName', { read: SwtLabel }) lblFileName: SwtLabel;
  @ViewChild('lblOutputLocation', { read: SwtLabel }) lblOutputLocation: SwtLabel;
  @ViewChild('lblRunDate', { read: SwtLabel }) lblRunDate: SwtLabel;
  @ViewChild('lblElapsedTime', { read: SwtLabel }) lblElapsedTime: SwtLabel;
  @ViewChild('lblFileSize', { read: SwtLabel }) lblFileSize: SwtLabel;
  @ViewChild('lblExportStatus', { read: SwtLabel }) lblExportStatus: SwtLabel;
  @ViewChild('lblMailStatus', { read: SwtLabel }) lblMailStatus: SwtLabel;
  @ViewChild('lblExportError', { read: SwtLabel }) lblExportError: SwtLabel;

  //**********************  SwtTextInput ***************************************************/ 
  @ViewChild('fileIdTextField', { read: SwtTextInput }) fileIdTextField: SwtTextInput;
  @ViewChild('scheduleIdTextField', { read: SwtTextInput }) scheduleIdTextField: SwtTextInput;
  @ViewChild('jobIdTextField', { read: SwtTextInput }) jobIdTextField: SwtTextInput;
  @ViewChild('reportTypeIdTextField', { read: SwtTextInput }) reportTypeIdTextField: SwtTextInput;
  @ViewChild('reportNameTextField', { read: SwtTextInput }) reportNameTextField: SwtTextInput;
  @ViewChild('fileNameTextField', { read: SwtTextInput }) fileNameTextField: SwtTextInput;
  @ViewChild('outputLocationTextField', { read: SwtTextInput }) outputLocationTextField: SwtTextInput;
  @ViewChild('runDateTextField', { read: SwtTextInput }) runDateTextField: SwtTextInput;
  @ViewChild('elapsedTimeTextField', { read: SwtTextInput }) elapsedTimeTextField: SwtTextInput;
  @ViewChild('fileSizeTextField', { read: SwtTextInput }) fileSizeTextField: SwtTextInput;
  @ViewChild('exportStatusTextField', { read: SwtTextInput }) exportStatusTextField: SwtTextInput;
  @ViewChild('mailStatusTextField', { read: SwtTextInput }) mailStatusTextField: SwtTextInput;
  @ViewChild('mailRsultTextField', { read: SwtTextInput }) mailRsultTextField: SwtTextInput;

  //**********************  SwtTextArea ***************************************************/ 
  @ViewChild('exportErrorTextField') exportErrorTextField: SwtTextInput;

  //**********************  SwtButton ***************************************************/ 
  @ViewChild('btnCancel', { read: SwtButton }) btnCancel: SwtButton;





  private swtAlert: SwtAlert;
  private logger: Logger;

  /**
  * Data Objects
  **/
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;

  /**                                                                                                           
   * Communication Objects                                                                                      
   **/
  private inputData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  public logic: CommonLogic = new CommonLogic();

  // variable to hold menu access id                               
  private menuAccessIdParent: number = 0;
  // Variable to hold attribute Id                                 
  private fileId: string = null;

  private screenName = 'Scheduled Report History Details';
  private versionNumber = '1.00.00';
  private releaseDate = '25 September 2019';
  public screenVersion  = new ScreenVersion(this.commonService) ;
  /* - END -- Screen Name and Version Number ---- */

  // private const ASCII_RESTRICT_PATTERN: string = "A-Za-z0-9\d_ !\"#$%&'()*+,\-.\/:;<=>?@[\\\]^`{|}~";
  // private const ACCOUNT_NAME_RESTRICT_PATTERN: string = "a-zA-Z0-9\d .,:;#\(\)\*\?\[\]%>_+=^\|\\+/";

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Scheduled Report History Details', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit(): void {
    // Load the Screen information
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);

    this.lblFileId.text = SwtUtil.getPredictMessage('label.schedReportHist.fileId', null) + ' *';
    this.fileIdTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.fileId', null);
    this.lblScheduleId.text = SwtUtil.getPredictMessage('label.schedReportHist.scheduleId', null);
    this.scheduleIdTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.scheduleId', null);
    this.lblJobId.text = SwtUtil.getPredictMessage('label.schedReportHist.jobId', null);
    this.jobIdTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.jobId', null);
    this.lblReportTypeId.text = SwtUtil.getPredictMessage('label.schedReportHist.reportTypeId', null);
    this.reportTypeIdTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.reportTypeId', null);
    this.lblReportName.text = SwtUtil.getPredictMessage('label.schedreporthist.column.reportName', null);
    this.reportNameTextField.toolTip = SwtUtil.getPredictMessage('tooltip.schedreporthist.reportName', null);
    this.lblFileName.text = SwtUtil.getPredictMessage('label.schedReportHist.fileName', null);
    this.fileNameTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.fileName', null);
    this.lblOutputLocation.text = SwtUtil.getPredictMessage('label.schedReportHist.outputLocation', null);
    this.outputLocationTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.outputLocation', null);
    this.lblRunDate.text = SwtUtil.getPredictMessage('label.schedReportHist.runDate', null);
    this.runDateTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.runDate', null);
    this.lblElapsedTime.text = SwtUtil.getPredictMessage('label.schedReportHist.elapsedTime', null);
    this.elapsedTimeTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.elapsedTime', null);
    this.lblFileSize.text = SwtUtil.getPredictMessage('label.schedReportHist.fileSize', null);
    this.fileSizeTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.fileSize', null);
    this.lblExportStatus.text = SwtUtil.getPredictMessage('label.schedReportHist.exportStatus', null);
    this.exportStatusTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.exportStatus', null);
    this.lblMailStatus.text = SwtUtil.getPredictMessage('label.schedReportHist.mailStatus', null);
    this.mailStatusTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.mailStatus', null);
    this.lblExportError.text = SwtUtil.getPredictMessage('label.schedReportHist.exportError', null);
    this.exportErrorTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.exportError', null);
    this.mailResultLabel.text = SwtUtil.getPredictMessage('label.schedReportHist.mailRsult', null);
    this.mailRsultTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.mailRsult', null);

    this.btnCancel.label = SwtUtil.getPredictMessage('button.close', null);
    this.btnCancel.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
  }


  /** 
			 *   This method has the initial configurations for the application like:<br> 
			 * 	- Base URL for the applicaiton<br>                                        
			 * 	- Action Path for the application<br>                                     
			 * 	- Action Method to be called first<br>                                    
			 */
  onLoad(): void {
    //Obtain the url of the context                                           
    this.baseURL = SwtUtil.getBaseURL();
    // initialize the requestParams                                           
    this.requestParams =[];

    this.logic.dateFormat = ExternalInterface.call('eval', 'dateFormat'); //'dd/MM/yyyy';
    this.logic.testDate = ExternalInterface.call('eval', 'dbDate'); //'01/10/2008';

    // get the menuAccessId                                                   
    this.menuAccessIdParent = ExternalInterface.call('eval', 'menuAccessIdParent');  //0; 
    //get the screen name                                                     
    this.screenName = ExternalInterface.call('eval', 'screenName'); //'viewScreen';
    //get the selected fileId                                           
    this.fileId = ExternalInterface.call('eval', 'fileId'); //'30';

    //Initialize the context menu                                             
    // initializeMenus();
    //result event                                                            
    this.inputData.cbResult = (data) => {
      this.inputDataResult(data);
    };
    //fault event                                                             
    this.inputData.cbFault = this.inputDataFault.bind(this);
    // set the encode url                                                     
    this.inputData.encodeURL = false;
    //action url	                                                          
    this.actionPath = "schedReportHist.do?";
    //Then declare the action method:	                                      
    this.actionMethod = "method=displaySchedReportHist";

    this.actionMethod += "&loadFlex=true";
    this.actionMethod += "&screenName=" + this.screenName;
    if (this.screenName != null) {
      this.actionMethod += "&fileId=" + this.fileId;
    }

    // inputData.resultFormat = "e4x";
    //Then apply them to the url member of the HTTPComms object:              
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    //Make initial request                                                    
    this.inputData.send(this.requestParams);
  }

  /**
          * Part of a callback function to all for control of the loading swf from the HTTPComms Object
 **/
  endOfComms(): void {
   // this.enableInterface();
  }
  /**
         * Part of a callback function to all for control of the loading swf from the HTTPComms Object
  **/
  startOfComms(): void {
    this.disableInterface();
  }

  /**
  * If a fault occurs with the connection with the server then display the lost connection label
  **/
  private inputDataFault() {
    let message = SwtUtil.getPredictMessage('label.genericException', null);
    this.swtAlert.error(message);
  }

  /***
   * 
   * 
   */
  private inputDataResult(event): void {
    this.logger.info('method [inputDataResult] - START ');
    // Variable Number Error Location
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        // If the previousXML is different to new xml then allow an update. Otherwise leave it alone
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          // Test the reply status first, as if the reply status is false then the data will not be valid
          if (this.jsonReader.getRequestReplyStatus()) {
            if (!this.jsonReader.isDataBuilding()) {


              // // Condition to chcek screen name is change or view          
              if (this.screenName != null && this.screenName != "addScreen") {
                //Disable primary keys fields                         
                this.fileIdTextField.enabled = false;
                this.fileIdTextField.text = this.jsonReader.getSingletons().fileId;
                this.scheduleIdTextField.text = this.jsonReader.getSingletons().scheduleId;
                this.selectedScheduleId.text = this.jsonReader.getSingletons().scheduleName;
                this.jobIdTextField.text = this.jsonReader.getSingletons().jobId;
                this.selectedJobId.text = this.jsonReader.getSingletons().jobName;
                this.reportTypeIdTextField.text = this.jsonReader.getSingletons().reportTypeId;
                this.selectedReportType.text = this.jsonReader.getSingletons().reportTypeName;
                this.runDateTextField.text = this.jsonReader.getSingletons().runDate;
                this.reportNameTextField.text = this.jsonReader.getSingletons().reportName;

                this.elapsedTimeTextField.text = this.jsonReader.getSingletons().elapsedTime;
                this.fileNameTextField.text = this.jsonReader.getSingletons().fileName;
                this.outputLocationTextField.text = this.jsonReader.getSingletons().outputLocation;
                this.fileSizeTextField.text = this.jsonReader.getSingletons().fileSize;
                this.exportStatusTextField.text = this.jsonReader.getSingletons().exportStatus;
                this.mailStatusTextField.text = this.jsonReader.getSingletons().mailStatusAsString;
                this.exportErrorTextField.text = this.jsonReader.getSingletons().exportError;

                if (this.jsonReader.getSingletons().mailStatus != null && this.jsonReader.getSingletons().mailStatus == "S") {
                  this.mailRsultTextField.text = this.jsonReader.getSingletons().mailRsult;
                  this.mailResultLabel.text = SwtUtil.getPredictMessage('label.schedReportHist.mailRsult', null);
                  this.mailRsultTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.mailRsult', null);
                } else {
                  this.mailRsultTextField.text = this.jsonReader.getSingletons().mailError;
                  this.mailResultLabel.text = SwtUtil.getPredictMessage('label.schedReportHist.errorDescription', null);
                  this.mailRsultTextField.toolTip = SwtUtil.getPredictMessage('tip.schedReportHist.errorDescription', null);

                }

              }
              if (this.screenName != null && this.screenName == "viewScreen") {
                this.disableInterface();
              }



            } else {
              // tslint:disable-next-line: max-line-length
              let message = SwtUtil.getPredictMessage('label.genericException', null);
              this.swtAlert.error(message);
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('method [inputDataResult] - error ', error);
    }
  }


  /**                                                                                                          
			 * Function called to close the window when close button is clicked                                          
			 * @param event :MouseEvent                                                                                  
			 */
  closeHandler(event): void {
    ExternalInterface.call("close");
  }

  /**                                                                                          
			 * Set all screen components in view mode(disable)                                           
			 *                                                                                           
			 **/
  protected disableInterface(): void {
    this.scheduleIdTextField.editable = false;
    this.fileIdTextField.editable = false;
    this.jobIdTextField.editable = false;
    this.reportTypeIdTextField.editable = false;
    this.runDateTextField.editable = false;
    this.elapsedTimeTextField.editable = false;
    this.fileNameTextField.editable = false;
    this.outputLocationTextField.editable = false;
    this.fileSizeTextField.editable = false;
    this.exportStatusTextField.editable = false;
    this.mailStatusTextField.editable = false;
    this.exportErrorTextField.editable = false;
    this.mailRsultTextField.editable = false;
    this.reportNameTextField.editable = false;
  }

  protected enableInterface(): void {
    this.fileIdTextField.enabled = true;
    this.jobIdTextField.enabled = true;
    this.reportTypeIdTextField.enabled = true;
    this.runDateTextField.enabled = true;
    this.elapsedTimeTextField.enabled = true;
    this.fileNameTextField.enabled = true;
    this.outputLocationTextField.enabled = true;
    this.fileSizeTextField.enabled = true;
    this.exportStatusTextField.enabled = true;
    this.mailStatusTextField.enabled = true;
    this.exportErrorTextField.enabled = true;
    this.mailRsultTextField.enabled = true;
  }

  doHelp() {
    ExternalInterface.call('help')
  }

}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: SchedReportHistAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SchedReportHistAdd],
  entryComponents: []
})
export class SchedReportHistAddModule { }
