import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule, Router } from '@angular/router';
import { SwtToolBoxModule, SwtModule, Logger, SwtAlert, CommonService, SwtUtil, SwtLoadingImage, SwtLabel, SwtTextInput, SwtButton, SwtNumericInput, SwtRadioButtonGroup, SwtRadioItem, SwtCheckBox, SwtTextArea, SwtCanvas, SwtComboBox, SwtFieldSet, SwtCommonGrid, ExternalInterface, JSONReader, HTTPComms, SwtPopUpManager, Alert, StringUtils, XML, Encryptor } from 'swt-tool-box';
import { ShowXML } from '../../EnhancedAlerting/ScenarioDetail/Tabs/ShowXML/ShowXML';
import { CcyExpectedTime } from './CcyExpectedTime/CcyExpectedTime';
declare function validateField(strField, strLabel, strPat, maxValue, minValue): any;
declare var require: any;
var prettyData = require('pretty-data');
declare var instanceElement: any;
import moment from "moment";
declare  function validateFormatTime(strField): any;
@Component({
  selector: 'app-critical-movement-update-detail',
  templateUrl: './CriticalMvtUpdateDetail.html',
  styleUrls: ['./CriticalMvtUpdateDetail.css']
})
export class CriticalMvtUpdateDetail extends SwtModule implements OnInit {
  
  @ViewChild('swtModule') swtModule: SwtModule;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  /***********SwtLabel***********/
  @ViewChild('entityLbl') entityLbl: SwtLabel;
  @ViewChild('entityDesc') entityDesc: SwtLabel;
  @ViewChild('criticalTypeLbl') criticalTypeLbl: SwtLabel;
  @ViewChild('descLbl') descLbl: SwtLabel;
  @ViewChild('workingDayOnlyLbl') workingDayOnlyLbl: SwtLabel;
  @ViewChild('scheduledTimeFrameLbl') scheduledTimeFrameLbl: SwtLabel;
  @ViewChild('runSqlUpdateLbl') runSqlUpdateLbl: SwtLabel;
  @ViewChild('unitLbl') unitLbl: SwtLabel;
  @ViewChild('activeBetweenLbl') activeBetweenLbl: SwtLabel;
  @ViewChild('andLbl') andLbl: SwtLabel;
  @ViewChild('lastExecLbl') lastExecLbl: SwtLabel;
  @ViewChild('lastExecTime') lastExecTime: SwtLabel;
  

  @ViewChild('categoryLbl') categoryLbl: SwtLabel;
  @ViewChild('orderInCategoryLbl') orderInCategoryLbl: SwtLabel;
  @ViewChild('sumToTotalLbl') sumToTotalLbl: SwtLabel;
  @ViewChild('sumToCategLbl') sumToCategLbl: SwtLabel;
  @ViewChild('reportableLbl') reportableLbl: SwtLabel;
  @ViewChild('reportIndivPayLbl') reportIndivPayLbl: SwtLabel;
  @ViewChild('enableUpdateProcessingLbl') enableUpdateProcessingLbl: SwtLabel;

  @ViewChild('sqlUpdateQueryLbl') sqlUpdateQueryLbl: SwtLabel;
  @ViewChild('updateMvtLbl') updateMvtLbl: SwtLabel;
  @ViewChild('setLbl') setLbl: SwtLabel;
  @ViewChild('whereLbl') whereLbl: SwtLabel;
  
  
  

  /***********SwtTextInput***********/
  @ViewChild('criticalTypeTxt') criticalTypeTxt: SwtTextInput;
  @ViewChild('descTxt') descTxt: SwtTextInput;
  @ViewChild('startTimeTxt') startTimeTxt: SwtTextInput;
  @ViewChild('endTimeTxt') endTimeTxt: SwtTextInput;
  

  /***********SwtButton***********/
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('validateQueryButton') validateQueryButton: SwtButton;

  
    /***********SwtNumericInput***********/

  @ViewChild('orderInCategoryTxt') orderInCategoryTxt: SwtNumericInput;
  @ViewChild('runSqlUpdateTxt') runSqlUpdateTxt: SwtNumericInput;

  
  @ViewChild('setTxtArea') setTxtArea: SwtTextArea;
  @ViewChild('whereTxtArea') whereTxtArea: SwtTextArea;

  @ViewChild('timeFrameGroup') timeFrameGroup: SwtRadioButtonGroup;
  @ViewChild('radioEntity') radioEntity: SwtRadioItem;
  @ViewChild('radioSystem') radioSystem: SwtRadioItem;

  /***********SwtCheckBox***********/
  @ViewChild('workingDayOnlyCheck') workingDayOnlyCheck: SwtCheckBox;
  @ViewChild('sumToCategCheck') sumToCategCheck: SwtCheckBox;
  @ViewChild('sumToTotalCheck') sumToTotalCheck: SwtCheckBox;
  @ViewChild('reportableCheck') reportableCheck: SwtCheckBox;
  @ViewChild('reportIndivPayCheck') reportIndivPayCheck: SwtCheckBox;
  @ViewChild('enableUpdateProcessingCheck') enableUpdateProcessingCheck: SwtCheckBox;
  
  /***********SwtComboBox***********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('categoryCombo') categoryCombo: SwtComboBox;

  /***********SwtCanvas***********/
  @ViewChild('ccyGridContainer') ccyGridContainer: SwtCanvas;

  /***********SwtFieldSet***********/
    @ViewChild('fieldSet') fieldSet: SwtFieldSet;


    @ViewChild('acceptButton') acceptButton: SwtButton;
    @ViewChild('rejectButton') rejectButton: SwtButton;
    @ViewChild('amendButton') amendButton: SwtButton;
  
    @ViewChild('cancelAmendButton') cancelAmendButton: SwtButton;
    @ViewChild('closeButton') closeButton: SwtButton;

      /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  private deleteData = new HTTPComms(this.commonService);
  public checkAuthData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  private logger: Logger;
  private 
  swtAlert: SwtAlert;
  private ccyGrid: SwtCommonGrid;
  private menuAccessId;
  private screenName;
  private parameters;
  private criticalPayTypeData;
  public win : any;
  public ccyComboData;
  private isFromMaintenanceEvent  = false;
  private isFromMaintenanceEventAmendMode  = false;
  private maintEventId = null;
  public criticalPayTypeId: string;
  private parentMenuAccess = null;
  private  authOthers= null;
  private  canAmendFacility= false;
  private requireAuthorisation = true;
  private entityId= null;
  public description = null;
  public ccyExpectedTimesList: any ;
  public operationsList: XML = new XML( "<operationsList/>");
  spreadDetailsGrid: any;
  constructor(private commonService: CommonService, private element: ElementRef, private router: Router) {
    super(element, commonService);
    this.logger = new Logger('Critical Movement Update Maintenance Screen', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    //this.screenName = ExternalInterface.call('eval', 'screenName');
    //this.parameters = ExternalInterface.call('eval', 'params') ? JSON.parse(ExternalInterface.call('eval', 'params')) :"";
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');

    this.acceptButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.label', null);
    this.acceptButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.tooltip', null);

    this.rejectButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.label', null);
    this.rejectButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.tooltip', null);


    
    if(window.opener.instanceElement) {
      let params = window.opener.instanceElement.getParams();
      this.screenName  = params[0];
      if (params[1]) {
        this.criticalPayTypeId= params[1] ;
      }
      if (params[2]) {
        this.entityId= params[2] ;
      }
      if (params.length > 3 &&  params[4]) {
        this.isFromMaintenanceEvent = true;
        if("I" == params[4]){
          this.isFromMaintenanceEventAmendMode = true;
      }
      params.length > 3 &&  params[4]
        this.maintEventId = params[3];
        if(params.length> 4 &&  params[5])
          this.parentMenuAccess = params[5]; 

          if(params.length> 5 &&  params[6])
            this.authOthers = params[6]; 

            if(params.length> 6 &&  params[7])
              this.canAmendFacility = StringUtils.isTrue(params[7]); 
            

        
      }

      // this.isFromMaintenanceEvent = false;
     }
    this.ccyGrid = <SwtCommonGrid>this.ccyGridContainer.addChild(SwtCommonGrid);
    this.entityLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.entityId', null);
    this.criticalTypeLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.criticalTypeId', null)+'*';
    this.descLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.desc', null);
    this.workingDayOnlyLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.workingDayOnly', null);
    this.scheduledTimeFrameLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.scheduledTimeFrame', null);
    this.runSqlUpdateLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.runSqlUpdate', null);
    this.unitLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.unit', null);
    this.activeBetweenLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.activeBetween', null);
    this.andLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.and', null);
    this.lastExecLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.lastExec', null);

    this.categoryLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.category', null);
    this.orderInCategoryLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.orderInCategory', null);
    this.sumToTotalLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.sumToTotal', null);
    this.sumToCategLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.sumToCateg', null);
    this.reportableLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.reportable', null);
    this.reportIndivPayLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.reportIndivPay', null);
    this.enableUpdateProcessingLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.enableUpdateProcessing', null);


    this.sqlUpdateQueryLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.sqlUpdateQuery', null);
    this.updateMvtLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.updateMvt', null);
    this.setLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.set', null);
    this.whereLbl.text = SwtUtil.getPredictMessage('criticalMvtUpdate.where', null);

    this.setTxtArea.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.set', null);
    this.whereTxtArea.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.where', null);


    this.entityCombo.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.entity', null);
    this.categoryCombo.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.category', null);


    this.criticalTypeTxt.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.criticalType', null);
    this.descTxt.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.desc', null);
    this.runSqlUpdateTxt.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.runSqlUpdate', null);

    this.saveButton.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.save', null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.cancel', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.add', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.change', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.delete', null);
    this.validateQueryButton.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.validateQuery', null);
    this.saveButton.label = SwtUtil.getPredictMessage('criticalMvtUpdate.save', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('criticalMvtUpdate.cancel', null);
    this.validateQueryButton.label = SwtUtil.getPredictMessage('criticalMvtUpdate.validateQuery', null);
    this.addButton.label = SwtUtil.getPredictMessage('criticalMvtUpdate.add', null);
    this.changeButton.label = SwtUtil.getPredictMessage('criticalMvtUpdate.change', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('criticalMvtUpdate.delete', null);

    this.startTimeTxt.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.startTime', null);
    this.endTimeTxt.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.endTime', null);
    this.orderInCategoryTxt.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.orderInCategory', null);

    this.radioEntity.label = SwtUtil.getPredictMessage('criticalMvtUpdate.radioEntity', null);
    this.radioSystem.label = SwtUtil.getPredictMessage('criticalMvtUpdate.radioSystem', null);
    this.radioEntity.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.radioEntity', null);
    this.radioSystem.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.radioSystem', null);

    this.timeFrameGroup.toolTip=  SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.timeFrameGroup', null);

    this.workingDayOnlyCheck.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.workingDayOnlyCheck', null);
    this.sumToCategCheck.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.sumToCategCheck', null);
    this.sumToTotalCheck.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.sumToTotalCheck', null);
    this.reportableCheck.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.reportableCheck', null);
    this.reportIndivPayCheck.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.reportIndivPayCheck', null);
    this.enableUpdateProcessingCheck.toolTip = SwtUtil.getPredictMessage('criticalMvtUpdate.toolTip.enableUpdateProcessingCheck', null);
    this.whereTxtArea.enabled=false;

    this.amendButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.amend.label', null);
    this.amendButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.amend.tooltip', null);

    this.cancelAmendButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.cancelAmendButton.toolTip = SwtUtil.getPredictMessage('tooltip.CancelChanges', null);

    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.entityMonitor.close', null);
    this.criticalTypeTxt.required = true;
  }

  onLoad(){
    this.requestParams = [];

    if(this.maintEventId && StringUtils.isTrue(this.authOthers) && this.screenName != "change"){
      this.rejectButton.visible = true;
      this.acceptButton.visible = true;
    }

    if(this.maintEventId  && this.screenName == "view"){
      this.amendButton.visible = true;
      this.amendButton.includeInLayout = true;
      this.saveButton.visible = false;
      this.saveButton.includeInLayout = false;
      if(StringUtils.isTrue(this.canAmendFacility)){
        this.amendButton.enabled = true;
        this.rejectButton.visible = true;
      }else {
        this.amendButton.enabled = false;
      }
    }else {
      if(this.maintEventId){
        this.saveButton.visible = true;
        this.saveButton.includeInLayout = true;
        this.cancelAmendButton.visible = true;
        this.cancelAmendButton.includeInLayout = true;

        this.closeButton.visible = true;
        this.closeButton.includeInLayout = true;

        this.cancelButton.visible = false;
        this.cancelButton.includeInLayout = false;

        this.amendButton.visible = false;
        this.amendButton.includeInLayout = false;
      }
    }

    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;

    this.deleteData.cbFault = this.inputDataFault.bind(this);
    this.deleteData.encodeURL = false;
    this.deleteData.cbStart = this.startOfComms.bind(this);
    this.deleteData.cbStop = this.endOfComms.bind(this);
    
    this.actionPath = "criticalPaymentType.do?";
    this.actionMethod = 'method=displayDetails';
    this.requestParams['method'] = this.screenName;
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['entityId'] = this.screenName !='add' ? this.entityId:"";
    this.requestParams['cpTypeId'] = this.screenName !='add'  ? this.criticalPayTypeId: "";
    this.requestParams['screenName'] = this.screenName;
    this.requestParams["isFromMaintenanceEvent"] = this.maintEventId?"true":"false";
    this.requestParams["maintEventId"] = this.maintEventId;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    this.ccyGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    }; 

    this.ccyGrid.rowColorFunction = ( dataContext, dataIndex, color, dataField ) => {
      return this.drawRowBackground( dataContext, dataIndex, color , dataField);
    };
  }

  drawRowBackground( dataContext, dataIndex, color,dataField ): string {
  
    let rColor: string;
    try {
      let colorFlag: string;
    // if(dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.entity){
      if('Y'==dataContext.slickgrid_rowcontent[dataField].isDeletedRow){
        rColor = "#ff808a";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isNewRow){
        rColor = "#c6efce";
      }else if('Y'== dataContext.slickgrid_rowcontent[dataField].isUpdatedRow){
        rColor = "#ee82ee";
     }
      
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }


  inputDataResult(event): void {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) { 
          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.entityCombo.selectedLabel = this.jsonReader.getSingletons().defaultEntity;
          this.description= this.jsonReader.getSingletons().criticalTypeDesc;
          this.entityDesc.text= this.entityCombo.selectedValue;
          this.categoryCombo.setComboData(this.jsonReader.getSelects());
          this.ccyComboData= this.jsonReader.getSelects()['select'].find(x => x.id === 'currencyList').option;
          let lastExecutedDate= this.jsonReader.getSingletons().lastExecutedDate;
          this.requireAuthorisation = this.jsonReader.getScreenAttributes()["requireAuthorisation"];
          if (!this.jsonReader.isDataBuilding()) {

            if (this.screenName != "add") {
              this.criticalPayTypeData= JSON.parse(this.jsonReader.getSingletons().criticalPayTypeObject);
            this.entityCombo.selectedLabel= this.criticalPayTypeData.id.entityId ? this.criticalPayTypeData.id.entityId : "";
            this.criticalTypeTxt.text= this.criticalPayTypeData.id.cpTypeId ? this.criticalPayTypeData.id.cpTypeId : "";
            this.descTxt.text= this.criticalPayTypeData.cpTypeDesc ? this.criticalPayTypeData.cpTypeDesc : "";
            this.categoryCombo.selectedValue= this.criticalPayTypeData.criticalPayCateg ? this.criticalPayTypeData.criticalPayCateg : "";
            this.orderInCategoryTxt.text= this.criticalPayTypeData.orderInCateg ? this.criticalPayTypeData.orderInCateg : "";
            this.sumToCategCheck.selected= this.criticalPayTypeData.sumToCateg == "Y" ? true : false;
            this.sumToTotalCheck.selected= this.criticalPayTypeData.sumToTotal == "Y" ? true : false;
            this.reportableCheck.selected= this.criticalPayTypeData.reportable == "Y" ? true : false;
            this.reportIndivPayCheck.selected= this.criticalPayTypeData.reportIndivPay == "Y" ? true : false;
            this.enableUpdateProcessingCheck.selected= this.criticalPayTypeData.enableProcess == "Y" ? true : false;
            this.setTxtArea.text= this.criticalPayTypeData.updateSetClause ? this.criticalPayTypeData.updateSetClause : "";
            this.whereTxtArea.text= this.criticalPayTypeData.updateWhereClause ? this.criticalPayTypeData.updateWhereClause : "";
            this.timeFrameGroup.selectedValue= this.criticalPayTypeData.execTimeFrame ? this.criticalPayTypeData.execTimeFrame : "";
            this.workingDayOnlyCheck.selected= this.criticalPayTypeData.execWorkDaysOnly == "Y" ? true : false;
            this.runSqlUpdateTxt.text= this.criticalPayTypeData.execFrequencyMins? this.criticalPayTypeData.execFrequencyMins : "";
            this.startTimeTxt.text= this.criticalPayTypeData.execStartTime? this.criticalPayTypeData.execStartTime : "";
            this.endTimeTxt.text= this.criticalPayTypeData.execEndTime? this.criticalPayTypeData.execEndTime : "";
            this.lastExecTime.text= this.criticalPayTypeData.lastExecSysTime? this.criticalPayTypeData.lastExecSysTime: lastExecutedDate; 
            }
            const obj = { columns: this.lastRecievedJSON.CriticalPayTypeDetail.ccyGrid.metadata.columns };
            this.ccyGrid.CustomGrid(obj);
            var gridRows = this.lastRecievedJSON.CriticalPayTypeDetail.ccyGrid.rows;
            if (gridRows && gridRows.size > 0) {

              this.ccyGrid.gridData = gridRows;

              this.ccyGrid.setRowSize = this.jsonReader.getRowSize();
            }
            else {
              
              this.ccyGrid.gridData = { size: 0, row: [] };
            }


            this.enableDisableFields(this.screenName);
            if (this.screenName != "add") {
              this.descTxt.text = this.description;

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeDesc_oldValue != this.jsonReader.getSingletons().criticalTypeDesc){
                this.descTxt.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeDesc_oldValue;
              }


              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeCateg_oldValue != this.jsonReader.getSingletons().criticalTypeCateg){
              this.categoryCombo.toolTipPreviousValue = this.getCategoryDesc(this.jsonReader.getSingletons().criticalTypeCateg_oldValue);
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeOrderInCateg_oldValue != this.jsonReader.getSingletons().criticalTypeOrderInCateg){
                this.orderInCategoryTxt.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeOrderInCateg_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeSumToCateg_oldValue != this.jsonReader.getSingletons().criticalTypeSumToCateg){
                this.sumToCategCheck.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeSumToCateg_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeSumToTotal_oldValue != this.jsonReader.getSingletons().criticalTypeSumToTotal){
                this.sumToTotalCheck.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeSumToTotal_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeReportable_oldValue != this.jsonReader.getSingletons().criticalTypeReportable){
                this.reportableCheck.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeReportable_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeReportIndivPay_oldValue != this.jsonReader.getSingletons().criticalTypeReportIndivPay){
                this.reportIndivPayCheck.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeReportIndivPay_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeEnableUpdateProcess_oldValue != this.jsonReader.getSingletons().criticalTypeEnableUpdateProcess){
                this.enableUpdateProcessingCheck.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeEnableUpdateProcess_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeSetClause_oldValue != this.jsonReader.getSingletons().criticalTypeSetClause){
                this.setTxtArea.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeSetClause_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeWhereClause_oldValue != this.jsonReader.getSingletons().criticalTypeWhereClause){
                this.whereTxtArea.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeWhereClause_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeTimeFrame_oldValue != this.jsonReader.getSingletons().criticalTypeTimeFrame){
                this.timeFrameGroup.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeTimeFrame_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeWorkingDays_oldValue != this.jsonReader.getSingletons().criticalTypeWorkingDays){
                this.workingDayOnlyCheck.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeWorkingDays_oldValue;
              }

              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeRunSqlUpdate_oldValue != this.jsonReader.getSingletons().criticalTypeRunSqlUpdate){
                this.runSqlUpdateTxt.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeRunSqlUpdate_oldValue;
              }

              
              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeStartTime_oldValue != this.jsonReader.getSingletons().criticalTypeStartTime){
                this.startTimeTxt.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeStartTime_oldValue;
              }

              
              if(this.maintEventId && this.jsonReader.getSingletons().criticalTypeEndTime_oldValue != this.jsonReader.getSingletons().criticalTypeEndTime){
                this.endTimeTxt.toolTipPreviousValue = this.jsonReader.getSingletons().criticalTypeEndTime_oldValue;
              }

            }
            this.swtModule.subscribeSpy([this.ccyGrid]);
            this.swtModule.onSpyChange.subscribe((e) => {
              this.ccyExpectedTimesList = this.ccyGrid.changes;
            });
            
            this.prevRecievedJSON = this.lastRecievedJSON;

          }
        }
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }
      }


    }

  }



  cellClickEventHandler(event): void {
      if(this.ccyGrid.selectedIndex >=0){
      this.changeButton.enabled = true;
      this.changeButton.buttonMode = true;
      this.deleteButton.enabled = true;
      this.deleteButton.buttonMode = true;
      }else{
        this.changeButton.enabled = false;
        this.changeButton.buttonMode = false;
        this.deleteButton.enabled = false;
        this.deleteButton.buttonMode = false;
      }
  }

  validateField(field, strLabel, strPat) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      var isValidField= validateField(field, strLabel, strPat, "", "");
      if (!isValidField) {
        field.text="";
        return false;
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'CriticalMvtUpdateDetail.ts', "validateField", errorLocation);
    }
  }


  logicValidateQuery(event) {
    let message = SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninAdd', null);
    if (this.logicUpdate.isBusy()) {
      this.logicUpdate.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyStatus() ) {
        this.swtAlert.info(this.jsonReader.getRequestReplyMessage(), null, Alert.OK, null, () => {
          // ExternalInterface.call('close');
        });
      } else if (!this.jsonReader.getRequestReplyStatus()) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage(), null, Alert.OK, null, () => {
          // ExternalInterface.call('close');
        });
      }
    }

  }
  
  saveHandler(){
    this.requestParams = [];
    this.ccyGrid.refresh();
        // Convert required data of the spread process point grid to XML before sending
          this.xmlDataCcyTimes();

      if (this.setTxtArea.text==""){
        this.whereTxtArea.text="";
      }

      if (!this.criticalTypeTxt.text) {
        this.swtAlert.error(SwtUtil.getPredictMessage('alert.mandatoryField', null));
        return;
      } 
      else if(this.enableUpdateProcessingCheck.selected && !this.whereTxtArea.text && !this.setTxtArea.text){
        this.swtAlert.error(SwtUtil.getPredictMessage('alert.criticalPaymentType.updateQuery', null));
        return;
      } else if(this.enableUpdateProcessingCheck.selected && !this.runSqlUpdateTxt.text){
        this.swtAlert.error(SwtUtil.getPredictMessage('alert.criticalPaymentType.runSqlUpdateEvery', null));
        return;
      }else if(this.setTxtArea.text && !this.whereTxtArea.text){
          this.swtAlert.error(SwtUtil.getPredictMessage('alert.criticalPaymentType.whereClauseIsEmpty', null));
          return;
      }else if((!this.startTimeTxt.text && this.endTimeTxt.text )|| (this.startTimeTxt.text && !this.endTimeTxt.text)){
        this.swtAlert.error(SwtUtil.getPredictMessage('alert.criticalPaymentType.activeBetween', null), null );
        return;
      }else{
      
      this.logicUpdate.cbStart = this.startOfComms.bind(this);
      this.logicUpdate.cbStop = this.endOfComms.bind(this);
      this.logicUpdate.cbResult = (event) => {
        //ExternalInterface.call("close");
        //window.opener.instanceElement.updateData();
        this.logicUpdateResult(event);
      };
      this.logicUpdate.cbFault = this.inputDataFault.bind(this);
      this.logicUpdate.encodeURL = false;
      this.actionPath = "criticalPaymentType.do?";
      this.actionMethod = 'method=saveCriticalMvtDetails';
      this.requestParams['entityId'] = this.entityCombo.selectedLabel;
      this.requestParams['type'] = this.criticalTypeTxt.text;
      this.requestParams['description'] = this.descTxt.text;
      this.requestParams['cateory'] = this.categoryCombo.selectedValue;
      this.requestParams['orderInCateg'] = this.orderInCategoryTxt.text;
      this.requestParams['sumToCategory'] = this.sumToCategCheck.selected ? "Y" : "N";
      this.requestParams['sumToTotal'] =  this.sumToTotalCheck.selected ? "Y" : "N";
      this.requestParams['reportable'] = this.reportableCheck.selected ? "Y" : "N";
      this.requestParams['reportIndivPay'] = this.reportIndivPayCheck.selected ? "Y" : "N";
      this.requestParams['enableUpdateProcessing'] = this.enableUpdateProcessingCheck.selected ? "Y" : "N";
      this.requestParams['setClause'] = this.setTxtArea.text;
      this.requestParams['whereClause'] = this.whereTxtArea.text;
      this.requestParams['schedTimeFrame'] = this.timeFrameGroup.selectedValue;
      this.requestParams['workingDayOnly'] = this.workingDayOnlyCheck.selected ? "Y" : "N";
      this.requestParams['runSqlUpdate'] = this.runSqlUpdateTxt.text;
      this.requestParams['startTime'] = this.startTimeTxt.text;
      this.requestParams['endTime'] = this.endTimeTxt.text;
      this.requestParams['xmlData'] = this.operationsList.toString();  
      this.requestParams["action"] = this.screenName=="add"?"save":"update";    
      this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
      this.logicUpdate.send(this.requestParams);
    }
  }


  getCcyTime() {
    let row = {};
    let rows=[];
      let data = this.ccyGrid.gridData;
      for (let i = 0; i < data.length; i++) {
        row = {         
          'CCY': data[i].ccy,
          'TIME': data[i].defaultExpectedTime
        };
        rows.push(row);
      }
      return rows;
  }

  enableField(textArea){
    if(textArea=="setTxtArea"){
      if(this.setTxtArea.text){
        this.whereTxtArea.enabled=true;
      }else{
        this.whereTxtArea.enabled=false;
      }
    }
    if(this.whereTxtArea.text && this.setTxtArea.text){
    this.validateQueryButton.enabled= true;
    }else{
    this.validateQueryButton.enabled= false;
    }
  }

  enableDisableFields(screenName){
    if(screenName=="view"){
      this.entityCombo.enabled= false;
      this.criticalTypeTxt.enabled= false ;
      this.descTxt.enabled= false;
      this.categoryCombo.enabled= false;
      this.orderInCategoryTxt.enabled= false;
      this.sumToCategCheck.enabled= false;
      this.sumToTotalCheck.enabled= false;
      this.reportableCheck.enabled= false;
      this.reportIndivPayCheck.enabled= false;
      this.enableUpdateProcessingCheck.enabled= false;
      this.setTxtArea.enabled= false;
      this.whereTxtArea.enabled= false;
      this.validateQueryButton.enabled= false;
      this.radioSystem.enabled= false;
      this.radioEntity.enabled= false;
      this.workingDayOnlyCheck.enabled= false;
      this.runSqlUpdateTxt.enabled= false;
      this.startTimeTxt.enabled= false;
      this.endTimeTxt.enabled= false;
      this.ccyGrid.enabled= false;
      this.ccyGrid.selectable= false;
      this.addButton.enabled= false;
      this.addButton.buttonMode= false;
      this.changeButton.enabled= false;
      this.changeButton.buttonMode= false;
      this.deleteButton.enabled= false;
      this.deleteButton.buttonMode= false;
      this.saveButton.enabled=false;
      this.saveButton.buttonMode= false;

    }else if(screenName=='change'){
      this.entityCombo.enabled= false;
      this.criticalTypeTxt.enabled= false ;
      this.descTxt.enabled= true;
      this.categoryCombo.enabled= true;
      this.orderInCategoryTxt.enabled= true;
      this.sumToCategCheck.enabled= true;
      this.sumToTotalCheck.enabled= true;
      this.reportableCheck.enabled= true;
      this.reportIndivPayCheck.enabled= true;
      this.enableUpdateProcessingCheck.enabled= true;
      this.setTxtArea.enabled= true;
      this.whereTxtArea.enabled= this.whereTxtArea.text?true:false;
      this.validateQueryButton.enabled= (this.whereTxtArea.text && this.setTxtArea.text)?true:false;
      this.radioSystem.enabled= true;
      this.radioEntity.enabled= true;
      this.workingDayOnlyCheck.enabled= true;
      this.runSqlUpdateTxt.enabled= true;
      this.startTimeTxt.enabled= true;
      this.endTimeTxt.enabled= true;
      this.ccyGrid.enabled= true;
      this.ccyGrid.selectable= true;
      this.addButton.enabled= true;
      this.addButton.buttonMode= true;
      this.changeButton.enabled= false;
      this.changeButton.buttonMode= false;
      this.deleteButton.enabled= false;
      this.deleteButton.buttonMode= false;
      this.saveButton.enabled=true;
      this.saveButton.buttonMode= true;
    }else {
      this.entityCombo.enabled= true;
      this.criticalTypeTxt.enabled= true ;
      this.descTxt.enabled= true;
      this.categoryCombo.enabled= true;
      this.orderInCategoryTxt.enabled= true;
      this.sumToCategCheck.enabled= true;
      this.sumToTotalCheck.enabled= true;
      this.reportableCheck.enabled= true;
      this.reportIndivPayCheck.enabled= true;
      this.enableUpdateProcessingCheck.enabled= true;
      this.setTxtArea.enabled= true;
      this.whereTxtArea.enabled= false;
      this.validateQueryButton.enabled= false;
      this.radioSystem.enabled= true;
      this.radioEntity.enabled= true;
      this.workingDayOnlyCheck.enabled= true;
      this.runSqlUpdateTxt.enabled= true;
      this.startTimeTxt.enabled= true;
      this.endTimeTxt.enabled= true;
      this.ccyGrid.enabled= true;
      this.ccyGrid.selectable= true;
      this.addButton.enabled= true;
      this.addButton.buttonMode= true;
      this.changeButton.enabled= false;
      this.changeButton.buttonMode= false;
      this.deleteButton.enabled= false;
      this.deleteButton.buttonMode= false;
      this.saveButton.enabled=true;
      this.saveButton.buttonMode= true;
    }
  }


  subScreenHandler(action) {
    this.win =  SwtPopUpManager.createPopUp(this, CcyExpectedTime, {
      title: " Currency expected time",
      action: action,
      ccyComboData: this.ccyComboData,
      gridData: this.ccyGrid.gridData,
      selectedRow: this.ccyGrid.selectedItem,
      selectedIndex: this.ccyGrid.selectedIndex
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '300';
    this.win.height = '160';
    this.win.showControls = true;
    this.win.id = "ccyExpectedTime";
    this.win.display();
  }

  deleteCcyHandler(){
    this.ccyGrid.removeSelected();
    this.ccyGrid.refresh();
  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


  /**                                                                                                                  
   * If a fault occurs with the connection with the server then display the lost connection label                      
   * @param event:FaultEvent                                                                                           
   **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  closeHandler(): void {
    ExternalInterface.call("close");
  }

  validateTime(textInput): any {
 
    let validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text  && validateFormatTime(textInput) == false) {
      this.swtAlert.warning(validTimeMessage, null );
      textInput.text = "";
      return false;
    }

  }

  checkRunSqlNumber(){
    if(Number(this.runSqlUpdateTxt.text) < 5) {
      this.swtAlert.error(SwtUtil.getPredictMessage('alert.criticalPaymentType.valueLessThanMin', null));
      this.runSqlUpdateTxt.text="5";
      return;
    }
    if(Number(this.runSqlUpdateTxt.text) > 1440) {
      this.swtAlert.error(SwtUtil.getPredictMessage('alert.criticalPaymentType.valueHigherThanMax', null));
      this.runSqlUpdateTxt.text="1440";
      return;
    }
  }


  validateQuery() {
    this.requestParams = [];

    this.requestParams['setClause'] = Encryptor.encode64(this.setTxtArea.text);
    this.requestParams['whereClause'] = Encryptor.encode64(this.whereTxtArea.text);

    this.logicUpdate.cbStart = this.startOfComms.bind(this);
    this.logicUpdate.cbStop = this.endOfComms.bind(this);
    this.logicUpdate.cbResult = (event) => {
      //ExternalInterface.call("close");
      //window.opener.instanceElement.updateData();
      this.logicValidateQuery(event);
    };
    this.logicUpdate.cbFault = this.inputDataFault.bind(this);
    this.logicUpdate.encodeURL = false;
    this.actionPath = "criticalPaymentType.do?";
    this.actionMethod = 'method=validateQuery';
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);
  }


acceptEventEventHandler(): void {
  // const message = SwtUtil.getPredictMessage('alert.columndelete', null);
  const message = SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoaccept', null);
  this.swtAlert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.acceptStatusHandler.bind(this), null);
}

rejectEventEventHandler(): void {
// const message = SwtUtil.getPredictMessage('alert.columndelete', null);
const message = SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoreject', null);
this.swtAlert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.rejectStatusHandler.bind(this), null);
}

acceptStatusHandler(closeEvent): void {
  if (closeEvent.detail == Alert.YES) {
    if (window.opener && window.opener.instanceElement) {
      this.changeStatusHandler('A');
    }
  }
}
rejectStatusHandler(closeEvent): void {
  if (closeEvent.detail == Alert.YES) {
    if (window.opener && window.opener.instanceElement) {
      this.changeStatusHandler('R');
    }
  }
}

changeStatusHandler(action) {
  let errorLocation = 0;
  try {
    this.actionPath = "maintenanceEvent.do?";
    this.actionMethod = 'method=updateMaintenanceEventStatus';
    errorLocation = 50;
    this.requestParams = [];
    this.requestParams['menuAccessId'] = this.parentMenuAccess;
    errorLocation = 60;
    this.requestParams['maintEventId'] =  this.maintEventId;
    errorLocation = 70;
    this.requestParams['action'] =  action;

    this.inputData.cbResult = (event) => {
      this.updateMaintenanceEventStatusResult(event);
    };
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 100;
    this.inputData.send(this.requestParams);
  } catch (error) {

    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "onLoad", errorLocation);
  }


}


updateMaintenanceEventStatusResult(event):void {
let errorLocation = 0;
try {
// Checks the inputData and stops the communication
if (this.inputData.isBusy()) {
  this.inputData.cbStop();
} else {
  this.jsonReader.setInputJSON(this.lastRecievedJSON);
  errorLocation = 10;
  //this.dataExport.enabled = true;
  if (this.jsonReader.getRequestReplyStatus()) {
    this.swtAlert.show(SwtUtil.getPredictMessage("maintenanceevent.details.alert.actionperfermored", null), "Warning", Alert.OK, null, this.closeWindow.bind(this));
    
  } else {
    if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
      this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
      }

    }
  }

} catch (error) {
  // log the error in ERROR LOG
  SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "cellClickEventHandler", errorLocation);
}
  
}

  /**
   * logicUpdateResult
   *
   * @param event: ResultEvent
   *
   * Method to get result of the group rules
   */
  logicUpdateResult(event): void {
    try {
      let message = SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninAdd', null);
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = event;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        if (JsonResult.getRequestReplyMessage() && JsonResult.getRequestReplyMessage().indexOf("DataIntegrityViolation") != -1) {
          this.swtAlert.error(message, null, Alert.OK, null, () => {
          });
        } else if (!this.jsonReader.getRequestReplyStatus()) {
          this.inputDataFault(event);
        } else {
          this.updateData();

          if(StringUtils.isTrue(this.requireAuthorisation)){
              this.swtAlert.show(SwtUtil.getPredictMessage("maintenanceevent.details.alert.actionneedauthorisation", null), "Warning", Alert.OK, null, this.closeWindow.bind(this));
            }else {
              if(this.maintEventId){
                  if(window.opener && window.opener.opener && window.opener.opener.instanceElement) {
                    window.opener.opener.instanceElement.updateData();
                  }
                  if(window.opener && window.opener.instanceElement) {
                     window.opener.close();
                  }
              
                   window.close();
              }else {
                 window.close();
              }
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
    }
  }

  /**
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  public updateData(): void {
    try {
      window.opener.instanceElement.updateDataFromChild();
      //this.parentDocument.updateDataFromChild();
    } catch (e) {
      console.log('error updateData', e)
    }
  }


closeWindow(event) {
  if(event.detail == Alert.OK) {
    //refresh parent
      if(this.maintEventId){
        if(window.opener && window.opener.opener && window.opener.opener.instanceElement) {
          window.opener.opener.instanceElement.updateData();
        }
        if(window.opener && window.opener.instanceElement) {
          window.opener.close();
        }
    
        window.close();
    }else {
      window.close();
    }
  }
}


getCategoryDesc(code) {
  var desc = null;

  switch (code) {
    case "V":
      desc = "Very Critical Payments";
      break;
    case "C":
      desc = "Critical Payments";
      break;
    case "N":
    desc = "Other Monitored Payments";
      break;
    default:
      break;
  }

  return desc;
}


/**
 * Method called to get an XML list of changed/addded/deleted data of spread process points 
 * 
 */
xmlDataCcyTimes(): any {
  let row = [];
  this.operationsList = new XML( "<operationsList/>");
  if (this.ccyExpectedTimesList != undefined) {
    for (var i = 0; i < this.ccyExpectedTimesList.getValues().length; i++) {
      row = [];
      row['CURRENCY_CODE'] = this.ccyExpectedTimesList.getValues()[i].crud_data.ccy;
      row['TIME'] = this.ccyExpectedTimesList.getValues()[i].crud_data.defaultExpectedTime;
      row['ENTITY'] = this.entityCombo.selectedLabel;
      row['CP_TYPE_ID'] = this.criticalTypeTxt.text;

      if (this.ccyExpectedTimesList.getValues()[i].crud_operation == "I") {
        this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'P_ILM_CRITICAL_PAYMENT_EXP', 'I', 'M'));
      }
      if (this.ccyExpectedTimesList.getValues()[i].crud_operation.substring(0, 1) == "U" && 
      this.ccyExpectedTimesList.getValues()[i].crud_data.defaultExpectedTime!=this.ccyExpectedTimesList.getValues()[i].crud_original_data.defaultExpectedTime) {
        this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'P_ILM_CRITICAL_PAYMENT_EXP', 'U', 'M'));
      }
      if (this.ccyExpectedTimesList.getValues()[i].crud_operation.substring(0, 1) == "D") {
        this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'P_ILM_CRITICAL_PAYMENT_EXP', 'D', 'M'));
      }
    }
  }
}

  /**
   * popupClosed
   * Method to close child windows when this screen is closed
   */
  popupClosed(): void {
    window.close();
    //SwtPopUpManager.getPopUpById("addGroupTitleWindow").close();

  }

  destoyAllTooltips(){
    $(".ui-tooltip" ).each(function( index ) {
        $(this).remove();
    });
  }

  amendEventHandler(){
    this.destoyAllTooltips();
    window.opener.instanceElement.setViewOrAmendSubScreenFromChild('change');
    const currentUrl = this.router.url;
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigateByUrl("/CriticalMvtUpdateDetail");
    });

  }

  cancelAmendEventHandler(){
    this.destoyAllTooltips();
    window.opener.instanceElement.setViewOrAmendSubScreenFromChild('view');
    const currentUrl = this.router.url;
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigateByUrl("/CriticalMvtUpdateDetail");
    });

  }



}


// Define lazy loading routes
const routes: Routes = [
  { path: '', component: CriticalMvtUpdateDetail }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CriticalMvtUpdateDetail],
  entryComponents: []
})
export class CriticalMvtUpdateDetailModule { }