<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingTop="5">
    <SwtCanvas width="100%">
      <Grid width="100%" height="100%" verticalGap="2">
        <GridRow #gridRowEntity>
          <GridItem width="80">
            <SwtLabel id="entityLabel" fontWeight="bold" #entityLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width="150">
            <HBox #entityComboHBox width="100%">
              <SwtComboBox id="entityCombo" #entityCombo  (change)="updateData()" 
              dataLabel="entity" width="135">
              </SwtComboBox>
            </HBox>            
          </GridItem>
          <GridItem width="10%">
            <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal" textAlign="left">
            </SwtLabel>
          </GridItem>
        </GridRow>
       
      </Grid>
    </SwtCanvas>
    <SwtCanvas #gridCanvas paddingBottom="5" styleName="canvasWithGreyBorder"  marginTop="10" border="false" width="100%" height="100%" minHeight="100"></SwtCanvas>
    <SwtCanvas id="canvasButtons" width="100%" height="35" marginTop="5">
      <HBox width="100%">
        <HBox paddingLeft="5" width="70%">
           <SwtButton id="addButton" #addButton (click)="doOpenChildWindow('add')" ></SwtButton>

            <SwtButton id="changeButton" #changeButton (click)="doOpenChildWindow('change')" 
              enabled="false"></SwtButton>

             <SwtButton id="viewButton" #viewButton (click)="doOpenChildWindow('view')" 
              enabled="false"></SwtButton>

            <SwtButton id="deleteButton" #deleteButton (click)="doDelete()" enabled="false" >
            </SwtButton>

            <SwtButton id="closeButton" #closeButton (click)="closeHandler()" ></SwtButton>
        </HBox>
        <HBox paddingRight="5" width="30%">
          <HBox width="80%">
           
          </HBox>
          <HBox width="20%" paddingTop="2">
            <SwtLoadingImage #loadingImage></SwtLoadingImage>
          </HBox>

        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>