import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { SwtModule, SwtTextArea, CommonService, SwtAlert } from 'swt-tool-box';
declare var require: any;

var prettyData = require('pretty-data');

@Component({
  selector: 'pcmessage-details',
  templateUrl: './MessageDetails.html',
  styleUrls: ['./MessageDetails.css']
})
export class MessageDetails extends SwtModule implements OnInit {

  @ViewChild('messageText') messageText: SwtTextArea;


  private swtAlert: SwtAlert;
  public messageId: string;
  public messageBody: string;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);

  }

  ngOnDestroy(): any {
  }


  ngOnInit() {

    var formattedXml = prettyData.pd.xml(this.messageBody);
    formattedXml = this.htmlEntities(formattedXml);
    
    this.messageText.htmlText = formattedXml ;
  }

  htmlEntities(str) {
    return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
    replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
}

}
