/**
 * Utility class for handling boolean value parsing from various data types
 */
export class BooleanParser {
    /**
     * Parses a value that could be a string, boolean, or undefined into a boolean
     * @param value - The value to parse (can be string 'true'/'false', boolean true/false, or undefined)
     * @param defaultValue - Optional default value if the input is undefined (defaults to false)
     * @returns boolean
     * 
     * @example
     * parseBooleanValue(true) // returns true
     * parseBooleanValue('true') // returns true
     * parseBooleanValue('false') // returns false
     * parseBooleanValue(undefined, true) // returns true
     */
    static parseBooleanValue(value: string | boolean | undefined | null, defaultValue: boolean = false): boolean {
        // Handle undefined or null
        if (value === undefined || value === null) {
            return defaultValue;
        }

        // Handle boolean
        if (typeof value === 'boolean') {
            return value;
        }

        // Handle string
        if (typeof value === 'string') {
            const normalizedValue = value.toLowerCase().trim();
            // Consider only explicit "false" as false, everything else as true
            return normalizedValue !== 'false';
        }

        return defaultValue;
    }

    /**
     * Strict version that only accepts specific boolean string values
     * @param value - The value to parse
     * @param defaultValue - Optional default value if the input is invalid (defaults to false)
     * @returns boolean
     * 
     * @example
     * parseStrictBooleanValue('true') // returns true
     * parseStrictBooleanValue('false') // returns false
     * parseStrictBooleanValue('yes') // returns default value
     */
    static parseStrictBooleanValue(value: string | boolean | undefined | null, defaultValue: boolean = false): boolean {
        // Handle undefined or null
        if (value === undefined || value === null) {
            return defaultValue;
        }

        // Handle boolean
        if (typeof value === 'boolean') {
            return value;
        }

        // Handle string
        if (typeof value === 'string') {
            const normalizedValue = value.toLowerCase().trim();
            if (normalizedValue === 'true') return true;
            if (normalizedValue === 'false') return false;
            return defaultValue;
        }

        return defaultValue;
    }
}