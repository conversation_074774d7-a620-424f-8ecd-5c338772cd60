import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild, HostListener } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {
  SwtModule, CommonService, SwtAlert, HTTPComms, JSONReader, SwtUtil, ExternalInterface,
  SwtLabel, SwtCanvas, SwtButton, SwtComboBox, SwtTextInput, SwtCommonGrid, SwtCommonGridPagination,
  SwtHelpButton, SwtLoadingImage, HBox, VBox, SwtRadioButtonGroup, SwtRadioItem,
  SwtToolBoxModule,
  Keyboard,
  focusManager
} from 'swt-tool-box';
import { PaginationChangedArgs } from 'angular-slickgrid';

declare var window: any;
declare var initialParams: any; // From JSP shell
declare var baseURL: string;    // From JSP shell

@Component({
  selector: 'app-correspondent-acct-maintenance',
  templateUrl: './CorrespondentAcctMaintenance.html',
  // styleUrls: ['./CorrespondentAcctMaintenance.css']
})
export class CorrespondentAcctMaintenance extends SwtModule implements OnInit {

  // ViewChilds for Filters
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('entityDescLabel') entityDescLabel: SwtLabel;

  @ViewChild('messageTypeSearchLabel') messageTypeSearchLabel: SwtLabel;
  @ViewChild('searchMessageTypeOptions') searchMessageTypeOptions: SwtRadioButtonGroup;
  @ViewChild('predefinedMsgTypeRadio') predefinedMsgTypeRadio: SwtRadioItem;
  @ViewChild('manualMsgTypeRadio') manualMsgTypeRadio: SwtRadioItem;

  @ViewChild('actualMessageTypeLabel') actualMessageTypeLabel: SwtLabel;
  @ViewChild('predefinedMsgTypeBox') predefinedMsgTypeBox: HBox;
  @ViewChild('messageTypeCombo') messageTypeCombo: SwtComboBox;
  @ViewChild('manualMsgTypeBox') manualMsgTypeBox: HBox;
  @ViewChild('otherMessageTypeInput') otherMessageTypeInput: SwtTextInput;

  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('currencyCombo') currencyCombo: SwtComboBox;
  @ViewChild('currencyDescLabel') currencyDescLabel: SwtLabel;

  // ViewChilds for Grid and Pagination
  @ViewChild('pageBoxTop') pageBoxTop: HBox;
  @ViewChild('numStepperTop') numStepperTop: SwtCommonGridPagination;
  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
  private mainGrid: SwtCommonGrid;

  // ViewChilds for Buttons
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('helpButton') helpButton: SwtHelpButton;
  @ViewChild('printButton') printButton: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  // Component State
    private actionMethod: string = "";
  private actionPath: string = "correspondentacctmaintenance.do?";
  private requestParams: any = {};
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON: any;
  private swtAlert: SwtAlert;
  private inputData: HTTPComms;
  private deleteOperationData: HTTPComms;
  private currencyAccessData: HTTPComms;
  private effectiveBaseURL: string;

  public isPredefinedMessageType: boolean = true;

  private currentSort: string = "0|true";
  private currentFilter: string = "All|All|All|All|All";
  public maxPage: number = 1;
  private currentPage: number = 1;
  private totalCount: number = 0;

  private menuEntityCurrGrpAccess: string = "1";
  private initialButtonPerms: any = { add: true, change: true, delete: true };
  private selectedCorrespondentAcctData: any = null; // Stores the full event data from grid click

  STATIC_COLUMN_INDEX: Record<string, number> = {
    messagetype: 0,
    currencycode: 1,
    corresaccid: 2,
    accountid: 3,
    accountname: 4
  };

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    this.inputData = new HTTPComms(this.commonService);
    this.deleteOperationData = new HTTPComms(this.commonService);
    this.currencyAccessData = new HTTPComms(this.commonService);
    this.effectiveBaseURL = SwtUtil.getBaseURL();
    window.CorrespondentAcctMaintenanceComponent = this;
  }

  ngOnInit() {
    this.setupLabelsAndTooltips();
    this.initializeGrid();
    this.applyInitialParameters();
    this.updateButtonStates();
  }

  private setupLabelsAndTooltips(): void {
    if (this.entityLabel) this.entityLabel.text = SwtUtil.getPredictMessage('entity.id', null) + '*';
    if (this.messageTypeSearchLabel) this.messageTypeSearchLabel.text = SwtUtil.getPredictMessage('correspondentaccountmaintenance.messageType', null) + '*';
    if (this.predefinedMsgTypeRadio) this.predefinedMsgTypeRadio.label = SwtUtil.getPredictMessage('correspondentaccountmaintenance.predefinedMessageType', null);
    if (this.manualMsgTypeRadio) this.manualMsgTypeRadio.label = SwtUtil.getPredictMessage('correspondentaccountmaintenance.manualMessageType', null);
    if (this.actualMessageTypeLabel) this.actualMessageTypeLabel.text = "";
    if (this.currencyLabel) this.currencyLabel.text = SwtUtil.getPredictMessage('correspondentaccountmaintenance.currencyCode', null) + '*';

    if (this.addButton) this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    if (this.changeButton) this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    if (this.deleteButton) this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    if (this.closeButton) this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    if (this.printButton) this.printButton.toolTip = SwtUtil.getPredictMessage('tooltip.printScreen', null);
    if (this.helpButton) this.helpButton.toolTip = SwtUtil.getPredictMessage('tooltip.helpScreen', null);

    this.entityCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectEntity', null);
    this.predefinedMsgTypeRadio.toolTip= SwtUtil.getPredictMessage('correspondentaccountmaintenance.predefinedMessageType', null);
    this.manualMsgTypeRadio.toolTip= SwtUtil.getPredictMessage('correspondentaccountmaintenance.manualMessageType', null);
    this.otherMessageTypeInput.toolTip= SwtUtil.getPredictMessage('tooltip.manualMessageType', null);
    this.messageTypeCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectMessageType', null);
    this.currencyCombo.toolTip= SwtUtil.getPredictMessage('tooltip.selectCurrencyId', null);
    this.addButton.toolTip= SwtUtil.getPredictMessage('tooltip.addNewCorrespondentAccount', null);
    this.changeButton.toolTip= SwtUtil.getPredictMessage('tooltip.changeSelCorrespondentAcct', null);
    this.deleteButton.toolTip= SwtUtil.getPredictMessage('tooltip.deleteSelCorrespondentAcct', null);
    this.closeButton.toolTip= SwtUtil.getPredictMessage('tooltip.close', null);

  }

  private initializeGrid(): void {
    if (this.dataGridContainer) {
        this.mainGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
        if (this.mainGrid) {
            this.mainGrid.clientSideSort = false;
            this.mainGrid.clientSideFilter = false;
            this.mainGrid.paginationComponent = this.numStepperTop;

            this.mainGrid.ITEM_CLICK.subscribe((event) => this.onGridItemClick(event)); // Pass full event
            this.mainGrid.onSortChanged = () => this.fetchDataWithCurrentState(1);
            this.mainGrid.onFilterChanged = () => this.fetchDataWithCurrentState(1);
    this.mainGrid.onPaginationChanged = (args: PaginationChangedArgs) => {
      console.log("🚀 ~ ngOnInit ~ this.currentPage:", this.currentPage,this.numStepperTop.value)
      if (this.numStepperTop.value !== this.currentPage) {
        this.fetchDataWithCurrentState(this.numStepperTop.value);
      }
    };
        }
    }
  }
  
  private applyInitialParameters(): void {
    if (typeof initialParams !== 'undefined' && initialParams) {
      this.menuEntityCurrGrpAccess = initialParams.menuEntityCurrGrpAccess || "1";
      this.initialButtonPerms = {
          add: true,
          change: true,
          delete: true
      };
      if(this.entityCombo && initialParams.defaultEntityId) this.entityCombo.selectedLabel = initialParams.defaultEntityId;
      if(this.currencyCombo && initialParams.defaultCurrencyCode) this.currencyCombo.selectedLabel = initialParams.defaultCurrencyCode;
      if(this.searchMessageTypeOptions && initialParams.defaultSearchMessageType) {
        this.searchMessageTypeOptions.selectedValue = initialParams.defaultSearchMessageType;
        //this.isPredefinedMessageType = initialParams.defaultSearchMessageType === 'P';
        if(this.isPredefinedMessageType && this.messageTypeCombo && initialParams.defaultMessageType) {
            this.messageTypeCombo.selectedLabel = initialParams.defaultMessageType;
        } else if (!this.isPredefinedMessageType && this.otherMessageTypeInput && initialParams.defaultOtherMessageType) {
            this.otherMessageTypeInput.text = initialParams.defaultOtherMessageType;
        }
      }
    }
    this.updateMessageTypeVisibility();
  }

  onLoad() {
    if(this.loadingImage) this.loadingImage.setVisible(false);
    this.configureHttpCallbacks(this.inputData, (event) => this.initialInputDataResult(event));
    
    if (typeof initialParams !== 'undefined' && initialParams && initialParams.actionError && initialParams.actionError !== 'null' && initialParams.actionError !== 'undefined') {
        this.swtAlert.error(initialParams.actionError);
    }
    
    this.fetchDataWithCurrentState(1, true);
  }

  private configureHttpCallbacks(commsInstance: HTTPComms, resultCallback: (event: any) => void): void {
    if(commsInstance) {
        commsInstance.cbStart = this.startOfComms.bind(this);
        commsInstance.cbStop = this.endOfComms.bind(this);
        commsInstance.cbResult = resultCallback;
        commsInstance.cbFault = this.inputDataFault.bind(this);
        commsInstance.encodeURL = false;
    }
  }
  
  private _pendingCurrencyAccessResolve: ((value: boolean | PromiseLike<boolean>) => void) | null = null;

  private currencyAccessResult(event: any): void {
    const reader = new JSONReader();
    reader.setInputJSON(event);
    let accessGranted = false;
    if (reader.getRequestReplyStatus()) {
        const singletons = reader.getSingletons();
        if (singletons) {
             // Ensure this matches the singleton name from Java for checkCurrencyAccessAngular
            accessGranted = singletons.accessGrantedFlag === "true";
        }
    } else {
        this.handleRequestError();
    }
    if (this._pendingCurrencyAccessResolve) {
        this._pendingCurrencyAccessResolve(accessGranted);
        this._pendingCurrencyAccessResolve = null;
    }
  }

  private initialInputDataResult(event: any): void {

    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);

    if (this.jsonReader.getRequestReplyStatus()) {
      if (this.entityCombo) this.entityCombo.setComboData(this.jsonReader.getSelects());
      if (this.currencyCombo) this.currencyCombo.setComboData(this.jsonReader.getSelects());
      if (this.messageTypeCombo) this.messageTypeCombo.setComboData(this.jsonReader.getSelects());

      const singletons = this.jsonReader.getSingletons();
      if (singletons) {
          if (this.entityCombo && !this.entityCombo.selectedLabel && singletons.defaultEntityId) this.entityCombo.selectedLabel = singletons.defaultEntityId;
          if (this.currencyCombo && !this.currencyCombo.selectedLabel && singletons.defaultCurrencyCode) this.currencyCombo.selectedLabel = singletons.defaultCurrencyCode;
          
          if(this.searchMessageTypeOptions && this.searchMessageTypeOptions.selectedValue === 'P' && this.messageTypeCombo && !this.messageTypeCombo.selectedLabel && singletons.defaultMessageType){
            this.messageTypeCombo.selectedLabel = singletons.defaultMessageType;
          } else if (this.searchMessageTypeOptions && this.searchMessageTypeOptions.selectedValue === 'M' && this.otherMessageTypeInput && !this.otherMessageTypeInput.text && singletons.defaultOtherMessageType){
            this.otherMessageTypeInput.text = singletons.defaultOtherMessageType;
          }
          
          if (this.entityDescLabel && this.entityCombo) this.entityDescLabel.text = this.entityCombo.selectedValue || '';
          if (this.currencyDescLabel && this.currencyCombo) this.currencyDescLabel.text = this.currencyCombo.selectedValue || '';

          this.menuEntityCurrGrpAccess = singletons.menuEntityCurrGrpAccess || this.menuEntityCurrGrpAccess;
      }

      this.processGridData(event);
      this.updateButtonStates();
    } else {
      this.handleRequestError();
    }
  }

  private regularInputDataResult(event: any): void {
    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    if (this.jsonReader.getRequestReplyStatus()) {
      this.processGridData(event);
    } else {
      this.handleRequestError();
    }
  }

  private processGridData(event: any): void {
    this.currentPage = 1; this.maxPage = 1; this.totalCount = 0;
    let listData, gridData, pagingData, metadata, rowsData;

    if (event && event.correspondentAcctList) { listData = event.correspondentAcctList; } // Check root element name
    if (listData && listData.grid) { gridData = listData.grid; }
    if (gridData && gridData.paging) { pagingData = gridData.paging; }
    if (gridData && gridData.metadata) { metadata = gridData.metadata; }
    if (gridData && gridData.rows) { rowsData = gridData.rows; }

    if (pagingData) {
      this.currentPage = Number(pagingData.currentpage || 1);
      this.maxPage = Number(pagingData.maxpage || 1);
      this.totalCount = Number(pagingData.totalcount || (rowsData && typeof rowsData.size === 'number' ? rowsData.size : 0));
    }

    if (this.numStepperTop) {
      this.numStepperTop.value = this.currentPage;
      this.numStepperTop.maximum = this.maxPage;
      this.numStepperTop.minimum = 1;
    }
    if (this.pageBoxTop) this.pageBoxTop.visible = this.maxPage > 1;

    if (gridData) {
      this.currentSort = gridData.currentSort || this.currentSort;
      this.currentFilter = gridData.currentFilter || this.currentFilter;
    }

    if (this.mainGrid && metadata && metadata.columns) {
      this.mainGrid.CustomGrid({ columns: metadata.columns });
    }

    if (this.mainGrid && rowsData && typeof rowsData.size === 'number' && rowsData.size > 0) {
      this.mainGrid.gridData = rowsData;
      if (typeof (this.mainGrid as any).setRowSize === 'function') { (this.mainGrid as any).setRowSize(this.totalCount); }
      else if (this.mainGrid.hasOwnProperty('setRowSize')) { (this.mainGrid as any).setRowSize = this.totalCount; }
    } else if (this.mainGrid) {
      this.mainGrid.gridData = { size: 0, row: [] };
      if (typeof (this.mainGrid as any).setRowSize === 'function') { (this.mainGrid as any).setRowSize(0); }
      else if (this.mainGrid.hasOwnProperty('setRowSize')) { (this.mainGrid as any).setRowSize = 0; }
    }
    this.selectedCorrespondentAcctData = null;
    if(this.mainGrid) this.mainGrid.selectedIndex = -1;
    this.updateButtonStates();
  }

  private handleRequestError(): void {
    let msg = 'An unknown error occurred.';
    let loc = '';
    if (this.lastRecievedJSON && this.lastRecievedJSON.request_reply) {
        const reply = this.lastRecievedJSON.request_reply;
        msg = (reply.message_text && reply.message_text.content) ? reply.message_text.content : (reply.message_text || 'Unknown error');
        loc = (reply.message_location && reply.message_location.content) ? reply.message_location.content : (reply.message_location || '');
    }
    this.swtAlert.error(`${msg}\n${loc}`, "Error");
  }

  fetchDataWithCurrentState(page: number = 1, isInitialLoad: boolean = false): void {
    this.currentPage = page;
    if(isInitialLoad) {
        this.configureHttpCallbacks(this.inputData, (event) => this.initialInputDataResult(event));
    } else {
        this.configureHttpCallbacks(this.inputData, (event) => this.regularInputDataResult(event));
    }
    
    this.requestParams = {
      entityId: this.entityCombo ? this.entityCombo.selectedLabel : '',
      searchMessageType: this.searchMessageTypeOptions ? this.searchMessageTypeOptions.selectedValue : 'P',
      messageType: (this.isPredefinedMessageType && this.messageTypeCombo) ? this.messageTypeCombo.selectedLabel : '',
      otherMessageType: (!this.isPredefinedMessageType && this.otherMessageTypeInput) ? this.otherMessageTypeInput.text : '',
      currencyCode: this.currencyCombo ? this.currencyCombo.selectedLabel : '',
      selectedSort: this.buildSortString(),
      selectedFilter: this.buildFilterString(),
      currentPage: this.currentPage,
    };
    this.actionMethod = 'method=displayAngular';
    this.inputData.url = this.effectiveBaseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  onFilterChange(fromEntityCombo: boolean = false): void {
    if (fromEntityCombo) {
        if (this.otherMessageTypeInput) this.otherMessageTypeInput.text = '';
        if (this.messageTypeCombo && this.messageTypeCombo.dataProvider && this.messageTypeCombo.dataProvider.length > 0) {
            this.messageTypeCombo.selectedIndex = 0;
            this.messageTypeCombo.selectedLabel = this.messageTypeCombo.dataProvider[0].label || this.messageTypeCombo.dataProvider[0];
        }
        if (this.currencyCombo && this.currencyCombo.dataProvider && this.currencyCombo.dataProvider.length > 0) {
            this.currencyCombo.selectedIndex = 0;
            this.currencyCombo.selectedLabel = this.currencyCombo.dataProvider[0].label || this.currencyCombo.dataProvider[0];
        }
    }
    if(this.entityCombo && this.entityDescLabel) this.entityDescLabel.text = this.entityCombo.selectedValue || '';
    if(this.currencyCombo && this.currencyDescLabel) this.currencyDescLabel.text = this.currencyCombo.selectedValue || '';
    this.fetchDataWithCurrentState(1,fromEntityCombo);
  }
@HostListener('document:keyup', ['$event'])
  handleDocumentKeyUp(event: KeyboardEvent): void {
    const targetElement = event.target as HTMLElement;

    // Check if the event originated from your specific SwtTextInput's internal input.
    // This assumes SwtTextInput renders an <input> element that either:
    // 1. Inherits or is assigned the ID "otherMessageTypeInput".
    // 2. Is a child of an element with this ID.
    
    let isTargetInput = false;

    if (targetElement && typeof targetElement.id === 'string' && targetElement.id === 'otherMessageTypeInput') {
      // This case handles if the SwtTextInput component itself is the target 
      // AND it's the element that directly receives keyboard input focus (less common for wrapper components),
      // OR if the SwtTextInput component cleverly makes its *internal native input* have this ID.
      isTargetInput = true;
    } else if (targetElement && targetElement.closest('#otherMessageTypeInput')) {
      // This is more robust: checks if the actual event target (likely a native <input>)
      // is a descendant of the element with id="otherMessageTypeInput".
      // This also assumes the actual input element inside SwtTextInput can receive focus.
      isTargetInput = true;
    }
    

    if (isTargetInput) {
      const inputValue = (event.target as HTMLInputElement).value; // Get value if it's an input

      if (event.key === 'Enter') {
        console.log("🚀 Enter key pressed on #otherMessageTypeInput!");
        // Your logic for Enter key
        this.onFilterChange();
      }
    } else {
      // console.log(`🚀 Document KeyUp from other target:`, event.key, targetElement);
    }
  }




  onSearchMessageTypeChange(): void {
    if (this.searchMessageTypeOptions) {
        this.isPredefinedMessageType = this.searchMessageTypeOptions.selectedValue === 'P';
    }

    this.updateMessageTypeVisibility();
    if ((this.isPredefinedMessageType && this.messageTypeCombo && this.messageTypeCombo.selectedLabel) || 
        (!this.isPredefinedMessageType && this.otherMessageTypeInput && this.otherMessageTypeInput.text)) {

          this.fetchDataWithCurrentState(1);
    } else if (this.isPredefinedMessageType && this.otherMessageTypeInput) {
        this.otherMessageTypeInput.text = "";
        // Optionally fetch if messageTypeCombo has a default or previously selected value
        if (this.messageTypeCombo) this.fetchDataWithCurrentState(1);
    } else if (!this.isPredefinedMessageType && this.messageTypeCombo) {
        if(this.messageTypeCombo) this.messageTypeCombo.selectedIndex = -1;
        // Optionally fetch if otherMessageTypeInput has a default or previously set value
        if (this.otherMessageTypeInput && this.otherMessageTypeInput.text) this.fetchDataWithCurrentState(1);
    }
  }
  
  private updateMessageTypeVisibility(): void {
    if (this.predefinedMsgTypeBox) this.predefinedMsgTypeBox.visible = this.isPredefinedMessageType;
    if (this.manualMsgTypeBox) this.manualMsgTypeBox.visible = !this.isPredefinedMessageType;
  }

  onGridItemClick(eventData: any): void { // eventData is the full event object from grid
    console.log("🚀 ~ CorrespondentAcctMaintenance ~ onGridItemClick ~ eventData:", eventData)
    if (eventData && eventData.target && eventData.target.data) {
        this.selectedCorrespondentAcctData = eventData.target.data; // Store actual row data
    } else {
        this.selectedCorrespondentAcctData = null;
    }
    this.updateButtonStates();
  }
  
  private applyInitialButtonPermissions(): void {
    const canInteract = this.menuEntityCurrGrpAccess === "0";
    if (this.addButton) this.addButton.enabled = canInteract && (this.initialButtonPerms.add !== undefined ? this.initialButtonPerms.add : true);
  }

  private updateButtonStates(): void {
    const isSelected = !!this.selectedCorrespondentAcctData;
    console.log("🚀 ~ CorrespondentAcctMaintenance ~ updateButtonStates ~ isSelected:", isSelected)
    const canInteract = this.menuEntityCurrGrpAccess === "0";
    console.log("🚀 ~ CorrespondentAcctMaintenance ~ updateButtonStates ~ canInteract:", canInteract)
    console.log("🚀 ~ CorrespondentAcctMaintenance ~ updateButtonStates ~ this.initialButtonPerms:", this.initialButtonPerms)

    if (this.addButton) {
        this.addButton.enabled = canInteract && (this.initialButtonPerms.add !== undefined ? this.initialButtonPerms.add : true);
    }
    if (this.changeButton) this.changeButton.enabled = isSelected && canInteract && (this.initialButtonPerms.change !== undefined ? this.initialButtonPerms.change : true);
    if (this.deleteButton) this.deleteButton.enabled = isSelected && canInteract && (this.initialButtonPerms.delete !== undefined ? this.initialButtonPerms.delete : true);
  }

  onAddClick(): void {
    if (!this.addButton || !this.addButton.enabled) return;
    let actualMessageType = '';
    if (this.searchMessageTypeOptions && this.searchMessageTypeOptions.selectedValue === 'P' && this.messageTypeCombo) {
        actualMessageType = this.messageTypeCombo.selectedLabel;
    } else if (this.searchMessageTypeOptions && this.searchMessageTypeOptions.selectedValue === 'M' && this.otherMessageTypeInput) {
        actualMessageType = this.otherMessageTypeInput.text;
    }
    const params = {
      entityId: this.entityCombo ? this.entityCombo.selectedLabel : '',
      messageType: actualMessageType,
      currencyCode: this.currencyCombo ? this.currencyCombo.selectedLabel : '',
      entityName: this.entityCombo ? this.entityCombo.selectedValue : ''
    };
    const url = `correspondentacctmaintenance.do?method=add&${this.buildQueryString(params)}`;
    if (typeof ExternalInterface.call === 'function') {
        ExternalInterface.call("openLegacyWindow", url, 'correspondentacctaddwindow', 'left=50,top=150,width=850,height=290,toolbar=0,resizable=yes,status=yes,scrollbars=no', 'true');
    }
  }

  onChangeClick(): void {
    if (!this.changeButton || !this.changeButton.enabled || !this.selectedCorrespondentAcctData) return;
    const rowData = this.selectedCorrespondentAcctData; // Already points to actual row data due to onGridItemClick change
    
    let selectedMsgTypeContent = '';
    if (rowData.messageType && rowData.messageType !== undefined) {
        selectedMsgTypeContent = rowData.messageType;
    } 
      
    let selectedCurrencyCodeContent = '';
    if(rowData.currencyCode && rowData.currencyCode !== undefined) {
        selectedCurrencyCodeContent = rowData.currencyCode;
    } 
    
    let selectedCorresAccIdContent = '';
    if(rowData.corresAccId && rowData.corresAccId !== undefined) {
        selectedCorresAccIdContent = rowData.corresAccId;
    }

      let accIdContent = '';
    if(rowData.accountId && rowData.accountId !== undefined) {
        accIdContent = rowData.accountId;
    }
      

      
    const params = {
      entityId: this.entityCombo ? this.entityCombo.selectedLabel : '',
      entityName: this.entityCombo ? this.entityCombo.selectedValue : '',
      selectedRowMessageType: selectedMsgTypeContent,
      selectedRowCurrencyCode: selectedCurrencyCodeContent,
      selectedCorresAccId: selectedCorresAccIdContent,
      selectedRowAccountId: accIdContent,
    };
    const url = `correspondentacctmaintenance.do?method=change&${this.buildQueryString(params)}`;
    console.log("🚀 ~ CorrespondentAcctMaintenance ~ onChangeClick ~ url:", url)
    if (typeof ExternalInterface.call === 'function') {
        ExternalInterface.call("openLegacyWindow", url, 'correspondentacctchangewindow', 'left=50,top=150,width=850,height=290,toolbar=0,resizable=yes,status=yes,scrollbars=no', 'true');
    }
  }
  
  private _currentCorrespAcctForDelete: any = null; // To store context for delete confirmation

  async onDeleteClick(): Promise<void> {
    if (!this.deleteButton || !this.deleteButton.enabled || !this.selectedCorrespondentAcctData) return;
    
    this._currentCorrespAcctForDelete = this.selectedCorrespondentAcctData; // Store the selected row data

    let currencyForCheck = '';
    if (this._currentCorrespAcctForDelete.currencyCode ) {
        currencyForCheck = this._currentCorrespAcctForDelete.currencyCode;
    } 

    const hasAccess = await this.checkCurrencyAccessAngular(
        this.entityCombo ? this.entityCombo.selectedLabel : '',
        currencyForCheck
    );

    if (!hasAccess) {
        this.swtAlert.error("Access denied for this currency operation.");
        this._currentCorrespAcctForDelete = null;
        return;
    }
    
    const confirmMessage =SwtUtil.getPredictMessage('confirm.delete', null);
    const confirmTitle = SwtUtil.getPredictMessage('screen.alert.confirm', null);
    this.swtAlert.confirm(confirmMessage, confirmTitle, SwtAlert.YES | SwtAlert.NO, null, this.handleDeleteConfirmation.bind(this), null);
  }
  
  private async checkCurrencyAccessAngular(entityId: string, currencyCode: string): Promise<boolean> {
    return new Promise((resolve) => {
        if (!this.currencyAccessData) {
            console.error("currencyAccessData HTTPComms not initialized");
            resolve(false);
            return;
        }
        this.configureHttpCallbacks(this.currencyAccessData, (event) => {
            const reader = new JSONReader();
            reader.setInputJSON(event);
            let accessGranted = false;
            if (reader.getRequestReplyStatus()) {
                const singletons = reader.getSingletons();
                if (singletons) {
                     accessGranted = singletons.accessGrantedFlag === "true";
                }
            } else {
                this.handleRequestError();
            }
             if (this._pendingCurrencyAccessResolve) {
                this._pendingCurrencyAccessResolve(accessGranted);
                this._pendingCurrencyAccessResolve = null;
            }
        });
         this._pendingCurrencyAccessResolve = resolve;
        const params = { entityId: entityId, currencyCode: currencyCode };
        this.currencyAccessData.url = `${this.effectiveBaseURL}${this.actionPath}method=checkCurrencyAccessAngular&${this.buildQueryString(params)}`;
        this.currencyAccessData.send({});
    });
  }

  private handleDeleteConfirmation(result: any): void {
  if (result && result.detail === SwtAlert.YES && this._currentCorrespAcctForDelete) {
    const dataToDelete = this._currentCorrespAcctForDelete; // Use the stored data

    let msgTypeContent = '';
    // Corrected: Assumes dataToDelete.messageType holds the direct value and uses camelCase.
    if (dataToDelete.messageType && dataToDelete.messageType !== undefined) {
      msgTypeContent = dataToDelete.messageType;
    }

    let currCodeContent = '';
    // Corrected: Assumes dataToDelete.currencyCode holds the direct value and uses camelCase.
    if (dataToDelete.currencyCode && dataToDelete.currencyCode !== undefined) {
      currCodeContent = dataToDelete.currencyCode;
    }

    let corresAccIdContent = '';
    // Corrected: Assumes dataToDelete.corresAccId holds the direct value and uses camelCase.
    if (dataToDelete.corresAccId && dataToDelete.corresAccId !== undefined) {
      corresAccIdContent = dataToDelete.corresAccId;
    }

    this.configureHttpCallbacks(this.deleteOperationData, (event) => {
      this.jsonReader.setInputJSON(event);
      if (this.jsonReader.getRequestReplyStatus()) {
        this.fetchDataWithCurrentState(this.currentPage);
      } else {
        this.handleRequestError();
      }
    });

    this.requestParams = {
      entityId: this.entityCombo ? this.entityCombo.selectedLabel : '',
      selectedRowMessageType: msgTypeContent,
      selectedRowCurrencyCode: currCodeContent,
      selectedCorresAccId: corresAccIdContent,
      currentPage: this.currentPage,
      selectedSort: this.buildSortString(),
      selectedFilter: this.buildFilterString()
    };

    const deleteUrl = `${this.effectiveBaseURL}${this.actionPath}method=deleteCorrespondentAcctAngular&${this.buildQueryString(this.requestParams)}`;
    this.deleteOperationData.url = deleteUrl;
    console.log("🚀 ~ CorrespondentAcctMaintenance ~ handleDeleteConfirmation ~ deleteUrl:", deleteUrl)
    this.deleteOperationData.send({});
  }
  this._currentCorrespAcctForDelete = null; // Clear stored data
}

  
  public refreshGridData(): void { // Called by JSP shell
    if (this.inputData && (!this.inputData.isBusy || (this.inputData.isBusy && !this.inputData.isBusy()))) { // Check if isBusy is a function
        //this.fetchDataWithCurrentState(this.numStepperTop && this.numStepperTop.value ? this.numStepperTop.value : this.currentPage);
        this.fetchDataWithCurrentState(1);
    }
  }
  
  public handleChildMessage(data: any): void {
    if (data && data.action === 'refreshParentGrid') {
        this.refreshGridData();
    }
  }

  onCloseClick(): void {
    if (typeof ExternalInterface.call === 'function') {
        ExternalInterface.call("close");
    }
  }

  onHelpClick(): void {
    if (typeof ExternalInterface.call === 'function') {
        ExternalInterface.call("openHelpWindow", 'print', 'Correspondent Account Maintenance');
    }
  }

  onPrintClick(): void {
    if (typeof ExternalInterface.call === 'function') {
        ExternalInterface.call("jsPrintPage");
    }
  }

  private buildSortString(): string {
    let id = "";
    if (this.mainGrid && this.mainGrid.sortedGridColumnId) {
        id = this.mainGrid.sortedGridColumnId.toLowerCase();
    }
    if (!id) return this.currentSort;
    
    const idx = this.STATIC_COLUMN_INDEX[id];
    if (idx === undefined) return this.currentSort;

    let dir = 'false';
    console.log("🚀 ~ CorrespondentAcctMaintenance ~ buildSortString ~ dir:", dir)
    if (this.mainGrid && this.mainGrid.sortDirection !== undefined && this.mainGrid.sortDirection !== null) {
        dir = String(this.mainGrid.sortDirection).toLowerCase();
      }
      console.log("🚀 ~ CorrespondentAcctMaintenance ~ buildSortString ~ dir:", dir)
    return `${idx}|${dir}`;
  }

  private buildFilterString(): string {
    const staticColumnValues = Object.values(this.STATIC_COLUMN_INDEX).filter(v => typeof v === 'number') as number[];
    const maxIdx = staticColumnValues.length > 0 ? Math.max(...staticColumnValues) : -1;

    if (maxIdx === -1 && this.currentFilter) return this.currentFilter; // Return current if no columns
    if (maxIdx === -1) return "";


    const filterArr: string[] = Array(maxIdx + 1).fill("All");
    const filters = (this.mainGrid && this.mainGrid.filters) ? this.mainGrid.filters : [];
    filters.forEach(f => {
      if (f && f.columnId) {
        const colId = f.columnId.toLowerCase();
        const idx = this.STATIC_COLUMN_INDEX[colId];
        if (idx !== undefined && f.operator === 'EQ' && f.searchTerms && f.searchTerms.length > 0) {
          filterArr[idx] = String(f.searchTerms[0]);
        }
      }
    });
    return filterArr.join('|');
  }

  private buildQueryString(params: any): string {
    const parts: string[] = [];
    for (const key in params) {
        if (params.hasOwnProperty(key) && params[key] !== undefined && params[key] !== null) {
             parts.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(params[key]))}`);
        }
    }
    return parts.join('&');
  }

  startOfComms(): void {
    if(this.loadingImage) this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    if(this.loadingImage) this.loadingImage.setVisible(false);
  }

  private inputDataFault(event: any): void {
    this.endOfComms();
    const faultInfo = (event && event.fault) ? event.fault : {};
    const msg = `${faultInfo.faultString || 'Communication fault.'}\n${faultInfo.faultCode || ''}\n${faultInfo.faultDetail || ''}`;
    this.swtAlert.error(msg, "Error");
  }
}

const routes: Routes = [
  { path: '', component: CorrespondentAcctMaintenance }
];
export const routing: ModuleWithProviders<RouterModule> = RouterModule.forChild(routes);

@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CorrespondentAcctMaintenance],
})
export class CorrespondentAcctMaintenanceModule { }