<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingTop="5">
    <SwtFieldSet id="fieldSet1" #fieldSet1 style="height: 292px; width: 100%; color:blue;">
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow>
          <VBox width="780">
            <GridRow width="100%" height="26">
              <GridItem width="65%">
                <GridItem width="300">
                  <GridItem width="120">
                    <SwtLabel id="entity" #entity></SwtLabel>
                  </GridItem>
                  <GridItem>
                    <SwtComboBox id="entityCombo" #entityCombo width="200" (change)="refreshComboList()"
                      dataLabel="entityList"> </SwtComboBox>
                  </GridItem>
                </GridItem>
                <GridItem paddingLeft="50">
                  <SwtLabel id="entityDesc" #entityDesc fontWeight="normal"></SwtLabel>
                </GridItem>
              </GridItem>
            </GridRow>



            <GridRow width="100%" height="25">
              <GridItem width="65%">
                <GridItem width="300">
                  <GridItem width="120">
                    <SwtLabel id="positionLevel" #positionLevel></SwtLabel>
                  </GridItem>
                  <GridItem>
                    <SwtComboBox enabled="false" id="positionCombo" #positionCombo width="60"
                      dataLabel="PositionLevelList"> </SwtComboBox>
                  </GridItem>
                </GridItem>
                <GridItem paddingLeft="50">
                  <SwtLabel id="positionName" #positionName fontWeight="normal"></SwtLabel>
                </GridItem>
              </GridItem>
            </GridRow>


            <GridRow width="100%" height="25">
              <GridItem width="95%">
                <GridItem width="535" paddingTop="2">
                  <GridItem width="120">
                    <SwtLabel id="valueDate" #valueDate></SwtLabel>
                  </GridItem>
                  <GridItem>
                    <SwtDateField id="valueDateField" #valueDateField (change)="validateDateField(valueDateField)"
                      width="70"></SwtDateField>
                  </GridItem>
                </GridItem>
              </GridItem>
            </GridRow>

          </VBox>
          <VBox width="100">
            <GridItem horizontalAlign="right">
              <SwtFieldSet style="color:blue;" id="fieldSet" #fieldSet>
                <SwtRadioButtonGroup #predictStatus id="predictStatus" align="vertical">
                  <SwtRadioItem value="I" groupName="predictStatus" selected="true" id="includedRadio" #includedRadio>
                  </SwtRadioItem>
                  <SwtRadioItem value="E" groupName="predictStatus" id="excludedRadio" #excludedRadio></SwtRadioItem>
                  <SwtRadioItem value="C" groupName="predictStatus" id="cancelledRadio" #cancelledRadio></SwtRadioItem>
                </SwtRadioButtonGroup>
              </SwtFieldSet>
            </GridItem>
          </VBox>
        </GridRow>
        <GridRow width="100%" height="25">
          <GridItem width="95%">
            <GridItem width="535">
              <GridItem width="120">
                <SwtLabel id="type" #type></SwtLabel>
              </GridItem>

              <GridItem>
                <SwtRadioButtonGroup #typeRadios id="typeRadios"  (change)="refreshAccountComboList()" align="horizontal">
                  <SwtRadioItem #cashRadio id="cashRadio" selected="true" value="C" groupName="typeRadios">
                  </SwtRadioItem>
                  <SwtRadioItem #securitiesRadio id="securitiesRadio" value="U" groupName="typeRadios"></SwtRadioItem>
                </SwtRadioButtonGroup>
              </GridItem>
            </GridItem>

            <GridItem width="400">
              <GridItem width="110">
                <SwtLabel id="postingDate" #postingDate></SwtLabel>
              </GridItem>
              <GridItem>
                <SwtDateField id="postingDateField" #postingDateField (change)="validateDateField(postingDateField)"
                  width="70"></SwtDateField>
              </GridItem>
            </GridItem>

          </GridItem>
        </GridRow>



        <GridRow width="100%" height="27">
          <GridItem width="535">
            <GridItem width="120">
              <SwtLabel id="amount" #amount></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="currencyCombo" #currencyCombo width="80" (change)="refreshAccountComboList()"
                dataLabel="currencyList"> </SwtComboBox>
            </GridItem>
            <GridItem paddingLeft="10">
              <SwtTextInput #amountTxtInput id="amountTxtInput" (focusOut)="validateReserve()" restrict="0-9mtb.,"
                editable="true" width="230" textAlign="right"></SwtTextInput>
            </GridItem>
            <GridItem paddingLeft="10">
              <SwtComboBox id="signCombo" #signCombo width="50"  dataLabel="signList">
              </SwtComboBox>
            </GridItem>
          </GridItem>

          <GridItem width="400">
            <GridItem width="110">
              <SwtLabel id="productType" #productType></SwtLabel>
            </GridItem>

            <GridItem width="160">
              <SwtTextInput id="productTypeTxtInput" #productTypeTxtInput maxChars="16" editable="true" width="200">
              </SwtTextInput>
            </GridItem>
          </GridItem>

        </GridRow>


        <GridRow width="100%" height="26">
          <GridItem width="70%">
            <GridItem width="120">
              <SwtLabel id="account" #account></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox #accountCombo id="accountCombo" editable="false" width="385" (change)="refreshLabel()"
                dataLabel="collAcctList"></SwtComboBox>
            </GridItem>
            <GridItem paddingLeft="15">
              <SwtLabel id="accountLabel" #accountLabel fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="26">
          <GridItem width="70%">
            <GridItem width="120">
              <SwtLabel id="bookCode" #bookCode></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="bookCodeCombo" #bookCodeCombo editable="false" width="200" (change)="refreshLabel()"
                dataLabel="collBookCode"></SwtComboBox>
            </GridItem>
            <GridItem paddingLeft="10">
              <SwtLabel id="bookCodeLabel" #bookCodeLabel fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="26">
          <GridItem width="70%">
            <GridItem width="120">
              <SwtLabel id="reference" #reference></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput #referenceTxtInput id="referenceTxtInput" editable="true" maxChars="35" width="385">
              </SwtTextInput>
            </GridItem>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="26">
          <GridItem width="70%">
            <GridItem width="120">
              <SwtLabel id="counterParty" #counterParty></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput #counterPartyTxtInput id="counterPartyTxtInput" editable="true" width="200"></SwtTextInput>
            </GridItem>
            <GridItem paddingLeft="10">
              <SwtButton [buttonMode]="true" id="counterPartyButton" #counterPartyButton width="30"
                (click)="preSearchParties()" label="...">
              </SwtButton>
            </GridItem>
            <GridItem paddingLeft="10">
              <SwtLabel id="counterPartyLabel" #counterPartyLabel fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="26">
          <GridItem width="70%">
            <GridItem width="120">
            </GridItem>
            <GridItem>
              <SwtTextInput #counterPartyTxtInput1 id="counterPartyTxtInput1" maxChars="35" editable="true" width="385">
              </SwtTextInput>
            </GridItem>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="27">
          <GridItem width="800">
            <GridItem width="120">
              <SwtLabel id="matchingParty" #matchingParty></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput #matchingPartyTxtInput id="matchingPartyTxtInput" editable="true" width="200">
              </SwtTextInput>
            </GridItem>
            <GridItem paddingLeft="10">
              <SwtButton [buttonMode]="true" id="matchingPartyButton" #matchingPartyButton width="30"
                (click)="preSearchParties()" label="...">
              </SwtButton>
            </GridItem>
            <GridItem paddingLeft="10">
              <SwtLabel id="matchingPartyLabel" #matchingPartyLabel fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="6%">
            <SwtButton [buttonMode]="true" id="saveButton" #saveButton (click)="savePreAdvice()" width="60">
            </SwtButton>
          </GridItem>
        </GridRow>

      </Grid>
    </SwtFieldSet>


    <SwtFieldSet id="fieldSet2" #fieldSet2 style="height: 70%; width: 100%;  padding-bottom: 5px;color:blue;">
      <VBox width="100%" height="100%" paddingLeft="5">
        <GridRow width="100%" height="30" top="3">
          <GridItem width="100%" height="100%">
            <GridItem width="73%" height="100%">
              <GridItem width="120" height="100%">
                <SwtLabel id="entity1" #entity1></SwtLabel>
              </GridItem>
              <GridItem width="90%" height="100%">
                <SwtComboBox id="entityCombo1" #entityCombo1 width="200" (change)="updateData()"
                  dataLabel="entityList1"> </SwtComboBox>
              </GridItem>
            </GridItem>
            <GridItem width="27%" height="100%">
              <GridItem height="100%" width="60">
                <SwtLabel id="inputBy" #inputBy></SwtLabel>
              </GridItem>
              <GridItem paddingLeft="10" height="100%" width="85%">
                <SwtRadioButtonGroup #user id="user" align="horizontal" (change)="updateData()">
                  <SwtRadioItem value="A"  width="90" groupName="user"  selected="true"  id="allRadio" #allRadio></SwtRadioItem>
                  <SwtRadioItem value="C"  width="100" groupName="user" id="currentRadio" #currentRadio>
                  </SwtRadioItem>
                </SwtRadioButtonGroup>
              </GridItem>
            </GridItem>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="100%" top="3">
          <SwtCanvas #preAdviceGridContainer id="preAdviceGridContainer" styleName="canvasWithGreyBorder" width="100%"
            height="100%" border="false"></SwtCanvas>
        </GridRow>

      </VBox>
    </SwtFieldSet>


    <SwtCanvas width="100%" height="4.5%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="importButton" #importButton (click)="openImportMvtScreen()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="mvtButton" #mvtButton (click)="openMvtDisplayScreen()" enabled="false">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="refreshButton" #refreshButton (click)="updateData()">
          </SwtButton>
        </HBox>
        <HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
          <div>
            <DataExport #dataExport id="dataExport"></DataExport>
          </div>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>

      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>