
import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService,
  ExternalInterface,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCommonGrid,
  SwtLoadingImage,
  SwtModule,
  SwtUtil,
  SwtToolBoxModule,
  SwtLabel,
  SwtTextInput,
  SwtComboBox,
  SwtCheckBox,
  StringUtils,
  SwtNumericInput,
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";
declare let instanceElement: any;
@Component({
  selector: 'app-forecast-monitor-template-detail',
  templateUrl: './ForecastMonitorTemplateDetail.html',
  styleUrls: ['./ForecastMonitorTemplateDetail.css']
})
export class ForecastMonitorTemplateDetail extends  SwtModule {
  @ViewChild('forecastMonitorTemplateDetailCanvas') forecastMonitorTemplateDetailCanvas: SwtCanvas;

  /********SwtButton*********************/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('suggestButton') suggestButton: SwtButton;
  /*****Labels*******/
  @ViewChild('lblColumnType') lblColumnType: SwtLabel;
  @ViewChild('lblColumnNo') lblColumnNo: SwtLabel;
  @ViewChild('lblShortName') lblShortName: SwtLabel;
  @ViewChild('lblDesc') lblDesc: SwtLabel;
  @ViewChild('lblContribute') lblContribute: SwtLabel;
  /****SwtTextInput***********/
  @ViewChild('txtColumnNo') txtColumnNo: SwtNumericInput;
  @ViewChild('txtShortName') txtShortName: SwtTextInput;
  @ViewChild('txtDescription') txtDescription: SwtTextInput;
  @ViewChild('txtMulitiplier') txtMulitiplier: SwtTextInput;
  /****Combo***********/
  @ViewChild('cbColumnType') cbColumnType: SwtComboBox;
  /*********Checkbox********/
  @ViewChild('chbPublic') chbPublic: SwtCheckBox;

  /*********LodingImage*************/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /* Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private saveData = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;
  private swtAlert: SwtAlert;
  private forecastMonitorTemplateDetailGrid: SwtCommonGrid;
  public menuAccessId: string = null;
  private  templateId:string=null;
  private userId:string=null;
  private type:string=null;
  private templateName:string=null;
  private screenName:string=null;
  private ordnialPos:string=null;
  private shortName:string=null;
  private modify:string=null;
  private copyFromFlag:string=null;
  private copiedTemplateId:string=null;
  private copiedUserId:string=null;
  private  totalMultiplier:string=null;
  private  totalChanged: boolean=false;
  private  columnId:string=null;
  private shortNameOnLoad: string=null;
  private typeId: string=null;
  private selectedRowId:string=null;
  private selectedEntityId:string=null;
  private  selectedId: string=null;
  private selectedColumn:String=null;
  private detailRowCount:number=0;


  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    window['Main'] = this;
    this.swtAlert = new SwtAlert(commonService);
  }
   ngOnDestroy(): any {
    instanceElement = null;
  }

  ngOnInit(): void {
    instanceElement = this;
    this.lblColumnType.text = SwtUtil.getPredictMessage('label.forecastMonitorTemplateAddDetail.columnType', null);
    this.cbColumnType.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAddDetail.columnType', null);
    this.lblColumnNo.text = SwtUtil.getPredictMessage('label.forecastMonitorTemplateAddDetail.columnId', null);
    this.txtColumnNo.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAddDetail.columnId', null);
    this.lblShortName.text = SwtUtil.getPredictMessage('label.forecastMonitorTemplateAddDetail.shortName', null);
    this.txtShortName.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAddDetail.shortName', null);
    this.lblDesc.text =SwtUtil.getPredictMessage('label.forecastMonitorTemplateAddDetail.description', null);
    this.txtDescription.toolTip =SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAddDetail.description', null);
    this.chbPublic.toolTip =SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAddDetail.contributeCheck', null);
    this.lblContribute.text =SwtUtil.getPredictMessage('label.forecastMonitorTemplateAddDetail.contributeCheck', null);
    this.txtMulitiplier.toolTip =SwtUtil.getPredictMessage('tooltip.forecastMonitorTemplateAddDetail.contributeText', null);
    this.suggestButton.label = SwtUtil.getPredictMessage('button.forecastmonitor.suggest', null);
    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
  }

  onLoad() {
    this.forecastMonitorTemplateDetailGrid = <SwtCommonGrid> this.forecastMonitorTemplateDetailCanvas.addChild(SwtCommonGrid);
    this.forecastMonitorTemplateDetailGrid.onFilterChanged = this.disableDeleteButton.bind(this);
    this.forecastMonitorTemplateDetailGrid.onSortChanged = this.disableDeleteButton.bind(this);
    this.type=ExternalInterface.call('eval', 'type');

    this.inputData.cbStart=this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    //fault event
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    //action url
    this.actionPath="forecastMonitorTemplate.do?method=";
    this.screenName=ExternalInterface.call('eval', 'screenName');
    this.templateId=ExternalInterface.call('eval', 'templateId');
    this.templateName=ExternalInterface.call('eval', 'templateName');
    this.ordnialPos=ExternalInterface.call('eval', 'ordinalPos');
    this.userId=ExternalInterface.call('eval', 'userId');
    this.shortName=ExternalInterface.call('eval', 'shortName');
    this.modify=ExternalInterface.call('eval', 'modify');
    this.copyFromFlag=ExternalInterface.call('eval', 'copyFromFlag');
    this.copiedTemplateId=ExternalInterface.call('eval', 'copiedTemplateId');
    this.copiedUserId=ExternalInterface.call('eval', 'copiedUserId');
    if (this.screenName != "change")
    {
      this.lblColumnType.text= this.lblColumnType.text + " *";
      this.actionMethod="addForecastDetails&loadFlex=true";
      this.actionMethod=this.actionMethod + "&templateId=" + this.templateId;
      this.actionMethod=this.actionMethod + "&templateName=" + this.templateName;
    }
    else
    {

      if (this.type == "Normal")
      {
        this.actionMethod="changeForecastDetails&loadFlex=true";
      }
      else
      {
        this.actionMethod="loadSubtotalChange&loadFlex=true";
      }
      this.actionMethod=this.actionMethod + "&ordinalPos=" + this.ordnialPos;
      this.actionMethod=this.actionMethod + "&userId=" +this.userId;
      this.actionMethod=this.actionMethod + "&columnId=" + ExternalInterface.call('eval', 'columnId');
      this.actionMethod=this.actionMethod + "&templateId=" + this.templateId;
      this.actionMethod=this.actionMethod + "&templateName=" + this.templateName;
      this.actionMethod=this.actionMethod + "&shortName=" + this.shortName;
      this.actionMethod=this.actionMethod + "&description=" + ExternalInterface.call('eval', 'description');
      this.actionMethod=this.actionMethod + "&type=" + this.type;
      this.actionMethod=this.actionMethod + "&modify=" + this.modify;
      this.actionMethod=this.actionMethod + "&copiedTemplateId=" + this.copiedTemplateId;
      this.actionMethod=this.actionMethod + "&copiedUserId=" + this.copiedUserId;
      this.actionMethod=this.actionMethod + "&copyFromFlag=" + this.copyFromFlag;
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      if (this.shortName == "Total")
      {
        this.txtColumnNo.enabled=false;
        this.txtDescription.enabled=false;
        this.txtShortName.enabled=false;
        this.chbPublic.enabled = false;
      }

      this.cbColumnType.enabled=false;

    }
    /*this.addEventListener("CellClick", cellLogic, true);
    this.addEventListener("filterUpdate", cellLogic, true);
    //Event listener to listen for any header renderer opens and closes
    this.addEventListener("HeaderComboOpen", openedCombo, true);
    this.addEventListener("HeaderComboClose", closedCombo, true);*/
    this.actionMethod=this.actionMethod + "&addClick=" + ExternalInterface.call('eval', 'addClick');
    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    //Make initial request
    this.inputData.send(this.requestParams);
    this.forecastMonitorTemplateDetailGrid.onRowClick = (event)=> {
    this.cellLogic(event);
    };
  }
  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  public inputDataResult(data): void {

    try {
      if (this.inputData && this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = data;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (this.jsonReader.getRequestReplyStatus()) {

          if (!this.jsonReader.isDataBuilding()) {
            this.cbColumnType.setComboData(this.jsonReader.getSelects(), true);
            const obj = {columns: this.jsonReader.getColumnData()};
            this.forecastMonitorTemplateDetailGrid.CustomGrid(obj);
            if (this.jsonReader.getGridData()) {
              this.forecastMonitorTemplateDetailGrid.gridData = this.jsonReader.getGridData();
              //this.gridJSONList= this.jsonReader.getGridData();
              //this.forecastMonitorTemplateDetailGrid.setRowSize = this.jsonReader.getRowSize();
            } else {
              this.forecastMonitorTemplateDetailGrid.dataProvider = [];
              this.forecastMonitorTemplateDetailGrid.selectedIndex = -1;
            }
            if (this.screenName == "change")
            {
              this.txtColumnNo.text= this.jsonReader.getScreenAttributes()["ordinalPos"].toString();
              this.txtShortName.text=this.shortName;
              this.shortNameOnLoad= this.txtShortName.text;
              // set displayname as this.shortName
              this.txtDescription.text=this.jsonReader.getScreenAttributes()["description"];
              this.totalMultiplier=this.jsonReader.getScreenAttributes()["totalMultiplier"];
              // Set total multipler
              if (this.totalMultiplier != "" && this.totalMultiplier != null)
              {
                this.chbPublic.selected=true;
                this.txtMulitiplier.text= this.totalMultiplier;
                this.txtMulitiplier.enabled=true;

              }
              // Disable text fields for total column
              if (this.cbColumnType.selectedLabel == "Total")
              {
                this.chbPublic.enabled=false;
                this.chbPublic.selected=false;
                this.txtMulitiplier.text="";
                this.txtMulitiplier.enabled=false;

              }
              this.columnId=this.jsonReader.getScreenAttributes()["columnId"];
            }

            this.txtShortName.text=this.jsonReader.getScreenAttributes()["displayName"];
            this.txtDescription.text = this.jsonReader.getScreenAttributes()["description"];
          }
          this.suggestButton.enabled= this.cbColumnType.selectedLabel != "Normal";
          this.prevRecievedJSON = this.lastRecievedJSON;

        } else {
          this.swtAlert.error(SwtUtil.getCommonMessages('alert.generic_exception'));
        }
        this.changeHighLightBorder();

        // }

      }
    } catch (e) {
      console.log('error in inputData', e);
    }
  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  inputDataFault(): void {
    this.swtAlert.error('alert.generic_exception');

  }
  /**
   * close the window from the close button
   **/
   closeHandler():void
  {
    ExternalInterface.call("close");
  }
  /**
   * When item click on the datagrd is method will be called
   * @param e:CellEvent
   **/
 cellLogic(e):void
  {
    if (this.forecastMonitorTemplateDetailGrid.selectedIndex >= 0)
    {
      this.deleteButton.enabled=true;
      if (this.cbColumnType.selectedLabel == "Normal")
      {
        this.typeId=this.forecastMonitorTemplateDetailGrid.selectedItem.type.content;
        //this.selectedRowId=this.forecastMonitorTemplateDetailGrid.selectedItem.rowid.content;
        this.selectedEntityId=this.forecastMonitorTemplateDetailGrid.selectedItem.entity.content;
        this.selectedId=this.forecastMonitorTemplateDetailGrid.selectedItem.id.content;
      }
      else
      {
        // read sub total records from selected grid
        this.selectedId=this.forecastMonitorTemplateDetailGrid.selectedItem.columnno.content;
        this.selectedColumn=this.forecastMonitorTemplateDetailGrid.selectedItem.displayname.content;
      }

    }
    else
    {
      this.deleteButton.enabled=false;
    }
  }
  /**
   * typeChange
   *
   * This method is used to change the grid for normal and subtotal
   */
  typeChange():void
  {
    // Condition to chcek selected label is subtotal
    if (this.cbColumnType.selectedLabel == "SubTotal")
    {
     this.actionMethod="loadSubtotalAdd";
      this.actionMethod+="&type=" + this.cbColumnType.selectedLabel;
      this.suggestButton.enabled=true;
    }
    else
    {
      this.actionMethod="addForecastDetails&loadFlex=true";
      this.actionMethod+="&type=" + this.cbColumnType.selectedLabel;
      this.suggestButton.enabled=false;
    }
    this.actionMethod=this.actionMethod + "&ordinalPos=" + this.ordnialPos;
    this.actionMethod= this.actionMethod + "&userId=" + this.userId;
    this.actionMethod= this.actionMethod + "&columnId=" + ExternalInterface.call('eval', 'columnId');
    this.actionMethod=this.actionMethod + "&templateId=" + this.templateId;
    this.actionMethod= this.actionMethod + "&templateName=" + this.templateName;
    this.actionMethod= this.actionMethod + "&displayName=" + ExternalInterface.call('eval', 'displayName');
    this.actionMethod=this.actionMethod + "&description=" + ExternalInterface.call('eval', 'description');
    this.actionMethod=this.actionMethod + "&type=" + this.type;
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }
  /**
   * loadAddPopup()
   *
   * This method is used to open Add forecast detail screen
   */
   loadAddPopup(pressedbtn: string):void
  {
try {


    /* Url to load Add template screen */
    if (StringUtils.trim(this.txtColumnNo.text.toString()) == "" || StringUtils.trim(this.txtShortName.text.toString()) == "" || StringUtils.trim(this.txtDescription.text.toString()) == "")
    {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecasttemplate.mandatory', null));
    }
    else
    {
      if (this.cbColumnType.selectedLabel == "Normal")
        this.actionMethod="addPopUp";
      else
      {
        this.actionMethod="addPopUpForSubtotal";
        this.actionMethod+="&pressedbutton=" + pressedbtn;
      }

      this.actionMethod+="&type=" + this.cbColumnType.selectedLabel;
      this.actionMethod+="&templateId=" + this.templateId;
      this.actionMethod+="&templateName=" + this.templateName;
      this.actionMethod+="&ordinalPos=" + StringUtils.trim(this.txtColumnNo.text.toString());
      this.actionMethod+="&userId=" + this.userId;
      this.actionMethod+="&columnId=" + StringUtils.trim(this.txtColumnNo.text.toString());
      this.actionMethod+="&description=" + escape(escape(StringUtils.trim(this.txtDescription.text.toString())));
      this.actionMethod+="&shortName=" + this.shortNameOnLoad;
      this.actionMethod+="&detailRowCount=" + this.detailRowCount;
      ExternalInterface.call("openChildWindow", this.actionMethod);
    }
} catch(e) {
  console.log('error', e)
}
  }
  /**
   * saveTemplateDetail
   *
   * Method to add selected details to Main screen
   */
  saveTemplateDetail():void
  {
    // variable to hold name changed flag
    let isNameChanged:Boolean=false;
    let columnSourceType = [];
    let entity = [];
    let sourceId = [];
    let sourceName  = [];
    let sourceMultiplier = [];
    let sourceOrdinalPos = [];
    if (StringUtils.trim(this.txtColumnNo.text.toString()) == "" || StringUtils.trim(this.txtShortName.text.toString()) == "" || StringUtils.trim(this.txtDescription.text.toString()) == "")
    {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.forecasttemplate.mandatory', null));
    }
    else
    {
      if (this.chbPublic.selected && this.txtMulitiplier.text == "")
      {
        this.swtAlert.warning(ExternalInterface.call('getBundle', 'alert', 'template-totalmultiplier', 'Please enter Total Multiplier'));
      }
      else if ((Number(this.txtColumnNo.text.toString())> 2 && Number(this.txtColumnNo.text.toString()) < 960) || this.cbColumnType.selectedLabel == "Total")
      {

        if (this.shortNameOnLoad != null && this.shortNameOnLoad != this.txtShortName.text)
        {
          isNameChanged=true;
        }
        this.actionMethod="saveTemplatesSrcInSession";
        this.requestParams=[];
        this.requestParams["type"]=this.cbColumnType.selectedLabel;
        this.requestParams["fromFlex"]=true;
        this.requestParams["templateId"]=this.templateId;
        this.requestParams["columnId"]=StringUtils.trim(this.txtColumnNo.text.toString());
        this.requestParams["shortName"]=StringUtils.trim(this.txtShortName.text.toString());
        this.requestParams["isNameChanged"]=isNameChanged;
        if (isNameChanged) {
          this.requestParams["shortNameOnLoad"]=this.shortNameOnLoad;
        }
        else
          this.requestParams["shortNameOnLoad"]=StringUtils.trim(this.txtShortName.text.toString());

        this.requestParams["description"]=StringUtils.trim(this.txtDescription.text.toString());
        this.requestParams["userId"]=this.userId;
        this.requestParams["multiplier"]=this.txtMulitiplier.text;
        if (this.screenName != "change")
          this.requestParams["modifiedValue"]="save";
        else
          this.requestParams["modifiedValue"]="update";


        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        //set the grid row data
        if(this.forecastMonitorTemplateDetailGrid.dataProvider.length > 0) {
          for (let i = 0; i < this.jsonReader.getRowSize(); i++) {
            if (this.cbColumnType.selectedLabel == "Normal") {
              columnSourceType[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i].type;
              entity[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i].entity;
              sourceId[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i]._id;
              sourceName[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i].name;
              sourceMultiplier[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i].multiplier;
            } else {
              columnSourceType[i] = "C";
              sourceId[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i].columnno;
              entity[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i].displayname;
              sourceName[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i].description;
              sourceMultiplier[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i].multiplier;
              sourceOrdinalPos[i] = this.forecastMonitorTemplateDetailGrid.dataProvider[i].ordinalpos;
            }


          }
          this.requestParams["columnSourceType"]=columnSourceType;
          this.requestParams["entity"]=entity;
          this.requestParams["sourceId"]=sourceId;
          this.requestParams["sourceName"]=sourceName;
          this.requestParams["sourceMultiplier"]=sourceMultiplier;
          this.requestParams["sourceOrdinalPos"]=sourceOrdinalPos;
        }
        this.saveData.cbStart=this.startOfComms.bind(this);
        this.saveData.cbStop=this.endOfComms.bind(this);
        this.saveData.cbResult= (event) =>{
          this.saveDataResult(event);
        };
        this.saveData.cbFault=this.inputDataFault.bind(this);
        this.saveData.encodeURL=false;

        this.saveData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.saveData.send(this.requestParams);
      }
      else
      {
        this.swtAlert.warning (ExternalInterface.call('getBundle', 'alert', 'template-columnnumbers', 'Column number should be in the range 3 to 959'));
      }
    }

  }
  /**
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   * */
saveDataResult(event):void
  {
    this.jsonReader.setInputJSON(event);
    if ( this.jsonReader.getRequestReplyStatus())
    {
      //get the received xml
      ExternalInterface.call("refreshParent");
      if(window.opener && window.opener.instanceElement )
        window.opener.instanceElement.reloadMain( );
    }
    else
    {
      this.swtAlert.error(this.jsonReader.getRequestReplyMessage());
    }
  }
  /**
   * deleteHandler()
   *
   * This method is used to delete the selected record
   */
deleteHandler():void {
    this.swtAlert.question(
      ExternalInterface.call('getBundle', 'alert', 'template-columndelete', 'Are you sure you want to remove this record?'), //message
      null, Alert.YES | Alert.NO,
      null, // no parent
       this.removeRecord.bind(this), // closer handler
      null); // default button
  }

  /**
   * This method is used to delete the selected record after confirm
   *
   * @param event CloseEvent
   */
 removeRecord(event):void
  {
    if (event.detail == Alert.YES)
    {
      if (this.cbColumnType.selectedLabel == "Normal")
      {
        this.requestParams["type"]=this.typeId;
        this.requestParams["id"]=this.selectedId;
        this.requestParams["entity"]=this.selectedEntityId;
      }
      else
      {
        this.requestParams["type"]="C";
        this.requestParams["id"]=this.selectedId;
        this.requestParams["displayname"]=this.selectedColumn;
      }
      this.requestParams["columnType"]=this.cbColumnType.selectedLabel;
      this.requestParams["templateId"]=this.templateId;
      if (this.shortNameOnLoad != null)
        this.requestParams["shortName"]=this.shortNameOnLoad;
      else
        this.requestParams["shortName"]=StringUtils.trim(this.txtShortName.text.toString());
      this.requestParams["fromFlex"] = true;
      this.actionMethod="deleteColumnSrc";
      this.inputData.cbResult= (event) => {
        this.refreshDetailGrid(event);
      };
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.deleteButton.enabled=false;
    }
  }
  /**
   * changeTextMultiplier()
   *
   * This method is used to enable or disable text multiplier
   */
  changeTextMultiplier():void
  {
    if (this.chbPublic.selected == true)
    {
      this.txtMulitiplier.enabled=true;
      this.txtMulitiplier.text="1";
      this.totalChanged=false;
    }
    else
    {
      this.txtMulitiplier.enabled=false;
      this.txtMulitiplier.text="";
      this.totalChanged=true;
    }
  }
  validateNumber():void
  {
    if(isNaN(Number(this.txtMulitiplier.text)) && this.txtMulitiplier.text) {
      this.swtAlert.error('Please enter a valid number');
      //      SwtAlert.getInstance().show(ExternalInterface.call('getBundle', 'alert', 'template-validnumber', 'Please enter a valid number'), ExternalInterface.call('getBundle', 'text', 'label-error', 'Error'));
    }


  }
  /**
   * refreshDetail()
   *
   * This method to refresh detail grid
   */
   refreshDetail():void
  {
    this.actionMethod="refreshDetail";
    this.actionMethod=this.actionMethod + "&ordinalPos=" + this.txtColumnNo.text.toString();
    this.actionMethod=this.actionMethod + "&userId=" + this.userId;
    this.actionMethod=this.actionMethod + "&columnId=" + ExternalInterface.call('eval', 'columnId');
    this.actionMethod=this.actionMethod + "&templateId=" + this.templateId;
    this.actionMethod=this.actionMethod + "&templateName=" + this.templateName;
    this.actionMethod=this.actionMethod + "&shortName=" + this.shortName;
    this.actionMethod=this.actionMethod + "&description=" + ExternalInterface.call('eval', 'description');
    this.actionMethod=this.actionMethod + "&type=" + this.type;
    this.actionMethod=this.actionMethod + "&modify=" + this.modify;
    this.actionMethod=this.actionMethod + "&copiedTemplateId=" + this.copiedTemplateId;
    this.actionMethod=this.actionMethod + "&totalMultiplier=" + this.txtMulitiplier.text;
    this.actionMethod=this.actionMethod + "&copiedUserId=" + this.copiedUserId;
    this.actionMethod=this.actionMethod + "&copyFromFlag=" + this.copyFromFlag;
    this.inputData.cbResult= (event) => {
      this.refreshDetailGrid(event);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  /**
   * refreshSubDetail()
   *
   * This method to refresh detail grid for sub total
   */
   refreshSubDetail():void {
    this.actionMethod="refreshSubDetail";
    this.actionMethod=this.actionMethod + "&ordinalPos=" + this.txtColumnNo.text.toString();
    this.actionMethod=this.actionMethod + "&userId=" + this.userId;
    this.actionMethod=this.actionMethod + "&columnId=" + ExternalInterface.call('eval', 'columnId');
    this.actionMethod=this.actionMethod + "&templateId=" + this.templateId;
    this.actionMethod=this.actionMethod + "&templateName=" + this.templateName;
    this.actionMethod=this.actionMethod + "&shortName=" + this.shortName;
    this.actionMethod=this.actionMethod + "&description=" + ExternalInterface.call('eval', 'description');
    this.actionMethod=this.actionMethod + "&type=" + this.type;
    this.actionMethod=this.actionMethod + "&modify=" + this.modify;
    this.actionMethod=this.actionMethod + "&copiedTemplateId=" + this.copiedTemplateId;
    this.actionMethod=this.actionMethod + "&totalMultiplier=" + this.txtMulitiplier.text;
    this.actionMethod=this.actionMethod + "&copiedUserId=" + this.copiedUserId;
    this.actionMethod=this.actionMethod + "&copyFromFlag=" + this.copyFromFlag;
    this.inputData.cbResult= (event) => {
      this.refreshDetailGrid(event);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  /**
   * refreshDetailGrid(event:ResultEvent)
   *
   * This method to refresh detail grid
   */
  refreshDetailGrid(event):void
  {
    //get the received xml
   this.lastRecievedJSON=event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    //Instantiate the ForecastMonitorTemplateGrid
   /* this.forecastMonitorTemplateDetailGrid=new ForecastMonitorTemplateDetailGrid(this.jsonReader.getColumnData(), _baseURL, deleteButton);
    cvGridContainer.addElement(this.forecastMonitorTemplateDetailGrid);
    this.forecastMonitorTemplateDetailGrid.colType=cbColumnType.selectedLabel;
    this.forecastMonitorTemplateDetailGrid.setListeners();
    this.forecastMonitorTemplateDetailGrid.lastGridXML=this.lastRecievedJSON.grid.rows.row;
    //set the grid row data*/
    const obj = {columns: this.jsonReader.getColumnData()};
    this.forecastMonitorTemplateDetailGrid.CustomGrid(obj);
    this.forecastMonitorTemplateDetailGrid.gridData=this.jsonReader.getGridData();
    this.forecastMonitorTemplateDetailGrid.setRowSize = this.jsonReader.getRowSize();
   // this.forecastMonitorTemplateDetailGrid.setRowSize=this.forecastMonitorTemplateDetailGrid.gridData.length();
    this.deleteButton.enabled=false;
  }
 changeHighLightBorder():void { 
    this.txtColumnNo.required = this.txtColumnNo.text.toString()=="";
    this.txtShortName.required = this.txtShortName.text=="";
    this.txtDescription.required = this.txtDescription.text=="";

    
  }

  doHelp() : void {
    ExternalInterface.call('help');
  }
  disableDeleteButton() {
    if(this.forecastMonitorTemplateDetailGrid.selectedIndex ==-1) {
      this.deleteButton.enabled = false;
    }
  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ForecastMonitorTemplateDetail }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ForecastMonitorTemplateDetail],
  entryComponents: []
})
export class ForecastMonitorTemplateDetailModule {}
