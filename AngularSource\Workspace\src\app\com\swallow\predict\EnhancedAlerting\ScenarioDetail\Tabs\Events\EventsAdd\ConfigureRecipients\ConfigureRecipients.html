<SwtModule #swtModule (creationComplete)='onLoad()' height='100%'  width='100%'>
  <VBox id="vbox1" width='100%' height='95%' paddingBottom="5" paddingLeft="5" paddingRight="5" paddingTop="5">

  <SwtTabNavigator #configRecipientsNavigator height="90%" width="100%">
  </SwtTabNavigator>

    <SwtCanvas id="canvasContainer"
               width="100%">
      <HBox width="100%" height="100%">
        <SwtButton #okButton
                   (click)="saveHandler()"
                   (keyDown)="keyDownEventHandler($event)"
                   id="okButton"></SwtButton>

        <SwtButton buttonMode="true"
                   id="cancelButton"
                   marginLeft="5"
                   #cancelButton
                   (click)="closeHandler();"
                   (keyDown)="keyDownEventHandler($event)"></SwtButton>

      </HBox>

      <HBox horizontalAlign="right" width="50%" paddingTop="5" >

       <SwtHelpButton id="helpIcon" 
       [buttonMode]="true" 
       enabled="true" 
       helpFile="configure-recipients" 
       (click)="doHelp()">
      </SwtHelpButton>
       
       <SwtButton [buttonMode]="true" 
                  #printButton 
                  id="printButton" 
                  styleName="printIcon" 
                  (click)="printPage()"
                  (keyDown)="keyDownEventHandler($event)">
      </SwtButton>

        <SwtLoadingImage #loadingImage></SwtLoadingImage>
      </HBox>

    </SwtCanvas>
  </VBox>
</SwtModule>

