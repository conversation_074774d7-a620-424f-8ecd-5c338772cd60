import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { SwtModule, CommonService, SwtCanvas, SwtLabel, SwtCheckBox, SwtCommonGrid, SwtUtil } from 'swt-tool-box';
import { Router } from '@angular/router';
declare var instanceElement: any;

@Component({
  selector: 'app-users-tab',
  templateUrl: './UsersTab.html',
  styleUrls: ['./UsersTab.css']
})
export class UsersTab extends SwtModule implements OnInit{
  @ViewChild('usersCanvas') usersCanvas: SwtCanvas;
  @ViewChild('selectAllLbl') selectAllLbl: SwtLabel;
  @ViewChild('selectAllCheck') selectAllCheck: SwtCheckBox;

  public usersGrid: SwtCommonGrid;
  public parentDocument: any;

  constructor(private commonService: CommonService, private element: ElementRef, private router: Router) {
    super(element, commonService);
   // this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.usersGrid = <SwtCommonGrid>this.usersCanvas.addChild(SwtCommonGrid);
    this.usersGrid.editable = true;
    this.selectAllLbl.text = SwtUtil.getPredictMessage("scenario.role.selectAll.label", null);
   // this.selectAllCheck.toolTip= SwtUtil.getPredictMessage("scenario.role.selectAll.tooltip", null);
  }

  onLoad(){

  }

  selectAll(){


    for (let i = 0; i < this.usersGrid.dataProvider.length; i++) {
        if(this.selectAllCheck.selected){

          this.usersGrid.dataProvider[i].send ="Y" ; 

        } else  {

          this.usersGrid.dataProvider[i].send ="N" ; 
          }        

    } 
    this.usersGrid.refresh();

  }


}
