import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { SwtModule, CommonService, SwtTextArea, SwtButton, SwtUtil, SwtFieldSet, SwtLabel, JSONReader, SwtAlert } from 'swt-tool-box';
import { HTTPComms } from 'swt-tool-box'; // Assuming HTTPComms is imported

@Component({
  selector: 'app-process-window',
  templateUrl: './ProcessWindow.html',
  styleUrls: ['./ProcessWindow.css']
})
export class ProcessWindow extends SwtModule implements OnInit {
  @ViewChild("processFieldSet") processFieldSet: SwtFieldSet;

  @ViewChild("statusLbl") statusLbl: SwtLabel;
  @ViewChild("statusDesc") statusDesc: SwtLabel;
  @ViewChild("mvtProcessed") mvtProcessed: SwtLabel;

  @ViewChild("summaryTxt") summaryTxt: SwtTextArea;

  @ViewChild("cancelButton") cancelButton: SwtButton;
  @ViewChild("closeExistButton") closeExistButton: SwtButton;
  @ViewChild("closeReturnButton") closeReturnButton: SwtButton;
  @ViewChild("generateReportButton") generateReportButton: SwtButton;

  progress: number = 0; // Progress percentage (0-100)
  isProcessCompleted: boolean = false;
  isCancelled: boolean = false;
  processInterval: any;
  statusText: string = "Not Running";
  progressText: string = "0 movements processed";
  processLog: string[] = [];

  movementList :  string;
  actionSent : string;
  seq : string;
  requestParamsSent : any;
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  private swtAlert: SwtAlert;
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  successCount: number;
  failedCount: number;
  startTime: any;
  constructor(private element: ElementRef, private commonService: CommonService) {
    super(element, commonService);
  }

  ngOnInit() {
    this.processFieldSet = SwtUtil.getPredictMessage('multipleMvtActions.fieldset.process', null);
    this.statusLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.status', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.closeExistButton.label = SwtUtil.getPredictMessage('multipleMvtActions.label.closeExitButton', null);
    this.closeReturnButton.label = SwtUtil.getPredictMessage('multipleMvtActions.label.closeReturnButton', null);
    this.generateReportButton.label = SwtUtil.getPredictMessage('multipleMvtActions.label.generateReportButton', null);
this.statusText = "Not Running";
  }

  onLoad() {
   // console.log("ProcessWindow loaded",this.movementList);
    this.startProcess();
  }

  startProcess() {
    this.statusText = "Running";
    this.progress = 0;
    this.isProcessCompleted = false;
    this.isCancelled = false;
    this.processLog = ["Process started at " + new Date().toLocaleString()];
    this.updateLog();
    this.pollServerForProgress();
  }

  pollServerForProgress() {
    this.processInterval = setInterval(() => {
      this.fetchProgressFromServer();
    }, 1000); // Poll every second
  }

  fetchProgressFromServer() {
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.updateProgress(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "multipleMvtActions.do?";
    this.actionMethod = 'method=checkProcessStatus';
    this.requestParams["seq"] = this.seq;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    
    this.inputData.send(this.requestParams);
  }


  startOfComms(): void {
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
  }
	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  updateProgress(event) {
    if (event) {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
  
      if (this.jsonReader.getRequestReplyStatus()) {
          this.seq = this.jsonReader.getSingletons().seq; // Store sequence ID
  
        // Extract values from the JSON response
        const processStatus = parseInt(this.jsonReader.getSingletons().processStatus);
        const pendingCount = parseInt(this.jsonReader.getSingletons().pendingCount);
        const totalCount = parseInt(this.jsonReader.getSingletons().totalCount);
        this.successCount = parseInt(this.jsonReader.getSingletons().successCount) || 0;
        this.failedCount = parseInt(this.jsonReader.getSingletons().failedCount) || 0;

        if (!isNaN(processStatus)) {
          this.progress = processStatus;
          this.progressText = `${this.progress}% - ${totalCount - pendingCount} out of ${totalCount} movements processed`;
          this.addProcessStep(processStatus);
        }
        // If processing is complete, fetch success and failure counts
        if (pendingCount === 0) {
          clearInterval(this.processInterval);
  

  
          this.finishProcessing();
        }
      }
    }
  }
  

  addProcessStep(progress: number) {
    const logs = new Set(this.processLog); // Use a Set to prevent duplicates
  
    // Capture start time if not already set
    if (!this.startTime) {
      this.startTime = new Date();
    }
  
    // Action-specific message
    switch (this.actionSent) {
      case 'ADD_MOV_NOTE':
        logs.add("Adding movement note...");
        break;
      case 'UPD_STATUS':
        logs.add("Updating movement status...");
        break;
      case 'UNMATCH':
        logs.add("Unmatching movements...");
        break;
      case 'RECONCILE':
        logs.add("Reconciling movements...");
        break;
      case 'UPDATE_OTHER_SETTLEMENT':
        logs.add("Updating settlement details...");
        break;
      default:
        logs.add("Processing movements...");
        break;
    }
  
    // Progress-based updates
    if (progress >= 10) logs.add("Processing ..");
    if (progress >= 90) logs.add("Clearing locks...");
    if (progress >= 100) {
      logs.add("Process completed successfully.");
  
      // Calculate and display processing time
      const endTime = new Date();
      const duration = (endTime.getTime() - this.startTime.getTime()) / 1000; // Time in seconds
      logs.add(`<b>Total processing time: ${duration.toFixed(2)} seconds.</b>`);
  
      console.log("🚀 ~ ProcessWindow ~ addProcessStep ~ this.successCount:", this.successCount)
      console.log("🚀 ~ ProcessWindow ~ addProcessStep ~ this.failedCount:", this.failedCount)
      // Display successful and failed movements count
      logs.add(`<b>Movements successfully updated: ${this.successCount}</b>`);
      logs.add(`<b>Movements failed to update: ${this.failedCount}</b>`);
    }
  
    // Convert Set back to array and update the log
    this.processLog = Array.from(logs);
    this.updateLog();
  }
  

  updateLog() {
    this.summaryTxt.htmlText = this.processLog.join("\n");
  }

  finishProcessing() {
    this.isProcessCompleted = true;
    this.statusText = "Completed";
    this.summaryTxt.htmlText = this.processLog.join("\n");
  }

  cancel() {
    if (confirm("Are you sure you want to cancel the process? Progress will not be reversed.")) {
      clearInterval(this.processInterval);
      this.statusText = "Cancelled";
      this.processLog.push("Process was cancelled by user.");
      this.updateLog();
      this.cleanUpAfterCancel();
      this.isProcessCompleted = true;
    }
    }

  cleanUpAfterCancel() {
    this.inputData.url = this.baseURL + "multipleMvtActions.do?method=cleanTempProcess";
    this.inputData.send([]);
  }

  closeAndExit() {
    // Logic for closing the popup and exiting
    if(this.titleWindow) {
      this.close();
      window.close();
    }
  }

  closeAndReturn() {
    // Logic for closing and returning to the previous screen
    this.parentDocument.refreshMovementList();
    
    if(this.titleWindow) {
      this.close();
    } else {
      window.close();
    }
   
  }

  generateReport() {
    // Start with the movement list
    //let queryParams = `method=exportMovements&movementList=${encodeURIComponent(this.movementList)}`;
    let queryParams = `method=exportMovements&seq=${encodeURIComponent(this.seq)}`;
    
    // Add note from p_note_text
    if (this.requestParamsSent.p_note_text) {
      queryParams += `&note=${encodeURIComponent(this.requestParamsSent.p_note_text)}`;
    }
    
    // Parse the JSON values to extract individual parameters
    let columnValues : any = {};
    if (this.requestParamsSent.p_json_values && this.requestParamsSent.p_json_values !== '[]') {
      try {
        const jsonValues = JSON.parse(this.requestParamsSent.p_json_values);
        
        // Convert the array of {column_name, column_value} objects to a simple object
        jsonValues.forEach(item => {
          // Convert database column names to the parameter names expected by the report
          switch (item.column_name) {
            case 'PREDICT_STATUS':
              columnValues.predictStatus = item.column_value;
              break;
            case 'EXT_BAL_STATUS':
              columnValues.externalStatus = item.column_value;
              break;
            case 'ILM_FCAST_STATUS':
              columnValues.ilmFcastStatus = item.column_value;
              break;
            case 'BOOKCODE':
              columnValues.book = item.column_value;
              break;
            case 'ORDERING_INSTITUTION':
              columnValues.orderingInst = item.column_value;
              break;
            case 'CRITICAL_PAYMENT_TYPE':
              columnValues.critPayType = item.column_value;
              break;
            case 'COUNTERPARTY_ID':
              columnValues.counterParty = item.column_value;
              break;
            case 'EXPECTED_SETTLEMENT_DATETIME':
              columnValues.expSettlAsString = item.column_value;
              break;
            case 'SETTLEMENT_DATETIME':
              columnValues.actSettlAsString = item.column_value;
              break;
          }
        
        });
      } catch (e) {
        console.error("Error parsing JSON values:", e);
      }
    }
    
    // Add all the extracted parameters to the query string
    if (columnValues.predictStatus) {
      queryParams += `&predictStatus=${encodeURIComponent(columnValues.predictStatus)}`;
    }
    if (columnValues.externalStatus) {
      queryParams += `&externalStatus=${encodeURIComponent(columnValues.externalStatus)}`;
    }
    if (columnValues.ilmFcastStatus) {
      queryParams += `&ilmFcastStatus=${encodeURIComponent(columnValues.ilmFcastStatus)}`;
    }
    if (columnValues.book) {
      queryParams += `&book=${encodeURIComponent(columnValues.book)}`;
    }
    if (columnValues.orderingInst) {
      queryParams += `&orderingInst=${encodeURIComponent(columnValues.orderingInst)}`;
    }
    if (columnValues.critPayType) {
      queryParams += `&critPayType=${encodeURIComponent(columnValues.critPayType)}`;
    }
    if (columnValues.counterParty) {
      queryParams += `&counterParty=${encodeURIComponent(columnValues.counterParty)}`;
    }
    if (columnValues.expSettlAsString) {
      queryParams += `&expSettlAsString=${encodeURIComponent(columnValues.expSettlAsString)}`;
    }
    if (columnValues.actSettlAsString) {
      queryParams += `&actSettlAsString=${encodeURIComponent(columnValues.actSettlAsString)}`;
    }
    queryParams += `&action=${encodeURIComponent(this.actionSent)}`;

    
    // Add the action parameter
    queryParams += `&action=${encodeURIComponent(this.actionSent)}`;
  
    const reportUrl = `${this.baseURL}${this.actionPath}${queryParams}`;
    console.log("🚀 ~ ProcessWindow ~ generateReport ~ reportUrl:", reportUrl);
    window.open(reportUrl, "_blank"); // Opens report download
  }
  
}