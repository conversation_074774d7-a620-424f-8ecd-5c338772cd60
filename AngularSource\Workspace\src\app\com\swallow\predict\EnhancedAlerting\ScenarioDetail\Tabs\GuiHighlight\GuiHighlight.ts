import {Component, ElementRef, ViewChild, OnInit} from '@angular/core';
import {
  SwtAlert,
  CommonService,
  SwtModule, SwtCanvas, SwtCommonGrid, SwtUtil, ExternalInterface, SwtLabel, SwtCheckBox
} from 'swt-tool-box';
declare var instanceElement: any;

@Component({
  selector: 'app-gui-highlight',
  templateUrl: './GuiHighlight.html',
  styleUrls: ['./GuiHighlight.css']
})
export class GuiHighlight extends SwtModule implements OnInit {

  @ViewChild('highlightCanvas') highlightCanvas: SwtCanvas;
  @ViewChild('noteLbl') noteLbl: SwtLabel;
  @ViewChild('noteTxt') noteTxt: SwtLabel;
  @ViewChild('critGuiHighLbl') critGuiHighLbl: SwtLabel;
  @ViewChild('critGuiHighCheck') critGuiHighCheck: SwtCheckBox;
  

  private swtalert: SwtAlert;
  public guiGrid: SwtCommonGrid;
  public operation : string = "";
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }
  public static ngOnDestroy(): any {
    instanceElement = null;
  }
  ngOnInit() {
    instanceElement = this;
    this.guiGrid = <SwtCommonGrid>this.highlightCanvas.addChild(SwtCommonGrid);
    this.guiGrid.editable = true;
    this.noteLbl.text = SwtUtil.getPredictMessage("scenario.guiHighlight.note.title", null);
    this.noteTxt.htmlText = SwtUtil.getPredictMessage("scenario.guiHighlight.note.text", null);
    this.critGuiHighLbl.text= SwtUtil.getPredictMessage("scenario.guiHighlight.critGuiHighLbl.text", null);
    //this.critGuiHighCheck.toolTip= SwtUtil.getPredictMessage("scenario.guiHighlight.critGuiHighCheck.title", null);

  }

  onLoad() {
    this.guiGrid.ITEM_CLICK.subscribe((selectedCell) => {
      this.cellClick(selectedCell);
    });
    this.guiGrid.onRowClick = ()=>{
      this.rowClick();
    };

  }

  cellClick(selectedCell) {
    if(selectedCell.target.field == "guiSelect" ) {
      if(selectedCell.target.data.guiSelect == "Y"){
        selectedCell.target.data.guiSelect == "N"
      }else{
        selectedCell.target.data.guiSelect == "Y"
      }
    }
  }





  rowClick() {
    if(this.guiGrid.selectedIndex >= 0 && this.parentDocument.methodName != "view") {

    } else {

    }
  }
  addGuiHandler() {
    this.operation = "add";
    ExternalInterface.call("guiHighlightSub", "add")
  }


  changeGuiHandler() {
    this.operation = "change";
    ExternalInterface.call("guiHighlightSub", "change")
  }


  refreshParent( idDescription: string, xml: string) {
    if(this.operation =="add") {

    } else {

    }

  }

}
