<SwtModule (creationComplete)='onLoad()' width="100%"  height="100%">
  <VBox  id="appContainer" #appContainer width='100%' height='100%' paddingLeft="5" paddingRight="5" verticalGap="0">
    <HBox height="2%" width="100%">
      <HBox width="99%"></HBox>
      <HBox horizontalAlign="right" width="1%">
        <SwtButton toolTip="Hide Control Bar" #imgShowHideControlBar id="imgShowHideControlBar"
                   (click)="showHideControlBar()"
                   styleName="minusIcon">
        </SwtButton>
      </HBox>
    </HBox>
    <SwtCanvas height="10%" minWidth="900" width="99%" marginTop="-5" #swtControlBar>
      <HBox width="100%" height="100%">
        <Grid verticalGap="2" height="100%" width="80%" >
          <GridRow width="50%" height="100%" paddingLeft="10">
            <GridItem width="110" height="100%">
              <SwtLabel  #entityLabel
                        paddingTop="0"
                        paddingLeft="5"
                        fontWeight="bold"></SwtLabel>
            </GridItem>
            <GridItem width="160" height="100%">
              <SwtComboBox dataLabel="entity" #cbEntity
                           id="cbEntity"
                           width="135"
                           (change)="entityChangeCombo($event)"></SwtComboBox>
            </GridItem>
            <GridItem width="20%" height="100%">
              <SwtLabel id="selectedEntity"  #selectedEntity
                        paddingTop="2"
                        fontWeight="normal"
                        textAlign="right"></SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow width="50%" height="100%" paddingLeft="10">
            <GridItem width="110" height="100%">
              <SwtLabel #currencyLabel
                        paddingTop="0"
                        paddingLeft="5"
                        fontWeight="bold"></SwtLabel>
            </GridItem>
            <GridItem width="160" height="100%">
              <SwtComboBox dataLabel="currency" #cbCurrency
                           id="cbCurrency"
                           width="135"
                           (change)="currencyChangeCombo($event)">

              </SwtComboBox>
            </GridItem>
            <GridItem width="20%" height="100%">
              <SwtLabel id="selectedCurrency" #selectedCurrency
                        textAlign="right"
                        paddingTop="2"
                        fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridRow>
        </Grid>
        <HBox paddingLeft="3" height="100%" width="20%" horizontalAlign="right">
                <SwtLabel  #templateIdLabel
                          paddingTop="-4"
                          fontWeight="bold"></SwtLabel>
                <SwtLabel id="lblTemplateId" #lblTemplateId
                          paddingTop="-4"
                          fontWeight="bold"></SwtLabel>

          </HBox>
        <HBox width="50%" horizontalAlign="right" includeInLayout="false" visible="false">
          <fieldset>
            <legend>Breakdown</legend>
            <SwtRadioButtonGroup #breakdown id="breakdown" align="horizontal">
              <SwtRadioItem value="M"
                            groupName="breakdown"
                            id="movementRadio" #movementRadio></SwtRadioItem>
              <SwtRadioItem  value="B"
                             groupName="breakdown"
                             id="bookRadio" #bookRadio></SwtRadioItem>
            </SwtRadioButtonGroup>
          </fieldset>
        </HBox>
      </HBox>
    </SwtCanvas>
    <VBox  id="vbGridContainer" #vbGridContainer minWidth="900" width="100%"  height="80%">
      <HDividedBox id="cvGridContainer" #cvGridContainer  width="100%" height="100%">
        <SwtCanvas class="left"
                   id="customGrid"
                   #customGrid
                   width="50%"
                   height="100%"
                   borderStyle="solid"
                   cornerRadius="4"
                   dropShadowEnabled="true"
                   borderColor="#f9f9f9" >
            <SwtCommonGrid id="forecastMonitorGrid"
                           #forecastMonitorGrid
                          width="100%"
                           height="100%">

            </SwtCommonGrid>
        </SwtCanvas>
        <SwtCanvas class="right"
                   id="customGridSub"
                     #customGridSub
                     width="50%"
                     height="100%"
                     borderStyle="solid"
                     cornerRadius="4"
                     dropShadowEnabled="true"
                     borderColor="#f9f9f9" >
              <SwtCommonGrid
                id="forecastMonitorGridSub"
                #forecastMonitorGridSub
                width="100%"
                height="100%">
              </SwtCommonGrid>
          </SwtCanvas>
      </HDividedBox>
    </VBox>
    <HBox width="99%"></HBox>
    <HBox height="2%" width="100%">
      <HBox width="99%"></HBox>
      <HBox horizontalAlign="right" width="1%">
        <SwtButton #imgShowHideButtonBar
                   id="imgShowHideButtonBar"
                   (click)="showHideButtonBar()"
                   styleName="minusIcon"></SwtButton>
      </HBox>
    </HBox>
    <SwtCanvas id="swtButtonBar" #swtButtonBar minWidth="900" marginTop="-5" width="99%" height="5%">
      <HBox width="100%">
        <HBox width="60%">
          <SwtButton #refreshButton
                     id="refreshButton"
                     (click)="updateData('yes')"
                     [buttonMode]="true"></SwtButton>
          <SwtButton #rateButton
                     id="rateButton"
                     (click)="rateHandler()"></SwtButton>
          <SwtButton #optionsButton
                     id="optionsButton"
                     (click)="optionClick()"></SwtButton>
          <SwtButton  #closeButton
                      (click)="closeHandler()"
                      id="closeButton"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" >
            <SwtText id="dataBuildingText"
                     #dataBuildingText
                     width="183"
                     height="16"
                     color="red"
                     fontWeight="bold"
                     text="DATA BUILD IN PROGRESS"
                     visible="false">
            </SwtText>
            <SwtText id="lostConnectionText"
                     #lostConnectionText
                     color="red"
                     fontWeight="bold"
                     height="16"
                     text="CONNECTION ERROR"
                     (click)="connError()"
                     visible="false">
            </SwtText>
            <SwtText #lastRef
                       id="lastRef"
                       height="16">
            </SwtText>
            <SwtText id="lastRefTime"
                       #lastRefTime
                       height="16">
            </SwtText>
            <div>
              <DataExport #exportContainer id="exportContainer"></DataExport>
            </div>

            <SwtHelpButton id="helpIcon"
                           #helpIcon
                           (click)="doHelp()">
            </SwtHelpButton>
            <SwtLoadingImage id="loadingImage" #loadingImage></SwtLoadingImage>
          </HBox>
      </HBox>
    </SwtCanvas >
  </VBox>
</SwtModule>
