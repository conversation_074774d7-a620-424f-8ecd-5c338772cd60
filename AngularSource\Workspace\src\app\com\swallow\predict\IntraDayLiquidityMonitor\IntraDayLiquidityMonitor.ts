import { Component, OnInit, NgModule, ModuleWithProviders, ViewChild, ElementRef } from '@angular/core';
import {
  SwtToolBoxModule,
  SwtTabNavigator,
  Tab,
  SwtUtil,
  ExternalInterface,
  Timer,
  HTTPComms,
  CommonService,
  SwtAlert,
  JSONReader,
  SwtModule,
  SwtDateField,
  SwtLoadingImage,
  SwtButton,
  SwtComboBox,
  SwtTextInput,
  SwtCheckBox,
  SwtLabel,
  Alert,
  CommonUtil,
  CommonLogic,
  Series,
  CheckBoxLegendItem,
  ILMLineChart,
  SeriesStyleProvider,
  SwtPopUpManager,
  ModuleLoader,
  ModuleEvent,
  Keyboard,
  ProcessStatusBox,
  ContextMenuItem, ScreenVersion, JSONViewer, StringUtils, TabSelectEvent, TabPushStategy
} from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
import { CombinedView } from './tabs/CombinedView/CombinedView';
import { GroupAnalysis } from './tabs/GroupAnalysis/GroupAnalysis';
import { GlobalView } from './tabs/GlobalView/GlobalView';
declare var require: any;
const $ = require('jquery');
import moment from "moment";
declare var instanceElement: any;

@Component({
  selector: 'app-intra-day-liquidity-monitor',
  templateUrl: './IntraDayLiquidityMonitor.html',
  styleUrls: ['./IntraDayLiquidityMonitor.css']
})

export class IntraDayLiquidityMonitor extends SwtModule implements OnInit {
  @ViewChild('tabs') tabNavigator: SwtTabNavigator;
  @ViewChild('combinedViewTab') combinedView: CombinedView;
  @ViewChild('groupAnalysisTab') groupAnalysis: GroupAnalysis;
  @ViewChild('globalViewTab') globalView: GlobalView;
  /***********SwtDateField***********/
  @ViewChild('valueDate') valueDate: SwtDateField;


  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('profileCombo') profileCombo: SwtComboBox;


  @ViewChild('timeframe') timeframe: SwtTextInput;

  @ViewChild('saveProfileImage') saveProfileImage: SwtButton;
  @ViewChild('revertProfileImage') revertProfileImage: SwtButton;
  @ViewChild('deleteProfileImage') deleteProfileImage: SwtButton;

  @ViewChild('valueDateLabel') valueDateLabel: SwtLabel;
  @ViewChild('ccyTimeLabel') ccyTimeLabel: SwtLabel;

  @ViewChild('processStatus') processStatus: ProcessStatusBox;



  public combinedViewTab: TabPushStategy;
  public groupAnalysisTab: TabPushStategy;
  public globalViewTab: TabPushStategy;

  private swtAlert: SwtAlert;

  private screenName: string = "";
  private versionNumber: string = "1.0";
  private errorLocation = 0;
  public entityTabName: string = "";
  public ccyTabName: string = "";



  /**
   * Timer Objects
   **/
    //Main Timer.
  private autoRefresh: Timer;
  private refreshRate: number = 10;
  /**
   * Logic Objects
   **/
    //TODO:
  private ilmLogic: IlmLogic = new IlmLogic();
  private comboOpen: Boolean = false;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  public iLMConfData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private invalidComms: string = null;
  private actionMethod: string = "";
  private actionPath: string = "";
  private ilmConfParams: Object;
  private requestParams = [];
  public previousCCy: String = null;
  public previousEntity: String = null;


  public currencyMutiplierValue: number = 1;

  public currencyDecimalPlaces: number = 2;

  /**
   * Popup Objects
   **/
  // private showXML:ShowXML;
  /**
   * Data Objects
   **/
  private jsonReader: JSONReader = new JSONReader();

  public lastRecievedJSON;
  public prevRecievedJSON;

  private systemDate: string;
  private menuAccessId: string;

  public refValueChanged: boolean = false;

  //TODO:
  //private  popup:SeriesStylePopup;

  //TODO:
  //	public  currFormatter:CurrencyFormatter = new CurrencyFormatter();
  public currencyFormat: string;
  public recalculateData: Boolean = false;
  //Export cancel function (callback function)
  private _exportCancelFunction: any = null;

  //Current cancel popup window
  //TODO:
  //public  _cancelPopup: ExportInProgress = null;
  private ccyMultiplierLabelValue: string = '';
  private recalculationResult: string = '';
  private previousSelectedEntity: number = -1;
  private previousSelectedCcy: number = -1;
  public processStatusHTTP = new HTTPComms(this.commonService);
  public dataStateHTTPComm = new HTTPComms(this.commonService);
  public recalculateDAta = new HTTPComms(this.commonService);
  public profileHTTPCom = new HTTPComms(this.commonService);

  private processStateTimer: Timer = null;
  public isRecalculateAlertShown: boolean = false;
  public isCalculationFinished: boolean = true;
  //TODO:
  //public saveProfilePopup:SaveProfilePopup;
  private lastProfileAction: string = null;

  private lastUsedProfile: string = null;

  public globalLegendsBoxWidth: number = -1;
  public analysisLegendsBoxWidth: number = -1;

  public lastSelectedTab: number = 1;

  public dateFormat: string = "";

  public combinedGlobalLegendsBoxWidth: number = -1;
  public combinedAnalysisLegendsBoxWidth: number = -1;
  public lastSelectedIndex: number;
  public lastSelectedDate : Date;
  public ilmTabId = null;
  public lastRefTime;
  private saveProfilePopupWindow;
  public screenVersion = new ScreenVersion(this.commonService);
  private showJSONPopup: any;
  private tabDateFromOption : string;
  private haveAnalysisTab = false;
  private haveGlobalTab = false;
  private haveCombinedTab = false;
  private firstLoadFlag = false;
  dataState: any;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    if (window.addEventListener) {
      window.addEventListener("message", this.receiver.bind(this), false);
    } else {
      (<any>window).attachEvent("onmessage", this.receiver.bind(this));
    }

  }


  //  receiveMessage (){
  // }
  public static ngOnDestroy(): any {
    instanceElement = null;
  }

  receiver(e) {
    if (e && e.data) {
      var methodName = e.data[0];
      var argumentsList = e.data[1];
      if (this[methodName]) {
        this[methodName].apply(this, argumentsList);
      }
    }

  }

  ngOnInit() {
    this.screenName = ExternalInterface.call('getBundle', 'text', 'label-screenName', 'Inra-Day Liquidity Monitor - Main Screen');
    instanceElement = this;
    this.valueDateLabel.text = ExternalInterface.call('getBundle', 'text', 'valuedate', 'Value Date');
    this.ccyTimeLabel.text = ExternalInterface.call('getBundle', 'text', 'timeframe', 'Ccy Timeframe');
    this.timeframe.toolTip = ExternalInterface.call('getBundle', 'tip', 'timeframe', 'Timeframe Of The Currency');

    this.profileCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'combo-profile', 'Select profile');
    this.saveProfileImage.toolTip = ExternalInterface.call('getBundle', 'tip', 'combo-saveprofile', 'Save profile');
    this.revertProfileImage.toolTip = ExternalInterface.call('getBundle', 'tip', 'combo-reloadProfile', 'Reload profile');
    this.deleteProfileImage.toolTip = ExternalInterface.call('getBundle', 'tip', 'combo-deleteprofile', 'Delete profile');
    this.processStatus.parentDocument = this;
    //   $(document).on('DOMNodeInserted', '.iframeiframe', function () {

    // });
    this.ilmTabId = (this.entityTabName + "/"+this.ccyTabName);
    TabSelectEvent.subscribe((tab) => {
      if(tab) {
        
        if(tab === this.ilmTabId){
          // setTimeout(() => {
            this.tabChanged();
            if(!this.isRefreshDone)
              this.dataRefresh(null);
          // }, 2001);
        }
        if(tab === "updateTabs#"+this.ilmTabId){
          if(this.ilmTabId == this.parentDocument.tabNavigator.getSelectedTab().id){
            this.dynamicCreation();
          }else{
            this.dynamicCreation(true);
          }
          // setTimeout(() => {
          //   this.onLoad(false);
          // }, 2500);
        }else if (tab == "useCurrencyMutliper#true"){
          this.updateValuesUsingCcyMultiplier(true);
        }else if (tab == "useCurrencyMutliper#false"){
          this.updateValuesUsingCcyMultiplier(false);
        }else if(tab === "updateSeriesStyle#"+this.ilmTabId){
          this.saveChangesForILMStyles(this.parentDocument.seriesStyleIsGlobal, this.parentDocument.seriesStyleComboboxValues, this.parentDocument.seriesStyleSizeOfAllCombo)
        }else if((""+tab).indexOf("changeRefreshRate#") != -1){
          this.refreshRate = parseInt((""+tab).split('#')[1]);
          this.autoRefresh.stop();
            this.autoRefresh = new Timer(( this.refreshRate * 1000 * 60), 0);
            this.autoRefresh.addEventListener("timer",  this.dataRefresh.bind(this));
            if ( this.autoRefresh != null) {
              if (! this.autoRefresh.running)
                this.autoRefresh.start();
            }
        }

      }
      });
      }
  dynamicCreation(fromUpdate= false): void {
    /**dynamic creation*///
try {

    this.firstLoadFlag=false;
    for(let i =0; i< this.parentDocument.tabsConfig.length; i++) {
      if(this.ilmTabId == this.parentDocument.tabsConfig[i].id) {
        if(!fromUpdate){
          this.tabDateFromOption = this.parentDocument.tabsConfig[i].selectedDate;
          if( this.tabDateFromOption != ""){
            if(this.valueDate.text == undefined || StringUtils.isEmpty(this.valueDate.text) )
              this.valueDate.selectedDate = moment(this.tabDateFromOption, this.dateFormat.toUpperCase()).toDate();
          }
        }

        if(this.parentDocument.tabsConfig[i].globalIsfound && !this.haveGlobalTab) {
          // this.globalViewTab = <Tab>this.tabNavigator.addChild(Tab);
          // this.globalViewTab.id = 'globalViewTab'+this.parentDocument.tabsConfig[i].id
          // this.globalViewTab.label = 'GlobalView';
          this.globalView = <GlobalView>this.globalViewTab.addChild(GlobalView);
          
          this.globalView.parentDocument = this;
          this.globalView.tabName = "GlobalView";
          this.globalView.id = 'globalViewTab'+this.ilmTabId;
          this.globalView.ilmTabId = this.ilmTabId;
          this.haveGlobalTab = true;
          this.globalViewTab.visible = true;
         this.firstLoadFlag=true;

        } else {
          this.globalViewTab.visible = this.parentDocument.tabsConfig[i].globalIsfound; 
          if(this.haveGlobalTab && !this.parentDocument.tabsConfig[i].globalIsfound){
            // this.tabNavigator.selectedIndex = 1;
            // this.globalViewTab.removeChild(this.globalView);
            // this.tabNavigator.removeChild(this.globalViewTab);
            // this.globalView = null;
            // this.globalViewTab = null;
            this.haveGlobalTab = false;
        }

          
        }
        if(this.globalView && !fromUpdate){
          setTimeout(() => {
            this.globalView.initData();
             this.globalView.resetScreen();
             this.globalView.onLoad(true);
         }, 1000);
        }
        
        if(this.parentDocument.tabsConfig[i].grpAnalysIsfound && !this.haveAnalysisTab){
          // this.groupAnalysisTab = <Tab>this.tabNavigator.addChild(Tab);
          // this.groupAnalysisTab.id = 'groupAnalysisTab'+this.parentDocument.tabsConfig[i].id
          // this.groupAnalysisTab.label = 'GroupAnalysis';
          // if(this.haveGlobalTab)
          
          this.groupAnalysis = <GroupAnalysis>this.groupAnalysisTab.addChild(GroupAnalysis);
          this.firstLoadFlag=true;
          // else
          //   this.groupAnalysis = <GroupAnalysis>this.groupAnalysisTab.addChildAt(GroupAnalysis,0);
          
          this.groupAnalysis.parentDocument = this;
          this.groupAnalysis.tabName = "GroupAnalysis";
          this.groupAnalysis.id = 'groupAnalysisTab'+this.ilmTabId;
          this.groupAnalysis.ilmTabId = this.ilmTabId;
          this.groupAnalysis.optionsSelectedGroups = this.parentDocument.tabsConfig[i].selectedGroups;
          this.haveAnalysisTab = true;
          this.groupAnalysisTab.visible = true;

        } else {
          this.groupAnalysisTab.visible = this.parentDocument.tabsConfig[i].grpAnalysIsfound; 

          if(!this.parentDocument.tabsConfig[i].grpAnalysIsfound && this.haveAnalysisTab){
            // this.tabNavigator.selectedIndex = 0;
            // this.groupAnalysisTab.removeAllChildren();
            // this.groupAnalysisTab.removeChild(this.groupAnalysis);

            // this.tabNavigator.removeChild(this.groupAnalysisTab)
            // this.groupAnalysis = null;
            // this.groupAnalysisTab = null;
            this.haveAnalysisTab = false;
        }


        }

        if(this.groupAnalysis && !fromUpdate){
          setTimeout(() => {
              this.groupAnalysis.initData();
              this.groupAnalysis.resetScreen();
              this.groupAnalysis.onLoad(true);
          }, 1000);
        }

        if(this.parentDocument.tabsConfig[i].combinedIsfound && !this.haveCombinedTab) {
          // this.combinedViewTab = <Tab>this.tabNavigator.addChild(Tab);
          // this.combinedViewTab.id = 'combinedViewTab'+this.parentDocument.tabsConfig[i].id
          // this.combinedViewTab.label = this.combinedViewTab.id = "CombinedView"
          this.combinedView = <CombinedView>this.combinedViewTab.addChild(CombinedView);
          this.combinedView.parentDocument = this;
          this.haveCombinedTab = true;
          this.combinedViewTab.visible = true;
          this.firstLoadFlag=true;
          setTimeout(() => {
            this.combinedView.initData(this.combinedGlobalLegendsBoxWidth, this.combinedAnalysisLegendsBoxWidth,  this.globalView.legendsDivider, this.groupAnalysis.legendsDivider);
          }, 0);
        }else {
          this.combinedViewTab.visible = this.parentDocument.tabsConfig[i].combinedIsfound;
          if(!this.parentDocument.tabsConfig[i].combinedIsfound && this.haveCombinedTab){
            // this.combinedViewTab.removeChild(this.combinedView);
            // this.combinedViewTab.removeAllChildren();
            // this.tabNavigator.removeChild(this.combinedViewTab);
            // this.combinedView = null;
            // this.combinedViewTab = null;
            this.haveCombinedTab = false;
            
          }

         

        }
      }
    }
    setTimeout(() => {
      if (this.firstLoadFlag) {
        if (this.haveAnalysisTab && this.haveGlobalTab) {
          this.tabNavigator.selectedIndex = 1;
        } else if ((!this.haveAnalysisTab)) {
        this.tabNavigator.selectedIndex = 0;
        } else if ((!this.haveGlobalTab)) {
          this.tabNavigator.selectedIndex = 1;
        } else if ((!this.haveCombinedTab)) {
        this.tabNavigator.selectedIndex = 0;
        }
      } else {
        this.tabNavigator.selectedIndex = this.tabNavigator.selectedIndex;
      }

    }, 0);
    } catch (error) {
  
    }
  }
  onLoad() {
    try {
        this.globalViewTab = <TabPushStategy>this.tabNavigator.addChildPushStategy(TabPushStategy);
        this.globalViewTab.id = 'globalViewTab'+this.ilmTabId;
        this.globalViewTab.label = 'GlobalView';
        
        this.groupAnalysisTab = <TabPushStategy>this.tabNavigator.addChildPushStategy(TabPushStategy);
        this.groupAnalysisTab.id = 'groupAnalysisTab'+this.ilmTabId;
        this.groupAnalysisTab.label = 'GroupAnalysis';
        
        
        
        this.combinedViewTab = <TabPushStategy>this.tabNavigator.addChildPushStategy(TabPushStategy);
        this.combinedViewTab.id = 'combinedViewTab'+this.ilmTabId;
        this.combinedViewTab.label = this.combinedViewTab.id = "CombinedView"
        
      this.dynamicCreation();
        this.initializeMenus();

      // Initialize the http communication  for ilm conf.
      this.iLMConfData.cbResult = this.saveResult.bind(this);
      this.iLMConfData.cbFault = this.saveFault.bind(this);
      this.iLMConfData.encodeURL = false;

      // Initialize the context menu
      // It defines the format to be displayed in Date field
      this.dateFormat = ExternalInterface.call('eval', 'dateFormat') || "DD/MM/YYYY";
      this.ilmLogic.testDate = ExternalInterface.call('eval', 'dbDate');
      this.valueDate.formatString = this.dateFormat.toUpperCase();
      // Set the tooltip for date field
      if (this.dateFormat == "DD/MM/YYYY")
        this.valueDate.toolTip = ExternalInterface.call('getBundle', 'tip', 'valuedateDDMMYY', 'Enter value date (\'DD/MM/YYYY\')');
      else
        this.valueDate.toolTip = ExternalInterface.call('getBundle', 'tip', 'valuedateMMDDYY', 'Enter value date (\'MM/DD/YYYY\')');
      const refreshRateValue = ExternalInterface.call('eval', 'checkStateRefreshTime') || 30;
      this.processStateTimer = new Timer((refreshRateValue * 1000), 0);
      this.processStateTimer.addEventListener("timer", this.currentPorcessStateHandler.bind(this));

      this.processStateTimer.start();
      //Initialize the communication objects
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "ilmAnalysisMonitor.do?";
      this.actionMethod = "";
      this.requestParams["entityId"] = this.entityTabName;
      this.requestParams["currencyId"] = this.ccyTabName;
      this.requestParams["selectedDate"] = this.valueDate.text;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      // this.resetParamsWhenBusy(this.inputData, this.requestParams);
      this.inputData.send(this.requestParams);
      if(this.groupAnalysis)
       this.groupAnalysis.runCheckProfileOnLoad();
      if(this.globalView)
       this.globalView.runCheckProfileOnLoad();
    } catch (e) {
      console.log(e);


    }

  }

    //Resend ilm config if the httpCom is busy
    resetParamsWhenBusy(httpCom: HTTPComms, params) {
      if (httpCom.isBusy()) {
        setTimeout(() => {
          this.resetParamsWhenBusy(httpCom, params)
        }, 0);
      } else {
        httpCom.send(params);
      }
    }
    

  private resetDate(): void {
    this.valueDate.selectedDate = moment(this.jsonReader.getScreenAttributes()["valueDate"], this.dateFormat.toUpperCase()).toDate();
  }
  private isDateChanged(): boolean {
    let selectedDate = this.valueDate.selectedDate;
    if(!this.lastSelectedDate || this.lastSelectedDate.getTime() != selectedDate.getTime()){
      return true;
    }else {
      return false;
    }
  }
  chartCreationCompleteHandler(tabName){
    this.parentDocument.enableDisableTab(this.ilmTabId, true);
  }

  getDataState(){
    if (!this.dataStateHTTPComm.isBusy()) {
      // Calls the inputDataResult function to load the datagrid
      this.dataStateHTTPComm.cbResult = (data) => {
        this.getDataStateInputResult(data);
      };
      // Sets the action path for Interface Monitor
      this.actionPath = "ilmAnalysisMonitor.do?";
      // Sets the action method to get the Interface Monitor Details
      this.actionMethod = "method=getDataState";
      // Sets the full URL for Interface Monitor
      this.dataStateHTTPComm.url = this.baseURL + this.actionPath + this.actionMethod;
      //processStatus.url = "progressStatus.xml";
      // Sets the flag for encoding URL to false
      this.dataStateHTTPComm.encodeURL = false;
      // Calls the inputDataFault function
      this.dataStateHTTPComm.cbFault = this.inputDataFault.bind(this);
      // set attibutes
      this.requestParams["entityId"] = this.entityTabName;
      this.requestParams["currencyId"] = this.ccyTabName;
      this.requestParams["selectedDate"] = this.valueDate.text;
      // Send the request to the server
      this.dataStateHTTPComm.send(this.requestParams);
    }


  }


  getDataStateInputResult(event){
    var jsonReaderTmp: JSONReader = new JSONReader();
    jsonReaderTmp.setInputJSON(event);
    //Retrieves the XML from ResultEvent
    if (jsonReaderTmp.getRequestReplyStatus()) {
      this.dataState = jsonReaderTmp.getScreenAttributes()["dataState"] //event.dataState;
      if(this.dataState== "M"){
        this.swtAlert.show(SwtUtil.getPredictMessage("ilmSummary.alert.groupStagedNotFound") , ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
      }else if(this.dataState == "C"){
        this.swtAlert.show(SwtUtil.getPredictMessage("ilmSummary.alert.dataFromArchiveNotFound") , ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
      }

    }
  }
  /**
   * Update the data, this is called whenever a fresh of the data is required.
   * This could be called from either a change in a combobox selection of from the timer
   **/
  public updateData(event, refreshAll: boolean = false): void {
    try {

      if (event != null) {
        if (event == "valueDate") {

          if (this.validateDateField(this.valueDate)) {
            if(!this.isDateChanged()){
              return;
            }
            let testDateAsDate = moment(this.ilmLogic.testDate, this.dateFormat.toUpperCase()).toDate();
            if (this.valueDate.selectedDate > testDateAsDate) {
              this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-dateOutsideRange', 'The selected date is outside the range, please chose another date'));
              // callLater(resetDate);
              setTimeout(() => {
                this.resetDate();
              }, 0);
              return;
            }
            this.isRecalculateAlertShown = false;
          } else {
            setTimeout(() => {
              this.resetDate();
            }, 0);
            return;
          }
        }
        // Disable the Group Analysis and Combined View tabs if the selected item is All in entity combo.
        else if (event == "entityCombo" || event == 'ccyCombo') {
          this.groupAnalysis.resetSelectedIndex();
          this.globalView.resetSelectedIndex();
          this.groupAnalysis.runCheckProfileOnLoad();
          this.globalView.runCheckProfileOnLoad();
          if (event == "entityCombo") {
            this.groupAnalysis.calledwhenEntityChanged = true;
            this.globalView.calledwhenEntityChanged = true;
            this.entityChanged();
            return;
          }

        }

        else if (this.entityTabName == "All") {
          this.requestParams = [];
          this.actionPath = "ilmAnalysisMonitor.do?";
          this.actionMethod = "";
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
          this.inputData.send(this.requestParams);
          return;
        }

        if (event != "refreshButton") {
          this.isRecalculateAlertShown = false;
          if(this.groupAnalysis){
          this.groupAnalysis.linechart.removeAllSeries();
          this.groupAnalysis.thresholdsLegends.removeAllChildren();
          }
          if(this.globalView){
          this.globalView.linechart.removeAllSeries();
          this.globalView.thresholdsLegends.removeAllChildren();

        }

        }
      }

      var uncheckedLengendInGlobal = [];
      var uncheckedLengendInAnalalysis = [];
      var legendItem: CheckBoxLegendItem;


      if(this.globalView){
      this.globalView.resetScreen();
      var globalInputData: HTTPComms = new HTTPComms(this.commonService);
      globalInputData.cbStart = this.startOfComms.bind(this);
      globalInputData.cbStop = this.endOfComms.bind(this);
      globalInputData.cbResult = (data) => {
        this.globalView.inputDataResultGrid(data);
      };
      globalInputData.cbFault = this.inputDataFault.bind(this);
      globalInputData.encodeURL = false;
      uncheckedLengendInGlobal = this.globalView.balanceLegend.getUncheckedLegends();
      const unselectedGlobalAccumulatedDCLegend = this.globalView.AccumulatedDCLegend.getUncheckedLegends();
      for (let k = 0; k < unselectedGlobalAccumulatedDCLegend.length; k++) {
        uncheckedLengendInGlobal.push(unselectedGlobalAccumulatedDCLegend[k]);
      }

      }
      if(this.groupAnalysis){

        this.groupAnalysis.resetScreen();
      uncheckedLengendInAnalalysis = this.groupAnalysis.balanceLegend.getUncheckedLegends();
      const unselectedAccumulatedDCLegend = this.groupAnalysis.AccumulatedDCLegend.getUncheckedLegends();
      for (let k = 0; k < unselectedAccumulatedDCLegend.length; k++) {
        uncheckedLengendInAnalalysis.push(unselectedAccumulatedDCLegend[k]);
      }

      }
     

    


  


      //Reset screen settings
      if (event != null && (event == "refreshButton" || event == "valueDate")) {
        if(this.globalView != null)
        this.globalView.resetScreen(false);
        if(this.groupAnalysis)
        this.groupAnalysis.resetScreen(false);
      } else {
        if(this.groupAnalysis){
        this.groupAnalysis.resetScreen(true);

        }
        if(this.globalView){
          this.globalView.resetScreen(true);

        }
        if (event != null) {
          this.refreshProfileList(this.entityTabName, this.ccyTabName);
          if(this.groupAnalysis){
          this.groupAnalysis.onLoadisCalled = true;
          this.groupAnalysis.runCheckProfileOnLoad();
          }
          if(this.globalView){
            this.globalView.onLoadisCalled = true;
          this.globalView.runCheckProfileOnLoad();
        }
        }
        else {
          if(this.groupAnalysis)
          this.groupAnalysis.onLoadisCalled = false;
          if(this.globalView)
          this.globalView.onLoadisCalled = false;
        }
      }
      if(this.globalView)
      this.globalView.uncheckedItemsInLengend = uncheckedLengendInGlobal;
      if(this.groupAnalysis)
      this.groupAnalysis.uncheckedItemsInLengend = uncheckedLengendInAnalalysis;
      this.previousSelectedCcy = -1; //TODO  this.ccyCombo.selectedIndex;
      this.requestParams = [];
      this.requestParams["entityId"] = this.entityTabName;
      this.requestParams["currencyId"] = this.ccyTabName;

      if (event != null && event == 'ccyCombo') {
        this.requestParams["currencyChanged"] = true;
      } else {
        this.requestParams["currencyChanged"] = false;
      }

      // this.requestParams["selectedDate"] = this.DateUtils.getDateAsString(this.valueDate);
      this.requestParams["selectedDate"] = this.valueDate.text;
      this.requestParams["useCcyMultiplier"] = "N";//useCcyMultiplier;
      this.requestParams["previousCurrency"] = this.previousCCy;
      this.requestParams["previousEntity"] = this.previousEntity;
      // Send the request to update the content for the global view tab
      this.actionMethod = "method=getGlobalTreeAndGridData";
      if(this.globalView){
      globalInputData.url = this.baseURL + this.actionPath + this.actionMethod;
      globalInputData.send(this.requestParams);

      }
      if(this.groupAnalysis){

      // Send the request to update the content for the group analysis tab
      this.actionMethod = "method=getGroupTreeAndGridData";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.cbResult = (data) => {
        this.groupAnalysis.inputDataResultGrid(data);
      };
      this.inputData.send(this.requestParams);
      }
      this.currentPorcessStateHandler(null);

    } catch (error) {
    }
  }



  // updateData(){
  //   this.globalView.onLoad(true);
  //   //this.globalView.onLoad(true);

  // }


  public refreshProfileList(entityId: string, ccyId: string): void {
    var selectedEntity: string = this.entityTabName;
    var selectedCcy: string = this.ccyTabName;

    //Initialize the communication objects
    this.profileHTTPCom.cbResult = (data) => {
      this.profileDataResult(data);
    };
    this.profileHTTPCom.cbFault = this.inputDataFault;
    this.profileHTTPCom.encodeURL = false;
    this.actionPath = "ilmAnalysisMonitor.do?";
    this.actionMethod = "method=getProfileList";
    this.requestParams = [];
    this.requestParams["entityId"] = selectedEntity;
    this.requestParams["currencyCode"] = selectedCcy;
    this.profileHTTPCom.url = this.baseURL + this.actionPath + this.actionMethod;
    this.profileHTTPCom.send(this.requestParams);

  }



  /**
   * Build elements of the screen such us the chart and slider to define the interval from XML
   **/
  private profileDataResult(event): void {
    //Retrieves the XML from ResultEvent
    this.isCalculationFinished = true;
    var profileXML = event;
    //Pass the XML received to xmlReader
    this.jsonReader.setInputJSON(profileXML);
    this.profileCombo.setComboData(this.jsonReader.getSelects());
    this.enableDisableProfileCombo();
  }

  private enableDisableProfileCombo(): void {
    if (this.profileCombo.selectedIndex == 0) {
      this.deleteProfileImage.enabled = false;
    }
    else {
      this.deleteProfileImage.enabled = true;
    }
    this.revertProfileImage.enabled = false;
  }

//fixme
  public showDataNotUpdatedAlert(recalculate: boolean): void {
    /*if (!this.refreshCB.selected) {
      if (recalculate && !this.isRecalculateAlertShown) {
        this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-errorDataNotUpToDate', "Existing ILM data is not entirely up to date. Current data will be shown.\nClick the flashing icon for more information"));
        this.isRecalculateAlertShown = true;
      }

    }*/
  }




  public tabChanged(): void {
    try {

      if (this.tabNavigator.selectedIndex == 0) {
        if (this.lastSelectedTab == 2) {
          this.combinedAnalysisLegendsBoxWidth = this.groupAnalysis.analysisLegendsBoxWidth;
          this.combinedGlobalLegendsBoxWidth = this.globalView.globalLegendsBoxWidth;
          this.combinedView.resetSubscriber();
        }else {
          if(this.groupAnalysis)
          this.analysisLegendsBoxWidth =  this.groupAnalysis.analysisLegendsBoxWidth;
        }
        this.enableDisableLabelsForCombined(false);
        if (this.combinedView != null && this.combinedView.borderContainerChartCombined.getChildren().length > 0){
          try {
            

            $("#"+ $.escapeSelector(this.ilmTabId)).find( "[name='"+this.globalView.id+"']" ).appendTo($(this.globalView.chartsContainer.domElement));
          } catch (error) {
            
          }
        }
        // this.globalView.setJSONDataForIFrameCharts();

        this.globalView.initData();


        setTimeout(() => {
          this.globalView.treeDivider.widthLeft = ''+this.globalView.globaltreeBoxWidth;

          if (this.globalLegendsBoxWidth != -1) {
            // this.globalView.legendsDivider.resizeDividedBox(null, true, this.globalLegendsBoxWidth);
            this.globalView.legendsDivider.setWidthRightWithoutEvent( '' + this.globalLegendsBoxWidth);
          }else {
            this.globalView.legendsDivider.widthRight = ''+this.globalView.globalLegendsBoxWidth
          }
          this.globalView.gridDivider.heightBottom = ''+this.globalView.globalgridBoxHeight;

        }, 0);

        // this.globalView.updateMinWidthMainScreen(this.globalView.treeDivider.buttonSelected, this.globalView.legendsDivider.buttonSelected);
        this.lastSelectedTab = 0;

      } else if (this.tabNavigator.selectedIndex == 1) {

        if (this.lastSelectedTab == 2) {
          this.combinedAnalysisLegendsBoxWidth = this.groupAnalysis.analysisLegendsBoxWidth;
          this.combinedGlobalLegendsBoxWidth = this.globalView.globalLegendsBoxWidth;
          this.combinedView.resetSubscriber();
        }else {
          if(this.globalView)
          this.globalLegendsBoxWidth =  this.globalView.globalLegendsBoxWidth;
        }

        this.enableDisableLabelsForCombined(false);
        if (this.combinedView != null && this.combinedView.secondContainer.getChildren().length > 0){
          $("#"+ $.escapeSelector(this.ilmTabId)).find( "[name='"+this.groupAnalysis.id+"']" ).appendTo($(this.groupAnalysis.chartsContainer.domElement));

        }

        this.groupAnalysis.initData();

        setTimeout(() => {
          this.groupAnalysis.treeDivider.widthLeft = ''+this.groupAnalysis.analysistreeBoxWidth;
          if (this.analysisLegendsBoxWidth != -1) {
            this.groupAnalysis.legendsDivider.setWidthRightWithoutEvent('' + this.analysisLegendsBoxWidth);
          }else {
            this.groupAnalysis.legendsDivider.widthRight = ''+this.groupAnalysis.analysisLegendsBoxWidth
          }
          this.groupAnalysis.gridDivider.heightBottom = ''+this.groupAnalysis.analysisgridBoxHeight;
        }, 0);
        this.lastSelectedTab = 1;
      } else if (this.tabNavigator.selectedIndex == 2) {
        try {
          this.analysisLegendsBoxWidth = this.groupAnalysis.legendsDivider.widthRightPixel?this.groupAnalysis.legendsDivider.widthRightPixel : this.groupAnalysis.analysisLegendsBoxWidth;
          this.globalLegendsBoxWidth = this.globalView.legendsDivider.widthRightPixel?this.globalView.legendsDivider.widthRightPixel : this.globalView.globalLegendsBoxWidth;
          // Enable the labels that will be shown in the combined view tab for both charts (global + group)

          this.enableDisableLabelsForCombined(true);
          $("#"+ $.escapeSelector(this.ilmTabId)).find( "[name='"+this.groupAnalysis.id+"']" ).appendTo($(this.combinedView.secondContainer.domElement));
          $("#"+ $.escapeSelector(this.ilmTabId)).find( "[name='"+this.globalView.id+"']" ).appendTo($(this.combinedView.borderContainerChartCombined.domElement));
          // Disable all the components, the user could not modify the selection in the combined tab
          this.combinedView.initData(this.combinedGlobalLegendsBoxWidth, this.combinedAnalysisLegendsBoxWidth,  this.globalView.legendsDivider, this.groupAnalysis.legendsDivider);
          this.lastSelectedTab = 2;

        } catch (error) {

        }
      }

    } catch (error) {

    }


  }

  private enableDisableLabelsForCombined(value: boolean): void {
    if(this.groupAnalysis){
    this.groupAnalysis.labelForCombined.visible = value;
    this.groupAnalysis.labelForCombined.includeInLayout = value
    }

    if(this.globalView){
    this.globalView.labelForCombined.visible = value;
    this.globalView.labelForCombined.includeInLayout = value;
  }
  }


  //FIXME:REMOVE THIS NOT NEEDED ANYMORE
  public entityChanged(): void {
    if (this.entityTabName == 'All') {
      this.globalView.resetScreen();
      this.groupAnalysis.resetScreen();
      this.inputData.cbResult = (data) => {
        this.isAllEntityAvailable(data);
      };
      this.actionMethod = "method=isAllEntityAvailable";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.requestParams["currencyId"] = this.ccyTabName;
      this.inputData.send(this.requestParams);

    } else {
      this.globalView.resetScreen();
      this.groupAnalysis.resetScreen();
      this.tabNavigator.getChildAt(1).enabled = true;
      this.tabNavigator.getChildAt(2).enabled = true;
      this.requestParams["entityChanged"] = "true";
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.actionMethod = "";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.requestParams["entityId"] = this.entityTabName;
      this.inputData.send(this.requestParams);
    }



  }

  //FIXME:REMOVE THIS NOT NEEDED ANYMORE
  /**
   * Build elements of the screen such us the chart and slider to define the interval from XML
   **/
  private isAllEntityAvailable(event): void {
    //Retrieves the XML from ResultEvent
    this.lastRecievedJSON = event;
    //Pass the XML received to xmlReader
    // Parse result json
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    if (this.jsonReader.getRequestReplyStatus()) {
      this.groupAnalysis.linechart.removeAllSeries();
      this.groupAnalysis.thresholdsLegends.removeAllChildren();
      this.globalView.linechart.removeAllSeries();
      this.globalView.thresholdsLegends.removeAllChildren();
      this.tabNavigator.selectedIndex = 0;
      this.tabNavigator.getChildAt(1).enabled = false;
      this.tabNavigator.getChildAt(2).enabled = false;
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.actionMethod = "";
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.requestParams["entityId"] = this.entityTabName;
      this.requestParams["currencyId"] = this.ccyTabName;
      this.requestParams["selectedDate"] = this.valueDate.text;
      this.inputData.send(this.requestParams);
    } else {
      // Alerts the user if any exceptions occured in the server side
      //FIXME:CHECK IF WORKING
      this.swtAlert.show(this.jsonReader.getRequestReplyMessage());
      //fixme fatma
      /*this.entityCombo.selectedIndex = this.previousSelectedEntity;
      this.ccyCombo.selectedIndex = this.previousSelectedCcy;*/
      this.updateData(null)
    }
  }


  inputDataResult(data): void {

    try {
      let jsonList = null;
      let header: string;
      let obj;
      // Get result as xml
      this.lastRecievedJSON = data;
      // Parse result json
      this.jsonReader.setInputJSON(this.lastRecievedJSON);



      if (this.inputData.isBusy())
        this.inputData.cbStop();
      else {
        //Retrieves the XML from ResultEvent
        this.lastRecievedJSON = data;
        //Pass the XML received to this.jsonReader
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        //Disabling visibility of lostConnectionText
        //fixme this.lastRefTime.text = this.jsonReader.getScreenAttributes()["lastRefTime"];
        this.lastRefTime = this.jsonReader.getScreenAttributes()["lastRefTime"];
        //Check the requst reply status in XML
        if (this.jsonReader.getRequestReplyStatus()) {
          // Condition to check the last received and previous xml to build data
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            this.valueDate.showToday = false;
            // Gets the selected value date
            if( this.tabDateFromOption != "")
              this.valueDate.selectedDate = moment(this.tabDateFromOption, this.dateFormat.toUpperCase()).toDate();
            else
             this.valueDate.selectedDate = moment(this.jsonReader.getScreenAttributes()["valueDate"], this.dateFormat.toUpperCase()).toDate();
            //var minDate = moment(this.jsonReader.getSingletons().minDate, this.dateFormat.toUpperCase(), true);
            this.valueDate.selectableRange = { rangStart: null, rangeEnd: this.valueDate.selectedDate };

            // Get the combo boxes value from xml
            /*this.entityCombo.setComboData(this.jsonReader.getSelects());
            this.ccyCombo.setComboData(this.jsonReader.getSelects());*/
            this.profileCombo.setComboData(this.jsonReader.getSelects());
            var dbLastUsedProfile: String = this.jsonReader.getScreenAttributes()["lastUsedProfile"];
            if (dbLastUsedProfile == "*") {
              if(this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content.toString().indexOf("*") == -1) {
                this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content = "*" + this.profileCombo.selectedLabel;
                this.profileCombo.selectedLabel = this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content;
              }

            }
            this.lastSelectedIndex = this.profileCombo.selectedIndex;
            this.enableDisableProfileCombo();


            // Get the current selection of refresh check box
            /* this.refreshCB.selected = (this.jsonReader.getScreenAttributes()["withrefresh"] == "Y") ? true : false;
             // Gets the current selection of ccy multiplier check box
             this.ccyMuliplierCB.selected = (this.jsonReader.getScreenAttributes()["currencymultiplier"] == "Y") ? true : false;*/
            // If we have no entiy/currencies defined for the user then an error should be shown
            if (this.entityTabName == "All") {
              //FIXME:CHECK IF NEEDED!!!
              // this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-noCcyEntity', 'There are no entity/currencies appropriately configured for your role'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
              return;
            }

            this.currencyFormat = this.jsonReader.getScreenAttributes()["currencyFormat"]; // either currencyPat1 or currencyPat2
            // Condition to check this.jsonReader databuilding

            if (!this.jsonReader.isDataBuilding()) {
              if (this.globalView && this.tabNavigator.selectedIndex == 0) {
                this.globalView.initData();
              } else if (this.groupAnalysis && this.tabNavigator.selectedIndex == 1) {
                this.groupAnalysis.initData();
              } else if (this.combinedView && this.tabNavigator.selectedIndex == 2) {
                this.combinedView.initData(this.combinedGlobalLegendsBoxWidth, this.combinedAnalysisLegendsBoxWidth,  this.globalView.legendsDivider, this.groupAnalysis.legendsDivider);
              }

            }
            // Checks and triggers the auto refresh, also sets the data grid style
            if (!this.autoRefresh) {

              //this.refreshText.text = "" + this.jsonReader.getRefreshRate();
              this.refreshRate = parseInt(this.jsonReader.getRefreshRate());

              this.autoRefresh = new Timer((this.refreshRate * 1000 * 60), 0);
              /*Set event listener to dataRefresh*/
              this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
            }
          }
        }
        else {
          // Alerts the user if any exceptions occured in the server side
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-contactAdmin', 'Error occurred, Please contact your System Administrator: \n') + this.jsonReader.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
        }
        if (this.autoRefresh != null) {
          if (!this.autoRefresh.running)
            this.autoRefresh.start();
        }
      }
      //fixme
      /*this.previousSelectedEntity = this.entityCombo.selectedIndex;
      this.previousSelectedCcy = this.ccyCombo.selectedIndex;*/
      // if(this.globalView) {
      //   this.globalView.resetScreen();
      //   this.globalView.onLoad(true);
      // }
      // if(this.groupAnalysis)
      //  this.groupAnalysis.resetScreen();

      //FIXME:
      if (this.entityTabName != "All") {
        if(this.groupAnalysis){

          // this.groupAnalysis.onLoad(true);

        }
      } else {
        this.tabChanged();
        this.tabNavigator.selectedIndex = 0;
        this.tabNavigator.getChildAt(1).enabled = false;
        this.tabNavigator.getChildAt(2).enabled = false;
      }
      setTimeout(() => {
        this.currentPorcessStateHandler(null);
      }, 0);


    } catch (e) {
      console.log(e);

    }



  }

  private isRefreshDone: boolean  = true;
  /**
   * This function is called by auto refresh rate to autorefresh the page
   **/
  private dataRefresh(event): void {
    if (this.parentDocument.refreshCB.selected){
      if(this.ilmTabId == this.parentDocument.tabNavigator.getSelectedTab().id){
        this.isRefreshDone = true;
      this.updateData(null, true);
      }else {
        this.isRefreshDone = false;
      }
    }
  }


  /**
   * Updates the line charts axis formatter function
   * */
  updateValuesUsingCcyMultiplier(ccyMuliplierCBValue): void {
    try {
//fixme fatmaaa
      this.setCcyMultiplierLabelValue();

    if(this.globalView != null) {
      this.globalView.updateValues(this.currencyMutiplierValue, this.currencyDecimalPlaces, this.currencyFormat, ccyMuliplierCBValue);
      this.globalView.redrawLiquidityZones();
      //Update data for IFrames
      if(this.globalView.linechart != null)
      this.globalView.linechart.callMethodInIframe('ccyMultiplierEventHandler', [ccyMuliplierCBValue]);
    }
    if(this.groupAnalysis != null) {
      this.groupAnalysis.updateValues(this.currencyMutiplierValue, this.currencyDecimalPlaces, this.currencyFormat, ccyMuliplierCBValue);
      this.groupAnalysis.redrawLiquidityZones();
      if(this.groupAnalysis.linechart != null)
      this.groupAnalysis.linechart.callMethodInIframe('ccyMultiplierEventHandler', [ccyMuliplierCBValue]);

    }


      //FIXME:test

        
 
    } catch (error) {

    }

  }


  public setCcyMultiplierLabelValue(): void {
    if (this.currencyMutiplierValue == 1000)
      this.ccyMultiplierLabelValue = '(T)';
    else if (this.currencyMutiplierValue == 1000000)
      this.ccyMultiplierLabelValue = '(M)';
    else if (this.currencyMutiplierValue == 1000000000)
      this.ccyMultiplierLabelValue = '(B)';
    else
      this.ccyMultiplierLabelValue = '';
    // this.ccyMultiplierLabel.text = this.ccyMultiplierLabelValue;
  }

  /**
   * Called when the user change the status of the refresh checkbox or when changing and focusing
   * out of the input text
   */
  public updateILMConf(event, profileValue): void {

    this.iLMConfData.url = this.baseURL + this.actionPath + "method=saveLiquidityMonitorConfig&";
    this.ilmConfParams = [];
    if (event == null) {
      this.lastProfileAction = "saveProfile";
      this.lastUsedProfile = profileValue;// (event as TextEvent).text;

      this.ilmConfParams["paramName"] = "profileName";
      this.ilmConfParams["paramValue"] = profileValue;
      this.ilmConfParams["entityId"] = this.entityTabName;
      this.ilmConfParams["currencyId"] = this.ccyTabName;
      this.ilmConfParams["isGeneral"] = "false";

    } else {
      //FIXME: fatma
      /*if (event == "refreshText") {
        this.autoRefresh.stop();

        if (this.refreshText.text == "" || parseInt(this.refreshText.text) == 0) {
          this.swtAlert.show(SwtUtil.getPredictMessage('alert.ilmanalysis.nonValidValue', null), "Error");
          return;
        }
        this.refreshRate = parseInt(this.refreshText.text);

        this.autoRefresh.stop();
        this.autoRefresh = new Timer(( this.refreshRate * 1000 * 60), 0);
        this.autoRefresh.addEventListener("timer",  this.dataRefresh.bind(this));
        if ( this.autoRefresh != null &&  this.refreshCB.selected) {
          if (! this.autoRefresh.running)
            this.autoRefresh.start();
        }
      }

      if (event == "refreshCB") {
        this.ilmConfParams["paramName"] = "withRefresh";
        this.ilmConfParams["paramValue"] = this.refreshCB.selected ? "Y" : "N";
      } else if (event == "refreshText" && this.refValueChanged) {
        this.ilmConfParams["paramName"] = "refreshRate";
        this.ilmConfParams["paramValue"] = this.refreshText.text;
      } else if (event == "ccyMuliplierCB") {
        this.ilmConfParams["paramName"] = "useCurrencyMultiplier";
        this.ilmConfParams["paramValue"] = this.ccyMuliplierCB.selected ? "Y" : "N";
      } else
      
      */
     if (event == "profileCombo") {

        this.ilmConfParams["paramName"] = "lastUsedProfile";
        this.ilmConfParams["entityId"] = this.entityTabName;
        this.ilmConfParams["currencyId"] = this.ccyTabName;

        if (this.profileCombo.selectedIndex == 0) {
          this.ilmConfParams["paramValue"] = "";
        } else {
          this.ilmConfParams["paramValue"] = String(this.profileCombo.selectedLabel).replace("*", "");
        }

      }
      this.ilmConfParams["isGeneral"] = "true";
    }
    this.iLMConfData.send(this.ilmConfParams);
  }


  inputDataFault(event): void {
    try {
      //lostConnectionText.visible=true;
      this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
      // If autoRefresh is not equal to null, start the timer.
      if (this.autoRefresh != null) {
        if (!this.autoRefresh.running)
          this.autoRefresh.start();
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.screenName, 'ClassName', 'inputDataFault', this.errorLocation);
    }
  }
  //FIXME:
  public isHttpComsBusy(): boolean {
    if(this.inputData.isBusy() || (this.globalView&& this.globalView.isBusyComms()) || (this.groupAnalysis &&this.groupAnalysis.isBusyComms())) {
      return true;
    }else {
      return false;
    }
  }
  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms(): void {
    /* 	if(isCalculationFinished){
      loadingImage.setVisible(false)
    }
    else */
    this.loadingImage.setVisible(true);
    this.valueDate.enabled = false;
    if(this.groupAnalysis) {
      this.groupAnalysis.alignScaleCB.enabled = false;
      this.groupAnalysis.includeOpenMvnts.enabled = false;
      this.groupAnalysis.sumByCutOff.enabled = false;
      this.groupAnalysis.sourceLiquidityTop.enabled = false;
    }
   if(this.globalView) {
    this.globalView.alignScaleCB.enabled = false;
    this.globalView.includeOpenMvnts.enabled = false;
    this.globalView.sumByCutOff.enabled = false;
    this.globalView.sourceLiquidityTop.enabled = false;
   }
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    if (!this.inputData.isBusy()) {
      if(!this.isHttpComsBusy()){
        this.loadingImage.setVisible(false);
      }
      this.valueDate.enabled = true;
    }
    if (this.isCalculationFinished) {

      //FIXME:
      if(this.groupAnalysis && !StringUtils.isEmpty(this.groupAnalysis.groupCombo.selectedItem.content) && !StringUtils.isEmpty(this.groupAnalysis.scenarioCombo.selectedItem.content)){
        this.groupAnalysis.alignScaleCB.enabled = true;
        this.groupAnalysis.includeOpenMvnts.enabled = true;
        this.groupAnalysis.sumByCutOff.enabled = true;
        this.groupAnalysis.sourceLiquidityTop.enabled = true;
      }
      if(this.globalView && !StringUtils.isEmpty(this.globalView.groupCombo.selectedItem.content) && !StringUtils.isEmpty(this.globalView.scenarioCombo.selectedItem.content)){
        this.globalView.alignScaleCB.enabled = true;
        this.globalView.includeOpenMvnts.enabled = true;
        this.globalView.sumByCutOff.enabled = true;
        this.globalView.sourceLiquidityTop.enabled = true;
      }
    }
  }

  /**
   * The result when saving the ilm configuration: withRefresh,
   * refreshRate and ranger delimiters
   * @param event
   */
  saveResult(event): void {
    var jsonReader: JSONReader = new JSONReader();
    jsonReader.setInputJSON(event);
    if (!jsonReader.getRequestReplyStatus())
      this.swtAlert.error(ExternalInterface.call('getBundle', 'text', 'label-contactAdmin', 'Error occurred, Please contact your System Administrator: \n') + jsonReader.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    else {
      if (this.lastProfileAction != null && this.lastProfileAction == "saveProfile") {
        this.refreshProfileList(this.entityTabName, this.ccyTabName);
        this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-profileSaved', 'The profile was successfully saved'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
      }
      this.lastProfileAction = null;
    }
  }


  /**
   * The result when saving the ilm configuration: withRefresh,
   * refreshRate and ranger delimiters
   * @param event
   */
  saveFault(event): void {
    this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-lossConnection', 'Unable to save to server\nPossible loss of connection'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
  }

  /**The function initializes the menus in the right click event on the metagroup monitor screen.
   * The links are redirected to their respective pages.
   */
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, null);
    let addMenuItem: ContextMenuItem = new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu = this.screenVersion.svContextMenu;
  }
  showGridJSON(): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }



  public updateNowDate(frameId: string): void {
    let isGlobal = frameId == 'GlobalView';
    if(isGlobal){

      if(this.globalView)
      this.globalView.drawNowLineAndLiquidityZones(null, true);
    }
    else{
      if(this.groupAnalysis)
      this.groupAnalysis.drawNowLineAndLiquidityZones(null, true);

  }
  }
  public highlightLegendFromHighChart(frameId: string, yField: string, highligh: boolean): void {
    // var highlightEvent:SeriesHighlightEvent = new SeriesHighlightEvent();
    var dto: any = {};
    dto.highligh = highligh;
    dto.yField = yField;
    let isGlobal = frameId == 'GlobalView';
    // Dispatch the event so that legends will be highlightened as well
    if (isGlobal) {
      if(this.globalView)
      this.globalView.highlighLegend(dto, true);
    } else {
      if(this.groupAnalysis)
      this.groupAnalysis.highlighLegend(dto, true);
    }
  }

  public updateChartsLiveValues(frameId: string, valuesList: any, sizeOfList: number, dateAsString: string): void {
    var i: number = 0;
    var comboList = [];
    var yField: string = null;
    var dataValue: string = null;
    var value: Object = null;
    for (i = 0; i < sizeOfList; i++) {
      value = valuesList[i];
      comboList[value['name']] = value['y'];
    }
    let isGlobal = frameId == 'GlobalView';
    if (isGlobal) {
      if(this.globalView){

      for (let i = 0; i < this.globalView.linechart.seriesList.getValues().length; i++) {
        let serie: Series = this.globalView.linechart.seriesList.getValues()[i];
        if (serie.seriesType == 'line') {
          yField = serie.yField;
          if (comboList[yField] != null) {
            dataValue = comboList[yField];
            serie.seriesLabel.seriesValue = this.globalView.leftVerticalAxisFormatter(Number(dataValue));
          }
        }
      }

      // groupAnalysis.linechart.timeDynamicValue.seriesValue = dateAsString?dateAsString:"";
      this.globalView.timeDynamicValue.seriesValue = dateAsString ? dateAsString : "";
      }


    } else {
      // for each(var serie:Series in groupAnalysis.linechart.series)
      if(this.groupAnalysis){

      for (let i = 0; i < this.groupAnalysis.linechart.seriesList.getValues().length; i++) {
        let serie: Series = this.groupAnalysis.linechart.seriesList.getValues()[i];
        if (serie.seriesType == 'line') {
          yField = serie.yField;
          if (comboList[yField] != null) {
            dataValue = comboList[yField];
            // serie.seriesLabel.seriesValue = groupAnalysis.linechart.rightAxisFormatter(Number(dataValue));
            serie.seriesLabel.seriesValue = this.groupAnalysis.leftVerticalAxisFormatter(Number(dataValue));
          }
        }
      }

      this.groupAnalysis.timeDynamicValue.seriesValue = dateAsString ? dateAsString : "";
    }
    }


  }



  currentPorcessStateHandler(evt): void {
    if (!this.processStatusHTTP.isBusy() && !this.recalculateDAta.isBusy()) {
      // Calls the inputDataResult function to load the datagrid
      this.processStatusHTTP.cbResult = (data) => {
        this.getCurrentPorcessState(data);
      };
      // Sets the action path for Interface Monitor
      this.actionPath = "ilmAnalysisMonitor.do?";
      // Sets the action method to get the Interface Monitor Details
      this.actionMethod = "method=getCurrentPorcessState";
      // Sets the full URL for Interface Monitor
      this.processStatusHTTP.url = this.baseURL + this.actionPath + this.actionMethod;
      //processStatus.url = "progressStatus.xml";
      // Sets the flag for encoding URL to false
      this.processStatusHTTP.encodeURL = false;
      // Calls the inputDataFault function
      this.processStatusHTTP.cbFault = this.inputDataFault.bind(this);
      // set attibutes
      this.requestParams["entityId"] = this.entityTabName;
      this.requestParams["currencyId"] = this.ccyTabName;
      this.requestParams["selectedDate"] = this.valueDate.text;
      // Send the request to the server
      this.processStatusHTTP.send(this.requestParams);
    }

  }

  getCurrentPorcessState(event): void {
    var jsonReaderTmp: JSONReader = new JSONReader();
    jsonReaderTmp.setInputJSON(event);
    //Retrieves the XML from ResultEvent
    if (jsonReaderTmp.getRequestReplyStatus()) {
      this.processStatus.setStyleFuction(event.intradayliquidity.data);
    } else {
      // Alerts the user if any exceptions occured in the server side
      if (jsonReaderTmp.getRequestReplyMessage() == "" && event.toString().indexOf("<") == -1) {
        this.swtAlert.show(event.toString().split("\n")[1], ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'),
          Alert.OK,
          this,
          this.closeWindow.bind(this), null, Alert.OK);

      } else {
        if (this.tabNavigator.selectedIndex == 1)
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-errorContactSystemAdmin', 'Error occurred, Please contact your System Administrator: ') + "\n" + jsonReaderTmp.getRequestReplyMessage() + "\n" + jsonReaderTmp.getRequestReplyLocation(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));

      }

    }
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  public startOfCommsCalulation(): void {
    this.valueDate.enabled = false;
    if(this.groupAnalysis){

    this.groupAnalysis.alignScaleCB.enabled = false;
    this.groupAnalysis.includeOpenMvnts.enabled = false;
    this.groupAnalysis.sumByCutOff.enabled = false;
    this.groupAnalysis.sourceLiquidityTop.enabled = false;
    }
    if(this.globalView){
    this.globalView.alignScaleCB.enabled = false;
    this.globalView.includeOpenMvnts.enabled = false;
    this.globalView.sumByCutOff.enabled = false;
    this.globalView.sourceLiquidityTop.enabled = false;
  }
  }


  public recalculateDataFunction(): void {
    try {

      if (!this.recalculateDAta.isBusy()) {
        var selectedEntity: String = this.entityTabName;
        var selectedCcy: String = this.ccyTabName;
        this.isCalculationFinished = false;
        this.processStatus.setCalculatingState();
        //Initialize the communication objects
        this.recalculateDAta.cbStart = this.startOfCommsCalulation.bind(this);
        this.recalculateDAta.cbStop = this.endOfComms.bind(this);
        this.recalculateDAta.cbResult = this.recalculateResult.bind(this);
        this.recalculateDAta.cbFault = this.inputDataFault;
        this.recalculateDAta.encodeURL = false;
        this.actionPath = "ilmAnalysisMonitor.do?";
        this.actionMethod = "method=recalculateData";
        this.requestParams = [];
        this.requestParams["entityId"] = selectedEntity;
        this.requestParams["currencyId"] = selectedCcy;
        this.requestParams["selectedDate"] = this.valueDate.text;
        this.requestParams["sequenceNumber"] = ExternalInterface.call('eval', 'uniqueSequenceNumber');
        this.recalculateDAta.url = this.baseURL + this.actionPath + this.actionMethod;
        this.recalculateDAta.send(this.requestParams);
      } else {
        this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-alertRecalculateRunning', 'A calculation process is already running, please wait...'));
      }
    } catch (error) {

    }
  }

  /**
   * Build elements of the screen such us the chart and slider to define the interval from XML
   **/
  private recalculateResult(event): void {
    //Retrieves the XML from ResultEvent
    this.isCalculationFinished = true;
    var xmlReader: JSONReader = new JSONReader();
    xmlReader.setInputJSON(event);
    if (xmlReader.getRequestReplyStatus()) {
      this.updateData(null);
    } else {
      // Alerts the user if any exceptions occured in the server side
      this.swtAlert.show(xmlReader.getRequestReplyMessage(), "Error");
    }
  }

  closeWindow(eventObj): void {
    if (eventObj.detail == Alert.OK) {
      ExternalInterface.call("close");
    }
  }

  public refreshTestDate(date: Date): void {
    //disabledRanges="{[{rangeStart: getNextDayDate()}]}"
    //FIXME:FIX RANGES and test date
    // this.ilmLogic.testDate  = date;
    // this.valueDate.disabledRanges = [{rangeStart: getNextDayDate()}];
    this.valueDate.selectableRange = { rangStart: null, rangeEnd: date };
  }


  public saveChangesForILMStyles(isGlobal: boolean, comboboxValues: any, sizeOfAllCombo: number): void {
    try {


      var comboList = [];
      var styleMapAsJSON: string = "";
      var chartName: string = "";
      var chartStyleName: string = "";
      var chartStyleBorderColor: string = "";
      var chartStyleColor: string = "";
      var chartTypeToDisplay: string = "";
      var chartTypeDetailsToDisplay: string = "";
      var value: Object = null;

      for (let i = 0; i < sizeOfAllCombo; i++) {
        value = comboboxValues[i];
        comboList[value['id']] = value['value'];
      }

      var lineChartSeries: ILMLineChart = null;
      if (isGlobal) {
        if(this.globalView)
        lineChartSeries = this.globalView.linechart;
      } else {
        if(this.groupAnalysis)
        lineChartSeries = this.groupAnalysis.linechart;
      }


      // for (let i = 0; i < comboList.length; i++) {
      for (const yFieldValue in comboList) {

        let series: Series = lineChartSeries.seriesList.getValue(yFieldValue);
        if (series) {
          if ((series.seriesType == 'line')) {
            // var yFieldValue: string = series.yField;
            let CheckBoxLegendItem = lineChartSeries.balancesLegend.getLegendItem(series);
            CheckBoxLegendItem.seriesStyle = comboList[yFieldValue];

            series.seriesLabel.seriesStyle = comboList[yFieldValue];
            series.appliedStyle = comboList[yFieldValue];

            lineChartSeries.updateStyleMetadata(yFieldValue, "" + comboList[yFieldValue]);



            if (yFieldValue.indexOf("Standard.ab") != -1) {
              var groupId: string = yFieldValue.split(".")[0];
              lineChartSeries.updateThresholdStyleColor(groupId, "" + comboList[yFieldValue]);
            }

            //FIXME:
            // lineChartSeries.distinctSimilarColorSeries();


            chartName = "\"name\":\"" + yFieldValue + "\"";
            chartStyleName = "\"chartStyleName\":\"" + SeriesStyleProvider.getStyleName(comboList[yFieldValue]) + "\"";
            chartStyleColor = "\"color\":\"" + SeriesStyleProvider.getStyleColorAsSring(comboList[yFieldValue]) + "\"";
            chartStyleBorderColor = "\"borderColor\":\"" + SeriesStyleProvider.getStyleBorderColorAsSring(comboList[yFieldValue]) + "\"";
            chartTypeToDisplay = "\"type\":\"line\"";
            chartTypeDetailsToDisplay = "\"typeDetails\":\"" + SeriesStyleProvider.getStyleType(comboList[yFieldValue]) + "\"";

            if (styleMapAsJSON.length == 0)
              styleMapAsJSON += "{" + chartName + "," + chartStyleName + "," + chartStyleColor + "," + chartStyleBorderColor + "," + chartTypeToDisplay + "," + chartTypeDetailsToDisplay + "}";
            else
              styleMapAsJSON += ",{" + chartName + "," + chartStyleName + "," + chartStyleColor + "," + chartStyleBorderColor + "," + chartTypeToDisplay + "," + chartTypeDetailsToDisplay + "}";

          } else {
            let CheckBoxLegendItem = lineChartSeries.accumulatedDCLegend.getLegendItem(series);
            if(CheckBoxLegendItem)
              CheckBoxLegendItem.seriesStyle = comboList[yFieldValue];
            series.appliedStyle = comboList[yFieldValue];
            lineChartSeries.updateStyleMetadata(yFieldValue, "" + comboList[yFieldValue]);

            chartName = "\"name\":\"" + yFieldValue + "\"";
            chartStyleName = "\"chartStyleName\":\"" + SeriesStyleProvider.getStyleName(comboList[yFieldValue]) + "\"";
            chartStyleColor = "\"color\":\"" + SeriesStyleProvider.getStyleColorAsSring(comboList[yFieldValue]) + "\"";
            chartStyleBorderColor = "\"borderColor\":\"" + SeriesStyleProvider.getStyleBorderColorAsSring(comboList[yFieldValue]) + "\"";
            chartTypeToDisplay = "\"type\":\"area\"";
            chartTypeDetailsToDisplay = "\"typeDetails\":\"" + SeriesStyleProvider.getStyleType(comboList[yFieldValue]) + "\"";

            if (styleMapAsJSON.length == 0)
              styleMapAsJSON += "{" + chartName + "," + chartStyleName + "," + chartStyleColor + "," + chartStyleBorderColor + "," + chartTypeToDisplay + "," + chartTypeDetailsToDisplay + "}";
            else
              styleMapAsJSON += ",{" + chartName + "," + chartStyleName + "," + chartStyleColor + "," + chartStyleBorderColor + "," + chartTypeToDisplay + "," + chartTypeDetailsToDisplay + "}";


          }
        }


      }
      styleMapAsJSON = "[" + styleMapAsJSON + "]";
      lineChartSeries.updateLineLegend();
      lineChartSeries.updateAreaLegend();
      lineChartSeries.updateLineLegend();
      lineChartSeries.updateAreaLegend();
      if (this.tabNavigator.selectedIndex == 0) {
        setTimeout(() => {
          if(this.globalView)
          this.globalView.updateILMSeriesStyle();
        }, 0);
      } else {
        setTimeout(() => {
          if(this.groupAnalysis)
          this.groupAnalysis.updateILMSeriesStyle();
        }, 0);
      }

      if (isGlobal){
        if(this.globalView)
        this.globalView.linechart.callMethodInIframe('changeChartsStyle', [styleMapAsJSON]);
      }
      else{
        if(this.groupAnalysis)
        this.groupAnalysis.linechart.callMethodInIframe('changeChartsStyle', [styleMapAsJSON]);
      }


    } catch (error) {

    }
  }

  public profileContentReverted(): void {
    this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content = String(this.profileCombo.selectedLabel).replace("*", "");
    this.profileCombo.selectedLabel = this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content;
    this.revertProfileImage.enabled = false;
    this.deleteProfileImage.enabled = true;
  }




  public saveProfile(event) {


    var noneLabel: String = null;
    var lastLabel: String = null;
    var selectedItem: String = null;
    var saveProfileArrayCollection = [];
    for (let i = 0; i < this.profileCombo.dataProvider.length; i++) {
      saveProfileArrayCollection.push(this.profileCombo.dataProvider[i].content.toString().replace("*", ""));
    }
    noneLabel = ExternalInterface.call('getBundle', 'text', 'label-noneProfile', '<None>')
    const index = saveProfileArrayCollection.indexOf(noneLabel);
    if (index > -1) {
      saveProfileArrayCollection.splice(index, 1);
    }
    // saveProfileArrayCollection.removeItemAt(saveProfileArrayCollection.getItemIndex(noneLabel));
    if (this.profileCombo.selectedIndex > 0)
      selectedItem = this.profileCombo.selectedItem.content.toString().replace("*", "");


    // ExternalInterface.call("populateProfileList", "saveProfileIFrame_iframe", saveProfileArrayCollection, selectedItem);


    try {
      this.saveProfilePopupWindow = SwtPopUpManager.createPopUp(this);
      this.saveProfilePopupWindow.title = "Save Profile"; // childTitle,
      this.saveProfilePopupWindow.isModal = true;
      this.saveProfilePopupWindow.width = "400";
      this.saveProfilePopupWindow.height = "130";
      this.saveProfilePopupWindow.id = "saveProfilePopupWindow";
      this.saveProfilePopupWindow.enableResize = false;
      this.saveProfilePopupWindow.showControls = true;
      this.saveProfilePopupWindow.saveProfileCollection = saveProfileArrayCollection;
      this.saveProfilePopupWindow.selectedProfileItem = selectedItem;
      this.saveProfilePopupWindow.ilmProfileCombo = this.profileCombo;
      // this.saveProfilePopupWindow['maxOrder'] = this.maxOrder;
      // this.saveProfilePopupWindow['listOrder'] = this.listOrder;
      const mLoader = new ModuleLoader(this.commonService);
      mLoader.addEventListener(ModuleEvent.READY, (event) => this.moduleReadyEventHandler(event));
      mLoader.loadModule("ilmSaveProfilePopup");

    } catch (e) {
      SwtUtil.logError(e, 'Predict', 'saveProfilePopupWindow', 'saveProfile', this.errorLocation);
    }

    // ExternalInterface.call('openChildWindow', 'addProfile');
  }

  private moduleReadyEventHandler(event) {
    this.saveProfilePopupWindow.addChild(event.target);
    this.saveProfilePopupWindow.display();
    this.saveProfilePopupWindow.onClose.subscribe(() => {
    }, error => {
      console.log(error);
    });
  }

  /**
   * This function is used to confirm the calculation process after clicking on the link button
   */
  public deleteProfileClickHandler(event): void {
    if (this.profileCombo.selectedIndex > 0) {

      this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-deleteProfile', 'Are you sure you want to delete the profile') + " " + this.profileCombo.selectedLabel + "?", //text
        ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'), Alert.OK | Alert.CANCEL,
        this, //parent
        this.deleteAlertListener.bind(this), //close handler
        null, Alert.CANCEL); //icon and default button
    }


  }
  /**
   * This function is used to revert profile changes
   */
  public revertProfileClickHandler(event): void {
    this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-revertProfile', 'Are you sure you want to revert this profile?'), //text
      ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'), Alert.OK | Alert.CANCEL,
      null,
      this.revertAlertListener.bind(this), Alert.CANCEL);
  }


  private temporarySavedProfile: string = null;
  /**
   * This function is used to confirm the calculation process after clicking on the link button
   */
  public saveProfileClickHandlerFromIframe(selectedProfile: string): void {
    this.temporarySavedProfile = selectedProfile;
    if (selectedProfile.length == 0) {
      this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-fillMandatoryFields', 'Please fill the profile name before saving'));
    } else if (this.profileCombo.getIndexOf(selectedProfile) != -1 || this.profileCombo.getIndexOf("*" + selectedProfile) != -1) {
      this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-overwriteProfile', 'Are you sure you want to overwrite this profile?'), //text
        ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'), Alert.OK | Alert.CANCEL,
        this, //parent
        this.overwriteAlertListenerIFrame, //close handler
        null, Alert.CANCEL); //icon and default button
    } else {
      this.saveProfileHandlerFromIFrame(selectedProfile);
    }


  }

  /**
   * This function is used to listen to the alert
   *
   * @param eventObj CloseEvent
   */
  private deleteAlertListener(eventObj): void {

    // Checks for Alert OK
    if (eventObj.detail == Alert.OK) {
      // Recalculate data if "OK" is clicked
      this.deleteProfile();
    }
  }


  /**
   * This function is used to listen to the alert
   *
   * @param eventObj CloseEvent
   */
  private revertAlertListener(eventObj): void {
    // Checks for Alert OK
    if (eventObj.detail == Alert.OK) {
      // Recalculate data if "OK" is clicked
      this.changeProfile(null);
    }
  }


  private deleteProfile(): void {
    var selectedEntity: String = this.entityTabName;
    var selectedCcy: String = this.ccyTabName;
    this.profileHTTPCom.cbResult = this.profileDataResult.bind(this);
    this.profileHTTPCom.cbFault = this.inputDataFault.bind(this);
    this.profileHTTPCom.encodeURL = false;
    this.actionPath = "ilmAnalysisMonitor.do?";
    this.actionMethod = "method=deleteProfile";
    this.requestParams =[];
    this.requestParams["selectedProfile"] = this.profileCombo.selectedLabel;
    this.requestParams["entityId"] = selectedEntity;
    this.requestParams["currencyCode"] = selectedCcy;
    this.profileHTTPCom.url = this.baseURL + this.actionPath + this.actionMethod;
    this.profileHTTPCom.send(this.requestParams);
  }



  /**
   * This function is used to listen to the alert
   *
   * @param eventObj CloseEvent
   */
  private overwriteAlertListenerIFrame(eventObj): void {
    // Checks for Alert OK
    if (eventObj.detail == Alert.OK) {
      // Recalculate data if "OK" is clicked
      this.saveProfileHandlerFromIFrame(this.temporarySavedProfile);
    }
    this.temporarySavedProfile = null;
  }
  /**
   * This function is used to listen to the alert
   *
   * @param eventObj CloseEvent
   */
  public overwriteAlertListener(eventObj): void {
    // Checks for Alert OK
    if (eventObj.detail == Alert.OK) {
      // Recalculate data if "OK" is clicked
      this.saveProfileHandler();
    }
  }

  public saveProfileHandler(): void {

    //FIXME:CHECK IF NEEDED
    // var textEvent:TextEvent = new TextEvent(TextEvent.TEXT_INPUT,false,false,saveProfilePopup.profileCombo.textInput.text);
    // updateILMConf(textEvent);
    // PopUpManager.removePopUp(saveProfilePopup);
  }

  private saveProfileHandlerFromIFrame(selectedProfile: string): void {

    this.updateILMConf(null, selectedProfile);
  }

  public changeProfile(event): void {
    if(this.profileCombo.selectedLabel){

    for (let i = 0; i < this.profileCombo.dataProvider.length; i++) {
      this.profileCombo.dataProvider[i].content = this.profileCombo.dataProvider[i].content.toString().replace("*", "");

    }
    this.profileCombo.selectedLabel = String(this.profileCombo.selectedLabel).replace("*", "")
    if(this.globalView){
    this.globalView.resetScreen();
    this.globalView.onLoad(true);
    this.globalView.runCheckProfileOnLoad();
    }
    if(this.groupAnalysis){
      this.groupAnalysis.resetScreen();
    this.groupAnalysis.runCheckProfileOnLoad();
    if (this.entityTabName != "All") {
      this.groupAnalysis.onLoad(true);
    }
    }

    else {
      this.tabNavigator.selectedIndex = 0;
      this.tabNavigator.getChildAt(1).enabled = false;
      this.tabNavigator.getChildAt(2).enabled = false;
    }
    this.lastSelectedIndex = this.profileCombo.selectedIndex;
    this.enableDisableProfileCombo();
  }
  }


  public profileContentChanged(): void {
    try {

      if(this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content.toString().indexOf("*") == -1) {
        this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content = "*" + this.profileCombo.selectedLabel;
        this.profileCombo.selectedLabel = this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content;
      }
      this.revertProfileImage.enabled = true;

      this.deleteProfileImage.enabled = false;
    } catch (error) {

    }
  }
  validateDateField(dateField) {
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('dashboardDetails.alert.dateFormat', null);
      if (dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase(), true);

        if (!date.isValid()) {
          this.swtAlert.warning(alert + this.dateFormat.toUpperCase());
          return false;
        }
      }
      dateField.selectedDate = date.toDate();
    } catch (error) {
    }

    return true;
  }

}

class IlmLogic extends CommonLogic {

}



