<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas minWidth="800" width="100%" height="120">
      <Grid width="100%" height="100%">
        <GridRow>
          <GridItem width="50%">
            <GridItem width="120">
              <SwtLabel id="entityLabel" #entityLabel textDictionaryId="bookCode.entity" >
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="entityCombo" width="135"
                           (open)="openedCombo($event)" (close)="closedCombo($event)" #entityCombo dataLabel="entity" (change)="entityChangeCombo($event)">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
          <GridItem  width="181">
            <SwtLabel #dateLabel id="dateLabel" textDictionaryId="bookMonitor.date">
            </SwtLabel>
          </GridItem>
          <GridItem>
            <SwtDateField id="startDate" #startDate width="70"
                          (change)="timeUpdate($event)"  (open)="openedCombo($event)"
                          (close)="closedDateField($event)" (focusOut)="validateStartDate($event)"
                           restrict="0-9/" toolTip="Enter value date (DD/MM/YYYY)">
            </SwtDateField>
          </GridItem>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="50%">
              <GridItem width="120">
                <SwtLabel id="currencyLabel" #currencyLabel textDictionaryId="bookMonitor.currency" >
                </SwtLabel>
              </GridItem>
              <GridItem>
                <SwtComboBox id="ccyCombo" #ccyCombo width="135" (change)="changeCombo($event)" (open)="openedCombo($event)" (close)="closedCombo($event)" dataLabel="currency">
                </SwtComboBox>
              </GridItem>
            <GridItem>
              <SwtLabel id="selectedCcy" #selectedCcy fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
            <GridItem width="145">
              <SwtLabel  textDictionaryId="bookMonitor.monitor"></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox  id="monitorCombo"  width="135"(open)="openedCombo($event)" (close)="closedCombo($event)" #monitorCombo dataLabel="monitor"
                           (change)="monitorComboChangeHandle($event)">
              </SwtComboBox>
            </GridItem>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="50%">
            <GridItem width="120">
              <SwtLabel #locationLabel id="locationLabel" textDictionaryId="bookMonitor.location" >
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="locationCombo" #locationCombo width="135" (open)="openedCombo($event)" (close)="closedCombo($event)" dataLabel="location" (change)="changeCombo($event)">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel id="locationType" #locationType fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="50%">
            <HBox width="100%" horizontalAlign="right">
            <GridItem width="125" >
              <SwtLabel textDictionaryId="bookMonitor.balance"></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput id="sodText" editable="false" textAlign="right" #sodText width="160">
              </SwtTextInput>
            </GridItem>
            </HBox>
          </GridItem>
        </GridRow>
        <GridRow id="selectGroup" #selectGroup>
          <GridItem width="50%">
            <GridItem width="120">
              <SwtLabel #groupcomboLabel id="groupcomboLabel" textDictionaryId="groupMonitor.group" >
              </SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox dataLabel="groupCode" id="groupCombo" #groupCombo width="135" (change)="changeCombo($event)">
              </SwtComboBox>
            </GridItem>
            <GridItem>
              <SwtLabel id="selectedGroup" #selectedGroup fontWeight="normal" paddingLeft="10">
              </SwtLabel>
            </GridItem>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <SwtTabNavigator #tabs id="tabs" height="3%" (onChange)="tabChange($event)" borderBottom="false" width="100%">

    </SwtTabNavigator>
    <VBox width="100%" height="100%" minWidth="800" paddingLeft="5" styleName="borderVBox">
      <SwtCanvas styleName="canvasWithGreyBorder" id="displaycontainer" border="false" width="100%" height="100%"
        #displaycontainer>
      </SwtCanvas>
      <SwtCanvas styleName="canvasWithGreyBorder" id="totalsContainer" border="false" width="100%" height="40"
        #totalsContainer>
      </SwtCanvas>
    </VBox>
    <SwtCanvas width="100%" height="40" minWidth="800" marginBottom="0">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton [buttonMode]="true" id="refreshButton" #refreshButton 
          (click)="updateData('yes')"  >
          </SwtButton>
          <SwtButton [buttonMode]="true" id="optionsButton" (click)="optionsHandler()" #optionsButton >
          </SwtButton>
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
        </HBox>


        <HBox horizontalAlign="right" horizontalGap="2"  >
          <SwtLabel visible="false" textDictionaryId="screen.buildInProgress" color="red" #dataBuildingText id="dataBuildingText"
                    height="16" ></SwtLabel>
          <SwtLabel visible="false" textDictionaryId="screen.connectionError" color="red" #lostConnectionText id="lostConnectionText"
                    (click)="connError($event)" height="16" >
          </SwtLabel>
          <SwtLabel textDictionaryId="screen.lastRefresh"  fontWeight="normal"></SwtLabel>
          <SwtLabel id="lastRefTime" #lastRefTime styleName="labelLeftRefTime" fontWeight="normal">
          </SwtLabel>
        </HBox>
        <HBox horizontalAlign="right" horizontalGap="2">
          <div>
            <DataExport #dataExport id="dataExport"></DataExport>
          </div>
          <SwtHelpButton id="helpIcon" (click)="doHelp()"> </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
        </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
