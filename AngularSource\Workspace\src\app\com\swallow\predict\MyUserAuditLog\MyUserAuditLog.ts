import { Component, OnInit, ViewChild, ElementRef, ModuleWithProviders, NgModule } from '@angular/core';
import { SwtLabel, SwtComboBox, SwtCanvas, SwtButton, SwtLoadingImage, SwtDateField, SwtDataExport, SwtCommonGridPagination, HBox, JSONReader, SwtAlert, HTTPComms, SwtUtil, Logger, SwtCommonGrid, CommonService, ExternalInterface, SwtToolBoxModule, SwtModule, SwtTextInput } from 'swt-tool-box';
import { PaginationChangedArgs } from 'angular-slickgrid';
import moment from 'moment';
import { Routes, RouterModule } from '@angular/router';

@Component({
  selector: 'app-my-user-audit-log',
  templateUrl: './MyUserAuditLog.html',
  styleUrls: ['./MyUserAuditLog.css']
})
export class MyUserAuditLog extends SwtModule implements OnInit {

    /***********SwtLabel***********/
    @ViewChild('user') user: SwtLabel;
    @ViewChild('userDesc') userDesc: SwtLabel;
    @ViewChild('startDateLabel') startDateLabel: SwtLabel;
    @ViewChild('endDateLabel') endDateLabel: SwtLabel;
    @ViewChild('lastRefTimeLabel') lastRefTimeLabel: SwtLabel;
    @ViewChild('lastRefTime') lastRefTime: SwtLabel;
  
    /***********SwtComboBox***********/
    @ViewChild('userTxtInput') userTxtInput: SwtTextInput;

    /***********SwtCanvas***********/
    @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
  
    /***********SwtButton***********/
    @ViewChild('refreshButton', { read: SwtButton }) refreshButton: SwtButton;
    @ViewChild("closeButton") closeButton: SwtButton;
    @ViewChild("viewButton") viewButton: SwtButton;
    @ViewChild('printButton') printButton: SwtButton;
  
    @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  
    /***********SwtDateField***********/
    @ViewChild('fromDateChooser') fromDateChooser: SwtDateField;
    @ViewChild('toDateChooser') toDateChooser: SwtDateField;
  
    /***********SwtDateField and Pagination***********/
    @ViewChild('dataExport') dataExport: SwtDataExport;
    @ViewChild('numstepper') numstepper: SwtCommonGridPagination;
    @ViewChild('pageBox') pageBox: HBox;
    private actionPath;
    private actionMethod: string = "";
    private requestParams;
    private jsonReader: JSONReader = new JSONReader();
    private lastRecievedJSON;
    private prevRecievedJSON;
    private swtAlert: SwtAlert;
    private inputData = new HTTPComms(this.commonService);
    private baseURL: string = SwtUtil.getBaseURL();
    public moduleId = "Predict";
    private dateFormat: string;
    private logger: Logger = null;
    public errorLocation = 0;
    private _invalidComms: string;
    private mainGrid: SwtCommonGrid;
    //Variable to store the last page no
    public lastNumber: Number = 0;
    private defaultFromDate;
    private defaultToDate;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);

  }

  ngOnInit() {
    this.mainGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.user.text= SwtUtil.getPredictMessage('auditLog.userId', null);
    this.closeButton.label = SwtUtil.getPredictMessage('sweep.close', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refreshWindow', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('tooltip.viewLogDetails', null);
    this.fromDateChooser.toolTip= SwtUtil.getPredictMessage('tooltip.selectFromDate', null);
    this.toDateChooser.toolTip= SwtUtil.getPredictMessage('tooltip.selectToDate', null);
    this.lastRefTimeLabel.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);
    this.startDateLabel.text = SwtUtil.getPredictMessage('label.from', null)+"*";
    this.endDateLabel.text = SwtUtil.getPredictMessage('label.to', null)+"*";
    this.mainGrid.clientSideSort = false;
    this.mainGrid.clientSideFilter = false;
    this.mainGrid.onPaginationChanged = (event) => {
       this.paginationChanged(event);
    };


  }



  onLoad() {
    let selectedFilter: string = null;
    let selectedSort : string = null;
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };

    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "userlog.do?";
    this.actionMethod = 'method=displayAngular';
    this.requestParams['selectedSort'] = selectedSort;
    this.requestParams['selectedFilter'] = selectedFilter;
    this.requestParams['fromDate'] = this.fromDateChooser.text;
    this.requestParams['toDate'] = this.toDateChooser.text;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    this.mainGrid.onFilterChanged = this.updateData.bind(this);
    this.mainGrid.onSortChanged = this.updateData.bind(this);

    this.mainGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    };
  }

  cellClickEventHandler(event): void {
    if(this.mainGrid.selectedIndex >=0){
    this.viewButton.enabled = true;
    this.viewButton.buttonMode = true;
    }else{
      this.viewButton.enabled = false;
      this.viewButton.buttonMode = false;
    }
  }
  
  paginationChanged(event: PaginationChangedArgs) {
    this.numstepper.processing = true;
    this.doPaginationChanged();
  }
  /*change data grid*/
  doPaginationChanged(): void {
    this.requestParams = [];
    var maxPage = this.lastRecievedJSON.userLogData.grid.paging.maxpage;
    //To hold current page
    var currentPage: string = null;
    // Get the FilteredGridColumn
    let selectedFilter: string = this.mainGrid.getFilteredGridColumns();
    // Get the SortedGridColumn
    let selectedSort: string = this.mainGrid.getSortedGridColumn();
    try {
      if (this.numstepper.value > 0) {
        if (((this.numstepper.value <= this.numstepper.maximum) && (this.numstepper.value != this.lastNumber) && (this.numstepper.value != 0))) {
         
          //Get the current page value
          currentPage = (this.numstepper.value).toString();

          this.inputData.cbStart = this.startOfComms.bind(this);
          this.inputData.cbStop = this.endOfComms.bind(this);
          this.inputData.cbResult = (event) => {
            this.inputDataResult(event);
          };
          this.inputData.cbFault = this.inputDataFault.bind(this);
          this.inputData.encodeURL = false;
          this.actionPath = "userlog.do?";
          this.actionMethod = 'method=next';          
          this.requestParams["currentPage"] = currentPage;
          this.requestParams["maxPages"] = maxPage;
          this.requestParams['selectedSort'] = selectedSort?selectedSort:'0|false';
          this.requestParams['selectedFilter'] = selectedFilter; 
          this.requestParams['goToPageNo'] = "-1";
          this.requestParams['fromDate'] = this.fromDateChooser.text;
          this.requestParams['toDate'] = this.toDateChooser.text;
          this.requestParams['userId'] = this.userTxtInput.text;
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
          this.inputData.send(this.requestParams);
        }
      }
      this.logger.info("method [doPaginationChanged] - END");
    }
    catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'errorLog', 0);
    }

  }


  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    let maxPage;
    try {
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);

        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            errorLocation = 10;
            //Gets the current page from xml
            this.numstepper.value = Number(event.userLogData.grid.paging.currentpage);
            //Gets the maximum no of pages value
            maxPage = event.userLogData.grid.paging.maxpage;
            //Sets the Numeric stepper maximum va+lue
            
            this.numstepper.maximum = Number(maxPage);
            this.mainGrid.paginationComponent = this.numstepper;
            //to make the pagination invisible if maxPage >1
            if(maxPage > 1) {
              this.pageBox.visible = true;
              this.numstepper.minimum=1;
              this.numstepper.maximum=maxPage;
            } else {
              this.pageBox.visible = false;
            }
            errorLocation = 20;
            let defaultUserId= this.jsonReader.getSingletons().defaultUserId;
            let defaultUserName= this.jsonReader.getSingletons().defaultUserName;
            this.userTxtInput.text = defaultUserId;
            this.userDesc.text= defaultUserName;
            this.dateFormat = this.jsonReader.getSingletons().dateFormat;
            this.defaultFromDate= this.jsonReader.getSingletons().fromDate.content;
            this.fromDateChooser.formatString = this.dateFormat.toLowerCase();
            this.fromDateChooser.text = this.defaultFromDate;
            errorLocation = 30;
            this.defaultToDate= this.jsonReader.getSingletons().toDate.content;
            this.toDateChooser.formatString = this.dateFormat.toLowerCase();
            this.toDateChooser.text = this.defaultToDate;

            this.lastRefTime.text = this.jsonReader.getSingletons().lastRefTime.content;

            if (!this.jsonReader.isDataBuilding()) {
              const obj = { columns: this.lastRecievedJSON.userLogData.grid.metadata.columns };
              //ErrorLog grid
              this.mainGrid.CustomGrid(obj);
              this.mainGrid.refreshFilters();
              var gridRows = this.lastRecievedJSON.userLogData.grid.rows;
              if (gridRows.size > 0) {
                this.mainGrid.gridData = gridRows;
                this.mainGrid.setRowSize = this.jsonReader.getSingletons().totalCount;
              }
              else {
                this.mainGrid.gridData = { size: 0, row: [] };
              }
              this.prevRecievedJSON = this.lastRecievedJSON;
            }
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }
        }
      }

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'ErrorLog.ts', "inputDataResult", errorLocation);
    }
  }


  
  viewDetails(): void {
    let selectedReferenceId= this.mainGrid.selectedItem.itemId.content;
    let selectedUserId= this.userTxtInput.text;
    let selectedDate= this.mainGrid.selectedItem.logDate.content;
    let selectedTime= this.mainGrid.selectedItem.logTime.content;
    let selectedReference= this.mainGrid.selectedItem.item.content;
    let selectedAction= this.mainGrid.selectedItem.action.content;
    ExternalInterface.call("viewDetails", "openViewDetails", selectedReferenceId, selectedUserId, selectedDate, selectedTime,
    selectedReference, selectedAction);
  }

  validateDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      errorLocation = 10;
      if (dateField.text) {
        errorLocation = 20;
        date = moment(dateField.text, this.dateFormat.toUpperCase(), true);
        errorLocation = 30;
        if (!date.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
            errorLocation = 40;
            this.setFocusDateField(dateField)
          });
          if(dateField.id=='fromDateChooser'){
          this.fromDateChooser.text= this.defaultFromDate;
          }else{
          this.toDateChooser.text= this.defaultToDate;
          }
          return false;
        }
        if (!this.checkDates()) {
          this.swtAlert.warning('End Date must be later than Start date');
          if(dateField.id=='fromDateChooser'){
            this.fromDateChooser.text= this.defaultFromDate;
            }else{
            this.toDateChooser.text= this.defaultToDate;
            }
          return;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          errorLocation = 50;
          this.setFocusDateField(dateField)
        });
        if(dateField.id=='fromDateChooser'){
          this.fromDateChooser.text= this.defaultFromDate;
          }else{
          this.toDateChooser.text= this.defaultToDate;
          }
        return false;
      }
      errorLocation = 60;
      dateField.selectedDate = date.toDate();
      this.refresh();
    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'ErrorLog.ts', "validateDateField", errorLocation);
    }

    return true;
  }

  setFocusDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      dateField.setFocus();
      errorLocation = 10;
      // dateField.text = this.jsonReader.getSingletons().displayedDate;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [setFocusDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'ErrorLocation.ts', "setFocusDateField", errorLocation);
    }
  }

  closeHandler() {
    ExternalInterface.call("close");
  }

  doHelp(): void {
    ExternalInterface.call("help");
  }

    /**
  * printPage
  *
  * param event
  *
  * Method to get call the action to get reports
  */
 printPage(): void {
  let errorLocation = 0;
  try {
    ExternalInterface.call('printPage');

  } catch (error) {
    // log the error in ERROR LOG
    SwtUtil.logError(error, this.moduleId, "className", "printPage", errorLocation);
  }
}


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  checkDates() {

    try {
      var startDate: any;
      var endDate: any;
      if (this.fromDateChooser.text) {
        startDate = moment(this.fromDateChooser.text, this.dateFormat.toUpperCase(), true);
      }
      if (this.toDateChooser.text) {
        endDate = moment(this.toDateChooser.text, this.dateFormat.toUpperCase(), true);
      }

      if (!startDate && endDate) {
        return false;
      }

      if (startDate && endDate && endDate.isBefore(startDate)) {
        return false;
      }

      return true;
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "className", "checkDates", this.errorLocation);

    }
  }
  refresh(){
    // Get the FilteredGridColumn
    let selectedFilter: string = this.mainGrid.getFilteredGridColumns();
    // Get the SortedGridColumn
    let selectedSort: string = this.mainGrid.getSortedGridColumn();
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };

    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "userlog.do?";
    this.actionMethod = 'method=showDetails';
    this.requestParams['selectedSort'] = selectedSort;
    this.requestParams['selectedFilter'] = selectedFilter;
    this.requestParams['fromDate'] = this.fromDateChooser.text;
    this.requestParams['toDate'] = this.toDateChooser.text;
    this.requestParams['userId'] = this.userTxtInput.text;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  updateData(resetpage): void {
    this.requestParams = [];
    var maxPage = this.lastRecievedJSON.userLogData.grid.paging.maxpage;
    //To hold current page
    var currentPage: string = null;
    // Get the FilteredGridColumn
    let selectedFilter: string = this.mainGrid.getFilteredGridColumns();
    // Get the SortedGridColumn
    let selectedSort: string = this.mainGrid.getSortedGridColumn();
    try {
          //Get the current page value
          currentPage = !resetpage?(this.numstepper.value).toString():"1";

          this.inputData.cbStart = this.startOfComms.bind(this);
          this.inputData.cbStop = this.endOfComms.bind(this);
          this.inputData.cbResult = (event) => {
            this.inputDataResult(event);
          };
          this.inputData.cbFault = this.inputDataFault.bind(this);
          this.inputData.encodeURL = false;
          this.actionPath = "userlog.do?";
          this.actionMethod = 'method=displayAngular';          
          this.requestParams["currentPage"] = currentPage;
          this.requestParams["maxPage"] = maxPage;
          this.requestParams['selectedSort'] = selectedSort;
          this.requestParams['selectedFilter'] = selectedFilter;
          this.requestParams['fromDate'] = this.fromDateChooser.text;
          this.requestParams['toDate'] = this.toDateChooser.text;
          this.requestParams['userId'] = this.userTxtInput.text;
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
          this.inputData.send(this.requestParams);

      this.logger.info("method [updateData] - END");
    }
    catch (error) {
      SwtUtil.logError(error, this.moduleId, 'ClassName', 'errorLog', 0);
    }

  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: MyUserAuditLog }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [MyUserAuditLog],
  entryComponents: []
})
export class MyUserAuditLogModule { }
