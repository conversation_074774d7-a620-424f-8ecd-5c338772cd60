<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingBottom="5" paddingRight="5">
    <Grid width="100%"  paddingLeft="10" paddingRight="10">

      <GridRow width="100%">

        <GridItem>
          <SwtLabel id="tableLbl" #tableLbl width="80"></SwtLabel>
        </GridItem>

       <GridItem width="150">
          <SwtComboBox id="tableCombo" #tableCombo width="185" dataLabel="listTables"> </SwtComboBox>
        </GridItem>
      </GridRow>
      <GridRow width="100%">
        <GridItem>
          <SwtLabel id="columnsLbl" #columnsLbl width="80"></SwtLabel>
        </GridItem>
        <GridItem width="150">
          <SwtComboBox id="columnsCombo" #columnsCombo width="185" dataLabel="Account"> </SwtComboBox>
        </GridItem>
      </GridRow>

      <GridRow width="100%">
        <GridItem>
          <SwtLabel id="colLbl" #colLbl width="80"></SwtLabel>
        </GridItem>
        <GridItem width="150">
          <SwtTextInput id="colLblTxt" #colLblTxt  width="185"></SwtTextInput>
        </GridItem>
      </GridRow>

    </Grid>

    <SwtCanvas height="35" width="100%" paddingTop="2" marginTop="10">
      <HBox paddingLeft="5" width="100%" height="100%">
        <HBox width="50%">
          <SwtButton tabIndex="1" buttonMode="true" id="saveButton" #saveButton (click)="save()" >
          </SwtButton>
        </HBox>
        <HBox width="50%" horizontalAlign="right" paddingRight="5">
          <SwtButton tabIndex="3" buttonMode="true" id="closeButton" #closeButton (click)="close()"></SwtButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>