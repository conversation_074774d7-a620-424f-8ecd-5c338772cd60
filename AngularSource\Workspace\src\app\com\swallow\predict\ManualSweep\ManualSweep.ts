import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, SwtAlert, CommonService, JSONReader, HTTPComms, SwtUtil, ExternalInterface, SwtLabel, SwtComboBox, SwtCanvas, SwtButton, SwtCommonGrid, SwtLoadingImage, Alert, ScreenVersion, SwtPopUpManager, ContextMenuItem, JSONViewer, SwtDateField, Logger, SwtCommonGridPagination, HBox, SwtCheckBox, SwtTabNavigator, CommonUtil, Tab, SwtDataExport, ExportEvent, StringUtils } from 'swt-tool-box';
import moment from 'moment';
declare var instanceElement: any;

@Component({
  selector: 'app-manual-sweep',
  templateUrl: './ManualSweep.html',
  styleUrls: ['./ManualSweep.css']
})
export class ManualSweep extends SwtModule implements OnInit {

  @ViewChild("tabs") tabs: SwtTabNavigator;
  @ViewChild("sweepTodayParent") sweepTodayParent: Tab;
  @ViewChild("sweepTodayPlusOneParent") sweepTodayPlusOneParent: Tab;
  @ViewChild("sweepTodayPlusTwoParent") sweepTodayPlusTwoParent: Tab;
  @ViewChild("sweepTodayPlusThreeParent") sweepTodayPlusThreeParent: Tab;
  @ViewChild("sweepTodayPlusFourParent") sweepTodayPlusFourParent: Tab;
  @ViewChild("sweepTodayPlusFiveParent") sweepTodayPlusFiveParent: Tab;
  @ViewChild("sweepTodayPlusSixParent") sweepTodayPlusSixParent: Tab;
  @ViewChild("sweepTodayPlusSevenParent") sweepTodayPlusSevenParent: Tab;
  @ViewChild("sweepTodayPlusEightParent") sweepTodayPlusEightParent: Tab;

  /***********SwtLabel***********/
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('secondEntityLabel') secondEntityLabel: SwtLabel;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('acctTypeLabel') acctTypeLabel: SwtLabel;
  @ViewChild('hideAccountsAfterCutoffLabel') hideAccountsAfterCutoffLabel: SwtLabel;

  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedSecondEntity') selectedSecondEntity: SwtLabel;
  @ViewChild('selectedCurrency') selectedCurrency: SwtLabel;
  @ViewChild('selectedAcctType') selectedAcctType: SwtLabel;
  @ViewChild('selectedAcctsLbl') selectedAcctsLbl: SwtLabel;
  @ViewChild('selectedAcctsVal') selectedAcctsVal: SwtLabel;

  @ViewChild('lastRef') lastRef: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
  /***********SwtComboBox***********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('secondEntityCombo') secondEntityCombo: SwtComboBox;
  @ViewChild('currencyCombo') currencyCombo: SwtComboBox;
  @ViewChild('acctTypeCombo') acctTypeCombo: SwtComboBox;

  /*********SwtCheckBox*********/
  @ViewChild('hideAccountsAfterCutoffCheck') hideAccountsAfterCutoffCheck: SwtCheckBox;

  /***********SwtCanvas***********/
  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
  @ViewChild('numstepper') numstepper: SwtCommonGridPagination;
  @ViewChild('pageBox') pageBox: HBox;

  
  /***********SwtButton***********/
  @ViewChild("refreshButton") refreshButton: SwtButton;
  @ViewChild("sweepButton") sweepButton: SwtButton;
  @ViewChild("clearButton") clearButton: SwtButton;
  @ViewChild("logButton") logButton: SwtButton;
  @ViewChild("closeButton") closeButton: SwtButton;
  @ViewChild('dataExport') dataExport: SwtDataExport;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  //Variable to store the last page no
  public lastNumber: Number = 0;
  private entityId: string = null;
  private secondEntityId : string = null;
  private actionPath;
  private actionMethod: string = "";
  private requestParams;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private mainGrid: SwtCommonGrid;
  private defaultEntity;
  private defaultSecondEntity;
  private selectedCurr;
  private selectedAcct;
  private defaultAcctType;
  private menuAccessId;
  private _invalidComms: string;
  public moduleId = "Predict";

  private errorLocation = 0;
  private menuAccess = "";
  public screenVersion = new ScreenVersion(this.commonService);
  public lastReceivedJSON;
  private showJsonPopup = null;
  private logger: Logger = null;
  public sysDateFrmSession: string;
  private arrayOftabs = [];

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }

  ngOnInit() {
    instanceElement = this;
    this.mainGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.mainGrid.allowMultipleSelection = true;

    this.entityLabel.text = SwtUtil.getPredictMessage('entity.id', null);
    this.secondEntityLabel.text = SwtUtil.getPredictMessage('movement.secondEntity', null);
    this.acctTypeLabel.text = SwtUtil.getPredictMessage('sweepsearch.accType', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('sweepsearch.currency', null);
    this.selectedAcctsLbl.text = SwtUtil.getPredictMessage('label.selected', null)+SwtUtil.getPredictMessage('role.roleAccounts', null)+': ';
    this.lastRef.text = SwtUtil.getPredictMessage('label.lastRefTime', null);

    this.entityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.secondEntityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.acctTypeCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectAccountType', null);
    this.currencyCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectCurrencyCode', null);
 
    this.hideAccountsAfterCutoffLabel.text = SwtUtil.getPredictMessage('tooltip.HideAccountsAfterCutoff', null);
    this.hideAccountsAfterCutoffCheck.toolTip = SwtUtil.getPredictMessage('button.tooltip.hidecutoffcutoff', null);

    this.refreshButton.label = SwtUtil.getPredictMessage('button.Refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refreshWindow', null);
    this.sweepButton.label = SwtUtil.getPredictMessage('button.sweep', null);
    this.sweepButton.toolTip = SwtUtil.getPredictMessage('tooltip.Sweep', null);
    this.clearButton.label = SwtUtil.getPredictMessage('button.clear', null);
    this.clearButton.toolTip = SwtUtil.getPredictMessage('button.tooltip.clearAccounts', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');

  }

  export(type: string): void {
    var selects = [];
    // Sets the column name
    selects.push("Entity=" + this.entityCombo.selectedLabel);
    selects.push("Ccy=" + this.currencyCombo.selectedLabel);
    selects.push("Account Type=" + this.acctTypeCombo.selectedLabel);
    let date= this.mainGrid.gridData && this.mainGrid.gridData[0] ? this.mainGrid.gridData[0].valueDate : this.tabs.getChildAt(this.tabs.selectedIndex).content;
    selects.push("Date=" + date);
    selects.push("Hide Cut-Off=" + this.hideAccountsAfterCutoffCheck.selected);
    let filteredColumnsArray: any[] = [...this.lastRecievedJSON.manualSweepDetailsList.grid.metadata.columns.column];

    filteredColumnsArray = filteredColumnsArray.filter((column: any) => {
    return column.dataelement !== 'isValueDateAchievable';
    }); 

    const filteredColumnsObject = {
      column: filteredColumnsArray
    };

    this.dataExport.convertData(filteredColumnsObject, this.mainGrid, null, selects, type, false);

  }

  onLoad() {
    this.requestParams = [];

    this.loadingImage.setVisible(false);

    if (this.menuAccess) {
      if (this.menuAccess !== "") {
        this.menuAccessId = Number(this.menuAccess);
      }
    }
    // set version number
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };

    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "sweep.do?";
    this.actionMethod = 'method=displayListByEntityAngular';
    this.requestParams['hideAccountsAfterCutOff'] = this.hideAccountsAfterCutoffCheck.selected;
    this.requestParams['isBusinessDay'] = 'true';

      /*should add the other params
        //params
      */
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

    this.mainGrid.onRowClick = () => {
      this.cellClickEventHandler();
    };

      //set row color
    this.mainGrid.rowColorFunction = (dataGrid, dataContext, color) => {
      return this.updateRowColor(dataGrid, dataContext, color);
    };

    ExportEvent.subscribe((type) => {
      this.export(type);
    });

  }


  private updateRowColor(dataGrid, dataContext, color ): any {
    // Variable to hold error location
    var errorLocation: number = 0;
    // Color
    var rColor: any;
    try {
      errorLocation = 10;
      if (dataGrid.isValueDateAchievable=="N") {
        errorLocation = 20;
        rColor = "#C0C0C0"; //0xDCDCDC not clear 
      }
      else {
        rColor = color;
      }
    }
    catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, this.getQualifiedClassName(this), "updateRowColor", errorLocation);
    }
    return rColor;
  }

  cellClickEventHandler(): void {
    if (this.mainGrid.selectedIndex >= 0) {
        this.clearButton.enabled = true;
        this.clearButton.buttonMode = true;
        //Enable sweep button when 2 rows are selectedbut we need first to checkAccess, account Access 
        //then check value date
        if (this.mainGrid.selectedItems.length >1) {
            let flag = this.getAccess(this.entityCombo.selectedLabel, this.getSelectedList("forCheckSweep"));

            if (flag && flag.split("*")[0] == "true"){

              if(this.mainGrid.selectedItems[0].valueDate.content==this.mainGrid.selectedItems[1].valueDate.content ){
              this.sweepButton.enabled = true;
              this.sweepButton.buttonMode = true;
              }else{
              this.sweepButton.enabled = false;
              this.sweepButton.buttonMode = false;
              }
            }else{
              this.sweepButton.enabled = false;
              this.sweepButton.buttonMode = false;
            }

            if (flag && flag.split("*")[1] == "false"){
              this.sweepButton.enabled = false;
              this.sweepButton.buttonMode = false;
            }
            
        } else {
            this.sweepButton.enabled = false;
            this.sweepButton.buttonMode = false;
        }


      if (this.mainGrid.selectedItems.length >2) {
          const firstIndice = this.mainGrid.selectedIndices[0];
          const secondIndice = this.mainGrid.selectedIndices[1];
          this.mainGrid.selectedIndices = [firstIndice,secondIndice ];         
      }
      let selectedItems = [];

      const firstElement = this.mainGrid.selectedItems[0];
      const secondElement = this.mainGrid.selectedItems[1];

      if (firstElement) {
        selectedItems.push(firstElement);
    }
      if (secondElement) {
        selectedItems.push(secondElement);
      }
        const accountIdString = selectedItems.map(element => `${element.accountId.content} (${element.entityId.content})`).join(',');

        this.selectedAcctsVal.text=accountIdString.replace(/,$/, '') ;

      //to be verified
        this.mainGrid.selectedItems= selectedItems;
        this.mainGrid.refresh();
 
    } else {
        this.sweepButton.enabled = false;
        this.sweepButton.buttonMode = false;
        this.clearButton.enabled = false;
        this.clearButton.buttonMode = false;
        this.selectedAcctsVal.text='' ;
    }
}


getAccess(entityId, selectedList) {
  let lockflag= "false";
  lockflag=ExternalInterface.call("getAccess", entityId, selectedList);
  return lockflag;
 }

  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
        // Checks the inputData and stops the communication
        if (this.inputData.isBusy()) {
          this.inputData.cbStop();
        } else {
          this.lastRecievedJSON = event;
          this.jsonReader.setInputJSON(this.lastRecievedJSON)

          if (this.jsonReader.getRequestReplyStatus()) {
            if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
              // Get lastRefTime
              let lastRef = this.jsonReader.getSingletons().lastRefTime;
              this.lastRefTime.text = lastRef.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');
              let isEntityChanged = this.jsonReader.getSingletons().isEntityChanged;
              this.sysDateFrmSession = this.jsonReader.getSingletons().sysDateFrmSession;
              errorLocation = 10;
              this.entityCombo.setComboData(this.jsonReader.getSelects());
              this.secondEntityCombo.setComboData(this.jsonReader.getSelects());
              this.acctTypeCombo.setComboData(this.jsonReader.getSelects());
              this.currencyCombo.setComboData(this.jsonReader.getSelects());

              this.defaultEntity = this.jsonReader.getSingletons().defaultEntity;
              if (this.defaultEntity != undefined) {
                this.entityCombo.selectedLabel = this.defaultEntity;
              }
              this.defaultSecondEntity = this.jsonReader.getSingletons().defaultSecondEntity;
              if (this.defaultSecondEntity != undefined) {
                this.secondEntityCombo.selectedLabel = this.defaultSecondEntity;
              }
              this.selectedCurr = this.jsonReader.getSingletons().selectedCurrencyCode;
              if (this.selectedCurr != undefined) {
                this.currencyCombo.selectedLabel = this.selectedCurr;
              }
              this.selectedAcct = this.jsonReader.getSingletons().selectedAcctType;
              if (this.selectedAcct != undefined) {
                this.acctTypeCombo.selectedLabel = this.selectedAcct;
              }
              errorLocation = 20;
              this.selectedEntity.text = this.entityCombo.selectedValue;
              this.selectedSecondEntity.text = this.secondEntityCombo.selectedValue?this.secondEntityCombo.selectedValue:"";
              this.selectedCurrency.text = this.currencyCombo.selectedValue;
              this.selectedAcctType.text = this.acctTypeCombo.selectedValue;

              if (!this.jsonReader.isDataBuilding()) {
                            //this condition to create tabs one time
            //if (this.lastRecievedJSON.accountmonitor.tabs != "") {
              if(this.arrayOftabs.length == 0) {
                if (this.tabs.getTabChildren().length > 0) {
                  for (let i = 0; i < this.arrayOftabs.length; i++) {
                    this.tabs.removeChild(this.arrayOftabs[i]);
                  }
                }

                this.sweepTodayParent = <Tab>this.tabs.addChild(Tab);
                this.sweepTodayPlusOneParent = <Tab>this.tabs.addChild(Tab);
                this.sweepTodayPlusTwoParent = <Tab>this.tabs.addChild(Tab);
                this.sweepTodayPlusThreeParent = <Tab>this.tabs.addChild(Tab);
                this.sweepTodayPlusFourParent = <Tab>this.tabs.addChild(Tab);
                this.sweepTodayPlusFiveParent = <Tab>this.tabs.addChild(Tab);
                this.sweepTodayPlusSixParent = <Tab>this.tabs.addChild(Tab);
                this.sweepTodayPlusSevenParent = <Tab>this.tabs.addChild(Tab);
                this.sweepTodayPlusEightParent = <Tab>this.tabs.addChild(Tab);

                this.arrayOftabs = [];
                this.arrayOftabs = [this.sweepTodayParent, this.sweepTodayPlusOneParent, this.sweepTodayPlusTwoParent, this.sweepTodayPlusThreeParent,
                  this.sweepTodayPlusFourParent, this.sweepTodayPlusFiveParent, this.sweepTodayPlusSixParent, this.sweepTodayPlusSevenParent, this.sweepTodayPlusEightParent];
              }
              for (let i = 0; i < this.lastRecievedJSON.manualSweepDetailsList.tabs.predictdate.length; i++) {
                this.arrayOftabs[i].label = this.lastRecievedJSON.manualSweepDetailsList.tabs.predictdate[i].dateLabel;
                this.arrayOftabs[i].businessday = this.lastRecievedJSON.manualSweepDetailsList.tabs.predictdate[i].businessday;
                this.arrayOftabs[i].content = this.lastRecievedJSON.manualSweepDetailsList.tabs.predictdate[i].content;
                if(this.arrayOftabs[i].businessday ==false){
                  this.arrayOftabs[i].setTabHeaderStyle("color","darkgray");
                }else
                  this.arrayOftabs[i].setTabHeaderStyle("color","black");
              }
            //}
                const obj = { columns: this.lastRecievedJSON.manualSweepDetailsList.grid.metadata.columns };
                //manual Sweep grid
                this.mainGrid.CustomGrid(obj);
                var gridRows = this.lastRecievedJSON.manualSweepDetailsList.grid.rows;
                errorLocation = 30;
                if (gridRows && gridRows.size > 0 && gridRows.row) {
                  this.mainGrid.gridData = gridRows; 
                  this.mainGrid.setRowSize = this.jsonReader.getRowSize();  
                  this.dataExport.enabled = true;           
                }
                else {
                  this.mainGrid.gridData = { size: 0, row: [] };
                  this.dataExport.enabled = false;
                }

                if(isEntityChanged=='true'){
                  this.tabs.selectedIndex=0;
                  this.mainGrid.selectedIndex=-1;
                }

                this.mainGrid.refresh();
                this.prevRecievedJSON = this.lastRecievedJSON;
              }
            }
          } else {
            if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
              this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
            }
          }
        }
      } catch (error) {
        SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'ManualSweep.ts', "inputDataResult", errorLocation);
      }
    }
  onCurrencyChange(): void {
    setTimeout(() => {
      this.updateData('false');
    }, 100); // or 100–200 ms if needed
  }


  onEntityChange(): void {
    setTimeout(() => {
      this.updateData('true');
    }, 100); // or 100–200 ms if needed
  }
  /*change data grid*/
  updateData(isEntityChanged): void {
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;
      this.requestParams = [];
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      this.entityId = this.entityCombo.selectedLabel;
      this.secondEntityId = this.secondEntityCombo.selectedLabel;
      //Gets the current page value
      errorLocation = 20;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
          errorLocation = 30;
        }
      }
      this.inputData.cbStart = this.startOfComms.bind(this);
      errorLocation = 40;
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      errorLocation = 50;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      errorLocation = 60;
      this.inputData.encodeURL = false;
      this.actionPath = "sweep.do?";
      this.actionMethod = 'method=displayListByEntityAngular';
      this.requestParams['entityId'] = this.entityCombo.selectedLabel;
      this.requestParams['selectedCurrencyCode'] = this.currencyCombo.selectedLabel;
      this.requestParams['selectedAcctType'] = this.acctTypeCombo.selectedLabel;
      this.requestParams['entityIdChanged'] = isEntityChanged;
      this.requestParams['selectedTabName'] = isEntityChanged=='true'?this.tabs.tabChildrenArray[0].label:this.tabs.selectedLabel;
      this.requestParams['isBusinessDay'] = this.tabs.getChildAt(this.tabs.selectedIndex).businessday;
      this.requestParams['selectedTabIndex'] = isEntityChanged=='true'?1:this.tabs.selectedIndex+1;
      this.requestParams['againstAccountEntityId'] = this.secondEntityCombo.selectedLabel;
      this.requestParams['hideAccountsAfterCutOff'] = this.hideAccountsAfterCutoffCheck.selected;
      console.log("🚀 ~ ManualSweep ~ updateData ~ this.requestParams:", this.requestParams)
      /*should add the other params
        //params
      */
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 70;
      this.inputData.send(this.requestParams);
      errorLocation = 80;

    } catch (error) {
      SwtUtil.logError(error, this.moduleId, this.commonService.getQualifiedClassName(this), "updateData", errorLocation);
    }
  }


  closeHandler() {
    ExternalInterface.call("close");
  }


  doHelp(): void {
    ExternalInterface.call("help");
  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.dataExport.enabled = false;
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.dataExport.enabled = false;

  }

  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }



  keyDownEventHandler(event) {

  }

  /** This function is used to display the XML for the monitor selected in the monitor combo
      */
  showJSONSelect(event): void {

    this.showJsonPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastReceivedJSON,
      });
    this.showJsonPopup.width = "700";
    this.showJsonPopup.title = "Last Received JSON";
    this.showJsonPopup.height = "500";
    this.showJsonPopup.enableResize = false;
    this.showJsonPopup.showControls = true;
    this.showJsonPopup.display();
  }

  refreshGridHideShowAccountsCutOff1() {

	  for (var i = 0; i < this.mainGrid.gridData.length; i++) {
	    var isValueDateAchievable = this.mainGrid.gridData[i].isValueDateAchievable;
	    var shouldHideRow = this.hideAccountsAfterCutoffCheck.selected && isValueDateAchievable === 'N';
	    if( this.mainGrid.selectedIndex==i && shouldHideRow){
			 //this.mainGrid.removeSelected;
    }
  }

  }


  tabIndexchangeHandler(): void {
    try {

      this.updateData("false");

    } catch (e) {
      console.log('tabIndexchangeHandler error', e)
    }
  }

  clearSelectedAccounts() {
    this.selectedAcctsVal.text='';
    this.mainGrid.selectedIndex=-1;
    this.clearButton.enabled = false;
    this.clearButton.buttonMode = false;
    this.sweepButton.enabled = false;
    this.sweepButton.buttonMode = false;
  }



  
  openSweepDetailWindow() {
    var swpDetailFlag = true;
    if (this.mainGrid.selectedItems.length > 0) {
      if(this.mainGrid.selectedItems[0].isValueDateAchievable.content=='N' || this.mainGrid.selectedItems[1].isValueDateAchievable.content=='N') {
      swpDetailFlag = false;
      Alert.okLabel = SwtUtil.getPredictMessage('button.ok', null);
      Alert.cancelLabel = SwtUtil.getPredictMessage('button.cancel', null);
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('manualSweeping.warning.messageForNonWorkingDays', null)));
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.OK | Alert.CANCEL, null, this.confirmOkSweep.bind(this));
    }else {
        this.okSweep();
      }
    } 

  }


    
  confirmOkSweep(event) {
    if (event.detail == Alert.OK) {
      this.okSweep();
    }
  }

  okSweep(){
	 var displaylevel1= "";
   var displaylevel2= "";
   
if (this.mainGrid.selectedItems.length > 0) {					
						displaylevel1 = this.mainGrid.selectedItems[0].displayLevel.content;
            displaylevel2 = this.mainGrid.selectedItems[1].displayLevel.content;
            
  }

		if(displaylevel1 == displaylevel2) {
      Alert.okLabel = SwtUtil.getPredictMessage('button.ok', null);
      Alert.cancelLabel = SwtUtil.getPredictMessage('button.cancel', null);
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('label.bothAccountSelectedAre', null)))+ displaylevel1+
      StringUtils.substitute((SwtUtil.getPredictMessage('label.accounts', null)))+ `\n` +
      StringUtils.substitute((SwtUtil.getPredictMessage('recovery.confrim.continue', null)));
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.OK | Alert.CANCEL, null, this.confirmOkHandle.bind(this));

		}
		else
		{

      this.okHandle();

    } 
  } 
   
  
  confirmOkHandle(event) {
    if (event.detail == Alert.OK) {
      this.okHandle();
    }
  }

  okHandle() {
  let selectedList= this.getSelectedList("sweepWindow");
  let flag = this.checkSweep(this.entityCombo.selectedLabel, selectedList);
  if (flag != "false") {
  Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
  Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
  var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('ShowErrMsgWindowWithBtn.errorMessage2', null)))+ flag +  `\n` +
  StringUtils.substitute((SwtUtil.getPredictMessage('ShowErrMsgWindowWithBtn.errorMessage3', null)));
  this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.ConfirmOpenSweepWindow.bind(this));
  }else{
      this.openSweepWindow();
  }
}


  checkSweep(entityCode, selectedList) {
    let flag= "false";
    flag=ExternalInterface.call("checkSweep", entityCode, selectedList);
    return flag;
   }

   ConfirmOpenSweepWindow(event) {
    if (event.detail == Alert.YES) {
      this.openSweepWindow();
    }
  }


   openSweepWindow() {
    let selectedList= this.getSelectedList("sweepWindow");
    ExternalInterface.call("openSweepWindow", this.entityCombo.selectedLabel, selectedList);
   }

   getSelectedList(calledFrom){
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
      errorLocation = 10;   
    let selectedRows = this.mainGrid.selectedItems;
    errorLocation = 20;
    var selectedList = "";
    for (let i=0; i < selectedRows.length; i++) 
    {
      errorLocation = 30;
     if( selectedRows[i]){
      errorLocation = 40;
      if(calledFrom=="forCheckSweep"){
        selectedList = selectedList + "'"+selectedRows[i].valueDate.content+" ',"+selectedRows[i].accountId.content+" ',"+ selectedRows[i].entityId.content+"',"+ 
        this.currencyCombo.selectedLabel+"|";
      }else{
          selectedList = selectedList + "'"+selectedRows[i].valueDate.content+" ',"+selectedRows[i].accountId.content+" ',"+ selectedRows[i].entityId.content+"|";
      }
       }
    } 
    return selectedList;
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "getSelectedList", errorLocation);
 }
   }


}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ManualSweep }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ManualSweep],
  entryComponents: []
})
export class ManualSweepModule { }